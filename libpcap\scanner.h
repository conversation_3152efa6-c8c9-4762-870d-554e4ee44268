#ifndef pcap_HEADER_H
#define pcap_HEADER_H 1
#define pcap_IN_HEADER 1

#line 6 "scanner.h"
/* Must come first for _LARGE_FILE_API on AIX. */
#include <config.h>

/*
 * Must come first to avoid warnings on Windows.
 *
 * Flex-generated scanners may only include <inttypes.h> if __STDC_VERSION__
 * is defined with a value >= 199901, meaning "full C99", and MSVC may not
 * define it with that value, because it isn't 100% C99-compliant, even
 * though it has an <inttypes.h> capable of defining everything the Flex
 * scanner needs.
 *
 * We, however, will include it if we know we have an MSVC version that has
 * it; this means that we may define the INTn_MAX and UINTn_MAX values in
 * scanner.c, and then include <stdint.h>, which may define them differently
 * (same value, but different string of characters), causing compiler warnings.
 *
 * If we include it here, and they're defined, that'll prevent scanner.c
 * from defining them.  So we include <pcap/pcap-inttypes.h>, to get
 * <inttypes.h> if we have it.
 */
#include <pcap/pcap-inttypes.h>

/*
 * grammar.h requires gencode.h and sometimes breaks in a polluted namespace
 * (see ftmacros.h), so include it early.
 */
#include "gencode.h"
#include "grammar.h"

#include "diag-control.h"

/*
 * Convert string to 32-bit unsigned integer; the string starts at
 * string and is string_len bytes long.
 *
 * On success, sets *val to the value and returns 1.
 * On failure, sets the BPF error string and returns 0.
 *
 * Also used in gencode.c
 */
typedef enum {
	STOULEN_OK,
	STOULEN_NOT_HEX_NUMBER,
	STOULEN_NOT_OCTAL_NUMBER,
	STOULEN_NOT_DECIMAL_NUMBER,
	STOULEN_ERROR
} stoulen_ret;

stoulen_ret stoulen(const char *string, size_t stringlen, bpf_u_int32 *val,
    compiler_state_t *cstate);

#line 59 "scanner.h"

#define  YY_INT_ALIGNED short int

/* A lexical scanner generated by flex */

#define FLEX_SCANNER
#define YY_FLEX_MAJOR_VERSION 2
#define YY_FLEX_MINOR_VERSION 6
#define YY_FLEX_SUBMINOR_VERSION 4
#if YY_FLEX_SUBMINOR_VERSION > 0
#define FLEX_BETA
#endif

#ifdef yy_create_buffer
#define pcap__create_buffer_ALREADY_DEFINED
#else
#define yy_create_buffer pcap__create_buffer
#endif

#ifdef yy_delete_buffer
#define pcap__delete_buffer_ALREADY_DEFINED
#else
#define yy_delete_buffer pcap__delete_buffer
#endif

#ifdef yy_scan_buffer
#define pcap__scan_buffer_ALREADY_DEFINED
#else
#define yy_scan_buffer pcap__scan_buffer
#endif

#ifdef yy_scan_string
#define pcap__scan_string_ALREADY_DEFINED
#else
#define yy_scan_string pcap__scan_string
#endif

#ifdef yy_scan_bytes
#define pcap__scan_bytes_ALREADY_DEFINED
#else
#define yy_scan_bytes pcap__scan_bytes
#endif

#ifdef yy_init_buffer
#define pcap__init_buffer_ALREADY_DEFINED
#else
#define yy_init_buffer pcap__init_buffer
#endif

#ifdef yy_flush_buffer
#define pcap__flush_buffer_ALREADY_DEFINED
#else
#define yy_flush_buffer pcap__flush_buffer
#endif

#ifdef yy_load_buffer_state
#define pcap__load_buffer_state_ALREADY_DEFINED
#else
#define yy_load_buffer_state pcap__load_buffer_state
#endif

#ifdef yy_switch_to_buffer
#define pcap__switch_to_buffer_ALREADY_DEFINED
#else
#define yy_switch_to_buffer pcap__switch_to_buffer
#endif

#ifdef yypush_buffer_state
#define pcap_push_buffer_state_ALREADY_DEFINED
#else
#define yypush_buffer_state pcap_push_buffer_state
#endif

#ifdef yypop_buffer_state
#define pcap_pop_buffer_state_ALREADY_DEFINED
#else
#define yypop_buffer_state pcap_pop_buffer_state
#endif

#ifdef yyensure_buffer_stack
#define pcap_ensure_buffer_stack_ALREADY_DEFINED
#else
#define yyensure_buffer_stack pcap_ensure_buffer_stack
#endif

#ifdef yylex
#define pcap_lex_ALREADY_DEFINED
#else
#define yylex pcap_lex
#endif

#ifdef yyrestart
#define pcap_restart_ALREADY_DEFINED
#else
#define yyrestart pcap_restart
#endif

#ifdef yylex_init
#define pcap_lex_init_ALREADY_DEFINED
#else
#define yylex_init pcap_lex_init
#endif

#ifdef yylex_init_extra
#define pcap_lex_init_extra_ALREADY_DEFINED
#else
#define yylex_init_extra pcap_lex_init_extra
#endif

#ifdef yylex_destroy
#define pcap_lex_destroy_ALREADY_DEFINED
#else
#define yylex_destroy pcap_lex_destroy
#endif

#ifdef yyget_debug
#define pcap_get_debug_ALREADY_DEFINED
#else
#define yyget_debug pcap_get_debug
#endif

#ifdef yyset_debug
#define pcap_set_debug_ALREADY_DEFINED
#else
#define yyset_debug pcap_set_debug
#endif

#ifdef yyget_extra
#define pcap_get_extra_ALREADY_DEFINED
#else
#define yyget_extra pcap_get_extra
#endif

#ifdef yyset_extra
#define pcap_set_extra_ALREADY_DEFINED
#else
#define yyset_extra pcap_set_extra
#endif

#ifdef yyget_in
#define pcap_get_in_ALREADY_DEFINED
#else
#define yyget_in pcap_get_in
#endif

#ifdef yyset_in
#define pcap_set_in_ALREADY_DEFINED
#else
#define yyset_in pcap_set_in
#endif

#ifdef yyget_out
#define pcap_get_out_ALREADY_DEFINED
#else
#define yyget_out pcap_get_out
#endif

#ifdef yyset_out
#define pcap_set_out_ALREADY_DEFINED
#else
#define yyset_out pcap_set_out
#endif

#ifdef yyget_leng
#define pcap_get_leng_ALREADY_DEFINED
#else
#define yyget_leng pcap_get_leng
#endif

#ifdef yyget_text
#define pcap_get_text_ALREADY_DEFINED
#else
#define yyget_text pcap_get_text
#endif

#ifdef yyget_lineno
#define pcap_get_lineno_ALREADY_DEFINED
#else
#define yyget_lineno pcap_get_lineno
#endif

#ifdef yyset_lineno
#define pcap_set_lineno_ALREADY_DEFINED
#else
#define yyset_lineno pcap_set_lineno
#endif

#ifdef yyget_column
#define pcap_get_column_ALREADY_DEFINED
#else
#define yyget_column pcap_get_column
#endif

#ifdef yyset_column
#define pcap_set_column_ALREADY_DEFINED
#else
#define yyset_column pcap_set_column
#endif

#ifdef yywrap
#define pcap_wrap_ALREADY_DEFINED
#else
#define yywrap pcap_wrap
#endif

#ifdef yyget_lval
#define pcap_get_lval_ALREADY_DEFINED
#else
#define yyget_lval pcap_get_lval
#endif

#ifdef yyset_lval
#define pcap_set_lval_ALREADY_DEFINED
#else
#define yyset_lval pcap_set_lval
#endif

#ifdef yyalloc
#define pcap_alloc_ALREADY_DEFINED
#else
#define yyalloc pcap_alloc
#endif

#ifdef yyrealloc
#define pcap_realloc_ALREADY_DEFINED
#else
#define yyrealloc pcap_realloc
#endif

#ifdef yyfree
#define pcap_free_ALREADY_DEFINED
#else
#define yyfree pcap_free
#endif

/* First, we deal with  platform-specific or compiler-specific issues. */

/* begin standard C headers. */
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <stdlib.h>

/* end standard C headers. */

/* flex integer type definitions */

#ifndef FLEXINT_H
#define FLEXINT_H

/* C99 systems have <inttypes.h>. Non-C99 systems may or may not. */

#if defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L

/* C99 says to define __STDC_LIMIT_MACROS before including stdint.h,
 * if you want the limit (max/min) macros for int types. 
 */
#ifndef __STDC_LIMIT_MACROS
#define __STDC_LIMIT_MACROS 1
#endif

#include <inttypes.h>
typedef int8_t flex_int8_t;
typedef uint8_t flex_uint8_t;
typedef int16_t flex_int16_t;
typedef uint16_t flex_uint16_t;
typedef int32_t flex_int32_t;
typedef uint32_t flex_uint32_t;
#else
typedef signed char flex_int8_t;
typedef short int flex_int16_t;
typedef int flex_int32_t;
typedef unsigned char flex_uint8_t; 
typedef unsigned short int flex_uint16_t;
typedef unsigned int flex_uint32_t;

/* Limits of integral types. */
#ifndef INT8_MIN
#define INT8_MIN               (-128)
#endif
#ifndef INT16_MIN
#define INT16_MIN              (-32767-1)
#endif
#ifndef INT32_MIN
#define INT32_MIN              (-2147483647-1)
#endif
#ifndef INT8_MAX
#define INT8_MAX               (127)
#endif
#ifndef INT16_MAX
#define INT16_MAX              (32767)
#endif
#ifndef INT32_MAX
#define INT32_MAX              (2147483647)
#endif
#ifndef UINT8_MAX
#define UINT8_MAX              (255U)
#endif
#ifndef UINT16_MAX
#define UINT16_MAX             (65535U)
#endif
#ifndef UINT32_MAX
#define UINT32_MAX             (4294967295U)
#endif

#ifndef SIZE_MAX
#define SIZE_MAX               (~(size_t)0)
#endif

#endif /* ! C99 */

#endif /* ! FLEXINT_H */

/* begin standard C++ headers. */

/* TODO: this is always defined, so inline it */
#define yyconst const

#if defined(__GNUC__) && __GNUC__ >= 3
#define yynoreturn __attribute__((__noreturn__))
#else
#define yynoreturn
#endif

/* An opaque pointer. */
#ifndef YY_TYPEDEF_YY_SCANNER_T
#define YY_TYPEDEF_YY_SCANNER_T
typedef void* yyscan_t;
#endif

/* For convenience, these vars (plus the bison vars far below)
   are macros in the reentrant scanner. */
#define yyin yyg->yyin_r
#define yyout yyg->yyout_r
#define yyextra yyg->yyextra_r
#define yyleng yyg->yyleng_r
#define yytext yyg->yytext_r
#define yylineno (YY_CURRENT_BUFFER_LVALUE->yy_bs_lineno)
#define yycolumn (YY_CURRENT_BUFFER_LVALUE->yy_bs_column)
#define yy_flex_debug yyg->yy_flex_debug_r

/* Size of default input buffer. */
#ifndef YY_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k.
 * Moreover, YY_BUF_SIZE is 2*YY_READ_BUF_SIZE in the general case.
 * Ditto for the __ia64__ case accordingly.
 */
#define YY_BUF_SIZE 32768
#else
#define YY_BUF_SIZE 16384
#endif /* __ia64__ */
#endif

#ifndef YY_TYPEDEF_YY_BUFFER_STATE
#define YY_TYPEDEF_YY_BUFFER_STATE
typedef struct yy_buffer_state *YY_BUFFER_STATE;
#endif

#ifndef YY_TYPEDEF_YY_SIZE_T
#define YY_TYPEDEF_YY_SIZE_T
typedef size_t yy_size_t;
#endif

#ifndef YY_STRUCT_YY_BUFFER_STATE
#define YY_STRUCT_YY_BUFFER_STATE
struct yy_buffer_state
	{
	FILE *yy_input_file;

	char *yy_ch_buf;		/* input buffer */
	char *yy_buf_pos;		/* current position in input buffer */

	/* Size of input buffer in bytes, not including room for EOB
	 * characters.
	 */
	int yy_buf_size;

	/* Number of characters read into yy_ch_buf, not including EOB
	 * characters.
	 */
	int yy_n_chars;

	/* Whether we "own" the buffer - i.e., we know we created it,
	 * and can realloc() it to grow it, and should free() it to
	 * delete it.
	 */
	int yy_is_our_buffer;

	/* Whether this is an "interactive" input source; if so, and
	 * if we're using stdio for input, then we want to use getc()
	 * instead of fread(), to make sure we stop fetching input after
	 * each newline.
	 */
	int yy_is_interactive;

	/* Whether we're considered to be at the beginning of a line.
	 * If so, '^' rules will be active on the next match, otherwise
	 * not.
	 */
	int yy_at_bol;

    int yy_bs_lineno; /**< The line count. */
    int yy_bs_column; /**< The column count. */

	/* Whether to try to fill the input buffer when we reach the
	 * end of it.
	 */
	int yy_fill_buffer;

	int yy_buffer_status;

	};
#endif /* !YY_STRUCT_YY_BUFFER_STATE */

void yyrestart ( FILE *input_file , yyscan_t yyscanner );
void yy_switch_to_buffer ( YY_BUFFER_STATE new_buffer , yyscan_t yyscanner );
YY_BUFFER_STATE yy_create_buffer ( FILE *file, int size , yyscan_t yyscanner );
void yy_delete_buffer ( YY_BUFFER_STATE b , yyscan_t yyscanner );
void yy_flush_buffer ( YY_BUFFER_STATE b , yyscan_t yyscanner );
void yypush_buffer_state ( YY_BUFFER_STATE new_buffer , yyscan_t yyscanner );
void yypop_buffer_state ( yyscan_t yyscanner );

YY_BUFFER_STATE yy_scan_buffer ( char *base, yy_size_t size , yyscan_t yyscanner );
YY_BUFFER_STATE yy_scan_string ( const char *yy_str , yyscan_t yyscanner );
YY_BUFFER_STATE yy_scan_bytes ( const char *bytes, int len , yyscan_t yyscanner );

void *yyalloc ( yy_size_t , yyscan_t yyscanner );
void *yyrealloc ( void *, yy_size_t , yyscan_t yyscanner );
void yyfree ( void * , yyscan_t yyscanner );

/* Begin user sect3 */

#define pcap_wrap(yyscanner) (/*CONSTCOND*/1)
#define YY_SKIP_YYWRAP

#define yytext_ptr yytext_r

#ifdef YY_HEADER_EXPORT_START_CONDITIONS
#define INITIAL 0

#endif

#ifndef YY_NO_UNISTD_H
/* Special case for "unistd.h", since it is non-ANSI. We include it way
 * down here because we want the user's section 1 to have been scanned first.
 * The user has a chance to override it with an option.
 */
#include <unistd.h>
#endif

#define YY_EXTRA_TYPE compiler_state_t *

int yylex_init (yyscan_t* scanner);

int yylex_init_extra ( YY_EXTRA_TYPE user_defined, yyscan_t* scanner);

/* Accessor methods to globals.
   These are made visible to non-reentrant scanners for convenience. */

int yylex_destroy ( yyscan_t yyscanner );

int yyget_debug ( yyscan_t yyscanner );

void yyset_debug ( int debug_flag , yyscan_t yyscanner );

YY_EXTRA_TYPE yyget_extra ( yyscan_t yyscanner );

void yyset_extra ( YY_EXTRA_TYPE user_defined , yyscan_t yyscanner );

FILE *yyget_in ( yyscan_t yyscanner );

void yyset_in  ( FILE * _in_str , yyscan_t yyscanner );

FILE *yyget_out ( yyscan_t yyscanner );

void yyset_out  ( FILE * _out_str , yyscan_t yyscanner );

			int yyget_leng ( yyscan_t yyscanner );

char *yyget_text ( yyscan_t yyscanner );

int yyget_lineno ( yyscan_t yyscanner );

void yyset_lineno ( int _line_number , yyscan_t yyscanner );

int yyget_column  ( yyscan_t yyscanner );

void yyset_column ( int _column_no , yyscan_t yyscanner );

YYSTYPE * yyget_lval ( yyscan_t yyscanner );

void yyset_lval ( YYSTYPE * yylval_param , yyscan_t yyscanner );

/* Macros after this point can all be overridden by user definitions in
 * section 1.
 */

#ifndef YY_SKIP_YYWRAP
#ifdef __cplusplus
extern "C" int yywrap ( yyscan_t yyscanner );
#else
extern int yywrap ( yyscan_t yyscanner );
#endif
#endif

#ifndef yytext_ptr
static void yy_flex_strncpy ( char *, const char *, int , yyscan_t yyscanner);
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen ( const char * , yyscan_t yyscanner);
#endif

#ifndef YY_NO_INPUT

#endif

/* Amount of stuff to slurp up with each read. */
#ifndef YY_READ_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k */
#define YY_READ_BUF_SIZE 16384
#else
#define YY_READ_BUF_SIZE 8192
#endif /* __ia64__ */
#endif

/* Number of entries by which start-condition stack grows. */
#ifndef YY_START_STACK_INCR
#define YY_START_STACK_INCR 25
#endif

/* Default declaration of generated scanner - a define so the user can
 * easily add parameters.
 */
#ifndef YY_DECL
#define YY_DECL_IS_OURS 1

extern int yylex \
               (YYSTYPE * yylval_param , yyscan_t yyscanner);

#define YY_DECL int yylex \
               (YYSTYPE * yylval_param , yyscan_t yyscanner)
#endif /* !YY_DECL */

/* yy_get_previous_state - get the state just before the EOB char was reached */

#undef YY_NEW_FILE
#undef YY_FLUSH_BUFFER
#undef yy_set_bol
#undef yy_new_buffer
#undef yy_set_interactive
#undef YY_DO_BEFORE_ACTION

#ifdef YY_DECL_IS_OURS
#undef YY_DECL_IS_OURS
#undef YY_DECL
#endif

#ifndef pcap__create_buffer_ALREADY_DEFINED
#undef yy_create_buffer
#endif
#ifndef pcap__delete_buffer_ALREADY_DEFINED
#undef yy_delete_buffer
#endif
#ifndef pcap__scan_buffer_ALREADY_DEFINED
#undef yy_scan_buffer
#endif
#ifndef pcap__scan_string_ALREADY_DEFINED
#undef yy_scan_string
#endif
#ifndef pcap__scan_bytes_ALREADY_DEFINED
#undef yy_scan_bytes
#endif
#ifndef pcap__init_buffer_ALREADY_DEFINED
#undef yy_init_buffer
#endif
#ifndef pcap__flush_buffer_ALREADY_DEFINED
#undef yy_flush_buffer
#endif
#ifndef pcap__load_buffer_state_ALREADY_DEFINED
#undef yy_load_buffer_state
#endif
#ifndef pcap__switch_to_buffer_ALREADY_DEFINED
#undef yy_switch_to_buffer
#endif
#ifndef pcap_push_buffer_state_ALREADY_DEFINED
#undef yypush_buffer_state
#endif
#ifndef pcap_pop_buffer_state_ALREADY_DEFINED
#undef yypop_buffer_state
#endif
#ifndef pcap_ensure_buffer_stack_ALREADY_DEFINED
#undef yyensure_buffer_stack
#endif
#ifndef pcap_lex_ALREADY_DEFINED
#undef yylex
#endif
#ifndef pcap_restart_ALREADY_DEFINED
#undef yyrestart
#endif
#ifndef pcap_lex_init_ALREADY_DEFINED
#undef yylex_init
#endif
#ifndef pcap_lex_init_extra_ALREADY_DEFINED
#undef yylex_init_extra
#endif
#ifndef pcap_lex_destroy_ALREADY_DEFINED
#undef yylex_destroy
#endif
#ifndef pcap_get_debug_ALREADY_DEFINED
#undef yyget_debug
#endif
#ifndef pcap_set_debug_ALREADY_DEFINED
#undef yyset_debug
#endif
#ifndef pcap_get_extra_ALREADY_DEFINED
#undef yyget_extra
#endif
#ifndef pcap_set_extra_ALREADY_DEFINED
#undef yyset_extra
#endif
#ifndef pcap_get_in_ALREADY_DEFINED
#undef yyget_in
#endif
#ifndef pcap_set_in_ALREADY_DEFINED
#undef yyset_in
#endif
#ifndef pcap_get_out_ALREADY_DEFINED
#undef yyget_out
#endif
#ifndef pcap_set_out_ALREADY_DEFINED
#undef yyset_out
#endif
#ifndef pcap_get_leng_ALREADY_DEFINED
#undef yyget_leng
#endif
#ifndef pcap_get_text_ALREADY_DEFINED
#undef yyget_text
#endif
#ifndef pcap_get_lineno_ALREADY_DEFINED
#undef yyget_lineno
#endif
#ifndef pcap_set_lineno_ALREADY_DEFINED
#undef yyset_lineno
#endif
#ifndef pcap_get_column_ALREADY_DEFINED
#undef yyget_column
#endif
#ifndef pcap_set_column_ALREADY_DEFINED
#undef yyset_column
#endif
#ifndef pcap_wrap_ALREADY_DEFINED
#undef yywrap
#endif
#ifndef pcap_get_lval_ALREADY_DEFINED
#undef yyget_lval
#endif
#ifndef pcap_set_lval_ALREADY_DEFINED
#undef yyset_lval
#endif
#ifndef pcap_get_lloc_ALREADY_DEFINED
#undef yyget_lloc
#endif
#ifndef pcap_set_lloc_ALREADY_DEFINED
#undef yyset_lloc
#endif
#ifndef pcap_alloc_ALREADY_DEFINED
#undef yyalloc
#endif
#ifndef pcap_realloc_ALREADY_DEFINED
#undef yyrealloc
#endif
#ifndef pcap_free_ALREADY_DEFINED
#undef yyfree
#endif
#ifndef pcap_text_ALREADY_DEFINED
#undef yytext
#endif
#ifndef pcap_leng_ALREADY_DEFINED
#undef yyleng
#endif
#ifndef pcap_in_ALREADY_DEFINED
#undef yyin
#endif
#ifndef pcap_out_ALREADY_DEFINED
#undef yyout
#endif
#ifndef pcap__flex_debug_ALREADY_DEFINED
#undef yy_flex_debug
#endif
#ifndef pcap_lineno_ALREADY_DEFINED
#undef yylineno
#endif
#ifndef pcap_tables_fload_ALREADY_DEFINED
#undef yytables_fload
#endif
#ifndef pcap_tables_destroy_ALREADY_DEFINED
#undef yytables_destroy
#endif
#ifndef pcap_TABLES_NAME_ALREADY_DEFINED
#undef yyTABLES_NAME
#endif

#line 469 "scanner.l"


#line 768 "scanner.h"
#undef pcap_IN_HEADER
#endif /* pcap_HEADER_H */
