/*
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that: (1) source code
 * distributions retain the above copyright notice and this paragraph
 * in its entirety, and (2) distributions including binary code include
 * the above copyright notice and this paragraph in its entirety in
 * the documentation or other materials provided with the distribution.
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND
 * WITHOUT ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, WITHOUT
 * LIMITATION, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE.
 */

/* \summary: Hypertext Transfer Protocol (HTTP) printer */

#include <config.h>

#include "netdissect-stdinc.h"

#include "netdissect.h"

/*
 * Includes WebDAV requests.
 */
static const char *httpcmds[] = {
	"GET",
	"PUT",
	"COPY",
	"HEAD",
	"LOCK",
	"MOVE",
	"POL<PERSON>",
	"POST",
	"<PERSON>OPY",
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON><PERSON>",
	"TRA<PERSON>",
	"<PERSON><PERSON><PERSON>",
	"<PERSON>R<PERSON>",
	"<PERSON>LE<PERSON>",
	"<PERSON>AR<PERSON>",
	"<PERSON>L<PERSON><PERSON>",
	"REPOR<PERSON>",
	"UPDATE",
	"NOTIFY",
	"BDELETE",
	"CONNECT",
	"OPTIONS",
	"CHECKIN",
	"PROPFIND",
	"CHECKOUT",
	"CCM_POST",
	"SUBSCRIBE",
	"PROPPATCH",
	"BPROPFIND",
	"BPROPPATCH",
	"UNCHECKOUT",
	"MKACTIVITY",
	"MKWORKSPACE",
	"UNSUBSCRIBE",
	"RPC_CONNECT",
	"VERSION-CONTROL",
	"BASELINE-CONTROL",
	NULL
};

void
http_print(netdissect_options *ndo, const u_char *pptr, u_int len)
{
	ndo->ndo_protocol = "http";
	txtproto_print(ndo, pptr, len, httpcmds, RESP_CODE_SECOND_TOKEN);
}
