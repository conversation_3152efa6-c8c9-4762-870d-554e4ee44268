.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_DATALINK_NAME_TO_VAL 3PCAP "25 July 2018"
.SH NAME
pcap_datalink_name_to_val \- get the link-layer header type value
corresponding to a header type name
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
int pcap_datalink_name_to_val(const char *name);
.ft
.fi
.SH DESCRIPTION
.BR pcap_datalink_name_to_val ()
translates a link-layer header type name, which is a
.B DLT_
name with the
.B DLT_
removed, to the corresponding link-layer header type value.  The
translation is case-insensitive.
.SH RETURN VALUE
.BR pcap_datalink_name_to_val ()
returns the type value on success and
.B PCAP_ERROR
if the name is not a known
type name.
.SH SEE ALSO
.BR pcap (3PCAP),
.BR pcap_set_datalink (3PCAP),
.BR pcap_datalink_val_to_name (3PCAP)
