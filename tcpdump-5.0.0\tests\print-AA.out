    1  2005-07-06 03:57:35.938066 IP 127.0.0.1.55920 > 127.0.0.1.80: Flags [S], seq 928549246, win 32767, options [mss 16396,sackOK,TS val 1306300950 ecr 0,nop,wscale 2], length 0
..............E..<.h@.@.!R.........p.P7X.~.........!....@....
M...........
    2  2005-07-06 03:57:35.938122 IP 127.0.0.1.80 > 127.0.0.1.55920: Flags [S.], seq 930778609, ack 928549247, win 32767, options [mss 16396,sackOK,TS val 1306300950 ecr 1306300950,nop,wscale 2], length 0
..............E..<..@.@.<..........P.p7z..7X......n.....@....
M...M.......
    3  2005-07-06 03:57:35.938167 IP 127.0.0.1.55920 > 127.0.0.1.80: Flags [.], ack 1, win 8192, options [nop,nop,TS val 1306300950 ecr 1306300950], length 0
..............E..4.j@.@.!X.........p.P7X..7z.... .7......
M...M...
    4  2005-07-06 03:57:35.939423 IP 127.0.0.1.55920 > 127.0.0.1.80: Flags [P.], seq 1:203, ack 1, win 8192, options [nop,nop,TS val 1306300951 ecr 1306300950], length 202: HTTP: GET / HTTP/1.1
..............E....l@.@. ..........p.P7X..7z.... ........
M...M...GET / HTTP/1.1
Host: localhost
User-Agent: ELinks/0.10.4-7-debian (textmode; Linux 2.6.11-1-686-smp i686; 132x56-2)
Accept: */*
Accept-Encoding: gzip
Accept-Language: en
Connection: Keep-Alive


    5  2005-07-06 03:57:35.940474 IP 127.0.0.1.80 > 127.0.0.1.55920: Flags [.], ack 203, win 8192, options [nop,nop,TS val 1306300952 ecr 1306300951], length 0
..............E..4..@.@............P.p7z..7X.I.. .7......
M...M...
    6  2005-07-06 03:57:35.941232 IP 127.0.0.1.80 > 127.0.0.1.55920: Flags [P.], seq 1:5560, ack 203, win 8192, options [nop,nop,TS val 1306300953 ecr 1306300951], length 5559: HTTP: HTTP/1.1 200 OK
..............E.....@.@..%.........P.p7z..7X.I.. ........
M...M...HTTP/1.1 200 OK
Date: Wed, 06 Jul 2005 03:57:35 GMT
Server: Apache/1.3.33
Last-Modified: Sun, 15 Aug 2004 00:43:41 GMT
ETag: "6e80f0-148a-411eb1bd"
Accept-Ranges: bytes
Content-Length: 5258
Keep-Alive: timeout=15, max=100
Connection: Keep-Alive
Content-Type: text/html; charset=iso-8859-1

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<HTML>
<HEAD>
   <META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=iso-8859-1">
   <META NAME="Description" CONTENT="The initial installation of Debian apache.">
   <TITLE>Placeholder page</TITLE>
</HEAD>
<BODY TEXT="#000000" BGCOLOR="#FFFFFF" LINK="#0000EF" VLINK="#55188A" ALINK="#FF0000">

<H1>Placeholder page</H1>
<H2>If you are just browsing the web</h2>

<P>The owner of this web site has not put up any web pages yet.
Please come back later.</P>

<P><SMALL><CITE>Move along, nothing to see here...</CITE> :-)</SMALL></P>

<H2>If you are trying to locate the administrator of this machine</H2>

<P>If you want to report something about this host's behavior, please
contact the Internet Service Provider (ISP) involved directly.</P>

<P>See the <A href="http://www.abuse.net/">Network Abuse
Clearinghouse</A> for how to do this.</P>

<H2>If you are the administrator of this machine</H2>

<P>The initial installation of <A href="http://www.debian.org/">Debian's
apache</A> web server package was successful.</P>

<P><STRONG>You should replace this page with your own web pages as
soon as possible.</STRONG></P>

<P>Unless you changed its configuration, your new server is configured as follows:
<UL>
<LI>
Configuration files can be found in <TT>/etc/apache</TT>.</LI>

<LI>
The <TT>DocumentRoot</TT>, which is the directory under which all your
HTML files should exist, is set to <TT>/var/www</TT>.</LI>

<LI>
CGI scripts are looked for in <TT>/usr/lib/cgi-bin</TT>, which is where
Debian packages will place their scripts.</LI>

<LI>
Log files are placed in <TT>/var/log/apache</TT>, and will be rotated
weekly.  The frequency of rotation can be easily changed by editing
<TT>/etc/logrotate.d/apache</TT>.</LI>

<LI>
The default directory index is <TT>index.html</TT>, meaning that requests
for a directory <TT>/foo/bar/</TT> will give the contents of the file <TT>/var/www/foo/bar/index.html</TT>
if it exists (assuming that <TT>/var/www</TT> is your <TT>DocumentRoot</TT>).</LI>

<LI>
User directories are enabled, and user documents will be looked for
in the <TT>public_html</TT> directory of the users' homes.  These dirs
should be under <TT>/home</TT>, and users will not be able to symlink
to files they don't own.</LI>

</UL>
All the standard apache modules are available with this release and are
now managed with debconf.  Type <TT>dpkg-reconfigure apache</TT> to
select which modules you want enabled.  Many other modules are available
through the Debian package system with the names <TT>libapache-mod-*</TT>.
If you need to compile a module yourself, you will need to install the
<TT>apache-dev</TT> package.

<P>More documentation on Apache can be found on:
<UL>
<LI>
The <A HREF="/doc/apache-doc/manual/">Apache documentation</A> stored on your server.</LI>

<LI>
The <A HREF="http://www.apache.org/">Apache Project</A> home site.</LI>

<LI>
The <A HREF="http://www.apache-ssl.org/">Apache-SSL</A> home site.</LI>

<LI>
The <A HREF="http://perl.apache.org/">mod perl</A> home site.</LI>

<LI>
The <A HREF="http://www.apacheweek.com/">ApacheWeek</A> newsletter.</LI>

<LI>
The <A HREF="http://www.debian.org/doc/">Debian Project
Documentation</A> which contains HOWTOs, FAQs, and software updates.</LI>
</UL>

<P>You can also consult the list of <A HREF="http://www.boutell.com/faq/">World
Wide Web Frequently Asked Questions</A> for information.

<H2>Let other people know about this server</H2>

<A HREF="http://netcraft.com/">Netcraft</A> provides an interesting free
service for web site monitoring and statistic collection.
You can let them know about your server using their
<A HREF="http://uptime.netcraft.com/">interface</A>.
Enabling the monitoring of your server will provide a better global overview
of who is using what and where, and it would give Debian a better
overview of the apache package usage.

<H2>About this page</H2>

<IMG ALIGN="right" ALT="" HEIGHT="247" WIDTH="278" SRC="icons/jhe061.png">

<P>This is a placeholder page installed by the <A
HREF="http://www.debian.org/">Debian</A>
release of the apache Web server package.

<P>This computer has installed the Debian GNU/Linux operating system,
but it has <strong>nothing to do with the Debian
Project</strong>. Please do <strong>not</strong> contact the Debian
Project about it.</P>

<P>If you find a bug in this apache package, or in Apache itself,
please file a bug report on it.  Instructions on doing this, and the
list of <A HREF="http://bugs.debian.org/src:apache">known bugs</A> of this
package, can be found in the 
<A HREF="http://www.debian.org/Bugs/Reporting">Debian Bug Tracking System</A>.

<P>Thanks for using this package, and congratulations for your choice of
a Debian system!</P>

<DIV align="center">
<a href="http://www.debian.org/">
<IMG align="middle" height="30" width="25" src="icons/debian/openlogo-25.jpg" alt="Debian">
</a>
<a href="http://www.apache.org/">
<IMG align="middle" height="32" width="259" src="icons/apache_pb.png" alt="Apache">
</a>
</DIV>

<!--
  This page was initially created by Johnie Ingram (http://netgod.net/)
  It was later edited by Matthew Wilcox and Josip Rodin.
  Last modified: $Date: 2004/06/20 15:33:57 $.
  -->

</BODY>
</HTML>

    7  2005-07-06 03:57:35.941260 IP 127.0.0.1.55920 > 127.0.0.1.80: Flags [.], ack 5560, win 12383, options [nop,nop,TS val 1306300953 ecr 1306300953], length 0
..............E..4.n@.@.!T.........p.P7X.I7z....0_.......
M...M...
    8  2005-07-06 03:57:37.229575 IP 127.0.0.1.55920 > 127.0.0.1.80: Flags [F.], seq 203, ack 5560, win 12383, options [nop,nop,TS val 1306302241 ecr 1306300953], length 0
..............E..4.p@.@.!R.........p.P7X.I7z....0_.......
M..!M...
    9  2005-07-06 03:57:37.230839 IP 127.0.0.1.80 > 127.0.0.1.55920: Flags [F.], seq 5560, ack 204, win 8192, options [nop,nop,TS val 1306302243 ecr 1306302241], length 0
..............E..4..@.@............P.p7z..7X.J.. ..5.....
M..#M..!
   10  2005-07-06 03:57:37.230900 IP 127.0.0.1.55920 > 127.0.0.1.80: Flags [.], ack 5561, win 12383, options [nop,nop,TS val 1306302243 ecr 1306302243], length 0
..............E..4.r@.@.!P.........p.P7X.J7z....0_.......
M..#M..#
