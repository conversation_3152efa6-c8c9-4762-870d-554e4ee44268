.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_LOOP 3PCAP "5 March 2022"
.SH NAME
pcap_loop, pcap_dispatch \- process packets from a live capture or savefile
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
typedef void (*pcap_handler)(u_char *user, const struct pcap_pkthdr *h,
    const u_char *bytes);
int pcap_loop(pcap_t *p, int cnt,
    pcap_handler callback, u_char *user);
int pcap_dispatch(pcap_t *p, int cnt,
    pcap_handler callback, u_char *user);
.ft
.fi
.SH DESCRIPTION
.BR pcap_loop ()
processes packets from a live capture or ``savefile'' until
.I cnt
packets are processed, the end of the ``savefile'' is
reached when reading from a ``savefile'',
.BR pcap_breakloop (3PCAP)
is called, or an error occurs.
It does
.B not
return when live packet buffer timeouts occur.
A value of
.B \-1
or
.B 0
for
.I cnt
is equivalent to infinity, so that packets are processed until another
ending condition occurs.
.PP
.BR pcap_dispatch ()
processes packets from a live capture or ``savefile'' until
.I cnt
packets are processed, the end of the current bufferful of packets is
reached when doing a live capture, the end of the ``savefile'' is
reached when reading from a ``savefile'',
.BR pcap_breakloop ()
is called, or an error occurs.
Thus, when doing a live capture,
.I cnt
is the maximum number of packets to process before returning, but is not
a minimum number; when reading a live capture, only one
bufferful of packets is read at a time, so fewer than
.I cnt
packets may be processed. A value of
.B \-1
or
.B 0
for
.I cnt
causes all the packets received in one buffer to be processed when
reading a live capture, and causes all the packets in the file to be
processed when reading a ``savefile''.
.PP
Note that, when doing a live capture on some platforms, if the read
timeout expires when there are no packets available,
.BR pcap_dispatch ()
will return 0, even when not in non-blocking mode, as there are no
packets to process.  Applications should be prepared for this to happen,
but must not rely on it happening.
.PP
.I callback
specifies a
.B pcap_handler
routine to be called with three arguments:
a
.B u_char
pointer which is passed in the
.I user
argument to
.BR pcap_loop ()
or
.BR pcap_dispatch (),
a
.B const struct pcap_pkthdr
pointer pointing to the packet time stamp and lengths, and a
.B const u_char
pointer to the first
.B caplen
(as given in the
.BR "struct pcap_pkthdr" ,
a pointer to which is passed to the callback routine)
bytes of data from the packet.  The
.B struct pcap_pkthdr
and the packet data are not to be freed by the callback routine, and are
not guaranteed to be valid after the callback routine returns; if the
code needs them to be valid after the callback, it must make a copy of
them.
.PP
The bytes of data from the packet begin with a link-layer header.  The
format of the link-layer header is indicated by the return value of the
.BR pcap_datalink (3PCAP)
routine when handed the
.B pcap_t
value also passed to
.BR pcap_loop ()
or
.BR pcap_dispatch ().
.I https://www.tcpdump.org/linktypes.html
lists the values
.BR pcap_datalink ()
can return and describes the packet formats that
correspond to those values.  The value it returns will be valid for all
packets received unless and until
.BR pcap_set_datalink (3PCAP)
is called; after a successful call to
.BR pcap_set_datalink (),
all subsequent packets will have a link-layer header of the type
specified by the link-layer header type value passed to
.BR pcap_set_datalink ().
.PP
Do
.B NOT
assume that the packets for a given capture or ``savefile`` will have
any given link-layer header type, such as
.B DLT_EN10MB
for Ethernet.  For example, the "any" device on Linux will have a
link-layer header type of
.B DLT_LINUX_SLL
or
.B DLT_LINUX_SLL2
even if all devices on the system at the time the "any" device is opened
have some other data link type, such as
.B DLT_EN10MB
for Ethernet.
.SH RETURN VALUE
.BR pcap_loop ()
returns
.B 0
if
.I cnt
is exhausted or if, when reading from a ``savefile'', no more packets
are available.  It returns
.B PCAP_ERROR_BREAK
if the loop terminated due to a call to
.BR pcap_breakloop ()
before any packets were processed,
.B PCAP_ERROR_NOT_ACTIVATED
if called on a capture handle that has been created but not activated,
or
.B PCAP_ERROR
if another error occurs.
It does
.B not
return when live packet buffer timeouts occur; instead, it attempts to
read more packets.
.PP
.BR pcap_dispatch ()
returns the number of packets processed on success; this can be 0 if no
packets were read from a live capture (if, for example, they were
discarded because they didn't pass the packet filter, or if, on
platforms that support a packet buffer timeout that starts before any
packets arrive, the timeout expires before any packets arrive, or if the
file descriptor for the capture device is in non-blocking mode and no
packets were available to be read) or if no more packets are available
in a ``savefile''. It returns
.B PCAP_ERROR_BREAK
if the loop terminated due to a call to
.BR pcap_breakloop ()
before any packets were processed,
.B PCAP_ERROR_NOT_ACTIVATED
if called on a capture handle that has been created but not activated,
or
.B PCAP_ERROR
if another error occurs.
.ft B
If your application uses pcap_breakloop(),
make sure that you explicitly check for PCAP_ERROR and PCAP_ERROR_BREAK,
rather than just checking for a return value < 0.
.ft R
.PP
If
.B PCAP_ERROR
is returned,
.BR pcap_geterr (3PCAP)
or
.BR pcap_perror (3PCAP)
may be called with
.I p
as an argument to fetch or display the error text.
.SH BACKWARD COMPATIBILITY
.PP
In libpcap versions before 1.5.0, the behavior when
.I cnt
was
.B 0
was undefined; different platforms and devices behaved differently,
so code that must work with these versions of libpcap should use
.BR \-1 ,
not
.BR 0 ,
as the value of
.IR cnt .
.SH SEE ALSO
.BR pcap (3PCAP)
