.TH IOSTAT 1 "JULY 2013" Linux "Linux User's Manual" -*- nroff -*-
.SH NAME
iostat \- Report Central Processing Unit (CPU) statistics and input/output
statistics for devices and partitions.
.SH SYNOPSIS
.ie 'yes'@WITH_DEBUG@' \{
.B iostat [ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]
.B [ -j { ID | LABEL | PATH | UUID | ... } ]
.B [ [ -T ] -g
.I group_name
.B ] [ -p [
.I device
.B [,...] | ALL ] ] [
.I device
.B [...] | ALL ] [ --debuginfo ] [
.I interval
.B [
.I count
.B ] ]
.\}
.el \{
.B iostat [ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]
.B [ -j { ID | LABEL | PATH | UUID | ... } ]
.B [ [ -T ] -g
.I group_name
.B ] [ -p [
.I device
.B [,...] | ALL ] ] [
.I device
.B [...] | ALL ] [
.I interval
.B [
.I count
.B ] ]
.\}
.SH DESCRIPTION
The
.B iostat
command is used for monitoring system input/output device
loading by observing the time the devices are active in relation
to their average transfer rates. The
.B iostat
command generates reports
that can be used to change system configuration to better balance
the input/output load between physical disks.

The first report generated by the
.B iostat
command provides statistics
concerning the time since the system was booted, unless the
.B -y
option is used (in this case, this first report is omitted).
Each subsequent report
covers the time since the previous report. All statistics are reported
each time the
.B iostat
command is run. The report consists of a
CPU header row followed by a row of
CPU statistics. On
multiprocessor systems, CPU statistics are calculated system-wide
as averages among all processors. A device header row is displayed
followed by a line of statistics for each device that is configured.

The
.I interval
parameter specifies the amount of time in seconds between
each report. The first report contains statistics for the time since
system startup (boot), unless the
.B -y
option is used (in this case, this report is omitted).
Each subsequent report contains statistics
collected during the interval since the previous report. The
.I count
parameter can be specified in conjunction with the
.I interval
parameter. If the
.I count
parameter is specified, the value of
.I count
determines the number of reports generated at
.I interval
seconds apart. If the
.I interval
parameter is specified without the
.I count
parameter, the
.B iostat
command generates reports continuously.

.SH REPORTS
The
.B iostat
command generates two types of reports, the CPU
Utilization report and the Device Utilization report.
.IP "CPU Utilization Report"
The first report generated by the
.B iostat
command is the CPU
Utilization Report. For multiprocessor systems, the CPU values are
global averages among all processors.
The report has the following format:

.B %user
.RS
.RS
Show the percentage of CPU utilization that occurred while
executing at the user level (application).
.RE

.B %nice
.RS
Show the percentage of CPU utilization that occurred while
executing at the user level with nice priority.
.RE

.B %system
.RS
Show the percentage of CPU utilization that occurred while
executing at the system level (kernel).
.RE

.B %iowait
.RS
Show the percentage of time that the CPU or CPUs were idle during which
the system had an outstanding disk I/O request.
.RE

.B %steal
.RS
Show the percentage of time spent in involuntary wait by the virtual CPU
or CPUs while the hypervisor was servicing another virtual processor.
.RE

.B %idle
.RS
Show the percentage of time that the CPU or CPUs were idle and the system
did not have an outstanding disk I/O request.
.RE
.RE
.IP "Device Utilization Report"
The second report generated by the
.B iostat
command is the Device Utilization
Report. The device report provides statistics on a per physical device
or partition basis. Block devices and partitions for which statistics are
to be displayed may be entered on the command line.
If no device nor partition
is entered, then statistics are displayed
for every device used by the system, and
providing that the kernel maintains statistics for it.
If the
.B ALL
keyword is given on the command line, then statistics are
displayed for every device defined by the system, including those
that have never been used.
Transfer rates are shown in 1K blocks by default, unless the environment
variable POSIXLY_CORRECT is set, in which case 512-byte blocks are used.
The report may show the following fields,
depending on the flags used:

.B Device:
.RS
.RS
This column gives the device (or partition) name as listed in the /dev
directory.

.RE
.B tps
.RS
Indicate the number of transfers per second that were issued
to the device. A transfer is an I/O request to the
device. Multiple logical requests can be combined into a single I/O
request to the device. A transfer is of indeterminate size.

.RE
.B Blk_read/s (kB_read/s, MB_read/s)
.RS
Indicate the amount of data read from the device expressed in a number of
blocks (kilobytes, megabytes) per second. Blocks are equivalent to sectors
and therefore have a size of 512 bytes.

.RE
.B Blk_wrtn/s (kB_wrtn/s, MB_wrtn/s)
.RS
Indicate the amount of data written to the device expressed in a number of
blocks (kilobytes, megabytes) per second.

.RE
.B Blk_read (kB_read, MB_read)
.RS
The total number of blocks (kilobytes, megabytes) read.

.RE
.B Blk_wrtn (kB_wrtn, MB_wrtn)
.RS
The total number of blocks (kilobytes, megabytes) written.

.RE
.B rrqm/s
.RS
The number of read requests merged per second that were queued to the device.

.RE
.B wrqm/s
.RS
The number of write requests merged per second that were queued to the device.

.RE
.B r/s
.RS
The number (after merges) of read requests completed per second for the device.

.RE
.B w/s
.RS
The number (after merges) of write requests completed per second for the device.

.RE
.B rsec/s (rkB/s, rMB/s)
.RS
The number of sectors (kilobytes, megabytes) read from the device per second.

.RE
.B wsec/s (wkB/s, wMB/s)
.RS
The number of sectors (kilobytes, megabytes) written to the device per second.

.RE
.B avgrq-sz
.RS
The average size (in sectors) of the requests that were issued to the device.

.RE
.B avgqu-sz
.RS
The average queue length of the requests that were issued to the device.

.RE
.B await
.RS
The average time (in milliseconds) for I/O requests issued to the device
to be served. This includes the time spent by the requests in queue and
the time spent servicing them.

.RE
.B r_await
.RS
The average time (in milliseconds) for read requests issued to the device
to be served. This includes the time spent by the requests in queue and
the time spent servicing them.

.RE
.B w_await
.RS
The average time (in milliseconds) for write requests issued to the device
to be served. This includes the time spent by the requests in queue and
the time spent servicing them.

.RE
.B svctm
.RS
The average service time (in milliseconds) for I/O requests that were issued
to the device. Warning! Do not trust this field any more.
This field will be removed in a future sysstat version.

.RE
.B %util
.RS
Percentage of CPU time during which I/O requests were issued to the device
(bandwidth utilization for the device). Device saturation occurs when this
value is close to 100% for devices serving requests serially.
But for devices serving requests in parallel, such as RAID arrays and
modern SSDs, this number does not reflect their performance limits.
.RE
.RE
.SH OPTIONS
.IP -c
Display the CPU utilization report.
.IP -d
Display the device utilization report.
.if 'yes'@WITH_DEBUG@' \{
.IP --debuginfo
Print debug output to stderr.
.\}
.IP "-g group_name { device [...] | ALL }
Display statistics for a group of devices.
The
.B iostat
command reports statistics for each individual device in the list
then a line of global statistics for the group displayed as
.B group_name
and made up of all the devices in the list. The
.B ALL
keyword means that all the block devices defined by the system shall be
included in the group.
.IP -h
Make the Device Utilization Report easier to read by a human.
.IP "-j { ID | LABEL | PATH | UUID | ... } [ device [...] | ALL ]"
Display persistent device names. Options
.BR ID ,
.BR LABEL ,
etc. specify the type of the persistent name. These options are not limited,
only prerequisite is that directory with required persistent names is present in
.IR /dev/disk .
Optionally, multiple devices can be specified in the chosen persistent name type.
Because persistent device names are usually long, option
.B -h
is enabled implicitly with this option.
.IP -k
Display statistics in kilobytes per second.
.IP -m
Display statistics in megabytes per second.
.IP -N
Display the registered device mapper names for any device mapper devices.
Useful for viewing LVM2 statistics.
.IP "-p [ { device [,...] | ALL } ]"
The -p option displays statistics for
block devices and all their partitions that are used by the system.
If a device name is entered on the command line, then statistics for it
and all its partitions are displayed. Last, the
.B ALL
keyword indicates that statistics have to be displayed for all the block
devices and partitions defined by the system, including those that have
never been used. If option
.B -j
is defined before this option, devices entered on the command line can be
specified with the chosen persistent name type.
.IP -T
This option must be used with option -g and indicates that only global
statistics for the group are to be displayed, and not statistics for
individual devices in the group.
.IP -t
Print the time for each report displayed. The timestamp format may depend
on the value of the S_TIME_FORMAT environment variable (see below).
.IP -V
Print version number then exit.
.IP -x
Display extended statistics.
.IP -y
Omit first report with statistics since system boot, if displaying
multiple records at given interval.
.IP -z
Tell
.B iostat
to omit output for any devices for which there was no activity
during the sample period.

.SH ENVIRONMENT
The
.B iostat
command takes into account the following environment variables:

.IP S_TIME_FORMAT
If this variable exists and its value is
.BR ISO
then the current locale will be ignored when printing the date in the report
header. The
.B iostat
command will use the ISO 8601 format (YYYY-MM-DD) instead.
The timestamp displayed with option -t will also be compliant with ISO 8601
format.

.IP POSIXLY_CORRECT
When this variable is set, transfer rates are shown in 512-byte blocks instead
of the default 1K blocks.

.SH EXAMPLES
.B iostat
.RS
Display a single history since boot report for all CPU and Devices.

.RE
.B iostat -d 2
.RS
Display a continuous device report at two second intervals.

.RE
.B iostat -d 2 6
.RS
Display six reports at two second intervals for all devices.

.RE
.B iostat -x sda sdb 2 6
.RS
Display six reports of extended statistics at two second intervals for devices
sda and sdb.

.RE
.B iostat -p sda 2 6
.RS
Display six reports at two second intervals for device sda and all its
partitions (sda1, etc.)
.SH BUGS
.I /proc
filesystem must be mounted for
.B iostat
to work.

Kernels older than 2.6.x are no longer supported.

The average service time (svctm field) value is meaningless,
as I/O statistics are now calculated at block level, and we don't know
when the disk driver starts to process a request. For this reason,
this field will be removed in a future sysstat version. 
.SH FILES
.I /proc/stat
contains system statistics.

.I /proc/uptime
contains system uptime.

.I /proc/diskstats
contains disks statistics.

.I /sys
contains statistics for block devices.

.I /proc/self/mountstats
contains statistics for network filesystems.

.I /dev/disk
contains persistent device names.
.SH AUTHOR
Sebastien Godard (sysstat <at> orange.fr)
.SH SEE ALSO
.BR sar (1),
.BR pidstat (1),
.BR mpstat (1),
.BR vmstat (8),
.BR nfsiostat (1),
.BR cifsiostat (1)

.I http://pagesperso-orange.fr/sebastien.godard/
