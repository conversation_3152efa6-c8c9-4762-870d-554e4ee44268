#!/bin/bash
# MySQL连接统计

GENERAL_LOG="/tmp/mysql-logs/general.log"

echo "MySQL连接统计"
echo "============="

# 统计总连接数
TOTAL_CONNECTS=$(grep -c "Connect" "$GENERAL_LOG")
echo "总连接数: $TOTAL_CONNECTS"

# 按时间统计 - 统计每秒真实连接数
echo ""
echo "按时间统计连接数 (每秒):"
grep "^[0-9].*Connect" "$GENERAL_LOG" | tail -20 | awk '{print $1" "$2}' | while read ts; do
    count=$(awk -v ts="$ts" '
    BEGIN { found=0; count=0 }
    $0 ~ "^"ts && /Connect/ { found=1; count=1; next }
    found && /^[0-9].*Connect/ { exit }
    found && /Connect/ { count++ }
    END { print count }
    ' "$GENERAL_LOG")
    echo "$count $ts"
done | sort -nr | head -20 | awk '{printf "%4d连接/秒  %s %s\n", $1, $2, $3}'

# 客户端IP统计
echo ""
echo "客户端IP连接统计:"
grep "Connect.*@" "$GENERAL_LOG" | \
    sed 's/.*@\([^[:space:]]*\).*/\1/' | \
    sort | uniq -c | sort -nr | \
    awk '{printf "%8d连接  %s\n", $1, $2}'

# 按IP和时间统计 - 统计每个IP每秒真实连接数
echo ""
echo "各IP每秒连接数统计 (最高的20个):"
grep "^[0-9].*Connect" "$GENERAL_LOG" | tail -20 | awk '{print $1" "$2}' | while read ts; do
    awk -v ts="$ts" '
    BEGIN { found=0 }
    $0 ~ "^"ts && /Connect/ {
        found=1
        match($0, /@([^[:space:]]+)/, arr)
        if (arr[1]) ip_count[arr[1]]++
        next
    }
    found && /^[0-9].*Connect/ { exit }
    found && /Connect.*@/ {
        match($0, /@([^[:space:]]+)/, arr)
        if (arr[1]) ip_count[arr[1]]++
    }
    END {
        for (ip in ip_count) {
            print ip_count[ip]" "ts" "ip
        }
    }
    ' "$GENERAL_LOG"
done | sort -nr | head -20 | awk '{printf "%4d连接/秒  %s %s  %s\n", $1, $2, $3, $4}'
