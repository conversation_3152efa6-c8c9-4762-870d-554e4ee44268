Begin4
Title:		sysstat - the sar, sadf, mpstat, iostat, nfsiostat, cifsiostat and pidstat commands for Linux
Version:	10.2.1
Entered-date:	2014-01-19
Description:	The sysstat package contains the sar, sadf, mpstat, iostat,
		pidstat, nfsiostat, cifsiostat and sa tools for Linux.
		The sar command collects and reports system activity
		information.
		The information collected by sar can be saved in a file
		in a binary format for future inspection.
		The statistics reported by sar concern I/O transfer rates,
		paging activity, process-related activities, interrupts,
		network activity, memory and swap space utilization, CPU
		utilization, kernel activities and TTY statistics, among
		others. Both UP and SMP machines are fully supported.
		The iostat command reports CPU utilization
		and I/O statistics for disks. The mpstat command reports
		global and per-processor statistics. The sadf command
		is used to display data collected by sar in various
		formats (XML, database-friendly, etc.).
		The pidstat command reports statistics for Linux tasks (processes).
		The nfsiostat command reports I/O statistics for network filesystems.
		The cifsiostat command reports I/O statistics for CIFS filesystems.
		NB: Send bugs, patches, suggestions and/or questions to
		(sysstat [at] orange.fr).
		URL: http://pagesperso-orange.fr/sebastien.godard/ 
Keywords:	system administration, sar, sadf, iostat, mpstat, pidstat, nfsiostat, cifsiostat, system accounting, performance, tuning
Author:		sysstat [at] orange.fr (Sebastien Godard)
Maintained-by:	sysstat [at] orange.fr (Sebastien Godard)
Primary-site:	http://pagesperso-orange.fr/sebastien.godard/
		384kB sysstat-10.2.1.tar.gz
		302kB sysstat-10.2.1.tar.bz2
		274kB sysstat-10.2.1.tar.xz
		388kB sysstat-10.2.1-1.src.rpm
		257kB sysstat-10.2.1-1.x86_64.rpm
Alternate-site:
Copying-policy:	GPL
End
