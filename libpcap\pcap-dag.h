/*
 * pcap-dag.h: Packet capture interface for Endace DAG card.
 *
 * The functionality of this code attempts to mimic that of pcap-linux as much
 * as possible.  This code is only needed when compiling in the DAG card code
 * at the same time as another type of device.
 *
 * Author: <PERSON>, <PERSON> ({richard,sean}@reeltwo.com)
 */

pcap_t *dag_create(const char *, char *, int *);
int dag_findalldevs(pcap_if_list_t *devlistp, char *errbuf);
