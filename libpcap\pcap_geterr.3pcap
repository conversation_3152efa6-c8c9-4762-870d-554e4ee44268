.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_GETERR 3PCAP "15 January 2016"
.SH NAME
pcap_geterr, pcap_perror \- get or print libpcap error message text
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
char *pcap_geterr(pcap_t *p);
void pcap_perror(pcap_t *p, const char *prefix);
.ft
.fi
.SH DESCRIPTION
.BR pcap_geterr ()
returns the error text pertaining to the last pcap library error.
.BR NOTE :
the pointer it returns will no longer point to a valid error message
string after the
.B pcap_t
passed to it is closed; you must use or copy the string before closing
the
.BR pcap_t .
.PP
.BR pcap_perror ()
prints the text of the last pcap library error on
.BR stderr ,
prefixed by
.IR prefix .
.SH SEE ALSO
.BR pcap (3PCAP),
.BR pcap_strerror (3PCAP)
