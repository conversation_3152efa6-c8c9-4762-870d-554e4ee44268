    1  14:20:20.060006 IP (tos 0x0, ttl 1, id 44530, offset 0, flags [none], proto UDP (17), length 84)
    *********.49998 > *********.49998: 
	LMPv1, msg-type: Begin Verify, Flags: [none], length: 56
	  Link ID Object (3), Class-Type: IPv4 Local (1) Flags: [non-negotiable], length: 8
	    IPv4 Link ID: ******* (0x01000000)
	  Message ID Object (5), Class-Type: 1 (1) Flags: [non-negotiable], length: 8
	    Message ID: 3 (0x00000003)
	  Link ID Object (3), Class-Type: IPv4 Remote (2) Flags: [non-negotiable], length: 8
	    IPv4 Link ID: ******* (0x01000000)
	  Verify Begin Object (8), Class-Type: 1 (1) Flags: [negotiable], length: 24
	    Flags: none
	    Verify Interval: 20
	    Data links: 30
	    Encoding type: Lambda (photonic)
	    Verify Transport Mechanism: 32768 (0x8000)
	    Transmission Rate: 0.001 Mbps
	    Wavelength: 8
    2  14:20:20.061756 IP (tos 0x0, ttl 1, id 44531, offset 0, flags [none], proto UDP (17), length 56)
    *********.49998 > *********.49998: 
	LMPv1, msg-type: Hello, Flags: [none], length: 28
	  Control Channel ID Object (1), Class-Type: Local (1) Flags: [non-negotiable], length: 8
	    Control Channel ID: 1 (0x00000001)
	  Hello Object (7), Class-Type: 1 (1) Flags: [non-negotiable], length: 12
	    Tx Seq: 50, Rx Seq: 60
    3  14:20:20.062080 IP (tos 0x0, ttl 1, id 44532, offset 0, flags [none], proto UDP (17), length 84)
    *********.49998 > *********.49998: 
	LMPv1, msg-type: Config NACK, Flags: [none], length: 56
	  Control Channel ID Object (1), Class-Type: Local (1) Flags: [non-negotiable], length: 8
	    Control Channel ID: 1 (0x00000001)
	  Node ID Object (2), Class-Type: Local (1) Flags: [non-negotiable], length: 8
	    Node ID: ********* (0x0a003201)
	  Control Channel ID Object (1), Class-Type: Remote (2) Flags: [non-negotiable], length: 8
	    Control Channel ID: 2 (0x00000002)
	  Message ID Object (5), Class-Type: 2 (2) Flags: [non-negotiable], length: 8
	    Message ID Ack: 3 (0x00000003)
	  Node ID Object (2), Class-Type: Remote (2) Flags: [non-negotiable], length: 8
	    Node ID: ********* (0x0a003202)
	  Configuration Object (6), Class-Type: 1 (1) Flags: [negotiable], length: 8
	    Hello Interval: 5
	    Hello Dead Interval: 15
    4  14:20:20.062335 IP (tos 0x0, ttl 1, id 44533, offset 0, flags [none], proto UDP (17), length 76)
    *********.49998 > *********.49998: 
	LMPv1, msg-type: Config ACK, Flags: [none], length: 48
	  Control Channel ID Object (1), Class-Type: Local (1) Flags: [non-negotiable], length: 8
	    Control Channel ID: 1 (0x00000001)
	  Node ID Object (2), Class-Type: Local (1) Flags: [non-negotiable], length: 8
	    Node ID: ********* (0x0a003201)
	  Control Channel ID Object (1), Class-Type: Remote (2) Flags: [non-negotiable], length: 8
	    Control Channel ID: 2 (0x00000002)
	  Message ID Object (5), Class-Type: 2 (2) Flags: [non-negotiable], length: 8
	    Message ID Ack: 3 (0x00000003)
	  Node ID Object (2), Class-Type: Remote (2) Flags: [non-negotiable], length: 8
	    Node ID: ********* (0x0a003202)
    5  14:20:20.062578 IP (tos 0x0, ttl 1, id 44534, offset 0, flags [none], proto UDP (17), length 68)
    *********.49998 > *********.49998: 
	LMPv1, msg-type: Config, Flags: [none], length: 40
	  Control Channel ID Object (1), Class-Type: Local (1) Flags: [non-negotiable], length: 8
	    Control Channel ID: 1 (0x00000001)
	  Message ID Object (5), Class-Type: 1 (1) Flags: [non-negotiable], length: 8
	    Message ID: 3 (0x00000003)
	  Node ID Object (2), Class-Type: Local (1) Flags: [non-negotiable], length: 8
	    Node ID: ********* (0x0a003201)
	  Configuration Object (6), Class-Type: 1 (1) Flags: [negotiable], length: 8
	    Hello Interval: 5
	    Hello Dead Interval: 15
    6  14:20:20.062787 IP (tos 0x0, ttl 1, id 44535, offset 0, flags [none], proto UDP (17), length 44)
    *********.49998 > *********.49998: 
	LMPv1, msg-type: Link Summary ACK, Flags: [none], length: 16
	  Message ID Object (5), Class-Type: 2 (2) Flags: [non-negotiable], length: 8
	    Message ID Ack: 1 (0x00000001)
    7  14:20:20.063397 IP (tos 0x0, ttl 1, id 44536, offset 0, flags [none], proto UDP (17), length 124)
    *********.49998 > *********.49998: 
	LMPv1, msg-type: Link Summary NACK, Flags: [none], length: 96
	  Message ID Object (5), Class-Type: 2 (2) Flags: [non-negotiable], length: 8
	    Message ID Ack: 1 (0x00000001)
	  Error Code Object (20), Class-Type: 2 (2) Flags: [non-negotiable], length: 8
	    Error Code: Unacceptable non-negotiable LINK-SUMMARY parameters, Renegotiate LINK-SUMMARY parameters, Invalid DATA-LINK Object, Unknown TE-LINK Object c-type, Unknown DATA-LINK Object c-type
	  Data Link Object (12), Class-Type: IPv4 (1) Flags: [non-negotiable], length: 36
	    Flags: [none]
	    Local Interface ID: *********** (0xc0a80101)
	    Remote Interface ID: *********** (0xc0a80102)
	    Subobject, Type: Interface Switching Type (1), Length: 12
	      Switching Type: Lambda-Switch Capable (150)
	      Encoding Type: Lambda (photonic) (8)
	      Min Reservable Bandwidth: 0.001 Mbps
	      Max Reservable Bandwidth: 0.001 Mbps
	    Subobject, Type: Wavelength (2), Length: 8
	      Wavelength: 6
	  Data Link Object (12), Class-Type: IPv4 (1) Flags: [non-negotiable], length: 36
	    Flags: [none]
	    Local Interface ID: ******** (0x0a010101)
	    Remote Interface ID: ******** (0x0a010102)
	    Subobject, Type: Interface Switching Type (1), Length: 12
	      Switching Type: Lambda-Switch Capable (150)
	      Encoding Type: ANSI/ETSI PDH (3)
	      Min Reservable Bandwidth: 9877.894 Mbps
	      Max Reservable Bandwidth: 10325.547 Mbps
	    Subobject, Type: Wavelength (2), Length: 8
	      Wavelength: 353
    8  14:20:20.063628 IP (tos 0x0, ttl 1, id 44537, offset 0, flags [none], proto UDP (17), length 68)
    *********.49998 > *********.49998: 
	LMPv1, msg-type: Begin Verify ACK, Flags: [none], length: 40
	  Link ID Object (3), Class-Type: IPv4 Local (1) Flags: [non-negotiable], length: 8
	    IPv4 Link ID: ******* (0x01000000)
	  Message ID Object (5), Class-Type: 2 (2) Flags: [non-negotiable], length: 8
	    Message ID Ack: 1 (0x00000001)
	  Verify Begin ACK Object (9), Class-Type: 1 (1) Flags: [negotiable], length: 8
	    Verify Dead Interval: 50
	    Verify Transport Response: 100
	  Verify ID Object (10), Class-Type: 1 (1) Flags: [non-negotiable], length: 8
	    Verify ID: 5
    9  14:20:20.063845 IP (tos 0x0, ttl 1, id 44538, offset 0, flags [none], proto UDP (17), length 60)
    *********.49998 > *********.49998: 
	LMPv1, msg-type: Begin Verify NACK, Flags: [none], length: 32
	  Link ID Object (3), Class-Type: IPv4 Local (1) Flags: [non-negotiable], length: 8
	    IPv4 Link ID: 10.0.0.0 (0x0a000000)
	  Message ID Object (5), Class-Type: 2 (2) Flags: [non-negotiable], length: 8
	    Message ID Ack: 3 (0x00000003)
	  Error Code Object (20), Class-Type: 1 (1) Flags: [non-negotiable], length: 8
	    Error Code: Link Verification Procedure Not supported, Unwilling to verify, Unsupported verification transport mechanism
   10  14:20:20.064049 IP (tos 0x0, ttl 1, id 44539, offset 0, flags [none], proto UDP (17), length 52)
    *********.49998 > *********.49998: 
	LMPv1, msg-type: End Verify, Flags: [none], length: 24
	  Message ID Object (5), Class-Type: 1 (1) Flags: [non-negotiable], length: 8
	    Message ID: 3 (0x00000003)
	  Verify ID Object (10), Class-Type: 1 (1) Flags: [non-negotiable], length: 8
	    Verify ID: 5
   11  14:20:20.064259 IP (tos 0x0, ttl 1, id 44540, offset 0, flags [none], proto UDP (17), length 52)
    *********.49998 > *********.49998: 
	LMPv1, msg-type: End Verify ACK, Flags: [none], length: 24
	  Message ID Object (5), Class-Type: 2 (2) Flags: [non-negotiable], length: 8
	    Message ID Ack: 3 (0x00000003)
	  Verify ID Object (10), Class-Type: 1 (1) Flags: [non-negotiable], length: 8
	    Verify ID: 5
   12  14:20:20.064464 IP (tos 0x0, ttl 1, id 44541, offset 0, flags [none], proto UDP (17), length 52)
    *********.49998 > *********.49998: 
	LMPv1, msg-type: Test, Flags: [none], length: 24
	  Interface ID Object (4), Class-Type: IPv4 Local (1) Flags: [non-negotiable], length: 8
	    IPv4 Link ID: ******* (0x01000000)
	  Verify ID Object (10), Class-Type: 1 (1) Flags: [non-negotiable], length: 8
	    Verify ID: 5
   13  14:20:20.064669 IP (tos 0x0, ttl 1, id 44542, offset 0, flags [none], proto UDP (17), length 52)
    *********.49998 > *********.49998: 
	LMPv1, msg-type: Test Status Failure, Flags: [none], length: 24
	  Message ID Object (5), Class-Type: 1 (1) Flags: [non-negotiable], length: 8
	    Message ID: 1 (0x00000001)
	  Verify ID Object (10), Class-Type: 1 (1) Flags: [non-negotiable], length: 8
	    Verify ID: 5
   14  14:20:20.064873 IP (tos 0x0, ttl 1, id 44543, offset 0, flags [none], proto UDP (17), length 52)
    *********.49998 > *********.49998: 
	LMPv1, msg-type: Test Status ACK, Flags: [none], length: 24
	  Message ID Object (5), Class-Type: 2 (2) Flags: [non-negotiable], length: 8
	    Message ID Ack: 1 (0x00000001)
	  Verify ID Object (10), Class-Type: 1 (1) Flags: [non-negotiable], length: 8
	    Verify ID: 5
   15  14:20:20.065080 IP (tos 0x0, ttl 1, id 44544, offset 0, flags [none], proto UDP (17), length 44)
    *********.49998 > *********.49998: 
	LMPv1, msg-type: Channel Status ACK, Flags: [none], length: 16
	  Message ID Object (5), Class-Type: 2 (2) Flags: [non-negotiable], length: 8
	    Message ID Ack: 3 (0x00000003)
   16  14:20:20.065317 IP (tos 0x0, ttl 1, id 44545, offset 0, flags [none], proto UDP (17), length 64)
    *********.49998 > *********.49998: 
	LMPv1, msg-type: Channel Status Request, Flags: [none], length: 36
	  Link ID Object (3), Class-Type: IPv4 Local (1) Flags: [non-negotiable], length: 8
	    IPv4 Link ID: ******* (0x01000000)
	  Message ID Object (5), Class-Type: 1 (1) Flags: [non-negotiable], length: 8
	    Message ID: 3 (0x00000003)
	  Channel Status Request Object (14), Class-Type: IPv4 (1) Flags: [non-negotiable], length: 12
	    Interface ID: ******* (0x02000000)
	    Interface ID: ******* (0x02000000)
   17  14:20:20.065542 IP (tos 0x0, ttl 1, id 44546, offset 0, flags [none], proto UDP (17), length 72)
    *********.49998 > *********.49998: 
	LMPv1, msg-type: Channel Status, Flags: [none], length: 44
	  Link ID Object (3), Class-Type: IPv4 Local (1) Flags: [non-negotiable], length: 8
	    IPv4 Link ID: ******* (0x01000000)
	  Message ID Object (5), Class-Type: 1 (1) Flags: [non-negotiable], length: 8
	    Message ID: 3 (0x00000003)
	  Channel Status Object (13), Class-Type: IPv4 (1) Flags: [non-negotiable], length: 20
	    Interface ID: ******* (0x01000000)
		    Active: Allocated (1)
		    Direction: Transmit (1)
		    Channel Status: Signal Fail (3)
	    Interface ID: ******* (0x01000000)
		    Active: Allocated (1)
		    Direction: Receive (0)
		    Channel Status: Signal Degraded (2)
   18  14:20:20.065749 IP (tos 0x0, ttl 1, id 44547, offset 0, flags [none], proto UDP (17), length 64)
    *********.49998 > *********.49998: 
	LMPv1, msg-type: Channel Status Response, Flags: [none], length: 36
	  Message ID Object (5), Class-Type: 2 (2) Flags: [non-negotiable], length: 8
	    Message ID Ack: 3 (0x00000003)
	  Channel Status Object (13), Class-Type: IPv4 (1) Flags: [non-negotiable], length: 20
	    Interface ID: ******* (0x01000000)
		    Active: Allocated (1)
		    Direction: Transmit (1)
		    Channel Status: Signal Degraded (2)
	    Interface ID: ******* (0x01000000)
		    Active: Allocated (1)
		    Direction: Transmit (1)
		    Channel Status: Signal Okay (1)
