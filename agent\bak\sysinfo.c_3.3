#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <unistd.h>
#include <sys/sysinfo.h>
#include "sysinfo.h"
#include <time.h>
#include <string.h>
#define FSHIFT		11		/* nr of bits of precision */
#define FIXED_1		(1<<FSHIFT)	/* 1.0 as fixed-point */  // =2048
#define LOAD_FREQ	(5*HZ+1)	/* 5 sec intervals */
#define EXP_1		1884		/* 1/exp(5sec/1min) as fixed-point */
#define EXP_5		2014		/* 1/exp(5sec/5min) */
#define EXP_15		2037		/* 1/exp(5sec/15min) */
#define LOAD_INT(x) ((x) >> FSHIFT) //计算整数
#define LOAD_FRAC(x) LOAD_INT(((x) & (FIXED_1-1)) * 100)//计算小数
//cpu info

#include <ctype.h>
#include <curses.h>
#ifndef NUMA_DISABLE
#include <dlfcn.h>
#endif
#include <errno.h>
#include <fcntl.h>
#include <float.h>
#include <limits.h>
#include <pwd.h>
#include <signal.h>
#include <stdarg.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <term.h>            // foul sob, defines all sorts of stuff...
#include <math.h>
#undef    tab
#undef    TTY
#include <termios.h>
#include <time.h>
#include <unistd.h>

#include <sys/ioctl.h>
#include <sys/resource.h>
#include <sys/select.h>      // also available via <sys/types.h>
#include <sys/time.h>
#include <sys/types.h>       // also available via <stdlib.h>
typedef          long long SIC_t;
 #define TRIMz(x)  ((tz = (SIC_t)(x)) < 0 ? 0 : tz)
   SIC_t u_frme, s_frme, n_frme, i_frme, w_frme, x_frme, y_frme, z_frme, tot_frme, tz;
   float scale;

#ifndef OFF_NUMASKIP
static int Numa_node_of_cpu(int num) { return (1 == (num % 4)) ? 0 : (num % 4); }
#else
static int Numa_node_of_cpu(int num) { return (num % 4); }
#endif
int smp_num_cpus;
#define CAPBUFSIZ    32
static int         Cpu_faux_tot;
typedef unsigned char FLG_t;
#define CAPTABMAX  9    
#define WINNAMSIZ  4 
#define CLRBUFSIZ    64
#define PFLAGSSIZ    80
#define GROUPSMAX  4     
#define SCREENMAX   512
static int Numa_node_tot;
typedef unsigned long long TIC_t;
typedef          long long SIC_t;
#define TICS_EDGE  20
#define CHKw(q,f)    (int)((q)->rc.winflags & (f))
#define View_CPUNOD  0x400000 
#define GRPNAMSIZ  WINNAMSIZ+2 
#define SIGNAL_STRING
#define QUICK_THREADS

typedef struct RCW_t {  // the 'window' portion of an rcfile
   int    sortindx,               // sort field (represented as procflag)
          winflags,               // 'view', 'show' and 'sort' mode flags
          maxtasks,               // user requested maximum, 0 equals all
          graph_cpus,             // 't' - View_STATES supplementary vals
          graph_mems,             // 'm' - View_MEMORY supplememtary vals
          summclr,                // a colors 'number' used for summ info
          msgsclr,                //             "           in msgs/pmts
          headclr,                //             "           in cols head
          taskclr;                //             "           in task rows
   char   winname [WINNAMSIZ],    // name for the window, user changeable
          fieldscur [PFLAGSSIZ];  // the fields for display & their order
} RCW_t;

typedef struct RCF_t {
   char   id;                   // rcfile version id
   int    mode_altscr;          // 'A' - Alt display mode (multi task windows)
   int    mode_irixps;          // 'I' - Irix vs. Solaris mode (SMP-only)
   float  delay_time;           // 'd'/'s' - How long to sleep twixt updates
   int    win_index;            // Curwin, as index
   RCW_t  win [GROUPSMAX];      // a 'WIN_t.rc' for each window
   int    fixed_widest;         // 'X' - wider non-scalable col addition
   int    summ_mscale;          // 'E' - scaling of summary memory values
   int    task_mscale;          // 'e' - scaling of process memory values
   int    zero_suppress;        // '0' - suppress scaled zeros toggle
} RCF_t;


 typedef struct CT_t {
   /* other kernels: u == user/us, n == nice/ni, s == system/sy, i == idle/id
      2.5.41 kernel: w == IO-wait/wa (io wait time)
      2.6.0  kernel: x == hi (hardware irq time), y == si (software irq time)
      2.6.11 kernel: z == st (virtual steal time) */
   TIC_t u, n, s, i, w, x, y, z;  // as represented in /proc/stat
#ifndef CPU_ZEROTICS
   SIC_t tot;                     // total from /proc/stat line 1
#endif
} CT_t;


 typedef struct CPU_t {
   CT_t cur;                      // current frame's cpu tics
   CT_t sav;                      // prior frame's cpu tics
#ifndef CPU_ZEROTICS
   SIC_t edge;                    // tics adjustment threshold boundary
#endif
   int id;                        // cpu number (0 - nn), or numa active flag
#ifndef NUMA_DISABLE
   int node;                      // the numa node it belongs to
#endif
} CPU_t;

#define MALLOC __attribute__ ((__malloc__))

static void *alloc_c (size_t num) MALLOC;
static void *alloc_c (size_t num) {
   void *pv;

   if (!num) ++num;
   if (!(pv = calloc(1, num)))
      perror("alloc_c failed");
   return pv;
} // end: alloc_c


static void *alloc_r (void *ptr, size_t num) MALLOC;
static void *alloc_r (void *ptr, size_t num) {
   void *pv;

   if (!num) ++num;
   if (!(pv = realloc(ptr, num)))
      perror("alloc_r failed");;
   return pv;
} // end: alloc_r


//end cpu info


float  active1=0;
float loads1,loads2,loads3;
float load1_old=-1;
float load5_old=-1;
float load15_old=-1;
int get_load()
{
  struct sysinfo si;
 
  sysinfo(&si);
  loads1=si.loads[0];
  loads2=si.loads[1];
  loads3=si.loads[2];

}
int output_load()
{
    
    float f=32;
    float e=10.24;
    get_load();
    float g0=round(loads1/f+e);
    float g1=round(loads2/f+e);
    float g2=round(loads3/f+e);
    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
         LOAD_INT((int)g0), LOAD_FRAC((int)g0),
               LOAD_INT((int)g1), LOAD_FRAC((int)g1),
               LOAD_INT((int)g2), LOAD_FRAC((int)g2));
//    return 0;
}

int get_time1(os_data *os)
{

   // 获取当前时间戳
    time_t now = time(NULL);
    
    // 转换为本地时间
    struct tm *local = localtime(&now);
    
    // 格式化时间为字符串
    //char time1[20];
  //  char* time1 = (char*)malloc(20 * sizeof(char)); 
	char *time2=alloc_c(20);
    
    strftime(time2, 20, "%Y-%m-%d %H:%M:%S", local);
	strcpy(os->time1,time2);
	free(time2);

}


char* get_time()
{

   // 获取当前时间戳
    time_t now = time(NULL);
   
    // 转换为本地时间
    struct tm *local = localtime(&now);
   
    // 格式化时间为字符串
    //char time1[20];
  //  char* time1 = (char*)malloc(20 * sizeof(char));
  	time1=alloc_c(20);

    strftime(time1, 20, "%Y-%m-%d %H:%M:%S", local);
        return time1;

}

int  avg_load(os_data *os)
{
    
    float f=32;
    float e=10.24;
    //char *avg_load1= (char*)malloc(5 * sizeof(char));
	char *avg_load1=alloc_c(5);
    //char  *avg_load1;
    //memset(avg_load1,0,5 * sizeof(char));
    get_load();
    float g0=round(loads1/f+e);
    float g1=round(loads2/f+e);
    float g2=round(loads3/f+e);
    int s1=LOAD_INT((int)g0);
    int s2=LOAD_FRAC((int)g0);
    sprintf(avg_load1, "%d%s%d", s1,".", s2);
    strcpy(os->avg_load11,avg_load1);
    free(avg_load1);
    
   // return avg_load1; 
    
    
    
    //float *load_avg=LOAD_INT((int)g0).LOAD_FRAC((int)g0);
    


}
//cpu func


        /*
         * This guy's modeled on libproc's 'eight_cpu_numbers' function except
         * we preserve all cpu data in our CPU_t array which is organized
         * as follows:
         *    cpus[0] thru cpus[n] == tics for each separate cpu
         *    cpus[sumSLOT]        == tics from the 1st /proc/stat line
         *  [ and beyond sumSLOT   == tics for each cpu NUMA node ] */
static CPU_t *cpus_refresh (CPU_t *cpus) {
 #define sumSLOT ( smp_num_cpus )
 #define totSLOT ( 1 + smp_num_cpus + Numa_node_tot)
   static FILE *fp = NULL;
   static int siz, sav_slot = -1;
   static char *buf;
   CPU_t *sum_ptr;                               // avoid gcc subscript bloat
   int i, num, tot_read;
#ifndef NUMA_DISABLE
   int node;
#endif
   char *bp;

   /*** hotplug_acclimated ***/
   if (sav_slot != sumSLOT) {
      sav_slot = sumSLOT;
      //zap_fieldstab();
      if (fp) { fclose(fp); fp = NULL; }
      if (cpus) { free(cpus); cpus = NULL; }
   }

   /* by opening this file once, we'll avoid the hit on minor page faults
      (sorry Linux, but you'll have to close it for us) */
   if (!fp) {
      if (!(fp = fopen("/proc/stat", "r")))
                 perror("fopen /proc/stat failed");
      /* note: we allocate one more CPU_t via totSLOT than 'cpus' so that a
               slot can hold tics representing the /proc/stat cpu summary */
      cpus = alloc_c(totSLOT * sizeof(CPU_t));
   }
   rewind(fp);
   fflush(fp);

 #define buffGRW 1024
   /* we slurp in the entire directory thus avoiding repeated calls to fgets,
      especially in a massively parallel environment.  additionally, each cpu
      line is then frozen in time rather than changing until we get around to
      accessing it.  this helps to minimize (not eliminate) most distortions. */
   tot_read = 0;
   if (buf) buf[0] = '\0';
   else buf = alloc_c((siz = buffGRW));
   while (0 < (num = fread(buf + tot_read, 1, (siz - tot_read), fp))) {
      tot_read += num;
      if (tot_read < siz) break;
      buf = alloc_r(buf, (siz += buffGRW));
   };
   buf[tot_read] = '\0';
   bp = buf;
 #undef buffGRW

   // remember from last time around
   sum_ptr = &cpus[sumSLOT];
   memcpy(&sum_ptr->sav, &sum_ptr->cur, sizeof(CT_t));
   // then value the last slot with the cpu summary line
   if (4 > sscanf(bp, "cpu %Lu %Lu %Lu %Lu %Lu %Lu %Lu %Lu"
      , &sum_ptr->cur.u, &sum_ptr->cur.n, &sum_ptr->cur.s
      , &sum_ptr->cur.i, &sum_ptr->cur.w, &sum_ptr->cur.x
      , &sum_ptr->cur.y, &sum_ptr->cur.z))
         perror("scanf failed");;
#ifndef CPU_ZEROTICS
   sum_ptr->cur.tot = sum_ptr->cur.u + sum_ptr->cur.s
      + sum_ptr->cur.n + sum_ptr->cur.i + sum_ptr->cur.w
      + sum_ptr->cur.x + sum_ptr->cur.y + sum_ptr->cur.z;
   /* if a cpu has registered substantially fewer tics than those expected,
      we'll force it to be treated as 'idle' so as not to present misleading
      percentages. */
   sum_ptr->edge =
      ((sum_ptr->cur.tot - sum_ptr->sav.tot) / smp_num_cpus) / (100 / TICS_EDGE);
#endif

#ifndef NUMA_DISABLE
   // forget all of the prior node statistics (maybe)
   //if (CHKw(Curwin, View_CPUNOD))
      memset(sum_ptr + 1, 0, Numa_node_tot * sizeof(CPU_t));
#endif

   // now value each separate cpu's tics...
   for (i = 0; i < sumSLOT; i++) {
      CPU_t *cpu_ptr = &cpus[i];               // avoid gcc subscript bloat
#ifdef PRETEND8CPUS
      bp = buf;
#endif
      bp = 1 + strchr(bp, '\n');
      // remember from last time around
      memcpy(&cpu_ptr->sav, &cpu_ptr->cur, sizeof(CT_t));
      if (4 > sscanf(bp, "cpu%d %Lu %Lu %Lu %Lu %Lu %Lu %Lu %Lu", &cpu_ptr->id
         , &cpu_ptr->cur.u, &cpu_ptr->cur.n, &cpu_ptr->cur.s
         , &cpu_ptr->cur.i, &cpu_ptr->cur.w, &cpu_ptr->cur.x
         , &cpu_ptr->cur.y, &cpu_ptr->cur.z)) {
            memmove(cpu_ptr, sum_ptr, sizeof(CPU_t));
            break;        // tolerate cpus taken offline
      }

#ifndef CPU_ZEROTICS
      cpu_ptr->edge = sum_ptr->edge;
#endif
#ifdef PRETEND8CPUS
      cpu_ptr->id = i;
#endif
#ifndef NUMA_DISABLE
      /* henceforth, with just a little more arithmetic we can avoid
         maintaining *any* node stats unless they're actually needed */
      if (0
      && Numa_node_tot
      && -1 < (node = Numa_node_of_cpu(cpu_ptr->id))) {
         // use our own pointer to avoid gcc subscript bloat
         CPU_t *nod_ptr = sum_ptr + 1 + node;
         nod_ptr->cur.u += cpu_ptr->cur.u; nod_ptr->sav.u += cpu_ptr->sav.u;
         nod_ptr->cur.n += cpu_ptr->cur.n; nod_ptr->sav.n += cpu_ptr->sav.n;
         nod_ptr->cur.s += cpu_ptr->cur.s; nod_ptr->sav.s += cpu_ptr->sav.s;
         nod_ptr->cur.i += cpu_ptr->cur.i; nod_ptr->sav.i += cpu_ptr->sav.i;
         nod_ptr->cur.w += cpu_ptr->cur.w; nod_ptr->sav.w += cpu_ptr->sav.w;
         nod_ptr->cur.x += cpu_ptr->cur.x; nod_ptr->sav.x += cpu_ptr->sav.x;
         nod_ptr->cur.y += cpu_ptr->cur.y; nod_ptr->sav.y += cpu_ptr->sav.y;
         nod_ptr->cur.z += cpu_ptr->cur.z; nod_ptr->sav.z += cpu_ptr->sav.z;
#ifndef CPU_ZEROTICS
         /* yep, we re-value this repeatedly for each cpu encountered, but we
            can then avoid a prior loop to selectively initialize each node */
         nod_ptr->edge = sum_ptr->edge;
#endif
         cpu_ptr->node = node;
#ifndef OFF_NUMASKIP
         nod_ptr->id = -1;
#endif
      }
#endif
   } // end: for each cpu

   Cpu_faux_tot = i;      // tolerate cpus taken offline

   return cpus;
 #undef sumSLOT
 #undef totSLOT
} // end: cpus_refresh







        /*
         * State display *Helper* function to calc and display the state
         * percentages for a single cpu.  In this way, we can support
         * the following environments without the usual code bloat.
         *    1) single cpu machines
         *    2) modest smp boxes with room for each cpu's percentages
         *    3) massive smp guys leaving little or no room for process
         *       display and thus requiring the cpu summary toggle */
static void summary_hlp (CPU_t *cpu,os_data* os) {
   /* we'll trim to zero if we get negative time ticks,
      which has happened with some SMP kernels (pre-2.4?)
      and when cpus are dynamically added or removed */

   u_frme = TRIMz(cpu->cur.u - cpu->sav.u);
   s_frme = TRIMz(cpu->cur.s - cpu->sav.s);
   n_frme = TRIMz(cpu->cur.n - cpu->sav.n);
   i_frme = TRIMz(cpu->cur.i - cpu->sav.i);
   w_frme = TRIMz(cpu->cur.w - cpu->sav.w);
   x_frme = TRIMz(cpu->cur.x - cpu->sav.x);
   y_frme = TRIMz(cpu->cur.y - cpu->sav.y);
   z_frme = TRIMz(cpu->cur.z - cpu->sav.z);
   tot_frme = u_frme + s_frme + n_frme + i_frme + w_frme + x_frme + y_frme + z_frme;
//   printf("cpu->id=%d tot_frme=%ld u_frme=%ld\n",cpu->id,tot_frme,u_frme);
#ifdef CPU_ZEROTICS
   if (1 > tot_frme) tot_frme = 1;
#else
   if (tot_frme < cpu->edge)
      tot_frme = u_frme = s_frme = n_frme = i_frme = w_frme = x_frme = y_frme = z_frme = 0;
   if (1 > tot_frme) i_frme = tot_frme = 1;
#endif
   scale = 100.0 / (float)tot_frme;

 //printf("(float)u_frme * scale=%f\n",(float)round(u_frme * scale*10)/10);      
   char s1[6],s2[6],s3[6],s4[6],s5[6],s6[6],s7[6],s8[6];
   sprintf(s1, "%.1f", (float)round(u_frme * scale*10)/10);
   sprintf(s2, "%.1f", (float)round(s_frme * scale*10)/10);
   sprintf(s3, "%.1f", (float)round(n_frme * scale*10)/10);
   sprintf(s4, "%.1f", (float)round(i_frme * scale*10)/10);
   sprintf(s5, "%.1f", (float)round(w_frme * scale*10)/10);
   sprintf(s6, "%.1f", (float)round(x_frme * scale*10)/10);
   sprintf(s7, "%.1f", (float)round(y_frme * scale*10)/10);
   sprintf(s8, "%.1f", (float)round(z_frme * scale*10)/10);
  os->cpu_p.u_frme=u_frme;
  os->cpu_p.u_f=cpu->cur.u;
  strcpy(os->cpu_p.u_frme_p, s1);
  strcpy(os->cpu_p.s_frme_p, s2);
  strcpy(os->cpu_p.n_frme_p, s3);
  strcpy(os->cpu_p.i_frme_p, s4);
  strcpy(os->cpu_p.w_frme_p, s5);
  strcpy(os->cpu_p.x_frme_p, s6);
  strcpy(os->cpu_p.y_frme_p, s7);
  strcpy(os->cpu_p.z_frme_p, s8);
  
   
   


   /* display some kinda' cpu state percentages
      (who or what is explained by the passed prefix) */
/*
   if (0) {
      static struct {
         const char *user, *syst, *type;
      } gtab[] = {
         { "%-.*s~7", "%-.*s~8", Graph_bars },
         { "%-.*s~4", "%-.*s~6", Graph_blks }
      };
      char user[SMLBUFSIZ], syst[SMLBUFSIZ], dual[MEDBUFSIZ];
      int ix = Curwin->rc.graph_cpus - 1;
      float pct_user = (float)(u_frme + n_frme) * scale,
            pct_syst = (float)s_frme * scale;
#ifndef QUICK_GRAPHS
      int num_user = (int)((pct_user * Graph_adj) + .5),
          num_syst = (int)((pct_syst * Graph_adj) + .5);
      if (num_user + num_syst > Graph_len) --num_syst;
      snprintf(user, sizeof(user), gtab[ix].user, num_user, gtab[ix].type);
      snprintf(syst, sizeof(syst), gtab[ix].syst, num_syst, gtab[ix].type);
#else
      snprintf(user, sizeof(user), gtab[ix].user, (int)((pct_user * Graph_adj) + .5), gtab[ix].type);
      snprintf(syst, sizeof(syst), gtab[ix].syst, (int)((pct_syst * Graph_adj) + .4), gtab[ix].type);
#endif
      snprintf(dual, sizeof(dual), "%s%s", user, syst);

      show_special(0, fmtmk("%%%s ~3%#5.1f~2/%-#5.1f~3 %3.0f[~1%-*s]~1\n"
         , pfx, pct_user, pct_syst, pct_user + pct_syst, Graph_len +4, dual));
   } else {
      show_special(0, fmtmk(Cpu_States_fmts, pfx
         , (float)u_frme * scale, (float)s_frme * scale
         , (float)n_frme * scale, (float)i_frme * scale
         , (float)w_frme * scale, (float)x_frme * scale
         , (float)y_frme * scale, (float)z_frme * scale));
   }
 #undef TRIMz
*/
} // end: summary_hlp









//end cpu func



//mem

/* This macro opens filename only if necessary and seeks to 0 so
 * that successive calls to the functions are more efficient.
 * It also reads the current contents of the file into the global buf.
 */
#define FILE_TO_BUF(filename, fd) do{                           \
    static int local_n;                                         \
    if (fd == -1 && (fd = open(filename, O_RDONLY)) == -1) {    \
        fputs(BAD_OPEN_MESSAGE, stderr);                        \
        fflush(NULL);                                           \
        _exit(102);                                             \
    }                                                           \
    lseek(fd, 0L, SEEK_SET);                                    \
    if ((local_n = read(fd, buf, sizeof buf - 1)) < 0) {        \
        perror(filename);                                       \
        fflush(NULL);                                           \
        _exit(103);                                             \
    }                                                           \
    buf[local_n] = '\0';                                        \
}while(0)
// As of 2.6.24 /proc/meminfo seems to need 888 on 64-bit,
// and would need 1258 if the obsolete fields were there.
// As of 3.13 /proc/vmstat needs 2623,
// and /proc/stat needs 3076.
static char buf[8192];
#define MEMINFO_FILE "/proc/meminfo"
static int meminfo_fd = -1;
#define BAD_OPEN_MESSAGE                                        \
"Error: /proc must be mounted\n"                                \
"  To mount /proc at boot you need an /etc/fstab line like:\n"  \
"      proc   /proc   proc    defaults\n"                       \
"  In the meantime, run \"mount proc /proc -t proc\"\n"

#define LINUX_VERSION(x,y,z)   (0x10000*(x) + 0x100*(y) + z)

int linux_version_code;
#define VM_MIN_FREE_FILE "/proc/sys/vm/min_free_kbytes"
static int vm_min_free_fd = -1;
/* return minimum of two values */
#define MIN(x,y) ((x) < (y) ? (x) : (y))

/***********************************************************************/
/*
 * Copyright 1999 by Albert Cahalan; all rights reserved.
 * This file may be used subject to the terms and conditions of the
 * GNU Library General Public License Version 2, or any later version
 * at your option, as published by the Free Software Foundation.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Library General Public License for more details.
 */

typedef struct mem_table_struct {
  const char *name;     /* memory type name */
  unsigned long *slot; /* slot in return struct */
} mem_table_struct;

static int compare_mem_table_structs(const void *a, const void *b){
  return strcmp(((const mem_table_struct*)a)->name,((const mem_table_struct*)b)->name);
}

/* example data, following junk, with comments added:
 *
 * MemTotal:        61768 kB    old
 * MemFree:          1436 kB    old
 * Buffers:          1312 kB    old
 * Cached:          20932 kB    old
 * Active:          12464 kB    new
 * Inact_dirty:      7772 kB    new
 * Inact_clean:      2008 kB    new
 * Inact_target:        0 kB    new
 * Inact_laundry:       0 kB    new, and might be missing too
 * HighTotal:           0 kB
 * HighFree:            0 kB
 * LowTotal:        61768 kB
 * LowFree:          1436 kB
 * SwapTotal:      122580 kB    old
 * SwapFree:        60352 kB    old
 * Inactive:        20420 kB    2.5.41+
 * Dirty:               0 kB    2.5.41+
 * Writeback:           0 kB    2.5.41+
 * Mapped:           9792 kB    2.5.41+
 * Shmem:              28 kB    2.6.32+
 * Slab:             4564 kB    2.5.41+
 * Committed_AS:     8440 kB    2.5.41+
 * PageTables:        304 kB    2.5.41+
 * ReverseMaps:      5738       2.5.41+
 * SwapCached:          0 kB    2.5.??+
 * HugePages_Total:   220       2.5.??+
 * HugePages_Free:    138       2.5.??+
 * Hugepagesize:     4096 kB    2.5.??+
 */

/* Shmem in 2.6.32+ */
unsigned long kb_main_shared;
/* old but still kicking -- the important stuff */
static unsigned long kb_page_cache;
unsigned long kb_main_buffers;
unsigned long kb_main_free;
unsigned long kb_main_total;
unsigned long kb_swap_free;
unsigned long kb_swap_total;
/* recently introduced */
unsigned long kb_high_free;
unsigned long kb_high_total;
unsigned long kb_low_free;
unsigned long kb_low_total;
unsigned long kb_main_available;
/* 2.4.xx era */
unsigned long kb_active;
unsigned long kb_inact_laundry;
unsigned long kb_inact_dirty;
unsigned long kb_inact_clean;
unsigned long kb_inact_target;
unsigned long kb_swap_cached;  /* late 2.4 and 2.6+ only */
/* derived values */
unsigned long kb_main_cached;
unsigned long kb_swap_used;
unsigned long kb_main_used;
/* 2.5.41+ */
unsigned long kb_writeback;
unsigned long kb_slab;
unsigned long nr_reversemaps;
unsigned long kb_committed_as;
unsigned long kb_dirty;
unsigned long kb_inactive;
unsigned long kb_mapped;
unsigned long kb_pagetables;
// seen on a 2.6.x kernel:
static unsigned long kb_vmalloc_chunk;
static unsigned long kb_vmalloc_total;
static unsigned long kb_vmalloc_used;
// seen on 2.6.24-rc6-git12
static unsigned long kb_anon_pages;
static unsigned long kb_bounce;
static unsigned long kb_commit_limit;
static unsigned long kb_nfs_unstable;
// seen on 2.6.18
static unsigned long kb_min_free;
// 2.6.19+
static unsigned long kb_slab_reclaimable;
static unsigned long kb_slab_unreclaimable;
// 2.6.27+
static unsigned long kb_active_file;
static unsigned long kb_inactive_file;


void meminfo(void){
  char namebuf[32]; /* big enough to hold any row name */
  mem_table_struct findme = { namebuf, NULL};
  mem_table_struct *found;
  char *head;
  char *tail;
  static const mem_table_struct mem_table[] = {
  {"Active",       &kb_active},       // important
  {"Active(file)", &kb_active_file},
  {"AnonPages",    &kb_anon_pages},
  {"Bounce",       &kb_bounce},
  {"Buffers",      &kb_main_buffers}, // important
  {"Cached",       &kb_page_cache},  // important
  {"CommitLimit",  &kb_commit_limit},
  {"Committed_AS", &kb_committed_as},
  {"Dirty",        &kb_dirty},        // kB version of vmstat nr_dirty
  {"HighFree",     &kb_high_free},
  {"HighTotal",    &kb_high_total},
  {"Inact_clean",  &kb_inact_clean},
  {"Inact_dirty",  &kb_inact_dirty},
  {"Inact_laundry",&kb_inact_laundry},
  {"Inact_target", &kb_inact_target},
  {"Inactive",     &kb_inactive},     // important
  {"Inactive(file)",&kb_inactive_file},
  {"LowFree",      &kb_low_free},
  {"LowTotal",     &kb_low_total},
  {"Mapped",       &kb_mapped},       // kB version of vmstat nr_mapped
  {"MemAvailable", &kb_main_available}, // important
  {"MemFree",      &kb_main_free},    // important
  {"MemTotal",     &kb_main_total},   // important
  {"NFS_Unstable", &kb_nfs_unstable},
  {"PageTables",   &kb_pagetables},   // kB version of vmstat nr_page_table_pages
  {"ReverseMaps",  &nr_reversemaps},  // same as vmstat nr_page_table_pages
  {"SReclaimable", &kb_slab_reclaimable}, // "slab reclaimable" (dentry and inode structures)
  {"SUnreclaim",   &kb_slab_unreclaimable},
  {"Shmem",        &kb_main_shared},  // kernel 2.6.32 and later
  {"Slab",         &kb_slab},         // kB version of vmstat nr_slab
  {"SwapCached",   &kb_swap_cached},
  {"SwapFree",     &kb_swap_free},    // important
  {"SwapTotal",    &kb_swap_total},   // important
  {"VmallocChunk", &kb_vmalloc_chunk},
  {"VmallocTotal", &kb_vmalloc_total},
  {"VmallocUsed",  &kb_vmalloc_used},
  {"Writeback",    &kb_writeback},    // kB version of vmstat nr_writeback
  };
  const int mem_table_count = sizeof(mem_table)/sizeof(mem_table_struct);
  unsigned long watermark_low;
  signed long mem_available;

  FILE_TO_BUF(MEMINFO_FILE,meminfo_fd);

  kb_inactive = ~0UL;
  kb_low_total = kb_main_available = 0;

  head = buf;
  for(;;){
    tail = strchr(head, ':');
    if(!tail) break;
    *tail = '\0';
    if(strlen(head) >= sizeof(namebuf)){
      head = tail+1;
      goto nextline;
    }
    strcpy(namebuf,head);
    found = bsearch(&findme, mem_table, mem_table_count,
        sizeof(mem_table_struct), compare_mem_table_structs
    );
    head = tail+1;
    if(!found) goto nextline;
    *(found->slot) = (unsigned long)strtoull(head,&tail,10);
nextline:
    tail = strchr(head, '\n');
    if(!tail) break;
    head = tail+1;
  }
  if(!kb_low_total){  /* low==main except with large-memory support */
    kb_low_total = kb_main_total;
    kb_low_free  = kb_main_free;
  }
  if(kb_inactive==~0UL){
    kb_inactive = kb_inact_dirty + kb_inact_clean + kb_inact_laundry;
  }
  kb_main_cached = kb_page_cache + kb_slab;
  kb_swap_used = kb_swap_total - kb_swap_free;
  kb_main_used = kb_main_total - kb_main_free - kb_main_cached - kb_main_buffers;


  /* zero? might need fallback for 2.6.27 <= kernel <? 3.14 */
  if (!kb_main_available) {
    if (linux_version_code < LINUX_VERSION(2, 6, 27))
      kb_main_available = kb_main_free;
    else {
      FILE_TO_BUF(VM_MIN_FREE_FILE, vm_min_free_fd);
      kb_min_free = (unsigned long) strtoull(buf,&tail,10);

      watermark_low = kb_min_free * 5 / 4; /* should be equal to sum of all 'low' fields in /proc/zoneinfo */

      mem_available = (signed long)kb_main_free - watermark_low
      + kb_inactive_file + kb_active_file - MIN((kb_inactive_file + kb_active_file) / 2, watermark_low)
      + kb_slab_reclaimable - MIN(kb_slab_reclaimable / 2, watermark_low);

      if (mem_available < 0) mem_available = 0;
      kb_main_available = (unsigned long)mem_available;
    }
  }

	 float pct_used = (float)kb_main_used * (100.0 / (float)kb_main_total);
	float	  pct_swap = kb_swap_total ? (float)kb_swap_used * (100.0 / (float)kb_swap_total):0;
	#ifdef MEMGRAPH_OLD
	float               pct_misc = (float)(kb_main_buffers + kb_main_cached) * (100.0 / (float)kb_main_total),
#else
	float               pct_misc = (float)(kb_main_total - kb_main_available - kb_main_used) * (100.0 / (float)kb_main_total);
#endif
	
	 printf("kb_main_used=%ld kb_main_total=%ld\n",kb_main_used,kb_main_total);
	 printf("pct_used=%.1f\n",pct_used);
	printf("pct_swap=%.1f\n",pct_swap);
	printf("pct_misc1=%.1f\n",(float)(kb_main_buffers + kb_main_cached) * (100.0 / (float)kb_main_total));
	printf("pct_misc2=%.1f\n",(float)(kb_main_total - kb_main_available - kb_main_used) * (100.0 / (float)kb_main_total));

}
//end mem
 
int sys()
{
    
//    float* arr = (float*)malloc(5 * sizeof(float));
    get_load();
    if ( load1_old ==-1 &&  load5_old == -1 &&  load15_old ==-1){ 
    	 load1_old=round(loads1/32);
         load5_old=round(loads2/32);
         load15_old=round(loads3/32);
    }
    sleep(5);
    float load1_new=round(loads1/32);
    float load5_new=round(loads2/32);
    float load15_new=round(loads3/32);
        if (active1*2048 >= load1_old)
         {
            active1=((( load1_new*2048-2047)-load1_old*1884)/164)/2048;
            active1=(active1 < 0) ? -active1 : active1;
          }
        else
        {
          active1=(((load1_new*2048)-load1_old*1884)/164)/2048;
          active1=(active1 < 0) ? -active1 : active1;

       }
//    unsigned int active1_1=round((round(((load1_new*2048-2047)-load1_old*1884)/164))/2048);
 //   unsigned int active5=(((load5_new*2048-2047)-load5_old*2014)/34)/2048;
  //  unsigned int active15=(((load15_new*2048-2047)-load15_old*2037)/11)/2048;
     //initialize struct
	os= alloc_c((sum_cores+1) * sizeof(os_data));
	os_data *os_sum=&os[sum_cores];
        //返回的字符指针转换为字符数组
      // strcpy(os->avg_load11, avg_load());
	avg_load(os_sum);
       //strcpy(os->time1, get_time());
	get_time1(os_sum);
      //对active1 四舍五入 
      int result = round(active1);
      os_sum->active1=result;
	//hostname set
	gethostname(os->hostname, sizeof(os->hostname));

	//cpu 
      int ii;
          smp_num_cpus = sysconf(_SC_NPROCESSORS_ONLN);
          os->cores=sum_cores;
         static CPU_t *smpcpu = NULL;
         smpcpu = cpus_refresh(smpcpu); //返回一个已经采集完信息的指针
         summary_hlp(&smpcpu[sum_cores],os_sum);
            // display each cpu's states separately, screen height permitting...
         for (ii = 0; ii < sum_cores;ii++ ) {
             printf("i=%d\n ", ii);
            os_data *os_cpus = &os[ii];  
            summary_hlp(&smpcpu[ii],os_cpus);
         }
    //end cpu           
	meminfo();
      
    
   // printf("active1_1=%d\n",active1_1);
    //printf("active5=%d\n",active5);
   // printf("active15=%d\n",active15);
//    return 1; 
    load1_old=load1_new;
    load5_old=load5_new;
    load15_old=load15_new;
//    return os;
}


