/*
 * We want a reentrant parser.
 */
%define api.pure

/*
 * We also want a reentrant scanner, so we have to pass the
 * handle for the reentrant scanner to the parser, and the
 * parser has to pass it to the lexical analyzer.
 *
 * We use void * rather than yyscan_t because, at least with some
 * versions of Flex and Bison, if you use yyscan_t in %parse-param and
 * %lex-param, you have to include scanner.h before grammar.h to get
 * yyscan_t declared, and you have to include grammar.h before scanner.h
 * to get YYSTYPE declared.  Using void * breaks the cycle; the Flex
 * documentation says yyscan_t is just a void *.
 */
%parse-param   {void *yyscanner}
%lex-param   {void *yyscanner}

/*
 * According to bison documentation, shift/reduce conflicts are not an issue
 * in most parsers as long as the number does not evolve over time:
 * https://www.gnu.org/software/bison/manual/html_node/Expect-Decl.html
 * So, following the advice use %expect to check the amount of shift/reduce
 * warnings.
 *
 * This doesn't appear to work in Berkeley YACC - 1.9 20170709; it still
 * warns of 38 shift/reduce conflicts.
 *
 * The Berkeley YACC documentation:
 *
 *    https://invisible-island.net/byacc/manpage/yacc.html
 *
 * claims that "<PERSON><PERSON>'s support for "%expect" is broken in more than one
 * release.", but doesn't give details.  Hopefully, that only means that
 * you get warnings even if you have the expected number of shift/reduce
 * conflicts, not that anything else fails.
 */
%expect 38

/*
 * And we need to pass the compiler state to the scanner.
 */
%parse-param { compiler_state_t *cstate }

%{
/*
 * Copyright (c) 1988, 1989, 1990, 1991, 1992, 1993, 1994, 1995, 1996
 *	The Regents of the University of California.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that: (1) source code distributions
 * retain the above copyright notice and this paragraph in its entirety, (2)
 * distributions including binary code include the above copyright notice and
 * this paragraph in its entirety in the documentation or other materials
 * provided with the distribution, and (3) all advertising materials mentioning
 * features or use of this software display the following acknowledgement:
 * ``This product includes software developed by the University of California,
 * Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
 * the University nor the names of its contributors may be used to endorse
 * or promote products derived from this software without specific prior
 * written permission.
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
 * WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
 *
 */

#include <config.h>

/*
 * grammar.h requires gencode.h and sometimes breaks in a polluted namespace
 * (see ftmacros.h), so include it early.
 */
#include "gencode.h"
#include "grammar.h"

#include <stdlib.h>

#include <stdio.h>

#include "diag-control.h"

#include "pcap-int.h"

#include "scanner.h"

#include "llc.h"
#include "ieee80211.h"
#include "pflog.h"
#include <pcap/namedb.h>

#ifdef HAVE_OS_PROTO_H
#include "os-proto.h"
#endif

/*
 * Work around some bugs in Berkeley YACC prior to the 2017-07-09
 * release.
 *
 * The 2005-05-05 release was the first one to define YYPATCH, so
 * we treat any release that either 1) doesn't define YYPATCH or
 * 2) defines it to a value < 20170709 as being buggy.
 */
#if defined(YYBYACC) && (!defined(YYPATCH) || YYPATCH < 20170709)
/*
 * Both Berkeley YACC and Bison define yydebug (under whatever name
 * it has) as a global, but Bison does so only if YYDEBUG is defined.
 * Berkeley YACC, prior to the 2017-07-09 release, defines it even if
 * YYDEBUG isn't defined; declare it here to suppress a warning.  The
 * 2017-07-09 release fixes that.
 */
#if !defined(YYDEBUG)
extern int yydebug;
#endif

/*
 * In Berkeley YACC, prior to the 2017-07-09 release, yynerrs (under
 * whatever name it has) is global, even if it's building a reentrant
 * parser.  In Bison, and in the Berkeley YACC 2017-07-09 release and
 * later, it's local in reentrant parsers.
 *
 * Declare it to squelch a warning.
 */
extern int yynerrs;
#endif

#define QSET(q, p, d, a) (q).proto = (unsigned char)(p),\
			 (q).dir = (unsigned char)(d),\
			 (q).addr = (unsigned char)(a)

struct tok {
	int v;			/* value */
	const char *s;		/* string */
};

static const struct tok ieee80211_types[] = {
	{ IEEE80211_FC0_TYPE_DATA, "data" },
	{ IEEE80211_FC0_TYPE_MGT, "mgt" },
	{ IEEE80211_FC0_TYPE_MGT, "management" },
	{ IEEE80211_FC0_TYPE_CTL, "ctl" },
	{ IEEE80211_FC0_TYPE_CTL, "control" },
	{ 0, NULL }
};
static const struct tok ieee80211_mgt_subtypes[] = {
	{ IEEE80211_FC0_SUBTYPE_ASSOC_REQ, "assocreq" },
	{ IEEE80211_FC0_SUBTYPE_ASSOC_REQ, "assoc-req" },
	{ IEEE80211_FC0_SUBTYPE_ASSOC_RESP, "assocresp" },
	{ IEEE80211_FC0_SUBTYPE_ASSOC_RESP, "assoc-resp" },
	{ IEEE80211_FC0_SUBTYPE_REASSOC_REQ, "reassocreq" },
	{ IEEE80211_FC0_SUBTYPE_REASSOC_REQ, "reassoc-req" },
	{ IEEE80211_FC0_SUBTYPE_REASSOC_RESP, "reassocresp" },
	{ IEEE80211_FC0_SUBTYPE_REASSOC_RESP, "reassoc-resp" },
	{ IEEE80211_FC0_SUBTYPE_PROBE_REQ, "probereq" },
	{ IEEE80211_FC0_SUBTYPE_PROBE_REQ, "probe-req" },
	{ IEEE80211_FC0_SUBTYPE_PROBE_RESP, "proberesp" },
	{ IEEE80211_FC0_SUBTYPE_PROBE_RESP, "probe-resp" },
	{ IEEE80211_FC0_SUBTYPE_BEACON, "beacon" },
	{ IEEE80211_FC0_SUBTYPE_ATIM, "atim" },
	{ IEEE80211_FC0_SUBTYPE_DISASSOC, "disassoc" },
	{ IEEE80211_FC0_SUBTYPE_DISASSOC, "disassociation" },
	{ IEEE80211_FC0_SUBTYPE_AUTH, "auth" },
	{ IEEE80211_FC0_SUBTYPE_AUTH, "authentication" },
	{ IEEE80211_FC0_SUBTYPE_DEAUTH, "deauth" },
	{ IEEE80211_FC0_SUBTYPE_DEAUTH, "deauthentication" },
	{ 0, NULL }
};
static const struct tok ieee80211_ctl_subtypes[] = {
	{ IEEE80211_FC0_SUBTYPE_BAR, "bar" },
	{ IEEE80211_FC0_SUBTYPE_BA, "ba" },
	{ IEEE80211_FC0_SUBTYPE_PS_POLL, "ps-poll" },
	{ IEEE80211_FC0_SUBTYPE_RTS, "rts" },
	{ IEEE80211_FC0_SUBTYPE_CTS, "cts" },
	{ IEEE80211_FC0_SUBTYPE_ACK, "ack" },
	{ IEEE80211_FC0_SUBTYPE_CF_END, "cf-end" },
	{ IEEE80211_FC0_SUBTYPE_CF_END_ACK, "cf-end-ack" },
	{ 0, NULL }
};
static const struct tok ieee80211_data_subtypes[] = {
	{ IEEE80211_FC0_SUBTYPE_DATA, "data" },
	{ IEEE80211_FC0_SUBTYPE_CF_ACK, "data-cf-ack" },
	{ IEEE80211_FC0_SUBTYPE_CF_POLL, "data-cf-poll" },
	{ IEEE80211_FC0_SUBTYPE_CF_ACPL, "data-cf-ack-poll" },
	{ IEEE80211_FC0_SUBTYPE_NODATA, "null" },
	{ IEEE80211_FC0_SUBTYPE_NODATA_CF_ACK, "cf-ack" },
	{ IEEE80211_FC0_SUBTYPE_NODATA_CF_POLL, "cf-poll"  },
	{ IEEE80211_FC0_SUBTYPE_NODATA_CF_ACPL, "cf-ack-poll" },
	{ IEEE80211_FC0_SUBTYPE_QOS|IEEE80211_FC0_SUBTYPE_DATA, "qos-data" },
	{ IEEE80211_FC0_SUBTYPE_QOS|IEEE80211_FC0_SUBTYPE_CF_ACK, "qos-data-cf-ack" },
	{ IEEE80211_FC0_SUBTYPE_QOS|IEEE80211_FC0_SUBTYPE_CF_POLL, "qos-data-cf-poll" },
	{ IEEE80211_FC0_SUBTYPE_QOS|IEEE80211_FC0_SUBTYPE_CF_ACPL, "qos-data-cf-ack-poll" },
	{ IEEE80211_FC0_SUBTYPE_QOS|IEEE80211_FC0_SUBTYPE_NODATA, "qos" },
	{ IEEE80211_FC0_SUBTYPE_QOS|IEEE80211_FC0_SUBTYPE_NODATA_CF_POLL, "qos-cf-poll" },
	{ IEEE80211_FC0_SUBTYPE_QOS|IEEE80211_FC0_SUBTYPE_NODATA_CF_ACPL, "qos-cf-ack-poll" },
	{ 0, NULL }
};
static const struct tok llc_s_subtypes[] = {
	{ LLC_RR, "rr" },
	{ LLC_RNR, "rnr" },
	{ LLC_REJ, "rej" },
	{ 0, NULL }
};
static const struct tok llc_u_subtypes[] = {
	{ LLC_UI, "ui" },
	{ LLC_UA, "ua" },
	{ LLC_DISC, "disc" },
	{ LLC_DM, "dm" },
	{ LLC_SABME, "sabme" },
	{ LLC_TEST, "test" },
	{ LLC_XID, "xid" },
	{ LLC_FRMR, "frmr" },
	{ 0, NULL }
};
struct type2tok {
	int type;
	const struct tok *tok;
};
static const struct type2tok ieee80211_type_subtypes[] = {
	{ IEEE80211_FC0_TYPE_MGT, ieee80211_mgt_subtypes },
	{ IEEE80211_FC0_TYPE_CTL, ieee80211_ctl_subtypes },
	{ IEEE80211_FC0_TYPE_DATA, ieee80211_data_subtypes },
	{ 0, NULL }
};

static int
str2tok(const char *str, const struct tok *toks)
{
	int i;

	for (i = 0; toks[i].s != NULL; i++) {
		if (pcapint_strcasecmp(toks[i].s, str) == 0) {
			/*
			 * Just in case somebody is using this to
			 * generate values of -1/0xFFFFFFFF.
			 * That won't work, as it's indistinguishable
			 * from an error.
			 */
			if (toks[i].v == -1)
				abort();
			return (toks[i].v);
		}
	}
	return (-1);
}

static const struct qual qerr = { Q_UNDEF, Q_UNDEF, Q_UNDEF, Q_UNDEF };

static void
yyerror(void *yyscanner _U_, compiler_state_t *cstate, const char *msg)
{
	bpf_set_error(cstate, "can't parse filter expression: %s", msg);
}

static const struct tok pflog_reasons[] = {
	{ PFRES_MATCH,		"match" },
	{ PFRES_BADOFF,		"bad-offset" },
	{ PFRES_FRAG,		"fragment" },
	{ PFRES_SHORT,		"short" },
	{ PFRES_NORM,		"normalize" },
	{ PFRES_MEMORY,		"memory" },
	{ PFRES_TS,		"bad-timestamp" },
	{ PFRES_CONGEST,	"congestion" },
	{ PFRES_IPOPTIONS,	"ip-option" },
	{ PFRES_PROTCKSUM,	"proto-cksum" },
	{ PFRES_BADSTATE,	"state-mismatch" },
	{ PFRES_STATEINS,	"state-insert" },
	{ PFRES_MAXSTATES,	"state-limit" },
	{ PFRES_SRCLIMIT,	"src-limit" },
	{ PFRES_SYNPROXY,	"synproxy" },
#if defined(__FreeBSD__)
	{ PFRES_MAPFAILED,	"map-failed" },
#elif defined(__NetBSD__)
	{ PFRES_STATELOCKED,	"state-locked" },
#elif defined(__OpenBSD__)
	{ PFRES_TRANSLATE,	"translate" },
	{ PFRES_NOROUTE,	"no-route" },
#elif defined(__APPLE__)
	{ PFRES_DUMMYNET,	"dummynet" },
#endif
	{ 0, NULL }
};

static int
pfreason_to_num(compiler_state_t *cstate, const char *reason)
{
	int i;

	i = str2tok(reason, pflog_reasons);
	if (i == -1)
		bpf_set_error(cstate, "unknown PF reason \"%s\"", reason);
	return (i);
}

static const struct tok pflog_actions[] = {
	{ PF_PASS,		"pass" },
	{ PF_PASS,		"accept" },	/* alias for "pass" */
	{ PF_DROP,		"drop" },
	{ PF_DROP,		"block" },	/* alias for "drop" */
	{ PF_SCRUB,		"scrub" },
	{ PF_NOSCRUB,		"noscrub" },
	{ PF_NAT,		"nat" },
	{ PF_NONAT,		"nonat" },
	{ PF_BINAT,		"binat" },
	{ PF_NOBINAT,		"nobinat" },
	{ PF_RDR,		"rdr" },
	{ PF_NORDR,		"nordr" },
	{ PF_SYNPROXY_DROP,	"synproxy-drop" },
#if defined(__FreeBSD__)
	{ PF_DEFER,		"defer" },
#elif defined(__OpenBSD__)
	{ PF_DEFER,		"defer" },
	{ PF_MATCH,		"match" },
	{ PF_DIVERT,		"divert" },
	{ PF_RT,		"rt" },
	{ PF_AFRT,		"afrt" },
#elif defined(__APPLE__)
	{ PF_DUMMYNET,		"dummynet" },
	{ PF_NODUMMYNET,	"nodummynet" },
	{ PF_NAT64,		"nat64" },
	{ PF_NONAT64,		"nonat64" },
#endif
	{ 0, NULL },
};

static int
pfaction_to_num(compiler_state_t *cstate, const char *action)
{
	int i;

	i = str2tok(action, pflog_actions);
	if (i == -1)
		bpf_set_error(cstate, "unknown PF action \"%s\"", action);
	return (i);
}

/*
 * For calls that might return an "an error occurred" value.
 */
#define CHECK_INT_VAL(val)	if (val == -1) YYABORT
#define CHECK_PTR_VAL(val)	if (val == NULL) YYABORT

DIAG_OFF_BISON_BYACC
%}

%union {
	int i;
	bpf_u_int32 h;
	char *s;
	struct stmt *stmt;
	struct arth *a;
	struct {
		struct qual q;
		int atmfieldtype;
		int mtp3fieldtype;
		struct block *b;
	} blk;
	struct block *rblk;
}

%type	<blk>	expr id nid pid term rterm qid
%type	<blk>	head
%type	<i>	pqual dqual aqual ndaqual
%type	<a>	arth narth
%type	<i>	byteop pname relop irelop
%type	<h>	pnum
%type	<blk>	and or paren not null prog
%type	<rblk>	other pfvar p80211 pllc
%type	<i>	atmtype atmmultitype
%type	<blk>	atmfield
%type	<blk>	atmfieldvalue atmvalue atmlistvalue
%type	<i>	mtp2type
%type	<blk>	mtp3field
%type	<blk>	mtp3fieldvalue mtp3value mtp3listvalue


%token  DST SRC HOST GATEWAY
%token  NET NETMASK PORT PORTRANGE LESS GREATER PROTO PROTOCHAIN CBYTE
%token  ARP RARP IP SCTP TCP UDP ICMP IGMP IGRP PIM VRRP CARP
%token  ATALK AARP DECNET LAT SCA MOPRC MOPDL
%token  TK_BROADCAST TK_MULTICAST
%token  NUM INBOUND OUTBOUND
%token  IFINDEX
%token  PF_IFNAME PF_RSET PF_RNR PF_SRNR PF_REASON PF_ACTION
%token	TYPE SUBTYPE DIR ADDR1 ADDR2 ADDR3 ADDR4 RA TA
%token  LINK
%token	GEQ LEQ NEQ
%token	ID EID HID HID6 AID
%token	LSH RSH
%token  LEN
%token  IPV6 ICMPV6 AH ESP
%token	VLAN MPLS
%token	PPPOED PPPOES GENEVE VXLAN
%token  ISO ESIS CLNP ISIS L1 L2 IIH LSP SNP CSNP PSNP
%token  STP
%token  IPX
%token  NETBEUI
%token	LANE LLC METAC BCC SC ILMIC OAMF4EC OAMF4SC
%token	OAM OAMF4 CONNECTMSG METACONNECT
%token	VPI VCI
%token	RADIO
%token	FISU LSSU MSU HFISU HLSSU HMSU
%token	SIO OPC DPC SLS HSIO HOPC HDPC HSLS
%token	LEX_ERROR

%type	<s> ID EID AID
%type	<s> HID HID6
%type	<h> NUM
%type	<i> action reason type subtype type_subtype dir

%left OR AND
%nonassoc  '!'
%left '|'
%left '&'
%left LSH RSH
%left '+' '-'
%left '*' '/'
%nonassoc UMINUS
%%
prog:	  null expr
{
	/*
	 * I'm not sure we have a reason to use yynerrs, but it's
	 * declared, and incremented, whether we need it or not,
	 * which means that Clang 15 will give a "set but not
	 * used" warning.  This should suppress the warning for
	 * yynerrs without suppressing it for other variables.
	 */
	(void) yynerrs;
	CHECK_INT_VAL(finish_parse(cstate, $2.b));
}
	| null
	;
null:	  /* null */		{ $$.q = qerr; }
	;
expr:	  term
	| expr and term		{ gen_and($1.b, $3.b); $$ = $3; }
	| expr and id		{ gen_and($1.b, $3.b); $$ = $3; }
	| expr or term		{ gen_or($1.b, $3.b); $$ = $3; }
	| expr or id		{ gen_or($1.b, $3.b); $$ = $3; }
	;
and:	  AND			{ $$ = $<blk>0; }
	;
or:	  OR			{ $$ = $<blk>0; }
	;
id:	  nid
	| pnum			{ CHECK_PTR_VAL(($$.b = gen_ncode(cstate, NULL, $1,
						   $$.q = $<blk>0.q))); }
	| paren pid ')'		{ $$ = $2; }
	;
nid:	  ID			{ CHECK_PTR_VAL($1); CHECK_PTR_VAL(($$.b = gen_scode(cstate, $1, $$.q = $<blk>0.q))); }
	| HID '/' NUM		{
				  CHECK_PTR_VAL($1);
				  /* Check whether HID/NUM is being used when appropriate */
				  $$.q = $<blk>0.q;
				  if ($$.q.addr == Q_PORT) {
					bpf_set_error(cstate, "'port' qualifier applied to IPv4 address and prefix length");
					YYABORT;
				  } else if ($$.q.addr == Q_PORTRANGE) {
					bpf_set_error(cstate, "'portrange' qualifier applied to IPv4 address and prefix length");
					YYABORT;
				  } else if ($$.q.addr == Q_PROTO) {
					bpf_set_error(cstate, "'proto' qualifier applied to IPv4 address and prefix length");
					YYABORT;
				  } else if ($$.q.addr == Q_PROTOCHAIN) {
					bpf_set_error(cstate, "'protochain' qualifier applied to IPv4 address and prefix length");
					YYABORT;
				  }
				  /* The default case in gen_mcode() catches Q_HOST and Q_GATEWAY. */
				  CHECK_PTR_VAL(($$.b = gen_mcode(cstate, $1, NULL, $3, $$.q)));
				}
	| HID NETMASK HID	{
				  CHECK_PTR_VAL($1);
				  /* Check whether HID mask HID is being used when appropriate */
				  $$.q = $<blk>0.q;
				  if ($$.q.addr == Q_PORT) {
					bpf_set_error(cstate, "'port' qualifier applied to IPv4 address and netmask");
					YYABORT;
				  } else if ($$.q.addr == Q_PORTRANGE) {
					bpf_set_error(cstate, "'portrange' qualifier applied to IPv4 address and netmask");
					YYABORT;
				  } else if ($$.q.addr == Q_PROTO) {
					bpf_set_error(cstate, "'proto' qualifier applied to IPv4 address and netmask");
					YYABORT;
				  } else if ($$.q.addr == Q_PROTOCHAIN) {
					bpf_set_error(cstate, "'protochain' qualifier applied to IPv4 address and netmask");
					YYABORT;
				  }
				  /* The default case in gen_mcode() catches Q_HOST and Q_GATEWAY. */
				  CHECK_PTR_VAL(($$.b = gen_mcode(cstate, $1, $3, 0, $$.q)));
				}
	| HID			{
				  CHECK_PTR_VAL($1);
				  /* Check whether HID is being used when appropriate */
				  $$.q = $<blk>0.q;
				  if ($$.q.addr == Q_PORT) {
					bpf_set_error(cstate, "'port' qualifier applied to IPv4 address");
					YYABORT;
				  } else if ($$.q.addr == Q_PORTRANGE) {
					bpf_set_error(cstate, "'portrange' qualifier applied to IPv4 address");
					YYABORT;
				  } else if ($$.q.addr == Q_PROTO) {
					bpf_set_error(cstate, "'proto' qualifier applied to IPv4 address");
					YYABORT;
				  } else if ($$.q.addr == Q_PROTOCHAIN) {
					bpf_set_error(cstate, "'protochain' qualifier applied to IPv4 address");
					YYABORT;
				  }
				  CHECK_PTR_VAL(($$.b = gen_ncode(cstate, $1, 0, $$.q)));
				}
	| HID6 '/' NUM		{
				  CHECK_PTR_VAL($1);
				  /* Check whether HID6/NUM is being used when appropriate */
				  $$.q = $<blk>0.q;
				  if ($$.q.addr == Q_PORT) {
					bpf_set_error(cstate, "'port' qualifier applied to IPv6 address and prefix length");
					YYABORT;
				  } else if ($$.q.addr == Q_PORTRANGE) {
					bpf_set_error(cstate, "'portrange' qualifier applied to IPv6 address and prefix length");
					YYABORT;
				  } else if ($$.q.addr == Q_PROTO) {
					bpf_set_error(cstate, "'proto' qualifier applied to IPv6 address and prefix length ");
					YYABORT;
				  } else if ($$.q.addr == Q_PROTOCHAIN) {
					bpf_set_error(cstate, "'protochain' qualifier applied to IPv6 address and prefix length");
					YYABORT;
				  }
				  /* The default case in gen_mcode6() catches Q_GATEWAY. */
				  CHECK_PTR_VAL(($$.b = gen_mcode6(cstate, $1, $3, $$.q)));
				}
	| HID6			{
				  CHECK_PTR_VAL($1);
				  /* Check whether HID6 is being used when appropriate */
				  $$.q = $<blk>0.q;
				  if ($$.q.addr == Q_PORT) {
					bpf_set_error(cstate, "'port' qualifier applied to IPv6 address");
					YYABORT;
				  } else if ($$.q.addr == Q_PORTRANGE) {
					bpf_set_error(cstate, "'portrange' qualifier applied to IPv6 address");
					YYABORT;
				  } else if ($$.q.addr == Q_PROTO) {
					bpf_set_error(cstate, "'proto' qualifier applied to IPv6 address");
					YYABORT;
				  } else if ($$.q.addr == Q_PROTOCHAIN) {
					bpf_set_error(cstate, "'protochain' qualifier applied to IPv6 address");
					YYABORT;
				  }
				  /* The default case in gen_mcode6() catches Q_GATEWAY. */
				  CHECK_PTR_VAL(($$.b = gen_mcode6(cstate, $1, 128, $$.q)));
				}
	| EID			{ CHECK_PTR_VAL($1); CHECK_PTR_VAL(($$.b = gen_ecode(cstate, $1, $$.q = $<blk>0.q))); }
	| AID			{ CHECK_PTR_VAL($1); CHECK_PTR_VAL(($$.b = gen_acode(cstate, $1, $$.q = $<blk>0.q))); }
	| not id		{ gen_not($2.b); $$ = $2; }
	;
not:	  '!'			{ $$ = $<blk>0; }
	;
paren:	  '('			{ $$ = $<blk>0; }
	;
pid:	  nid
	| qid and id		{ gen_and($1.b, $3.b); $$ = $3; }
	| qid or id		{ gen_or($1.b, $3.b); $$ = $3; }
	;
qid:	  pnum			{ CHECK_PTR_VAL(($$.b = gen_ncode(cstate, NULL, $1,
						   $$.q = $<blk>0.q))); }
	| pid
	;
term:	  rterm
	| not term		{ gen_not($2.b); $$ = $2; }
	;
head:	  pqual dqual aqual	{ QSET($$.q, $1, $2, $3); }
	| pqual dqual		{ QSET($$.q, $1, $2, Q_DEFAULT); }
	| pqual aqual		{ QSET($$.q, $1, Q_DEFAULT, $2); }
	| pqual PROTO		{ QSET($$.q, $1, Q_DEFAULT, Q_PROTO); }
	| pqual PROTOCHAIN	{
#ifdef NO_PROTOCHAIN
				  bpf_set_error(cstate, "protochain not supported");
				  YYABORT;
#else
				  QSET($$.q, $1, Q_DEFAULT, Q_PROTOCHAIN);
#endif
				}
	| pqual ndaqual		{ QSET($$.q, $1, Q_DEFAULT, $2); }
	;
rterm:	  head id		{ $$ = $2; }
	| paren expr ')'	{ $$.b = $2.b; $$.q = $1.q; }
	| pname			{ CHECK_PTR_VAL(($$.b = gen_proto_abbrev(cstate, $1))); $$.q = qerr; }
	| arth relop arth	{ CHECK_PTR_VAL(($$.b = gen_relation(cstate, $2, $1, $3, 0)));
				  $$.q = qerr; }
	| arth irelop arth	{ CHECK_PTR_VAL(($$.b = gen_relation(cstate, $2, $1, $3, 1)));
				  $$.q = qerr; }
	| other			{ $$.b = $1; $$.q = qerr; }
	| atmtype		{ CHECK_PTR_VAL(($$.b = gen_atmtype_abbrev(cstate, $1))); $$.q = qerr; }
	| atmmultitype		{ CHECK_PTR_VAL(($$.b = gen_atmmulti_abbrev(cstate, $1))); $$.q = qerr; }
	| atmfield atmvalue	{ $$.b = $2.b; $$.q = qerr; }
	| mtp2type		{ CHECK_PTR_VAL(($$.b = gen_mtp2type_abbrev(cstate, $1))); $$.q = qerr; }
	| mtp3field mtp3value	{ $$.b = $2.b; $$.q = qerr; }
	;
/* protocol level qualifiers */
pqual:	  pname
	|			{ $$ = Q_DEFAULT; }
	;
/* 'direction' qualifiers */
dqual:	  SRC			{ $$ = Q_SRC; }
	| DST			{ $$ = Q_DST; }
	| SRC OR DST		{ $$ = Q_OR; }
	| DST OR SRC		{ $$ = Q_OR; }
	| SRC AND DST		{ $$ = Q_AND; }
	| DST AND SRC		{ $$ = Q_AND; }
	| ADDR1			{ $$ = Q_ADDR1; }
	| ADDR2			{ $$ = Q_ADDR2; }
	| ADDR3			{ $$ = Q_ADDR3; }
	| ADDR4			{ $$ = Q_ADDR4; }
	| RA			{ $$ = Q_RA; }
	| TA			{ $$ = Q_TA; }
	;
/* address type qualifiers */
aqual:	  HOST			{ $$ = Q_HOST; }
	| NET			{ $$ = Q_NET; }
	| PORT			{ $$ = Q_PORT; }
	| PORTRANGE		{ $$ = Q_PORTRANGE; }
	;
/* non-directional address type qualifiers */
ndaqual:  GATEWAY		{ $$ = Q_GATEWAY; }
	;
pname:	  LINK			{ $$ = Q_LINK; }
	| IP			{ $$ = Q_IP; }
	| ARP			{ $$ = Q_ARP; }
	| RARP			{ $$ = Q_RARP; }
	| SCTP			{ $$ = Q_SCTP; }
	| TCP			{ $$ = Q_TCP; }
	| UDP			{ $$ = Q_UDP; }
	| ICMP			{ $$ = Q_ICMP; }
	| IGMP			{ $$ = Q_IGMP; }
	| IGRP			{ $$ = Q_IGRP; }
	| PIM			{ $$ = Q_PIM; }
	| VRRP			{ $$ = Q_VRRP; }
	| CARP			{ $$ = Q_CARP; }
	| ATALK			{ $$ = Q_ATALK; }
	| AARP			{ $$ = Q_AARP; }
	| DECNET		{ $$ = Q_DECNET; }
	| LAT			{ $$ = Q_LAT; }
	| SCA			{ $$ = Q_SCA; }
	| MOPDL			{ $$ = Q_MOPDL; }
	| MOPRC			{ $$ = Q_MOPRC; }
	| IPV6			{ $$ = Q_IPV6; }
	| ICMPV6		{ $$ = Q_ICMPV6; }
	| AH			{ $$ = Q_AH; }
	| ESP			{ $$ = Q_ESP; }
	| ISO			{ $$ = Q_ISO; }
	| ESIS			{ $$ = Q_ESIS; }
	| ISIS			{ $$ = Q_ISIS; }
	| L1			{ $$ = Q_ISIS_L1; }
	| L2			{ $$ = Q_ISIS_L2; }
	| IIH			{ $$ = Q_ISIS_IIH; }
	| LSP			{ $$ = Q_ISIS_LSP; }
	| SNP			{ $$ = Q_ISIS_SNP; }
	| PSNP			{ $$ = Q_ISIS_PSNP; }
	| CSNP			{ $$ = Q_ISIS_CSNP; }
	| CLNP			{ $$ = Q_CLNP; }
	| STP			{ $$ = Q_STP; }
	| IPX			{ $$ = Q_IPX; }
	| NETBEUI		{ $$ = Q_NETBEUI; }
	| RADIO			{ $$ = Q_RADIO; }
	;
other:	  pqual TK_BROADCAST	{ CHECK_PTR_VAL(($$ = gen_broadcast(cstate, $1))); }
	| pqual TK_MULTICAST	{ CHECK_PTR_VAL(($$ = gen_multicast(cstate, $1))); }
	| LESS NUM		{ CHECK_PTR_VAL(($$ = gen_less(cstate, $2))); }
	| GREATER NUM		{ CHECK_PTR_VAL(($$ = gen_greater(cstate, $2))); }
	| CBYTE NUM byteop NUM	{ CHECK_PTR_VAL(($$ = gen_byteop(cstate, $3, $2, $4))); }
	| INBOUND		{ CHECK_PTR_VAL(($$ = gen_inbound_outbound(cstate, 0))); }
	| OUTBOUND		{ CHECK_PTR_VAL(($$ = gen_inbound_outbound(cstate, 1))); }
	| IFINDEX NUM		{ CHECK_PTR_VAL(($$ = gen_ifindex(cstate, $2))); }
	| VLAN pnum		{ CHECK_PTR_VAL(($$ = gen_vlan(cstate, $2, 1))); }
	| VLAN			{ CHECK_PTR_VAL(($$ = gen_vlan(cstate, 0, 0))); }
	| MPLS pnum		{ CHECK_PTR_VAL(($$ = gen_mpls(cstate, $2, 1))); }
	| MPLS			{ CHECK_PTR_VAL(($$ = gen_mpls(cstate, 0, 0))); }
	| PPPOED		{ CHECK_PTR_VAL(($$ = gen_pppoed(cstate))); }
	| PPPOES pnum		{ CHECK_PTR_VAL(($$ = gen_pppoes(cstate, $2, 1))); }
	| PPPOES		{ CHECK_PTR_VAL(($$ = gen_pppoes(cstate, 0, 0))); }
	| GENEVE pnum		{ CHECK_PTR_VAL(($$ = gen_geneve(cstate, $2, 1))); }
	| GENEVE		{ CHECK_PTR_VAL(($$ = gen_geneve(cstate, 0, 0))); }
	| VXLAN pnum		{ CHECK_PTR_VAL(($$ = gen_vxlan(cstate, $2, 1))); }
	| VXLAN			{ CHECK_PTR_VAL(($$ = gen_vxlan(cstate, 0, 0))); }
	| pfvar			{ $$ = $1; }
	| pqual p80211		{ $$ = $2; }
	| pllc			{ $$ = $1; }
	;

pfvar:	  PF_IFNAME ID		{ CHECK_PTR_VAL($2); CHECK_PTR_VAL(($$ = gen_pf_ifname(cstate, $2))); }
	| PF_RSET ID		{ CHECK_PTR_VAL($2); CHECK_PTR_VAL(($$ = gen_pf_ruleset(cstate, $2))); }
	| PF_RNR NUM		{ CHECK_PTR_VAL(($$ = gen_pf_rnr(cstate, $2))); }
	| PF_SRNR NUM		{ CHECK_PTR_VAL(($$ = gen_pf_srnr(cstate, $2))); }
	| PF_REASON reason	{ CHECK_PTR_VAL(($$ = gen_pf_reason(cstate, $2))); }
	| PF_ACTION action	{ CHECK_PTR_VAL(($$ = gen_pf_action(cstate, $2))); }
	;

p80211:   TYPE type SUBTYPE subtype
				{ CHECK_PTR_VAL(($$ = gen_p80211_type(cstate, $2 | $4,
					IEEE80211_FC0_TYPE_MASK |
					IEEE80211_FC0_SUBTYPE_MASK)));
				}
	| TYPE type		{ CHECK_PTR_VAL(($$ = gen_p80211_type(cstate, $2,
					IEEE80211_FC0_TYPE_MASK)));
				}
	| SUBTYPE type_subtype	{ CHECK_PTR_VAL(($$ = gen_p80211_type(cstate, $2,
					IEEE80211_FC0_TYPE_MASK |
					IEEE80211_FC0_SUBTYPE_MASK)));
				}
	| DIR dir		{ CHECK_PTR_VAL(($$ = gen_p80211_fcdir(cstate, $2))); }
	;

type:	  NUM			{ if (($1 & (~IEEE80211_FC0_TYPE_MASK)) != 0) {
					bpf_set_error(cstate, "invalid 802.11 type value 0x%02x", $1);
					YYABORT;
				  }
				  $$ = (int)$1;
				}
	| ID			{ CHECK_PTR_VAL($1);
				  $$ = str2tok($1, ieee80211_types);
				  if ($$ == -1) {
					bpf_set_error(cstate, "unknown 802.11 type name \"%s\"", $1);
					YYABORT;
				  }
				}
	;

subtype:  NUM			{ if (($1 & (~IEEE80211_FC0_SUBTYPE_MASK)) != 0) {
					bpf_set_error(cstate, "invalid 802.11 subtype value 0x%02x", $1);
					YYABORT;
				  }
				  $$ = (int)$1;
				}
	| ID			{ const struct tok *types = NULL;
				  int i;
				  CHECK_PTR_VAL($1);
				  for (i = 0;; i++) {
					if (ieee80211_type_subtypes[i].tok == NULL) {
						/* Ran out of types */
						bpf_set_error(cstate, "unknown 802.11 type");
						YYABORT;
					}
					if ($<i>-1 == ieee80211_type_subtypes[i].type) {
						types = ieee80211_type_subtypes[i].tok;
						break;
					}
				  }

				  $$ = str2tok($1, types);
				  if ($$ == -1) {
					bpf_set_error(cstate, "unknown 802.11 subtype name \"%s\"", $1);
					YYABORT;
				  }
				}
	;

type_subtype:	ID		{ int i;
				  CHECK_PTR_VAL($1);
				  for (i = 0;; i++) {
					if (ieee80211_type_subtypes[i].tok == NULL) {
						/* Ran out of types */
						bpf_set_error(cstate, "unknown 802.11 type name");
						YYABORT;
					}
					$$ = str2tok($1, ieee80211_type_subtypes[i].tok);
					if ($$ != -1) {
						$$ |= ieee80211_type_subtypes[i].type;
						break;
					}
				  }
				}
		;

pllc:	LLC			{ CHECK_PTR_VAL(($$ = gen_llc(cstate))); }
	| LLC ID		{ CHECK_PTR_VAL($2);
				  if (pcapint_strcasecmp($2, "i") == 0) {
					CHECK_PTR_VAL(($$ = gen_llc_i(cstate)));
				  } else if (pcapint_strcasecmp($2, "s") == 0) {
					CHECK_PTR_VAL(($$ = gen_llc_s(cstate)));
				  } else if (pcapint_strcasecmp($2, "u") == 0) {
					CHECK_PTR_VAL(($$ = gen_llc_u(cstate)));
				  } else {
					int subtype;

					subtype = str2tok($2, llc_s_subtypes);
					if (subtype != -1) {
						CHECK_PTR_VAL(($$ = gen_llc_s_subtype(cstate, subtype)));
					} else {
						subtype = str2tok($2, llc_u_subtypes);
						if (subtype == -1) {
							bpf_set_error(cstate, "unknown LLC type name \"%s\"", $2);
							YYABORT;
						}
						CHECK_PTR_VAL(($$ = gen_llc_u_subtype(cstate, subtype)));
					}
				  }
				}
				/* sigh, "rnr" is already a keyword for PF */
	| LLC PF_RNR		{ CHECK_PTR_VAL(($$ = gen_llc_s_subtype(cstate, LLC_RNR))); }
	;

dir:	  NUM			{ if (($1 & (~IEEE80211_FC1_DIR_MASK)) != 0) {
					bpf_set_error(cstate, "invalid 802.11 direction value 0x%x", $1);
					YYABORT;
				  }
				  $$ = (int)$1;
				}
	| ID			{ CHECK_PTR_VAL($1);
				  if (pcapint_strcasecmp($1, "nods") == 0)
					$$ = IEEE80211_FC1_DIR_NODS;
				  else if (pcapint_strcasecmp($1, "tods") == 0)
					$$ = IEEE80211_FC1_DIR_TODS;
				  else if (pcapint_strcasecmp($1, "fromds") == 0)
					$$ = IEEE80211_FC1_DIR_FROMDS;
				  else if (pcapint_strcasecmp($1, "dstods") == 0)
					$$ = IEEE80211_FC1_DIR_DSTODS;
				  else {
					bpf_set_error(cstate, "unknown 802.11 direction");
					YYABORT;
				  }
				}
	;

reason:	  NUM			{ $$ = $1; }
	| ID			{ CHECK_PTR_VAL($1); CHECK_INT_VAL(($$ = pfreason_to_num(cstate, $1))); }
	;

action:	  ID			{ CHECK_PTR_VAL($1); CHECK_INT_VAL(($$ = pfaction_to_num(cstate, $1))); }
	;

relop:	  '>'			{ $$ = BPF_JGT; }
	| GEQ			{ $$ = BPF_JGE; }
	| '='			{ $$ = BPF_JEQ; }
	;
irelop:	  LEQ			{ $$ = BPF_JGT; }
	| '<'			{ $$ = BPF_JGE; }
	| NEQ			{ $$ = BPF_JEQ; }
	;
arth:	  pnum			{ CHECK_PTR_VAL(($$ = gen_loadi(cstate, $1))); }
	| narth
	;
narth:	  pname '[' arth ']'		{ CHECK_PTR_VAL(($$ = gen_load(cstate, $1, $3, 1))); }
	| pname '[' arth ':' NUM ']'	{ CHECK_PTR_VAL(($$ = gen_load(cstate, $1, $3, $5))); }
	| arth '+' arth			{ CHECK_PTR_VAL(($$ = gen_arth(cstate, BPF_ADD, $1, $3))); }
	| arth '-' arth			{ CHECK_PTR_VAL(($$ = gen_arth(cstate, BPF_SUB, $1, $3))); }
	| arth '*' arth			{ CHECK_PTR_VAL(($$ = gen_arth(cstate, BPF_MUL, $1, $3))); }
	| arth '/' arth			{ CHECK_PTR_VAL(($$ = gen_arth(cstate, BPF_DIV, $1, $3))); }
	| arth '%' arth			{ CHECK_PTR_VAL(($$ = gen_arth(cstate, BPF_MOD, $1, $3))); }
	| arth '&' arth			{ CHECK_PTR_VAL(($$ = gen_arth(cstate, BPF_AND, $1, $3))); }
	| arth '|' arth			{ CHECK_PTR_VAL(($$ = gen_arth(cstate, BPF_OR, $1, $3))); }
	| arth '^' arth			{ CHECK_PTR_VAL(($$ = gen_arth(cstate, BPF_XOR, $1, $3))); }
	| arth LSH arth			{ CHECK_PTR_VAL(($$ = gen_arth(cstate, BPF_LSH, $1, $3))); }
	| arth RSH arth			{ CHECK_PTR_VAL(($$ = gen_arth(cstate, BPF_RSH, $1, $3))); }
	| '-' arth %prec UMINUS		{ CHECK_PTR_VAL(($$ = gen_neg(cstate, $2))); }
	| paren narth ')'		{ $$ = $2; }
	| LEN				{ CHECK_PTR_VAL(($$ = gen_loadlen(cstate))); }
	;
byteop:	  '&'			{ $$ = '&'; }
	| '|'			{ $$ = '|'; }
	| '<'			{ $$ = '<'; }
	| '>'			{ $$ = '>'; }
	| '='			{ $$ = '='; }
	;
pnum:	  NUM
	| paren pnum ')'	{ $$ = $2; }
	;
atmtype: LANE			{ $$ = A_LANE; }
	| METAC			{ $$ = A_METAC;	}
	| BCC			{ $$ = A_BCC; }
	| OAMF4EC		{ $$ = A_OAMF4EC; }
	| OAMF4SC		{ $$ = A_OAMF4SC; }
	| SC			{ $$ = A_SC; }
	| ILMIC			{ $$ = A_ILMIC; }
	;
atmmultitype: OAM		{ $$ = A_OAM; }
	| OAMF4			{ $$ = A_OAMF4; }
	| CONNECTMSG		{ $$ = A_CONNECTMSG; }
	| METACONNECT		{ $$ = A_METACONNECT; }
	;
	/* ATM field types quantifier */
atmfield: VPI			{ $$.atmfieldtype = A_VPI; }
	| VCI			{ $$.atmfieldtype = A_VCI; }
	;
atmvalue: atmfieldvalue
	| relop NUM		{ CHECK_PTR_VAL(($$.b = gen_atmfield_code(cstate, $<blk>0.atmfieldtype, $2, $1, 0))); }
	| irelop NUM		{ CHECK_PTR_VAL(($$.b = gen_atmfield_code(cstate, $<blk>0.atmfieldtype, $2, $1, 1))); }
	| paren atmlistvalue ')' { $$.b = $2.b; $$.q = qerr; }
	;
atmfieldvalue: NUM {
	$$.atmfieldtype = $<blk>0.atmfieldtype;
	if ($$.atmfieldtype == A_VPI ||
	    $$.atmfieldtype == A_VCI)
		CHECK_PTR_VAL(($$.b = gen_atmfield_code(cstate, $$.atmfieldtype, $1, BPF_JEQ, 0)));
	}
	;
atmlistvalue: atmfieldvalue
	| atmlistvalue or atmfieldvalue { gen_or($1.b, $3.b); $$ = $3; }
	;
	/* MTP2 types quantifier */
mtp2type: FISU			{ $$ = M_FISU; }
	| LSSU			{ $$ = M_LSSU; }
	| MSU			{ $$ = M_MSU; }
	| HFISU			{ $$ = MH_FISU; }
	| HLSSU			{ $$ = MH_LSSU; }
	| HMSU			{ $$ = MH_MSU; }
	;
	/* MTP3 field types quantifier */
mtp3field: SIO			{ $$.mtp3fieldtype = M_SIO; }
	| OPC			{ $$.mtp3fieldtype = M_OPC; }
	| DPC			{ $$.mtp3fieldtype = M_DPC; }
	| SLS                   { $$.mtp3fieldtype = M_SLS; }
	| HSIO			{ $$.mtp3fieldtype = MH_SIO; }
	| HOPC			{ $$.mtp3fieldtype = MH_OPC; }
	| HDPC			{ $$.mtp3fieldtype = MH_DPC; }
	| HSLS                  { $$.mtp3fieldtype = MH_SLS; }
	;
mtp3value: mtp3fieldvalue
	| relop NUM		{ CHECK_PTR_VAL(($$.b = gen_mtp3field_code(cstate, $<blk>0.mtp3fieldtype, $2, $1, 0))); }
	| irelop NUM		{ CHECK_PTR_VAL(($$.b = gen_mtp3field_code(cstate, $<blk>0.mtp3fieldtype, $2, $1, 1))); }
	| paren mtp3listvalue ')' { $$.b = $2.b; $$.q = qerr; }
	;
mtp3fieldvalue: NUM {
	$$.mtp3fieldtype = $<blk>0.mtp3fieldtype;
	if ($$.mtp3fieldtype == M_SIO ||
	    $$.mtp3fieldtype == M_OPC ||
	    $$.mtp3fieldtype == M_DPC ||
	    $$.mtp3fieldtype == M_SLS ||
	    $$.mtp3fieldtype == MH_SIO ||
	    $$.mtp3fieldtype == MH_OPC ||
	    $$.mtp3fieldtype == MH_DPC ||
	    $$.mtp3fieldtype == MH_SLS)
		CHECK_PTR_VAL(($$.b = gen_mtp3field_code(cstate, $$.mtp3fieldtype, $1, BPF_JEQ, 0)));
	}
	;
mtp3listvalue: mtp3fieldvalue
	| mtp3listvalue or mtp3fieldvalue { gen_or($1.b, $3.b); $$ = $3; }
	;
%%
