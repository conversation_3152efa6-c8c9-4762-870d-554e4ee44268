# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: sysstat <at> orange.fr\n"
"POT-Creation-Date: 2014-01-19 11:04+0100\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=CHARSET\n"
"Content-Transfer-Encoding: 8bit\n"

#: sadf_misc.c:611
#, c-format
msgid "System activity data file: %s (%#x)\n"
msgstr ""

#: sadf_misc.c:620
#, c-format
msgid "Host: "
msgstr ""

#: sadf_misc.c:627
#, c-format
msgid "File time: "
msgstr ""

#: sadf_misc.c:632
#, c-format
msgid "Size of a long int: %d\n"
msgstr ""

#: sadf_misc.c:634
#, c-format
msgid "List of activities:\n"
msgstr ""

#: sadf_misc.c:647
#, c-format
msgid "\t[Unknown activity format]"
msgstr ""

#: iostat.c:86 cifsiostat.c:71 mpstat.c:90 sar.c:94 pidstat.c:83
#: nfsiostat.c:70
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ]\n"
msgstr ""

#: iostat.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ] [ --debuginfo ]\n"
msgstr ""

#: iostat.c:95
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ]\n"
msgstr ""

#: iostat.c:328
#, c-format
msgid "Cannot find disk data\n"
msgstr ""

#: iostat.c:1385 sa_common.c:1303
#, c-format
msgid "Invalid type of persistent device name\n"
msgstr ""

#: sadc.c:84
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
msgstr ""

#: sadc.c:87
#, c-format
msgid ""
"Options are:\n"
"[ -C <comment> ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"
msgstr ""

#: sadc.c:258
#, c-format
msgid "Cannot write data to system activity file: %s\n"
msgstr ""

#: sadc.c:545
#, c-format
msgid "Cannot write system activity file header: %s\n"
msgstr ""

#: sadc.c:658 sadc.c:667 sadc.c:728 ioconf.c:489 rd_stats.c:69
#: sa_common.c:1109 count.c:118
#, c-format
msgid "Cannot open %s: %s\n"
msgstr ""

#: sadc.c:850
#, c-format
msgid "Cannot append data to that file (%s)\n"
msgstr ""

#: common.c:62
#, c-format
msgid "sysstat version %s\n"
msgstr ""

#: cifsiostat.c:75 nfsiostat.c:74
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"
msgstr ""

#: cifsiostat.c:78 nfsiostat.c:77
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
msgstr ""

#: sadf.c:86
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <datafile> ]\n"
msgstr ""

#: sadf.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -C ] [ -d | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -P { <cpu> [,...] | ALL } ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"
"[ -- <sar_options> ]\n"
msgstr ""

#: mpstat.c:93
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -P { <cpu> [,...] | ON | ALL } ]\n"
msgstr ""

#: mpstat.c:598 sar.c:402 pidstat.c:2130
msgid "Average:"
msgstr ""

#: mpstat.c:967
#, c-format
msgid "Not that many processors!\n"
msgstr ""

#: sar.c:109
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -d ] [ -F ] [ -H ] [ -h ] [ -p ] [ -q ] [ -"
"R ]\n"
"[ -r ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ] [ -v ] [ -W ] [ -w ] [ -y ]\n"
"[ -I { <int> [,...] | SUM | ALL | XALL } ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -m { <keyword> [,...] | ALL } ] [ -n { <keyword> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filename> ] | -o [ <filename> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"
msgstr ""

#: sar.c:131
#, c-format
msgid "Main options and reports:\n"
msgstr ""

#: sar.c:132
#, c-format
msgid "\t-B\tPaging statistics\n"
msgstr ""

#: sar.c:133
#, c-format
msgid "\t-b\tI/O and transfer rate statistics\n"
msgstr ""

#: sar.c:134
#, c-format
msgid "\t-d\tBlock devices statistics\n"
msgstr ""

#: sar.c:135
#, c-format
msgid "\t-F\tFilesystems statistics\n"
msgstr ""

#: sar.c:136
#, c-format
msgid "\t-H\tHugepages utilization statistics\n"
msgstr ""

#: sar.c:137
#, c-format
msgid ""
"\t-I { <int> | SUM | ALL | XALL }\n"
"\t\tInterrupts statistics\n"
msgstr ""

#: sar.c:139
#, c-format
msgid ""
"\t-m { <keyword> [,...] | ALL }\n"
"\t\tPower management statistics\n"
"\t\tKeywords are:\n"
"\t\tCPU\tCPU instantaneous clock frequency\n"
"\t\tFAN\tFans speed\n"
"\t\tFREQ\tCPU average clock frequency\n"
"\t\tIN\tVoltage inputs\n"
"\t\tTEMP\tDevices temperature\n"
"\t\tUSB\tUSB devices plugged into the system\n"
msgstr ""

#: sar.c:148
#, c-format
msgid ""
"\t-n { <keyword> [,...] | ALL }\n"
"\t\tNetwork statistics\n"
"\t\tKeywords are:\n"
"\t\tDEV\tNetwork interfaces\n"
"\t\tEDEV\tNetwork interfaces (errors)\n"
"\t\tNFS\tNFS client\n"
"\t\tNFSD\tNFS server\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP traffic\t(v4)\n"
"\t\tEIP\tIP traffic\t(v4) (errors)\n"
"\t\tICMP\tICMP traffic\t(v4)\n"
"\t\tEICMP\tICMP traffic\t(v4) (errors)\n"
"\t\tTCP\tTCP traffic\t(v4)\n"
"\t\tETCP\tTCP traffic\t(v4) (errors)\n"
"\t\tUDP\tUDP traffic\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP traffic\t(v6)\n"
"\t\tEIP6\tIP traffic\t(v6) (errors)\n"
"\t\tICMP6\tICMP traffic\t(v6)\n"
"\t\tEICMP6\tICMP traffic\t(v6) (errors)\n"
"\t\tUDP6\tUDP traffic\t(v6)\n"
msgstr ""

#: sar.c:169
#, c-format
msgid "\t-q\tQueue length and load average statistics\n"
msgstr ""

#: sar.c:170
#, c-format
msgid "\t-R\tMemory statistics\n"
msgstr ""

#: sar.c:171
#, c-format
msgid "\t-r\tMemory utilization statistics\n"
msgstr ""

#: sar.c:172
#, c-format
msgid "\t-S\tSwap space utilization statistics\n"
msgstr ""

#: sar.c:173
#, c-format
msgid ""
"\t-u [ ALL ]\n"
"\t\tCPU utilization statistics\n"
msgstr ""

#: sar.c:175
#, c-format
msgid "\t-v\tKernel tables statistics\n"
msgstr ""

#: sar.c:176
#, c-format
msgid "\t-W\tSwapping statistics\n"
msgstr ""

#: sar.c:177
#, c-format
msgid "\t-w\tTask creation and system switching statistics\n"
msgstr ""

#: sar.c:178
#, c-format
msgid "\t-y\tTTY devices statistics\n"
msgstr ""

#: sar.c:236
#, c-format
msgid "End of data collecting unexpected\n"
msgstr ""

#: sar.c:823
#, c-format
msgid "Invalid data format\n"
msgstr ""

#: sar.c:827
#, c-format
msgid "Using a wrong data collector from a different sysstat version\n"
msgstr ""

#: sar.c:851
#, c-format
msgid "Inconsistent input data\n"
msgstr ""

#: sar.c:1032 pidstat.c:214
#, c-format
msgid "Requested activities not available\n"
msgstr ""

#: sar.c:1302
#, c-format
msgid "-f and -o options are mutually exclusive\n"
msgstr ""

#: sar.c:1308
#, c-format
msgid "Not reading from a system activity file (use -f option)\n"
msgstr ""

#: sar.c:1440
#, c-format
msgid "Cannot find the data collector (%s)\n"
msgstr ""

#: pr_stats.c:2355 pr_stats.c:2368 pr_stats.c:2468 pr_stats.c:2480
msgid "Summary"
msgstr ""

#: pr_stats.c:2406
msgid "Other devices not listed here"
msgstr ""

#: sa_common.c:917
#, c-format
msgid "Error while reading system activity file: %s\n"
msgstr ""

#: sa_common.c:927
#, c-format
msgid "End of system activity file unexpected\n"
msgstr ""

#: sa_common.c:946
#, c-format
msgid "File created by sar/sadc from sysstat version %d.%d.%d"
msgstr ""

#: sa_common.c:977
#, c-format
msgid "Invalid system activity file: %s\n"
msgstr ""

#: sa_common.c:984
#, c-format
msgid ""
"Current sysstat version can no longer read the format of this file (%#x)\n"
msgstr ""

#: sa_common.c:1216
#, c-format
msgid "Requested activities not available in file %s\n"
msgstr ""

#: pidstat.c:86
#, c-format
msgid ""
"Options are:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -r ] [ -s ] [ -t ] [ -U [ <username> ] ] [ -"
"u ]\n"
"[ -V ] [ -v ] [ -w ] [ -C <command> ] [ -p { <pid> [,...] | SELF | ALL } ]\n"
"[ -T { TASK | CHILD | ALL } ]\n"
msgstr ""

#: count.c:168
#, c-format
msgid "Cannot handle so many processors!\n"
msgstr ""
