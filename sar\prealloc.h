/*
 * sysstat: System performance tools for Linux
 * (C) 1999-2014 by <PERSON><PERSON><PERSON> (sysstat <at> orange.fr)
 */

#ifndef _PREALLOC_H
#define _PREALLOC_H

/* Preallocation constants for sar */
#define NR_IFACE_PREALLOC	(2 * 1)
#define NR_SERIAL_PREALLOC	(2 * 1)
#define NR_DISK_PREALLOC	(3 * 1)
#define NR_FREQ_PREALLOC	(0 * 1)
#define NR_USB_PREALLOC		(5 * 1)
#define NR_FILESYSTEM_PREALLOC	(3 * 1)

#endif  /* _PREALLOC_H */
