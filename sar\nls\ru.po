# translation of sysstat messages to Russian
# NLS support for the sysstat package.
# Copyright (C) 1999, 2009, 2010 Free Software Foundation, Inc.
# This file is distributed under the same license as the sysstat package.
#
# <PERSON><PERSON><PERSON><PERSON> GODARD <sysstat [at] orange.fr>, 1999.
# <PERSON> <<EMAIL>>, 2002.
# <PERSON> <<EMAIL>>, 2009, 2010, 2011, 2012, 2013.
# <PERSON> <<EMAIL>>, 2011.
msgid ""
msgstr ""
"Project-Id-Version: sysstat 10.1.6\n"
"Report-Msgid-Bugs-To: sysstat <at> orange.fr\n"
"POT-Creation-Date: 2013-06-08 09:01+0200\n"
"PO-Revision-Date: 2013-06-10 20:58+0400\n"
"Last-Translator: <PERSON> <y<PERSON>@komyakino.ru>\n"
"Language-Team: Russian <<EMAIL>>\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Lokalize 1.4\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#: iostat.c:86 cifsiostat.c:71 mpstat.c:90 sar.c:94 pidstat.c:83
#: nfsiostat.c:70
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ]\n"
msgstr "Использование: %s [ параметры ] [ <интервал> [ <счётчик> ] ]\n"

#: iostat.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ] [ --debuginfo ]\n"
msgstr ""
"Параметры:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | … } ]\n"
" [ [ -T ] -g <имя_группы> ] [ -p[ <устройство> [,…] | ALL ] ]\n"
"[ <устройство> […] | ALL ] [ --debuginfo ]\n"

#: iostat.c:95
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ]\n"
msgstr ""
"Параметры:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | … } ]\n"
" [ [ -T ] -g <имя_группы> ] [ -p[ <устройство> [,…] | ALL ] ]\n"
"[ <устройство> […] | ALL ]\n"

#: iostat.c:330
#, c-format
msgid "Cannot find disk data\n"
msgstr "Не удалось найти данные диска\n"

#: iostat.c:1394 sa_common.c:1303
#, c-format
msgid "Invalid type of persistent device name\n"
msgstr "Некорректный тип постоянного имени устройства\n"

#: sadf_misc.c:596
#, c-format
msgid "System activity data file: %s (%#x)\n"
msgstr "Недопустимый файл системных показателей: %s (%#x)\n"

#: sadf_misc.c:605
#, c-format
msgid "Host: "
msgstr "Узел: "

#: sadf_misc.c:611
#, c-format
msgid "Size of a long int: %d\n"
msgstr "Размер long int: %d\n"

#: sadf_misc.c:613
#, c-format
msgid "List of activities:\n"
msgstr "Список показателей:\n"

#: sadf_misc.c:626
#, c-format
msgid "\t[Unknown activity format]"
msgstr "\t[Неизвестный формат показателя]"

#: sadc.c:84
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
msgstr "Использование: %s [ параметры ] [ <интервал> [ <счётчик> ] ] [ <вых_файл> ]\n"

#: sadc.c:87
#, c-format
msgid ""
"Options are:\n"
"[ -C <comment> ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"
msgstr ""
"Параметры:\n"
"[ -C <комм.> ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"

#: sadc.c:250
#, c-format
msgid "Cannot write data to system activity file: %s\n"
msgstr "Не удалось записать данные в файл системных показателей: %s\n"

#: sadc.c:537
#, c-format
msgid "Cannot write system activity file header: %s\n"
msgstr "Не удалось записать заголовок в файл системных показателей: %s\n"

#: sadc.c:650 sadc.c:659 sadc.c:720 ioconf.c:491 rd_stats.c:105
#: sa_common.c:1109 count.c:275
#, c-format
msgid "Cannot open %s: %s\n"
msgstr "Не удалось открыть %s: %s\n"

#: sadc.c:842
#, c-format
msgid "Cannot append data to that file (%s)\n"
msgstr "Не удалось добавить данные в этот файл (%s)\n"

#: common.c:62
#, c-format
msgid "sysstat version %s\n"
msgstr "sysstat, версия %s\n"

#: cifsiostat.c:75 nfsiostat.c:74
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"
msgstr ""
"Параметры:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"

#: cifsiostat.c:78 nfsiostat.c:77
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
msgstr ""
"Параметры:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#: mpstat.c:93
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -P { <cpu> [,...] | ON | ALL } ]\n"
msgstr ""
"Параметры:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -P { <ЦП> [,…] | ON | ALL } ]\n"

# sar.c:
#: mpstat.c:609 sar.c:402 pidstat.c:1857
msgid "Average:"
msgstr "Среднее:"

#: mpstat.c:976
#, c-format
msgid "Not that many processors!\n"
msgstr "Нет такого количества процессоров в системе!\n"

#: sadf.c:86
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <datafile> ]\n"
msgstr "Использование: %s [ параметры ] [ <интервал> [ <счётчик> ] ] [ <файл_данных> ]\n"

#: sadf.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -C ] [ -d | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -P { <cpu> [,...] | ALL } ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"
"[ -- <sar_options> ]\n"
msgstr ""
"Параметры:\n"
"[ -C ] [ -d | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -P { <ЦП> [,…] | ALL } ] [ -s [ <чч:мм:сс> ] ] [ -e [ <чч:мм:сс> ] ]\n"
"[ -- <параметры_sar> ]\n"

#: sar.c:109
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -d ] [ -F ] [ -H ] [ -h ] [ -p ] [ -q ] [ -R ]\n"
"[ -r ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ] [ -v ] [ -W ] [ -w ] [ -y ]\n"
"[ -I { <int> [,...] | SUM | ALL | XALL } ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -m { <keyword> [,...] | ALL } ] [ -n { <keyword> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filename> ] | -o [ <filename> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"
msgstr ""
"Параметры:\n"
"[ -A ] [ -b ] [ -B ] [ -C ] [ -d ] [ -F ] [ -H ] [ -h ] [ -p ] [ -q ] [ -R ]\n"
"[ -r ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ] [ -v ] [ -W ] [ -v ] [ -y ]\n"
"[ -I { <целое> [,…] | SUM | ALL | XALL } ] [ -P { <ЦП> [,…] | ALL } ]\n"
"[ -m { <ключ_слово> [,…] | ALL } ] [ -n { <ключ_слово> [,…] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | … } ]\n"
"[ -f [ <имя_файла> ] | -o [ <имя_файла> ]| -[0-9]+ ]\n"
"[ -i <интервал> ] [ -s [ <чч:мм:сс> ] ] [ -e [ <чч:мм:сс> ] ]\n"

#: sar.c:131
#, c-format
msgid "Main options and reports:\n"
msgstr "Основные параметры и отчёты:\n"

#: sar.c:132
#, c-format
msgid "\t-b\tI/O and transfer rate statistics\n"
msgstr "\t-b\tСтатистика ввода-вывода и скорости передачи\n"

#: sar.c:133
#, c-format
msgid "\t-B\tPaging statistics\n"
msgstr "\t-B\tСтатистика обмена страниц\n"

#: sar.c:134
#, c-format
msgid "\t-d\tBlock devices statistics\n"
msgstr "\t-d\tСтатистика блочных устройств\n"

#: sar.c:135
#, c-format
msgid "\t-F\tFilesystems statistics\n"
msgstr "\t-F\tСтатистика файловых систем\n"

#: sar.c:136
#, c-format
msgid "\t-H\tHugepages utilization statistics\n"
msgstr "\t-H\tСтатистика использования огромных страниц\n"

#: sar.c:137
#, c-format
msgid ""
"\t-I { <int> | SUM | ALL | XALL }\n"
"\t\tInterrupts statistics\n"
msgstr ""
"\t-I { <целое> | SUM | ALL | XALL }\n"
"\t\tСтатистика прерываний\n"

#: sar.c:139
#, c-format
msgid ""
"\t-m { <keyword> [,...] | ALL }\n"
"\t\tPower management statistics\n"
"\t\tKeywords are:\n"
"\t\tCPU\tCPU instantaneous clock frequency\n"
"\t\tFAN\tFans speed\n"
"\t\tFREQ\tCPU average clock frequency\n"
"\t\tIN\tVoltage inputs\n"
"\t\tTEMP\tDevices temperature\n"
"\t\tUSB\tUSB devices plugged into the system\n"
msgstr ""
"\t-m { <ключ_слово> [,…] | ALL }\n"
"\t\tСтатистика управления питанием\n"
"\t\tКлючевые слова:\n"
"\t\tCPU\tтекущая частота работы ЦП\n"
"\t\tFAN\tскорость вращения вентиляторов\n"
"\t\tFREQ\tсредняя частота работы ЦП\n"
"\t\tIN\tВходные напряжения\n"
"\t\tTEMP\tтемпература устройств\n"
"\t\tUSB\tподключённые USB-устройства\n"

#: sar.c:148
#, c-format
msgid ""
"\t-n { <keyword> [,...] | ALL }\n"
"\t\tNetwork statistics\n"
"\t\tKeywords are:\n"
"\t\tDEV\tNetwork interfaces\n"
"\t\tEDEV\tNetwork interfaces (errors)\n"
"\t\tNFS\tNFS client\n"
"\t\tNFSD\tNFS server\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP traffic\t(v4)\n"
"\t\tEIP\tIP traffic\t(v4) (errors)\n"
"\t\tICMP\tICMP traffic\t(v4)\n"
"\t\tEICMP\tICMP traffic\t(v4) (errors)\n"
"\t\tTCP\tTCP traffic\t(v4)\n"
"\t\tETCP\tTCP traffic\t(v4) (errors)\n"
"\t\tUDP\tUDP traffic\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP traffic\t(v6)\n"
"\t\tEIP6\tIP traffic\t(v6) (errors)\n"
"\t\tICMP6\tICMP traffic\t(v6)\n"
"\t\tEICMP6\tICMP traffic\t(v6) (errors)\n"
"\t\tUDP6\tUDP traffic\t(v6)\n"
msgstr ""
"\t-n { <ключ_слово> [,…] | ALL }\n"
"\t\tСетевая статистика\n"
"\t\tКлючевые слова:\n"
"\t\tDEV\tСетевые интерфейсы\n"
"\t\tEDEV\tСетевые интерфейсы (ошибки)\n"
"\t\tNFS\tКлиент NFS\n"
"\t\tNFSD\tСервер NFS\n"
"\t\tSOCK\tСокеты\t(v4)\n"
"\t\tIP\tIP трафик\t(v4)\n"
"\t\tEIP\tIP трафик\t(v4) (errors)\n"
"\t\tICMP\tICMP трафик\t(v4)\n"
"\t\tEICMP\tICMP трафик\t(v4) (errors)\n"
"\t\tTCP\tTCP трафик\t(v4)\n"
"\t\tETCP\tTCP трафик\t(v4) (errors)\n"
"\t\tUDP\tUDP трафик\t(v4)\n"
"\t\tSOCK6\tСокеты\t(v6)\n"
"\t\tIP6\tIP трафик\t(v6)\n"
"\t\tEIP6\tIP трафик\t(v6) (errors)\n"
"\t\tICMP6\tICMP трафик\t(v6)\n"
"\t\tEICMP6\tICMP трафик\t(v6) (errors)\n"
"\t\tUDP6\tUDP трафик\t(v6)\n"

#: sar.c:169
#, c-format
msgid "\t-q\tQueue length and load average statistics\n"
msgstr "\t-q\tСтатистика длины и средней загрузки очереди\n"

#: sar.c:170
#, c-format
msgid "\t-r\tMemory utilization statistics\n"
msgstr "\t-r\tСтатистика использования памяти\n"

#: sar.c:171
#, c-format
msgid "\t-R\tMemory statistics\n"
msgstr "\t-R\tСтатистика памяти\n"

#: sar.c:172
#, c-format
msgid "\t-S\tSwap space utilization statistics\n"
msgstr "\t-S\tСтатистика использования области подкачки\n"

#: sar.c:173
#, c-format
msgid ""
"\t-u [ ALL ]\n"
"\t\tCPU utilization statistics\n"
msgstr ""
"\t-u [ ALL ]\n"
"\t\tСтатистика использования ЦП\n"

#: sar.c:175
#, c-format
msgid "\t-v\tKernel tables statistics\n"
msgstr "\t-v\tСтатистика таблиц ядра\n"

#: sar.c:176
#, c-format
msgid "\t-w\tTask creation and system switching statistics\n"
msgstr "\t-w\tСтатистика создания задач и системных переключений\n"

#: sar.c:177
#, c-format
msgid "\t-W\tSwapping statistics\n"
msgstr "\t-W\tСтатистика раздела подкачки\n"

#: sar.c:178
#, c-format
msgid "\t-y\tTTY devices statistics\n"
msgstr "\t-y\tСтатистика устройств TTY\n"

#: sar.c:236
#, c-format
msgid "End of data collecting unexpected\n"
msgstr "Неожиданно закончились собираемые данные\n"

#: sar.c:823
#, c-format
msgid "Invalid data format\n"
msgstr "Недопустимый формат данных\n"

#: sar.c:827
#, c-format
msgid "Using a wrong data collector from a different sysstat version\n"
msgstr "Используется ошибочное средство сбора данных от другой версии sysstat\n"

#: sar.c:851
#, c-format
msgid "Inconsistent input data\n"
msgstr "Несогласованные входные данные\n"

#: sar.c:1034 pidstat.c:216
#, c-format
msgid "Requested activities not available\n"
msgstr "Запрошенный показатель недоступен\n"

#: sar.c:1304
#, c-format
msgid "-f and -o options are mutually exclusive\n"
msgstr "Параметры -f и -o являются взаимоисключающими\n"

#: sar.c:1310
#, c-format
msgid "Not reading from a system activity file (use -f option)\n"
msgstr "Не выполняется чтение из файла системных показателей (используйте параметр -f)\n"

#: sar.c:1442
#, c-format
msgid "Cannot find the data collector (%s)\n"
msgstr "Не удалось найти средство сбора данных (%s)\n"

#: sa_common.c:917
#, c-format
msgid "Error while reading system activity file: %s\n"
msgstr "Ошибка чтения файла системных показателей: %s\n"

#: sa_common.c:927
#, c-format
msgid "End of system activity file unexpected\n"
msgstr "Неожиданный конец файла системных показателей\n"

#: sa_common.c:946
#, c-format
msgid "File created by sar/sadc from sysstat version %d.%d.%d"
msgstr "Файл создан с помощью sar/sadc из sysstat версии %d.%d.%d"

#: sa_common.c:977
#, c-format
msgid "Invalid system activity file: %s\n"
msgstr "Недопустимый файл системных показателей: %s\n"

#: sa_common.c:984
#, c-format
msgid "Current sysstat version can no longer read the format of this file (%#x)\n"
msgstr "В текущей версии sysstat больше не поддерживается формат этого файла (%#x)\n"

#: sa_common.c:1216
#, c-format
msgid "Requested activities not available in file %s\n"
msgstr "Запрашиваемые показатели из файла %s недоступны\n"

#: pidstat.c:86
#, c-format
msgid ""
"Options are:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -r ] [ -s ] [ -t ] [ -U [ <username> ] ] [ -u ]\n"
"[ -V ] [ -w ] [ -C <command> ] [ -p { <pid> [,...] | SELF | ALL } ]\n"
"[ -T { TASK | CHILD | ALL } ]\n"
msgstr ""
"Параметры:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -r ] [ -s ] [ -t ] [ -U [ <имя_пользователя> ] ]\n"
"[ -u ] [ -V ] [ -w ] [ -C <команда> ] [ -p { <pid> [,…] | SELF | ALL } ]\n"
"[ -T { TASK | CHILD | ALL } ]\n"

#: count.c:321
#, c-format
msgid "Cannot handle so many processors!\n"
msgstr "Не удаётся обработать так много процессоров!\n"

#: pr_stats.c:2348 pr_stats.c:2361 pr_stats.c:2461 pr_stats.c:2473
msgid "Summary"
msgstr "Сводка"

#: pr_stats.c:2399
msgid "Other devices not listed here"
msgstr "Другие устройства здесь не перечислены"

#~ msgid ""
#~ "Options are:\n"
#~ "[ --debuginfo ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
#~ msgstr ""
#~ "Параметры:\n"
#~ "[ --debuginfo ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#~ msgid "\t-m\tPower management statistics\n"
#~ msgstr "\t-B\tСтатистика по управлению питанием\n"
