#!/bin/sh
# @SA_LIB_DIR@/sa1
# (C) 1999-2017 <PERSON><PERSON><PERSON> (sysstat <at> orange.fr)
#
#@(#) @PACKAGE_NAME@-@PACKAGE_VERSION@
#@(#) sa1: Collect and store binary data in system activity data file.
#

# Set default value for some variables.
# Used only if ${SYSCONFIG_DIR}/sysstat doesn't exist!
HISTORY=0
SADC_OPTIONS=""
SA_DIR=@SA_DIR@
SYSCONFIG_DIR=@SYSCONFIG_DIR@
umask 0022

[ -r ${SYSCONFIG_DIR}/sysstat ] && . ${SYSCONFIG_DIR}/sysstat
[ -d ${SA_DIR} ] || SA_DIR=@SA_DIR@

if [ ${HISTORY} -gt 28 ]
then
	SADC_OPTIONS="${SADC_OPTIONS} -D"
fi

ENDIR=@SA_LIB_DIR@
cd ${ENDIR}
[ "$1" = "--boot" ] && shift && BOOT=y || BOOT=n
if [ $# = 0 ] && [ "${BOOT}" = "n" ]
then
# Note: Stats are written at the end of previous file *and* at the
# beginning of the new one (when there is a file rotation) only if
# outfile has been specified as '-' on the command line...
	exec ${ENDIR}/sadc -F -L ${SADC_OPTIONS} 1 1 ${SA_DIR}
else
	exec ${ENDIR}/sadc -F -L ${SADC_OPTIONS} $* ${SA_DIR}
fi
