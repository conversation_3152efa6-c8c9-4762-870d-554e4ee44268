/* config.h.in.  Generated from configure.ac by autoheader.  */

/* Define to 1 if arpa/inet.h declares `ether_hostton' */
#undef ARPA_INET_H_DECLARES_ETHER_HOSTTON

/* Enable optimizer debugging */
#undef BDEBUG

/* Define to 1 if DAG transmit support is enabled */
#undef ENABLE_DAG_TX

/* define if you want to build the instrument functions code */
#undef ENABLE_INSTRUMENT_FUNCTIONS

/* Define to 1 if remote packet capture is to be supported */
#undef ENABLE_REMOTE

/* define if we have the AIX getnetbyname_r() */
#undef HAVE_AIX_GETNETBYNAME_R

/* define if we have the AIX getprotobyname_r() */
#undef HAVE_AIX_GETPROTOBYNAME_R

/* Define to 1 if you have the `asprintf' function. */
#undef HAVE_ASPRINTF

/* Define to 1 if you have the <dagapi.h> header file. */
#undef HAVE_DAGAPI_H

/* define if you have the DAG API */
#undef HAVE_DAG_API

/* define if you have vdag_set_device_info() */
#undef HAVE_DAG_VDAG

/* Define to 1 if you have the declaration of `ether_hostton' */
#undef HAVE_DECL_ETHER_HOSTTON

/* Define to 1 if you have the declaration of `SKF_AD_VLAN_TAG_PRESENT', and
   to 0 if you don't. */
#undef HAVE_DECL_SKF_AD_VLAN_TAG_PRESENT

/* Define to 1 if `dl_module_id_1' is a member of `dl_hp_ppa_info_t'. */
#undef HAVE_DL_HP_PPA_INFO_T_DL_MODULE_ID_1

/* Define to 1 if the system has the type `dl_passive_req_t'. */
#undef HAVE_DL_PASSIVE_REQ_T

/* Define to 1 if you have the `ether_hostton' function. */
#undef HAVE_ETHER_HOSTTON

/* Define to 1 if fseeko (and presumably ftello) exists and is declared. */
#undef HAVE_FSEEKO

/* Define to 1 if you have the `getspnam' function. */
#undef HAVE_GETSPNAM

/* Define to 1 if using GNU libc. */
#undef HAVE_GLIBC

/* Define to 1 if you have a GNU-style `strerror_r' function. */
#undef HAVE_GNU_STRERROR_R

/* on HP-UX 10.20 or later */
#undef HAVE_HPUX10_20_OR_LATER

/* Define to 1 if you have the <inttypes.h> header file. */
#undef HAVE_INTTYPES_H

/* if libdlpi exists */
#undef HAVE_LIBDLPI

/* if libnl exists */
#undef HAVE_LIBNL

/* Define to 1 if you have the <linux/compiler.h> header file. */
#undef HAVE_LINUX_COMPILER_H

/* define if we have the Linux getnetbyname_r() */
#undef HAVE_LINUX_GETNETBYNAME_R

/* define if we have the Linux getprotobyname_r() */
#undef HAVE_LINUX_GETPROTOBYNAME_R

/* Define to 1 if you have the <linux/net_tstamp.h> header file. */
#undef HAVE_LINUX_NET_TSTAMP_H

/* Define to 1 if you have the <linux/usbdevice_fs.h> header file. */
#undef HAVE_LINUX_USBDEVICE_FS_H

/* Define to 1 if you have the <net/bpf.h> header file. */
#undef HAVE_NET_BPF_H

/* Define to 1 if you have the <net/if_media.h> header file. */
#undef HAVE_NET_IF_MEDIA_H

/* Use OpenSSL */
#undef HAVE_OPENSSL

/* if there's an os-proto.h for this platform, to use additional prototypes */
#undef HAVE_OS_PROTO_H

/* Define to 1 if you have a POSIX-style `strerror_r' function. */
#undef HAVE_POSIX_STRERROR_R

/* define if you have the Myricom SNF API */
#undef HAVE_SNF_API

/* Define to 1 if the system has the type `socklen_t'. */
#undef HAVE_SOCKLEN_T

/* On Solaris */
#undef HAVE_SOLARIS

/* target host supports Solaris "any" device */
#undef HAVE_SOLARIS_ANY_DEVICE

/* define if we have the Solaris getnetbyname_r() */
#undef HAVE_SOLARIS_GETNETBYNAME_R

/* define if we have the Solaris getprotobyname_r() */
#undef HAVE_SOLARIS_GETPROTOBYNAME_R

/* Define to 1 if you have the <stdint.h> header file. */
#undef HAVE_STDINT_H

/* Define to 1 if you have the <stdio.h> header file. */
#undef HAVE_STDIO_H

/* Define to 1 if you have the <stdlib.h> header file. */
#undef HAVE_STDLIB_H

/* Define to 1 if you have the <strings.h> header file. */
#undef HAVE_STRINGS_H

/* Define to 1 if you have the <string.h> header file. */
#undef HAVE_STRING_H

/* Define to 1 if you have the `strlcat' function. */
#undef HAVE_STRLCAT

/* Define to 1 if you have the `strlcpy' function. */
#undef HAVE_STRLCPY

/* Define to 1 if you have the `strtok_r' function. */
#undef HAVE_STRTOK_R

/* Define to 1 if the system has the type `struct BPF_TIMEVAL'. */
#undef HAVE_STRUCT_BPF_TIMEVAL

/* Define to 1 if the system has the type `struct ether_addr'. */
#undef HAVE_STRUCT_ETHER_ADDR

/* Define to 1 if `msg_control' is a member of `struct msghdr'. */
#undef HAVE_STRUCT_MSGHDR_MSG_CONTROL

/* Define to 1 if `msg_flags' is a member of `struct msghdr'. */
#undef HAVE_STRUCT_MSGHDR_MSG_FLAGS

/* Define to 1 if the system has the type `struct rte_ether_addr'. */
#undef HAVE_STRUCT_RTE_ETHER_ADDR

/* Define to 1 if `hci_channel' is a member of `struct sockaddr_hci'. */
#undef HAVE_STRUCT_SOCKADDR_HCI_HCI_CHANNEL

/* Define to 1 if `sa_len' is a member of `struct sockaddr'. */
#undef HAVE_STRUCT_SOCKADDR_SA_LEN

/* Define to 1 if `tp_vlan_tci' is a member of `struct tpacket_auxdata'. */
#undef HAVE_STRUCT_TPACKET_AUXDATA_TP_VLAN_TCI

/* Define to 1 if `bRequestType' is a member of `struct
   usbdevfs_ctrltransfer'. */
#undef HAVE_STRUCT_USBDEVFS_CTRLTRANSFER_BREQUESTTYPE

/* Define to 1 if you have the <sys/bufmod.h> header file. */
#undef HAVE_SYS_BUFMOD_H

/* Define to 1 if you have the <sys/dlpi_ext.h> header file. */
#undef HAVE_SYS_DLPI_EXT_H

/* Define to 1 if you have the <sys/dlpi.h> header file. */
#undef HAVE_SYS_DLPI_H

/* Define to 1 if you have the <sys/ioccom.h> header file. */
#undef HAVE_SYS_IOCCOM_H

/* Define to 1 if you have the <sys/stat.h> header file. */
#undef HAVE_SYS_STAT_H

/* Define to 1 if you have the <sys/types.h> header file. */
#undef HAVE_SYS_TYPES_H

/* Define to 1 if using uclibc(-ng). */
#undef HAVE_UCLIBC

/* Define to 1 if you have the <unistd.h> header file. */
#undef HAVE_UNISTD_H

/* Define to 1 if you have the `vasprintf' function. */
#undef HAVE_VASPRINTF

/* Define to 1 if you have the `vsyslog' function. */
#undef HAVE_VSYSLOG

/* Define to 1 if you have the `_wcserror_s' function. */
#undef HAVE__WCSERROR_S

/* define if __atomic_load_n is supported by the compiler */
#undef HAVE___ATOMIC_LOAD_N

/* define if __atomic_store_n is supported by the compiler */
#undef HAVE___ATOMIC_STORE_N

/* Define to 1 if netinet/ether.h declares `ether_hostton' */
#undef NETINET_ETHER_H_DECLARES_ETHER_HOSTTON

/* Define to 1 if netinet/if_ether.h declares `ether_hostton' */
#undef NETINET_IF_ETHER_H_DECLARES_ETHER_HOSTTON

/* Define to 1 if net/ethernet.h declares `ether_hostton' */
#undef NET_ETHERNET_H_DECLARES_ETHER_HOSTTON

/* do not use protochain */
#undef NO_PROTOCHAIN

/* Define to the address where bug reports for this package should be sent. */
#undef PACKAGE_BUGREPORT

/* Define to the full name of this package. */
#undef PACKAGE_NAME

/* Define to the full name and version of this package. */
#undef PACKAGE_STRING

/* Define to the one symbol short name of this package. */
#undef PACKAGE_TARNAME

/* Define to the home page for this package. */
#undef PACKAGE_URL

/* Define to the version of this package. */
#undef PACKAGE_VERSION

/* target host supports Bluetooth sniffing */
#undef PCAP_SUPPORT_BT

/* target host supports Bluetooth Monitor */
#undef PCAP_SUPPORT_BT_MONITOR

/* support D-Bus sniffing */
#undef PCAP_SUPPORT_DBUS

/* target host supports DPDK */
#undef PCAP_SUPPORT_DPDK

/* target host supports Linux usbmon for USB sniffing */
#undef PCAP_SUPPORT_LINUX_USBMON

/* target host supports netfilter sniffing */
#undef PCAP_SUPPORT_NETFILTER

/* target host supports netmap */
#undef PCAP_SUPPORT_NETMAP

/* target host supports RDMA sniffing */
#undef PCAP_SUPPORT_RDMASNIFF

/* The size of `time_t', as computed by sizeof. */
#undef SIZEOF_TIME_T

/* The size of `void *', as computed by sizeof. */
#undef SIZEOF_VOID_P

/* Define to 1 if all of the C90 standard headers exist (not just the ones
   required in a freestanding environment). This macro is provided for
   backward compatibility; new code need not use it. */
#undef STDC_HEADERS

/* Define to 1 if sys/ethernet.h declares `ether_hostton' */
#undef SYS_ETHERNET_H_DECLARES_ETHER_HOSTTON

/* Enable parser debugging */
#undef YYDEBUG

/* Define to 1 if `lex' declares `yytext' as a `char *' by default, not a
   `char[]'. */
#undef YYTEXT_POINTER

/* Number of bits in a file offset, on hosts where this is settable. */
#undef _FILE_OFFSET_BITS

/* Define to 1 to make fseeko visible on some hosts (e.g. glibc 2.2). */
#undef _LARGEFILE_SOURCE

/* Define for large files, on AIX-style hosts. */
#undef _LARGE_FILES
