cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/devname.o -MMD -MP -MF .deps/agent/devname.d -c agent/devname.c -o agent/devname.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/err.o -MMD -MP -MF .deps/agent/err.d -c agent/err.c -o agent/err.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/escape.o -MMD -MP -MF .deps/agent/escape.d -c agent/escape.c -o agent/escape.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/info.o -MMD -MP -MF .deps/agent/info.d -c agent/info.c -o agent/info.o
In file included from agent/info.c:545:
agent/pids.h:271:30: warning: ‘struct pids_info’ declared inside parameter list will not be visible outside of this definition or declaration
  271 | typedef void (*SET_t)(struct pids_info *, struct pids_result *, proc_t *);
      |                              ^~~~~~~~~
agent/info.c: In function ‘zap_fieldstab’:
agent/info.c:1455:10: warning: suggest braces around empty body in an ‘if’ statement [-Wempty-body]
 1455 |          ;
      |          ^
agent/info.c: In function ‘output_load’:
agent/info.c:1508:14: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 2 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |            ~~^
      |              |
      |              long unsigned int
      |            %u
agent/info.c:1508:20: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 3 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                ~~~~^
      |                    |
      |                    long unsigned int
      |                %02u
agent/info.c:1508:24: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 4 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                      ~~^
      |                        |
      |                        long unsigned int
      |                      %u
agent/info.c:1508:30: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 5 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                          ~~~~^
      |                              |
      |                              long unsigned int
      |                          %02u
agent/info.c:1508:34: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 6 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                                ~~^
      |                                  |
      |                                  long unsigned int
      |                                %u
agent/info.c:1508:40: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 7 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                                    ~~~~^
      |                                        |
      |                                        long unsigned int
      |                                    %02u
agent/info.c: In function ‘avg_load’:
agent/info.c:1562:10: warning: unused variable ‘g2’ [-Wunused-variable]
 1562 |    float g2 = round(loads3 / f + e);
      |          ^~
agent/info.c:1561:10: warning: unused variable ‘g1’ [-Wunused-variable]
 1561 |    float g1 = round(loads2 / f + e);
      |          ^~
agent/info.c: In function ‘meminfo’:
agent/info.c:2076:10: warning: unused variable ‘pct_misc’ [-Wunused-variable]
 2076 |    float pct_misc = (float)(kb_main_total - kb_main_available - kb_main_used) * (100.0 / (float)kb_main_total);
      |          ^~~~~~~~
agent/info.c: In function ‘before’:
agent/info.c:2094:8: warning: unused variable ‘linux_version_code’ [-Wunused-variable]
 2094 |    int linux_version_code = procps_linux_version();
      |        ^~~~~~~~~~~~~~~~~~
agent/info.c: In function ‘cpu_help’:
agent/info.c:2218:10: warning: unused variable ‘scale1’ [-Wunused-variable]
 2218 |    float scale1 = 0.0;
      |          ^~~~~~
agent/info.c: In function ‘tasks_refresh’:
agent/info.c:2491:8: warning: unused variable ‘i’ [-Wunused-variable]
 2491 |    int i, what;
      |        ^
agent/info.c:2488:15: warning: unused variable ‘n_alloc’ [-Wunused-variable]
 2488 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c: In function ‘tasks_refresh1’:
agent/info.c:2554:8: warning: unused variable ‘i’ [-Wunused-variable]
 2554 |    int i, what;
      |        ^
agent/info.c:2551:15: warning: unused variable ‘n_alloc’ [-Wunused-variable]
 2551 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c: In function ‘cal_cpu’:
agent/info.c:2765:24: warning: argument to ‘sizeof’ in ‘snprintf’ call is the same expression as the destination; did you mean to provide an explicit length? [-Wsizeof-pointer-memaccess]
 2765 |    snprintf(buf, sizeof(buf), "%#.1f", u);
      |                        ^
agent/info.c: In function ‘cal_mem’:
agent/info.c:2812:24: warning: argument to ‘sizeof’ in ‘snprintf’ call is the same expression as the destination; did you mean to provide an explicit length? [-Wsizeof-pointer-memaccess]
 2812 |    snprintf(buf, sizeof(buf), "%#.1f", m);
      |                        ^
agent/info.c: In function ‘sys’:
agent/info.c:3088:13: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3088 |             return;
      |             ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c:3254:11: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3254 |           return;
      |           ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c:3260:11: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3260 |           return;
      |           ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c:3269:11: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3269 |           return;
      |           ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c: In function ‘get_load’:
agent/info.c:1498:1: warning: control reaches end of non-void function [-Wreturn-type]
 1498 | }
      | ^
agent/info.c: In function ‘output_load’:
agent/info.c:1513:1: warning: control reaches end of non-void function [-Wreturn-type]
 1513 | }
      | ^
agent/info.c: In function ‘get_time1’:
agent/info.c:1533:1: warning: control reaches end of non-void function [-Wreturn-type]
 1533 | }
      | ^
agent/info.c: In function ‘avg_load’:
agent/info.c:1573:1: warning: control reaches end of non-void function [-Wreturn-type]
 1573 | }
      | ^
agent/info.c: In function ‘cpu_help’:
agent/info.c:2260:1: warning: control reaches end of non-void function [-Wreturn-type]
 2260 | }
      | ^
agent/info.c: In function ‘mem_help’:
agent/info.c:2349:1: warning: control reaches end of non-void function [-Wreturn-type]
 2349 | }
      | ^
agent/info.c: In function ‘init_cpu’:
agent/info.c:2620:1: warning: control reaches end of non-void function [-Wreturn-type]
 2620 | }
      | ^
agent/info.c: In function ‘init_parpids’:
agent/info.c:2850:1: warning: control reaches end of non-void function [-Wreturn-type]
 2850 | }
      | ^
agent/info.c: In function ‘init_thrpids’:
agent/info.c:2886:1: warning: control reaches end of non-void function [-Wreturn-type]
 2886 | }
      | ^
agent/info.c: In function ‘add_process_thread_count’:
agent/info.c:2896:1: warning: control reaches end of non-void function [-Wreturn-type]
 2896 | }
      | ^
agent/info.c: In function ‘add_thread_count’:
agent/info.c:2906:1: warning: control reaches end of non-void function [-Wreturn-type]
 2906 | }
      | ^
agent/info.c: At top level:
agent/info.c:2551:15: warning: ‘n_alloc’ defined but not used [-Wunused-variable]
 2551 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c:2488:15: warning: ‘n_alloc’ defined but not used [-Wunused-variable]
 2488 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c:2948:13: warning: ‘wait_for_semaphore’ defined but not used [-Wunused-function]
 2948 | static void wait_for_semaphore(sem_t *sem) {
      |             ^~~~~~~~~~~~~~~~~~
agent/info.c:2664:13: warning: ‘forest_begin’ defined but not used [-Wunused-function]
 2664 | static void forest_begin(WIN_t *q)
      |             ^~~~~~~~~~~~
agent/info.c:2545:14: warning: ‘tasks_refresh1’ defined but not used [-Wunused-function]
 2545 | static void *tasks_refresh1(void *unused)
      |              ^~~~~~~~~~~~~~
agent/info.c:2358:13: warning: ‘wins_stage_1’ defined but not used [-Wunused-function]
 2358 | static void wins_stage_1(void)
      |             ^~~~~~~~~~~~
agent/info.c:1355:13: warning: ‘zap_fieldstab’ defined but not used [-Wunused-function]
 1355 | static void zap_fieldstab(void)
      |             ^~~~~~~~~~~~~
agent/info.c:1348:20: warning: ‘Cursor_state’ defined but not used [-Wunused-variable]
 1348 | static const char *Cursor_state = "";
      |                    ^~~~~~~~~~~~
agent/info.c:1265:13: warning: ‘win_names’ defined but not used [-Wunused-function]
 1265 | static void win_names(WIN_t *q, const char *name)
      |             ^~~~~~~~~
agent/info.c:842:13: warning: ‘Stdout_buf’ defined but not used [-Wunused-variable]
  842 | static char Stdout_buf[2048];
      |             ^~~~~~~~~~
agent/info.c:841:12: warning: ‘Ttychanged’ defined but not used [-Wunused-variable]
  841 | static int Ttychanged = 0;
      |            ^~~~~~~~~~
agent/info.c:840:5: warning: ‘Tty_raw’ defined but not used [-Wunused-variable]
  840 |     Tty_raw; // for unsolicited input
      |     ^~~~~~~
agent/info.c:836:23: warning: ‘Tty_original’ defined but not used [-Wunused-variable]
  836 | static struct termios Tty_original, // our inherited terminal definition
      |                       ^~~~~~~~~~~~
agent/info.c:826:15: warning: ‘Pseudo_size’ defined but not used [-Wunused-variable]
  826 | static size_t Pseudo_size;
      |               ^~~~~~~~~~~
agent/info.c:825:15: warning: ‘Bot_show_func’ defined but not used [-Wunused-variable]
  825 | static void (*Bot_show_func)(void);
      |               ^~~~~~~~~~~~~
agent/info.c:824:14: warning: ‘Bot_focus_func’ defined but not used [-Wunused-variable]
  824 | static BOT_f Bot_focus_func;
      |              ^~~~~~~~~~~~~~
agent/info.c:822:5: warning: ‘Bot_buf’ defined but not used [-Wunused-variable]
  822 |     Bot_buf[BOTBUFSIZ]; // the 'environ' can be huge
      |     ^~~~~~~
agent/info.c:821:6: warning: ‘Bot_head’ defined but not used [-Wunused-variable]
  821 |     *Bot_head,
      |      ^~~~~~~~
agent/info.c:820:13: warning: ‘Bot_sep’ defined but not used [-Wunused-variable]
  820 | static char Bot_sep,
      |             ^~~~~~~
agent/info.c:818:5: warning: ‘Bot_indx’ defined but not used [-Wunused-variable]
  818 |     Bot_indx = BOT_UNFOCUS,
      |     ^~~~~~~~
agent/info.c:817:5: warning: ‘Bot_rsvd’ defined but not used [-Wunused-variable]
  817 |     Bot_rsvd,
      |     ^~~~~~~~
agent/info.c:816:5: warning: ‘Bot_what’ defined but not used [-Wunused-variable]
  816 |     Bot_what,
      |     ^~~~~~~~
agent/info.c:815:12: warning: ‘Bot_task’ defined but not used [-Wunused-variable]
  815 | static int Bot_task,
      |            ^~~~~~~~
agent/info.c:787:12: warning: ‘Cap_can_goto’ defined but not used [-Wunused-variable]
  787 | static int Cap_can_goto = 0;
      |            ^~~~~~~~~~~~
agent/info.c:785:12: warning: ‘Cap_avoid_eol’ defined but not used [-Wunused-variable]
  785 | static int Cap_avoid_eol = 0;
      |            ^~~~~~~~~~~~~
agent/info.c:782:13: warning: ‘Cap_smam’ defined but not used [-Wunused-variable]
  782 |             Cap_smam[CAPBUFSIZ] = "";
      |             ^~~~~~~~
agent/info.c:781:13: warning: ‘Cap_rmam’ defined but not used [-Wunused-variable]
  781 | static char Cap_rmam[CAPBUFSIZ] = "",
      |             ^~~~~~~~
agent/info.c:779:13: warning: ‘Caps_endline’ defined but not used [-Wunused-variable]
  779 |             Caps_endline[SMLBUFSIZ] = "";
      |             ^~~~~~~~~~~~
agent/info.c:778:13: warning: ‘Caps_off’ defined but not used [-Wunused-variable]
  778 |             Caps_off[CAPBUFSIZ] = "",
      |             ^~~~~~~~
agent/info.c:777:13: warning: ‘Cap_reverse’ defined but not used [-Wunused-variable]
  777 |             Cap_reverse[CAPBUFSIZ] = "",
      |             ^~~~~~~~~~~
agent/info.c:775:13: warning: ‘Cap_home’ defined but not used [-Wunused-variable]
  775 |             Cap_home[CAPBUFSIZ] = "",
      |             ^~~~~~~~
agent/info.c:774:5: warning: ‘Cap_clr_eos’ defined but not used [-Wunused-variable]
  774 |     Cap_clr_eos[CAPBUFSIZ] = "",
      |     ^~~~~~~~~~~
agent/info.c:773:5: warning: ‘Cap_curs_hide’ defined but not used [-Wunused-variable]
  773 |     Cap_curs_hide[CAPBUFSIZ] = "",       // batch requirements!
      |     ^~~~~~~~~~~~~
agent/info.c:772:5: warning: ‘Cap_curs_huge’ defined but not used [-Wunused-variable]
  772 |     Cap_curs_huge[CAPBUFSIZ] = "",       // to remind people of those
      |     ^~~~~~~~~~~~~
agent/info.c:771:5: warning: ‘Cap_curs_norm’ defined but not used [-Wunused-variable]
  771 |     Cap_curs_norm[CAPBUFSIZ] = "",       // cost nothing but DO serve
      |     ^~~~~~~~~~~~~
agent/info.c:770:5: warning: ‘Cap_clr_scr’ defined but not used [-Wunused-variable]
  770 |     Cap_clr_scr[CAPBUFSIZ] = "",         // the assignments used here
      |     ^~~~~~~~~~~
agent/info.c:769:5: warning: ‘Cap_nl_clreos’ defined but not used [-Wunused-variable]
  769 |     Cap_nl_clreos[CAPBUFSIZ] = "",       // are initialized to zeros!
      |     ^~~~~~~~~~~~~
agent/info.c:768:13: warning: ‘Cap_clr_eol’ defined but not used [-Wunused-variable]
  768 | static char Cap_clr_eol[CAPBUFSIZ] = "", // global and/or static vars
      |             ^~~~~~~~~~~
agent/info.c:757:14: warning: ‘Myname’ defined but not used [-Wunused-variable]
  757 | static char *Myname;
      |              ^~~~~~
agent/info.c:604:17: warning: ‘Sigwinch_set’ defined but not used [-Wunused-variable]
  604 | static sigset_t Sigwinch_set;
      |                 ^~~~~~~~~~~~
agent/info.c:595:26: warning: ‘Pids_ctx1’ defined but not used [-Wunused-variable]
  595 | static struct pids_info *Pids_ctx1;
      |                          ^~~~~~~~~
agent/info.c:586:5: warning: ‘Width_mode’ defined but not used [-Wunused-variable]
  586 |     Width_mode = 0,   // set w/ 'w' - potential output override
      |     ^~~~~~~~~~
agent/info.c:585:5: warning: ‘Secure_mode’ defined but not used [-Wunused-variable]
  585 |     Secure_mode = 0,  // set if some functionality restricted
      |     ^~~~~~~~~~~
agent/info.c:584:5: warning: ‘Loops’ defined but not used [-Wunused-variable]
  584 |     Loops = -1,       // number of iterations, -1 loops forever
      |     ^~~~~
agent/info.c:583:12: warning: ‘Batch’ defined but not used [-Wunused-variable]
  583 | static int Batch = 0, // batch mode, collect no input, dumb output
      |            ^~~~~
agent/info.c:576:13: warning: ‘Adjoin_sp’ defined but not used [-Wunused-variable]
  576 | static char Adjoin_sp[] = " ~6 ~1";
      |             ^~~~~~~~~
agent/info.c:556:38: warning: ‘Max_lines’ defined but not used [-Wunused-variable]
  556 | static int Screen_cols, Screen_rows, Max_lines;
      |                                      ^~~~~~~~~
agent/info.c:556:25: warning: ‘Screen_rows’ defined but not used [-Wunused-variable]
  556 | static int Screen_cols, Screen_rows, Max_lines;
      |                         ^~~~~~~~~~~
agent/info.c:554:14: warning: ‘Pseudo_screen’ defined but not used [-Wunused-variable]
  554 | static char *Pseudo_screen;
      |              ^~~~~~~~~~~~~
agent/info.c: In function ‘avg_load’:
agent/info.c:1565:33: warning: ‘%d’ directive output may be truncated writing between 1 and 8 bytes into a region of size 7 [-Wformat-truncation=]
 1565 |    snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
      |                                 ^~
agent/info.c:1565:32: note: directive argument in the range [-1048576, 1048575]
 1565 |    snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
      |                                ^~~~~~~~
agent/info.c:1565:32: note: directive argument in the range [0, 99]
agent/info.c:1565:4: note: ‘snprintf’ output between 4 and 12 bytes into a destination of size 7
 1565 |    snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
      |    ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/meminfo.o -MMD -MP -MF .deps/agent/meminfo.d -c agent/meminfo.c -o agent/meminfo.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/namespace.o -MMD -MP -MF .deps/agent/namespace.d -c agent/namespace.c -o agent/namespace.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/numa.o -MMD -MP -MF .deps/agent/numa.d -c agent/numa.c -o agent/numa.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/pids.o -MMD -MP -MF .deps/agent/pids.d -c agent/pids.c -o agent/pids.o
In file included from agent/pids.c:44:
agent/pids.h:271:30: warning: ‘struct pids_info’ declared inside parameter list will not be visible outside of this definition or declaration
  271 | typedef void (*SET_t)(struct pids_info *, struct pids_result *, proc_t *);
      |                              ^~~~~~~~~
agent/pids.c: In function ‘free_pids_str’:
agent/pids.c:135:47: warning: unused parameter ‘R’ [-Wunused-parameter]
  135 | static void freNAME(str) (struct pids_result *R) {
      |                           ~~~~~~~~~~~~~~~~~~~~^
agent/pids.c: In function ‘free_pids_strv’:
agent/pids.c:139:48: warning: unused parameter ‘R’ [-Wunused-parameter]
  139 | static void freNAME(strv) (struct pids_result *R) {
      |                            ~~~~~~~~~~~~~~~~~~~~^
agent/pids.c: In function ‘pids_assign_results’:
agent/pids.c:990:17: warning: passing argument 1 of ‘*that’ from incompatible pointer type [-Wincompatible-pointer-types]
  990 |         (*that)(info, this, p);
      |                 ^~~~
      |                 |
      |                 struct pids_info *
agent/pids.c:990:17: note: expected ‘struct pids_info *’ but argument is of type ‘struct pids_info *’
agent/pids.c: In function ‘pids_cleanup_stacks_all’:
agent/pids.c:1023:13: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1023 |         ext = ext->next;
      |             ^
agent/pids.c: In function ‘pids_itemize_stacks_all’:
agent/pids.c:1082:13: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1082 |         ext = ext->next;
      |             ^
agent/pids.c: In function ‘pids_stacks_alloc’:
agent/pids.c:1315:18: warning: assignment to ‘struct stacks_extent *’ from incompatible pointer type ‘struct stacks_extent2 *’ [-Wincompatible-pointer-types]
 1315 |     p_blob->next = info->extents;                              // push this extent onto... |
      |                  ^
agent/pids.c:1330:12: warning: returning ‘struct stacks_extent2 *’ from a function with incompatible return type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1330 |     return p_blob;
      |            ^~~~~~
agent/pids.c: In function ‘pids_stacks_fetch’:
agent/pids.c:1367:19: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1367 |         if (!(ext = pids_stacks_alloc(info, STACKS_INIT)))
      |                   ^
agent/pids.c:1381:23: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1381 |             || (!(ext = pids_stacks_alloc(info, STACKS_GROW))))
      |                       ^
agent/pids.c: In function ‘pids_stacks_fetch1’:
agent/pids.c:1431:19: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1431 |         if (!(ext = pids_stacks_alloc(info, STACKS_INIT)))
      |                   ^
agent/pids.c:1445:23: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1445 |             || (!(ext = pids_stacks_alloc(info, STACKS_GROW))))
      |                       ^
agent/pids.c: In function ‘procps_pids_unref’:
agent/pids.c:1626:34: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1626 |                 (*info)->extents = (*info)->extents->next;
      |                                  ^
agent/pids.c:1633:25: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1633 |                 nextext = ext->next;
      |                         ^
agent/pids.c: In function ‘procps_pids_get’:
agent/pids.c:1710:29: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1710 |         if (!(info->get_ext = pids_stacks_alloc(info, 1)))
      |                             ^
agent/pids.c: In function ‘procps_pids_reset’:
agent/pids.c:1844:27: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1844 |             info->extents = p->next;
      |                           ^
agent/pids.c: In function ‘procps_pids_sort’:
agent/pids.c:1947:23: warning: variable ‘parms’ set but not used [-Wunused-but-set-variable]
 1947 |     struct sort_parms parms;
      |                       ^~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/pwcache.o -MMD -MP -MF .deps/agent/pwcache.d -c agent/pwcache.c -o agent/pwcache.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/readproc.o -MMD -MP -MF .deps/agent/readproc.d -c agent/readproc.c -o agent/readproc.o
agent/readproc.c: In function ‘stat2proc’:
agent/readproc.c:597:16: warning: unused variable ‘le’ [-Wunused-variable]
  597 |         size_t le = strlen(buf) + 1;
      |                ^~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/send.o -MMD -MP -MF .deps/agent/send.d -c agent/send.c -o agent/send.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/send_data.o -MMD -MP -MF .deps/agent/send_data.d -c agent/send_data.c -o agent/send_data.o
In file included from agent/send_data.c:50:
agent/pids.h:271:30: warning: ‘struct pids_info’ declared inside parameter list will not be visible outside of this definition or declaration
  271 | typedef void (*SET_t)(struct pids_info *, struct pids_result *, proc_t *);
      |                              ^~~~~~~~~
agent/send_data.c: In function ‘sigint_handler’:
agent/send_data.c:263:9: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
  263 |         write_log("ctrl-c caught ....\n");
      |         ^~~~~~~~~
agent/send_data.c:260:25: warning: unused parameter ‘sig’ [-Wunused-parameter]
  260 | void sigint_handler(int sig)
      |                     ~~~~^~~
agent/send_data.c: In function ‘stat2proc’:
agent/send_data.c:365:25: warning: implicit declaration of function ‘escape_str’ [-Wimplicit-function-declaration]
  365 |                         escape_str(buf, raw, sizeof(buf));
      |                         ^~~~~~~~~~
agent/send_data.c:416:9: warning: implicit declaration of function ‘LEAVE’ [-Wimplicit-function-declaration]
  416 |         LEAVE(0x160);
      |         ^~~~~
agent/send_data.c: In function ‘check_self’:
agent/send_data.c:462:24: warning: missing initializer for field ‘siz’ of ‘struct utlbuf_s’ [-Wmissing-field-initializers]
  462 |                 struct utlbuf_s ub = {NULL, 0};
      |                        ^~~~~~~~
In file included from agent/send_data.c:11:
agent/readproc.h:42:11: note: ‘siz’ declared here
   42 |     int   siz;     // current len of the above
      |           ^~~
agent/send_data.c:463:21: warning: variable ‘rc’ set but not used [-Wunused-but-set-variable]
  463 |                 int rc = 0;
      |                     ^~
agent/send_data.c: In function ‘alarm_handler1’:
agent/send_data.c:1191:25: warning: unused parameter ‘sig’ [-Wunused-parameter]
 1191 | void alarm_handler1(int sig)
      |                     ~~~~^~~
agent/send_data.c: At top level:
agent/send_data.c:1574:6: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
 1574 | void write_log(const char *format, ...)
      |      ^~~~~~~~~
agent/send_data.c:263:9: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
  263 |         write_log("ctrl-c caught ....\n");
      |         ^~~~~~~~~
agent/send_data.c: In function ‘main’:
agent/send_data.c:1840:46: warning: passing argument 3 of ‘pthread_create’ from incompatible pointer type [-Wincompatible-pointer-types]
 1840 |                 pthread_create(&sar1, &attr, &sadc1, NULL);
      |                                              ^~~~~~
      |                                              |
      |                                              int (*)()
In file included from agent/top.h:23,
                 from agent/send_data.c:5:
/usr/include/pthread.h:204:36: note: expected ‘void * (*)(void *)’ but argument is of type ‘int (*)()’
  204 |                            void *(*__start_routine) (void *),
      |                            ~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~
agent/send_data.c:1842:50: warning: passing argument 3 of ‘pthread_create’ from incompatible pointer type [-Wincompatible-pointer-types]
 1842 |                 pthread_create(&tcpdump1, &attr, &tcp1, argv[1]);
      |                                                  ^~~~~
      |                                                  |
      |                                                  int (*)(char *)
/usr/include/pthread.h:204:36: note: expected ‘void * (*)(void *)’ but argument is of type ‘int (*)(char *)’
  204 |                            void *(*__start_routine) (void *),
      |                            ~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~
agent/send_data.c:2053:33: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2053 |         for (int isar = 0; isar < os->int_count; isar++) {
      |                                 ^
agent/send_data.c:2064:33: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2064 |         for (int isar = 0; isar < os->enet_count; isar++) {
      |                                 ^
agent/send_data.c:2085:35: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2085 |         for (int idisk = 0; idisk < os->disk_count; idisk++) {
      |                                   ^
agent/send_data.c:2099:31: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2099 |         for (int ifs = 0; ifs < os->file_count; ifs++) {
      |                               ^
agent/send_data.c:2124:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2124 |         for (tcd = 0; tcd < os->group_s; tcd++)
      |                           ^
agent/send_data.c:2141:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2141 |         for (tcd = 0; tcd < os->group_s1; tcd++)
      |                           ^
agent/send_data.c:2159:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2159 |         for (tcd = 0; tcd < os->group_p; tcd++)
      |                           ^
agent/send_data.c:2196:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2196 |         for (tcd = 0; tcd < os->group_f; tcd++)
      |                           ^
agent/send_data.c:2214:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2214 |         for (tcd = 0; tcd < os->group_r; tcd++)
      |                           ^
agent/send_data.c:2232:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2232 |         for (tcd = 0; tcd < os->group_ack; tcd++)
      |                           ^
agent/send_data.c:1756:19: warning: unused variable ‘th’ [-Wunused-variable]
 1756 |         pthread_t th;
      |                   ^~
agent/send_data.c:1723:19: warning: unused variable ‘th1’ [-Wunused-variable]
 1723 |         pthread_t th1;
      |                   ^~~
agent/send_data.c: In function ‘c_tcp1’:
agent/send_data.c:1246:1: warning: control reaches end of non-void function [-Wreturn-type]
 1246 | }
      | ^
agent/send_data.c: In function ‘free_all1’:
agent/send_data.c:1552:1: warning: control reaches end of non-void function [-Wreturn-type]
 1552 | }
      | ^
agent/top.h: At top level:
agent/top.h:104:23: warning: ‘Stat_items’ defined but not used [-Wunused-variable]
  104 | static enum stat_item Stat_items[] = {
      |                       ^~~~~~~~~~
agent/top.h:60:13: warning: ‘UTF8_tab’ defined but not used [-Wunused-variable]
   60 | static char UTF8_tab[] = {
      |             ^~~~~~~~
agent/top.h:56:14: warning: ‘Frame_etscale’ defined but not used [-Wunused-variable]
   56 | static float Frame_etscale;
      |              ^~~~~~~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/stat.o -MMD -MP -MF .deps/agent/stat.d -c agent/stat.c -o agent/stat.o
agent/stat.c: In function ‘procps_stat_reap’:
agent/stat.c:1239:9: warning: unused variable ‘ff’ [-Wunused-variable]
 1239 |     int ff;
      |         ^~
agent/stat.c: In function ‘sum_tics’:
agent/stat.c:1425:10: warning: variable ‘scale’ set but not used [-Wunused-but-set-variable]
 1425 |    float scale;
      |          ^~~~~
agent/stat.c:1424:18: warning: unused variable ‘rx’ [-Wunused-variable]
 1424 |    struct rx_st *rx;
      |                  ^~
agent/stat.c:1423:10: warning: variable ‘idl_frme’ set but not used [-Wunused-but-set-variable]
 1423 |    SIC_t idl_frme, tot_frme;
      |          ^~~~~~~~
agent/stat.c:1433:1: warning: no return statement in function returning non-void [-Wreturn-type]
 1433 | } // end: sum_tics
      | ^
agent/stat.c:1419:41: warning: unused parameter ‘this’ [-Wunused-parameter]
 1419 | static int sum_tics (struct stat_stack *this) {
      |                      ~~~~~~~~~~~~~~~~~~~^~~~
agent/stat.c: At top level:
agent/stat.c:1419:12: warning: ‘sum_tics’ defined but not used [-Wunused-function]
 1419 | static int sum_tics (struct stat_stack *this) {
      |            ^~~~~~~~
agent/stat.c:44:20: warning: ‘Cpu_States_fmts’ defined but not used [-Wunused-variable]
   44 | static const char *Cpu_States_fmts;
      |                    ^~~~~~~~~~~~~~~
agent/stat.c:43:20: warning: ‘Cpu_pmax’ defined but not used [-Wunused-variable]
   43 | static float       Cpu_pmax;
      |                    ^~~~~~~~
agent/stat.c:42:20: warning: ‘Hertz’ defined but not used [-Wunused-variable]
   42 | static long        Hertz;
      |                    ^~~~~
In file included from agent/stat.c:36:
agent/top.h:104:23: warning: ‘Stat_items’ defined but not used [-Wunused-variable]
  104 | static enum stat_item Stat_items[] = {
      |                       ^~~~~~~~~~
agent/top.h:60:13: warning: ‘UTF8_tab’ defined but not used [-Wunused-variable]
   60 | static char UTF8_tab[] = {
      |             ^~~~~~~~
agent/top.h:56:14: warning: ‘Frame_etscale’ defined but not used [-Wunused-variable]
   56 | static float Frame_etscale;
      |              ^~~~~~~~~~~~~
agent/stat.c: In function ‘sum_tics’:
agent/stat.c:1430:7: warning: ‘tot_frme’ is used uninitialized [-Wuninitialized]
 1430 |    if (1 > tot_frme) idl_frme = tot_frme = 1;
      |       ^
agent/stat.c:1423:20: note: ‘tot_frme’ was declared here
 1423 |    SIC_t idl_frme, tot_frme;
      |                    ^~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/sysinfo.o -MMD -MP -MF .deps/agent/sysinfo.d -c agent/sysinfo.c -o agent/sysinfo.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/thpool.o -MMD -MP -MF .deps/agent/thpool.d -c agent/thpool.c -o agent/thpool.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/top.o -MMD -MP -MF .deps/agent/top.d -c agent/top.c -o agent/top.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/top_nls.o -MMD -MP -MF .deps/agent/top_nls.d -c agent/top_nls.c -o agent/top_nls.o
In file included from agent/top_nls.c:24:
agent/top.h: In function ‘utf8_cols’:
agent/top.h:89:23: warning: implicit declaration of function ‘mbtowc’; did you mean ‘mbrtowc’? [-Wimplicit-function-declaration]
   89 |                 (void)mbtowc(&wc, (const char *)p, n);
      |                       ^~~~~~
      |                       mbrtowc
agent/top.h: At top level:
agent/top.h:104:23: warning: ‘Stat_items’ defined but not used [-Wunused-variable]
  104 | static enum stat_item Stat_items[] = {
      |                       ^~~~~~~~~~
agent/top.h:60:13: warning: ‘UTF8_tab’ defined but not used [-Wunused-variable]
   60 | static char UTF8_tab[] = {
      |             ^~~~~~~~
agent/top.h:56:14: warning: ‘Frame_etscale’ defined but not used [-Wunused-variable]
   56 | static float Frame_etscale;
      |              ^~~~~~~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/uptime.o -MMD -MP -MF .deps/agent/uptime.d -c agent/uptime.c -o agent/uptime.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/version.o -MMD -MP -MF .deps/agent/version.d -c agent/version.c -o agent/version.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/wchan.o -MMD -MP -MF .deps/agent/wchan.d -c agent/wchan.c -o agent/wchan.o
make -C ./sar
make[1]: Entering directory '/app/no_perf/sar'
gcc -o act_sadc.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SADC -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  activity.c
gcc -o sa_wrap.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP sa_wrap.c
gcc -o sa_common.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP sa_common.c
gcc -o rd_stats.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SADC -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  rd_stats.c
rd_stats.c: In function ‘read_filesystem’:
rd_stats.c:2361:46: warning: passing argument 2 of ‘statvfs’ from incompatible pointer type [-Wincompatible-pointer-types]
 2361 |                         if ((statvfs(mountp, &buf) < 0) || (!buf.f_blocks))
      |                                              ^~~~
      |                                              |
      |                                              struct statfs *
In file included from rd_stats.c:31:
/usr/include/x86_64-linux-gnu/sys/statvfs.h:52:48: note: expected ‘struct statvfs * restrict’ but argument is of type ‘struct statfs *’
   52 |                     struct statvfs *__restrict __buf)
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~
gcc -o count.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SADC -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  count.c
ar rvs librdstats.a rd_stats.o count.o
r - rd_stats.o
r - count.o
gcc -o rd_sensors.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP rd_sensors.c
ar rv librdsensors.a rd_sensors.o
r - rd_sensors.o
gcc -o sadc.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP sadc.c
In file included from sadc.c:86:
../agent/top.h: In function ‘utf8_cols’:
../agent/top.h:91:26: warning: implicit declaration of function ‘wcwidth’ [-Wimplicit-function-declaration]
   91 |                 if ((n = wcwidth(wc)) < 0)
      |                          ^~~~~~~
gcc -o act_sar.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SAR -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  activity.c
gcc -o public.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP public.c
public.c: In function ‘update_sar_lock1’:
public.c:8:1: warning: control reaches end of non-void function [-Wreturn-type]
    8 | }
      | ^
gcc -o pr_stats.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP pr_stats.c
In file included from pr_stats.c:33:
../agent/top.h: In function ‘utf8_cols’:
../agent/top.h:91:26: warning: implicit declaration of function ‘wcwidth’ [-Wimplicit-function-declaration]
   91 |                 if ((n = wcwidth(wc)) < 0)
      |                          ^~~~~~~
gcc -o network_security.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP network_security.c
network_security.c: In function ‘build_socket_process_map’:
network_security.c:88:55: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size between 236 and 245 [-Wformat-truncation=]
   88 |             snprintf(path, sizeof(path), "/proc/%d/fd/%s", pid, fd_entry->d_name);
      |                                                       ^~
network_security.c:88:13: note: ‘snprintf’ output between 12 and 276 bytes into a destination of size 256
   88 |             snprintf(path, sizeof(path), "/proc/%d/fd/%s", pid, fd_entry->d_name);
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
gcc -o iostat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP iostat.c
iostat.c: In function ‘read_sysfs_dlist_part_stat’:
iostat.c:565:53: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size between 0 and 1023 [-Wformat-truncation=]
  565 |                 snprintf(filename, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                                                     ^~
iostat.c:565:17: note: ‘snprintf’ output between 7 and 1285 bytes into a destination of size 1024
  565 |                 snprintf(filename, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
gcc -o rd_stats_light.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  rd_stats.c
rd_stats.c: In function ‘read_filesystem’:
rd_stats.c:2361:46: warning: passing argument 2 of ‘statvfs’ from incompatible pointer type [-Wincompatible-pointer-types]
 2361 |                         if ((statvfs(mountp, &buf) < 0) || (!buf.f_blocks))
      |                                              ^~~~
      |                                              |
      |                                              struct statfs *
In file included from rd_stats.c:31:
/usr/include/x86_64-linux-gnu/sys/statvfs.h:52:48: note: expected ‘struct statvfs * restrict’ but argument is of type ‘struct statfs *’
   52 |                     struct statvfs *__restrict __buf)
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~
gcc -o count_light.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  count.c
ar rvs librdstats_light.a rd_stats_light.o count_light.o
r - rd_stats_light.o
r - count_light.o
gcc -o common.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP common.c
common.c: In function ‘get_dev_part_nr’:
common.c:214:49: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size between 0 and 1023 [-Wformat-truncation=]
  214 |                 snprintf(line, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                                                 ^~
common.c:214:17: note: ‘snprintf’ output between 7 and 1285 bytes into a destination of size 1024
  214 |                 snprintf(line, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
gcc -o ioconf.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP ioconf.c
ioconf.c: In function ‘ioc_init’:
ioconf.c:301:46: warning: ‘%s’ directive writing up to 31 bytes into a region of size 16 [-Wformat-overflow=]
  301 |                         sprintf(blkp->cfmt, "%s%s%%d", blkp->name, cfmt);
      |                                              ^~
ioconf.c:301:25: note: ‘sprintf’ output between 3 and 49 bytes into a destination of size 16
  301 |                         sprintf(blkp->cfmt, "%s%s%%d", blkp->name, cfmt);
      |                         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ioconf.c:322:42: warning: ‘d’ directive writing 1 byte into a region of size between 0 and 15 [-Wformat-overflow=]
  322 |                 sprintf(blkp->pfmt, "%s%%d", (*pfmt == '*') ? "" : pfmt);
      |                                          ^
ioconf.c:322:17: note: ‘sprintf’ output between 3 and 18 bytes into a destination of size 16
  322 |                 sprintf(blkp->pfmt, "%s%%d", (*pfmt == '*') ? "" : pfmt);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ioconf.c: In function ‘transform_devmapname’:
ioconf.c:497:51: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size 244 [-Wformat-truncation=]
  497 |                 snprintf(filen, MAX_FILE_LEN, "%s/%s", DEVMAP_DIR, dp->d_name);
      |                                                   ^~
ioconf.c:497:17: note: ‘snprintf’ output between 13 and 268 bytes into a destination of size 256
  497 |                 snprintf(filen, MAX_FILE_LEN, "%s/%s", DEVMAP_DIR, dp->d_name);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ar rvs libsyscom.a common.o ioconf.o
r - common.o
r - ioconf.o
gcc -o iostat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context iostat.o librdstats_light.a libsyscom.a 
gcc -o mpstat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP mpstat.c
gcc -o mpstat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context mpstat.o librdstats_light.a libsyscom.a 
gcc -o pidstat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP pidstat.c
gcc -o pidstat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context pidstat.o librdstats_light.a libsyscom.a 
gcc -o nfsiostat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP nfsiostat.c
gcc -o nfsiostat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context nfsiostat.o librdstats_light.a libsyscom.a 
gcc -o cifsiostat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP cifsiostat.c
gcc -o cifsiostat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context cifsiostat.o librdstats_light.a libsyscom.a 
make[1]: Leaving directory '/app/no_perf/sar'
make -C ./libpcap
make[1]: Entering directory '/app/no_perf/libpcap'
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-linux.o pcap-linux.c
pcap-linux.c: In function ‘pcap_wait_for_frames_mmap’:
pcap-linux.c:3664:25: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
 3664 |                         write_log("pcap_wait_for_frames_mmap: POSIX定时器信号触发(循环开始)，1秒时间到\n");
      |                         ^~~~~~~~~
pcap-linux.c: In function ‘pcap_read_linux_mmap_v2’:
pcap-linux.c:4431:24: warning: implicit declaration of function ‘get_optimal_timer_fd’ [-Wimplicit-function-declaration]
 4431 |         int timer_fd = get_optimal_timer_fd();  // 获取v2函数的定时器
      |                        ^~~~~~~~~~~~~~~~~~~~
pcap-linux.c: At top level:
pcap-linux.c:6334:13: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
 6334 | extern void write_log(const char *format, ...);
      |             ^~~~~~~~~
pcap-linux.c:3664:25: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
 3664 |                         write_log("pcap_wait_for_frames_mmap: POSIX定时器信号触发(循环开始)，1秒时间到\n");
      |                         ^~~~~~~~~
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o fad-getad.o fad-getad.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-usb-linux.o pcap-usb-linux.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-netfilter-linux.o pcap-netfilter-linux.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap.o pcap.c
pcap.c: In function ‘posix_timer_handler’:
pcap.c:242:9: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
  242 |         write_log("POSIX定时器信号触发，设置pcap_timeout1=1\n");
      |         ^~~~~~~~~
pcap.c: At top level:
pcap.c:259:13: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
  259 | extern void write_log(const char *format, ...);
      |             ^~~~~~~~~
pcap.c:242:9: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
  242 |         write_log("POSIX定时器信号触发，设置pcap_timeout1=1\n");
      |         ^~~~~~~~~
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c ./gencode.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o optimize.o optimize.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o nametoaddr.o nametoaddr.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o etherent.o etherent.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o fmtutils.o fmtutils.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-util.o pcap-util.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o savefile.o savefile.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o sf-pcap.o sf-pcap.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o sf-pcapng.o sf-pcapng.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-common.o pcap-common.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o bpf_image.o bpf_image.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o bpf_filter.o bpf_filter.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o bpf_dump.o bpf_dump.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c scanner.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c grammar.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -o strlcat.o -c ./missing/strlcat.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -o strlcpy.o -c ./missing/strlcpy.c
ar rc libpcap.a pcap-linux.o fad-getad.o pcap-usb-linux.o pcap-netfilter-linux.o pcap.o gencode.o optimize.o nametoaddr.o etherent.o fmtutils.o pcap-util.o savefile.o sf-pcap.o sf-pcapng.o pcap-common.o bpf_image.o bpf_filter.o bpf_dump.o scanner.o grammar.o strlcat.o strlcpy.o 
ranlib libpcap.a
VER=`cat ./VERSION`; \
MAJOR_VER=`sed 's/\([0-9][0-9]*\)\..*/\1/' ./VERSION`; \
gcc  -shared -Wl,-soname,libpcap.so.$MAJOR_VER \
    -o libpcap.so.$VER pcap-linux.o fad-getad.o pcap-usb-linux.o pcap-netfilter-linux.o pcap.o gencode.o optimize.o nametoaddr.o etherent.o fmtutils.o pcap-util.o savefile.o sf-pcap.o sf-pcapng.o pcap-common.o bpf_image.o bpf_filter.o bpf_dump.o scanner.o grammar.o strlcat.o strlcpy.o  
make[1]: Leaving directory '/app/no_perf/libpcap'
make -C ./tcpdump-5.0.0 tcpdump.o fptype.o libnetdissect.a
make[1]: Entering directory '/app/no_perf/tcpdump-5.0.0'
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o tcpdump.o tcpdump.c
tcpdump.c: In function ‘init_barriers’:
tcpdump.c:384:17: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
  384 |                 write_log("init_barriers: barriers已经初始化\n");
      |                 ^~~~~~~~~
tcpdump.c:391:13: warning: implicit declaration of function ‘efficient_barrier_init’; did you mean ‘pthread_barrier_init’? [-Wimplicit-function-declaration]
  391 |         if (efficient_barrier_init(&barrier_a_done, 3) != 0) {
      |             ^~~~~~~~~~~~~~~~~~~~~~
      |             pthread_barrier_init
tcpdump.c:399:17: warning: implicit declaration of function ‘efficient_barrier_destroy’; did you mean ‘pthread_barrier_destroy’? [-Wimplicit-function-declaration]
  399 |                 efficient_barrier_destroy(&barrier_a_done);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~
      |                 pthread_barrier_destroy
tcpdump.c: At top level:
tcpdump.c:3655:6: warning: conflicting types for ‘efficient_barrier_destroy’; have ‘void(efficient_barrier_t *)’
 3655 | void efficient_barrier_destroy(efficient_barrier_t *barrier)
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~
tcpdump.c:399:17: note: previous implicit declaration of ‘efficient_barrier_destroy’ with type ‘void(efficient_barrier_t *)’
  399 |                 efficient_barrier_destroy(&barrier_a_done);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o fptype.o fptype.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o addrtoname.o addrtoname.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o addrtostr.o addrtostr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o af.o af.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o ascii_strcasecmp.o ascii_strcasecmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o checksum.o checksum.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o cpack.o cpack.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o gmpls.o gmpls.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o in_cksum.o in_cksum.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o ipproto.o ipproto.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o l2vpn.o l2vpn.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o netdissect.o netdissect.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o netdissect-alloc.o netdissect-alloc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o nlpid.o nlpid.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o ntp.o ntp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o oui.o oui.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o parsenfsfh.o parsenfsfh.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print.o print.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-802_11.o print-802_11.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-802_15_4.o print-802_15_4.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ah.o print-ah.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ahcp.o print-ahcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-aodv.o print-aodv.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-aoe.o print-aoe.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ap1394.o print-ap1394.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-arcnet.o print-arcnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-arista.o print-arista.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-arp.o print-arp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ascii.o print-ascii.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-atalk.o print-atalk.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-atm.o print-atm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-babel.o print-babel.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bcm-li.o print-bcm-li.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-beep.o print-beep.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bfd.o print-bfd.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bgp.o print-bgp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bootp.o print-bootp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-brcmtag.o print-brcmtag.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bt.o print-bt.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-calm-fast.o print-calm-fast.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-carp.o print-carp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cdp.o print-cdp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cfm.o print-cfm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-chdlc.o print-chdlc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cip.o print-cip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cnfp.o print-cnfp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dccp.o print-dccp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-decnet.o print-decnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dhcp6.o print-dhcp6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-domain.o print-domain.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dsa.o print-dsa.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dtp.o print-dtp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dvmrp.o print-dvmrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-eap.o print-eap.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-egp.o print-egp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-eigrp.o print-eigrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-enc.o print-enc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-erspan.o print-erspan.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-esp.o print-esp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ether.o print-ether.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-fddi.o print-fddi.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-forces.o print-forces.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-fr.o print-fr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-frag6.o print-frag6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ftp.o print-ftp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-geneve.o print-geneve.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-geonet.o print-geonet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-gre.o print-gre.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-hncp.o print-hncp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-hsrp.o print-hsrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-http.o print-http.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-icmp.o print-icmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-icmp6.o print-icmp6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-igmp.o print-igmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-igrp.o print-igrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip-demux.o print-ip-demux.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip.o print-ip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip6.o print-ip6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip6opts.o print-ip6opts.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipcomp.o print-ipcomp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipfc.o print-ipfc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipnet.o print-ipnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipoib.o print-ipoib.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipx.o print-ipx.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-isakmp.o print-isakmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-isoclns.o print-isoclns.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-juniper.o print-juniper.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-krb.o print-krb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-l2tp.o print-l2tp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lane.o print-lane.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ldp.o print-ldp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lisp.o print-lisp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-llc.o print-llc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lldp.o print-lldp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lmp.o print-lmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-loopback.o print-loopback.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lspping.o print-lspping.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lwapp.o print-lwapp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lwres.o print-lwres.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-m3ua.o print-m3ua.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-macsec.o print-macsec.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mobile.o print-mobile.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mobility.o print-mobility.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mpcp.o print-mpcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mpls.o print-mpls.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mptcp.o print-mptcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-msdp.o print-msdp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-msnlb.o print-msnlb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nflog.o print-nflog.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nfs.o print-nfs.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nhrp.o print-nhrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nsh.o print-nsh.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ntp.o print-ntp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-null.o print-null.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-olsr.o print-olsr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-openflow-1.0.o print-openflow-1.0.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-openflow-1.3.o print-openflow-1.3.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-openflow.o print-openflow.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ospf.o print-ospf.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ospf6.o print-ospf6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-otv.o print-otv.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pflog.o print-pflog.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pgm.o print-pgm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pim.o print-pim.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pktap.o print-pktap.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ppi.o print-ppi.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ppp.o print-ppp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pppoe.o print-pppoe.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pptp.o print-pptp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ptp.o print-ptp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-quic.o print-quic.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-radius.o print-radius.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-raw.o print-raw.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-realtek.o print-realtek.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-resp.o print-resp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rip.o print-rip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ripng.o print-ripng.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rpki-rtr.o print-rpki-rtr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rsvp.o print-rsvp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rt6.o print-rt6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rtsp.o print-rtsp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rx.o print-rx.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sctp.o print-sctp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sflow.o print-sflow.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sip.o print-sip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sl.o print-sl.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sll.o print-sll.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-slow.o print-slow.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-smtp.o print-smtp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-snmp.o print-snmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-someip.o print-someip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ssh.o print-ssh.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-stp.o print-stp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sunatm.o print-sunatm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sunrpc.o print-sunrpc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-symantec.o print-symantec.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-syslog.o print-syslog.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-tcp.o print-tcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-telnet.o print-telnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-tftp.o print-tftp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-timed.o print-timed.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-tipc.o print-tipc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-token.o print-token.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-udld.o print-udld.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-udp.o print-udp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-unsupported.o print-unsupported.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-usb.o print-usb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vjc.o print-vjc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vqp.o print-vqp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vrrp.o print-vrrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vsock.o print-vsock.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vtp.o print-vtp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vxlan-gpe.o print-vxlan-gpe.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vxlan.o print-vxlan.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-wb.o print-wb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-whois.o print-whois.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-zep.o print-zep.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-zephyr.o print-zephyr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-zeromq.o print-zeromq.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o signature.o signature.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o strtoaddr.o strtoaddr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o util-print.o util-print.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -o strlcat.o -c ./missing/strlcat.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -o strlcpy.o -c ./missing/strlcpy.c
ar cr libnetdissect.a addrtoname.o addrtostr.o af.o ascii_strcasecmp.o checksum.o cpack.o gmpls.o in_cksum.o ipproto.o l2vpn.o netdissect.o netdissect-alloc.o nlpid.o ntp.o oui.o parsenfsfh.o print.o print-802_11.o print-802_15_4.o print-ah.o print-ahcp.o print-aodv.o print-aoe.o print-ap1394.o print-arcnet.o print-arista.o print-arp.o print-ascii.o print-atalk.o print-atm.o print-babel.o print-bcm-li.o print-beep.o print-bfd.o print-bgp.o print-bootp.o print-brcmtag.o print-bt.o print-calm-fast.o print-carp.o print-cdp.o print-cfm.o print-chdlc.o print-cip.o print-cnfp.o print-dccp.o print-decnet.o print-dhcp6.o print-domain.o print-dsa.o print-dtp.o print-dvmrp.o print-eap.o print-egp.o print-eigrp.o print-enc.o print-erspan.o print-esp.o print-ether.o print-fddi.o print-forces.o print-fr.o print-frag6.o print-ftp.o print-geneve.o print-geonet.o print-gre.o print-hncp.o print-hsrp.o print-http.o print-icmp.o print-icmp6.o print-igmp.o print-igrp.o print-ip-demux.o print-ip.o print-ip6.o print-ip6opts.o print-ipcomp.o print-ipfc.o print-ipnet.o print-ipoib.o print-ipx.o print-isakmp.o print-isoclns.o print-juniper.o print-krb.o print-l2tp.o print-lane.o print-ldp.o print-lisp.o print-llc.o print-lldp.o print-lmp.o print-loopback.o print-lspping.o print-lwapp.o print-lwres.o print-m3ua.o print-macsec.o print-mobile.o print-mobility.o print-mpcp.o print-mpls.o print-mptcp.o print-msdp.o print-msnlb.o print-nflog.o print-nfs.o print-nhrp.o print-nsh.o print-ntp.o print-null.o print-olsr.o print-openflow-1.0.o print-openflow-1.3.o print-openflow.o print-ospf.o print-ospf6.o print-otv.o print-pflog.o print-pgm.o print-pim.o print-pktap.o print-ppi.o print-ppp.o print-pppoe.o print-pptp.o print-ptp.o print-quic.o print-radius.o print-raw.o print-realtek.o print-resp.o print-rip.o print-ripng.o print-rpki-rtr.o print-rsvp.o print-rt6.o print-rtsp.o print-rx.o print-sctp.o print-sflow.o print-sip.o print-sl.o print-sll.o print-slow.o print-smtp.o print-snmp.o print-someip.o print-ssh.o print-stp.o print-sunatm.o print-sunrpc.o print-symantec.o print-syslog.o print-tcp.o print-telnet.o print-tftp.o print-timed.o print-tipc.o print-token.o print-udld.o print-udp.o print-unsupported.o print-usb.o print-vjc.o print-vqp.o print-vrrp.o print-vsock.o print-vtp.o print-vxlan-gpe.o print-vxlan.o print-wb.o print-whois.o print-zep.o print-zephyr.o print-zeromq.o signature.o strtoaddr.o util-print.o  strlcat.o strlcpy.o
ranlib libnetdissect.a
make[1]: Leaving directory '/app/no_perf/tcpdump-5.0.0'
cc -g ./agent/devname.o ./agent/err.o ./agent/escape.o ./agent/info.o ./agent/meminfo.o ./agent/namespace.o ./agent/numa.o ./agent/pids.o ./agent/pwcache.o ./agent/readproc.o ./agent/send.o ./agent/send_data.o ./agent/stat.o ./agent/sysinfo.o ./agent/thpool.o ./agent/top.o ./agent/top_nls.o ./agent/uptime.o ./agent/version.o ./agent/wchan.o  ./sar/act_sadc.o ./sar/sadc.o ./sar/pr_stats.o  ./sar/sa_wrap.o ./sar/sa_common.o ./sar/network_security.o ./sar/librdstats.a ./sar/librdsensors.a ./sar/libsyscom.a  ./tcpdump-5.0.0/fptype.o ./tcpdump-5.0.0/tcpdump.o ./tcpdump-5.0.0/libnetdissect.a ./libpcap/libpcap.a -o send_data -lm -lpthread -ldl -lrt -lcrypto
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/devname.o -MMD -MP -MF .deps/agent/devname.d -c agent/devname.c -o agent/devname.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/err.o -MMD -MP -MF .deps/agent/err.d -c agent/err.c -o agent/err.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/escape.o -MMD -MP -MF .deps/agent/escape.d -c agent/escape.c -o agent/escape.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/info.o -MMD -MP -MF .deps/agent/info.d -c agent/info.c -o agent/info.o
In file included from agent/info.c:545:
agent/pids.h:271:30: warning: ‘struct pids_info’ declared inside parameter list will not be visible outside of this definition or declaration
  271 | typedef void (*SET_t)(struct pids_info *, struct pids_result *, proc_t *);
      |                              ^~~~~~~~~
agent/info.c: In function ‘zap_fieldstab’:
agent/info.c:1455:10: warning: suggest braces around empty body in an ‘if’ statement [-Wempty-body]
 1455 |          ;
      |          ^
agent/info.c: In function ‘output_load’:
agent/info.c:1508:14: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 2 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |            ~~^
      |              |
      |              long unsigned int
      |            %u
agent/info.c:1508:20: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 3 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                ~~~~^
      |                    |
      |                    long unsigned int
      |                %02u
agent/info.c:1508:24: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 4 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                      ~~^
      |                        |
      |                        long unsigned int
      |                      %u
agent/info.c:1508:30: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 5 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                          ~~~~^
      |                              |
      |                              long unsigned int
      |                          %02u
agent/info.c:1508:34: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 6 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                                ~~^
      |                                  |
      |                                  long unsigned int
      |                                %u
agent/info.c:1508:40: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 7 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                                    ~~~~^
      |                                        |
      |                                        long unsigned int
      |                                    %02u
agent/info.c: In function ‘avg_load’:
agent/info.c:1562:10: warning: unused variable ‘g2’ [-Wunused-variable]
 1562 |    float g2 = round(loads3 / f + e);
      |          ^~
agent/info.c:1561:10: warning: unused variable ‘g1’ [-Wunused-variable]
 1561 |    float g1 = round(loads2 / f + e);
      |          ^~
agent/info.c: In function ‘meminfo’:
agent/info.c:2076:10: warning: unused variable ‘pct_misc’ [-Wunused-variable]
 2076 |    float pct_misc = (float)(kb_main_total - kb_main_available - kb_main_used) * (100.0 / (float)kb_main_total);
      |          ^~~~~~~~
agent/info.c: In function ‘before’:
agent/info.c:2094:8: warning: unused variable ‘linux_version_code’ [-Wunused-variable]
 2094 |    int linux_version_code = procps_linux_version();
      |        ^~~~~~~~~~~~~~~~~~
agent/info.c: In function ‘cpu_help’:
agent/info.c:2218:10: warning: unused variable ‘scale1’ [-Wunused-variable]
 2218 |    float scale1 = 0.0;
      |          ^~~~~~
agent/info.c: In function ‘tasks_refresh’:
agent/info.c:2491:8: warning: unused variable ‘i’ [-Wunused-variable]
 2491 |    int i, what;
      |        ^
agent/info.c:2488:15: warning: unused variable ‘n_alloc’ [-Wunused-variable]
 2488 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c: In function ‘tasks_refresh1’:
agent/info.c:2554:8: warning: unused variable ‘i’ [-Wunused-variable]
 2554 |    int i, what;
      |        ^
agent/info.c:2551:15: warning: unused variable ‘n_alloc’ [-Wunused-variable]
 2551 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c: In function ‘cal_cpu’:
agent/info.c:2765:24: warning: argument to ‘sizeof’ in ‘snprintf’ call is the same expression as the destination; did you mean to provide an explicit length? [-Wsizeof-pointer-memaccess]
 2765 |    snprintf(buf, sizeof(buf), "%#.1f", u);
      |                        ^
agent/info.c: In function ‘cal_mem’:
agent/info.c:2812:24: warning: argument to ‘sizeof’ in ‘snprintf’ call is the same expression as the destination; did you mean to provide an explicit length? [-Wsizeof-pointer-memaccess]
 2812 |    snprintf(buf, sizeof(buf), "%#.1f", m);
      |                        ^
agent/info.c: In function ‘sys’:
agent/info.c:3088:13: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3088 |             return;
      |             ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c:3254:11: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3254 |           return;
      |           ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c:3260:11: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3260 |           return;
      |           ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c:3269:11: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3269 |           return;
      |           ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c: In function ‘get_load’:
agent/info.c:1498:1: warning: control reaches end of non-void function [-Wreturn-type]
 1498 | }
      | ^
agent/info.c: In function ‘output_load’:
agent/info.c:1513:1: warning: control reaches end of non-void function [-Wreturn-type]
 1513 | }
      | ^
agent/info.c: In function ‘get_time1’:
agent/info.c:1533:1: warning: control reaches end of non-void function [-Wreturn-type]
 1533 | }
      | ^
agent/info.c: In function ‘avg_load’:
agent/info.c:1573:1: warning: control reaches end of non-void function [-Wreturn-type]
 1573 | }
      | ^
agent/info.c: In function ‘cpu_help’:
agent/info.c:2260:1: warning: control reaches end of non-void function [-Wreturn-type]
 2260 | }
      | ^
agent/info.c: In function ‘mem_help’:
agent/info.c:2349:1: warning: control reaches end of non-void function [-Wreturn-type]
 2349 | }
      | ^
agent/info.c: In function ‘init_cpu’:
agent/info.c:2620:1: warning: control reaches end of non-void function [-Wreturn-type]
 2620 | }
      | ^
agent/info.c: In function ‘init_parpids’:
agent/info.c:2850:1: warning: control reaches end of non-void function [-Wreturn-type]
 2850 | }
      | ^
agent/info.c: In function ‘init_thrpids’:
agent/info.c:2886:1: warning: control reaches end of non-void function [-Wreturn-type]
 2886 | }
      | ^
agent/info.c: In function ‘add_process_thread_count’:
agent/info.c:2896:1: warning: control reaches end of non-void function [-Wreturn-type]
 2896 | }
      | ^
agent/info.c: In function ‘add_thread_count’:
agent/info.c:2906:1: warning: control reaches end of non-void function [-Wreturn-type]
 2906 | }
      | ^
agent/info.c: At top level:
agent/info.c:2551:15: warning: ‘n_alloc’ defined but not used [-Wunused-variable]
 2551 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c:2488:15: warning: ‘n_alloc’ defined but not used [-Wunused-variable]
 2488 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c:2948:13: warning: ‘wait_for_semaphore’ defined but not used [-Wunused-function]
 2948 | static void wait_for_semaphore(sem_t *sem) {
      |             ^~~~~~~~~~~~~~~~~~
agent/info.c:2664:13: warning: ‘forest_begin’ defined but not used [-Wunused-function]
 2664 | static void forest_begin(WIN_t *q)
      |             ^~~~~~~~~~~~
agent/info.c:2545:14: warning: ‘tasks_refresh1’ defined but not used [-Wunused-function]
 2545 | static void *tasks_refresh1(void *unused)
      |              ^~~~~~~~~~~~~~
agent/info.c:2358:13: warning: ‘wins_stage_1’ defined but not used [-Wunused-function]
 2358 | static void wins_stage_1(void)
      |             ^~~~~~~~~~~~
agent/info.c:1355:13: warning: ‘zap_fieldstab’ defined but not used [-Wunused-function]
 1355 | static void zap_fieldstab(void)
      |             ^~~~~~~~~~~~~
agent/info.c:1348:20: warning: ‘Cursor_state’ defined but not used [-Wunused-variable]
 1348 | static const char *Cursor_state = "";
      |                    ^~~~~~~~~~~~
agent/info.c:1265:13: warning: ‘win_names’ defined but not used [-Wunused-function]
 1265 | static void win_names(WIN_t *q, const char *name)
      |             ^~~~~~~~~
agent/info.c:842:13: warning: ‘Stdout_buf’ defined but not used [-Wunused-variable]
  842 | static char Stdout_buf[2048];
      |             ^~~~~~~~~~
agent/info.c:841:12: warning: ‘Ttychanged’ defined but not used [-Wunused-variable]
  841 | static int Ttychanged = 0;
      |            ^~~~~~~~~~
agent/info.c:840:5: warning: ‘Tty_raw’ defined but not used [-Wunused-variable]
  840 |     Tty_raw; // for unsolicited input
      |     ^~~~~~~
agent/info.c:836:23: warning: ‘Tty_original’ defined but not used [-Wunused-variable]
  836 | static struct termios Tty_original, // our inherited terminal definition
      |                       ^~~~~~~~~~~~
agent/info.c:826:15: warning: ‘Pseudo_size’ defined but not used [-Wunused-variable]
  826 | static size_t Pseudo_size;
      |               ^~~~~~~~~~~
agent/info.c:825:15: warning: ‘Bot_show_func’ defined but not used [-Wunused-variable]
  825 | static void (*Bot_show_func)(void);
      |               ^~~~~~~~~~~~~
agent/info.c:824:14: warning: ‘Bot_focus_func’ defined but not used [-Wunused-variable]
  824 | static BOT_f Bot_focus_func;
      |              ^~~~~~~~~~~~~~
agent/info.c:822:5: warning: ‘Bot_buf’ defined but not used [-Wunused-variable]
  822 |     Bot_buf[BOTBUFSIZ]; // the 'environ' can be huge
      |     ^~~~~~~
agent/info.c:821:6: warning: ‘Bot_head’ defined but not used [-Wunused-variable]
  821 |     *Bot_head,
      |      ^~~~~~~~
agent/info.c:820:13: warning: ‘Bot_sep’ defined but not used [-Wunused-variable]
  820 | static char Bot_sep,
      |             ^~~~~~~
agent/info.c:818:5: warning: ‘Bot_indx’ defined but not used [-Wunused-variable]
  818 |     Bot_indx = BOT_UNFOCUS,
      |     ^~~~~~~~
agent/info.c:817:5: warning: ‘Bot_rsvd’ defined but not used [-Wunused-variable]
  817 |     Bot_rsvd,
      |     ^~~~~~~~
agent/info.c:816:5: warning: ‘Bot_what’ defined but not used [-Wunused-variable]
  816 |     Bot_what,
      |     ^~~~~~~~
agent/info.c:815:12: warning: ‘Bot_task’ defined but not used [-Wunused-variable]
  815 | static int Bot_task,
      |            ^~~~~~~~
agent/info.c:787:12: warning: ‘Cap_can_goto’ defined but not used [-Wunused-variable]
  787 | static int Cap_can_goto = 0;
      |            ^~~~~~~~~~~~
agent/info.c:785:12: warning: ‘Cap_avoid_eol’ defined but not used [-Wunused-variable]
  785 | static int Cap_avoid_eol = 0;
      |            ^~~~~~~~~~~~~
agent/info.c:782:13: warning: ‘Cap_smam’ defined but not used [-Wunused-variable]
  782 |             Cap_smam[CAPBUFSIZ] = "";
      |             ^~~~~~~~
agent/info.c:781:13: warning: ‘Cap_rmam’ defined but not used [-Wunused-variable]
  781 | static char Cap_rmam[CAPBUFSIZ] = "",
      |             ^~~~~~~~
agent/info.c:779:13: warning: ‘Caps_endline’ defined but not used [-Wunused-variable]
  779 |             Caps_endline[SMLBUFSIZ] = "";
      |             ^~~~~~~~~~~~
agent/info.c:778:13: warning: ‘Caps_off’ defined but not used [-Wunused-variable]
  778 |             Caps_off[CAPBUFSIZ] = "",
      |             ^~~~~~~~
agent/info.c:777:13: warning: ‘Cap_reverse’ defined but not used [-Wunused-variable]
  777 |             Cap_reverse[CAPBUFSIZ] = "",
      |             ^~~~~~~~~~~
agent/info.c:775:13: warning: ‘Cap_home’ defined but not used [-Wunused-variable]
  775 |             Cap_home[CAPBUFSIZ] = "",
      |             ^~~~~~~~
agent/info.c:774:5: warning: ‘Cap_clr_eos’ defined but not used [-Wunused-variable]
  774 |     Cap_clr_eos[CAPBUFSIZ] = "",
      |     ^~~~~~~~~~~
agent/info.c:773:5: warning: ‘Cap_curs_hide’ defined but not used [-Wunused-variable]
  773 |     Cap_curs_hide[CAPBUFSIZ] = "",       // batch requirements!
      |     ^~~~~~~~~~~~~
agent/info.c:772:5: warning: ‘Cap_curs_huge’ defined but not used [-Wunused-variable]
  772 |     Cap_curs_huge[CAPBUFSIZ] = "",       // to remind people of those
      |     ^~~~~~~~~~~~~
agent/info.c:771:5: warning: ‘Cap_curs_norm’ defined but not used [-Wunused-variable]
  771 |     Cap_curs_norm[CAPBUFSIZ] = "",       // cost nothing but DO serve
      |     ^~~~~~~~~~~~~
agent/info.c:770:5: warning: ‘Cap_clr_scr’ defined but not used [-Wunused-variable]
  770 |     Cap_clr_scr[CAPBUFSIZ] = "",         // the assignments used here
      |     ^~~~~~~~~~~
agent/info.c:769:5: warning: ‘Cap_nl_clreos’ defined but not used [-Wunused-variable]
  769 |     Cap_nl_clreos[CAPBUFSIZ] = "",       // are initialized to zeros!
      |     ^~~~~~~~~~~~~
agent/info.c:768:13: warning: ‘Cap_clr_eol’ defined but not used [-Wunused-variable]
  768 | static char Cap_clr_eol[CAPBUFSIZ] = "", // global and/or static vars
      |             ^~~~~~~~~~~
agent/info.c:757:14: warning: ‘Myname’ defined but not used [-Wunused-variable]
  757 | static char *Myname;
      |              ^~~~~~
agent/info.c:604:17: warning: ‘Sigwinch_set’ defined but not used [-Wunused-variable]
  604 | static sigset_t Sigwinch_set;
      |                 ^~~~~~~~~~~~
agent/info.c:595:26: warning: ‘Pids_ctx1’ defined but not used [-Wunused-variable]
  595 | static struct pids_info *Pids_ctx1;
      |                          ^~~~~~~~~
agent/info.c:586:5: warning: ‘Width_mode’ defined but not used [-Wunused-variable]
  586 |     Width_mode = 0,   // set w/ 'w' - potential output override
      |     ^~~~~~~~~~
agent/info.c:585:5: warning: ‘Secure_mode’ defined but not used [-Wunused-variable]
  585 |     Secure_mode = 0,  // set if some functionality restricted
      |     ^~~~~~~~~~~
agent/info.c:584:5: warning: ‘Loops’ defined but not used [-Wunused-variable]
  584 |     Loops = -1,       // number of iterations, -1 loops forever
      |     ^~~~~
agent/info.c:583:12: warning: ‘Batch’ defined but not used [-Wunused-variable]
  583 | static int Batch = 0, // batch mode, collect no input, dumb output
      |            ^~~~~
agent/info.c:576:13: warning: ‘Adjoin_sp’ defined but not used [-Wunused-variable]
  576 | static char Adjoin_sp[] = " ~6 ~1";
      |             ^~~~~~~~~
agent/info.c:556:38: warning: ‘Max_lines’ defined but not used [-Wunused-variable]
  556 | static int Screen_cols, Screen_rows, Max_lines;
      |                                      ^~~~~~~~~
agent/info.c:556:25: warning: ‘Screen_rows’ defined but not used [-Wunused-variable]
  556 | static int Screen_cols, Screen_rows, Max_lines;
      |                         ^~~~~~~~~~~
agent/info.c:554:14: warning: ‘Pseudo_screen’ defined but not used [-Wunused-variable]
  554 | static char *Pseudo_screen;
      |              ^~~~~~~~~~~~~
agent/info.c: In function ‘avg_load’:
agent/info.c:1565:33: warning: ‘%d’ directive output may be truncated writing between 1 and 8 bytes into a region of size 7 [-Wformat-truncation=]
 1565 |    snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
      |                                 ^~
agent/info.c:1565:32: note: directive argument in the range [-1048576, 1048575]
 1565 |    snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
      |                                ^~~~~~~~
agent/info.c:1565:32: note: directive argument in the range [0, 99]
agent/info.c:1565:4: note: ‘snprintf’ output between 4 and 12 bytes into a destination of size 7
 1565 |    snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
      |    ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/meminfo.o -MMD -MP -MF .deps/agent/meminfo.d -c agent/meminfo.c -o agent/meminfo.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/namespace.o -MMD -MP -MF .deps/agent/namespace.d -c agent/namespace.c -o agent/namespace.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/numa.o -MMD -MP -MF .deps/agent/numa.d -c agent/numa.c -o agent/numa.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/pids.o -MMD -MP -MF .deps/agent/pids.d -c agent/pids.c -o agent/pids.o
In file included from agent/pids.c:44:
agent/pids.h:271:30: warning: ‘struct pids_info’ declared inside parameter list will not be visible outside of this definition or declaration
  271 | typedef void (*SET_t)(struct pids_info *, struct pids_result *, proc_t *);
      |                              ^~~~~~~~~
agent/pids.c: In function ‘free_pids_str’:
agent/pids.c:135:47: warning: unused parameter ‘R’ [-Wunused-parameter]
  135 | static void freNAME(str) (struct pids_result *R) {
      |                           ~~~~~~~~~~~~~~~~~~~~^
agent/pids.c: In function ‘free_pids_strv’:
agent/pids.c:139:48: warning: unused parameter ‘R’ [-Wunused-parameter]
  139 | static void freNAME(strv) (struct pids_result *R) {
      |                            ~~~~~~~~~~~~~~~~~~~~^
agent/pids.c: In function ‘pids_assign_results’:
agent/pids.c:990:17: warning: passing argument 1 of ‘*that’ from incompatible pointer type [-Wincompatible-pointer-types]
  990 |         (*that)(info, this, p);
      |                 ^~~~
      |                 |
      |                 struct pids_info *
agent/pids.c:990:17: note: expected ‘struct pids_info *’ but argument is of type ‘struct pids_info *’
agent/pids.c: In function ‘pids_cleanup_stacks_all’:
agent/pids.c:1023:13: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1023 |         ext = ext->next;
      |             ^
agent/pids.c: In function ‘pids_itemize_stacks_all’:
agent/pids.c:1082:13: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1082 |         ext = ext->next;
      |             ^
agent/pids.c: In function ‘pids_stacks_alloc’:
agent/pids.c:1315:18: warning: assignment to ‘struct stacks_extent *’ from incompatible pointer type ‘struct stacks_extent2 *’ [-Wincompatible-pointer-types]
 1315 |     p_blob->next = info->extents;                              // push this extent onto... |
      |                  ^
agent/pids.c:1330:12: warning: returning ‘struct stacks_extent2 *’ from a function with incompatible return type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1330 |     return p_blob;
      |            ^~~~~~
agent/pids.c: In function ‘pids_stacks_fetch’:
agent/pids.c:1367:19: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1367 |         if (!(ext = pids_stacks_alloc(info, STACKS_INIT)))
      |                   ^
agent/pids.c:1381:23: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1381 |             || (!(ext = pids_stacks_alloc(info, STACKS_GROW))))
      |                       ^
agent/pids.c: In function ‘pids_stacks_fetch1’:
agent/pids.c:1431:19: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1431 |         if (!(ext = pids_stacks_alloc(info, STACKS_INIT)))
      |                   ^
agent/pids.c:1445:23: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1445 |             || (!(ext = pids_stacks_alloc(info, STACKS_GROW))))
      |                       ^
agent/pids.c: In function ‘procps_pids_unref’:
agent/pids.c:1626:34: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1626 |                 (*info)->extents = (*info)->extents->next;
      |                                  ^
agent/pids.c:1633:25: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1633 |                 nextext = ext->next;
      |                         ^
agent/pids.c: In function ‘procps_pids_get’:
agent/pids.c:1710:29: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1710 |         if (!(info->get_ext = pids_stacks_alloc(info, 1)))
      |                             ^
agent/pids.c: In function ‘procps_pids_reset’:
agent/pids.c:1844:27: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1844 |             info->extents = p->next;
      |                           ^
agent/pids.c: In function ‘procps_pids_sort’:
agent/pids.c:1947:23: warning: variable ‘parms’ set but not used [-Wunused-but-set-variable]
 1947 |     struct sort_parms parms;
      |                       ^~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/pwcache.o -MMD -MP -MF .deps/agent/pwcache.d -c agent/pwcache.c -o agent/pwcache.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/readproc.o -MMD -MP -MF .deps/agent/readproc.d -c agent/readproc.c -o agent/readproc.o
agent/readproc.c: In function ‘stat2proc’:
agent/readproc.c:597:16: warning: unused variable ‘le’ [-Wunused-variable]
  597 |         size_t le = strlen(buf) + 1;
      |                ^~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/send.o -MMD -MP -MF .deps/agent/send.d -c agent/send.c -o agent/send.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/send_data.o -MMD -MP -MF .deps/agent/send_data.d -c agent/send_data.c -o agent/send_data.o
In file included from agent/send_data.c:50:
agent/pids.h:271:30: warning: ‘struct pids_info’ declared inside parameter list will not be visible outside of this definition or declaration
  271 | typedef void (*SET_t)(struct pids_info *, struct pids_result *, proc_t *);
      |                              ^~~~~~~~~
agent/send_data.c: In function ‘sigint_handler’:
agent/send_data.c:263:9: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
  263 |         write_log("ctrl-c caught ....\n");
      |         ^~~~~~~~~
agent/send_data.c:260:25: warning: unused parameter ‘sig’ [-Wunused-parameter]
  260 | void sigint_handler(int sig)
      |                     ~~~~^~~
agent/send_data.c: In function ‘stat2proc’:
agent/send_data.c:365:25: warning: implicit declaration of function ‘escape_str’ [-Wimplicit-function-declaration]
  365 |                         escape_str(buf, raw, sizeof(buf));
      |                         ^~~~~~~~~~
agent/send_data.c:416:9: warning: implicit declaration of function ‘LEAVE’ [-Wimplicit-function-declaration]
  416 |         LEAVE(0x160);
      |         ^~~~~
agent/send_data.c: In function ‘check_self’:
agent/send_data.c:462:24: warning: missing initializer for field ‘siz’ of ‘struct utlbuf_s’ [-Wmissing-field-initializers]
  462 |                 struct utlbuf_s ub = {NULL, 0};
      |                        ^~~~~~~~
In file included from agent/send_data.c:11:
agent/readproc.h:42:11: note: ‘siz’ declared here
   42 |     int   siz;     // current len of the above
      |           ^~~
agent/send_data.c:463:21: warning: variable ‘rc’ set but not used [-Wunused-but-set-variable]
  463 |                 int rc = 0;
      |                     ^~
agent/send_data.c: In function ‘alarm_handler1’:
agent/send_data.c:1191:25: warning: unused parameter ‘sig’ [-Wunused-parameter]
 1191 | void alarm_handler1(int sig)
      |                     ~~~~^~~
agent/send_data.c: At top level:
agent/send_data.c:1574:6: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
 1574 | void write_log(const char *format, ...)
      |      ^~~~~~~~~
agent/send_data.c:263:9: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
  263 |         write_log("ctrl-c caught ....\n");
      |         ^~~~~~~~~
agent/send_data.c: In function ‘main’:
agent/send_data.c:1840:46: warning: passing argument 3 of ‘pthread_create’ from incompatible pointer type [-Wincompatible-pointer-types]
 1840 |                 pthread_create(&sar1, &attr, &sadc1, NULL);
      |                                              ^~~~~~
      |                                              |
      |                                              int (*)()
In file included from agent/top.h:23,
                 from agent/send_data.c:5:
/usr/include/pthread.h:204:36: note: expected ‘void * (*)(void *)’ but argument is of type ‘int (*)()’
  204 |                            void *(*__start_routine) (void *),
      |                            ~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~
agent/send_data.c:1842:50: warning: passing argument 3 of ‘pthread_create’ from incompatible pointer type [-Wincompatible-pointer-types]
 1842 |                 pthread_create(&tcpdump1, &attr, &tcp1, argv[1]);
      |                                                  ^~~~~
      |                                                  |
      |                                                  int (*)(char *)
/usr/include/pthread.h:204:36: note: expected ‘void * (*)(void *)’ but argument is of type ‘int (*)(char *)’
  204 |                            void *(*__start_routine) (void *),
      |                            ~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~
agent/send_data.c:2053:33: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2053 |         for (int isar = 0; isar < os->int_count; isar++) {
      |                                 ^
agent/send_data.c:2064:33: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2064 |         for (int isar = 0; isar < os->enet_count; isar++) {
      |                                 ^
agent/send_data.c:2085:35: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2085 |         for (int idisk = 0; idisk < os->disk_count; idisk++) {
      |                                   ^
agent/send_data.c:2099:31: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2099 |         for (int ifs = 0; ifs < os->file_count; ifs++) {
      |                               ^
agent/send_data.c:2124:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2124 |         for (tcd = 0; tcd < os->group_s; tcd++)
      |                           ^
agent/send_data.c:2141:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2141 |         for (tcd = 0; tcd < os->group_s1; tcd++)
      |                           ^
agent/send_data.c:2159:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2159 |         for (tcd = 0; tcd < os->group_p; tcd++)
      |                           ^
agent/send_data.c:2196:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2196 |         for (tcd = 0; tcd < os->group_f; tcd++)
      |                           ^
agent/send_data.c:2214:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2214 |         for (tcd = 0; tcd < os->group_r; tcd++)
      |                           ^
agent/send_data.c:2232:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2232 |         for (tcd = 0; tcd < os->group_ack; tcd++)
      |                           ^
agent/send_data.c:1756:19: warning: unused variable ‘th’ [-Wunused-variable]
 1756 |         pthread_t th;
      |                   ^~
agent/send_data.c:1723:19: warning: unused variable ‘th1’ [-Wunused-variable]
 1723 |         pthread_t th1;
      |                   ^~~
agent/send_data.c: In function ‘c_tcp1’:
agent/send_data.c:1246:1: warning: control reaches end of non-void function [-Wreturn-type]
 1246 | }
      | ^
agent/send_data.c: In function ‘free_all1’:
agent/send_data.c:1552:1: warning: control reaches end of non-void function [-Wreturn-type]
 1552 | }
      | ^
agent/top.h: At top level:
agent/top.h:104:23: warning: ‘Stat_items’ defined but not used [-Wunused-variable]
  104 | static enum stat_item Stat_items[] = {
      |                       ^~~~~~~~~~
agent/top.h:60:13: warning: ‘UTF8_tab’ defined but not used [-Wunused-variable]
   60 | static char UTF8_tab[] = {
      |             ^~~~~~~~
agent/top.h:56:14: warning: ‘Frame_etscale’ defined but not used [-Wunused-variable]
   56 | static float Frame_etscale;
      |              ^~~~~~~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/stat.o -MMD -MP -MF .deps/agent/stat.d -c agent/stat.c -o agent/stat.o
agent/stat.c: In function ‘procps_stat_reap’:
agent/stat.c:1239:9: warning: unused variable ‘ff’ [-Wunused-variable]
 1239 |     int ff;
      |         ^~
agent/stat.c: In function ‘sum_tics’:
agent/stat.c:1425:10: warning: variable ‘scale’ set but not used [-Wunused-but-set-variable]
 1425 |    float scale;
      |          ^~~~~
agent/stat.c:1424:18: warning: unused variable ‘rx’ [-Wunused-variable]
 1424 |    struct rx_st *rx;
      |                  ^~
agent/stat.c:1423:10: warning: variable ‘idl_frme’ set but not used [-Wunused-but-set-variable]
 1423 |    SIC_t idl_frme, tot_frme;
      |          ^~~~~~~~
agent/stat.c:1433:1: warning: no return statement in function returning non-void [-Wreturn-type]
 1433 | } // end: sum_tics
      | ^
agent/stat.c:1419:41: warning: unused parameter ‘this’ [-Wunused-parameter]
 1419 | static int sum_tics (struct stat_stack *this) {
      |                      ~~~~~~~~~~~~~~~~~~~^~~~
agent/stat.c: At top level:
agent/stat.c:1419:12: warning: ‘sum_tics’ defined but not used [-Wunused-function]
 1419 | static int sum_tics (struct stat_stack *this) {
      |            ^~~~~~~~
agent/stat.c:44:20: warning: ‘Cpu_States_fmts’ defined but not used [-Wunused-variable]
   44 | static const char *Cpu_States_fmts;
      |                    ^~~~~~~~~~~~~~~
agent/stat.c:43:20: warning: ‘Cpu_pmax’ defined but not used [-Wunused-variable]
   43 | static float       Cpu_pmax;
      |                    ^~~~~~~~
agent/stat.c:42:20: warning: ‘Hertz’ defined but not used [-Wunused-variable]
   42 | static long        Hertz;
      |                    ^~~~~
In file included from agent/stat.c:36:
agent/top.h:104:23: warning: ‘Stat_items’ defined but not used [-Wunused-variable]
  104 | static enum stat_item Stat_items[] = {
      |                       ^~~~~~~~~~
agent/top.h:60:13: warning: ‘UTF8_tab’ defined but not used [-Wunused-variable]
   60 | static char UTF8_tab[] = {
      |             ^~~~~~~~
agent/top.h:56:14: warning: ‘Frame_etscale’ defined but not used [-Wunused-variable]
   56 | static float Frame_etscale;
      |              ^~~~~~~~~~~~~
agent/stat.c: In function ‘sum_tics’:
agent/stat.c:1430:7: warning: ‘tot_frme’ is used uninitialized [-Wuninitialized]
 1430 |    if (1 > tot_frme) idl_frme = tot_frme = 1;
      |       ^
agent/stat.c:1423:20: note: ‘tot_frme’ was declared here
 1423 |    SIC_t idl_frme, tot_frme;
      |                    ^~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/sysinfo.o -MMD -MP -MF .deps/agent/sysinfo.d -c agent/sysinfo.c -o agent/sysinfo.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/thpool.o -MMD -MP -MF .deps/agent/thpool.d -c agent/thpool.c -o agent/thpool.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/top.o -MMD -MP -MF .deps/agent/top.d -c agent/top.c -o agent/top.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/top_nls.o -MMD -MP -MF .deps/agent/top_nls.d -c agent/top_nls.c -o agent/top_nls.o
In file included from agent/top_nls.c:24:
agent/top.h: In function ‘utf8_cols’:
agent/top.h:89:23: warning: implicit declaration of function ‘mbtowc’; did you mean ‘mbrtowc’? [-Wimplicit-function-declaration]
   89 |                 (void)mbtowc(&wc, (const char *)p, n);
      |                       ^~~~~~
      |                       mbrtowc
agent/top.h: At top level:
agent/top.h:104:23: warning: ‘Stat_items’ defined but not used [-Wunused-variable]
  104 | static enum stat_item Stat_items[] = {
      |                       ^~~~~~~~~~
agent/top.h:60:13: warning: ‘UTF8_tab’ defined but not used [-Wunused-variable]
   60 | static char UTF8_tab[] = {
      |             ^~~~~~~~
agent/top.h:56:14: warning: ‘Frame_etscale’ defined but not used [-Wunused-variable]
   56 | static float Frame_etscale;
      |              ^~~~~~~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/uptime.o -MMD -MP -MF .deps/agent/uptime.d -c agent/uptime.c -o agent/uptime.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/version.o -MMD -MP -MF .deps/agent/version.d -c agent/version.c -o agent/version.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/wchan.o -MMD -MP -MF .deps/agent/wchan.d -c agent/wchan.c -o agent/wchan.o
make -C ./sar
make[1]: Entering directory '/app/no_perf/sar'
gcc -o act_sadc.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SADC -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  activity.c
gcc -o sa_wrap.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP sa_wrap.c
gcc -o sa_common.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP sa_common.c
gcc -o rd_stats.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SADC -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  rd_stats.c
rd_stats.c: In function ‘read_filesystem’:
rd_stats.c:2361:46: warning: passing argument 2 of ‘statvfs’ from incompatible pointer type [-Wincompatible-pointer-types]
 2361 |                         if ((statvfs(mountp, &buf) < 0) || (!buf.f_blocks))
      |                                              ^~~~
      |                                              |
      |                                              struct statfs *
In file included from rd_stats.c:31:
/usr/include/x86_64-linux-gnu/sys/statvfs.h:52:48: note: expected ‘struct statvfs * restrict’ but argument is of type ‘struct statfs *’
   52 |                     struct statvfs *__restrict __buf)
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~
gcc -o count.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SADC -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  count.c
ar rvs librdstats.a rd_stats.o count.o
r - rd_stats.o
r - count.o
gcc -o rd_sensors.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP rd_sensors.c
ar rv librdsensors.a rd_sensors.o
r - rd_sensors.o
gcc -o sadc.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP sadc.c
In file included from sadc.c:86:
../agent/top.h: In function ‘utf8_cols’:
../agent/top.h:91:26: warning: implicit declaration of function ‘wcwidth’ [-Wimplicit-function-declaration]
   91 |                 if ((n = wcwidth(wc)) < 0)
      |                          ^~~~~~~
gcc -o act_sar.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SAR -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  activity.c
gcc -o public.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP public.c
public.c: In function ‘update_sar_lock1’:
public.c:8:1: warning: control reaches end of non-void function [-Wreturn-type]
    8 | }
      | ^
gcc -o pr_stats.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP pr_stats.c
In file included from pr_stats.c:33:
../agent/top.h: In function ‘utf8_cols’:
../agent/top.h:91:26: warning: implicit declaration of function ‘wcwidth’ [-Wimplicit-function-declaration]
   91 |                 if ((n = wcwidth(wc)) < 0)
      |                          ^~~~~~~
gcc -o network_security.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP network_security.c
network_security.c: In function ‘build_socket_process_map’:
network_security.c:88:55: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size between 236 and 245 [-Wformat-truncation=]
   88 |             snprintf(path, sizeof(path), "/proc/%d/fd/%s", pid, fd_entry->d_name);
      |                                                       ^~
network_security.c:88:13: note: ‘snprintf’ output between 12 and 276 bytes into a destination of size 256
   88 |             snprintf(path, sizeof(path), "/proc/%d/fd/%s", pid, fd_entry->d_name);
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
gcc -o iostat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP iostat.c
iostat.c: In function ‘read_sysfs_dlist_part_stat’:
iostat.c:565:53: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size between 0 and 1023 [-Wformat-truncation=]
  565 |                 snprintf(filename, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                                                     ^~
iostat.c:565:17: note: ‘snprintf’ output between 7 and 1285 bytes into a destination of size 1024
  565 |                 snprintf(filename, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
gcc -o rd_stats_light.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  rd_stats.c
rd_stats.c: In function ‘read_filesystem’:
rd_stats.c:2361:46: warning: passing argument 2 of ‘statvfs’ from incompatible pointer type [-Wincompatible-pointer-types]
 2361 |                         if ((statvfs(mountp, &buf) < 0) || (!buf.f_blocks))
      |                                              ^~~~
      |                                              |
      |                                              struct statfs *
In file included from rd_stats.c:31:
/usr/include/x86_64-linux-gnu/sys/statvfs.h:52:48: note: expected ‘struct statvfs * restrict’ but argument is of type ‘struct statfs *’
   52 |                     struct statvfs *__restrict __buf)
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~
gcc -o count_light.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  count.c
ar rvs librdstats_light.a rd_stats_light.o count_light.o
r - rd_stats_light.o
r - count_light.o
gcc -o common.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP common.c
common.c: In function ‘get_dev_part_nr’:
common.c:214:49: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size between 0 and 1023 [-Wformat-truncation=]
  214 |                 snprintf(line, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                                                 ^~
common.c:214:17: note: ‘snprintf’ output between 7 and 1285 bytes into a destination of size 1024
  214 |                 snprintf(line, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
gcc -o ioconf.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP ioconf.c
ioconf.c: In function ‘ioc_init’:
ioconf.c:301:46: warning: ‘%s’ directive writing up to 31 bytes into a region of size 16 [-Wformat-overflow=]
  301 |                         sprintf(blkp->cfmt, "%s%s%%d", blkp->name, cfmt);
      |                                              ^~
ioconf.c:301:25: note: ‘sprintf’ output between 3 and 49 bytes into a destination of size 16
  301 |                         sprintf(blkp->cfmt, "%s%s%%d", blkp->name, cfmt);
      |                         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ioconf.c:322:42: warning: ‘d’ directive writing 1 byte into a region of size between 0 and 15 [-Wformat-overflow=]
  322 |                 sprintf(blkp->pfmt, "%s%%d", (*pfmt == '*') ? "" : pfmt);
      |                                          ^
ioconf.c:322:17: note: ‘sprintf’ output between 3 and 18 bytes into a destination of size 16
  322 |                 sprintf(blkp->pfmt, "%s%%d", (*pfmt == '*') ? "" : pfmt);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ioconf.c: In function ‘transform_devmapname’:
ioconf.c:497:51: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size 244 [-Wformat-truncation=]
  497 |                 snprintf(filen, MAX_FILE_LEN, "%s/%s", DEVMAP_DIR, dp->d_name);
      |                                                   ^~
ioconf.c:497:17: note: ‘snprintf’ output between 13 and 268 bytes into a destination of size 256
  497 |                 snprintf(filen, MAX_FILE_LEN, "%s/%s", DEVMAP_DIR, dp->d_name);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ar rvs libsyscom.a common.o ioconf.o
r - common.o
r - ioconf.o
gcc -o iostat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context iostat.o librdstats_light.a libsyscom.a 
gcc -o mpstat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP mpstat.c
gcc -o mpstat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context mpstat.o librdstats_light.a libsyscom.a 
gcc -o pidstat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP pidstat.c
gcc -o pidstat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context pidstat.o librdstats_light.a libsyscom.a 
gcc -o nfsiostat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP nfsiostat.c
gcc -o nfsiostat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context nfsiostat.o librdstats_light.a libsyscom.a 
gcc -o cifsiostat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP cifsiostat.c
gcc -o cifsiostat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context cifsiostat.o librdstats_light.a libsyscom.a 
make[1]: Leaving directory '/app/no_perf/sar'
make -C ./libpcap
make[1]: Entering directory '/app/no_perf/libpcap'
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-linux.o pcap-linux.c
pcap-linux.c: In function ‘pcap_wait_for_frames_mmap’:
pcap-linux.c:3664:25: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
 3664 |                         write_log("pcap_wait_for_frames_mmap: POSIX定时器信号触发(循环开始)，1秒时间到\n");
      |                         ^~~~~~~~~
pcap-linux.c: In function ‘pcap_read_linux_mmap_v2’:
pcap-linux.c:4448:24: warning: implicit declaration of function ‘get_optimal_timer_fd’ [-Wimplicit-function-declaration]
 4448 |         int timer_fd = get_optimal_timer_fd();  // 获取v2函数的定时器
      |                        ^~~~~~~~~~~~~~~~~~~~
pcap-linux.c: At top level:
pcap-linux.c:6351:13: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
 6351 | extern void write_log(const char *format, ...);
      |             ^~~~~~~~~
pcap-linux.c:3664:25: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
 3664 |                         write_log("pcap_wait_for_frames_mmap: POSIX定时器信号触发(循环开始)，1秒时间到\n");
      |                         ^~~~~~~~~
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o fad-getad.o fad-getad.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-usb-linux.o pcap-usb-linux.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-netfilter-linux.o pcap-netfilter-linux.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap.o pcap.c
pcap.c: In function ‘posix_timer_handler’:
pcap.c:242:9: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
  242 |         write_log("POSIX定时器信号触发，设置pcap_timeout1=1\n");
      |         ^~~~~~~~~
pcap.c: At top level:
pcap.c:259:13: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
  259 | extern void write_log(const char *format, ...);
      |             ^~~~~~~~~
pcap.c:242:9: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
  242 |         write_log("POSIX定时器信号触发，设置pcap_timeout1=1\n");
      |         ^~~~~~~~~
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c ./gencode.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o optimize.o optimize.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o nametoaddr.o nametoaddr.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o etherent.o etherent.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o fmtutils.o fmtutils.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-util.o pcap-util.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o savefile.o savefile.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o sf-pcap.o sf-pcap.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o sf-pcapng.o sf-pcapng.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-common.o pcap-common.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o bpf_image.o bpf_image.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o bpf_filter.o bpf_filter.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o bpf_dump.o bpf_dump.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c scanner.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c grammar.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -o strlcat.o -c ./missing/strlcat.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -o strlcpy.o -c ./missing/strlcpy.c
ar rc libpcap.a pcap-linux.o fad-getad.o pcap-usb-linux.o pcap-netfilter-linux.o pcap.o gencode.o optimize.o nametoaddr.o etherent.o fmtutils.o pcap-util.o savefile.o sf-pcap.o sf-pcapng.o pcap-common.o bpf_image.o bpf_filter.o bpf_dump.o scanner.o grammar.o strlcat.o strlcpy.o 
ranlib libpcap.a
VER=`cat ./VERSION`; \
MAJOR_VER=`sed 's/\([0-9][0-9]*\)\..*/\1/' ./VERSION`; \
gcc  -shared -Wl,-soname,libpcap.so.$MAJOR_VER \
    -o libpcap.so.$VER pcap-linux.o fad-getad.o pcap-usb-linux.o pcap-netfilter-linux.o pcap.o gencode.o optimize.o nametoaddr.o etherent.o fmtutils.o pcap-util.o savefile.o sf-pcap.o sf-pcapng.o pcap-common.o bpf_image.o bpf_filter.o bpf_dump.o scanner.o grammar.o strlcat.o strlcpy.o  
make[1]: Leaving directory '/app/no_perf/libpcap'
make -C ./tcpdump-5.0.0 tcpdump.o fptype.o libnetdissect.a
make[1]: Entering directory '/app/no_perf/tcpdump-5.0.0'
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o tcpdump.o tcpdump.c
tcpdump.c: In function ‘init_barriers’:
tcpdump.c:384:17: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
  384 |                 write_log("init_barriers: barriers已经初始化\n");
      |                 ^~~~~~~~~
tcpdump.c:391:13: warning: implicit declaration of function ‘efficient_barrier_init’; did you mean ‘pthread_barrier_init’? [-Wimplicit-function-declaration]
  391 |         if (efficient_barrier_init(&barrier_a_done, 3) != 0) {
      |             ^~~~~~~~~~~~~~~~~~~~~~
      |             pthread_barrier_init
tcpdump.c:399:17: warning: implicit declaration of function ‘efficient_barrier_destroy’; did you mean ‘pthread_barrier_destroy’? [-Wimplicit-function-declaration]
  399 |                 efficient_barrier_destroy(&barrier_a_done);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~
      |                 pthread_barrier_destroy
tcpdump.c: At top level:
tcpdump.c:3655:6: warning: conflicting types for ‘efficient_barrier_destroy’; have ‘void(efficient_barrier_t *)’
 3655 | void efficient_barrier_destroy(efficient_barrier_t *barrier)
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~
tcpdump.c:399:17: note: previous implicit declaration of ‘efficient_barrier_destroy’ with type ‘void(efficient_barrier_t *)’
  399 |                 efficient_barrier_destroy(&barrier_a_done);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o fptype.o fptype.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o addrtoname.o addrtoname.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o addrtostr.o addrtostr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o af.o af.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o ascii_strcasecmp.o ascii_strcasecmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o checksum.o checksum.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o cpack.o cpack.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o gmpls.o gmpls.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o in_cksum.o in_cksum.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o ipproto.o ipproto.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o l2vpn.o l2vpn.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o netdissect.o netdissect.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o netdissect-alloc.o netdissect-alloc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o nlpid.o nlpid.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o ntp.o ntp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o oui.o oui.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o parsenfsfh.o parsenfsfh.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print.o print.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-802_11.o print-802_11.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-802_15_4.o print-802_15_4.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ah.o print-ah.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ahcp.o print-ahcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-aodv.o print-aodv.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-aoe.o print-aoe.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ap1394.o print-ap1394.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-arcnet.o print-arcnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-arista.o print-arista.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-arp.o print-arp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ascii.o print-ascii.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-atalk.o print-atalk.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-atm.o print-atm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-babel.o print-babel.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bcm-li.o print-bcm-li.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-beep.o print-beep.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bfd.o print-bfd.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bgp.o print-bgp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bootp.o print-bootp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-brcmtag.o print-brcmtag.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bt.o print-bt.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-calm-fast.o print-calm-fast.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-carp.o print-carp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cdp.o print-cdp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cfm.o print-cfm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-chdlc.o print-chdlc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cip.o print-cip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cnfp.o print-cnfp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dccp.o print-dccp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-decnet.o print-decnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dhcp6.o print-dhcp6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-domain.o print-domain.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dsa.o print-dsa.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dtp.o print-dtp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dvmrp.o print-dvmrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-eap.o print-eap.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-egp.o print-egp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-eigrp.o print-eigrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-enc.o print-enc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-erspan.o print-erspan.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-esp.o print-esp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ether.o print-ether.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-fddi.o print-fddi.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-forces.o print-forces.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-fr.o print-fr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-frag6.o print-frag6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ftp.o print-ftp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-geneve.o print-geneve.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-geonet.o print-geonet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-gre.o print-gre.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-hncp.o print-hncp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-hsrp.o print-hsrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-http.o print-http.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-icmp.o print-icmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-icmp6.o print-icmp6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-igmp.o print-igmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-igrp.o print-igrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip-demux.o print-ip-demux.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip.o print-ip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip6.o print-ip6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip6opts.o print-ip6opts.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipcomp.o print-ipcomp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipfc.o print-ipfc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipnet.o print-ipnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipoib.o print-ipoib.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipx.o print-ipx.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-isakmp.o print-isakmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-isoclns.o print-isoclns.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-juniper.o print-juniper.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-krb.o print-krb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-l2tp.o print-l2tp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lane.o print-lane.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ldp.o print-ldp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lisp.o print-lisp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-llc.o print-llc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lldp.o print-lldp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lmp.o print-lmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-loopback.o print-loopback.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lspping.o print-lspping.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lwapp.o print-lwapp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lwres.o print-lwres.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-m3ua.o print-m3ua.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-macsec.o print-macsec.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mobile.o print-mobile.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mobility.o print-mobility.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mpcp.o print-mpcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mpls.o print-mpls.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mptcp.o print-mptcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-msdp.o print-msdp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-msnlb.o print-msnlb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nflog.o print-nflog.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nfs.o print-nfs.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nhrp.o print-nhrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nsh.o print-nsh.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ntp.o print-ntp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-null.o print-null.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-olsr.o print-olsr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-openflow-1.0.o print-openflow-1.0.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-openflow-1.3.o print-openflow-1.3.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-openflow.o print-openflow.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ospf.o print-ospf.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ospf6.o print-ospf6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-otv.o print-otv.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pflog.o print-pflog.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pgm.o print-pgm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pim.o print-pim.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pktap.o print-pktap.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ppi.o print-ppi.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ppp.o print-ppp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pppoe.o print-pppoe.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pptp.o print-pptp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ptp.o print-ptp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-quic.o print-quic.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-radius.o print-radius.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-raw.o print-raw.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-realtek.o print-realtek.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-resp.o print-resp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rip.o print-rip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ripng.o print-ripng.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rpki-rtr.o print-rpki-rtr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rsvp.o print-rsvp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rt6.o print-rt6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rtsp.o print-rtsp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rx.o print-rx.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sctp.o print-sctp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sflow.o print-sflow.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sip.o print-sip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sl.o print-sl.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sll.o print-sll.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-slow.o print-slow.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-smtp.o print-smtp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-snmp.o print-snmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-someip.o print-someip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ssh.o print-ssh.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-stp.o print-stp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sunatm.o print-sunatm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sunrpc.o print-sunrpc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-symantec.o print-symantec.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-syslog.o print-syslog.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-tcp.o print-tcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-telnet.o print-telnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-tftp.o print-tftp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-timed.o print-timed.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-tipc.o print-tipc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-token.o print-token.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-udld.o print-udld.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-udp.o print-udp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-unsupported.o print-unsupported.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-usb.o print-usb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vjc.o print-vjc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vqp.o print-vqp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vrrp.o print-vrrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vsock.o print-vsock.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vtp.o print-vtp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vxlan-gpe.o print-vxlan-gpe.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vxlan.o print-vxlan.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-wb.o print-wb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-whois.o print-whois.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-zep.o print-zep.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-zephyr.o print-zephyr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-zeromq.o print-zeromq.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o signature.o signature.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o strtoaddr.o strtoaddr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o util-print.o util-print.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -o strlcat.o -c ./missing/strlcat.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -o strlcpy.o -c ./missing/strlcpy.c
ar cr libnetdissect.a addrtoname.o addrtostr.o af.o ascii_strcasecmp.o checksum.o cpack.o gmpls.o in_cksum.o ipproto.o l2vpn.o netdissect.o netdissect-alloc.o nlpid.o ntp.o oui.o parsenfsfh.o print.o print-802_11.o print-802_15_4.o print-ah.o print-ahcp.o print-aodv.o print-aoe.o print-ap1394.o print-arcnet.o print-arista.o print-arp.o print-ascii.o print-atalk.o print-atm.o print-babel.o print-bcm-li.o print-beep.o print-bfd.o print-bgp.o print-bootp.o print-brcmtag.o print-bt.o print-calm-fast.o print-carp.o print-cdp.o print-cfm.o print-chdlc.o print-cip.o print-cnfp.o print-dccp.o print-decnet.o print-dhcp6.o print-domain.o print-dsa.o print-dtp.o print-dvmrp.o print-eap.o print-egp.o print-eigrp.o print-enc.o print-erspan.o print-esp.o print-ether.o print-fddi.o print-forces.o print-fr.o print-frag6.o print-ftp.o print-geneve.o print-geonet.o print-gre.o print-hncp.o print-hsrp.o print-http.o print-icmp.o print-icmp6.o print-igmp.o print-igrp.o print-ip-demux.o print-ip.o print-ip6.o print-ip6opts.o print-ipcomp.o print-ipfc.o print-ipnet.o print-ipoib.o print-ipx.o print-isakmp.o print-isoclns.o print-juniper.o print-krb.o print-l2tp.o print-lane.o print-ldp.o print-lisp.o print-llc.o print-lldp.o print-lmp.o print-loopback.o print-lspping.o print-lwapp.o print-lwres.o print-m3ua.o print-macsec.o print-mobile.o print-mobility.o print-mpcp.o print-mpls.o print-mptcp.o print-msdp.o print-msnlb.o print-nflog.o print-nfs.o print-nhrp.o print-nsh.o print-ntp.o print-null.o print-olsr.o print-openflow-1.0.o print-openflow-1.3.o print-openflow.o print-ospf.o print-ospf6.o print-otv.o print-pflog.o print-pgm.o print-pim.o print-pktap.o print-ppi.o print-ppp.o print-pppoe.o print-pptp.o print-ptp.o print-quic.o print-radius.o print-raw.o print-realtek.o print-resp.o print-rip.o print-ripng.o print-rpki-rtr.o print-rsvp.o print-rt6.o print-rtsp.o print-rx.o print-sctp.o print-sflow.o print-sip.o print-sl.o print-sll.o print-slow.o print-smtp.o print-snmp.o print-someip.o print-ssh.o print-stp.o print-sunatm.o print-sunrpc.o print-symantec.o print-syslog.o print-tcp.o print-telnet.o print-tftp.o print-timed.o print-tipc.o print-token.o print-udld.o print-udp.o print-unsupported.o print-usb.o print-vjc.o print-vqp.o print-vrrp.o print-vsock.o print-vtp.o print-vxlan-gpe.o print-vxlan.o print-wb.o print-whois.o print-zep.o print-zephyr.o print-zeromq.o signature.o strtoaddr.o util-print.o  strlcat.o strlcpy.o
ranlib libnetdissect.a
make[1]: Leaving directory '/app/no_perf/tcpdump-5.0.0'
cc -g ./agent/devname.o ./agent/err.o ./agent/escape.o ./agent/info.o ./agent/meminfo.o ./agent/namespace.o ./agent/numa.o ./agent/pids.o ./agent/pwcache.o ./agent/readproc.o ./agent/send.o ./agent/send_data.o ./agent/stat.o ./agent/sysinfo.o ./agent/thpool.o ./agent/top.o ./agent/top_nls.o ./agent/uptime.o ./agent/version.o ./agent/wchan.o  ./sar/act_sadc.o ./sar/sadc.o ./sar/pr_stats.o  ./sar/sa_wrap.o ./sar/sa_common.o ./sar/network_security.o ./sar/librdstats.a ./sar/librdsensors.a ./sar/libsyscom.a  ./tcpdump-5.0.0/fptype.o ./tcpdump-5.0.0/tcpdump.o ./tcpdump-5.0.0/libnetdissect.a ./libpcap/libpcap.a -o send_data -lm -lpthread -ldl -lrt -lcrypto
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/devname.o -MMD -MP -MF .deps/agent/devname.d -c agent/devname.c -o agent/devname.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/err.o -MMD -MP -MF .deps/agent/err.d -c agent/err.c -o agent/err.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/escape.o -MMD -MP -MF .deps/agent/escape.d -c agent/escape.c -o agent/escape.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/info.o -MMD -MP -MF .deps/agent/info.d -c agent/info.c -o agent/info.o
In file included from agent/info.c:545:
agent/pids.h:271:30: warning: ‘struct pids_info’ declared inside parameter list will not be visible outside of this definition or declaration
  271 | typedef void (*SET_t)(struct pids_info *, struct pids_result *, proc_t *);
      |                              ^~~~~~~~~
agent/info.c: In function ‘zap_fieldstab’:
agent/info.c:1455:10: warning: suggest braces around empty body in an ‘if’ statement [-Wempty-body]
 1455 |          ;
      |          ^
agent/info.c: In function ‘output_load’:
agent/info.c:1508:14: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 2 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |            ~~^
      |              |
      |              long unsigned int
      |            %u
agent/info.c:1508:20: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 3 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                ~~~~^
      |                    |
      |                    long unsigned int
      |                %02u
agent/info.c:1508:24: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 4 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                      ~~^
      |                        |
      |                        long unsigned int
      |                      %u
agent/info.c:1508:30: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 5 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                          ~~~~^
      |                              |
      |                              long unsigned int
      |                          %02u
agent/info.c:1508:34: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 6 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                                ~~^
      |                                  |
      |                                  long unsigned int
      |                                %u
agent/info.c:1508:40: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 7 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                                    ~~~~^
      |                                        |
      |                                        long unsigned int
      |                                    %02u
agent/info.c: In function ‘avg_load’:
agent/info.c:1562:10: warning: unused variable ‘g2’ [-Wunused-variable]
 1562 |    float g2 = round(loads3 / f + e);
      |          ^~
agent/info.c:1561:10: warning: unused variable ‘g1’ [-Wunused-variable]
 1561 |    float g1 = round(loads2 / f + e);
      |          ^~
agent/info.c: In function ‘meminfo’:
agent/info.c:2076:10: warning: unused variable ‘pct_misc’ [-Wunused-variable]
 2076 |    float pct_misc = (float)(kb_main_total - kb_main_available - kb_main_used) * (100.0 / (float)kb_main_total);
      |          ^~~~~~~~
agent/info.c: In function ‘before’:
agent/info.c:2094:8: warning: unused variable ‘linux_version_code’ [-Wunused-variable]
 2094 |    int linux_version_code = procps_linux_version();
      |        ^~~~~~~~~~~~~~~~~~
agent/info.c: In function ‘cpu_help’:
agent/info.c:2218:10: warning: unused variable ‘scale1’ [-Wunused-variable]
 2218 |    float scale1 = 0.0;
      |          ^~~~~~
agent/info.c: In function ‘tasks_refresh’:
agent/info.c:2491:8: warning: unused variable ‘i’ [-Wunused-variable]
 2491 |    int i, what;
      |        ^
agent/info.c:2488:15: warning: unused variable ‘n_alloc’ [-Wunused-variable]
 2488 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c: In function ‘tasks_refresh1’:
agent/info.c:2554:8: warning: unused variable ‘i’ [-Wunused-variable]
 2554 |    int i, what;
      |        ^
agent/info.c:2551:15: warning: unused variable ‘n_alloc’ [-Wunused-variable]
 2551 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c: In function ‘cal_cpu’:
agent/info.c:2765:24: warning: argument to ‘sizeof’ in ‘snprintf’ call is the same expression as the destination; did you mean to provide an explicit length? [-Wsizeof-pointer-memaccess]
 2765 |    snprintf(buf, sizeof(buf), "%#.1f", u);
      |                        ^
agent/info.c: In function ‘cal_mem’:
agent/info.c:2812:24: warning: argument to ‘sizeof’ in ‘snprintf’ call is the same expression as the destination; did you mean to provide an explicit length? [-Wsizeof-pointer-memaccess]
 2812 |    snprintf(buf, sizeof(buf), "%#.1f", m);
      |                        ^
agent/info.c: In function ‘sys’:
agent/info.c:3088:13: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3088 |             return;
      |             ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c:3254:11: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3254 |           return;
      |           ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c:3260:11: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3260 |           return;
      |           ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c:3269:11: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3269 |           return;
      |           ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c: In function ‘get_load’:
agent/info.c:1498:1: warning: control reaches end of non-void function [-Wreturn-type]
 1498 | }
      | ^
agent/info.c: In function ‘output_load’:
agent/info.c:1513:1: warning: control reaches end of non-void function [-Wreturn-type]
 1513 | }
      | ^
agent/info.c: In function ‘get_time1’:
agent/info.c:1533:1: warning: control reaches end of non-void function [-Wreturn-type]
 1533 | }
      | ^
agent/info.c: In function ‘avg_load’:
agent/info.c:1573:1: warning: control reaches end of non-void function [-Wreturn-type]
 1573 | }
      | ^
agent/info.c: In function ‘cpu_help’:
agent/info.c:2260:1: warning: control reaches end of non-void function [-Wreturn-type]
 2260 | }
      | ^
agent/info.c: In function ‘mem_help’:
agent/info.c:2349:1: warning: control reaches end of non-void function [-Wreturn-type]
 2349 | }
      | ^
agent/info.c: In function ‘init_cpu’:
agent/info.c:2620:1: warning: control reaches end of non-void function [-Wreturn-type]
 2620 | }
      | ^
agent/info.c: In function ‘init_parpids’:
agent/info.c:2850:1: warning: control reaches end of non-void function [-Wreturn-type]
 2850 | }
      | ^
agent/info.c: In function ‘init_thrpids’:
agent/info.c:2886:1: warning: control reaches end of non-void function [-Wreturn-type]
 2886 | }
      | ^
agent/info.c: In function ‘add_process_thread_count’:
agent/info.c:2896:1: warning: control reaches end of non-void function [-Wreturn-type]
 2896 | }
      | ^
agent/info.c: In function ‘add_thread_count’:
agent/info.c:2906:1: warning: control reaches end of non-void function [-Wreturn-type]
 2906 | }
      | ^
agent/info.c: At top level:
agent/info.c:2551:15: warning: ‘n_alloc’ defined but not used [-Wunused-variable]
 2551 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c:2488:15: warning: ‘n_alloc’ defined but not used [-Wunused-variable]
 2488 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c:2948:13: warning: ‘wait_for_semaphore’ defined but not used [-Wunused-function]
 2948 | static void wait_for_semaphore(sem_t *sem) {
      |             ^~~~~~~~~~~~~~~~~~
agent/info.c:2664:13: warning: ‘forest_begin’ defined but not used [-Wunused-function]
 2664 | static void forest_begin(WIN_t *q)
      |             ^~~~~~~~~~~~
agent/info.c:2545:14: warning: ‘tasks_refresh1’ defined but not used [-Wunused-function]
 2545 | static void *tasks_refresh1(void *unused)
      |              ^~~~~~~~~~~~~~
agent/info.c:2358:13: warning: ‘wins_stage_1’ defined but not used [-Wunused-function]
 2358 | static void wins_stage_1(void)
      |             ^~~~~~~~~~~~
agent/info.c:1355:13: warning: ‘zap_fieldstab’ defined but not used [-Wunused-function]
 1355 | static void zap_fieldstab(void)
      |             ^~~~~~~~~~~~~
agent/info.c:1348:20: warning: ‘Cursor_state’ defined but not used [-Wunused-variable]
 1348 | static const char *Cursor_state = "";
      |                    ^~~~~~~~~~~~
agent/info.c:1265:13: warning: ‘win_names’ defined but not used [-Wunused-function]
 1265 | static void win_names(WIN_t *q, const char *name)
      |             ^~~~~~~~~
agent/info.c:842:13: warning: ‘Stdout_buf’ defined but not used [-Wunused-variable]
  842 | static char Stdout_buf[2048];
      |             ^~~~~~~~~~
agent/info.c:841:12: warning: ‘Ttychanged’ defined but not used [-Wunused-variable]
  841 | static int Ttychanged = 0;
      |            ^~~~~~~~~~
agent/info.c:840:5: warning: ‘Tty_raw’ defined but not used [-Wunused-variable]
  840 |     Tty_raw; // for unsolicited input
      |     ^~~~~~~
agent/info.c:836:23: warning: ‘Tty_original’ defined but not used [-Wunused-variable]
  836 | static struct termios Tty_original, // our inherited terminal definition
      |                       ^~~~~~~~~~~~
agent/info.c:826:15: warning: ‘Pseudo_size’ defined but not used [-Wunused-variable]
  826 | static size_t Pseudo_size;
      |               ^~~~~~~~~~~
agent/info.c:825:15: warning: ‘Bot_show_func’ defined but not used [-Wunused-variable]
  825 | static void (*Bot_show_func)(void);
      |               ^~~~~~~~~~~~~
agent/info.c:824:14: warning: ‘Bot_focus_func’ defined but not used [-Wunused-variable]
  824 | static BOT_f Bot_focus_func;
      |              ^~~~~~~~~~~~~~
agent/info.c:822:5: warning: ‘Bot_buf’ defined but not used [-Wunused-variable]
  822 |     Bot_buf[BOTBUFSIZ]; // the 'environ' can be huge
      |     ^~~~~~~
agent/info.c:821:6: warning: ‘Bot_head’ defined but not used [-Wunused-variable]
  821 |     *Bot_head,
      |      ^~~~~~~~
agent/info.c:820:13: warning: ‘Bot_sep’ defined but not used [-Wunused-variable]
  820 | static char Bot_sep,
      |             ^~~~~~~
agent/info.c:818:5: warning: ‘Bot_indx’ defined but not used [-Wunused-variable]
  818 |     Bot_indx = BOT_UNFOCUS,
      |     ^~~~~~~~
agent/info.c:817:5: warning: ‘Bot_rsvd’ defined but not used [-Wunused-variable]
  817 |     Bot_rsvd,
      |     ^~~~~~~~
agent/info.c:816:5: warning: ‘Bot_what’ defined but not used [-Wunused-variable]
  816 |     Bot_what,
      |     ^~~~~~~~
agent/info.c:815:12: warning: ‘Bot_task’ defined but not used [-Wunused-variable]
  815 | static int Bot_task,
      |            ^~~~~~~~
agent/info.c:787:12: warning: ‘Cap_can_goto’ defined but not used [-Wunused-variable]
  787 | static int Cap_can_goto = 0;
      |            ^~~~~~~~~~~~
agent/info.c:785:12: warning: ‘Cap_avoid_eol’ defined but not used [-Wunused-variable]
  785 | static int Cap_avoid_eol = 0;
      |            ^~~~~~~~~~~~~
agent/info.c:782:13: warning: ‘Cap_smam’ defined but not used [-Wunused-variable]
  782 |             Cap_smam[CAPBUFSIZ] = "";
      |             ^~~~~~~~
agent/info.c:781:13: warning: ‘Cap_rmam’ defined but not used [-Wunused-variable]
  781 | static char Cap_rmam[CAPBUFSIZ] = "",
      |             ^~~~~~~~
agent/info.c:779:13: warning: ‘Caps_endline’ defined but not used [-Wunused-variable]
  779 |             Caps_endline[SMLBUFSIZ] = "";
      |             ^~~~~~~~~~~~
agent/info.c:778:13: warning: ‘Caps_off’ defined but not used [-Wunused-variable]
  778 |             Caps_off[CAPBUFSIZ] = "",
      |             ^~~~~~~~
agent/info.c:777:13: warning: ‘Cap_reverse’ defined but not used [-Wunused-variable]
  777 |             Cap_reverse[CAPBUFSIZ] = "",
      |             ^~~~~~~~~~~
agent/info.c:775:13: warning: ‘Cap_home’ defined but not used [-Wunused-variable]
  775 |             Cap_home[CAPBUFSIZ] = "",
      |             ^~~~~~~~
agent/info.c:774:5: warning: ‘Cap_clr_eos’ defined but not used [-Wunused-variable]
  774 |     Cap_clr_eos[CAPBUFSIZ] = "",
      |     ^~~~~~~~~~~
agent/info.c:773:5: warning: ‘Cap_curs_hide’ defined but not used [-Wunused-variable]
  773 |     Cap_curs_hide[CAPBUFSIZ] = "",       // batch requirements!
      |     ^~~~~~~~~~~~~
agent/info.c:772:5: warning: ‘Cap_curs_huge’ defined but not used [-Wunused-variable]
  772 |     Cap_curs_huge[CAPBUFSIZ] = "",       // to remind people of those
      |     ^~~~~~~~~~~~~
agent/info.c:771:5: warning: ‘Cap_curs_norm’ defined but not used [-Wunused-variable]
  771 |     Cap_curs_norm[CAPBUFSIZ] = "",       // cost nothing but DO serve
      |     ^~~~~~~~~~~~~
agent/info.c:770:5: warning: ‘Cap_clr_scr’ defined but not used [-Wunused-variable]
  770 |     Cap_clr_scr[CAPBUFSIZ] = "",         // the assignments used here
      |     ^~~~~~~~~~~
agent/info.c:769:5: warning: ‘Cap_nl_clreos’ defined but not used [-Wunused-variable]
  769 |     Cap_nl_clreos[CAPBUFSIZ] = "",       // are initialized to zeros!
      |     ^~~~~~~~~~~~~
agent/info.c:768:13: warning: ‘Cap_clr_eol’ defined but not used [-Wunused-variable]
  768 | static char Cap_clr_eol[CAPBUFSIZ] = "", // global and/or static vars
      |             ^~~~~~~~~~~
agent/info.c:757:14: warning: ‘Myname’ defined but not used [-Wunused-variable]
  757 | static char *Myname;
      |              ^~~~~~
agent/info.c:604:17: warning: ‘Sigwinch_set’ defined but not used [-Wunused-variable]
  604 | static sigset_t Sigwinch_set;
      |                 ^~~~~~~~~~~~
agent/info.c:595:26: warning: ‘Pids_ctx1’ defined but not used [-Wunused-variable]
  595 | static struct pids_info *Pids_ctx1;
      |                          ^~~~~~~~~
agent/info.c:586:5: warning: ‘Width_mode’ defined but not used [-Wunused-variable]
  586 |     Width_mode = 0,   // set w/ 'w' - potential output override
      |     ^~~~~~~~~~
agent/info.c:585:5: warning: ‘Secure_mode’ defined but not used [-Wunused-variable]
  585 |     Secure_mode = 0,  // set if some functionality restricted
      |     ^~~~~~~~~~~
agent/info.c:584:5: warning: ‘Loops’ defined but not used [-Wunused-variable]
  584 |     Loops = -1,       // number of iterations, -1 loops forever
      |     ^~~~~
agent/info.c:583:12: warning: ‘Batch’ defined but not used [-Wunused-variable]
  583 | static int Batch = 0, // batch mode, collect no input, dumb output
      |            ^~~~~
agent/info.c:576:13: warning: ‘Adjoin_sp’ defined but not used [-Wunused-variable]
  576 | static char Adjoin_sp[] = " ~6 ~1";
      |             ^~~~~~~~~
agent/info.c:556:38: warning: ‘Max_lines’ defined but not used [-Wunused-variable]
  556 | static int Screen_cols, Screen_rows, Max_lines;
      |                                      ^~~~~~~~~
agent/info.c:556:25: warning: ‘Screen_rows’ defined but not used [-Wunused-variable]
  556 | static int Screen_cols, Screen_rows, Max_lines;
      |                         ^~~~~~~~~~~
agent/info.c:554:14: warning: ‘Pseudo_screen’ defined but not used [-Wunused-variable]
  554 | static char *Pseudo_screen;
      |              ^~~~~~~~~~~~~
agent/info.c: In function ‘avg_load’:
agent/info.c:1565:33: warning: ‘%d’ directive output may be truncated writing between 1 and 8 bytes into a region of size 7 [-Wformat-truncation=]
 1565 |    snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
      |                                 ^~
agent/info.c:1565:32: note: directive argument in the range [-1048576, 1048575]
 1565 |    snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
      |                                ^~~~~~~~
agent/info.c:1565:32: note: directive argument in the range [0, 99]
agent/info.c:1565:4: note: ‘snprintf’ output between 4 and 12 bytes into a destination of size 7
 1565 |    snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
      |    ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/meminfo.o -MMD -MP -MF .deps/agent/meminfo.d -c agent/meminfo.c -o agent/meminfo.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/namespace.o -MMD -MP -MF .deps/agent/namespace.d -c agent/namespace.c -o agent/namespace.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/numa.o -MMD -MP -MF .deps/agent/numa.d -c agent/numa.c -o agent/numa.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/pids.o -MMD -MP -MF .deps/agent/pids.d -c agent/pids.c -o agent/pids.o
In file included from agent/pids.c:44:
agent/pids.h:271:30: warning: ‘struct pids_info’ declared inside parameter list will not be visible outside of this definition or declaration
  271 | typedef void (*SET_t)(struct pids_info *, struct pids_result *, proc_t *);
      |                              ^~~~~~~~~
agent/pids.c: In function ‘free_pids_str’:
agent/pids.c:135:47: warning: unused parameter ‘R’ [-Wunused-parameter]
  135 | static void freNAME(str) (struct pids_result *R) {
      |                           ~~~~~~~~~~~~~~~~~~~~^
agent/pids.c: In function ‘free_pids_strv’:
agent/pids.c:139:48: warning: unused parameter ‘R’ [-Wunused-parameter]
  139 | static void freNAME(strv) (struct pids_result *R) {
      |                            ~~~~~~~~~~~~~~~~~~~~^
agent/pids.c: In function ‘pids_assign_results’:
agent/pids.c:990:17: warning: passing argument 1 of ‘*that’ from incompatible pointer type [-Wincompatible-pointer-types]
  990 |         (*that)(info, this, p);
      |                 ^~~~
      |                 |
      |                 struct pids_info *
agent/pids.c:990:17: note: expected ‘struct pids_info *’ but argument is of type ‘struct pids_info *’
agent/pids.c: In function ‘pids_cleanup_stacks_all’:
agent/pids.c:1023:13: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1023 |         ext = ext->next;
      |             ^
agent/pids.c: In function ‘pids_itemize_stacks_all’:
agent/pids.c:1082:13: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1082 |         ext = ext->next;
      |             ^
agent/pids.c: In function ‘pids_stacks_alloc’:
agent/pids.c:1315:18: warning: assignment to ‘struct stacks_extent *’ from incompatible pointer type ‘struct stacks_extent2 *’ [-Wincompatible-pointer-types]
 1315 |     p_blob->next = info->extents;                              // push this extent onto... |
      |                  ^
agent/pids.c:1330:12: warning: returning ‘struct stacks_extent2 *’ from a function with incompatible return type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1330 |     return p_blob;
      |            ^~~~~~
agent/pids.c: In function ‘pids_stacks_fetch’:
agent/pids.c:1367:19: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1367 |         if (!(ext = pids_stacks_alloc(info, STACKS_INIT)))
      |                   ^
agent/pids.c:1381:23: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1381 |             || (!(ext = pids_stacks_alloc(info, STACKS_GROW))))
      |                       ^
agent/pids.c: In function ‘pids_stacks_fetch1’:
agent/pids.c:1431:19: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1431 |         if (!(ext = pids_stacks_alloc(info, STACKS_INIT)))
      |                   ^
agent/pids.c:1445:23: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1445 |             || (!(ext = pids_stacks_alloc(info, STACKS_GROW))))
      |                       ^
agent/pids.c: In function ‘procps_pids_unref’:
agent/pids.c:1626:34: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1626 |                 (*info)->extents = (*info)->extents->next;
      |                                  ^
agent/pids.c:1633:25: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1633 |                 nextext = ext->next;
      |                         ^
agent/pids.c: In function ‘procps_pids_get’:
agent/pids.c:1710:29: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1710 |         if (!(info->get_ext = pids_stacks_alloc(info, 1)))
      |                             ^
agent/pids.c: In function ‘procps_pids_reset’:
agent/pids.c:1844:27: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1844 |             info->extents = p->next;
      |                           ^
agent/pids.c: In function ‘procps_pids_sort’:
agent/pids.c:1947:23: warning: variable ‘parms’ set but not used [-Wunused-but-set-variable]
 1947 |     struct sort_parms parms;
      |                       ^~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/pwcache.o -MMD -MP -MF .deps/agent/pwcache.d -c agent/pwcache.c -o agent/pwcache.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/readproc.o -MMD -MP -MF .deps/agent/readproc.d -c agent/readproc.c -o agent/readproc.o
agent/readproc.c: In function ‘stat2proc’:
agent/readproc.c:597:16: warning: unused variable ‘le’ [-Wunused-variable]
  597 |         size_t le = strlen(buf) + 1;
      |                ^~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/send.o -MMD -MP -MF .deps/agent/send.d -c agent/send.c -o agent/send.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/send_data.o -MMD -MP -MF .deps/agent/send_data.d -c agent/send_data.c -o agent/send_data.o
In file included from agent/send_data.c:50:
agent/pids.h:271:30: warning: ‘struct pids_info’ declared inside parameter list will not be visible outside of this definition or declaration
  271 | typedef void (*SET_t)(struct pids_info *, struct pids_result *, proc_t *);
      |                              ^~~~~~~~~
agent/send_data.c: In function ‘sigint_handler’:
agent/send_data.c:263:9: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
  263 |         write_log("ctrl-c caught ....\n");
      |         ^~~~~~~~~
agent/send_data.c:260:25: warning: unused parameter ‘sig’ [-Wunused-parameter]
  260 | void sigint_handler(int sig)
      |                     ~~~~^~~
agent/send_data.c: In function ‘stat2proc’:
agent/send_data.c:365:25: warning: implicit declaration of function ‘escape_str’ [-Wimplicit-function-declaration]
  365 |                         escape_str(buf, raw, sizeof(buf));
      |                         ^~~~~~~~~~
agent/send_data.c:416:9: warning: implicit declaration of function ‘LEAVE’ [-Wimplicit-function-declaration]
  416 |         LEAVE(0x160);
      |         ^~~~~
agent/send_data.c: In function ‘check_self’:
agent/send_data.c:462:24: warning: missing initializer for field ‘siz’ of ‘struct utlbuf_s’ [-Wmissing-field-initializers]
  462 |                 struct utlbuf_s ub = {NULL, 0};
      |                        ^~~~~~~~
In file included from agent/send_data.c:11:
agent/readproc.h:42:11: note: ‘siz’ declared here
   42 |     int   siz;     // current len of the above
      |           ^~~
agent/send_data.c:463:21: warning: variable ‘rc’ set but not used [-Wunused-but-set-variable]
  463 |                 int rc = 0;
      |                     ^~
agent/send_data.c: In function ‘alarm_handler1’:
agent/send_data.c:1191:25: warning: unused parameter ‘sig’ [-Wunused-parameter]
 1191 | void alarm_handler1(int sig)
      |                     ~~~~^~~
agent/send_data.c: At top level:
agent/send_data.c:1574:6: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
 1574 | void write_log(const char *format, ...)
      |      ^~~~~~~~~
agent/send_data.c:263:9: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
  263 |         write_log("ctrl-c caught ....\n");
      |         ^~~~~~~~~
agent/send_data.c: In function ‘main’:
agent/send_data.c:1840:46: warning: passing argument 3 of ‘pthread_create’ from incompatible pointer type [-Wincompatible-pointer-types]
 1840 |                 pthread_create(&sar1, &attr, &sadc1, NULL);
      |                                              ^~~~~~
      |                                              |
      |                                              int (*)()
In file included from agent/top.h:23,
                 from agent/send_data.c:5:
/usr/include/pthread.h:204:36: note: expected ‘void * (*)(void *)’ but argument is of type ‘int (*)()’
  204 |                            void *(*__start_routine) (void *),
      |                            ~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~
agent/send_data.c:1842:50: warning: passing argument 3 of ‘pthread_create’ from incompatible pointer type [-Wincompatible-pointer-types]
 1842 |                 pthread_create(&tcpdump1, &attr, &tcp1, argv[1]);
      |                                                  ^~~~~
      |                                                  |
      |                                                  int (*)(char *)
/usr/include/pthread.h:204:36: note: expected ‘void * (*)(void *)’ but argument is of type ‘int (*)(char *)’
  204 |                            void *(*__start_routine) (void *),
      |                            ~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~
agent/send_data.c:2053:33: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2053 |         for (int isar = 0; isar < os->int_count; isar++) {
      |                                 ^
agent/send_data.c:2064:33: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2064 |         for (int isar = 0; isar < os->enet_count; isar++) {
      |                                 ^
agent/send_data.c:2085:35: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2085 |         for (int idisk = 0; idisk < os->disk_count; idisk++) {
      |                                   ^
agent/send_data.c:2099:31: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2099 |         for (int ifs = 0; ifs < os->file_count; ifs++) {
      |                               ^
agent/send_data.c:2124:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2124 |         for (tcd = 0; tcd < os->group_s; tcd++)
      |                           ^
agent/send_data.c:2141:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2141 |         for (tcd = 0; tcd < os->group_s1; tcd++)
      |                           ^
agent/send_data.c:2159:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2159 |         for (tcd = 0; tcd < os->group_p; tcd++)
      |                           ^
agent/send_data.c:2196:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2196 |         for (tcd = 0; tcd < os->group_f; tcd++)
      |                           ^
agent/send_data.c:2214:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2214 |         for (tcd = 0; tcd < os->group_r; tcd++)
      |                           ^
agent/send_data.c:2232:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2232 |         for (tcd = 0; tcd < os->group_ack; tcd++)
      |                           ^
agent/send_data.c:1756:19: warning: unused variable ‘th’ [-Wunused-variable]
 1756 |         pthread_t th;
      |                   ^~
agent/send_data.c:1723:19: warning: unused variable ‘th1’ [-Wunused-variable]
 1723 |         pthread_t th1;
      |                   ^~~
agent/send_data.c: In function ‘c_tcp1’:
agent/send_data.c:1246:1: warning: control reaches end of non-void function [-Wreturn-type]
 1246 | }
      | ^
agent/send_data.c: In function ‘free_all1’:
agent/send_data.c:1552:1: warning: control reaches end of non-void function [-Wreturn-type]
 1552 | }
      | ^
agent/top.h: At top level:
agent/top.h:104:23: warning: ‘Stat_items’ defined but not used [-Wunused-variable]
  104 | static enum stat_item Stat_items[] = {
      |                       ^~~~~~~~~~
agent/top.h:60:13: warning: ‘UTF8_tab’ defined but not used [-Wunused-variable]
   60 | static char UTF8_tab[] = {
      |             ^~~~~~~~
agent/top.h:56:14: warning: ‘Frame_etscale’ defined but not used [-Wunused-variable]
   56 | static float Frame_etscale;
      |              ^~~~~~~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/stat.o -MMD -MP -MF .deps/agent/stat.d -c agent/stat.c -o agent/stat.o
agent/stat.c: In function ‘procps_stat_reap’:
agent/stat.c:1239:9: warning: unused variable ‘ff’ [-Wunused-variable]
 1239 |     int ff;
      |         ^~
agent/stat.c: In function ‘sum_tics’:
agent/stat.c:1425:10: warning: variable ‘scale’ set but not used [-Wunused-but-set-variable]
 1425 |    float scale;
      |          ^~~~~
agent/stat.c:1424:18: warning: unused variable ‘rx’ [-Wunused-variable]
 1424 |    struct rx_st *rx;
      |                  ^~
agent/stat.c:1423:10: warning: variable ‘idl_frme’ set but not used [-Wunused-but-set-variable]
 1423 |    SIC_t idl_frme, tot_frme;
      |          ^~~~~~~~
agent/stat.c:1433:1: warning: no return statement in function returning non-void [-Wreturn-type]
 1433 | } // end: sum_tics
      | ^
agent/stat.c:1419:41: warning: unused parameter ‘this’ [-Wunused-parameter]
 1419 | static int sum_tics (struct stat_stack *this) {
      |                      ~~~~~~~~~~~~~~~~~~~^~~~
agent/stat.c: At top level:
agent/stat.c:1419:12: warning: ‘sum_tics’ defined but not used [-Wunused-function]
 1419 | static int sum_tics (struct stat_stack *this) {
      |            ^~~~~~~~
agent/stat.c:44:20: warning: ‘Cpu_States_fmts’ defined but not used [-Wunused-variable]
   44 | static const char *Cpu_States_fmts;
      |                    ^~~~~~~~~~~~~~~
agent/stat.c:43:20: warning: ‘Cpu_pmax’ defined but not used [-Wunused-variable]
   43 | static float       Cpu_pmax;
      |                    ^~~~~~~~
agent/stat.c:42:20: warning: ‘Hertz’ defined but not used [-Wunused-variable]
   42 | static long        Hertz;
      |                    ^~~~~
In file included from agent/stat.c:36:
agent/top.h:104:23: warning: ‘Stat_items’ defined but not used [-Wunused-variable]
  104 | static enum stat_item Stat_items[] = {
      |                       ^~~~~~~~~~
agent/top.h:60:13: warning: ‘UTF8_tab’ defined but not used [-Wunused-variable]
   60 | static char UTF8_tab[] = {
      |             ^~~~~~~~
agent/top.h:56:14: warning: ‘Frame_etscale’ defined but not used [-Wunused-variable]
   56 | static float Frame_etscale;
      |              ^~~~~~~~~~~~~
agent/stat.c: In function ‘sum_tics’:
agent/stat.c:1430:7: warning: ‘tot_frme’ is used uninitialized [-Wuninitialized]
 1430 |    if (1 > tot_frme) idl_frme = tot_frme = 1;
      |       ^
agent/stat.c:1423:20: note: ‘tot_frme’ was declared here
 1423 |    SIC_t idl_frme, tot_frme;
      |                    ^~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/sysinfo.o -MMD -MP -MF .deps/agent/sysinfo.d -c agent/sysinfo.c -o agent/sysinfo.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/thpool.o -MMD -MP -MF .deps/agent/thpool.d -c agent/thpool.c -o agent/thpool.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/top.o -MMD -MP -MF .deps/agent/top.d -c agent/top.c -o agent/top.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/top_nls.o -MMD -MP -MF .deps/agent/top_nls.d -c agent/top_nls.c -o agent/top_nls.o
In file included from agent/top_nls.c:24:
agent/top.h: In function ‘utf8_cols’:
agent/top.h:89:23: warning: implicit declaration of function ‘mbtowc’; did you mean ‘mbrtowc’? [-Wimplicit-function-declaration]
   89 |                 (void)mbtowc(&wc, (const char *)p, n);
      |                       ^~~~~~
      |                       mbrtowc
agent/top.h: At top level:
agent/top.h:104:23: warning: ‘Stat_items’ defined but not used [-Wunused-variable]
  104 | static enum stat_item Stat_items[] = {
      |                       ^~~~~~~~~~
agent/top.h:60:13: warning: ‘UTF8_tab’ defined but not used [-Wunused-variable]
   60 | static char UTF8_tab[] = {
      |             ^~~~~~~~
agent/top.h:56:14: warning: ‘Frame_etscale’ defined but not used [-Wunused-variable]
   56 | static float Frame_etscale;
      |              ^~~~~~~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/uptime.o -MMD -MP -MF .deps/agent/uptime.d -c agent/uptime.c -o agent/uptime.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/version.o -MMD -MP -MF .deps/agent/version.d -c agent/version.c -o agent/version.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/wchan.o -MMD -MP -MF .deps/agent/wchan.d -c agent/wchan.c -o agent/wchan.o
make -C ./sar
make[1]: Entering directory '/app/no_perf/sar'
gcc -o act_sadc.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SADC -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  activity.c
gcc -o sa_wrap.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP sa_wrap.c
gcc -o sa_common.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP sa_common.c
gcc -o rd_stats.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SADC -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  rd_stats.c
rd_stats.c: In function ‘read_filesystem’:
rd_stats.c:2361:46: warning: passing argument 2 of ‘statvfs’ from incompatible pointer type [-Wincompatible-pointer-types]
 2361 |                         if ((statvfs(mountp, &buf) < 0) || (!buf.f_blocks))
      |                                              ^~~~
      |                                              |
      |                                              struct statfs *
In file included from rd_stats.c:31:
/usr/include/x86_64-linux-gnu/sys/statvfs.h:52:48: note: expected ‘struct statvfs * restrict’ but argument is of type ‘struct statfs *’
   52 |                     struct statvfs *__restrict __buf)
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~
gcc -o count.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SADC -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  count.c
ar rvs librdstats.a rd_stats.o count.o
r - rd_stats.o
r - count.o
gcc -o rd_sensors.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP rd_sensors.c
ar rv librdsensors.a rd_sensors.o
r - rd_sensors.o
gcc -o sadc.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP sadc.c
In file included from sadc.c:86:
../agent/top.h: In function ‘utf8_cols’:
../agent/top.h:91:26: warning: implicit declaration of function ‘wcwidth’ [-Wimplicit-function-declaration]
   91 |                 if ((n = wcwidth(wc)) < 0)
      |                          ^~~~~~~
gcc -o act_sar.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SAR -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  activity.c
gcc -o public.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP public.c
public.c: In function ‘update_sar_lock1’:
public.c:8:1: warning: control reaches end of non-void function [-Wreturn-type]
    8 | }
      | ^
gcc -o pr_stats.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP pr_stats.c
In file included from pr_stats.c:33:
../agent/top.h: In function ‘utf8_cols’:
../agent/top.h:91:26: warning: implicit declaration of function ‘wcwidth’ [-Wimplicit-function-declaration]
   91 |                 if ((n = wcwidth(wc)) < 0)
      |                          ^~~~~~~
gcc -o network_security.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP network_security.c
network_security.c: In function ‘build_socket_process_map’:
network_security.c:88:55: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size between 236 and 245 [-Wformat-truncation=]
   88 |             snprintf(path, sizeof(path), "/proc/%d/fd/%s", pid, fd_entry->d_name);
      |                                                       ^~
network_security.c:88:13: note: ‘snprintf’ output between 12 and 276 bytes into a destination of size 256
   88 |             snprintf(path, sizeof(path), "/proc/%d/fd/%s", pid, fd_entry->d_name);
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
gcc -o iostat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP iostat.c
iostat.c: In function ‘read_sysfs_dlist_part_stat’:
iostat.c:565:53: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size between 0 and 1023 [-Wformat-truncation=]
  565 |                 snprintf(filename, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                                                     ^~
iostat.c:565:17: note: ‘snprintf’ output between 7 and 1285 bytes into a destination of size 1024
  565 |                 snprintf(filename, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
gcc -o rd_stats_light.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  rd_stats.c
rd_stats.c: In function ‘read_filesystem’:
rd_stats.c:2361:46: warning: passing argument 2 of ‘statvfs’ from incompatible pointer type [-Wincompatible-pointer-types]
 2361 |                         if ((statvfs(mountp, &buf) < 0) || (!buf.f_blocks))
      |                                              ^~~~
      |                                              |
      |                                              struct statfs *
In file included from rd_stats.c:31:
/usr/include/x86_64-linux-gnu/sys/statvfs.h:52:48: note: expected ‘struct statvfs * restrict’ but argument is of type ‘struct statfs *’
   52 |                     struct statvfs *__restrict __buf)
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~
gcc -o count_light.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  count.c
ar rvs librdstats_light.a rd_stats_light.o count_light.o
r - rd_stats_light.o
r - count_light.o
gcc -o common.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP common.c
common.c: In function ‘get_dev_part_nr’:
common.c:214:49: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size between 0 and 1023 [-Wformat-truncation=]
  214 |                 snprintf(line, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                                                 ^~
common.c:214:17: note: ‘snprintf’ output between 7 and 1285 bytes into a destination of size 1024
  214 |                 snprintf(line, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
gcc -o ioconf.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP ioconf.c
ioconf.c: In function ‘ioc_init’:
ioconf.c:301:46: warning: ‘%s’ directive writing up to 31 bytes into a region of size 16 [-Wformat-overflow=]
  301 |                         sprintf(blkp->cfmt, "%s%s%%d", blkp->name, cfmt);
      |                                              ^~
ioconf.c:301:25: note: ‘sprintf’ output between 3 and 49 bytes into a destination of size 16
  301 |                         sprintf(blkp->cfmt, "%s%s%%d", blkp->name, cfmt);
      |                         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ioconf.c:322:42: warning: ‘d’ directive writing 1 byte into a region of size between 0 and 15 [-Wformat-overflow=]
  322 |                 sprintf(blkp->pfmt, "%s%%d", (*pfmt == '*') ? "" : pfmt);
      |                                          ^
ioconf.c:322:17: note: ‘sprintf’ output between 3 and 18 bytes into a destination of size 16
  322 |                 sprintf(blkp->pfmt, "%s%%d", (*pfmt == '*') ? "" : pfmt);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ioconf.c: In function ‘transform_devmapname’:
ioconf.c:497:51: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size 244 [-Wformat-truncation=]
  497 |                 snprintf(filen, MAX_FILE_LEN, "%s/%s", DEVMAP_DIR, dp->d_name);
      |                                                   ^~
ioconf.c:497:17: note: ‘snprintf’ output between 13 and 268 bytes into a destination of size 256
  497 |                 snprintf(filen, MAX_FILE_LEN, "%s/%s", DEVMAP_DIR, dp->d_name);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ar rvs libsyscom.a common.o ioconf.o
r - common.o
r - ioconf.o
gcc -o iostat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context iostat.o librdstats_light.a libsyscom.a 
gcc -o mpstat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP mpstat.c
gcc -o mpstat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context mpstat.o librdstats_light.a libsyscom.a 
gcc -o pidstat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP pidstat.c
gcc -o pidstat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context pidstat.o librdstats_light.a libsyscom.a 
gcc -o nfsiostat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP nfsiostat.c
gcc -o nfsiostat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context nfsiostat.o librdstats_light.a libsyscom.a 
gcc -o cifsiostat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP cifsiostat.c
gcc -o cifsiostat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context cifsiostat.o librdstats_light.a libsyscom.a 
make[1]: Leaving directory '/app/no_perf/sar'
make -C ./libpcap
make[1]: Entering directory '/app/no_perf/libpcap'
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-linux.o pcap-linux.c
pcap-linux.c: In function ‘pcap_wait_for_frames_mmap’:
pcap-linux.c:3664:25: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
 3664 |                         write_log("pcap_wait_for_frames_mmap: POSIX定时器信号触发(循环开始)，1秒时间到\n");
      |                         ^~~~~~~~~
pcap-linux.c:3695:40: warning: implicit declaration of function ‘epoll_create’; did you mean ‘pcap_create’? [-Wimplicit-function-declaration]
 3695 |                         int epoll_fd = epoll_create(1);
      |                                        ^~~~~~~~~~~~
      |                                        pcap_create
pcap-linux.c:3697:52: error: storage size of ‘ev’ isn’t known
 3697 |                                 struct epoll_event ev, events[1];
      |                                                    ^~
pcap-linux.c:3697:56: error: array type has incomplete element type ‘struct epoll_event’
 3697 |                                 struct epoll_event ev, events[1];
      |                                                        ^~~~~~
pcap-linux.c:3700:45: error: ‘EPOLLIN’ undeclared (first use in this function); did you mean ‘POLLIN’?
 3700 |                                 ev.events = EPOLLIN;
      |                                             ^~~~~~~
      |                                             POLLIN
pcap-linux.c:3700:45: note: each undeclared identifier is reported only once for each function it appears in
pcap-linux.c:3702:37: warning: implicit declaration of function ‘epoll_ctl’ [-Wimplicit-function-declaration]
 3702 |                                 if (epoll_ctl(epoll_fd, EPOLL_CTL_ADD, timer_fd, &ev) == 0) {
      |                                     ^~~~~~~~~
pcap-linux.c:3702:57: error: ‘EPOLL_CTL_ADD’ undeclared (first use in this function)
 3702 |                                 if (epoll_ctl(epoll_fd, EPOLL_CTL_ADD, timer_fd, &ev) == 0) {
      |                                                         ^~~~~~~~~~~~~
pcap-linux.c:3704:52: warning: implicit declaration of function ‘epoll_wait’ [-Wimplicit-function-declaration]
 3704 |                                         int nfds = epoll_wait(epoll_fd, events, 1, 0);
      |                                                    ^~~~~~~~~~
pcap-linux.c: In function ‘pcap_read_linux_mmap_v2’:
pcap-linux.c:4460:24: warning: implicit declaration of function ‘get_optimal_timer_fd’ [-Wimplicit-function-declaration]
 4460 |         int timer_fd = get_optimal_timer_fd();  // 获取v2函数的定时器
      |                        ^~~~~~~~~~~~~~~~~~~~
pcap-linux.c: At top level:
pcap-linux.c:6363:13: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
 6363 | extern void write_log(const char *format, ...);
      |             ^~~~~~~~~
pcap-linux.c:3664:25: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
 3664 |                         write_log("pcap_wait_for_frames_mmap: POSIX定时器信号触发(循环开始)，1秒时间到\n");
      |                         ^~~~~~~~~
make[1]: *** [Makefile:84: pcap-linux.o] Error 1
make[1]: Leaving directory '/app/no_perf/libpcap'
make: *** [Makefile:39: send_data] Error 2
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/devname.o -MMD -MP -MF .deps/agent/devname.d -c agent/devname.c -o agent/devname.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/err.o -MMD -MP -MF .deps/agent/err.d -c agent/err.c -o agent/err.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/escape.o -MMD -MP -MF .deps/agent/escape.d -c agent/escape.c -o agent/escape.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/info.o -MMD -MP -MF .deps/agent/info.d -c agent/info.c -o agent/info.o
In file included from agent/info.c:545:
agent/pids.h:271:30: warning: ‘struct pids_info’ declared inside parameter list will not be visible outside of this definition or declaration
  271 | typedef void (*SET_t)(struct pids_info *, struct pids_result *, proc_t *);
      |                              ^~~~~~~~~
agent/info.c: In function ‘zap_fieldstab’:
agent/info.c:1455:10: warning: suggest braces around empty body in an ‘if’ statement [-Wempty-body]
 1455 |          ;
      |          ^
agent/info.c: In function ‘output_load’:
agent/info.c:1508:14: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 2 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |            ~~^
      |              |
      |              long unsigned int
      |            %u
agent/info.c:1508:20: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 3 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                ~~~~^
      |                    |
      |                    long unsigned int
      |                %02u
agent/info.c:1508:24: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 4 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                      ~~^
      |                        |
      |                        long unsigned int
      |                      %u
agent/info.c:1508:30: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 5 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                          ~~~~^
      |                              |
      |                              long unsigned int
      |                          %02u
agent/info.c:1508:34: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 6 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                                ~~^
      |                                  |
      |                                  long unsigned int
      |                                %u
agent/info.c:1508:40: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 7 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                                    ~~~~^
      |                                        |
      |                                        long unsigned int
      |                                    %02u
agent/info.c: In function ‘avg_load’:
agent/info.c:1562:10: warning: unused variable ‘g2’ [-Wunused-variable]
 1562 |    float g2 = round(loads3 / f + e);
      |          ^~
agent/info.c:1561:10: warning: unused variable ‘g1’ [-Wunused-variable]
 1561 |    float g1 = round(loads2 / f + e);
      |          ^~
agent/info.c: In function ‘meminfo’:
agent/info.c:2076:10: warning: unused variable ‘pct_misc’ [-Wunused-variable]
 2076 |    float pct_misc = (float)(kb_main_total - kb_main_available - kb_main_used) * (100.0 / (float)kb_main_total);
      |          ^~~~~~~~
agent/info.c: In function ‘before’:
agent/info.c:2094:8: warning: unused variable ‘linux_version_code’ [-Wunused-variable]
 2094 |    int linux_version_code = procps_linux_version();
      |        ^~~~~~~~~~~~~~~~~~
agent/info.c: In function ‘cpu_help’:
agent/info.c:2218:10: warning: unused variable ‘scale1’ [-Wunused-variable]
 2218 |    float scale1 = 0.0;
      |          ^~~~~~
agent/info.c: In function ‘tasks_refresh’:
agent/info.c:2491:8: warning: unused variable ‘i’ [-Wunused-variable]
 2491 |    int i, what;
      |        ^
agent/info.c:2488:15: warning: unused variable ‘n_alloc’ [-Wunused-variable]
 2488 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c: In function ‘tasks_refresh1’:
agent/info.c:2554:8: warning: unused variable ‘i’ [-Wunused-variable]
 2554 |    int i, what;
      |        ^
agent/info.c:2551:15: warning: unused variable ‘n_alloc’ [-Wunused-variable]
 2551 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c: In function ‘cal_cpu’:
agent/info.c:2765:24: warning: argument to ‘sizeof’ in ‘snprintf’ call is the same expression as the destination; did you mean to provide an explicit length? [-Wsizeof-pointer-memaccess]
 2765 |    snprintf(buf, sizeof(buf), "%#.1f", u);
      |                        ^
agent/info.c: In function ‘cal_mem’:
agent/info.c:2812:24: warning: argument to ‘sizeof’ in ‘snprintf’ call is the same expression as the destination; did you mean to provide an explicit length? [-Wsizeof-pointer-memaccess]
 2812 |    snprintf(buf, sizeof(buf), "%#.1f", m);
      |                        ^
agent/info.c: In function ‘sys’:
agent/info.c:3088:13: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3088 |             return;
      |             ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c:3254:11: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3254 |           return;
      |           ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c:3260:11: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3260 |           return;
      |           ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c:3269:11: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3269 |           return;
      |           ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c: In function ‘get_load’:
agent/info.c:1498:1: warning: control reaches end of non-void function [-Wreturn-type]
 1498 | }
      | ^
agent/info.c: In function ‘output_load’:
agent/info.c:1513:1: warning: control reaches end of non-void function [-Wreturn-type]
 1513 | }
      | ^
agent/info.c: In function ‘get_time1’:
agent/info.c:1533:1: warning: control reaches end of non-void function [-Wreturn-type]
 1533 | }
      | ^
agent/info.c: In function ‘avg_load’:
agent/info.c:1573:1: warning: control reaches end of non-void function [-Wreturn-type]
 1573 | }
      | ^
agent/info.c: In function ‘cpu_help’:
agent/info.c:2260:1: warning: control reaches end of non-void function [-Wreturn-type]
 2260 | }
      | ^
agent/info.c: In function ‘mem_help’:
agent/info.c:2349:1: warning: control reaches end of non-void function [-Wreturn-type]
 2349 | }
      | ^
agent/info.c: In function ‘init_cpu’:
agent/info.c:2620:1: warning: control reaches end of non-void function [-Wreturn-type]
 2620 | }
      | ^
agent/info.c: In function ‘init_parpids’:
agent/info.c:2850:1: warning: control reaches end of non-void function [-Wreturn-type]
 2850 | }
      | ^
agent/info.c: In function ‘init_thrpids’:
agent/info.c:2886:1: warning: control reaches end of non-void function [-Wreturn-type]
 2886 | }
      | ^
agent/info.c: In function ‘add_process_thread_count’:
agent/info.c:2896:1: warning: control reaches end of non-void function [-Wreturn-type]
 2896 | }
      | ^
agent/info.c: In function ‘add_thread_count’:
agent/info.c:2906:1: warning: control reaches end of non-void function [-Wreturn-type]
 2906 | }
      | ^
agent/info.c: At top level:
agent/info.c:2551:15: warning: ‘n_alloc’ defined but not used [-Wunused-variable]
 2551 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c:2488:15: warning: ‘n_alloc’ defined but not used [-Wunused-variable]
 2488 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c:2948:13: warning: ‘wait_for_semaphore’ defined but not used [-Wunused-function]
 2948 | static void wait_for_semaphore(sem_t *sem) {
      |             ^~~~~~~~~~~~~~~~~~
agent/info.c:2664:13: warning: ‘forest_begin’ defined but not used [-Wunused-function]
 2664 | static void forest_begin(WIN_t *q)
      |             ^~~~~~~~~~~~
agent/info.c:2545:14: warning: ‘tasks_refresh1’ defined but not used [-Wunused-function]
 2545 | static void *tasks_refresh1(void *unused)
      |              ^~~~~~~~~~~~~~
agent/info.c:2358:13: warning: ‘wins_stage_1’ defined but not used [-Wunused-function]
 2358 | static void wins_stage_1(void)
      |             ^~~~~~~~~~~~
agent/info.c:1355:13: warning: ‘zap_fieldstab’ defined but not used [-Wunused-function]
 1355 | static void zap_fieldstab(void)
      |             ^~~~~~~~~~~~~
agent/info.c:1348:20: warning: ‘Cursor_state’ defined but not used [-Wunused-variable]
 1348 | static const char *Cursor_state = "";
      |                    ^~~~~~~~~~~~
agent/info.c:1265:13: warning: ‘win_names’ defined but not used [-Wunused-function]
 1265 | static void win_names(WIN_t *q, const char *name)
      |             ^~~~~~~~~
agent/info.c:842:13: warning: ‘Stdout_buf’ defined but not used [-Wunused-variable]
  842 | static char Stdout_buf[2048];
      |             ^~~~~~~~~~
agent/info.c:841:12: warning: ‘Ttychanged’ defined but not used [-Wunused-variable]
  841 | static int Ttychanged = 0;
      |            ^~~~~~~~~~
agent/info.c:840:5: warning: ‘Tty_raw’ defined but not used [-Wunused-variable]
  840 |     Tty_raw; // for unsolicited input
      |     ^~~~~~~
agent/info.c:836:23: warning: ‘Tty_original’ defined but not used [-Wunused-variable]
  836 | static struct termios Tty_original, // our inherited terminal definition
      |                       ^~~~~~~~~~~~
agent/info.c:826:15: warning: ‘Pseudo_size’ defined but not used [-Wunused-variable]
  826 | static size_t Pseudo_size;
      |               ^~~~~~~~~~~
agent/info.c:825:15: warning: ‘Bot_show_func’ defined but not used [-Wunused-variable]
  825 | static void (*Bot_show_func)(void);
      |               ^~~~~~~~~~~~~
agent/info.c:824:14: warning: ‘Bot_focus_func’ defined but not used [-Wunused-variable]
  824 | static BOT_f Bot_focus_func;
      |              ^~~~~~~~~~~~~~
agent/info.c:822:5: warning: ‘Bot_buf’ defined but not used [-Wunused-variable]
  822 |     Bot_buf[BOTBUFSIZ]; // the 'environ' can be huge
      |     ^~~~~~~
agent/info.c:821:6: warning: ‘Bot_head’ defined but not used [-Wunused-variable]
  821 |     *Bot_head,
      |      ^~~~~~~~
agent/info.c:820:13: warning: ‘Bot_sep’ defined but not used [-Wunused-variable]
  820 | static char Bot_sep,
      |             ^~~~~~~
agent/info.c:818:5: warning: ‘Bot_indx’ defined but not used [-Wunused-variable]
  818 |     Bot_indx = BOT_UNFOCUS,
      |     ^~~~~~~~
agent/info.c:817:5: warning: ‘Bot_rsvd’ defined but not used [-Wunused-variable]
  817 |     Bot_rsvd,
      |     ^~~~~~~~
agent/info.c:816:5: warning: ‘Bot_what’ defined but not used [-Wunused-variable]
  816 |     Bot_what,
      |     ^~~~~~~~
agent/info.c:815:12: warning: ‘Bot_task’ defined but not used [-Wunused-variable]
  815 | static int Bot_task,
      |            ^~~~~~~~
agent/info.c:787:12: warning: ‘Cap_can_goto’ defined but not used [-Wunused-variable]
  787 | static int Cap_can_goto = 0;
      |            ^~~~~~~~~~~~
agent/info.c:785:12: warning: ‘Cap_avoid_eol’ defined but not used [-Wunused-variable]
  785 | static int Cap_avoid_eol = 0;
      |            ^~~~~~~~~~~~~
agent/info.c:782:13: warning: ‘Cap_smam’ defined but not used [-Wunused-variable]
  782 |             Cap_smam[CAPBUFSIZ] = "";
      |             ^~~~~~~~
agent/info.c:781:13: warning: ‘Cap_rmam’ defined but not used [-Wunused-variable]
  781 | static char Cap_rmam[CAPBUFSIZ] = "",
      |             ^~~~~~~~
agent/info.c:779:13: warning: ‘Caps_endline’ defined but not used [-Wunused-variable]
  779 |             Caps_endline[SMLBUFSIZ] = "";
      |             ^~~~~~~~~~~~
agent/info.c:778:13: warning: ‘Caps_off’ defined but not used [-Wunused-variable]
  778 |             Caps_off[CAPBUFSIZ] = "",
      |             ^~~~~~~~
agent/info.c:777:13: warning: ‘Cap_reverse’ defined but not used [-Wunused-variable]
  777 |             Cap_reverse[CAPBUFSIZ] = "",
      |             ^~~~~~~~~~~
agent/info.c:775:13: warning: ‘Cap_home’ defined but not used [-Wunused-variable]
  775 |             Cap_home[CAPBUFSIZ] = "",
      |             ^~~~~~~~
agent/info.c:774:5: warning: ‘Cap_clr_eos’ defined but not used [-Wunused-variable]
  774 |     Cap_clr_eos[CAPBUFSIZ] = "",
      |     ^~~~~~~~~~~
agent/info.c:773:5: warning: ‘Cap_curs_hide’ defined but not used [-Wunused-variable]
  773 |     Cap_curs_hide[CAPBUFSIZ] = "",       // batch requirements!
      |     ^~~~~~~~~~~~~
agent/info.c:772:5: warning: ‘Cap_curs_huge’ defined but not used [-Wunused-variable]
  772 |     Cap_curs_huge[CAPBUFSIZ] = "",       // to remind people of those
      |     ^~~~~~~~~~~~~
agent/info.c:771:5: warning: ‘Cap_curs_norm’ defined but not used [-Wunused-variable]
  771 |     Cap_curs_norm[CAPBUFSIZ] = "",       // cost nothing but DO serve
      |     ^~~~~~~~~~~~~
agent/info.c:770:5: warning: ‘Cap_clr_scr’ defined but not used [-Wunused-variable]
  770 |     Cap_clr_scr[CAPBUFSIZ] = "",         // the assignments used here
      |     ^~~~~~~~~~~
agent/info.c:769:5: warning: ‘Cap_nl_clreos’ defined but not used [-Wunused-variable]
  769 |     Cap_nl_clreos[CAPBUFSIZ] = "",       // are initialized to zeros!
      |     ^~~~~~~~~~~~~
agent/info.c:768:13: warning: ‘Cap_clr_eol’ defined but not used [-Wunused-variable]
  768 | static char Cap_clr_eol[CAPBUFSIZ] = "", // global and/or static vars
      |             ^~~~~~~~~~~
agent/info.c:757:14: warning: ‘Myname’ defined but not used [-Wunused-variable]
  757 | static char *Myname;
      |              ^~~~~~
agent/info.c:604:17: warning: ‘Sigwinch_set’ defined but not used [-Wunused-variable]
  604 | static sigset_t Sigwinch_set;
      |                 ^~~~~~~~~~~~
agent/info.c:595:26: warning: ‘Pids_ctx1’ defined but not used [-Wunused-variable]
  595 | static struct pids_info *Pids_ctx1;
      |                          ^~~~~~~~~
agent/info.c:586:5: warning: ‘Width_mode’ defined but not used [-Wunused-variable]
  586 |     Width_mode = 0,   // set w/ 'w' - potential output override
      |     ^~~~~~~~~~
agent/info.c:585:5: warning: ‘Secure_mode’ defined but not used [-Wunused-variable]
  585 |     Secure_mode = 0,  // set if some functionality restricted
      |     ^~~~~~~~~~~
agent/info.c:584:5: warning: ‘Loops’ defined but not used [-Wunused-variable]
  584 |     Loops = -1,       // number of iterations, -1 loops forever
      |     ^~~~~
agent/info.c:583:12: warning: ‘Batch’ defined but not used [-Wunused-variable]
  583 | static int Batch = 0, // batch mode, collect no input, dumb output
      |            ^~~~~
agent/info.c:576:13: warning: ‘Adjoin_sp’ defined but not used [-Wunused-variable]
  576 | static char Adjoin_sp[] = " ~6 ~1";
      |             ^~~~~~~~~
agent/info.c:556:38: warning: ‘Max_lines’ defined but not used [-Wunused-variable]
  556 | static int Screen_cols, Screen_rows, Max_lines;
      |                                      ^~~~~~~~~
agent/info.c:556:25: warning: ‘Screen_rows’ defined but not used [-Wunused-variable]
  556 | static int Screen_cols, Screen_rows, Max_lines;
      |                         ^~~~~~~~~~~
agent/info.c:554:14: warning: ‘Pseudo_screen’ defined but not used [-Wunused-variable]
  554 | static char *Pseudo_screen;
      |              ^~~~~~~~~~~~~
agent/info.c: In function ‘avg_load’:
agent/info.c:1565:33: warning: ‘%d’ directive output may be truncated writing between 1 and 8 bytes into a region of size 7 [-Wformat-truncation=]
 1565 |    snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
      |                                 ^~
agent/info.c:1565:32: note: directive argument in the range [-1048576, 1048575]
 1565 |    snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
      |                                ^~~~~~~~
agent/info.c:1565:32: note: directive argument in the range [0, 99]
agent/info.c:1565:4: note: ‘snprintf’ output between 4 and 12 bytes into a destination of size 7
 1565 |    snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
      |    ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/meminfo.o -MMD -MP -MF .deps/agent/meminfo.d -c agent/meminfo.c -o agent/meminfo.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/namespace.o -MMD -MP -MF .deps/agent/namespace.d -c agent/namespace.c -o agent/namespace.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/numa.o -MMD -MP -MF .deps/agent/numa.d -c agent/numa.c -o agent/numa.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/pids.o -MMD -MP -MF .deps/agent/pids.d -c agent/pids.c -o agent/pids.o
In file included from agent/pids.c:44:
agent/pids.h:271:30: warning: ‘struct pids_info’ declared inside parameter list will not be visible outside of this definition or declaration
  271 | typedef void (*SET_t)(struct pids_info *, struct pids_result *, proc_t *);
      |                              ^~~~~~~~~
agent/pids.c: In function ‘free_pids_str’:
agent/pids.c:135:47: warning: unused parameter ‘R’ [-Wunused-parameter]
  135 | static void freNAME(str) (struct pids_result *R) {
      |                           ~~~~~~~~~~~~~~~~~~~~^
agent/pids.c: In function ‘free_pids_strv’:
agent/pids.c:139:48: warning: unused parameter ‘R’ [-Wunused-parameter]
  139 | static void freNAME(strv) (struct pids_result *R) {
      |                            ~~~~~~~~~~~~~~~~~~~~^
agent/pids.c: In function ‘pids_assign_results’:
agent/pids.c:990:17: warning: passing argument 1 of ‘*that’ from incompatible pointer type [-Wincompatible-pointer-types]
  990 |         (*that)(info, this, p);
      |                 ^~~~
      |                 |
      |                 struct pids_info *
agent/pids.c:990:17: note: expected ‘struct pids_info *’ but argument is of type ‘struct pids_info *’
agent/pids.c: In function ‘pids_cleanup_stacks_all’:
agent/pids.c:1023:13: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1023 |         ext = ext->next;
      |             ^
agent/pids.c: In function ‘pids_itemize_stacks_all’:
agent/pids.c:1082:13: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1082 |         ext = ext->next;
      |             ^
agent/pids.c: In function ‘pids_stacks_alloc’:
agent/pids.c:1315:18: warning: assignment to ‘struct stacks_extent *’ from incompatible pointer type ‘struct stacks_extent2 *’ [-Wincompatible-pointer-types]
 1315 |     p_blob->next = info->extents;                              // push this extent onto... |
      |                  ^
agent/pids.c:1330:12: warning: returning ‘struct stacks_extent2 *’ from a function with incompatible return type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1330 |     return p_blob;
      |            ^~~~~~
agent/pids.c: In function ‘pids_stacks_fetch’:
agent/pids.c:1367:19: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1367 |         if (!(ext = pids_stacks_alloc(info, STACKS_INIT)))
      |                   ^
agent/pids.c:1381:23: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1381 |             || (!(ext = pids_stacks_alloc(info, STACKS_GROW))))
      |                       ^
agent/pids.c: In function ‘pids_stacks_fetch1’:
agent/pids.c:1431:19: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1431 |         if (!(ext = pids_stacks_alloc(info, STACKS_INIT)))
      |                   ^
agent/pids.c:1445:23: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1445 |             || (!(ext = pids_stacks_alloc(info, STACKS_GROW))))
      |                       ^
agent/pids.c: In function ‘procps_pids_unref’:
agent/pids.c:1626:34: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1626 |                 (*info)->extents = (*info)->extents->next;
      |                                  ^
agent/pids.c:1633:25: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1633 |                 nextext = ext->next;
      |                         ^
agent/pids.c: In function ‘procps_pids_get’:
agent/pids.c:1710:29: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1710 |         if (!(info->get_ext = pids_stacks_alloc(info, 1)))
      |                             ^
agent/pids.c: In function ‘procps_pids_reset’:
agent/pids.c:1844:27: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1844 |             info->extents = p->next;
      |                           ^
agent/pids.c: In function ‘procps_pids_sort’:
agent/pids.c:1947:23: warning: variable ‘parms’ set but not used [-Wunused-but-set-variable]
 1947 |     struct sort_parms parms;
      |                       ^~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/pwcache.o -MMD -MP -MF .deps/agent/pwcache.d -c agent/pwcache.c -o agent/pwcache.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/readproc.o -MMD -MP -MF .deps/agent/readproc.d -c agent/readproc.c -o agent/readproc.o
agent/readproc.c: In function ‘stat2proc’:
agent/readproc.c:597:16: warning: unused variable ‘le’ [-Wunused-variable]
  597 |         size_t le = strlen(buf) + 1;
      |                ^~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/send.o -MMD -MP -MF .deps/agent/send.d -c agent/send.c -o agent/send.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/send_data.o -MMD -MP -MF .deps/agent/send_data.d -c agent/send_data.c -o agent/send_data.o
In file included from agent/send_data.c:50:
agent/pids.h:271:30: warning: ‘struct pids_info’ declared inside parameter list will not be visible outside of this definition or declaration
  271 | typedef void (*SET_t)(struct pids_info *, struct pids_result *, proc_t *);
      |                              ^~~~~~~~~
agent/send_data.c: In function ‘sigint_handler’:
agent/send_data.c:263:9: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
  263 |         write_log("ctrl-c caught ....\n");
      |         ^~~~~~~~~
agent/send_data.c:260:25: warning: unused parameter ‘sig’ [-Wunused-parameter]
  260 | void sigint_handler(int sig)
      |                     ~~~~^~~
agent/send_data.c: In function ‘stat2proc’:
agent/send_data.c:365:25: warning: implicit declaration of function ‘escape_str’ [-Wimplicit-function-declaration]
  365 |                         escape_str(buf, raw, sizeof(buf));
      |                         ^~~~~~~~~~
agent/send_data.c:416:9: warning: implicit declaration of function ‘LEAVE’ [-Wimplicit-function-declaration]
  416 |         LEAVE(0x160);
      |         ^~~~~
agent/send_data.c: In function ‘check_self’:
agent/send_data.c:462:24: warning: missing initializer for field ‘siz’ of ‘struct utlbuf_s’ [-Wmissing-field-initializers]
  462 |                 struct utlbuf_s ub = {NULL, 0};
      |                        ^~~~~~~~
In file included from agent/send_data.c:11:
agent/readproc.h:42:11: note: ‘siz’ declared here
   42 |     int   siz;     // current len of the above
      |           ^~~
agent/send_data.c:463:21: warning: variable ‘rc’ set but not used [-Wunused-but-set-variable]
  463 |                 int rc = 0;
      |                     ^~
agent/send_data.c: In function ‘alarm_handler1’:
agent/send_data.c:1191:25: warning: unused parameter ‘sig’ [-Wunused-parameter]
 1191 | void alarm_handler1(int sig)
      |                     ~~~~^~~
agent/send_data.c: At top level:
agent/send_data.c:1574:6: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
 1574 | void write_log(const char *format, ...)
      |      ^~~~~~~~~
agent/send_data.c:263:9: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
  263 |         write_log("ctrl-c caught ....\n");
      |         ^~~~~~~~~
agent/send_data.c: In function ‘main’:
agent/send_data.c:1840:46: warning: passing argument 3 of ‘pthread_create’ from incompatible pointer type [-Wincompatible-pointer-types]
 1840 |                 pthread_create(&sar1, &attr, &sadc1, NULL);
      |                                              ^~~~~~
      |                                              |
      |                                              int (*)()
In file included from agent/top.h:23,
                 from agent/send_data.c:5:
/usr/include/pthread.h:204:36: note: expected ‘void * (*)(void *)’ but argument is of type ‘int (*)()’
  204 |                            void *(*__start_routine) (void *),
      |                            ~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~
agent/send_data.c:1842:50: warning: passing argument 3 of ‘pthread_create’ from incompatible pointer type [-Wincompatible-pointer-types]
 1842 |                 pthread_create(&tcpdump1, &attr, &tcp1, argv[1]);
      |                                                  ^~~~~
      |                                                  |
      |                                                  int (*)(char *)
/usr/include/pthread.h:204:36: note: expected ‘void * (*)(void *)’ but argument is of type ‘int (*)(char *)’
  204 |                            void *(*__start_routine) (void *),
      |                            ~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~
agent/send_data.c:2053:33: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2053 |         for (int isar = 0; isar < os->int_count; isar++) {
      |                                 ^
agent/send_data.c:2064:33: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2064 |         for (int isar = 0; isar < os->enet_count; isar++) {
      |                                 ^
agent/send_data.c:2085:35: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2085 |         for (int idisk = 0; idisk < os->disk_count; idisk++) {
      |                                   ^
agent/send_data.c:2099:31: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2099 |         for (int ifs = 0; ifs < os->file_count; ifs++) {
      |                               ^
agent/send_data.c:2124:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2124 |         for (tcd = 0; tcd < os->group_s; tcd++)
      |                           ^
agent/send_data.c:2141:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2141 |         for (tcd = 0; tcd < os->group_s1; tcd++)
      |                           ^
agent/send_data.c:2159:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2159 |         for (tcd = 0; tcd < os->group_p; tcd++)
      |                           ^
agent/send_data.c:2196:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2196 |         for (tcd = 0; tcd < os->group_f; tcd++)
      |                           ^
agent/send_data.c:2214:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2214 |         for (tcd = 0; tcd < os->group_r; tcd++)
      |                           ^
agent/send_data.c:2232:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2232 |         for (tcd = 0; tcd < os->group_ack; tcd++)
      |                           ^
agent/send_data.c:1756:19: warning: unused variable ‘th’ [-Wunused-variable]
 1756 |         pthread_t th;
      |                   ^~
agent/send_data.c:1723:19: warning: unused variable ‘th1’ [-Wunused-variable]
 1723 |         pthread_t th1;
      |                   ^~~
agent/send_data.c: In function ‘c_tcp1’:
agent/send_data.c:1246:1: warning: control reaches end of non-void function [-Wreturn-type]
 1246 | }
      | ^
agent/send_data.c: In function ‘free_all1’:
agent/send_data.c:1552:1: warning: control reaches end of non-void function [-Wreturn-type]
 1552 | }
      | ^
agent/top.h: At top level:
agent/top.h:104:23: warning: ‘Stat_items’ defined but not used [-Wunused-variable]
  104 | static enum stat_item Stat_items[] = {
      |                       ^~~~~~~~~~
agent/top.h:60:13: warning: ‘UTF8_tab’ defined but not used [-Wunused-variable]
   60 | static char UTF8_tab[] = {
      |             ^~~~~~~~
agent/top.h:56:14: warning: ‘Frame_etscale’ defined but not used [-Wunused-variable]
   56 | static float Frame_etscale;
      |              ^~~~~~~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/stat.o -MMD -MP -MF .deps/agent/stat.d -c agent/stat.c -o agent/stat.o
agent/stat.c: In function ‘procps_stat_reap’:
agent/stat.c:1239:9: warning: unused variable ‘ff’ [-Wunused-variable]
 1239 |     int ff;
      |         ^~
agent/stat.c: In function ‘sum_tics’:
agent/stat.c:1425:10: warning: variable ‘scale’ set but not used [-Wunused-but-set-variable]
 1425 |    float scale;
      |          ^~~~~
agent/stat.c:1424:18: warning: unused variable ‘rx’ [-Wunused-variable]
 1424 |    struct rx_st *rx;
      |                  ^~
agent/stat.c:1423:10: warning: variable ‘idl_frme’ set but not used [-Wunused-but-set-variable]
 1423 |    SIC_t idl_frme, tot_frme;
      |          ^~~~~~~~
agent/stat.c:1433:1: warning: no return statement in function returning non-void [-Wreturn-type]
 1433 | } // end: sum_tics
      | ^
agent/stat.c:1419:41: warning: unused parameter ‘this’ [-Wunused-parameter]
 1419 | static int sum_tics (struct stat_stack *this) {
      |                      ~~~~~~~~~~~~~~~~~~~^~~~
agent/stat.c: At top level:
agent/stat.c:1419:12: warning: ‘sum_tics’ defined but not used [-Wunused-function]
 1419 | static int sum_tics (struct stat_stack *this) {
      |            ^~~~~~~~
agent/stat.c:44:20: warning: ‘Cpu_States_fmts’ defined but not used [-Wunused-variable]
   44 | static const char *Cpu_States_fmts;
      |                    ^~~~~~~~~~~~~~~
agent/stat.c:43:20: warning: ‘Cpu_pmax’ defined but not used [-Wunused-variable]
   43 | static float       Cpu_pmax;
      |                    ^~~~~~~~
agent/stat.c:42:20: warning: ‘Hertz’ defined but not used [-Wunused-variable]
   42 | static long        Hertz;
      |                    ^~~~~
In file included from agent/stat.c:36:
agent/top.h:104:23: warning: ‘Stat_items’ defined but not used [-Wunused-variable]
  104 | static enum stat_item Stat_items[] = {
      |                       ^~~~~~~~~~
agent/top.h:60:13: warning: ‘UTF8_tab’ defined but not used [-Wunused-variable]
   60 | static char UTF8_tab[] = {
      |             ^~~~~~~~
agent/top.h:56:14: warning: ‘Frame_etscale’ defined but not used [-Wunused-variable]
   56 | static float Frame_etscale;
      |              ^~~~~~~~~~~~~
agent/stat.c: In function ‘sum_tics’:
agent/stat.c:1430:7: warning: ‘tot_frme’ is used uninitialized [-Wuninitialized]
 1430 |    if (1 > tot_frme) idl_frme = tot_frme = 1;
      |       ^
agent/stat.c:1423:20: note: ‘tot_frme’ was declared here
 1423 |    SIC_t idl_frme, tot_frme;
      |                    ^~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/sysinfo.o -MMD -MP -MF .deps/agent/sysinfo.d -c agent/sysinfo.c -o agent/sysinfo.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/thpool.o -MMD -MP -MF .deps/agent/thpool.d -c agent/thpool.c -o agent/thpool.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/top.o -MMD -MP -MF .deps/agent/top.d -c agent/top.c -o agent/top.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/top_nls.o -MMD -MP -MF .deps/agent/top_nls.d -c agent/top_nls.c -o agent/top_nls.o
In file included from agent/top_nls.c:24:
agent/top.h: In function ‘utf8_cols’:
agent/top.h:89:23: warning: implicit declaration of function ‘mbtowc’; did you mean ‘mbrtowc’? [-Wimplicit-function-declaration]
   89 |                 (void)mbtowc(&wc, (const char *)p, n);
      |                       ^~~~~~
      |                       mbrtowc
agent/top.h: At top level:
agent/top.h:104:23: warning: ‘Stat_items’ defined but not used [-Wunused-variable]
  104 | static enum stat_item Stat_items[] = {
      |                       ^~~~~~~~~~
agent/top.h:60:13: warning: ‘UTF8_tab’ defined but not used [-Wunused-variable]
   60 | static char UTF8_tab[] = {
      |             ^~~~~~~~
agent/top.h:56:14: warning: ‘Frame_etscale’ defined but not used [-Wunused-variable]
   56 | static float Frame_etscale;
      |              ^~~~~~~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/uptime.o -MMD -MP -MF .deps/agent/uptime.d -c agent/uptime.c -o agent/uptime.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/version.o -MMD -MP -MF .deps/agent/version.d -c agent/version.c -o agent/version.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/wchan.o -MMD -MP -MF .deps/agent/wchan.d -c agent/wchan.c -o agent/wchan.o
make -C ./sar
make[1]: Entering directory '/app/no_perf/sar'
gcc -o act_sadc.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SADC -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  activity.c
gcc -o sa_wrap.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP sa_wrap.c
gcc -o sa_common.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP sa_common.c
gcc -o rd_stats.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SADC -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  rd_stats.c
rd_stats.c: In function ‘read_filesystem’:
rd_stats.c:2361:46: warning: passing argument 2 of ‘statvfs’ from incompatible pointer type [-Wincompatible-pointer-types]
 2361 |                         if ((statvfs(mountp, &buf) < 0) || (!buf.f_blocks))
      |                                              ^~~~
      |                                              |
      |                                              struct statfs *
In file included from rd_stats.c:31:
/usr/include/x86_64-linux-gnu/sys/statvfs.h:52:48: note: expected ‘struct statvfs * restrict’ but argument is of type ‘struct statfs *’
   52 |                     struct statvfs *__restrict __buf)
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~
gcc -o count.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SADC -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  count.c
ar rvs librdstats.a rd_stats.o count.o
r - rd_stats.o
r - count.o
gcc -o rd_sensors.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP rd_sensors.c
ar rv librdsensors.a rd_sensors.o
r - rd_sensors.o
gcc -o sadc.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP sadc.c
In file included from sadc.c:86:
../agent/top.h: In function ‘utf8_cols’:
../agent/top.h:91:26: warning: implicit declaration of function ‘wcwidth’ [-Wimplicit-function-declaration]
   91 |                 if ((n = wcwidth(wc)) < 0)
      |                          ^~~~~~~
gcc -o act_sar.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SAR -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  activity.c
gcc -o public.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP public.c
public.c: In function ‘update_sar_lock1’:
public.c:8:1: warning: control reaches end of non-void function [-Wreturn-type]
    8 | }
      | ^
gcc -o pr_stats.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP pr_stats.c
In file included from pr_stats.c:33:
../agent/top.h: In function ‘utf8_cols’:
../agent/top.h:91:26: warning: implicit declaration of function ‘wcwidth’ [-Wimplicit-function-declaration]
   91 |                 if ((n = wcwidth(wc)) < 0)
      |                          ^~~~~~~
gcc -o network_security.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP network_security.c
network_security.c: In function ‘build_socket_process_map’:
network_security.c:88:55: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size between 236 and 245 [-Wformat-truncation=]
   88 |             snprintf(path, sizeof(path), "/proc/%d/fd/%s", pid, fd_entry->d_name);
      |                                                       ^~
network_security.c:88:13: note: ‘snprintf’ output between 12 and 276 bytes into a destination of size 256
   88 |             snprintf(path, sizeof(path), "/proc/%d/fd/%s", pid, fd_entry->d_name);
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
gcc -o iostat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP iostat.c
iostat.c: In function ‘read_sysfs_dlist_part_stat’:
iostat.c:565:53: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size between 0 and 1023 [-Wformat-truncation=]
  565 |                 snprintf(filename, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                                                     ^~
iostat.c:565:17: note: ‘snprintf’ output between 7 and 1285 bytes into a destination of size 1024
  565 |                 snprintf(filename, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
gcc -o rd_stats_light.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  rd_stats.c
rd_stats.c: In function ‘read_filesystem’:
rd_stats.c:2361:46: warning: passing argument 2 of ‘statvfs’ from incompatible pointer type [-Wincompatible-pointer-types]
 2361 |                         if ((statvfs(mountp, &buf) < 0) || (!buf.f_blocks))
      |                                              ^~~~
      |                                              |
      |                                              struct statfs *
In file included from rd_stats.c:31:
/usr/include/x86_64-linux-gnu/sys/statvfs.h:52:48: note: expected ‘struct statvfs * restrict’ but argument is of type ‘struct statfs *’
   52 |                     struct statvfs *__restrict __buf)
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~
gcc -o count_light.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  count.c
ar rvs librdstats_light.a rd_stats_light.o count_light.o
r - rd_stats_light.o
r - count_light.o
gcc -o common.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP common.c
common.c: In function ‘get_dev_part_nr’:
common.c:214:49: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size between 0 and 1023 [-Wformat-truncation=]
  214 |                 snprintf(line, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                                                 ^~
common.c:214:17: note: ‘snprintf’ output between 7 and 1285 bytes into a destination of size 1024
  214 |                 snprintf(line, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
gcc -o ioconf.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP ioconf.c
ioconf.c: In function ‘ioc_init’:
ioconf.c:301:46: warning: ‘%s’ directive writing up to 31 bytes into a region of size 16 [-Wformat-overflow=]
  301 |                         sprintf(blkp->cfmt, "%s%s%%d", blkp->name, cfmt);
      |                                              ^~
ioconf.c:301:25: note: ‘sprintf’ output between 3 and 49 bytes into a destination of size 16
  301 |                         sprintf(blkp->cfmt, "%s%s%%d", blkp->name, cfmt);
      |                         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ioconf.c:322:42: warning: ‘d’ directive writing 1 byte into a region of size between 0 and 15 [-Wformat-overflow=]
  322 |                 sprintf(blkp->pfmt, "%s%%d", (*pfmt == '*') ? "" : pfmt);
      |                                          ^
ioconf.c:322:17: note: ‘sprintf’ output between 3 and 18 bytes into a destination of size 16
  322 |                 sprintf(blkp->pfmt, "%s%%d", (*pfmt == '*') ? "" : pfmt);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ioconf.c: In function ‘transform_devmapname’:
ioconf.c:497:51: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size 244 [-Wformat-truncation=]
  497 |                 snprintf(filen, MAX_FILE_LEN, "%s/%s", DEVMAP_DIR, dp->d_name);
      |                                                   ^~
ioconf.c:497:17: note: ‘snprintf’ output between 13 and 268 bytes into a destination of size 256
  497 |                 snprintf(filen, MAX_FILE_LEN, "%s/%s", DEVMAP_DIR, dp->d_name);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ar rvs libsyscom.a common.o ioconf.o
r - common.o
r - ioconf.o
gcc -o iostat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context iostat.o librdstats_light.a libsyscom.a 
gcc -o mpstat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP mpstat.c
gcc -o mpstat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context mpstat.o librdstats_light.a libsyscom.a 
gcc -o pidstat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP pidstat.c
gcc -o pidstat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context pidstat.o librdstats_light.a libsyscom.a 
gcc -o nfsiostat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP nfsiostat.c
gcc -o nfsiostat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context nfsiostat.o librdstats_light.a libsyscom.a 
gcc -o cifsiostat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP cifsiostat.c
gcc -o cifsiostat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context cifsiostat.o librdstats_light.a libsyscom.a 
make[1]: Leaving directory '/app/no_perf/sar'
make -C ./libpcap
make[1]: Entering directory '/app/no_perf/libpcap'
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-linux.o pcap-linux.c
pcap-linux.c: In function ‘pcap_wait_for_frames_mmap’:
pcap-linux.c:3666:25: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
 3666 |                         write_log("pcap_wait_for_frames_mmap: POSIX定时器信号触发(循环开始)，1秒时间到\n");
      |                         ^~~~~~~~~
pcap-linux.c:3696:48: error: ‘v’ undeclared (first use in this function)
 3696 |                 if (current_timer_type == 1 && v >= 0) {
      |                                                ^
pcap-linux.c:3696:48: note: each undeclared identifier is reported only once for each function it appears in
pcap-linux.c: In function ‘pcap_read_linux_mmap_v2’:
pcap-linux.c:4470:24: warning: implicit declaration of function ‘get_optimal_timer_fd’ [-Wimplicit-function-declaration]
 4470 |         int timer_fd = get_optimal_timer_fd();  // 获取v2函数的定时器
      |                        ^~~~~~~~~~~~~~~~~~~~
pcap-linux.c: At top level:
pcap-linux.c:6373:13: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
 6373 | extern void write_log(const char *format, ...);
      |             ^~~~~~~~~
pcap-linux.c:3666:25: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
 3666 |                         write_log("pcap_wait_for_frames_mmap: POSIX定时器信号触发(循环开始)，1秒时间到\n");
      |                         ^~~~~~~~~
make[1]: *** [Makefile:84: pcap-linux.o] Error 1
make[1]: Leaving directory '/app/no_perf/libpcap'
make: *** [Makefile:39: send_data] Error 2
make -C ./sar
make[1]: Entering directory '/app/no_perf/sar'
ar rv librdsensors.a rd_sensors.o
r - rd_sensors.o
make[1]: Leaving directory '/app/no_perf/sar'
make -C ./libpcap
make[1]: Entering directory '/app/no_perf/libpcap'
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-linux.o pcap-linux.c
pcap-linux.c: In function ‘pcap_wait_for_frames_mmap’:
pcap-linux.c:3666:25: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
 3666 |                         write_log("pcap_wait_for_frames_mmap: POSIX定时器信号触发(循环开始)，1秒时间到\n");
      |                         ^~~~~~~~~
pcap-linux.c: In function ‘pcap_read_linux_mmap_v2’:
pcap-linux.c:4470:24: warning: implicit declaration of function ‘get_optimal_timer_fd’ [-Wimplicit-function-declaration]
 4470 |         int timer_fd = get_optimal_timer_fd();  // 获取v2函数的定时器
      |                        ^~~~~~~~~~~~~~~~~~~~
pcap-linux.c: At top level:
pcap-linux.c:6373:13: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
 6373 | extern void write_log(const char *format, ...);
      |             ^~~~~~~~~
pcap-linux.c:3666:25: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
 3666 |                         write_log("pcap_wait_for_frames_mmap: POSIX定时器信号触发(循环开始)，1秒时间到\n");
      |                         ^~~~~~~~~
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o fad-getad.o fad-getad.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-usb-linux.o pcap-usb-linux.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-netfilter-linux.o pcap-netfilter-linux.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap.o pcap.c
pcap.c: In function ‘posix_timer_handler’:
pcap.c:242:9: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
  242 |         write_log("POSIX定时器信号触发，设置pcap_timeout1=1\n");
      |         ^~~~~~~~~
pcap.c: At top level:
pcap.c:259:13: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
  259 | extern void write_log(const char *format, ...);
      |             ^~~~~~~~~
pcap.c:242:9: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
  242 |         write_log("POSIX定时器信号触发，设置pcap_timeout1=1\n");
      |         ^~~~~~~~~
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c ./gencode.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o optimize.o optimize.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o nametoaddr.o nametoaddr.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o etherent.o etherent.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o fmtutils.o fmtutils.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-util.o pcap-util.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o savefile.o savefile.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o sf-pcap.o sf-pcap.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o sf-pcapng.o sf-pcapng.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-common.o pcap-common.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o bpf_image.o bpf_image.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o bpf_filter.o bpf_filter.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o bpf_dump.o bpf_dump.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c scanner.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c grammar.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -o strlcat.o -c ./missing/strlcat.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -o strlcpy.o -c ./missing/strlcpy.c
ar rc libpcap.a pcap-linux.o fad-getad.o pcap-usb-linux.o pcap-netfilter-linux.o pcap.o gencode.o optimize.o nametoaddr.o etherent.o fmtutils.o pcap-util.o savefile.o sf-pcap.o sf-pcapng.o pcap-common.o bpf_image.o bpf_filter.o bpf_dump.o scanner.o grammar.o strlcat.o strlcpy.o 
ranlib libpcap.a
VER=`cat ./VERSION`; \
MAJOR_VER=`sed 's/\([0-9][0-9]*\)\..*/\1/' ./VERSION`; \
gcc  -shared -Wl,-soname,libpcap.so.$MAJOR_VER \
    -o libpcap.so.$VER pcap-linux.o fad-getad.o pcap-usb-linux.o pcap-netfilter-linux.o pcap.o gencode.o optimize.o nametoaddr.o etherent.o fmtutils.o pcap-util.o savefile.o sf-pcap.o sf-pcapng.o pcap-common.o bpf_image.o bpf_filter.o bpf_dump.o scanner.o grammar.o strlcat.o strlcpy.o  
make[1]: Leaving directory '/app/no_perf/libpcap'
make -C ./tcpdump-5.0.0 tcpdump.o fptype.o libnetdissect.a
make[1]: Entering directory '/app/no_perf/tcpdump-5.0.0'
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o tcpdump.o tcpdump.c
tcpdump.c: In function ‘init_barriers’:
tcpdump.c:384:17: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
  384 |                 write_log("init_barriers: barriers已经初始化\n");
      |                 ^~~~~~~~~
tcpdump.c:391:13: warning: implicit declaration of function ‘efficient_barrier_init’; did you mean ‘pthread_barrier_init’? [-Wimplicit-function-declaration]
  391 |         if (efficient_barrier_init(&barrier_a_done, 3) != 0) {
      |             ^~~~~~~~~~~~~~~~~~~~~~
      |             pthread_barrier_init
tcpdump.c:399:17: warning: implicit declaration of function ‘efficient_barrier_destroy’; did you mean ‘pthread_barrier_destroy’? [-Wimplicit-function-declaration]
  399 |                 efficient_barrier_destroy(&barrier_a_done);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~
      |                 pthread_barrier_destroy
tcpdump.c: At top level:
tcpdump.c:3655:6: warning: conflicting types for ‘efficient_barrier_destroy’; have ‘void(efficient_barrier_t *)’
 3655 | void efficient_barrier_destroy(efficient_barrier_t *barrier)
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~
tcpdump.c:399:17: note: previous implicit declaration of ‘efficient_barrier_destroy’ with type ‘void(efficient_barrier_t *)’
  399 |                 efficient_barrier_destroy(&barrier_a_done);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o fptype.o fptype.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o addrtoname.o addrtoname.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o addrtostr.o addrtostr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o af.o af.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o ascii_strcasecmp.o ascii_strcasecmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o checksum.o checksum.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o cpack.o cpack.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o gmpls.o gmpls.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o in_cksum.o in_cksum.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o ipproto.o ipproto.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o l2vpn.o l2vpn.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o netdissect.o netdissect.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o netdissect-alloc.o netdissect-alloc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o nlpid.o nlpid.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o ntp.o ntp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o oui.o oui.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o parsenfsfh.o parsenfsfh.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print.o print.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-802_11.o print-802_11.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-802_15_4.o print-802_15_4.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ah.o print-ah.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ahcp.o print-ahcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-aodv.o print-aodv.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-aoe.o print-aoe.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ap1394.o print-ap1394.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-arcnet.o print-arcnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-arista.o print-arista.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-arp.o print-arp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ascii.o print-ascii.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-atalk.o print-atalk.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-atm.o print-atm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-babel.o print-babel.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bcm-li.o print-bcm-li.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-beep.o print-beep.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bfd.o print-bfd.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bgp.o print-bgp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bootp.o print-bootp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-brcmtag.o print-brcmtag.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bt.o print-bt.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-calm-fast.o print-calm-fast.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-carp.o print-carp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cdp.o print-cdp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cfm.o print-cfm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-chdlc.o print-chdlc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cip.o print-cip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cnfp.o print-cnfp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dccp.o print-dccp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-decnet.o print-decnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dhcp6.o print-dhcp6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-domain.o print-domain.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dsa.o print-dsa.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dtp.o print-dtp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dvmrp.o print-dvmrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-eap.o print-eap.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-egp.o print-egp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-eigrp.o print-eigrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-enc.o print-enc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-erspan.o print-erspan.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-esp.o print-esp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ether.o print-ether.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-fddi.o print-fddi.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-forces.o print-forces.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-fr.o print-fr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-frag6.o print-frag6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ftp.o print-ftp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-geneve.o print-geneve.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-geonet.o print-geonet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-gre.o print-gre.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-hncp.o print-hncp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-hsrp.o print-hsrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-http.o print-http.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-icmp.o print-icmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-icmp6.o print-icmp6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-igmp.o print-igmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-igrp.o print-igrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip-demux.o print-ip-demux.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip.o print-ip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip6.o print-ip6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip6opts.o print-ip6opts.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipcomp.o print-ipcomp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipfc.o print-ipfc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipnet.o print-ipnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipoib.o print-ipoib.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipx.o print-ipx.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-isakmp.o print-isakmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-isoclns.o print-isoclns.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-juniper.o print-juniper.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-krb.o print-krb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-l2tp.o print-l2tp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lane.o print-lane.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ldp.o print-ldp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lisp.o print-lisp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-llc.o print-llc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lldp.o print-lldp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lmp.o print-lmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-loopback.o print-loopback.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lspping.o print-lspping.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lwapp.o print-lwapp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lwres.o print-lwres.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-m3ua.o print-m3ua.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-macsec.o print-macsec.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mobile.o print-mobile.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mobility.o print-mobility.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mpcp.o print-mpcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mpls.o print-mpls.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mptcp.o print-mptcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-msdp.o print-msdp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-msnlb.o print-msnlb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nflog.o print-nflog.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nfs.o print-nfs.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nhrp.o print-nhrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nsh.o print-nsh.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ntp.o print-ntp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-null.o print-null.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-olsr.o print-olsr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-openflow-1.0.o print-openflow-1.0.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-openflow-1.3.o print-openflow-1.3.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-openflow.o print-openflow.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ospf.o print-ospf.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ospf6.o print-ospf6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-otv.o print-otv.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pflog.o print-pflog.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pgm.o print-pgm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pim.o print-pim.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pktap.o print-pktap.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ppi.o print-ppi.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ppp.o print-ppp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pppoe.o print-pppoe.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pptp.o print-pptp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ptp.o print-ptp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-quic.o print-quic.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-radius.o print-radius.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-raw.o print-raw.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-realtek.o print-realtek.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-resp.o print-resp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rip.o print-rip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ripng.o print-ripng.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rpki-rtr.o print-rpki-rtr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rsvp.o print-rsvp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rt6.o print-rt6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rtsp.o print-rtsp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rx.o print-rx.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sctp.o print-sctp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sflow.o print-sflow.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sip.o print-sip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sl.o print-sl.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sll.o print-sll.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-slow.o print-slow.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-smtp.o print-smtp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-snmp.o print-snmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-someip.o print-someip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ssh.o print-ssh.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-stp.o print-stp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sunatm.o print-sunatm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sunrpc.o print-sunrpc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-symantec.o print-symantec.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-syslog.o print-syslog.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-tcp.o print-tcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-telnet.o print-telnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-tftp.o print-tftp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-timed.o print-timed.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-tipc.o print-tipc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-token.o print-token.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-udld.o print-udld.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-udp.o print-udp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-unsupported.o print-unsupported.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-usb.o print-usb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vjc.o print-vjc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vqp.o print-vqp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vrrp.o print-vrrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vsock.o print-vsock.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vtp.o print-vtp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vxlan-gpe.o print-vxlan-gpe.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vxlan.o print-vxlan.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-wb.o print-wb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-whois.o print-whois.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-zep.o print-zep.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-zephyr.o print-zephyr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-zeromq.o print-zeromq.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o signature.o signature.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o strtoaddr.o strtoaddr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o util-print.o util-print.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -o strlcat.o -c ./missing/strlcat.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -o strlcpy.o -c ./missing/strlcpy.c
ar cr libnetdissect.a addrtoname.o addrtostr.o af.o ascii_strcasecmp.o checksum.o cpack.o gmpls.o in_cksum.o ipproto.o l2vpn.o netdissect.o netdissect-alloc.o nlpid.o ntp.o oui.o parsenfsfh.o print.o print-802_11.o print-802_15_4.o print-ah.o print-ahcp.o print-aodv.o print-aoe.o print-ap1394.o print-arcnet.o print-arista.o print-arp.o print-ascii.o print-atalk.o print-atm.o print-babel.o print-bcm-li.o print-beep.o print-bfd.o print-bgp.o print-bootp.o print-brcmtag.o print-bt.o print-calm-fast.o print-carp.o print-cdp.o print-cfm.o print-chdlc.o print-cip.o print-cnfp.o print-dccp.o print-decnet.o print-dhcp6.o print-domain.o print-dsa.o print-dtp.o print-dvmrp.o print-eap.o print-egp.o print-eigrp.o print-enc.o print-erspan.o print-esp.o print-ether.o print-fddi.o print-forces.o print-fr.o print-frag6.o print-ftp.o print-geneve.o print-geonet.o print-gre.o print-hncp.o print-hsrp.o print-http.o print-icmp.o print-icmp6.o print-igmp.o print-igrp.o print-ip-demux.o print-ip.o print-ip6.o print-ip6opts.o print-ipcomp.o print-ipfc.o print-ipnet.o print-ipoib.o print-ipx.o print-isakmp.o print-isoclns.o print-juniper.o print-krb.o print-l2tp.o print-lane.o print-ldp.o print-lisp.o print-llc.o print-lldp.o print-lmp.o print-loopback.o print-lspping.o print-lwapp.o print-lwres.o print-m3ua.o print-macsec.o print-mobile.o print-mobility.o print-mpcp.o print-mpls.o print-mptcp.o print-msdp.o print-msnlb.o print-nflog.o print-nfs.o print-nhrp.o print-nsh.o print-ntp.o print-null.o print-olsr.o print-openflow-1.0.o print-openflow-1.3.o print-openflow.o print-ospf.o print-ospf6.o print-otv.o print-pflog.o print-pgm.o print-pim.o print-pktap.o print-ppi.o print-ppp.o print-pppoe.o print-pptp.o print-ptp.o print-quic.o print-radius.o print-raw.o print-realtek.o print-resp.o print-rip.o print-ripng.o print-rpki-rtr.o print-rsvp.o print-rt6.o print-rtsp.o print-rx.o print-sctp.o print-sflow.o print-sip.o print-sl.o print-sll.o print-slow.o print-smtp.o print-snmp.o print-someip.o print-ssh.o print-stp.o print-sunatm.o print-sunrpc.o print-symantec.o print-syslog.o print-tcp.o print-telnet.o print-tftp.o print-timed.o print-tipc.o print-token.o print-udld.o print-udp.o print-unsupported.o print-usb.o print-vjc.o print-vqp.o print-vrrp.o print-vsock.o print-vtp.o print-vxlan-gpe.o print-vxlan.o print-wb.o print-whois.o print-zep.o print-zephyr.o print-zeromq.o signature.o strtoaddr.o util-print.o  strlcat.o strlcpy.o
ranlib libnetdissect.a
make[1]: Leaving directory '/app/no_perf/tcpdump-5.0.0'
cc -g ./agent/devname.o ./agent/err.o ./agent/escape.o ./agent/info.o ./agent/meminfo.o ./agent/namespace.o ./agent/numa.o ./agent/pids.o ./agent/pwcache.o ./agent/readproc.o ./agent/send.o ./agent/send_data.o ./agent/stat.o ./agent/sysinfo.o ./agent/thpool.o ./agent/top.o ./agent/top_nls.o ./agent/uptime.o ./agent/version.o ./agent/wchan.o  ./sar/act_sadc.o ./sar/sadc.o ./sar/pr_stats.o  ./sar/sa_wrap.o ./sar/sa_common.o ./sar/network_security.o ./sar/librdstats.a ./sar/librdsensors.a ./sar/libsyscom.a  ./tcpdump-5.0.0/fptype.o ./tcpdump-5.0.0/tcpdump.o ./tcpdump-5.0.0/libnetdissect.a ./libpcap/libpcap.a -o send_data -lm -lpthread -ldl -lrt -lcrypto
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/devname.o -MMD -MP -MF .deps/agent/devname.d -c agent/devname.c -o agent/devname.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/err.o -MMD -MP -MF .deps/agent/err.d -c agent/err.c -o agent/err.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/escape.o -MMD -MP -MF .deps/agent/escape.d -c agent/escape.c -o agent/escape.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/info.o -MMD -MP -MF .deps/agent/info.d -c agent/info.c -o agent/info.o
In file included from agent/info.c:545:
agent/pids.h:271:30: warning: ‘struct pids_info’ declared inside parameter list will not be visible outside of this definition or declaration
  271 | typedef void (*SET_t)(struct pids_info *, struct pids_result *, proc_t *);
      |                              ^~~~~~~~~
agent/info.c: In function ‘zap_fieldstab’:
agent/info.c:1455:10: warning: suggest braces around empty body in an ‘if’ statement [-Wempty-body]
 1455 |          ;
      |          ^
agent/info.c: In function ‘output_load’:
agent/info.c:1508:14: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 2 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |            ~~^
      |              |
      |              long unsigned int
      |            %u
agent/info.c:1508:20: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 3 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                ~~~~^
      |                    |
      |                    long unsigned int
      |                %02u
agent/info.c:1508:24: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 4 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                      ~~^
      |                        |
      |                        long unsigned int
      |                      %u
agent/info.c:1508:30: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 5 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                          ~~~~^
      |                              |
      |                              long unsigned int
      |                          %02u
agent/info.c:1508:34: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 6 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                                ~~^
      |                                  |
      |                                  long unsigned int
      |                                %u
agent/info.c:1508:40: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 7 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                                    ~~~~^
      |                                        |
      |                                        long unsigned int
      |                                    %02u
agent/info.c: In function ‘avg_load’:
agent/info.c:1562:10: warning: unused variable ‘g2’ [-Wunused-variable]
 1562 |    float g2 = round(loads3 / f + e);
      |          ^~
agent/info.c:1561:10: warning: unused variable ‘g1’ [-Wunused-variable]
 1561 |    float g1 = round(loads2 / f + e);
      |          ^~
agent/info.c: In function ‘meminfo’:
agent/info.c:2076:10: warning: unused variable ‘pct_misc’ [-Wunused-variable]
 2076 |    float pct_misc = (float)(kb_main_total - kb_main_available - kb_main_used) * (100.0 / (float)kb_main_total);
      |          ^~~~~~~~
agent/info.c: In function ‘before’:
agent/info.c:2094:8: warning: unused variable ‘linux_version_code’ [-Wunused-variable]
 2094 |    int linux_version_code = procps_linux_version();
      |        ^~~~~~~~~~~~~~~~~~
agent/info.c: In function ‘cpu_help’:
agent/info.c:2218:10: warning: unused variable ‘scale1’ [-Wunused-variable]
 2218 |    float scale1 = 0.0;
      |          ^~~~~~
agent/info.c: In function ‘tasks_refresh’:
agent/info.c:2491:8: warning: unused variable ‘i’ [-Wunused-variable]
 2491 |    int i, what;
      |        ^
agent/info.c:2488:15: warning: unused variable ‘n_alloc’ [-Wunused-variable]
 2488 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c: In function ‘tasks_refresh1’:
agent/info.c:2554:8: warning: unused variable ‘i’ [-Wunused-variable]
 2554 |    int i, what;
      |        ^
agent/info.c:2551:15: warning: unused variable ‘n_alloc’ [-Wunused-variable]
 2551 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c: In function ‘cal_cpu’:
agent/info.c:2765:24: warning: argument to ‘sizeof’ in ‘snprintf’ call is the same expression as the destination; did you mean to provide an explicit length? [-Wsizeof-pointer-memaccess]
 2765 |    snprintf(buf, sizeof(buf), "%#.1f", u);
      |                        ^
agent/info.c: In function ‘cal_mem’:
agent/info.c:2812:24: warning: argument to ‘sizeof’ in ‘snprintf’ call is the same expression as the destination; did you mean to provide an explicit length? [-Wsizeof-pointer-memaccess]
 2812 |    snprintf(buf, sizeof(buf), "%#.1f", m);
      |                        ^
agent/info.c: In function ‘sys’:
agent/info.c:3088:13: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3088 |             return;
      |             ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c:3254:11: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3254 |           return;
      |           ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c:3260:11: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3260 |           return;
      |           ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c:3269:11: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3269 |           return;
      |           ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c: In function ‘get_load’:
agent/info.c:1498:1: warning: control reaches end of non-void function [-Wreturn-type]
 1498 | }
      | ^
agent/info.c: In function ‘output_load’:
agent/info.c:1513:1: warning: control reaches end of non-void function [-Wreturn-type]
 1513 | }
      | ^
agent/info.c: In function ‘get_time1’:
agent/info.c:1533:1: warning: control reaches end of non-void function [-Wreturn-type]
 1533 | }
      | ^
agent/info.c: In function ‘avg_load’:
agent/info.c:1573:1: warning: control reaches end of non-void function [-Wreturn-type]
 1573 | }
      | ^
agent/info.c: In function ‘cpu_help’:
agent/info.c:2260:1: warning: control reaches end of non-void function [-Wreturn-type]
 2260 | }
      | ^
agent/info.c: In function ‘mem_help’:
agent/info.c:2349:1: warning: control reaches end of non-void function [-Wreturn-type]
 2349 | }
      | ^
agent/info.c: In function ‘init_cpu’:
agent/info.c:2620:1: warning: control reaches end of non-void function [-Wreturn-type]
 2620 | }
      | ^
agent/info.c: In function ‘init_parpids’:
agent/info.c:2850:1: warning: control reaches end of non-void function [-Wreturn-type]
 2850 | }
      | ^
agent/info.c: In function ‘init_thrpids’:
agent/info.c:2886:1: warning: control reaches end of non-void function [-Wreturn-type]
 2886 | }
      | ^
agent/info.c: In function ‘add_process_thread_count’:
agent/info.c:2896:1: warning: control reaches end of non-void function [-Wreturn-type]
 2896 | }
      | ^
agent/info.c: In function ‘add_thread_count’:
agent/info.c:2906:1: warning: control reaches end of non-void function [-Wreturn-type]
 2906 | }
      | ^
agent/info.c: At top level:
agent/info.c:2551:15: warning: ‘n_alloc’ defined but not used [-Wunused-variable]
 2551 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c:2488:15: warning: ‘n_alloc’ defined but not used [-Wunused-variable]
 2488 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c:2948:13: warning: ‘wait_for_semaphore’ defined but not used [-Wunused-function]
 2948 | static void wait_for_semaphore(sem_t *sem) {
      |             ^~~~~~~~~~~~~~~~~~
agent/info.c:2664:13: warning: ‘forest_begin’ defined but not used [-Wunused-function]
 2664 | static void forest_begin(WIN_t *q)
      |             ^~~~~~~~~~~~
agent/info.c:2545:14: warning: ‘tasks_refresh1’ defined but not used [-Wunused-function]
 2545 | static void *tasks_refresh1(void *unused)
      |              ^~~~~~~~~~~~~~
agent/info.c:2358:13: warning: ‘wins_stage_1’ defined but not used [-Wunused-function]
 2358 | static void wins_stage_1(void)
      |             ^~~~~~~~~~~~
agent/info.c:1355:13: warning: ‘zap_fieldstab’ defined but not used [-Wunused-function]
 1355 | static void zap_fieldstab(void)
      |             ^~~~~~~~~~~~~
agent/info.c:1348:20: warning: ‘Cursor_state’ defined but not used [-Wunused-variable]
 1348 | static const char *Cursor_state = "";
      |                    ^~~~~~~~~~~~
agent/info.c:1265:13: warning: ‘win_names’ defined but not used [-Wunused-function]
 1265 | static void win_names(WIN_t *q, const char *name)
      |             ^~~~~~~~~
agent/info.c:842:13: warning: ‘Stdout_buf’ defined but not used [-Wunused-variable]
  842 | static char Stdout_buf[2048];
      |             ^~~~~~~~~~
agent/info.c:841:12: warning: ‘Ttychanged’ defined but not used [-Wunused-variable]
  841 | static int Ttychanged = 0;
      |            ^~~~~~~~~~
agent/info.c:840:5: warning: ‘Tty_raw’ defined but not used [-Wunused-variable]
  840 |     Tty_raw; // for unsolicited input
      |     ^~~~~~~
agent/info.c:836:23: warning: ‘Tty_original’ defined but not used [-Wunused-variable]
  836 | static struct termios Tty_original, // our inherited terminal definition
      |                       ^~~~~~~~~~~~
agent/info.c:826:15: warning: ‘Pseudo_size’ defined but not used [-Wunused-variable]
  826 | static size_t Pseudo_size;
      |               ^~~~~~~~~~~
agent/info.c:825:15: warning: ‘Bot_show_func’ defined but not used [-Wunused-variable]
  825 | static void (*Bot_show_func)(void);
      |               ^~~~~~~~~~~~~
agent/info.c:824:14: warning: ‘Bot_focus_func’ defined but not used [-Wunused-variable]
  824 | static BOT_f Bot_focus_func;
      |              ^~~~~~~~~~~~~~
agent/info.c:822:5: warning: ‘Bot_buf’ defined but not used [-Wunused-variable]
  822 |     Bot_buf[BOTBUFSIZ]; // the 'environ' can be huge
      |     ^~~~~~~
agent/info.c:821:6: warning: ‘Bot_head’ defined but not used [-Wunused-variable]
  821 |     *Bot_head,
      |      ^~~~~~~~
agent/info.c:820:13: warning: ‘Bot_sep’ defined but not used [-Wunused-variable]
  820 | static char Bot_sep,
      |             ^~~~~~~
agent/info.c:818:5: warning: ‘Bot_indx’ defined but not used [-Wunused-variable]
  818 |     Bot_indx = BOT_UNFOCUS,
      |     ^~~~~~~~
agent/info.c:817:5: warning: ‘Bot_rsvd’ defined but not used [-Wunused-variable]
  817 |     Bot_rsvd,
      |     ^~~~~~~~
agent/info.c:816:5: warning: ‘Bot_what’ defined but not used [-Wunused-variable]
  816 |     Bot_what,
      |     ^~~~~~~~
agent/info.c:815:12: warning: ‘Bot_task’ defined but not used [-Wunused-variable]
  815 | static int Bot_task,
      |            ^~~~~~~~
agent/info.c:787:12: warning: ‘Cap_can_goto’ defined but not used [-Wunused-variable]
  787 | static int Cap_can_goto = 0;
      |            ^~~~~~~~~~~~
agent/info.c:785:12: warning: ‘Cap_avoid_eol’ defined but not used [-Wunused-variable]
  785 | static int Cap_avoid_eol = 0;
      |            ^~~~~~~~~~~~~
agent/info.c:782:13: warning: ‘Cap_smam’ defined but not used [-Wunused-variable]
  782 |             Cap_smam[CAPBUFSIZ] = "";
      |             ^~~~~~~~
agent/info.c:781:13: warning: ‘Cap_rmam’ defined but not used [-Wunused-variable]
  781 | static char Cap_rmam[CAPBUFSIZ] = "",
      |             ^~~~~~~~
agent/info.c:779:13: warning: ‘Caps_endline’ defined but not used [-Wunused-variable]
  779 |             Caps_endline[SMLBUFSIZ] = "";
      |             ^~~~~~~~~~~~
agent/info.c:778:13: warning: ‘Caps_off’ defined but not used [-Wunused-variable]
  778 |             Caps_off[CAPBUFSIZ] = "",
      |             ^~~~~~~~
agent/info.c:777:13: warning: ‘Cap_reverse’ defined but not used [-Wunused-variable]
  777 |             Cap_reverse[CAPBUFSIZ] = "",
      |             ^~~~~~~~~~~
agent/info.c:775:13: warning: ‘Cap_home’ defined but not used [-Wunused-variable]
  775 |             Cap_home[CAPBUFSIZ] = "",
      |             ^~~~~~~~
agent/info.c:774:5: warning: ‘Cap_clr_eos’ defined but not used [-Wunused-variable]
  774 |     Cap_clr_eos[CAPBUFSIZ] = "",
      |     ^~~~~~~~~~~
agent/info.c:773:5: warning: ‘Cap_curs_hide’ defined but not used [-Wunused-variable]
  773 |     Cap_curs_hide[CAPBUFSIZ] = "",       // batch requirements!
      |     ^~~~~~~~~~~~~
agent/info.c:772:5: warning: ‘Cap_curs_huge’ defined but not used [-Wunused-variable]
  772 |     Cap_curs_huge[CAPBUFSIZ] = "",       // to remind people of those
      |     ^~~~~~~~~~~~~
agent/info.c:771:5: warning: ‘Cap_curs_norm’ defined but not used [-Wunused-variable]
  771 |     Cap_curs_norm[CAPBUFSIZ] = "",       // cost nothing but DO serve
      |     ^~~~~~~~~~~~~
agent/info.c:770:5: warning: ‘Cap_clr_scr’ defined but not used [-Wunused-variable]
  770 |     Cap_clr_scr[CAPBUFSIZ] = "",         // the assignments used here
      |     ^~~~~~~~~~~
agent/info.c:769:5: warning: ‘Cap_nl_clreos’ defined but not used [-Wunused-variable]
  769 |     Cap_nl_clreos[CAPBUFSIZ] = "",       // are initialized to zeros!
      |     ^~~~~~~~~~~~~
agent/info.c:768:13: warning: ‘Cap_clr_eol’ defined but not used [-Wunused-variable]
  768 | static char Cap_clr_eol[CAPBUFSIZ] = "", // global and/or static vars
      |             ^~~~~~~~~~~
agent/info.c:757:14: warning: ‘Myname’ defined but not used [-Wunused-variable]
  757 | static char *Myname;
      |              ^~~~~~
agent/info.c:604:17: warning: ‘Sigwinch_set’ defined but not used [-Wunused-variable]
  604 | static sigset_t Sigwinch_set;
      |                 ^~~~~~~~~~~~
agent/info.c:595:26: warning: ‘Pids_ctx1’ defined but not used [-Wunused-variable]
  595 | static struct pids_info *Pids_ctx1;
      |                          ^~~~~~~~~
agent/info.c:586:5: warning: ‘Width_mode’ defined but not used [-Wunused-variable]
  586 |     Width_mode = 0,   // set w/ 'w' - potential output override
      |     ^~~~~~~~~~
agent/info.c:585:5: warning: ‘Secure_mode’ defined but not used [-Wunused-variable]
  585 |     Secure_mode = 0,  // set if some functionality restricted
      |     ^~~~~~~~~~~
agent/info.c:584:5: warning: ‘Loops’ defined but not used [-Wunused-variable]
  584 |     Loops = -1,       // number of iterations, -1 loops forever
      |     ^~~~~
agent/info.c:583:12: warning: ‘Batch’ defined but not used [-Wunused-variable]
  583 | static int Batch = 0, // batch mode, collect no input, dumb output
      |            ^~~~~
agent/info.c:576:13: warning: ‘Adjoin_sp’ defined but not used [-Wunused-variable]
  576 | static char Adjoin_sp[] = " ~6 ~1";
      |             ^~~~~~~~~
agent/info.c:556:38: warning: ‘Max_lines’ defined but not used [-Wunused-variable]
  556 | static int Screen_cols, Screen_rows, Max_lines;
      |                                      ^~~~~~~~~
agent/info.c:556:25: warning: ‘Screen_rows’ defined but not used [-Wunused-variable]
  556 | static int Screen_cols, Screen_rows, Max_lines;
      |                         ^~~~~~~~~~~
agent/info.c:554:14: warning: ‘Pseudo_screen’ defined but not used [-Wunused-variable]
  554 | static char *Pseudo_screen;
      |              ^~~~~~~~~~~~~
agent/info.c: In function ‘avg_load’:
agent/info.c:1565:33: warning: ‘%d’ directive output may be truncated writing between 1 and 8 bytes into a region of size 7 [-Wformat-truncation=]
 1565 |    snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
      |                                 ^~
agent/info.c:1565:32: note: directive argument in the range [-1048576, 1048575]
 1565 |    snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
      |                                ^~~~~~~~
agent/info.c:1565:32: note: directive argument in the range [0, 99]
agent/info.c:1565:4: note: ‘snprintf’ output between 4 and 12 bytes into a destination of size 7
 1565 |    snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
      |    ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/meminfo.o -MMD -MP -MF .deps/agent/meminfo.d -c agent/meminfo.c -o agent/meminfo.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/namespace.o -MMD -MP -MF .deps/agent/namespace.d -c agent/namespace.c -o agent/namespace.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/numa.o -MMD -MP -MF .deps/agent/numa.d -c agent/numa.c -o agent/numa.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/pids.o -MMD -MP -MF .deps/agent/pids.d -c agent/pids.c -o agent/pids.o
In file included from agent/pids.c:44:
agent/pids.h:271:30: warning: ‘struct pids_info’ declared inside parameter list will not be visible outside of this definition or declaration
  271 | typedef void (*SET_t)(struct pids_info *, struct pids_result *, proc_t *);
      |                              ^~~~~~~~~
agent/pids.c: In function ‘free_pids_str’:
agent/pids.c:135:47: warning: unused parameter ‘R’ [-Wunused-parameter]
  135 | static void freNAME(str) (struct pids_result *R) {
      |                           ~~~~~~~~~~~~~~~~~~~~^
agent/pids.c: In function ‘free_pids_strv’:
agent/pids.c:139:48: warning: unused parameter ‘R’ [-Wunused-parameter]
  139 | static void freNAME(strv) (struct pids_result *R) {
      |                            ~~~~~~~~~~~~~~~~~~~~^
agent/pids.c: In function ‘pids_assign_results’:
agent/pids.c:990:17: warning: passing argument 1 of ‘*that’ from incompatible pointer type [-Wincompatible-pointer-types]
  990 |         (*that)(info, this, p);
      |                 ^~~~
      |                 |
      |                 struct pids_info *
agent/pids.c:990:17: note: expected ‘struct pids_info *’ but argument is of type ‘struct pids_info *’
agent/pids.c: In function ‘pids_cleanup_stacks_all’:
agent/pids.c:1023:13: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1023 |         ext = ext->next;
      |             ^
agent/pids.c: In function ‘pids_itemize_stacks_all’:
agent/pids.c:1082:13: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1082 |         ext = ext->next;
      |             ^
agent/pids.c: In function ‘pids_stacks_alloc’:
agent/pids.c:1315:18: warning: assignment to ‘struct stacks_extent *’ from incompatible pointer type ‘struct stacks_extent2 *’ [-Wincompatible-pointer-types]
 1315 |     p_blob->next = info->extents;                              // push this extent onto... |
      |                  ^
agent/pids.c:1330:12: warning: returning ‘struct stacks_extent2 *’ from a function with incompatible return type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1330 |     return p_blob;
      |            ^~~~~~
agent/pids.c: In function ‘pids_stacks_fetch’:
agent/pids.c:1367:19: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1367 |         if (!(ext = pids_stacks_alloc(info, STACKS_INIT)))
      |                   ^
agent/pids.c:1381:23: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1381 |             || (!(ext = pids_stacks_alloc(info, STACKS_GROW))))
      |                       ^
agent/pids.c: In function ‘pids_stacks_fetch1’:
agent/pids.c:1431:19: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1431 |         if (!(ext = pids_stacks_alloc(info, STACKS_INIT)))
      |                   ^
agent/pids.c:1445:23: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1445 |             || (!(ext = pids_stacks_alloc(info, STACKS_GROW))))
      |                       ^
agent/pids.c: In function ‘procps_pids_unref’:
agent/pids.c:1626:34: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1626 |                 (*info)->extents = (*info)->extents->next;
      |                                  ^
agent/pids.c:1633:25: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1633 |                 nextext = ext->next;
      |                         ^
agent/pids.c: In function ‘procps_pids_get’:
agent/pids.c:1710:29: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1710 |         if (!(info->get_ext = pids_stacks_alloc(info, 1)))
      |                             ^
agent/pids.c: In function ‘procps_pids_reset’:
agent/pids.c:1844:27: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1844 |             info->extents = p->next;
      |                           ^
agent/pids.c: In function ‘procps_pids_sort’:
agent/pids.c:1947:23: warning: variable ‘parms’ set but not used [-Wunused-but-set-variable]
 1947 |     struct sort_parms parms;
      |                       ^~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/pwcache.o -MMD -MP -MF .deps/agent/pwcache.d -c agent/pwcache.c -o agent/pwcache.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/readproc.o -MMD -MP -MF .deps/agent/readproc.d -c agent/readproc.c -o agent/readproc.o
agent/readproc.c: In function ‘stat2proc’:
agent/readproc.c:597:16: warning: unused variable ‘le’ [-Wunused-variable]
  597 |         size_t le = strlen(buf) + 1;
      |                ^~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/send.o -MMD -MP -MF .deps/agent/send.d -c agent/send.c -o agent/send.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/send_data.o -MMD -MP -MF .deps/agent/send_data.d -c agent/send_data.c -o agent/send_data.o
In file included from agent/send_data.c:50:
agent/pids.h:271:30: warning: ‘struct pids_info’ declared inside parameter list will not be visible outside of this definition or declaration
  271 | typedef void (*SET_t)(struct pids_info *, struct pids_result *, proc_t *);
      |                              ^~~~~~~~~
agent/send_data.c: In function ‘sigint_handler’:
agent/send_data.c:263:9: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
  263 |         write_log("ctrl-c caught ....\n");
      |         ^~~~~~~~~
agent/send_data.c:260:25: warning: unused parameter ‘sig’ [-Wunused-parameter]
  260 | void sigint_handler(int sig)
      |                     ~~~~^~~
agent/send_data.c: In function ‘stat2proc’:
agent/send_data.c:365:25: warning: implicit declaration of function ‘escape_str’ [-Wimplicit-function-declaration]
  365 |                         escape_str(buf, raw, sizeof(buf));
      |                         ^~~~~~~~~~
agent/send_data.c:416:9: warning: implicit declaration of function ‘LEAVE’ [-Wimplicit-function-declaration]
  416 |         LEAVE(0x160);
      |         ^~~~~
agent/send_data.c: In function ‘check_self’:
agent/send_data.c:462:24: warning: missing initializer for field ‘siz’ of ‘struct utlbuf_s’ [-Wmissing-field-initializers]
  462 |                 struct utlbuf_s ub = {NULL, 0};
      |                        ^~~~~~~~
In file included from agent/send_data.c:11:
agent/readproc.h:42:11: note: ‘siz’ declared here
   42 |     int   siz;     // current len of the above
      |           ^~~
agent/send_data.c:463:21: warning: variable ‘rc’ set but not used [-Wunused-but-set-variable]
  463 |                 int rc = 0;
      |                     ^~
agent/send_data.c: In function ‘alarm_handler1’:
agent/send_data.c:1191:25: warning: unused parameter ‘sig’ [-Wunused-parameter]
 1191 | void alarm_handler1(int sig)
      |                     ~~~~^~~
agent/send_data.c: At top level:
agent/send_data.c:1574:6: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
 1574 | void write_log(const char *format, ...)
      |      ^~~~~~~~~
agent/send_data.c:263:9: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
  263 |         write_log("ctrl-c caught ....\n");
      |         ^~~~~~~~~
agent/send_data.c: In function ‘main’:
agent/send_data.c:1840:46: warning: passing argument 3 of ‘pthread_create’ from incompatible pointer type [-Wincompatible-pointer-types]
 1840 |                 pthread_create(&sar1, &attr, &sadc1, NULL);
      |                                              ^~~~~~
      |                                              |
      |                                              int (*)()
In file included from agent/top.h:23,
                 from agent/send_data.c:5:
/usr/include/pthread.h:204:36: note: expected ‘void * (*)(void *)’ but argument is of type ‘int (*)()’
  204 |                            void *(*__start_routine) (void *),
      |                            ~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~
agent/send_data.c:1842:50: warning: passing argument 3 of ‘pthread_create’ from incompatible pointer type [-Wincompatible-pointer-types]
 1842 |                 pthread_create(&tcpdump1, &attr, &tcp1, argv[1]);
      |                                                  ^~~~~
      |                                                  |
      |                                                  int (*)(char *)
/usr/include/pthread.h:204:36: note: expected ‘void * (*)(void *)’ but argument is of type ‘int (*)(char *)’
  204 |                            void *(*__start_routine) (void *),
      |                            ~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~
agent/send_data.c:2053:33: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2053 |         for (int isar = 0; isar < os->int_count; isar++) {
      |                                 ^
agent/send_data.c:2064:33: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2064 |         for (int isar = 0; isar < os->enet_count; isar++) {
      |                                 ^
agent/send_data.c:2085:35: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2085 |         for (int idisk = 0; idisk < os->disk_count; idisk++) {
      |                                   ^
agent/send_data.c:2099:31: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2099 |         for (int ifs = 0; ifs < os->file_count; ifs++) {
      |                               ^
agent/send_data.c:2124:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2124 |         for (tcd = 0; tcd < os->group_s; tcd++)
      |                           ^
agent/send_data.c:2141:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2141 |         for (tcd = 0; tcd < os->group_s1; tcd++)
      |                           ^
agent/send_data.c:2159:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2159 |         for (tcd = 0; tcd < os->group_p; tcd++)
      |                           ^
agent/send_data.c:2196:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2196 |         for (tcd = 0; tcd < os->group_f; tcd++)
      |                           ^
agent/send_data.c:2214:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2214 |         for (tcd = 0; tcd < os->group_r; tcd++)
      |                           ^
agent/send_data.c:2232:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2232 |         for (tcd = 0; tcd < os->group_ack; tcd++)
      |                           ^
agent/send_data.c:1756:19: warning: unused variable ‘th’ [-Wunused-variable]
 1756 |         pthread_t th;
      |                   ^~
agent/send_data.c:1723:19: warning: unused variable ‘th1’ [-Wunused-variable]
 1723 |         pthread_t th1;
      |                   ^~~
agent/send_data.c: In function ‘c_tcp1’:
agent/send_data.c:1246:1: warning: control reaches end of non-void function [-Wreturn-type]
 1246 | }
      | ^
agent/send_data.c: In function ‘free_all1’:
agent/send_data.c:1552:1: warning: control reaches end of non-void function [-Wreturn-type]
 1552 | }
      | ^
agent/top.h: At top level:
agent/top.h:104:23: warning: ‘Stat_items’ defined but not used [-Wunused-variable]
  104 | static enum stat_item Stat_items[] = {
      |                       ^~~~~~~~~~
agent/top.h:60:13: warning: ‘UTF8_tab’ defined but not used [-Wunused-variable]
   60 | static char UTF8_tab[] = {
      |             ^~~~~~~~
agent/top.h:56:14: warning: ‘Frame_etscale’ defined but not used [-Wunused-variable]
   56 | static float Frame_etscale;
      |              ^~~~~~~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/stat.o -MMD -MP -MF .deps/agent/stat.d -c agent/stat.c -o agent/stat.o
agent/stat.c: In function ‘procps_stat_reap’:
agent/stat.c:1239:9: warning: unused variable ‘ff’ [-Wunused-variable]
 1239 |     int ff;
      |         ^~
agent/stat.c: In function ‘sum_tics’:
agent/stat.c:1425:10: warning: variable ‘scale’ set but not used [-Wunused-but-set-variable]
 1425 |    float scale;
      |          ^~~~~
agent/stat.c:1424:18: warning: unused variable ‘rx’ [-Wunused-variable]
 1424 |    struct rx_st *rx;
      |                  ^~
agent/stat.c:1423:10: warning: variable ‘idl_frme’ set but not used [-Wunused-but-set-variable]
 1423 |    SIC_t idl_frme, tot_frme;
      |          ^~~~~~~~
agent/stat.c:1433:1: warning: no return statement in function returning non-void [-Wreturn-type]
 1433 | } // end: sum_tics
      | ^
agent/stat.c:1419:41: warning: unused parameter ‘this’ [-Wunused-parameter]
 1419 | static int sum_tics (struct stat_stack *this) {
      |                      ~~~~~~~~~~~~~~~~~~~^~~~
agent/stat.c: At top level:
agent/stat.c:1419:12: warning: ‘sum_tics’ defined but not used [-Wunused-function]
 1419 | static int sum_tics (struct stat_stack *this) {
      |            ^~~~~~~~
agent/stat.c:44:20: warning: ‘Cpu_States_fmts’ defined but not used [-Wunused-variable]
   44 | static const char *Cpu_States_fmts;
      |                    ^~~~~~~~~~~~~~~
agent/stat.c:43:20: warning: ‘Cpu_pmax’ defined but not used [-Wunused-variable]
   43 | static float       Cpu_pmax;
      |                    ^~~~~~~~
agent/stat.c:42:20: warning: ‘Hertz’ defined but not used [-Wunused-variable]
   42 | static long        Hertz;
      |                    ^~~~~
In file included from agent/stat.c:36:
agent/top.h:104:23: warning: ‘Stat_items’ defined but not used [-Wunused-variable]
  104 | static enum stat_item Stat_items[] = {
      |                       ^~~~~~~~~~
agent/top.h:60:13: warning: ‘UTF8_tab’ defined but not used [-Wunused-variable]
   60 | static char UTF8_tab[] = {
      |             ^~~~~~~~
agent/top.h:56:14: warning: ‘Frame_etscale’ defined but not used [-Wunused-variable]
   56 | static float Frame_etscale;
      |              ^~~~~~~~~~~~~
agent/stat.c: In function ‘sum_tics’:
agent/stat.c:1430:7: warning: ‘tot_frme’ is used uninitialized [-Wuninitialized]
 1430 |    if (1 > tot_frme) idl_frme = tot_frme = 1;
      |       ^
agent/stat.c:1423:20: note: ‘tot_frme’ was declared here
 1423 |    SIC_t idl_frme, tot_frme;
      |                    ^~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/sysinfo.o -MMD -MP -MF .deps/agent/sysinfo.d -c agent/sysinfo.c -o agent/sysinfo.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/thpool.o -MMD -MP -MF .deps/agent/thpool.d -c agent/thpool.c -o agent/thpool.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/top.o -MMD -MP -MF .deps/agent/top.d -c agent/top.c -o agent/top.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/top_nls.o -MMD -MP -MF .deps/agent/top_nls.d -c agent/top_nls.c -o agent/top_nls.o
In file included from agent/top_nls.c:24:
agent/top.h: In function ‘utf8_cols’:
agent/top.h:89:23: warning: implicit declaration of function ‘mbtowc’; did you mean ‘mbrtowc’? [-Wimplicit-function-declaration]
   89 |                 (void)mbtowc(&wc, (const char *)p, n);
      |                       ^~~~~~
      |                       mbrtowc
agent/top.h: At top level:
agent/top.h:104:23: warning: ‘Stat_items’ defined but not used [-Wunused-variable]
  104 | static enum stat_item Stat_items[] = {
      |                       ^~~~~~~~~~
agent/top.h:60:13: warning: ‘UTF8_tab’ defined but not used [-Wunused-variable]
   60 | static char UTF8_tab[] = {
      |             ^~~~~~~~
agent/top.h:56:14: warning: ‘Frame_etscale’ defined but not used [-Wunused-variable]
   56 | static float Frame_etscale;
      |              ^~~~~~~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/uptime.o -MMD -MP -MF .deps/agent/uptime.d -c agent/uptime.c -o agent/uptime.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/version.o -MMD -MP -MF .deps/agent/version.d -c agent/version.c -o agent/version.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/wchan.o -MMD -MP -MF .deps/agent/wchan.d -c agent/wchan.c -o agent/wchan.o
make -C ./sar
make[1]: Entering directory '/app/no_perf/sar'
gcc -o act_sadc.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SADC -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  activity.c
gcc -o sa_wrap.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP sa_wrap.c
gcc -o sa_common.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP sa_common.c
gcc -o rd_stats.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SADC -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  rd_stats.c
rd_stats.c: In function ‘read_filesystem’:
rd_stats.c:2361:46: warning: passing argument 2 of ‘statvfs’ from incompatible pointer type [-Wincompatible-pointer-types]
 2361 |                         if ((statvfs(mountp, &buf) < 0) || (!buf.f_blocks))
      |                                              ^~~~
      |                                              |
      |                                              struct statfs *
In file included from rd_stats.c:31:
/usr/include/x86_64-linux-gnu/sys/statvfs.h:52:48: note: expected ‘struct statvfs * restrict’ but argument is of type ‘struct statfs *’
   52 |                     struct statvfs *__restrict __buf)
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~
gcc -o count.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SADC -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  count.c
ar rvs librdstats.a rd_stats.o count.o
r - rd_stats.o
r - count.o
gcc -o rd_sensors.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP rd_sensors.c
ar rv librdsensors.a rd_sensors.o
r - rd_sensors.o
gcc -o sadc.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP sadc.c
In file included from sadc.c:86:
../agent/top.h: In function ‘utf8_cols’:
../agent/top.h:91:26: warning: implicit declaration of function ‘wcwidth’ [-Wimplicit-function-declaration]
   91 |                 if ((n = wcwidth(wc)) < 0)
      |                          ^~~~~~~
gcc -o act_sar.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SAR -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  activity.c
gcc -o public.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP public.c
public.c: In function ‘update_sar_lock1’:
public.c:8:1: warning: control reaches end of non-void function [-Wreturn-type]
    8 | }
      | ^
gcc -o pr_stats.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP pr_stats.c
In file included from pr_stats.c:33:
../agent/top.h: In function ‘utf8_cols’:
../agent/top.h:91:26: warning: implicit declaration of function ‘wcwidth’ [-Wimplicit-function-declaration]
   91 |                 if ((n = wcwidth(wc)) < 0)
      |                          ^~~~~~~
gcc -o network_security.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP network_security.c
network_security.c: In function ‘build_socket_process_map’:
network_security.c:88:55: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size between 236 and 245 [-Wformat-truncation=]
   88 |             snprintf(path, sizeof(path), "/proc/%d/fd/%s", pid, fd_entry->d_name);
      |                                                       ^~
network_security.c:88:13: note: ‘snprintf’ output between 12 and 276 bytes into a destination of size 256
   88 |             snprintf(path, sizeof(path), "/proc/%d/fd/%s", pid, fd_entry->d_name);
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
gcc -o iostat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP iostat.c
iostat.c: In function ‘read_sysfs_dlist_part_stat’:
iostat.c:565:53: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size between 0 and 1023 [-Wformat-truncation=]
  565 |                 snprintf(filename, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                                                     ^~
iostat.c:565:17: note: ‘snprintf’ output between 7 and 1285 bytes into a destination of size 1024
  565 |                 snprintf(filename, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
gcc -o rd_stats_light.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  rd_stats.c
rd_stats.c: In function ‘read_filesystem’:
rd_stats.c:2361:46: warning: passing argument 2 of ‘statvfs’ from incompatible pointer type [-Wincompatible-pointer-types]
 2361 |                         if ((statvfs(mountp, &buf) < 0) || (!buf.f_blocks))
      |                                              ^~~~
      |                                              |
      |                                              struct statfs *
In file included from rd_stats.c:31:
/usr/include/x86_64-linux-gnu/sys/statvfs.h:52:48: note: expected ‘struct statvfs * restrict’ but argument is of type ‘struct statfs *’
   52 |                     struct statvfs *__restrict __buf)
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~
gcc -o count_light.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  count.c
ar rvs librdstats_light.a rd_stats_light.o count_light.o
r - rd_stats_light.o
r - count_light.o
gcc -o common.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP common.c
common.c: In function ‘get_dev_part_nr’:
common.c:214:49: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size between 0 and 1023 [-Wformat-truncation=]
  214 |                 snprintf(line, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                                                 ^~
common.c:214:17: note: ‘snprintf’ output between 7 and 1285 bytes into a destination of size 1024
  214 |                 snprintf(line, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
gcc -o ioconf.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP ioconf.c
ioconf.c: In function ‘ioc_init’:
ioconf.c:301:46: warning: ‘%s’ directive writing up to 31 bytes into a region of size 16 [-Wformat-overflow=]
  301 |                         sprintf(blkp->cfmt, "%s%s%%d", blkp->name, cfmt);
      |                                              ^~
ioconf.c:301:25: note: ‘sprintf’ output between 3 and 49 bytes into a destination of size 16
  301 |                         sprintf(blkp->cfmt, "%s%s%%d", blkp->name, cfmt);
      |                         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ioconf.c:322:42: warning: ‘d’ directive writing 1 byte into a region of size between 0 and 15 [-Wformat-overflow=]
  322 |                 sprintf(blkp->pfmt, "%s%%d", (*pfmt == '*') ? "" : pfmt);
      |                                          ^
ioconf.c:322:17: note: ‘sprintf’ output between 3 and 18 bytes into a destination of size 16
  322 |                 sprintf(blkp->pfmt, "%s%%d", (*pfmt == '*') ? "" : pfmt);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ioconf.c: In function ‘transform_devmapname’:
ioconf.c:497:51: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size 244 [-Wformat-truncation=]
  497 |                 snprintf(filen, MAX_FILE_LEN, "%s/%s", DEVMAP_DIR, dp->d_name);
      |                                                   ^~
ioconf.c:497:17: note: ‘snprintf’ output between 13 and 268 bytes into a destination of size 256
  497 |                 snprintf(filen, MAX_FILE_LEN, "%s/%s", DEVMAP_DIR, dp->d_name);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ar rvs libsyscom.a common.o ioconf.o
r - common.o
r - ioconf.o
gcc -o iostat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context iostat.o librdstats_light.a libsyscom.a 
gcc -o mpstat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP mpstat.c
gcc -o mpstat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context mpstat.o librdstats_light.a libsyscom.a 
gcc -o pidstat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP pidstat.c
gcc -o pidstat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context pidstat.o librdstats_light.a libsyscom.a 
gcc -o nfsiostat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP nfsiostat.c
gcc -o nfsiostat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context nfsiostat.o librdstats_light.a libsyscom.a 
gcc -o cifsiostat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP cifsiostat.c
gcc -o cifsiostat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context cifsiostat.o librdstats_light.a libsyscom.a 
make[1]: Leaving directory '/app/no_perf/sar'
make -C ./libpcap
make[1]: Entering directory '/app/no_perf/libpcap'
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-linux.o pcap-linux.c
pcap-linux.c: In function ‘pcap_wait_for_frames_mmap’:
pcap-linux.c:3666:25: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
 3666 |                         write_log("pcap_wait_for_frames_mmap: POSIX定时器信号触发(循环开始)，1秒时间到\n");
      |                         ^~~~~~~~~
pcap-linux.c: In function ‘pcap_read_linux_mmap_v2’:
pcap-linux.c:4454:24: warning: implicit declaration of function ‘get_optimal_timer_fd’ [-Wimplicit-function-declaration]
 4454 |         int timer_fd = get_optimal_timer_fd();  // 获取v2函数的定时器
      |                        ^~~~~~~~~~~~~~~~~~~~
pcap-linux.c: At top level:
pcap-linux.c:6357:13: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
 6357 | extern void write_log(const char *format, ...);
      |             ^~~~~~~~~
pcap-linux.c:3666:25: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
 3666 |                         write_log("pcap_wait_for_frames_mmap: POSIX定时器信号触发(循环开始)，1秒时间到\n");
      |                         ^~~~~~~~~
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o fad-getad.o fad-getad.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-usb-linux.o pcap-usb-linux.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-netfilter-linux.o pcap-netfilter-linux.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap.o pcap.c
pcap.c: In function ‘posix_timer_handler’:
pcap.c:242:9: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
  242 |         write_log("POSIX定时器信号触发，设置pcap_timeout1=1\n");
      |         ^~~~~~~~~
pcap.c: At top level:
pcap.c:259:13: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
  259 | extern void write_log(const char *format, ...);
      |             ^~~~~~~~~
pcap.c:242:9: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
  242 |         write_log("POSIX定时器信号触发，设置pcap_timeout1=1\n");
      |         ^~~~~~~~~
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c ./gencode.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o optimize.o optimize.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o nametoaddr.o nametoaddr.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o etherent.o etherent.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o fmtutils.o fmtutils.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-util.o pcap-util.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o savefile.o savefile.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o sf-pcap.o sf-pcap.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o sf-pcapng.o sf-pcapng.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-common.o pcap-common.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o bpf_image.o bpf_image.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o bpf_filter.o bpf_filter.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o bpf_dump.o bpf_dump.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c scanner.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c grammar.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -o strlcat.o -c ./missing/strlcat.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -o strlcpy.o -c ./missing/strlcpy.c
ar rc libpcap.a pcap-linux.o fad-getad.o pcap-usb-linux.o pcap-netfilter-linux.o pcap.o gencode.o optimize.o nametoaddr.o etherent.o fmtutils.o pcap-util.o savefile.o sf-pcap.o sf-pcapng.o pcap-common.o bpf_image.o bpf_filter.o bpf_dump.o scanner.o grammar.o strlcat.o strlcpy.o 
ranlib libpcap.a
VER=`cat ./VERSION`; \
MAJOR_VER=`sed 's/\([0-9][0-9]*\)\..*/\1/' ./VERSION`; \
gcc  -shared -Wl,-soname,libpcap.so.$MAJOR_VER \
    -o libpcap.so.$VER pcap-linux.o fad-getad.o pcap-usb-linux.o pcap-netfilter-linux.o pcap.o gencode.o optimize.o nametoaddr.o etherent.o fmtutils.o pcap-util.o savefile.o sf-pcap.o sf-pcapng.o pcap-common.o bpf_image.o bpf_filter.o bpf_dump.o scanner.o grammar.o strlcat.o strlcpy.o  
make[1]: Leaving directory '/app/no_perf/libpcap'
make -C ./tcpdump-5.0.0 tcpdump.o fptype.o libnetdissect.a
make[1]: Entering directory '/app/no_perf/tcpdump-5.0.0'
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o tcpdump.o tcpdump.c
tcpdump.c: In function ‘init_barriers’:
tcpdump.c:384:17: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
  384 |                 write_log("init_barriers: barriers已经初始化\n");
      |                 ^~~~~~~~~
tcpdump.c:391:13: warning: implicit declaration of function ‘efficient_barrier_init’; did you mean ‘pthread_barrier_init’? [-Wimplicit-function-declaration]
  391 |         if (efficient_barrier_init(&barrier_a_done, 3) != 0) {
      |             ^~~~~~~~~~~~~~~~~~~~~~
      |             pthread_barrier_init
tcpdump.c:399:17: warning: implicit declaration of function ‘efficient_barrier_destroy’; did you mean ‘pthread_barrier_destroy’? [-Wimplicit-function-declaration]
  399 |                 efficient_barrier_destroy(&barrier_a_done);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~
      |                 pthread_barrier_destroy
tcpdump.c: At top level:
tcpdump.c:3655:6: warning: conflicting types for ‘efficient_barrier_destroy’; have ‘void(efficient_barrier_t *)’
 3655 | void efficient_barrier_destroy(efficient_barrier_t *barrier)
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~
tcpdump.c:399:17: note: previous implicit declaration of ‘efficient_barrier_destroy’ with type ‘void(efficient_barrier_t *)’
  399 |                 efficient_barrier_destroy(&barrier_a_done);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o fptype.o fptype.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o addrtoname.o addrtoname.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o addrtostr.o addrtostr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o af.o af.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o ascii_strcasecmp.o ascii_strcasecmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o checksum.o checksum.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o cpack.o cpack.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o gmpls.o gmpls.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o in_cksum.o in_cksum.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o ipproto.o ipproto.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o l2vpn.o l2vpn.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o netdissect.o netdissect.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o netdissect-alloc.o netdissect-alloc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o nlpid.o nlpid.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o ntp.o ntp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o oui.o oui.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o parsenfsfh.o parsenfsfh.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print.o print.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-802_11.o print-802_11.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-802_15_4.o print-802_15_4.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ah.o print-ah.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ahcp.o print-ahcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-aodv.o print-aodv.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-aoe.o print-aoe.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ap1394.o print-ap1394.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-arcnet.o print-arcnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-arista.o print-arista.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-arp.o print-arp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ascii.o print-ascii.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-atalk.o print-atalk.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-atm.o print-atm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-babel.o print-babel.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bcm-li.o print-bcm-li.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-beep.o print-beep.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bfd.o print-bfd.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bgp.o print-bgp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bootp.o print-bootp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-brcmtag.o print-brcmtag.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bt.o print-bt.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-calm-fast.o print-calm-fast.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-carp.o print-carp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cdp.o print-cdp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cfm.o print-cfm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-chdlc.o print-chdlc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cip.o print-cip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cnfp.o print-cnfp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dccp.o print-dccp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-decnet.o print-decnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dhcp6.o print-dhcp6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-domain.o print-domain.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dsa.o print-dsa.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dtp.o print-dtp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dvmrp.o print-dvmrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-eap.o print-eap.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-egp.o print-egp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-eigrp.o print-eigrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-enc.o print-enc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-erspan.o print-erspan.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-esp.o print-esp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ether.o print-ether.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-fddi.o print-fddi.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-forces.o print-forces.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-fr.o print-fr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-frag6.o print-frag6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ftp.o print-ftp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-geneve.o print-geneve.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-geonet.o print-geonet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-gre.o print-gre.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-hncp.o print-hncp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-hsrp.o print-hsrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-http.o print-http.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-icmp.o print-icmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-icmp6.o print-icmp6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-igmp.o print-igmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-igrp.o print-igrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip-demux.o print-ip-demux.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip.o print-ip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip6.o print-ip6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip6opts.o print-ip6opts.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipcomp.o print-ipcomp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipfc.o print-ipfc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipnet.o print-ipnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipoib.o print-ipoib.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipx.o print-ipx.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-isakmp.o print-isakmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-isoclns.o print-isoclns.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-juniper.o print-juniper.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-krb.o print-krb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-l2tp.o print-l2tp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lane.o print-lane.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ldp.o print-ldp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lisp.o print-lisp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-llc.o print-llc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lldp.o print-lldp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lmp.o print-lmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-loopback.o print-loopback.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lspping.o print-lspping.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lwapp.o print-lwapp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lwres.o print-lwres.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-m3ua.o print-m3ua.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-macsec.o print-macsec.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mobile.o print-mobile.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mobility.o print-mobility.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mpcp.o print-mpcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mpls.o print-mpls.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mptcp.o print-mptcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-msdp.o print-msdp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-msnlb.o print-msnlb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nflog.o print-nflog.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nfs.o print-nfs.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nhrp.o print-nhrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nsh.o print-nsh.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ntp.o print-ntp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-null.o print-null.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-olsr.o print-olsr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-openflow-1.0.o print-openflow-1.0.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-openflow-1.3.o print-openflow-1.3.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-openflow.o print-openflow.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ospf.o print-ospf.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ospf6.o print-ospf6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-otv.o print-otv.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pflog.o print-pflog.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pgm.o print-pgm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pim.o print-pim.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pktap.o print-pktap.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ppi.o print-ppi.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ppp.o print-ppp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pppoe.o print-pppoe.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pptp.o print-pptp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ptp.o print-ptp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-quic.o print-quic.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-radius.o print-radius.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-raw.o print-raw.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-realtek.o print-realtek.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-resp.o print-resp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rip.o print-rip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ripng.o print-ripng.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rpki-rtr.o print-rpki-rtr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rsvp.o print-rsvp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rt6.o print-rt6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rtsp.o print-rtsp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rx.o print-rx.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sctp.o print-sctp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sflow.o print-sflow.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sip.o print-sip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sl.o print-sl.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sll.o print-sll.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-slow.o print-slow.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-smtp.o print-smtp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-snmp.o print-snmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-someip.o print-someip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ssh.o print-ssh.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-stp.o print-stp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sunatm.o print-sunatm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sunrpc.o print-sunrpc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-symantec.o print-symantec.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-syslog.o print-syslog.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-tcp.o print-tcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-telnet.o print-telnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-tftp.o print-tftp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-timed.o print-timed.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-tipc.o print-tipc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-token.o print-token.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-udld.o print-udld.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-udp.o print-udp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-unsupported.o print-unsupported.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-usb.o print-usb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vjc.o print-vjc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vqp.o print-vqp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vrrp.o print-vrrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vsock.o print-vsock.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vtp.o print-vtp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vxlan-gpe.o print-vxlan-gpe.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vxlan.o print-vxlan.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-wb.o print-wb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-whois.o print-whois.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-zep.o print-zep.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-zephyr.o print-zephyr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-zeromq.o print-zeromq.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o signature.o signature.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o strtoaddr.o strtoaddr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o util-print.o util-print.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -o strlcat.o -c ./missing/strlcat.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -o strlcpy.o -c ./missing/strlcpy.c
ar cr libnetdissect.a addrtoname.o addrtostr.o af.o ascii_strcasecmp.o checksum.o cpack.o gmpls.o in_cksum.o ipproto.o l2vpn.o netdissect.o netdissect-alloc.o nlpid.o ntp.o oui.o parsenfsfh.o print.o print-802_11.o print-802_15_4.o print-ah.o print-ahcp.o print-aodv.o print-aoe.o print-ap1394.o print-arcnet.o print-arista.o print-arp.o print-ascii.o print-atalk.o print-atm.o print-babel.o print-bcm-li.o print-beep.o print-bfd.o print-bgp.o print-bootp.o print-brcmtag.o print-bt.o print-calm-fast.o print-carp.o print-cdp.o print-cfm.o print-chdlc.o print-cip.o print-cnfp.o print-dccp.o print-decnet.o print-dhcp6.o print-domain.o print-dsa.o print-dtp.o print-dvmrp.o print-eap.o print-egp.o print-eigrp.o print-enc.o print-erspan.o print-esp.o print-ether.o print-fddi.o print-forces.o print-fr.o print-frag6.o print-ftp.o print-geneve.o print-geonet.o print-gre.o print-hncp.o print-hsrp.o print-http.o print-icmp.o print-icmp6.o print-igmp.o print-igrp.o print-ip-demux.o print-ip.o print-ip6.o print-ip6opts.o print-ipcomp.o print-ipfc.o print-ipnet.o print-ipoib.o print-ipx.o print-isakmp.o print-isoclns.o print-juniper.o print-krb.o print-l2tp.o print-lane.o print-ldp.o print-lisp.o print-llc.o print-lldp.o print-lmp.o print-loopback.o print-lspping.o print-lwapp.o print-lwres.o print-m3ua.o print-macsec.o print-mobile.o print-mobility.o print-mpcp.o print-mpls.o print-mptcp.o print-msdp.o print-msnlb.o print-nflog.o print-nfs.o print-nhrp.o print-nsh.o print-ntp.o print-null.o print-olsr.o print-openflow-1.0.o print-openflow-1.3.o print-openflow.o print-ospf.o print-ospf6.o print-otv.o print-pflog.o print-pgm.o print-pim.o print-pktap.o print-ppi.o print-ppp.o print-pppoe.o print-pptp.o print-ptp.o print-quic.o print-radius.o print-raw.o print-realtek.o print-resp.o print-rip.o print-ripng.o print-rpki-rtr.o print-rsvp.o print-rt6.o print-rtsp.o print-rx.o print-sctp.o print-sflow.o print-sip.o print-sl.o print-sll.o print-slow.o print-smtp.o print-snmp.o print-someip.o print-ssh.o print-stp.o print-sunatm.o print-sunrpc.o print-symantec.o print-syslog.o print-tcp.o print-telnet.o print-tftp.o print-timed.o print-tipc.o print-token.o print-udld.o print-udp.o print-unsupported.o print-usb.o print-vjc.o print-vqp.o print-vrrp.o print-vsock.o print-vtp.o print-vxlan-gpe.o print-vxlan.o print-wb.o print-whois.o print-zep.o print-zephyr.o print-zeromq.o signature.o strtoaddr.o util-print.o  strlcat.o strlcpy.o
ranlib libnetdissect.a
make[1]: Leaving directory '/app/no_perf/tcpdump-5.0.0'
cc -g ./agent/devname.o ./agent/err.o ./agent/escape.o ./agent/info.o ./agent/meminfo.o ./agent/namespace.o ./agent/numa.o ./agent/pids.o ./agent/pwcache.o ./agent/readproc.o ./agent/send.o ./agent/send_data.o ./agent/stat.o ./agent/sysinfo.o ./agent/thpool.o ./agent/top.o ./agent/top_nls.o ./agent/uptime.o ./agent/version.o ./agent/wchan.o  ./sar/act_sadc.o ./sar/sadc.o ./sar/pr_stats.o  ./sar/sa_wrap.o ./sar/sa_common.o ./sar/network_security.o ./sar/librdstats.a ./sar/librdsensors.a ./sar/libsyscom.a  ./tcpdump-5.0.0/fptype.o ./tcpdump-5.0.0/tcpdump.o ./tcpdump-5.0.0/libnetdissect.a ./libpcap/libpcap.a -o send_data -lm -lpthread -ldl -lrt -lcrypto
make -C ./sar
make[1]: Entering directory '/app/no_perf/sar'
ar rv librdsensors.a rd_sensors.o
r - rd_sensors.o
make[1]: Leaving directory '/app/no_perf/sar'
make -C ./libpcap
make[1]: Entering directory '/app/no_perf/libpcap'
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-linux.o pcap-linux.c
pcap-linux.c: In function ‘pcap_wait_for_frames_mmap’:
pcap-linux.c:3666:25: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
 3666 |                         write_log("pcap_wait_for_frames_mmap: POSIX定时器信号触发(循环开始)，1秒时间到\n");
      |                         ^~~~~~~~~
pcap-linux.c: In function ‘pcap_read_linux_mmap_v2’:
pcap-linux.c:4457:24: warning: implicit declaration of function ‘get_optimal_timer_fd’ [-Wimplicit-function-declaration]
 4457 |         int timer_fd = get_optimal_timer_fd();  // 获取v2函数的定时器
      |                        ^~~~~~~~~~~~~~~~~~~~
pcap-linux.c: At top level:
pcap-linux.c:6360:13: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
 6360 | extern void write_log(const char *format, ...);
      |             ^~~~~~~~~
pcap-linux.c:3666:25: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
 3666 |                         write_log("pcap_wait_for_frames_mmap: POSIX定时器信号触发(循环开始)，1秒时间到\n");
      |                         ^~~~~~~~~
ar rc libpcap.a pcap-linux.o fad-getad.o pcap-usb-linux.o pcap-netfilter-linux.o pcap.o gencode.o optimize.o nametoaddr.o etherent.o fmtutils.o pcap-util.o savefile.o sf-pcap.o sf-pcapng.o pcap-common.o bpf_image.o bpf_filter.o bpf_dump.o scanner.o grammar.o strlcat.o strlcpy.o 
ranlib libpcap.a
VER=`cat ./VERSION`; \
MAJOR_VER=`sed 's/\([0-9][0-9]*\)\..*/\1/' ./VERSION`; \
gcc  -shared -Wl,-soname,libpcap.so.$MAJOR_VER \
    -o libpcap.so.$VER pcap-linux.o fad-getad.o pcap-usb-linux.o pcap-netfilter-linux.o pcap.o gencode.o optimize.o nametoaddr.o etherent.o fmtutils.o pcap-util.o savefile.o sf-pcap.o sf-pcapng.o pcap-common.o bpf_image.o bpf_filter.o bpf_dump.o scanner.o grammar.o strlcat.o strlcpy.o  
make[1]: Leaving directory '/app/no_perf/libpcap'
make -C ./tcpdump-5.0.0 tcpdump.o fptype.o libnetdissect.a
make[1]: Entering directory '/app/no_perf/tcpdump-5.0.0'
make[1]: 'tcpdump.o' is up to date.
make[1]: 'fptype.o' is up to date.
make[1]: 'libnetdissect.a' is up to date.
make[1]: Leaving directory '/app/no_perf/tcpdump-5.0.0'
cc -g ./agent/devname.o ./agent/err.o ./agent/escape.o ./agent/info.o ./agent/meminfo.o ./agent/namespace.o ./agent/numa.o ./agent/pids.o ./agent/pwcache.o ./agent/readproc.o ./agent/send.o ./agent/send_data.o ./agent/stat.o ./agent/sysinfo.o ./agent/thpool.o ./agent/top.o ./agent/top_nls.o ./agent/uptime.o ./agent/version.o ./agent/wchan.o  ./sar/act_sadc.o ./sar/sadc.o ./sar/pr_stats.o  ./sar/sa_wrap.o ./sar/sa_common.o ./sar/network_security.o ./sar/librdstats.a ./sar/librdsensors.a ./sar/libsyscom.a  ./tcpdump-5.0.0/fptype.o ./tcpdump-5.0.0/tcpdump.o ./tcpdump-5.0.0/libnetdissect.a ./libpcap/libpcap.a -o send_data -lm -lpthread -ldl -lrt -lcrypto
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/devname.o -MMD -MP -MF .deps/agent/devname.d -c agent/devname.c -o agent/devname.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/err.o -MMD -MP -MF .deps/agent/err.d -c agent/err.c -o agent/err.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/escape.o -MMD -MP -MF .deps/agent/escape.d -c agent/escape.c -o agent/escape.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/info.o -MMD -MP -MF .deps/agent/info.d -c agent/info.c -o agent/info.o
In file included from agent/info.c:545:
agent/pids.h:271:30: warning: ‘struct pids_info’ declared inside parameter list will not be visible outside of this definition or declaration
  271 | typedef void (*SET_t)(struct pids_info *, struct pids_result *, proc_t *);
      |                              ^~~~~~~~~
agent/info.c: In function ‘zap_fieldstab’:
agent/info.c:1455:10: warning: suggest braces around empty body in an ‘if’ statement [-Wempty-body]
 1455 |          ;
      |          ^
agent/info.c: In function ‘output_load’:
agent/info.c:1508:14: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 2 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |            ~~^
      |              |
      |              long unsigned int
      |            %u
agent/info.c:1508:20: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 3 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                ~~~~^
      |                    |
      |                    long unsigned int
      |                %02u
agent/info.c:1508:24: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 4 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                      ~~^
      |                        |
      |                        long unsigned int
      |                      %u
agent/info.c:1508:30: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 5 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                          ~~~~^
      |                              |
      |                              long unsigned int
      |                          %02u
agent/info.c:1508:34: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 6 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                                ~~^
      |                                  |
      |                                  long unsigned int
      |                                %u
agent/info.c:1508:40: warning: format ‘%lu’ expects argument of type ‘long unsigned int’, but argument 7 has type ‘int’ [-Wformat=]
 1508 |    printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
      |                                    ~~~~^
      |                                        |
      |                                        long unsigned int
      |                                    %02u
agent/info.c: In function ‘avg_load’:
agent/info.c:1562:10: warning: unused variable ‘g2’ [-Wunused-variable]
 1562 |    float g2 = round(loads3 / f + e);
      |          ^~
agent/info.c:1561:10: warning: unused variable ‘g1’ [-Wunused-variable]
 1561 |    float g1 = round(loads2 / f + e);
      |          ^~
agent/info.c: In function ‘meminfo’:
agent/info.c:2076:10: warning: unused variable ‘pct_misc’ [-Wunused-variable]
 2076 |    float pct_misc = (float)(kb_main_total - kb_main_available - kb_main_used) * (100.0 / (float)kb_main_total);
      |          ^~~~~~~~
agent/info.c: In function ‘before’:
agent/info.c:2094:8: warning: unused variable ‘linux_version_code’ [-Wunused-variable]
 2094 |    int linux_version_code = procps_linux_version();
      |        ^~~~~~~~~~~~~~~~~~
agent/info.c: In function ‘cpu_help’:
agent/info.c:2218:10: warning: unused variable ‘scale1’ [-Wunused-variable]
 2218 |    float scale1 = 0.0;
      |          ^~~~~~
agent/info.c: In function ‘tasks_refresh’:
agent/info.c:2491:8: warning: unused variable ‘i’ [-Wunused-variable]
 2491 |    int i, what;
      |        ^
agent/info.c:2488:15: warning: unused variable ‘n_alloc’ [-Wunused-variable]
 2488 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c: In function ‘tasks_refresh1’:
agent/info.c:2554:8: warning: unused variable ‘i’ [-Wunused-variable]
 2554 |    int i, what;
      |        ^
agent/info.c:2551:15: warning: unused variable ‘n_alloc’ [-Wunused-variable]
 2551 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c: In function ‘cal_cpu’:
agent/info.c:2765:24: warning: argument to ‘sizeof’ in ‘snprintf’ call is the same expression as the destination; did you mean to provide an explicit length? [-Wsizeof-pointer-memaccess]
 2765 |    snprintf(buf, sizeof(buf), "%#.1f", u);
      |                        ^
agent/info.c: In function ‘cal_mem’:
agent/info.c:2812:24: warning: argument to ‘sizeof’ in ‘snprintf’ call is the same expression as the destination; did you mean to provide an explicit length? [-Wsizeof-pointer-memaccess]
 2812 |    snprintf(buf, sizeof(buf), "%#.1f", m);
      |                        ^
agent/info.c: In function ‘sys’:
agent/info.c:3088:13: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3088 |             return;
      |             ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c:3254:11: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3254 |           return;
      |           ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c:3260:11: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3260 |           return;
      |           ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c:3269:11: warning: ‘return’ with no value, in function returning non-void [-Wreturn-type]
 3269 |           return;
      |           ^~~~~~
agent/info.c:2964:5: note: declared here
 2964 | int sys()
      |     ^~~
agent/info.c: In function ‘get_load’:
agent/info.c:1498:1: warning: control reaches end of non-void function [-Wreturn-type]
 1498 | }
      | ^
agent/info.c: In function ‘output_load’:
agent/info.c:1513:1: warning: control reaches end of non-void function [-Wreturn-type]
 1513 | }
      | ^
agent/info.c: In function ‘get_time1’:
agent/info.c:1533:1: warning: control reaches end of non-void function [-Wreturn-type]
 1533 | }
      | ^
agent/info.c: In function ‘avg_load’:
agent/info.c:1573:1: warning: control reaches end of non-void function [-Wreturn-type]
 1573 | }
      | ^
agent/info.c: In function ‘cpu_help’:
agent/info.c:2260:1: warning: control reaches end of non-void function [-Wreturn-type]
 2260 | }
      | ^
agent/info.c: In function ‘mem_help’:
agent/info.c:2349:1: warning: control reaches end of non-void function [-Wreturn-type]
 2349 | }
      | ^
agent/info.c: In function ‘init_cpu’:
agent/info.c:2620:1: warning: control reaches end of non-void function [-Wreturn-type]
 2620 | }
      | ^
agent/info.c: In function ‘init_parpids’:
agent/info.c:2850:1: warning: control reaches end of non-void function [-Wreturn-type]
 2850 | }
      | ^
agent/info.c: In function ‘init_thrpids’:
agent/info.c:2886:1: warning: control reaches end of non-void function [-Wreturn-type]
 2886 | }
      | ^
agent/info.c: In function ‘add_process_thread_count’:
agent/info.c:2896:1: warning: control reaches end of non-void function [-Wreturn-type]
 2896 | }
      | ^
agent/info.c: In function ‘add_thread_count’:
agent/info.c:2906:1: warning: control reaches end of non-void function [-Wreturn-type]
 2906 | }
      | ^
agent/info.c: At top level:
agent/info.c:2551:15: warning: ‘n_alloc’ defined but not used [-Wunused-variable]
 2551 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c:2488:15: warning: ‘n_alloc’ defined but not used [-Wunused-variable]
 2488 |    static int n_alloc = -1; // size of windows stacks arrays
      |               ^~~~~~~
agent/info.c:2948:13: warning: ‘wait_for_semaphore’ defined but not used [-Wunused-function]
 2948 | static void wait_for_semaphore(sem_t *sem) {
      |             ^~~~~~~~~~~~~~~~~~
agent/info.c:2664:13: warning: ‘forest_begin’ defined but not used [-Wunused-function]
 2664 | static void forest_begin(WIN_t *q)
      |             ^~~~~~~~~~~~
agent/info.c:2545:14: warning: ‘tasks_refresh1’ defined but not used [-Wunused-function]
 2545 | static void *tasks_refresh1(void *unused)
      |              ^~~~~~~~~~~~~~
agent/info.c:2358:13: warning: ‘wins_stage_1’ defined but not used [-Wunused-function]
 2358 | static void wins_stage_1(void)
      |             ^~~~~~~~~~~~
agent/info.c:1355:13: warning: ‘zap_fieldstab’ defined but not used [-Wunused-function]
 1355 | static void zap_fieldstab(void)
      |             ^~~~~~~~~~~~~
agent/info.c:1348:20: warning: ‘Cursor_state’ defined but not used [-Wunused-variable]
 1348 | static const char *Cursor_state = "";
      |                    ^~~~~~~~~~~~
agent/info.c:1265:13: warning: ‘win_names’ defined but not used [-Wunused-function]
 1265 | static void win_names(WIN_t *q, const char *name)
      |             ^~~~~~~~~
agent/info.c:842:13: warning: ‘Stdout_buf’ defined but not used [-Wunused-variable]
  842 | static char Stdout_buf[2048];
      |             ^~~~~~~~~~
agent/info.c:841:12: warning: ‘Ttychanged’ defined but not used [-Wunused-variable]
  841 | static int Ttychanged = 0;
      |            ^~~~~~~~~~
agent/info.c:840:5: warning: ‘Tty_raw’ defined but not used [-Wunused-variable]
  840 |     Tty_raw; // for unsolicited input
      |     ^~~~~~~
agent/info.c:836:23: warning: ‘Tty_original’ defined but not used [-Wunused-variable]
  836 | static struct termios Tty_original, // our inherited terminal definition
      |                       ^~~~~~~~~~~~
agent/info.c:826:15: warning: ‘Pseudo_size’ defined but not used [-Wunused-variable]
  826 | static size_t Pseudo_size;
      |               ^~~~~~~~~~~
agent/info.c:825:15: warning: ‘Bot_show_func’ defined but not used [-Wunused-variable]
  825 | static void (*Bot_show_func)(void);
      |               ^~~~~~~~~~~~~
agent/info.c:824:14: warning: ‘Bot_focus_func’ defined but not used [-Wunused-variable]
  824 | static BOT_f Bot_focus_func;
      |              ^~~~~~~~~~~~~~
agent/info.c:822:5: warning: ‘Bot_buf’ defined but not used [-Wunused-variable]
  822 |     Bot_buf[BOTBUFSIZ]; // the 'environ' can be huge
      |     ^~~~~~~
agent/info.c:821:6: warning: ‘Bot_head’ defined but not used [-Wunused-variable]
  821 |     *Bot_head,
      |      ^~~~~~~~
agent/info.c:820:13: warning: ‘Bot_sep’ defined but not used [-Wunused-variable]
  820 | static char Bot_sep,
      |             ^~~~~~~
agent/info.c:818:5: warning: ‘Bot_indx’ defined but not used [-Wunused-variable]
  818 |     Bot_indx = BOT_UNFOCUS,
      |     ^~~~~~~~
agent/info.c:817:5: warning: ‘Bot_rsvd’ defined but not used [-Wunused-variable]
  817 |     Bot_rsvd,
      |     ^~~~~~~~
agent/info.c:816:5: warning: ‘Bot_what’ defined but not used [-Wunused-variable]
  816 |     Bot_what,
      |     ^~~~~~~~
agent/info.c:815:12: warning: ‘Bot_task’ defined but not used [-Wunused-variable]
  815 | static int Bot_task,
      |            ^~~~~~~~
agent/info.c:787:12: warning: ‘Cap_can_goto’ defined but not used [-Wunused-variable]
  787 | static int Cap_can_goto = 0;
      |            ^~~~~~~~~~~~
agent/info.c:785:12: warning: ‘Cap_avoid_eol’ defined but not used [-Wunused-variable]
  785 | static int Cap_avoid_eol = 0;
      |            ^~~~~~~~~~~~~
agent/info.c:782:13: warning: ‘Cap_smam’ defined but not used [-Wunused-variable]
  782 |             Cap_smam[CAPBUFSIZ] = "";
      |             ^~~~~~~~
agent/info.c:781:13: warning: ‘Cap_rmam’ defined but not used [-Wunused-variable]
  781 | static char Cap_rmam[CAPBUFSIZ] = "",
      |             ^~~~~~~~
agent/info.c:779:13: warning: ‘Caps_endline’ defined but not used [-Wunused-variable]
  779 |             Caps_endline[SMLBUFSIZ] = "";
      |             ^~~~~~~~~~~~
agent/info.c:778:13: warning: ‘Caps_off’ defined but not used [-Wunused-variable]
  778 |             Caps_off[CAPBUFSIZ] = "",
      |             ^~~~~~~~
agent/info.c:777:13: warning: ‘Cap_reverse’ defined but not used [-Wunused-variable]
  777 |             Cap_reverse[CAPBUFSIZ] = "",
      |             ^~~~~~~~~~~
agent/info.c:775:13: warning: ‘Cap_home’ defined but not used [-Wunused-variable]
  775 |             Cap_home[CAPBUFSIZ] = "",
      |             ^~~~~~~~
agent/info.c:774:5: warning: ‘Cap_clr_eos’ defined but not used [-Wunused-variable]
  774 |     Cap_clr_eos[CAPBUFSIZ] = "",
      |     ^~~~~~~~~~~
agent/info.c:773:5: warning: ‘Cap_curs_hide’ defined but not used [-Wunused-variable]
  773 |     Cap_curs_hide[CAPBUFSIZ] = "",       // batch requirements!
      |     ^~~~~~~~~~~~~
agent/info.c:772:5: warning: ‘Cap_curs_huge’ defined but not used [-Wunused-variable]
  772 |     Cap_curs_huge[CAPBUFSIZ] = "",       // to remind people of those
      |     ^~~~~~~~~~~~~
agent/info.c:771:5: warning: ‘Cap_curs_norm’ defined but not used [-Wunused-variable]
  771 |     Cap_curs_norm[CAPBUFSIZ] = "",       // cost nothing but DO serve
      |     ^~~~~~~~~~~~~
agent/info.c:770:5: warning: ‘Cap_clr_scr’ defined but not used [-Wunused-variable]
  770 |     Cap_clr_scr[CAPBUFSIZ] = "",         // the assignments used here
      |     ^~~~~~~~~~~
agent/info.c:769:5: warning: ‘Cap_nl_clreos’ defined but not used [-Wunused-variable]
  769 |     Cap_nl_clreos[CAPBUFSIZ] = "",       // are initialized to zeros!
      |     ^~~~~~~~~~~~~
agent/info.c:768:13: warning: ‘Cap_clr_eol’ defined but not used [-Wunused-variable]
  768 | static char Cap_clr_eol[CAPBUFSIZ] = "", // global and/or static vars
      |             ^~~~~~~~~~~
agent/info.c:757:14: warning: ‘Myname’ defined but not used [-Wunused-variable]
  757 | static char *Myname;
      |              ^~~~~~
agent/info.c:604:17: warning: ‘Sigwinch_set’ defined but not used [-Wunused-variable]
  604 | static sigset_t Sigwinch_set;
      |                 ^~~~~~~~~~~~
agent/info.c:595:26: warning: ‘Pids_ctx1’ defined but not used [-Wunused-variable]
  595 | static struct pids_info *Pids_ctx1;
      |                          ^~~~~~~~~
agent/info.c:586:5: warning: ‘Width_mode’ defined but not used [-Wunused-variable]
  586 |     Width_mode = 0,   // set w/ 'w' - potential output override
      |     ^~~~~~~~~~
agent/info.c:585:5: warning: ‘Secure_mode’ defined but not used [-Wunused-variable]
  585 |     Secure_mode = 0,  // set if some functionality restricted
      |     ^~~~~~~~~~~
agent/info.c:584:5: warning: ‘Loops’ defined but not used [-Wunused-variable]
  584 |     Loops = -1,       // number of iterations, -1 loops forever
      |     ^~~~~
agent/info.c:583:12: warning: ‘Batch’ defined but not used [-Wunused-variable]
  583 | static int Batch = 0, // batch mode, collect no input, dumb output
      |            ^~~~~
agent/info.c:576:13: warning: ‘Adjoin_sp’ defined but not used [-Wunused-variable]
  576 | static char Adjoin_sp[] = " ~6 ~1";
      |             ^~~~~~~~~
agent/info.c:556:38: warning: ‘Max_lines’ defined but not used [-Wunused-variable]
  556 | static int Screen_cols, Screen_rows, Max_lines;
      |                                      ^~~~~~~~~
agent/info.c:556:25: warning: ‘Screen_rows’ defined but not used [-Wunused-variable]
  556 | static int Screen_cols, Screen_rows, Max_lines;
      |                         ^~~~~~~~~~~
agent/info.c:554:14: warning: ‘Pseudo_screen’ defined but not used [-Wunused-variable]
  554 | static char *Pseudo_screen;
      |              ^~~~~~~~~~~~~
agent/info.c: In function ‘avg_load’:
agent/info.c:1565:33: warning: ‘%d’ directive output may be truncated writing between 1 and 8 bytes into a region of size 7 [-Wformat-truncation=]
 1565 |    snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
      |                                 ^~
agent/info.c:1565:32: note: directive argument in the range [-1048576, 1048575]
 1565 |    snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
      |                                ^~~~~~~~
agent/info.c:1565:32: note: directive argument in the range [0, 99]
agent/info.c:1565:4: note: ‘snprintf’ output between 4 and 12 bytes into a destination of size 7
 1565 |    snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
      |    ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/meminfo.o -MMD -MP -MF .deps/agent/meminfo.d -c agent/meminfo.c -o agent/meminfo.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/namespace.o -MMD -MP -MF .deps/agent/namespace.d -c agent/namespace.c -o agent/namespace.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/numa.o -MMD -MP -MF .deps/agent/numa.d -c agent/numa.c -o agent/numa.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/pids.o -MMD -MP -MF .deps/agent/pids.d -c agent/pids.c -o agent/pids.o
In file included from agent/pids.c:44:
agent/pids.h:271:30: warning: ‘struct pids_info’ declared inside parameter list will not be visible outside of this definition or declaration
  271 | typedef void (*SET_t)(struct pids_info *, struct pids_result *, proc_t *);
      |                              ^~~~~~~~~
agent/pids.c: In function ‘free_pids_str’:
agent/pids.c:135:47: warning: unused parameter ‘R’ [-Wunused-parameter]
  135 | static void freNAME(str) (struct pids_result *R) {
      |                           ~~~~~~~~~~~~~~~~~~~~^
agent/pids.c: In function ‘free_pids_strv’:
agent/pids.c:139:48: warning: unused parameter ‘R’ [-Wunused-parameter]
  139 | static void freNAME(strv) (struct pids_result *R) {
      |                            ~~~~~~~~~~~~~~~~~~~~^
agent/pids.c: In function ‘pids_assign_results’:
agent/pids.c:990:17: warning: passing argument 1 of ‘*that’ from incompatible pointer type [-Wincompatible-pointer-types]
  990 |         (*that)(info, this, p);
      |                 ^~~~
      |                 |
      |                 struct pids_info *
agent/pids.c:990:17: note: expected ‘struct pids_info *’ but argument is of type ‘struct pids_info *’
agent/pids.c: In function ‘pids_cleanup_stacks_all’:
agent/pids.c:1023:13: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1023 |         ext = ext->next;
      |             ^
agent/pids.c: In function ‘pids_itemize_stacks_all’:
agent/pids.c:1082:13: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1082 |         ext = ext->next;
      |             ^
agent/pids.c: In function ‘pids_stacks_alloc’:
agent/pids.c:1315:18: warning: assignment to ‘struct stacks_extent *’ from incompatible pointer type ‘struct stacks_extent2 *’ [-Wincompatible-pointer-types]
 1315 |     p_blob->next = info->extents;                              // push this extent onto... |
      |                  ^
agent/pids.c:1330:12: warning: returning ‘struct stacks_extent2 *’ from a function with incompatible return type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1330 |     return p_blob;
      |            ^~~~~~
agent/pids.c: In function ‘pids_stacks_fetch’:
agent/pids.c:1367:19: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1367 |         if (!(ext = pids_stacks_alloc(info, STACKS_INIT)))
      |                   ^
agent/pids.c:1381:23: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1381 |             || (!(ext = pids_stacks_alloc(info, STACKS_GROW))))
      |                       ^
agent/pids.c: In function ‘pids_stacks_fetch1’:
agent/pids.c:1431:19: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1431 |         if (!(ext = pids_stacks_alloc(info, STACKS_INIT)))
      |                   ^
agent/pids.c:1445:23: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1445 |             || (!(ext = pids_stacks_alloc(info, STACKS_GROW))))
      |                       ^
agent/pids.c: In function ‘procps_pids_unref’:
agent/pids.c:1626:34: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1626 |                 (*info)->extents = (*info)->extents->next;
      |                                  ^
agent/pids.c:1633:25: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1633 |                 nextext = ext->next;
      |                         ^
agent/pids.c: In function ‘procps_pids_get’:
agent/pids.c:1710:29: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1710 |         if (!(info->get_ext = pids_stacks_alloc(info, 1)))
      |                             ^
agent/pids.c: In function ‘procps_pids_reset’:
agent/pids.c:1844:27: warning: assignment to ‘struct stacks_extent2 *’ from incompatible pointer type ‘struct stacks_extent *’ [-Wincompatible-pointer-types]
 1844 |             info->extents = p->next;
      |                           ^
agent/pids.c: In function ‘procps_pids_sort’:
agent/pids.c:1947:23: warning: variable ‘parms’ set but not used [-Wunused-but-set-variable]
 1947 |     struct sort_parms parms;
      |                       ^~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/pwcache.o -MMD -MP -MF .deps/agent/pwcache.d -c agent/pwcache.c -o agent/pwcache.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/readproc.o -MMD -MP -MF .deps/agent/readproc.d -c agent/readproc.c -o agent/readproc.o
agent/readproc.c: In function ‘stat2proc’:
agent/readproc.c:597:16: warning: unused variable ‘le’ [-Wunused-variable]
  597 |         size_t le = strlen(buf) + 1;
      |                ^~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/send.o -MMD -MP -MF .deps/agent/send.d -c agent/send.c -o agent/send.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/send_data.o -MMD -MP -MF .deps/agent/send_data.d -c agent/send_data.c -o agent/send_data.o
In file included from agent/send_data.c:50:
agent/pids.h:271:30: warning: ‘struct pids_info’ declared inside parameter list will not be visible outside of this definition or declaration
  271 | typedef void (*SET_t)(struct pids_info *, struct pids_result *, proc_t *);
      |                              ^~~~~~~~~
agent/send_data.c: In function ‘sigint_handler’:
agent/send_data.c:263:9: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
  263 |         write_log("ctrl-c caught ....\n");
      |         ^~~~~~~~~
agent/send_data.c:260:25: warning: unused parameter ‘sig’ [-Wunused-parameter]
  260 | void sigint_handler(int sig)
      |                     ~~~~^~~
agent/send_data.c: In function ‘stat2proc’:
agent/send_data.c:365:25: warning: implicit declaration of function ‘escape_str’ [-Wimplicit-function-declaration]
  365 |                         escape_str(buf, raw, sizeof(buf));
      |                         ^~~~~~~~~~
agent/send_data.c:416:9: warning: implicit declaration of function ‘LEAVE’ [-Wimplicit-function-declaration]
  416 |         LEAVE(0x160);
      |         ^~~~~
agent/send_data.c: In function ‘check_self’:
agent/send_data.c:462:24: warning: missing initializer for field ‘siz’ of ‘struct utlbuf_s’ [-Wmissing-field-initializers]
  462 |                 struct utlbuf_s ub = {NULL, 0};
      |                        ^~~~~~~~
In file included from agent/send_data.c:11:
agent/readproc.h:42:11: note: ‘siz’ declared here
   42 |     int   siz;     // current len of the above
      |           ^~~
agent/send_data.c:463:21: warning: variable ‘rc’ set but not used [-Wunused-but-set-variable]
  463 |                 int rc = 0;
      |                     ^~
agent/send_data.c: In function ‘alarm_handler1’:
agent/send_data.c:1191:25: warning: unused parameter ‘sig’ [-Wunused-parameter]
 1191 | void alarm_handler1(int sig)
      |                     ~~~~^~~
agent/send_data.c: At top level:
agent/send_data.c:1574:6: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
 1574 | void write_log(const char *format, ...)
      |      ^~~~~~~~~
agent/send_data.c:263:9: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
  263 |         write_log("ctrl-c caught ....\n");
      |         ^~~~~~~~~
agent/send_data.c: In function ‘main’:
agent/send_data.c:1840:46: warning: passing argument 3 of ‘pthread_create’ from incompatible pointer type [-Wincompatible-pointer-types]
 1840 |                 pthread_create(&sar1, &attr, &sadc1, NULL);
      |                                              ^~~~~~
      |                                              |
      |                                              int (*)()
In file included from agent/top.h:23,
                 from agent/send_data.c:5:
/usr/include/pthread.h:204:36: note: expected ‘void * (*)(void *)’ but argument is of type ‘int (*)()’
  204 |                            void *(*__start_routine) (void *),
      |                            ~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~
agent/send_data.c:1842:50: warning: passing argument 3 of ‘pthread_create’ from incompatible pointer type [-Wincompatible-pointer-types]
 1842 |                 pthread_create(&tcpdump1, &attr, &tcp1, argv[1]);
      |                                                  ^~~~~
      |                                                  |
      |                                                  int (*)(char *)
/usr/include/pthread.h:204:36: note: expected ‘void * (*)(void *)’ but argument is of type ‘int (*)(char *)’
  204 |                            void *(*__start_routine) (void *),
      |                            ~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~
agent/send_data.c:2053:33: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2053 |         for (int isar = 0; isar < os->int_count; isar++) {
      |                                 ^
agent/send_data.c:2064:33: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2064 |         for (int isar = 0; isar < os->enet_count; isar++) {
      |                                 ^
agent/send_data.c:2085:35: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2085 |         for (int idisk = 0; idisk < os->disk_count; idisk++) {
      |                                   ^
agent/send_data.c:2099:31: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2099 |         for (int ifs = 0; ifs < os->file_count; ifs++) {
      |                               ^
agent/send_data.c:2124:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2124 |         for (tcd = 0; tcd < os->group_s; tcd++)
      |                           ^
agent/send_data.c:2141:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2141 |         for (tcd = 0; tcd < os->group_s1; tcd++)
      |                           ^
agent/send_data.c:2159:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2159 |         for (tcd = 0; tcd < os->group_p; tcd++)
      |                           ^
agent/send_data.c:2196:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2196 |         for (tcd = 0; tcd < os->group_f; tcd++)
      |                           ^
agent/send_data.c:2214:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2214 |         for (tcd = 0; tcd < os->group_r; tcd++)
      |                           ^
agent/send_data.c:2232:27: warning: comparison of integer expressions of different signedness: ‘int’ and ‘unsigned int’ [-Wsign-compare]
 2232 |         for (tcd = 0; tcd < os->group_ack; tcd++)
      |                           ^
agent/send_data.c:1756:19: warning: unused variable ‘th’ [-Wunused-variable]
 1756 |         pthread_t th;
      |                   ^~
agent/send_data.c:1723:19: warning: unused variable ‘th1’ [-Wunused-variable]
 1723 |         pthread_t th1;
      |                   ^~~
agent/send_data.c: In function ‘c_tcp1’:
agent/send_data.c:1246:1: warning: control reaches end of non-void function [-Wreturn-type]
 1246 | }
      | ^
agent/send_data.c: In function ‘free_all1’:
agent/send_data.c:1552:1: warning: control reaches end of non-void function [-Wreturn-type]
 1552 | }
      | ^
agent/top.h: At top level:
agent/top.h:104:23: warning: ‘Stat_items’ defined but not used [-Wunused-variable]
  104 | static enum stat_item Stat_items[] = {
      |                       ^~~~~~~~~~
agent/top.h:60:13: warning: ‘UTF8_tab’ defined but not used [-Wunused-variable]
   60 | static char UTF8_tab[] = {
      |             ^~~~~~~~
agent/top.h:56:14: warning: ‘Frame_etscale’ defined but not used [-Wunused-variable]
   56 | static float Frame_etscale;
      |              ^~~~~~~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/stat.o -MMD -MP -MF .deps/agent/stat.d -c agent/stat.c -o agent/stat.o
agent/stat.c: In function ‘procps_stat_reap’:
agent/stat.c:1239:9: warning: unused variable ‘ff’ [-Wunused-variable]
 1239 |     int ff;
      |         ^~
agent/stat.c: In function ‘sum_tics’:
agent/stat.c:1425:10: warning: variable ‘scale’ set but not used [-Wunused-but-set-variable]
 1425 |    float scale;
      |          ^~~~~
agent/stat.c:1424:18: warning: unused variable ‘rx’ [-Wunused-variable]
 1424 |    struct rx_st *rx;
      |                  ^~
agent/stat.c:1423:10: warning: variable ‘idl_frme’ set but not used [-Wunused-but-set-variable]
 1423 |    SIC_t idl_frme, tot_frme;
      |          ^~~~~~~~
agent/stat.c:1433:1: warning: no return statement in function returning non-void [-Wreturn-type]
 1433 | } // end: sum_tics
      | ^
agent/stat.c:1419:41: warning: unused parameter ‘this’ [-Wunused-parameter]
 1419 | static int sum_tics (struct stat_stack *this) {
      |                      ~~~~~~~~~~~~~~~~~~~^~~~
agent/stat.c: At top level:
agent/stat.c:1419:12: warning: ‘sum_tics’ defined but not used [-Wunused-function]
 1419 | static int sum_tics (struct stat_stack *this) {
      |            ^~~~~~~~
agent/stat.c:44:20: warning: ‘Cpu_States_fmts’ defined but not used [-Wunused-variable]
   44 | static const char *Cpu_States_fmts;
      |                    ^~~~~~~~~~~~~~~
agent/stat.c:43:20: warning: ‘Cpu_pmax’ defined but not used [-Wunused-variable]
   43 | static float       Cpu_pmax;
      |                    ^~~~~~~~
agent/stat.c:42:20: warning: ‘Hertz’ defined but not used [-Wunused-variable]
   42 | static long        Hertz;
      |                    ^~~~~
In file included from agent/stat.c:36:
agent/top.h:104:23: warning: ‘Stat_items’ defined but not used [-Wunused-variable]
  104 | static enum stat_item Stat_items[] = {
      |                       ^~~~~~~~~~
agent/top.h:60:13: warning: ‘UTF8_tab’ defined but not used [-Wunused-variable]
   60 | static char UTF8_tab[] = {
      |             ^~~~~~~~
agent/top.h:56:14: warning: ‘Frame_etscale’ defined but not used [-Wunused-variable]
   56 | static float Frame_etscale;
      |              ^~~~~~~~~~~~~
agent/stat.c: In function ‘sum_tics’:
agent/stat.c:1430:7: warning: ‘tot_frme’ is used uninitialized [-Wuninitialized]
 1430 |    if (1 > tot_frme) idl_frme = tot_frme = 1;
      |       ^
agent/stat.c:1423:20: note: ‘tot_frme’ was declared here
 1423 |    SIC_t idl_frme, tot_frme;
      |                    ^~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/sysinfo.o -MMD -MP -MF .deps/agent/sysinfo.d -c agent/sysinfo.c -o agent/sysinfo.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/thpool.o -MMD -MP -MF .deps/agent/thpool.d -c agent/thpool.c -o agent/thpool.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/top.o -MMD -MP -MF .deps/agent/top.d -c agent/top.c -o agent/top.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/top_nls.o -MMD -MP -MF .deps/agent/top_nls.d -c agent/top_nls.c -o agent/top_nls.o
In file included from agent/top_nls.c:24:
agent/top.h: In function ‘utf8_cols’:
agent/top.h:89:23: warning: implicit declaration of function ‘mbtowc’; did you mean ‘mbrtowc’? [-Wimplicit-function-declaration]
   89 |                 (void)mbtowc(&wc, (const char *)p, n);
      |                       ^~~~~~
      |                       mbrtowc
agent/top.h: At top level:
agent/top.h:104:23: warning: ‘Stat_items’ defined but not used [-Wunused-variable]
  104 | static enum stat_item Stat_items[] = {
      |                       ^~~~~~~~~~
agent/top.h:60:13: warning: ‘UTF8_tab’ defined but not used [-Wunused-variable]
   60 | static char UTF8_tab[] = {
      |             ^~~~~~~~
agent/top.h:56:14: warning: ‘Frame_etscale’ defined but not used [-Wunused-variable]
   56 | static float Frame_etscale;
      |              ^~~~~~~~~~~~~
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/uptime.o -MMD -MP -MF .deps/agent/uptime.d -c agent/uptime.c -o agent/uptime.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/version.o -MMD -MP -MF .deps/agent/version.d -c agent/version.c -o agent/version.o
cc -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap -MT agent/wchan.o -MMD -MP -MF .deps/agent/wchan.d -c agent/wchan.c -o agent/wchan.o
make -C ./sar
make[1]: Entering directory '/app/no_perf/sar'
gcc -o act_sadc.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SADC -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  activity.c
gcc -o sa_wrap.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP sa_wrap.c
gcc -o sa_common.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP sa_common.c
gcc -o rd_stats.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SADC -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  rd_stats.c
rd_stats.c: In function ‘read_filesystem’:
rd_stats.c:2361:46: warning: passing argument 2 of ‘statvfs’ from incompatible pointer type [-Wincompatible-pointer-types]
 2361 |                         if ((statvfs(mountp, &buf) < 0) || (!buf.f_blocks))
      |                                              ^~~~
      |                                              |
      |                                              struct statfs *
In file included from rd_stats.c:31:
/usr/include/x86_64-linux-gnu/sys/statvfs.h:52:48: note: expected ‘struct statvfs * restrict’ but argument is of type ‘struct statfs *’
   52 |                     struct statvfs *__restrict __buf)
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~
gcc -o count.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SADC -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  count.c
ar rvs librdstats.a rd_stats.o count.o
r - rd_stats.o
r - count.o
gcc -o rd_sensors.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP rd_sensors.c
ar rv librdsensors.a rd_sensors.o
r - rd_sensors.o
gcc -o sadc.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP sadc.c
In file included from sadc.c:86:
../agent/top.h: In function ‘utf8_cols’:
../agent/top.h:91:26: warning: implicit declaration of function ‘wcwidth’ [-Wimplicit-function-declaration]
   91 |                 if ((n = wcwidth(wc)) < 0)
      |                          ^~~~~~~
gcc -o act_sar.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSOURCE_SAR -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  activity.c
gcc -o public.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP public.c
public.c: In function ‘update_sar_lock1’:
public.c:8:1: warning: control reaches end of non-void function [-Wreturn-type]
    8 | }
      | ^
gcc -o pr_stats.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP pr_stats.c
In file included from pr_stats.c:33:
../agent/top.h: In function ‘utf8_cols’:
../agent/top.h:91:26: warning: implicit declaration of function ‘wcwidth’ [-Wimplicit-function-declaration]
   91 |                 if ((n = wcwidth(wc)) < 0)
      |                          ^~~~~~~
gcc -o network_security.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP network_security.c
network_security.c: In function ‘build_socket_process_map’:
network_security.c:88:55: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size between 236 and 245 [-Wformat-truncation=]
   88 |             snprintf(path, sizeof(path), "/proc/%d/fd/%s", pid, fd_entry->d_name);
      |                                                       ^~
network_security.c:88:13: note: ‘snprintf’ output between 12 and 276 bytes into a destination of size 256
   88 |             snprintf(path, sizeof(path), "/proc/%d/fd/%s", pid, fd_entry->d_name);
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
gcc -o iostat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP iostat.c
iostat.c: In function ‘read_sysfs_dlist_part_stat’:
iostat.c:565:53: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size between 0 and 1023 [-Wformat-truncation=]
  565 |                 snprintf(filename, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                                                     ^~
iostat.c:565:17: note: ‘snprintf’ output between 7 and 1285 bytes into a destination of size 1024
  565 |                 snprintf(filename, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
gcc -o rd_stats_light.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  rd_stats.c
rd_stats.c: In function ‘read_filesystem’:
rd_stats.c:2361:46: warning: passing argument 2 of ‘statvfs’ from incompatible pointer type [-Wincompatible-pointer-types]
 2361 |                         if ((statvfs(mountp, &buf) < 0) || (!buf.f_blocks))
      |                                              ^~~~
      |                                              |
      |                                              struct statfs *
In file included from rd_stats.c:31:
/usr/include/x86_64-linux-gnu/sys/statvfs.h:52:48: note: expected ‘struct statvfs * restrict’ but argument is of type ‘struct statfs *’
   52 |                     struct statvfs *__restrict __buf)
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~
gcc -o count_light.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  count.c
ar rvs librdstats_light.a rd_stats_light.o count_light.o
r - rd_stats_light.o
r - count_light.o
gcc -o common.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP common.c
common.c: In function ‘get_dev_part_nr’:
common.c:214:49: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size between 0 and 1023 [-Wformat-truncation=]
  214 |                 snprintf(line, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                                                 ^~
common.c:214:17: note: ‘snprintf’ output between 7 and 1285 bytes into a destination of size 1024
  214 |                 snprintf(line, MAX_PF_NAME, "%s/%s/%s", dfile, drd->d_name, S_STAT);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
gcc -o ioconf.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP ioconf.c
ioconf.c: In function ‘ioc_init’:
ioconf.c:301:46: warning: ‘%s’ directive writing up to 31 bytes into a region of size 16 [-Wformat-overflow=]
  301 |                         sprintf(blkp->cfmt, "%s%s%%d", blkp->name, cfmt);
      |                                              ^~
ioconf.c:301:25: note: ‘sprintf’ output between 3 and 49 bytes into a destination of size 16
  301 |                         sprintf(blkp->cfmt, "%s%s%%d", blkp->name, cfmt);
      |                         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ioconf.c:322:42: warning: ‘d’ directive writing 1 byte into a region of size between 0 and 15 [-Wformat-overflow=]
  322 |                 sprintf(blkp->pfmt, "%s%%d", (*pfmt == '*') ? "" : pfmt);
      |                                          ^
ioconf.c:322:17: note: ‘sprintf’ output between 3 and 18 bytes into a destination of size 16
  322 |                 sprintf(blkp->pfmt, "%s%%d", (*pfmt == '*') ? "" : pfmt);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ioconf.c: In function ‘transform_devmapname’:
ioconf.c:497:51: warning: ‘%s’ directive output may be truncated writing up to 255 bytes into a region of size 244 [-Wformat-truncation=]
  497 |                 snprintf(filen, MAX_FILE_LEN, "%s/%s", DEVMAP_DIR, dp->d_name);
      |                                                   ^~
ioconf.c:497:17: note: ‘snprintf’ output between 13 and 268 bytes into a destination of size 256
  497 |                 snprintf(filen, MAX_FILE_LEN, "%s/%s", DEVMAP_DIR, dp->d_name);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ar rvs libsyscom.a common.o ioconf.o
r - common.o
r - ioconf.o
gcc -o iostat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context iostat.o librdstats_light.a libsyscom.a 
gcc -o mpstat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP mpstat.c
gcc -o mpstat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context mpstat.o librdstats_light.a libsyscom.a 
gcc -o pidstat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP pidstat.c
gcc -o pidstat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context pidstat.o librdstats_light.a libsyscom.a 
gcc -o nfsiostat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP nfsiostat.c
gcc -o nfsiostat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context nfsiostat.o librdstats_light.a libsyscom.a 
gcc -o cifsiostat.o -c -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context -DSA_DIR=\"/var/log/sa\" -DSADC_PATH=\"./sadc\"  -MMD -MP cifsiostat.c
gcc -o cifsiostat -g -Wall -pipe -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-int-in-bool-context cifsiostat.o librdstats_light.a libsyscom.a 
make[1]: Leaving directory '/app/no_perf/sar'
make -C ./libpcap
make[1]: Entering directory '/app/no_perf/libpcap'
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-linux.o pcap-linux.c
pcap-linux.c: In function ‘pcap_wait_for_frames_mmap’:
pcap-linux.c:3666:25: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
 3666 |                         write_log("pcap_wait_for_frames_mmap: POSIX定时器信号触发(循环开始)，1秒时间到\n");
      |                         ^~~~~~~~~
pcap-linux.c: In function ‘pcap_read_linux_mmap_v2’:
pcap-linux.c:4457:24: warning: implicit declaration of function ‘get_optimal_timer_fd’ [-Wimplicit-function-declaration]
 4457 |         int timer_fd = get_optimal_timer_fd();  // 获取v2函数的定时器
      |                        ^~~~~~~~~~~~~~~~~~~~
pcap-linux.c: At top level:
pcap-linux.c:6360:13: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
 6360 | extern void write_log(const char *format, ...);
      |             ^~~~~~~~~
pcap-linux.c:3666:25: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
 3666 |                         write_log("pcap_wait_for_frames_mmap: POSIX定时器信号触发(循环开始)，1秒时间到\n");
      |                         ^~~~~~~~~
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o fad-getad.o fad-getad.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-usb-linux.o pcap-usb-linux.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-netfilter-linux.o pcap-netfilter-linux.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap.o pcap.c
pcap.c: In function ‘posix_timer_handler’:
pcap.c:242:9: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
  242 |         write_log("POSIX定时器信号触发，设置pcap_timeout1=1\n");
      |         ^~~~~~~~~
pcap.c: At top level:
pcap.c:259:13: warning: conflicting types for ‘write_log’; have ‘void(const char *, ...)’
  259 | extern void write_log(const char *format, ...);
      |             ^~~~~~~~~
pcap.c:242:9: note: previous implicit declaration of ‘write_log’ with type ‘void(const char *, ...)’
  242 |         write_log("POSIX定时器信号触发，设置pcap_timeout1=1\n");
      |         ^~~~~~~~~
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c ./gencode.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o optimize.o optimize.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o nametoaddr.o nametoaddr.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o etherent.o etherent.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o fmtutils.o fmtutils.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-util.o pcap-util.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o savefile.o savefile.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o sf-pcap.o sf-pcap.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o sf-pcapng.o sf-pcapng.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o pcap-common.o pcap-common.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o bpf_image.o bpf_image.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o bpf_filter.o bpf_filter.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c -o bpf_dump.o bpf_dump.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c scanner.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -c grammar.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -o strlcat.o -c ./missing/strlcat.c
gcc -fvisibility=hidden  -fpic -I.  -DBUILDING_PCAP -Dpcap_EXPORTS -DHAVE_CONFIG_H  -g     -o strlcpy.o -c ./missing/strlcpy.c
ar rc libpcap.a pcap-linux.o fad-getad.o pcap-usb-linux.o pcap-netfilter-linux.o pcap.o gencode.o optimize.o nametoaddr.o etherent.o fmtutils.o pcap-util.o savefile.o sf-pcap.o sf-pcapng.o pcap-common.o bpf_image.o bpf_filter.o bpf_dump.o scanner.o grammar.o strlcat.o strlcpy.o 
ranlib libpcap.a
VER=`cat ./VERSION`; \
MAJOR_VER=`sed 's/\([0-9][0-9]*\)\..*/\1/' ./VERSION`; \
gcc  -shared -Wl,-soname,libpcap.so.$MAJOR_VER \
    -o libpcap.so.$VER pcap-linux.o fad-getad.o pcap-usb-linux.o pcap-netfilter-linux.o pcap.o gencode.o optimize.o nametoaddr.o etherent.o fmtutils.o pcap-util.o savefile.o sf-pcap.o sf-pcapng.o pcap-common.o bpf_image.o bpf_filter.o bpf_dump.o scanner.o grammar.o strlcat.o strlcpy.o  
make[1]: Leaving directory '/app/no_perf/libpcap'
make -C ./tcpdump-5.0.0 tcpdump.o fptype.o libnetdissect.a
make[1]: Entering directory '/app/no_perf/tcpdump-5.0.0'
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o tcpdump.o tcpdump.c
tcpdump.c: In function ‘init_barriers’:
tcpdump.c:384:17: warning: implicit declaration of function ‘write_log’ [-Wimplicit-function-declaration]
  384 |                 write_log("init_barriers: barriers已经初始化\n");
      |                 ^~~~~~~~~
tcpdump.c:391:13: warning: implicit declaration of function ‘efficient_barrier_init’; did you mean ‘pthread_barrier_init’? [-Wimplicit-function-declaration]
  391 |         if (efficient_barrier_init(&barrier_a_done, 3) != 0) {
      |             ^~~~~~~~~~~~~~~~~~~~~~
      |             pthread_barrier_init
tcpdump.c:399:17: warning: implicit declaration of function ‘efficient_barrier_destroy’; did you mean ‘pthread_barrier_destroy’? [-Wimplicit-function-declaration]
  399 |                 efficient_barrier_destroy(&barrier_a_done);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~
      |                 pthread_barrier_destroy
tcpdump.c: At top level:
tcpdump.c:3655:6: warning: conflicting types for ‘efficient_barrier_destroy’; have ‘void(efficient_barrier_t *)’
 3655 | void efficient_barrier_destroy(efficient_barrier_t *barrier)
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~
tcpdump.c:399:17: note: previous implicit declaration of ‘efficient_barrier_destroy’ with type ‘void(efficient_barrier_t *)’
  399 |                 efficient_barrier_destroy(&barrier_a_done);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o fptype.o fptype.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o addrtoname.o addrtoname.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o addrtostr.o addrtostr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o af.o af.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o ascii_strcasecmp.o ascii_strcasecmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o checksum.o checksum.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o cpack.o cpack.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o gmpls.o gmpls.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o in_cksum.o in_cksum.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o ipproto.o ipproto.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o l2vpn.o l2vpn.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o netdissect.o netdissect.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o netdissect-alloc.o netdissect-alloc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o nlpid.o nlpid.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o ntp.o ntp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o oui.o oui.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o parsenfsfh.o parsenfsfh.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print.o print.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-802_11.o print-802_11.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-802_15_4.o print-802_15_4.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ah.o print-ah.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ahcp.o print-ahcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-aodv.o print-aodv.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-aoe.o print-aoe.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ap1394.o print-ap1394.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-arcnet.o print-arcnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-arista.o print-arista.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-arp.o print-arp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ascii.o print-ascii.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-atalk.o print-atalk.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-atm.o print-atm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-babel.o print-babel.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bcm-li.o print-bcm-li.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-beep.o print-beep.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bfd.o print-bfd.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bgp.o print-bgp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bootp.o print-bootp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-brcmtag.o print-brcmtag.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-bt.o print-bt.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-calm-fast.o print-calm-fast.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-carp.o print-carp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cdp.o print-cdp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cfm.o print-cfm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-chdlc.o print-chdlc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cip.o print-cip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-cnfp.o print-cnfp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dccp.o print-dccp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-decnet.o print-decnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dhcp6.o print-dhcp6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-domain.o print-domain.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dsa.o print-dsa.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dtp.o print-dtp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-dvmrp.o print-dvmrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-eap.o print-eap.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-egp.o print-egp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-eigrp.o print-eigrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-enc.o print-enc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-erspan.o print-erspan.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-esp.o print-esp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ether.o print-ether.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-fddi.o print-fddi.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-forces.o print-forces.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-fr.o print-fr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-frag6.o print-frag6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ftp.o print-ftp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-geneve.o print-geneve.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-geonet.o print-geonet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-gre.o print-gre.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-hncp.o print-hncp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-hsrp.o print-hsrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-http.o print-http.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-icmp.o print-icmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-icmp6.o print-icmp6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-igmp.o print-igmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-igrp.o print-igrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip-demux.o print-ip-demux.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip.o print-ip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip6.o print-ip6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ip6opts.o print-ip6opts.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipcomp.o print-ipcomp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipfc.o print-ipfc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipnet.o print-ipnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipoib.o print-ipoib.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ipx.o print-ipx.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-isakmp.o print-isakmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-isoclns.o print-isoclns.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-juniper.o print-juniper.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-krb.o print-krb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-l2tp.o print-l2tp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lane.o print-lane.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ldp.o print-ldp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lisp.o print-lisp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-llc.o print-llc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lldp.o print-lldp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lmp.o print-lmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-loopback.o print-loopback.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lspping.o print-lspping.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lwapp.o print-lwapp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-lwres.o print-lwres.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-m3ua.o print-m3ua.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-macsec.o print-macsec.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mobile.o print-mobile.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mobility.o print-mobility.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mpcp.o print-mpcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mpls.o print-mpls.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-mptcp.o print-mptcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-msdp.o print-msdp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-msnlb.o print-msnlb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nflog.o print-nflog.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nfs.o print-nfs.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nhrp.o print-nhrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-nsh.o print-nsh.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ntp.o print-ntp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-null.o print-null.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-olsr.o print-olsr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-openflow-1.0.o print-openflow-1.0.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-openflow-1.3.o print-openflow-1.3.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-openflow.o print-openflow.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ospf.o print-ospf.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ospf6.o print-ospf6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-otv.o print-otv.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pflog.o print-pflog.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pgm.o print-pgm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pim.o print-pim.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pktap.o print-pktap.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ppi.o print-ppi.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ppp.o print-ppp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pppoe.o print-pppoe.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-pptp.o print-pptp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ptp.o print-ptp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-quic.o print-quic.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-radius.o print-radius.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-raw.o print-raw.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-realtek.o print-realtek.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-resp.o print-resp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rip.o print-rip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ripng.o print-ripng.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rpki-rtr.o print-rpki-rtr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rsvp.o print-rsvp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rt6.o print-rt6.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rtsp.o print-rtsp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-rx.o print-rx.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sctp.o print-sctp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sflow.o print-sflow.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sip.o print-sip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sl.o print-sl.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sll.o print-sll.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-slow.o print-slow.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-smtp.o print-smtp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-snmp.o print-snmp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-someip.o print-someip.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-ssh.o print-ssh.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-stp.o print-stp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sunatm.o print-sunatm.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-sunrpc.o print-sunrpc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-symantec.o print-symantec.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-syslog.o print-syslog.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-tcp.o print-tcp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-telnet.o print-telnet.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-tftp.o print-tftp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-timed.o print-timed.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-tipc.o print-tipc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-token.o print-token.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-udld.o print-udld.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-udp.o print-udp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-unsupported.o print-unsupported.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-usb.o print-usb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vjc.o print-vjc.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vqp.o print-vqp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vrrp.o print-vrrp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vsock.o print-vsock.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vtp.o print-vtp.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vxlan-gpe.o print-vxlan-gpe.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-vxlan.o print-vxlan.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-wb.o print-wb.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-whois.o print-whois.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-zep.o print-zep.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-zephyr.o print-zephyr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o print-zeromq.o print-zeromq.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o signature.o signature.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o strtoaddr.o strtoaddr.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -c -o util-print.o util-print.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -o strlcat.o -c ./missing/strlcat.c
gcc -I. -I../libpcap  -I/usr/local/include  -DHAVE_CONFIG_H   -g  -o strlcpy.o -c ./missing/strlcpy.c
ar cr libnetdissect.a addrtoname.o addrtostr.o af.o ascii_strcasecmp.o checksum.o cpack.o gmpls.o in_cksum.o ipproto.o l2vpn.o netdissect.o netdissect-alloc.o nlpid.o ntp.o oui.o parsenfsfh.o print.o print-802_11.o print-802_15_4.o print-ah.o print-ahcp.o print-aodv.o print-aoe.o print-ap1394.o print-arcnet.o print-arista.o print-arp.o print-ascii.o print-atalk.o print-atm.o print-babel.o print-bcm-li.o print-beep.o print-bfd.o print-bgp.o print-bootp.o print-brcmtag.o print-bt.o print-calm-fast.o print-carp.o print-cdp.o print-cfm.o print-chdlc.o print-cip.o print-cnfp.o print-dccp.o print-decnet.o print-dhcp6.o print-domain.o print-dsa.o print-dtp.o print-dvmrp.o print-eap.o print-egp.o print-eigrp.o print-enc.o print-erspan.o print-esp.o print-ether.o print-fddi.o print-forces.o print-fr.o print-frag6.o print-ftp.o print-geneve.o print-geonet.o print-gre.o print-hncp.o print-hsrp.o print-http.o print-icmp.o print-icmp6.o print-igmp.o print-igrp.o print-ip-demux.o print-ip.o print-ip6.o print-ip6opts.o print-ipcomp.o print-ipfc.o print-ipnet.o print-ipoib.o print-ipx.o print-isakmp.o print-isoclns.o print-juniper.o print-krb.o print-l2tp.o print-lane.o print-ldp.o print-lisp.o print-llc.o print-lldp.o print-lmp.o print-loopback.o print-lspping.o print-lwapp.o print-lwres.o print-m3ua.o print-macsec.o print-mobile.o print-mobility.o print-mpcp.o print-mpls.o print-mptcp.o print-msdp.o print-msnlb.o print-nflog.o print-nfs.o print-nhrp.o print-nsh.o print-ntp.o print-null.o print-olsr.o print-openflow-1.0.o print-openflow-1.3.o print-openflow.o print-ospf.o print-ospf6.o print-otv.o print-pflog.o print-pgm.o print-pim.o print-pktap.o print-ppi.o print-ppp.o print-pppoe.o print-pptp.o print-ptp.o print-quic.o print-radius.o print-raw.o print-realtek.o print-resp.o print-rip.o print-ripng.o print-rpki-rtr.o print-rsvp.o print-rt6.o print-rtsp.o print-rx.o print-sctp.o print-sflow.o print-sip.o print-sl.o print-sll.o print-slow.o print-smtp.o print-snmp.o print-someip.o print-ssh.o print-stp.o print-sunatm.o print-sunrpc.o print-symantec.o print-syslog.o print-tcp.o print-telnet.o print-tftp.o print-timed.o print-tipc.o print-token.o print-udld.o print-udp.o print-unsupported.o print-usb.o print-vjc.o print-vqp.o print-vrrp.o print-vsock.o print-vtp.o print-vxlan-gpe.o print-vxlan.o print-wb.o print-whois.o print-zep.o print-zephyr.o print-zeromq.o signature.o strtoaddr.o util-print.o  strlcat.o strlcpy.o
ranlib libnetdissect.a
make[1]: Leaving directory '/app/no_perf/tcpdump-5.0.0'
cc -g ./agent/devname.o ./agent/err.o ./agent/escape.o ./agent/info.o ./agent/meminfo.o ./agent/namespace.o ./agent/numa.o ./agent/pids.o ./agent/pwcache.o ./agent/readproc.o ./agent/send.o ./agent/send_data.o ./agent/stat.o ./agent/sysinfo.o ./agent/thpool.o ./agent/top.o ./agent/top_nls.o ./agent/uptime.o ./agent/version.o ./agent/wchan.o  ./sar/act_sadc.o ./sar/sadc.o ./sar/pr_stats.o  ./sar/sa_wrap.o ./sar/sa_common.o ./sar/network_security.o ./sar/librdstats.a ./sar/librdsensors.a ./sar/libsyscom.a  ./tcpdump-5.0.0/fptype.o ./tcpdump-5.0.0/tcpdump.o ./tcpdump-5.0.0/libnetdissect.a ./libpcap/libpcap.a -o send_data -lm -lpthread -ldl -lrt -lcrypto
