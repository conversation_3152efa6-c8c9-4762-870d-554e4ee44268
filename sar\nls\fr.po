# French translation of sysstat.
# Copyright (C) 2013 Free Software Foundation, Inc.
# This file is distributed under the same license as the sysstat package.
#
# <PERSON><PERSON><PERSON><PERSON> GODARD <sysstat [at] orange.fr>, 1999.
# <PERSON> <<EMAIL>>, 2011.
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2013.
msgid ""
msgstr ""
"Project-Id-Version: sysstat 10.1.6\n"
"Report-Msgid-Bugs-To: sysstat <at> orange.fr\n"
"POT-Creation-Date: 2013-06-08 09:01+0200\n"
"PO-Revision-Date: 2013-06-11 08:56+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: French <<EMAIL>>\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms:  nplurals=2; plural=(n > 1);\n"
"X-Generator: Lokalize 1.0\n"

#: iostat.c:86 cifsiostat.c:71 mpstat.c:90 sar.c:94 pidstat.c:83
#: nfsiostat.c:70
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ]\n"
msgstr "Utilisation : %s [ options ] [ <intervalle> [ <itérations> ] ]\n"

#: iostat.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ] [ --debuginfo ]\n"
msgstr ""
"Options possibles :\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | … } ]\n"
"[ [ -T ] -g <nom_groupe> ] [ -p [ <périph.> [,…] | ALL ] ]\n"
"[ <périph.> […] | ALL ] [ --debuginfo ]\n"

#: iostat.c:95
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ]\n"
msgstr ""
"Options possibles :\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | … } ]\n"
"[ [ -T ] -g <nom_groupe> ] [ -p [ <périph> [,…] | ALL ] ]\n"
"[ <périph> […] | ALL ]\n"

#: iostat.c:330
#, c-format
msgid "Cannot find disk data\n"
msgstr "Impossible de trouver les données du disque\n"

#: iostat.c:1394 sa_common.c:1303
#, c-format
msgid "Invalid type of persistent device name\n"
msgstr "Type de périphérique persistant invalide\n"

#: sadf_misc.c:596
#, c-format
msgid "System activity data file: %s (%#x)\n"
msgstr "Fichier des données d'activité système :%s (%#x)\n"

#: sadf_misc.c:605
#, c-format
msgid "Host: "
msgstr "Hôte : "

#: sadf_misc.c:611
#, c-format
msgid "Size of a long int: %d\n"
msgstr "Taille d'un « long int » :%d\n"

#: sadf_misc.c:613
#, c-format
msgid "List of activities:\n"
msgstr "Liste des activités :\n"

#: sadf_misc.c:626
#, c-format
msgid "\t[Unknown activity format]"
msgstr "\t[Format d'activité inconnu]"

#: sadc.c:84
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
msgstr "Utilisation :%s [ options ] [ <intervalle> [ <itérations> ] ] [ <fichier_de_sortie> ]\n"

#: sadc.c:87
#, c-format
msgid ""
"Options are:\n"
"[ -C <comment> ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"
msgstr ""
"Options possibles :\n"
"[ -C <commentaire> ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"

#: sadc.c:250
#, c-format
msgid "Cannot write data to system activity file: %s\n"
msgstr "Impossible d'écrire les données dans le fichier d'activité système :%s\n"

#: sadc.c:537
#, c-format
msgid "Cannot write system activity file header: %s\n"
msgstr "Impossible d'écrire l'entête du fichier d'activité système :%s\n"

#: sadc.c:650 sadc.c:659 sadc.c:720 ioconf.c:491 rd_stats.c:105
#: sa_common.c:1109 count.c:275
#, c-format
msgid "Cannot open %s: %s\n"
msgstr "Impossible d'ouvrir %s :%s\n"

#: sadc.c:842
#, c-format
msgid "Cannot append data to that file (%s)\n"
msgstr "Impossible d'ajouter des données à la fin de ce fichier (%s)\n"

#: common.c:62
#, c-format
msgid "sysstat version %s\n"
msgstr "sysstat version %s\n"

#: cifsiostat.c:75 nfsiostat.c:74
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"
msgstr ""
"Options possibles :\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"

#: cifsiostat.c:78 nfsiostat.c:77
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
msgstr ""
"Options possibles :\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#: mpstat.c:93
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -P { <cpu> [,...] | ON | ALL } ]\n"
msgstr ""
"Options possibles :\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -P { <cpu> [,…] | ON | ALL } ]\n"

# sar.c:
#: mpstat.c:609 sar.c:402 pidstat.c:1857
msgid "Average:"
msgstr "Moyenne :"

#: mpstat.c:976
#, c-format
msgid "Not that many processors!\n"
msgstr "Pas tant de processeurs !\n"

#: sadf.c:86
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <datafile> ]\n"
msgstr "Utilisation :%s [ options ] [ <intervalle> [ <itérations> ] ] [ <fichier_données> ]\n"

#: sadf.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -C ] [ -d | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -P { <cpu> [,...] | ALL } ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"
"[ -- <sar_options> ]\n"
msgstr ""
"Options possibles :\n"
"[ -C ] [ -d | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -P { <cpu> [,…] | ALL } ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"
"[ -- <options_sar> ]\n"

#: sar.c:109
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -d ] [ -F ] [ -H ] [ -h ] [ -p ] [ -q ] [ -R ]\n"
"[ -r ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ] [ -v ] [ -W ] [ -w ] [ -y ]\n"
"[ -I { <int> [,...] | SUM | ALL | XALL } ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -m { <keyword> [,...] | ALL } ] [ -n { <keyword> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filename> ] | -o [ <filename> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"
msgstr ""
"Options possibles :\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -d ] [ -F ] [-H ] [ -h ] [ -p ] [ -q ] [ -R ]\n"
"[ -r ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ] [ -v ] [ -W ] [ -w ] [ -y ]\n"
"[ -I { <entier> [,…] | SUM | ALL | XALL } ] [ -P { <cpu> [,…] | ALL } ]\n"
"[ -m { <mot-clef> [,…] | ALL } ] [ -n { <mot-clef> [,…] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | … } ]\n"
"[ -f [ <nom_fichier> ] | -o [ <nom_fichier> ] | -[0-9]+ ]\n"
"[ -i <intervalle> ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"

#: sar.c:131
#, c-format
msgid "Main options and reports:\n"
msgstr "Options principales et rapports :\n"

#: sar.c:132
#, c-format
msgid "\t-b\tI/O and transfer rate statistics\n"
msgstr "\t-b\tStatistiques entrées/sorties et taux de transfert\n"

#: sar.c:133
#, c-format
msgid "\t-B\tPaging statistics\n"
msgstr "\t-B\tStatistiques pages mémoire\n"

#: sar.c:134
#, c-format
msgid "\t-d\tBlock devices statistics\n"
msgstr "\t-d\tStatistiques périphériques par blocs\n"

#: sar.c:135
#, c-format
msgid "\t-F\tFilesystems statistics\n"
msgstr "\t-F\tStatistiques systèmes de fichiers\n"

#: sar.c:136
#, c-format
msgid "\t-H\tHugepages utilization statistics\n"
msgstr "\t-H\tStatistiques d'utilisation des pages larges\n"

#: sar.c:137
#, c-format
msgid ""
"\t-I { <int> | SUM | ALL | XALL }\n"
"\t\tInterrupts statistics\n"
msgstr ""
"\t-I { <int> | SUM | ALL | XALL }\n"
"\t\tStatistiques interruptions\n"

#: sar.c:139
#, c-format
msgid ""
"\t-m { <keyword> [,...] | ALL }\n"
"\t\tPower management statistics\n"
"\t\tKeywords are:\n"
"\t\tCPU\tCPU instantaneous clock frequency\n"
"\t\tFAN\tFans speed\n"
"\t\tFREQ\tCPU average clock frequency\n"
"\t\tIN\tVoltage inputs\n"
"\t\tTEMP\tDevices temperature\n"
"\t\tUSB\tUSB devices plugged into the system\n"
msgstr ""
"\t-m { <motclef> [,…] | ALL }\n"
"\t\tStatistiques de gestion énergie\n"
"\t\tMots-clefs possibles :\n"
"\t\tCPU\tFréquence horloge instantanée CPU\n"
"\t\tFAN\tVitesse ventilateurs\n"
"\t\tFREQ\tFréquence horloge moyenne CPU\n"
"\t\tIN\tTensions en entrée\n"
"\t\tTEMP\tTempérature périphériques\n"
"\t\tUSB\tPériphériques USB connectés au système\n"

#: sar.c:148
#, c-format
msgid ""
"\t-n { <keyword> [,...] | ALL }\n"
"\t\tNetwork statistics\n"
"\t\tKeywords are:\n"
"\t\tDEV\tNetwork interfaces\n"
"\t\tEDEV\tNetwork interfaces (errors)\n"
"\t\tNFS\tNFS client\n"
"\t\tNFSD\tNFS server\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP traffic\t(v4)\n"
"\t\tEIP\tIP traffic\t(v4) (errors)\n"
"\t\tICMP\tICMP traffic\t(v4)\n"
"\t\tEICMP\tICMP traffic\t(v4) (errors)\n"
"\t\tTCP\tTCP traffic\t(v4)\n"
"\t\tETCP\tTCP traffic\t(v4) (errors)\n"
"\t\tUDP\tUDP traffic\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP traffic\t(v6)\n"
"\t\tEIP6\tIP traffic\t(v6) (errors)\n"
"\t\tICMP6\tICMP traffic\t(v6)\n"
"\t\tEICMP6\tICMP traffic\t(v6) (errors)\n"
"\t\tUDP6\tUDP traffic\t(v6)\n"
msgstr ""
"\t-n { <mot_clé> [,…] | ALL }\n"
"\t\tStatistiques réseau\n"
"\t\tMots-clés possibles :\n"
"\t\tDEV\tInterfaces réseau\n"
"\t\tEDEV\tInterfaces réseau (erreurs)\n"
"\t\tNFS\tClient NFS\n"
"\t\tNFSD\tServeur NFS\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tTrafic IP\t(v4)\n"
"\t\tEIP\tTrafic IP\t(v4) (erreurs)\n"
"\t\tICMP\tTrafic ICMP\t(v4)\n"
"\t\tEICMP\tTrafic ICMP\t(v4) (erreurs)\n"
"\t\tTCP\tTrafic TCP\t(v4)\n"
"\t\tETCP\tTrafic TCP\t(v4) (erreurs)\n"
"\t\tUDP\tTrafic UDP\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tTrafic IP\t(v6)\n"
"\t\tEIP6\tTrafic\t(v6) (erreurs)\n"
"\t\tICMP6\tTrafic ICMP\t(v6)\n"
"\t\tEICMP6\tTrafic ICMP\t(v6) (erreurs)\n"
"\t\tUDP6\tTrafic UDP\t(v6)\n"

#: sar.c:169
#, c-format
msgid "\t-q\tQueue length and load average statistics\n"
msgstr "\t-q\tStatistiques longueur de queue et charge moyenne\n"

#: sar.c:170
#, c-format
msgid "\t-r\tMemory utilization statistics\n"
msgstr "\t-r\tStatistiques d'utilisation mémoire\n"

#: sar.c:171
#, c-format
msgid "\t-R\tMemory statistics\n"
msgstr "\t-R\tStatistiques mémoire\n"

#: sar.c:172
#, c-format
msgid "\t-S\tSwap space utilization statistics\n"
msgstr "\t-S\tStatistiques d'utilisation de l'espace d'échange\n"

#: sar.c:173
#, c-format
msgid ""
"\t-u [ ALL ]\n"
"\t\tCPU utilization statistics\n"
msgstr ""
"\t-u [ ALL ]\n"
"\t\tStatistiques d'utilisation CPU\n"

#: sar.c:175
#, c-format
msgid "\t-v\tKernel tables statistics\n"
msgstr "\t-v\tStatistiques tables noyau\n"

#: sar.c:176
#, c-format
msgid "\t-w\tTask creation and system switching statistics\n"
msgstr "\t-w\tStatistiques de création et commutation de tâches par le système\n"

#: sar.c:177
#, c-format
msgid "\t-W\tSwapping statistics\n"
msgstr "\t-W\tStatistiques d'échange (mémoire)\n"

#: sar.c:178
#, c-format
msgid "\t-y\tTTY devices statistics\n"
msgstr "\t-y\tStatistiques périph. consoles (TTY)\n"

#: sar.c:236
#, c-format
msgid "End of data collecting unexpected\n"
msgstr "Fin inattendue de collecte des données\n"

#: sar.c:823
#, c-format
msgid "Invalid data format\n"
msgstr "Format de données non valide\n"

#: sar.c:827
#, c-format
msgid "Using a wrong data collector from a different sysstat version\n"
msgstr "Utilisation d'un mauvais collecteur de données venant d'une autre version de sysstat\n"

#: sar.c:851
#, c-format
msgid "Inconsistent input data\n"
msgstr "Données inconsistantes en entrée\n"

#: sar.c:1034 pidstat.c:216
#, c-format
msgid "Requested activities not available\n"
msgstr "Activités demandées non disponibles\n"

#: sar.c:1304
#, c-format
msgid "-f and -o options are mutually exclusive\n"
msgstr "Les options -f et -o ne peuvent être utilisées ensemble\n"

#: sar.c:1310
#, c-format
msgid "Not reading from a system activity file (use -f option)\n"
msgstr "Pas de lecture d'un fichier d'activité système (utilisez l'option -f)\n"

#: sar.c:1442
#, c-format
msgid "Cannot find the data collector (%s)\n"
msgstr "Impossible de trouver le collecteur de données (%s)\n"

#: sa_common.c:917
#, c-format
msgid "Error while reading system activity file: %s\n"
msgstr "Erreur lors de la lecture du fichier d'activité système :%s\n"

#: sa_common.c:927
#, c-format
msgid "End of system activity file unexpected\n"
msgstr "Fin du fichier d'activité système inattendue\n"

#: sa_common.c:946
#, c-format
msgid "File created by sar/sadc from sysstat version %d.%d.%d"
msgstr "Fichier créé par sar/sadc de la version %d.%d.%d de sysstat"

#: sa_common.c:977
#, c-format
msgid "Invalid system activity file: %s\n"
msgstr "Fichier d'activité système non valide :%s\n"

#: sa_common.c:984
#, c-format
msgid "Current sysstat version can no longer read the format of this file (%#x)\n"
msgstr "La version actuelle de sysstat ne peut plus lire le format de ce fichier (%#x)\n"

#: sa_common.c:1216
#, c-format
msgid "Requested activities not available in file %s\n"
msgstr "Activités demandées non enregistrées dans le fichier %s\n"

#: pidstat.c:86
#, c-format
msgid ""
"Options are:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -r ] [ -s ] [ -t ] [ -U [ <username> ] ] [ -u ]\n"
"[ -V ] [ -w ] [ -C <command> ] [ -p { <pid> [,...] | SELF | ALL } ]\n"
"[ -T { TASK | CHILD | ALL } ]\n"
msgstr ""
"Options possibles  :\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -r ] [ -s ] [ -t ] [ -U [ <nomutilisateur> ] ] [ -u ]\n"
"[ -V ] [ -w ] [ -C <commande> ] [ -p { <pid> [,…] | SELF | ALL } ]\n"
"[ -T { TASK | CHILD | ALL } ]\n"

#: count.c:321
#, c-format
msgid "Cannot handle so many processors!\n"
msgstr "Impossible de gérer autant de processeurs !\n"

#: pr_stats.c:2348 pr_stats.c:2361 pr_stats.c:2461 pr_stats.c:2473
msgid "Summary"
msgstr "Résumé"

#: pr_stats.c:2399
msgid "Other devices not listed here"
msgstr "Autres périphériques non listés ici"

#~ msgid ""
#~ "Options are:\n"
#~ "[ --debuginfo ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
#~ msgstr ""
#~ "Options possibles :\n"
#~ "[ --debuginfo ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#~ msgid "\t-m\tPower management statistics\n"
#~ msgstr "\t-m\tStatistiques gestion d'énergie\n"

#~ msgid "-x and -p options are mutually exclusive\n"
#~ msgstr "Les options -x et -p ne peuvent être utilisées ensemble\n"

#~ msgid "Time: %s\n"
#~ msgstr "Heure : %s\n"

#~ msgid ""
#~ "Usage: %s [ options... ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
#~ "Options are:\n"
#~ "[ -C <comment> ] [ -d ] [ -F ] [ -I ] [ -V ]\n"
#~ msgstr ""
#~ "Utilisation: %s [ options... ] [ <intervalle> [ <itérations> ] ] [ <fichier> ]\n"
#~ "Options possibles:\n"
#~ "[ -C <commentaire> ] [ -d ] [ -F ] [ -I ] [ -V ]\n"

#~ msgid "Not an SMP machine...\n"
#~ msgstr "Pas une machine multiprocesseur...\n"
