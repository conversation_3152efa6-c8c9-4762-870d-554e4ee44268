# French translation of sysstat.
# Copyright (C) 2017 Free Software Foundation, Inc.
# This file is distributed under the same license as the sysstat package.
#
# <PERSON><PERSON><PERSON><PERSON> GODARD <sysstat [at] orange.fr>, 1999.
# <PERSON> <<EMAIL>>, 2011.
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2017.
msgid ""
msgstr ""
"Project-Id-Version: sysstat 11.6.0\n"
"Report-Msgid-Bugs-To: sysstat <at> orange.fr\n"
"POT-Creation-Date: 2017-08-14 08:32+0200\n"
"PO-Revision-Date: 2017-08-16 08:45+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: French <<EMAIL>>\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"Plural-Forms:  nplurals=2; plural=(n >= 2);\n"

#: iostat.c:86 cifsiostat.c:70 mpstat.c:126 sar.c:96 tapestat.c:95
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ]\n"
msgstr "Utilisation : %s [ options ] [ <intervalle> [ <itérations> ] ]\n"

#: iostat.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ] [ --debuginfo ]\n"
msgstr ""
"Options possibles :\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | … } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <nom_groupe> ] [ -p [ <périph.> [,…] | ALL ] ]\n"
"[ <périph.> […] | ALL ] [ --debuginfo ]\n"

#: iostat.c:95
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ]\n"
msgstr ""
"Options possibles :\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | … } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <nom_groupe> ] [ -p [ <périph.> [,…] | ALL ] ]\n"
"[ <périph.> […] | ALL ]\n"

#: iostat.c:326
#, c-format
msgid "Cannot find disk data\n"
msgstr "Impossible de trouver les données du disque\n"

#: iostat.c:1812 sa_common.c:1590
#, c-format
msgid "Invalid type of persistent device name\n"
msgstr "Type de périphérique persistant invalide\n"

#: sadc.c:89
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
msgstr "Utilisation :%s [ options ] [ <intervalle> [ <itérations> ] ] [ <fichier_de_sortie> ]\n"

#: sadc.c:92
#, c-format
msgid ""
"Options are:\n"
"[ -C <comment> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"
msgstr ""
"Options possibles :\n"
"[ -C <commentaire> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"

#: sadc.c:267
#, c-format
msgid "Cannot write data to system activity file: %s\n"
msgstr "Impossible d'écrire les données dans le fichier d'activité système :%s\n"

#: sadc.c:563
#, c-format
msgid "Cannot write system activity file header: %s\n"
msgstr "Impossible d'écrire l'entête du fichier d'activité système :%s\n"

#: sadc.c:763 sadc.c:772 sadc.c:839 ioconf.c:510 rd_stats.c:69
#: sa_common.c:1204 count.c:118
#, c-format
msgid "Cannot open %s: %s\n"
msgstr "Impossible d'ouvrir %s :%s\n"

#: sadc.c:1019
#, c-format
msgid "Cannot append data to that file (%s)\n"
msgstr "Impossible d'ajouter des données à la fin de ce fichier (%s)\n"

#: common.c:77
#, c-format
msgid "sysstat version %s\n"
msgstr "sysstat version %s\n"

#: cifsiostat.c:74
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"
msgstr ""
"Options possibles :\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"

#: cifsiostat.c:77
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
msgstr ""
"Options possibles :\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#: mpstat.c:129
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -n ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -N { <node_list> | ALL } ] [ -o JSON ] [ -P { <cpu_list> | ON | ALL } ]\n"
msgstr ""
"Options possibles :\n"
"[ -A ] [ -n ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -N { <liste_nœuds> | ALL } ] [ -o JSON ] [ -P { <liste_cpu> | ON | ALL } ]\n"

# sar.c:
#: mpstat.c:1672 sar.c:358 pidstat.c:2406
msgid "Average:"
msgstr "Moyenne :"

#: sadf.c:87
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <datafile> | -[0-9]+ ]\n"
msgstr "Utilisation :%s [ options ] [ <intervalle> [ <itérations> ] ] [ <fichier_données> | -[0-9]+ ]\n"

#: sadf.c:90
#, c-format
msgid ""
"Options are:\n"
"[ -C ] [ -c | -d | -g | -j | -p | -r | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -O <opts> [,...] ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
"[ -- <sar_options> ]\n"
msgstr ""
"Options possibles :\n"
"[ -C ] [ -c | -d | -g | -j | -p | -r | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -O <opts> [,…] ] [ -P { <cpu> [,…] | ALL } ]\n"
"[ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
"[ -- <options_sar> ]\n"

#: pr_stats.c:2538 pr_stats.c:2549 pr_stats.c:2656 pr_stats.c:2667
msgid "Summary:"
msgstr "Résumé:"

#: pr_stats.c:2591
msgid "Other devices not listed here"
msgstr "Autres périphériques non listés ici"

#: sar.c:111
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ MOUNT ] ] [ -H ] [ -h ]\n"
"[ -p ] [ -q ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --human ] [ --sadc ]\n"
"[ -I { <int_list> | SUM | ALL } ] [ -P { <cpu_list> | ALL } ]\n"
"[ -m { <keyword> [,...] | ALL } ] [ -n { <keyword> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filename> ] | -o [ <filename> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
msgstr ""
"Options possibles :\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ MOUNT ] ] [-H ] [ -h ]\n"
"[ -p ] [ -q ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --human ] [ --sadc ]\n"
"[ -I { <liste_entiers> | SUM | ALL } ] [ -P { <liste_cpu> | ALL } ]\n"
"[ -m { <mot-clef> [,…] | ALL } ] [ -n { <mot-clef> [,…] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | … } ]\n"
"[ -f [ <nom_fichier> ] | -o [ <nom_fichier> ] | -[0-9]+ ]\n"
"[ -i <intervalle> ] [ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"

#: sar.c:134
#, c-format
msgid "Main options and reports:\n"
msgstr "Options principales et rapports :\n"

#: sar.c:135
#, c-format
msgid "\t-B\tPaging statistics\n"
msgstr "\t-B\tStatistiques pages mémoire\n"

#: sar.c:136
#, c-format
msgid "\t-b\tI/O and transfer rate statistics\n"
msgstr "\t-b\tStatistiques entrées/sorties et taux de transfert\n"

#: sar.c:137
#, c-format
msgid "\t-d\tBlock devices statistics\n"
msgstr "\t-d\tStatistiques périphériques par blocs\n"

#: sar.c:138
#, c-format
msgid "\t-F [ MOUNT ]\n"
msgstr "\t-F [ MOUNT ]\n"

#: sar.c:139
#, c-format
msgid "\t\tFilesystems statistics\n"
msgstr "\t\tStatistiques systèmes de fichiers\n"

#: sar.c:140
#, c-format
msgid "\t-H\tHugepages utilization statistics\n"
msgstr "\t-H\tStatistiques d'utilisation des pages larges\n"

#: sar.c:141
#, c-format
msgid ""
"\t-I { <int_list> | SUM | ALL }\n"
"\t\tInterrupts statistics\n"
msgstr ""
"\t-I { <liste_int> | SUM | ALL }\n"
"\t\tStatistiques interruptions\n"

#: sar.c:143
#, c-format
msgid ""
"\t-m { <keyword> [,...] | ALL }\n"
"\t\tPower management statistics\n"
"\t\tKeywords are:\n"
"\t\tCPU\tCPU instantaneous clock frequency\n"
"\t\tFAN\tFans speed\n"
"\t\tFREQ\tCPU average clock frequency\n"
"\t\tIN\tVoltage inputs\n"
"\t\tTEMP\tDevices temperature\n"
"\t\tUSB\tUSB devices plugged into the system\n"
msgstr ""
"\t-m { <motclef> [,…] | ALL }\n"
"\t\tStatistiques de gestion énergie\n"
"\t\tMots-clefs possibles :\n"
"\t\tCPU\tFréquence horloge instantanée CPU\n"
"\t\tFAN\tVitesse ventilateurs\n"
"\t\tFREQ\tFréquence horloge moyenne CPU\n"
"\t\tIN\tTensions en entrée\n"
"\t\tTEMP\tTempérature périphériques\n"
"\t\tUSB\tPériphériques USB connectés au système\n"

#: sar.c:152
#, c-format
msgid ""
"\t-n { <keyword> [,...] | ALL }\n"
"\t\tNetwork statistics\n"
"\t\tKeywords are:\n"
"\t\tDEV\tNetwork interfaces\n"
"\t\tEDEV\tNetwork interfaces (errors)\n"
"\t\tNFS\tNFS client\n"
"\t\tNFSD\tNFS server\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP traffic\t(v4)\n"
"\t\tEIP\tIP traffic\t(v4) (errors)\n"
"\t\tICMP\tICMP traffic\t(v4)\n"
"\t\tEICMP\tICMP traffic\t(v4) (errors)\n"
"\t\tTCP\tTCP traffic\t(v4)\n"
"\t\tETCP\tTCP traffic\t(v4) (errors)\n"
"\t\tUDP\tUDP traffic\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP traffic\t(v6)\n"
"\t\tEIP6\tIP traffic\t(v6) (errors)\n"
"\t\tICMP6\tICMP traffic\t(v6)\n"
"\t\tEICMP6\tICMP traffic\t(v6) (errors)\n"
"\t\tUDP6\tUDP traffic\t(v6)\n"
"\t\tFC\tFibre channel HBAs\n"
"\t\tSOFT\tSoftware-based network processing\n"
msgstr ""
"\t-n { <mot_clé> [,…] | ALL }\n"
"\t\tStatistiques réseau\n"
"\t\tMots-clés possibles :\n"
"\t\tDEV\tInterfaces réseau\n"
"\t\tEDEV\tInterfaces réseau (erreurs)\n"
"\t\tNFS\tClient NFS\n"
"\t\tNFSD\tServeur NFS\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tTrafic IP\t(v4)\n"
"\t\tEIP\tTrafic IP\t(v4) (erreurs)\n"
"\t\tICMP\tTrafic ICMP\t(v4)\n"
"\t\tEICMP\tTrafic ICMP\t(v4) (erreurs)\n"
"\t\tTCP\tTrafic TCP\t(v4)\n"
"\t\tETCP\tTrafic TCP\t(v4) (erreurs)\n"
"\t\tUDP\tTrafic UDP\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tTrafic IP\t(v6)\n"
"\t\tEIP6\tTrafic IP\t(v6) (erreurs)\n"
"\t\tICMP6\tTrafic ICMP\t(v6)\n"
"\t\tEICMP6\tTrafic ICMP\t(v6) (erreurs)\n"
"\t\tUDP6\tTrafic UDP\t(v6)\n"
"\t\tFC\tHBA Fibre channel\n"
"\t\tSOFT\tTraitement logiciel du réseau\n"

#: sar.c:175
#, c-format
msgid "\t-q\tQueue length and load average statistics\n"
msgstr "\t-q\tStatistiques longueur de queue et charge moyenne\n"

#: sar.c:176
#, c-format
msgid ""
"\t-r [ ALL ]\n"
"\t\tMemory utilization statistics\n"
msgstr ""
"\t-r [ ALL ]\n"
"\t\tStatistiques d'utilisation mémoire\n"

#: sar.c:178
#, c-format
msgid "\t-S\tSwap space utilization statistics\n"
msgstr "\t-S\tStatistiques d'utilisation de l'espace d'échange\n"

#: sar.c:179
#, c-format
msgid ""
"\t-u [ ALL ]\n"
"\t\tCPU utilization statistics\n"
msgstr ""
"\t-u [ ALL ]\n"
"\t\tStatistiques d'utilisation CPU\n"

#: sar.c:181
#, c-format
msgid "\t-v\tKernel tables statistics\n"
msgstr "\t-v\tStatistiques tables noyau\n"

#: sar.c:182
#, c-format
msgid "\t-W\tSwapping statistics\n"
msgstr "\t-W\tStatistiques d'échange (mémoire)\n"

#: sar.c:183
#, c-format
msgid "\t-w\tTask creation and system switching statistics\n"
msgstr "\t-w\tStatistiques de création et commutation de tâches par le système\n"

#: sar.c:184
#, c-format
msgid "\t-y\tTTY devices statistics\n"
msgstr "\t-y\tStatistiques périph. consoles (TTY)\n"

#: sar.c:198
#, c-format
msgid "Data collector will be sought in PATH\n"
msgstr "Le collecteur de données sera recherché dans le PATH\n"

#: sar.c:201
#, c-format
msgid "Data collector found: %s\n"
msgstr "Collecteur de données trouvé: %s\n"

#: sar.c:260
#, c-format
msgid "End of data collecting unexpected\n"
msgstr "Fin inattendue de collecte des données\n"

#: sar.c:813
#, c-format
msgid "Using a wrong data collector from a different sysstat version\n"
msgstr "Utilisation d'un mauvais collecteur de données venant d'une autre version de sysstat\n"

#: sar.c:865
#, c-format
msgid "Inconsistent input data\n"
msgstr "Données inconsistantes en entrée\n"

#: sar.c:1044 pidstat.c:239
#, c-format
msgid "Requested activities not available\n"
msgstr "Activités demandées non disponibles\n"

#: sar.c:1347
#, c-format
msgid "-f and -o options are mutually exclusive\n"
msgstr "Les options -f et -o ne peuvent être utilisées ensemble\n"

#: sar.c:1353
#, c-format
msgid "Not reading from a system activity file (use -f option)\n"
msgstr "Pas de lecture d'un fichier d'activité système (utilisez l'option -f)\n"

#: sar.c:1489
#, c-format
msgid "Cannot find the data collector (%s)\n"
msgstr "Impossible de trouver le collecteur de données (%s)\n"

#: sa_conv.c:69
#, c-format
msgid "Cannot convert the format of this file\n"
msgstr "Ne peux convertir le format de ce fichier\n"

#: sa_conv.c:562
#, c-format
msgid ""
"\n"
"CPU activity not found in file. Aborting...\n"
msgstr ""
"\n"
"Activité CPU non trouvée dans le fichier. Abandon…\n"

#: sa_conv.c:578
#, c-format
msgid ""
"\n"
"Invalid data found. Aborting...\n"
msgstr ""
"\n"
"Donnée invalide trouvée. Abandon…\n"

#: sa_conv.c:897
#, c-format
msgid "Statistics: "
msgstr "Statistiques: "

#: sa_conv.c:1016
#, c-format
msgid ""
"\n"
"File successfully converted to sysstat format version %s\n"
msgstr ""
"\n"
"Fichier converti avec succès au format systat version %s\n"

#: pidstat.c:87
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ -e <program> <args> ]\n"
msgstr "Utilisation : %s [ options ] [ <intervalle> [ <itérations> ] ] [ -e <programme> <args> ]\n"

#: pidstat.c:90
#, c-format
msgid ""
"Options are:\n"
"[ -d ] [ -H ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ] [ -U [ <username> ] ]\n"
"[ -u ] [ -V ] [ -v ] [ -w ] [ -C <command> ] [ -G <process_name> ] [ --human ]\n"
"[ -p { <pid> [,...] | SELF | ALL } ] [ -T { TASK | CHILD | ALL } ]\n"
msgstr ""
"Options possibles  :\n"
"[ -d ] [ -H ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ] [ -U [ <nomutilisateur> ] ]\n"
"[ -u ] [ -V ] [ -v ] [ -w ] [ -C <commande> ] [ -G <nom_processus> ] [ --human ]\n"
"[ -p { <pid> [,…] | SELF | ALL } ] [ -T { TASK | CHILD | ALL } ]\n"

#: sa_common.c:1000
#, c-format
msgid "Error while reading system activity file: %s\n"
msgstr "Erreur lors de la lecture du fichier d'activité système :%s\n"

#: sa_common.c:1010
#, c-format
msgid "End of system activity file unexpected\n"
msgstr "Fin du fichier d'activité système inattendue\n"

#: sa_common.c:1029
#, c-format
msgid "File created by sar/sadc from sysstat version %d.%d.%d"
msgstr "Fichier créé par sar/sadc de la version %d.%d.%d de sysstat"

#: sa_common.c:1062
#, c-format
msgid "Invalid system activity file: %s\n"
msgstr "Fichier d'activité système non valide :%s\n"

#: sa_common.c:1074
#, c-format
msgid "Endian format mismatch\n"
msgstr "Format d'ordre des octets incompatible\n"

#: sa_common.c:1078
#, c-format
msgid "Current sysstat version cannot read the format of this file (%#x)\n"
msgstr "La version actuelle de sysstat ne peut pas lire le format de ce fichier (%#x)\n"

#: sa_common.c:1207
#, c-format
msgid "Please check if data collecting is enabled\n"
msgstr "Veuillez vérifier si la collecte des données est activée\n"

#: sa_common.c:1400
#, c-format
msgid "Requested activities not available in file %s\n"
msgstr "Activités demandées non enregistrées dans le fichier %s\n"

#: tapestat.c:97
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"
msgstr ""
"Options possibles :\n"
"[ --human ] [ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"

#: tapestat.c:263
#, c-format
msgid "No tape drives with statistics found\n"
msgstr "Aucun lecteur de bande avec des statistiques n'a été trouvé\n"

#: count.c:169
#, c-format
msgid "Cannot handle so many processors!\n"
msgstr "Impossible de gérer autant de processeurs !\n"

#: sadf_misc.c:834
#, c-format
msgid "System activity data file: %s (%#x)\n"
msgstr "Fichier des données d'activité système :%s (%#x)\n"

#: sadf_misc.c:843
#, c-format
msgid "Genuine sa datafile: %s (%x)\n"
msgstr "Fichier de données sa authentique:%s (%#x)\n"

#: sadf_misc.c:844
msgid "no"
msgstr "non"

#: sadf_misc.c:844
msgid "yes"
msgstr "oui"

#: sadf_misc.c:847
#, c-format
msgid "Host: "
msgstr "Hôte : "

#: sadf_misc.c:854
#, c-format
msgid "Number of CPU for last samples in file: %u\n"
msgstr "Nombre de CPU dans les derniers échantillons du fichier: %u\n"

#: sadf_misc.c:860
#, c-format
msgid "File date: %s\n"
msgstr "Date du fichier : %s\n"

#: sadf_misc.c:863
#, c-format
msgid "File time: "
msgstr "Heure du fichier : "

#: sadf_misc.c:868
#, c-format
msgid "Size of a long int: %d\n"
msgstr "Taille d'un « long int » :%d\n"

#: sadf_misc.c:874
#, c-format
msgid "List of activities:\n"
msgstr "Liste des activités :\n"

#: sadf_misc.c:887
#, c-format
msgid "\t[Unknown activity format]"
msgstr "\t[Format d'activité inconnu]"

#~ msgid "\t-R\tMemory statistics\n"
#~ msgstr "\t-R\tStatistiques mémoire\n"

#~ msgid ""
#~ "Options are:\n"
#~ "[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
#~ msgstr ""
#~ "Options possibles :\n"
#~ "[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#~ msgid "Not that many processors!\n"
#~ msgstr "Pas tant de processeurs !\n"

#~ msgid "Invalid data format\n"
#~ msgstr "Format de données non valide\n"

#~ msgid "\t-m\tPower management statistics\n"
#~ msgstr "\t-m\tStatistiques gestion d'énergie\n"

#~ msgid "-x and -p options are mutually exclusive\n"
#~ msgstr "Les options -x et -p ne peuvent être utilisées ensemble\n"

#~ msgid "Time: %s\n"
#~ msgstr "Heure : %s\n"

#~ msgid ""
#~ "Usage: %s [ options... ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
#~ "Options are:\n"
#~ "[ -C <comment> ] [ -d ] [ -F ] [ -I ] [ -V ]\n"
#~ msgstr ""
#~ "Utilisation: %s [ options... ] [ <intervalle> [ <itérations> ] ] [ <fichier> ]\n"
#~ "Options possibles:\n"
#~ "[ -C <commentaire> ] [ -d ] [ -F ] [ -I ] [ -V ]\n"

#~ msgid "Not an SMP machine...\n"
#~ msgstr "Pas une machine multiprocesseur...\n"
