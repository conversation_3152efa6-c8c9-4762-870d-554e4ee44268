    1  2019-10-23 20:58:40.639715 IP (tos 0x0, ttl 64, id 42311, offset 0, flags [none], proto UDP (17), length 57)
    *********.46225 > *********.53: 13784+ A? example.com. (29)
    2  2019-10-23 20:58:40.661836 IP (tos 0x0, ttl 48, id 43077, offset 0, flags [none], proto UDP (17), length 73)
    *********.53 > *********.46225: 13784*- 1/0/0 example.com. A ************* (45)
    3  2019-10-23 20:58:41.671700 IP (tos 0x0, ttl 64, id 42441, offset 0, flags [none], proto UDP (17), length 68)
    *********.46225 > *********.53: 47424+ [1au] A? example.com. (40)
    4  2019-10-23 20:58:41.693577 IP (tos 0x0, ttl 48, id 64719, offset 0, flags [none], proto UDP (17), length 255)
    *********.53 > *********.46225: 47424*- 2/0/1 example.com. A *************, example.com. RRSIG (227)
    5  2019-10-23 20:58:42.703534 IP (tos 0x0, ttl 64, id 42575, offset 0, flags [none], proto UDP (17), length 79)
    *********.46225 > *********.53: 41739+ [1au] A? example.com. (51)
    6  2019-10-23 20:58:42.725559 IP (tos 0x0, ttl 48, id 21779, offset 0, flags [none], proto UDP (17), length 95)
    *********.53 > *********.46225: 41739*- 1/0/1 example.com. A ************* (67)
    7  2019-10-23 20:58:43.734693 IP (tos 0x0, ttl 64, id 42674, offset 0, flags [none], proto UDP (17), length 80)
    *********.46225 > *********.53: 18065+ [1au] A? example.com. (52)
    8  2019-10-23 20:58:43.756707 IP (tos 0x0, ttl 48, id 43697, offset 0, flags [none], proto UDP (17), length 96)
    *********.53 > *********.46225: 18065*- 1/0/1 example.com. A ************* (68)
    9  2019-10-23 20:58:44.766528 IP (tos 0x0, ttl 64, id 42890, offset 0, flags [none], proto UDP (17), length 89)
    *********.46225 > *********.53: 34237+ [1au] A? example.com. (61)
   10  2019-10-23 20:58:44.788502 IP (tos 0x0, ttl 48, id 65435, offset 0, flags [none], proto UDP (17), length 105)
    *********.53 > *********.46225: 34237*- 1/0/1 example.com. A ************* (77)
   11  2019-10-23 20:58:45.797638 IP (tos 0x0, ttl 64, id 43148, offset 0, flags [none], proto UDP (17), length 80)
    *********.46225 > *********.53: 30225+ [1au] A? example.com. (52)
   12  2019-10-23 20:58:45.819504 IP (tos 0x0, ttl 48, id 21512, offset 0, flags [none], proto UDP (17), length 112)
    *********.53 > *********.46225: 30225*- 1/0/1 example.com. A ************* (84)
   13  2019-10-23 20:58:46.829454 IP (tos 0x0, ttl 64, id 43211, offset 0, flags [none], proto UDP (17), length 74)
    *********.46225 > *********.53: 52688+ [1au] A? example.com. (46)
   14  2019-10-23 20:58:46.851441 IP (tos 0x0, ttl 48, id 43346, offset 0, flags [none], proto UDP (17), length 255)
    *********.53 > *********.46225: 52688*- 2/0/1 example.com. A *************, example.com. RRSIG (227)
   15  2019-10-23 20:58:47.860858 IP (tos 0x0, ttl 64, id 43235, offset 0, flags [none], proto UDP (17), length 83)
    *********.46225 > *********.53: 57808+ [1au] A? example.com. (55)
   16  2019-10-23 20:58:47.882669 IP (tos 0x0, ttl 48, id 64510, offset 0, flags [none], proto UDP (17), length 255)
    *********.53 > *********.46225: 57808*- 2/0/1 example.com. A *************, example.com. RRSIG (227)
   17  2019-10-23 20:58:48.892587 IP (tos 0x0, ttl 64, id 43380, offset 0, flags [none], proto UDP (17), length 89)
    *********.46225 > *********.53: 33054+ [1au] A? example.com. (61)
   18  2019-10-23 20:58:48.914406 IP (tos 0x0, ttl 48, id 20611, offset 0, flags [none], proto UDP (17), length 255)
    *********.53 > *********.46225: 33054*- 2/0/1 example.com. A *************, example.com. RRSIG (227)
   19  2019-10-23 20:58:49.924625 IP (tos 0x0, ttl 64, id 43587, offset 0, flags [none], proto UDP (17), length 74)
    *********.46225 > *********.53: 14353+ [1au] A? example.com. (46)
   20  2019-10-23 20:58:49.946523 IP (tos 0x0, ttl 48, id 42366, offset 0, flags [none], proto UDP (17), length 84)
    *********.53 > *********.46225: 14353*- 1/0/1 example.com. A ************* (56)
   21  2019-10-23 20:58:50.956601 IP (tos 0x0, ttl 64, id 43603, offset 0, flags [none], proto UDP (17), length 74)
    *********.46225 > *********.53: 17010+ [1au] A? example.com. (46)
   22  2019-10-23 20:58:50.978366 IP (tos 0x0, ttl 48, id 64034, offset 0, flags [none], proto UDP (17), length 255)
    *********.53 > *********.46225: 17010*- 2/0/1 example.com. A *************, example.com. RRSIG (227)
   23  2019-10-23 20:58:51.988387 IP (tos 0x0, ttl 64, id 43789, offset 0, flags [none], proto UDP (17), length 76)
    *********.46225 > *********.53: 3894+ [1au] A? example.com. (48)
   24  2019-10-23 20:58:52.010258 IP (tos 0x0, ttl 48, id 20169, offset 0, flags [none], proto UDP (17), length 255)
    *********.53 > *********.46225: 3894*- 2/0/1 example.com. A *************, example.com. RRSIG (227)
   25  2019-10-23 20:58:53.015716 IP (tos 0x0, ttl 64, id 43925, offset 0, flags [none], proto UDP (17), length 72)
    *********.46225 > *********.53: 8476+ [1au] A? example.com. (44)
   26  2019-10-23 20:58:53.037529 IP (tos 0x0, ttl 48, id 42142, offset 0, flags [none], proto UDP (17), length 84)
    *********.53 > *********.46225: 8476*- 1/0/1 example.com. A ************* (56)
   27  2019-10-23 20:58:54.047412 IP (tos 0x0, ttl 64, id 44065, offset 0, flags [none], proto UDP (17), length 76)
    *********.46225 > *********.53: 3966+ [1au] A? example.com. (48)
   28  2019-10-23 20:58:54.069358 IP (tos 0x0, ttl 48, id 64128, offset 0, flags [none], proto UDP (17), length 84)
    *********.53 > *********.46225: 3966*- 1/0/1 example.com. A ************* (56)
   29  2019-10-23 20:58:55.078435 IP (tos 0x0, ttl 64, id 44256, offset 0, flags [none], proto UDP (17), length 72)
    *********.46225 > *********.53: 26580+ [1au] A? example.com. (44)
   30  2019-10-23 20:58:55.100224 IP (tos 0x0, ttl 48, id 18983, offset 0, flags [none], proto UDP (17), length 84)
    *********.53 > *********.46225: 26580*- 1/0/1 example.com. A ************* (56)
   31  2019-10-23 20:58:56.110237 IP (tos 0x0, ttl 64, id 44374, offset 0, flags [none], proto UDP (17), length 82)
    *********.46225 > *********.53: 2190+ [1au] A? example.com. (54)
   32  2019-10-23 20:58:56.132070 IP (tos 0x0, ttl 48, id 39653, offset 0, flags [none], proto UDP (17), length 84)
    *********.53 > *********.46225: 2190*- 1/0/1 example.com. A ************* (56)
   33  2019-10-23 20:58:57.142215 IP (tos 0x0, ttl 64, id 44395, offset 0, flags [none], proto UDP (17), length 76)
    *********.46225 > *********.53: 16386+ [1au] A? example.com. (48)
   34  2019-10-23 20:58:57.164050 IP (tos 0x0, ttl 48, id 60845, offset 0, flags [none], proto UDP (17), length 84)
    *********.53 > *********.46225: 16386*- 1/0/1 example.com. A ************* (56)
   35  2019-10-23 20:58:58.174193 IP (tos 0x0, ttl 64, id 44643, offset 0, flags [none], proto UDP (17), length 78)
    *********.46225 > *********.53: 6373+ [1au] A? example.com. (50)
   36  2019-10-23 20:58:58.195911 IP (tos 0x0, ttl 48, id 16078, offset 0, flags [none], proto UDP (17), length 84)
    *********.53 > *********.46225: 6373*- 1/0/1 example.com. A ************* (56)
   37  2019-10-23 20:58:59.206181 IP (tos 0x0, ttl 64, id 44899, offset 0, flags [none], proto UDP (17), length 108)
    *********.46225 > *********.53: 29267+ [1au] A? example.com. (80)
   38  2019-10-23 20:58:59.228465 IP (tos 0x0, ttl 48, id 37094, offset 0, flags [none], proto UDP (17), length 112)
    *********.53 > *********.46225: 29267*- 1/0/1 example.com. A ************* (84)
   39  2019-10-23 20:59:00.238274 IP (tos 0x0, ttl 64, id 45092, offset 0, flags [none], proto UDP (17), length 90)
    *********.46225 > *********.53: 59326+ [1au] A? example.com. (62)
   40  2019-10-23 20:59:00.260209 IP (tos 0x0, ttl 48, id 58611, offset 0, flags [none], proto UDP (17), length 122)
    *********.53 > *********.46225: 59326*- 1/0/1 example.com. A ************* (94)
   41  2019-10-23 20:59:01.269390 IP (tos 0x0, ttl 64, id 45316, offset 0, flags [none], proto UDP (17), length 100)
    *********.46225 > *********.53: 17122+ [1au] A? example.com. (72)
   42  2019-10-23 20:59:01.291167 IP (tos 0x0, ttl 48, id 14192, offset 0, flags [none], proto UDP (17), length 255)
    *********.53 > *********.46225: 17122*- 2/0/1 example.com. A *************, example.com. RRSIG (227)
