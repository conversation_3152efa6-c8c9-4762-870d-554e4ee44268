# Danish translation of the sysstat package.
# Copyright (C) 2017 Free Software Foundation, Inc.
# This file is distributed under the same license as the sysstat package.
#
# <PERSON> <<EMAIL>>, 2007.
# <PERSON><PERSON> <<EMAIL>>, 2009.
# <PERSON> <<EMAIL>>, 2008, 2009, 2010, 2011, 2012, 2013, 2014, 2016, 2017.
#
# power management -> strømstyring
#
msgid ""
msgstr ""
"Project-Id-Version: sysstat-11.5.7\n"
"Report-Msgid-Bugs-To: sysstat <at> orange.fr\n"
"POT-Creation-Date: 2017-07-05 10:38+0200\n"
"PO-Revision-Date: 2017-07-06 16:19+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Danish <<EMAIL>>\n"
"Language: da\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: iostat.c:86 cifsiostat.c:70 sar.c:96 mpstat.c:126 pidstat.c:87
#: tapestat.c:95
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ]\n"
msgstr "Brug: %s [ tilvalg ] [ <interval> [ <count> ] ]\n"

#: iostat.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ] [ --debuginfo ]\n"
msgstr ""
"Tilvalg er:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | ETIKET | STI | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <gruppenavn> ] [ -p ] <enhed> [,...] | ALL ] ]\n"
"[ <enhed> [...] | ALL ] [ --debuginfo ]\n"

#: iostat.c:95
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ]\n"
msgstr ""
"Tilvalg er:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | ETIKET | STI | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <gruppenavn> ] [ -p [ <enhed> [,...] | ALL ] ]\n"
"[ <enhed> [...] | ALL ]\n"

#: iostat.c:326
#, c-format
msgid "Cannot find disk data\n"
msgstr "Kan ikke lokalisere diskdata\n"

#: iostat.c:1812 sa_common.c:1588
#, c-format
msgid "Invalid type of persistent device name\n"
msgstr "Ugyldig type vedvarende enhedsnavn\n"

#: sadc.c:89
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
msgstr "Brug: %s [ tilvalg ] [ <interval> [ <count> ] ] [ <uddatafil> ]\n"

#: sadc.c:92
#, c-format
msgid ""
"Options are:\n"
"[ -C <comment> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"
msgstr ""
"Tilvalg er:\n"
"[ -C <kommentar> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"

#: sadc.c:267
#, c-format
msgid "Cannot write data to system activity file: %s\n"
msgstr "Kan ikke skrive data til systemaktivitetsfil: %s\n"

#: sadc.c:563
#, c-format
msgid "Cannot write system activity file header: %s\n"
msgstr "Kan ikke skrive systemaktivitetsfilhoved: %s\n"

#: sadc.c:763 sadc.c:772 sadc.c:839 ioconf.c:510 rd_stats.c:69
#: sa_common.c:1204 count.c:118
#, c-format
msgid "Cannot open %s: %s\n"
msgstr "Kan ikke åbne %s: %s\n"

#: sadc.c:1019
#, c-format
msgid "Cannot append data to that file (%s)\n"
msgstr "Kan ikke tilføje data til den fil (%s)\n"

#: common.c:77
#, c-format
msgid "sysstat version %s\n"
msgstr "sysstat version %s\n"

#: cifsiostat.c:74
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"
msgstr ""
"Tilvalg er:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"

#: cifsiostat.c:77
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
msgstr ""
"Tilvalg er:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#: sadf.c:87
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <datafile> | -[0-9]+ ]\n"
msgstr "Brug: %s [ tilvalg ] [ <interval> [ <antal> ] ] [ <datafil> | -[0-9]+ ]\n"

#: sadf.c:90
#, c-format
msgid ""
"Options are:\n"
"[ -C ] [ -c | -d | -g | -j | -p | -r | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -O <opts> [,...] ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
"[ -- <sar_options> ]\n"
msgstr ""
"Tilvalg er:\n"
"[ -C ] [ -c | -d | -g | -j | -p | -r | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -O <tilv.> [,...] ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -s [ <tt:mm[:ss]> ] ] [ -e [ <tt:mm[:ss]> ] ]\n"
"[ -- <sar-tilvalg> ]\n"

#: pr_stats.c:2538 pr_stats.c:2549 pr_stats.c:2656 pr_stats.c:2667
msgid "Summary:"
msgstr "Resume:"

#: pr_stats.c:2591
msgid "Other devices not listed here"
msgstr "Andre enheder ikke vist her"

#: sar.c:111
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ MOUNT ] ] [ -H ] [ -h ]\n"
"[ -p ] [ -q ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --human ] [ --sadc ]\n"
"[ -I { <int_list> | SUM | ALL } ] [ -P { <cpu_list> | ALL } ]\n"
"[ -m { <keyword> [,...] | ALL } ] [ -n { <keyword> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filename> ] | -o [ <filename> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
msgstr ""
"Tilvalg:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ MOUNT ] ] [ -H ] [ -h ]\n"
"[ -p ] [ -q ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --human ] [ --sadc ]\n"
"[ -I { <int-liste> | SUM | ALL } ] [ -P { <cpu-liste> | ALL } ]\n"
"[ -m { <nøgleord> [,...] | ALL } ] [ -n { <nøgleord> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filnavn> ] | -o [ <filnavn> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <tt:mm[:ss]> ] ] [ -e [ <tt:mm[:ss]> ] ]\n"

#: sar.c:134
#, c-format
msgid "Main options and reports:\n"
msgstr "Vigtigste tilvalg og rapporter:\n"

#: sar.c:135
#, c-format
msgid "\t-B\tPaging statistics\n"
msgstr "\t-B\tPagingstatistik\n"

#: sar.c:136
#, c-format
msgid "\t-b\tI/O and transfer rate statistics\n"
msgstr "\t-b\tI/O og statistik for overførelsesrate\n"

#: sar.c:137
#, c-format
msgid "\t-d\tBlock devices statistics\n"
msgstr "\t-d\tStatistik for blokenheder\n"

#: sar.c:138
#, c-format
msgid "\t-F [ MOUNT ]\n"
msgstr "\t-F [ MOUNT ]\n"

#: sar.c:139
#, c-format
msgid "\t\tFilesystems statistics\n"
msgstr "\t\tStatistik for filsystemer\n"

#: sar.c:140
#, c-format
msgid "\t-H\tHugepages utilization statistics\n"
msgstr "\t-H\tForbrugsstatistik for store sider\n"

#: sar.c:141
#, c-format
msgid ""
"\t-I { <int_list> | SUM | ALL }\n"
"\t\tInterrupts statistics\n"
msgstr ""
"\t-I { <int-liste> | SUM | ALL }\n"
"\t\tStatistik for afbrydelser\n"

# clock ? tog fra KDE ksysguard (klok)
# øjeblikkelig klokfrekvens / "CPU-klokfrevens lige nu:"/"CPUens 
# klokfrekvens lige nu
# spændingsinddata eller spændingsindgange.
#: sar.c:143
#, c-format
msgid ""
"\t-m { <keyword> [,...] | ALL }\n"
"\t\tPower management statistics\n"
"\t\tKeywords are:\n"
"\t\tCPU\tCPU instantaneous clock frequency\n"
"\t\tFAN\tFans speed\n"
"\t\tFREQ\tCPU average clock frequency\n"
"\t\tIN\tVoltage inputs\n"
"\t\tTEMP\tDevices temperature\n"
"\t\tUSB\tUSB devices plugged into the system\n"
msgstr ""
"\t-m { <nøgleord> [,...] | ALL }\n"
"\t\tStatistik for strømstyring\n"
"\t\tNøgleord:\n"
"\t\tCPU\tCPU-klokfrekvens lige nu\n"
"\t\tFAN\tBlæserhastighed\n"
"\t\tFREQ\tGennemsnitlig CPU-klokfrekvens\n"
"\t\tIN\tSpændingsindgange\n"
"\t\tTEMP\tEnhedens temperatur\n"
"\t\tUSB\tUSB-enheder tilsluttet systemet\n"

#: sar.c:152
#, c-format
msgid ""
"\t-n { <keyword> [,...] | ALL }\n"
"\t\tNetwork statistics\n"
"\t\tKeywords are:\n"
"\t\tDEV\tNetwork interfaces\n"
"\t\tEDEV\tNetwork interfaces (errors)\n"
"\t\tNFS\tNFS client\n"
"\t\tNFSD\tNFS server\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP traffic\t(v4)\n"
"\t\tEIP\tIP traffic\t(v4) (errors)\n"
"\t\tICMP\tICMP traffic\t(v4)\n"
"\t\tEICMP\tICMP traffic\t(v4) (errors)\n"
"\t\tTCP\tTCP traffic\t(v4)\n"
"\t\tETCP\tTCP traffic\t(v4) (errors)\n"
"\t\tUDP\tUDP traffic\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP traffic\t(v6)\n"
"\t\tEIP6\tIP traffic\t(v6) (errors)\n"
"\t\tICMP6\tICMP traffic\t(v6)\n"
"\t\tEICMP6\tICMP traffic\t(v6) (errors)\n"
"\t\tUDP6\tUDP traffic\t(v6)\n"
"\t\tFC\tFibre channel HBAs\n"
"\t\tSOFT\tSoftware-based network processing\n"
msgstr ""
"\t-n { <nøgleord> [,...] | ALL }\n"
"\t\tNetværksstatistik\n"
"\t\tNøgleord er:\n"
"\t\tDEV\tNetværksgrænseflader\n"
"\t\tEDEV\tNetværksgrænseflader (fejl)\n"
"\t\tNFS\tNFS-klient\n"
"\t\tNFSD\tNFS-server\n"
"\t\tSOCK\tSokler\t(v4)\n"
"\t\tIP\tIP-trafik\t(v4)\n"
"\t\tEIP\tIP-trafik\t(v4) (fejl)\n"
"\t\tICMP\tICMP-trafik\t(v4)\n"
"\t\tEICMP\tICMP-trafik\t(v4) (fejl)\n"
"\t\tTCP\tTCP-trafik\t(v4)\n"
"\t\tETCP\tTCP-trafik\t(v4) (fejl)\n"
"\t\tUDP\tUDP-trafik\t(v4)\n"
"\t\tSOCK6\tSokler\t(v6)\n"
"\t\tIP6\tIP-trafik\t(v6)\n"
"\t\tEIP6\tIP-trafik\t(v6) (fejl)\n"
"\t\tICMP6\tICMP-trafik\t(v6)\n"
"\t\tEICMP6\tICMP-trafik\t(v6) (fejl)\n"
"\t\tUDP6\tUDP-trafik\t(v6)\n"
"\t\tFC\tFiberkanal-HBA'er\n"
"\t\tSOFT\tProgrambaseret netværksbehandling\n"

#: sar.c:175
#, c-format
msgid "\t-q\tQueue length and load average statistics\n"
msgstr "\t-q\tStatistik for kølængde og gennemsnitlig belastning\n"

#: sar.c:176
#, c-format
msgid ""
"\t-r [ ALL ]\n"
"\t\tMemory utilization statistics\n"
msgstr ""
"\t-r [ ALL ]\n"
"\t\tStatistik for hukommelsesforbrug\n"

#: sar.c:178
#, c-format
msgid "\t-S\tSwap space utilization statistics\n"
msgstr "\t-S\tStatistik for swappladsforbrug\n"

#: sar.c:179
#, c-format
msgid ""
"\t-u [ ALL ]\n"
"\t\tCPU utilization statistics\n"
msgstr ""
"\t-u [ ALL ]\n"
"\t\tStatistik for CPU-forbrug\n"

#: sar.c:181
#, c-format
msgid "\t-v\tKernel tables statistics\n"
msgstr "\t-v\tStatistik for kernetabeller\n"

#: sar.c:182
#, c-format
msgid "\t-W\tSwapping statistics\n"
msgstr "\t-W\tStatistik for swapping\n"

#: sar.c:183
#, c-format
msgid "\t-w\tTask creation and system switching statistics\n"
msgstr "\t-w\tOpgaveoprettelse og statistik for systemskift\n"

#: sar.c:184
#, c-format
msgid "\t-y\tTTY devices statistics\n"
msgstr "\t-y\tStatistik for TTY-enheder\n"

#: sar.c:198
#, c-format
msgid "Data collector will be sought in PATH\n"
msgstr "Dataindsamler vil blive søgt i STI\n"

#: sar.c:201
#, c-format
msgid "Data collector found: %s\n"
msgstr "Dataindsamler fundet: %s\n"

#: sar.c:260
#, c-format
msgid "End of data collecting unexpected\n"
msgstr "Uventet afslutning på dataindsamling\n"

# sar.c:
#: sar.c:358 mpstat.c:1672 pidstat.c:2397
msgid "Average:"
msgstr "Middel:"

#: sar.c:813
#, c-format
msgid "Using a wrong data collector from a different sysstat version\n"
msgstr "Bruger en forkert dataindsamler fra en anden sysstat version\n"

#: sar.c:865
#, c-format
msgid "Inconsistent input data\n"
msgstr "Inkonsistent inddata\n"

#: sar.c:1044 pidstat.c:239
#, c-format
msgid "Requested activities not available\n"
msgstr "Angivne aktiviteter er ikke tilgængelig\n"

#: sar.c:1351
#, c-format
msgid "-f and -o options are mutually exclusive\n"
msgstr "tilvalgene -f og -o udelukker hinanden\n"

#: sar.c:1357
#, c-format
msgid "Not reading from a system activity file (use -f option)\n"
msgstr "Læsning sker ikke fra en systemaktivitetsfil (brug tilvalget -f)\n"

#: sar.c:1493
#, c-format
msgid "Cannot find the data collector (%s)\n"
msgstr "Kan ikke lokalisere dataindsamleren (%s)\n"

#: mpstat.c:129
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -n ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -N { <node_list> | ALL } ] [ -o JSON ] [ -P { <cpu_list> | ON | ALL } ]\n"
msgstr ""
"Tilvalg er:\n"
"[ -A ] [ -n ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -N { <node_list> | ALL } ] [ -o JSON ] [ -P { <cpu-liste> | ON | ALL } ]\n"

#: sa_conv.c:69
#, c-format
msgid "Cannot convert the format of this file\n"
msgstr "Kan ikke konvertere formatet for denne fil\n"

#: sa_conv.c:562
#, c-format
msgid ""
"\n"
"CPU activity not found in file. Aborting...\n"
msgstr ""
"\n"
"Cpu-aktivitet blev ikke fundet i filen. Afbryder ...\n"

#: sa_conv.c:578
#, c-format
msgid ""
"\n"
"Invalid data found. Aborting...\n"
msgstr ""
"\n"
"Ugyldige data fundet. Afbryder ...\n"

#: sa_conv.c:897
#, c-format
msgid "Statistics: "
msgstr "Statistik: "

#: sa_conv.c:1016
#, c-format
msgid ""
"\n"
"File successfully converted to sysstat format version %s\n"
msgstr ""
"\n"
"Fil blev konverteret til sysstats formatversion %s\n"

#: pidstat.c:90
#, c-format
msgid ""
"Options are:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ] [ -U [ <username> ] ]\n"
"[ -u ] [ -V ] [ -v ] [ -w ] [ -C <command> ] [ -G <process_name> ] [ --human ]\n"
"[ -p { <pid> [,...] | SELF | ALL } ] [ -T { TASK | CHILD | ALL } ]\n"
msgstr ""
"Tilvalg er:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ] [ -U [ <brugernavn> ] ]\n"
"[ -u ] [ -V ] [ -v ] [ -w ] [ -C <kommando> ] [ -G <procesnavn> ] [ --human ]\n"
"[ -p { <pid> [,...] | SELF | ALL } ] [ -T { TASK | CHILD | ALL } ]\n"

#: sa_common.c:1000
#, c-format
msgid "Error while reading system activity file: %s\n"
msgstr "Læsefejl ved indlæsning af aktivitetsfil: %s\n"

#: sa_common.c:1010
#, c-format
msgid "End of system activity file unexpected\n"
msgstr "Uventet afslutning på systemaktivitetsfil\n"

#: sa_common.c:1029
#, c-format
msgid "File created by sar/sadc from sysstat version %d.%d.%d"
msgstr "Fil oprettet af sar/sadc fra sysstat version %d.%d.%d"

#: sa_common.c:1062
#, c-format
msgid "Invalid system activity file: %s\n"
msgstr "Ugyldig systemaktivitetsfil: %s\n"

#: sa_common.c:1074
#, c-format
msgid "Endian format mismatch\n"
msgstr "Endian-formater er ikke ens\n"

#: sa_common.c:1078
#, c-format
msgid "Current sysstat version cannot read the format of this file (%#x)\n"
msgstr "Nuværende sysstat-version kan ikke læse formatet på denne fil (%#x)\n"

#: sa_common.c:1207
#, c-format
msgid "Please check if data collecting is enabled\n"
msgstr "Kontroller venligst om dataindsamling er aktiveret\n"

#: sa_common.c:1400
#, c-format
msgid "Requested activities not available in file %s\n"
msgstr "Angivne aktiviteter findes ikke i filen %s\n"

#: tapestat.c:97
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"
msgstr ""
"Tilvalg er:\n"
"[ --human ] [ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"

#: tapestat.c:263
#, c-format
msgid "No tape drives with statistics found\n"
msgstr "Ingen bånddrev med statistik blev fundet\n"

#: count.c:169
#, c-format
msgid "Cannot handle so many processors!\n"
msgstr "Kan ikke håndtere så mange cpuer!\n"

#: sadf_misc.c:834
#, c-format
msgid "System activity data file: %s (%#x)\n"
msgstr "Systemaktivitetsdatafil: %s (%#x)\n"

#: sadf_misc.c:843
#, c-format
msgid "Genuine sa datafile: %s (%x)\n"
msgstr "Ægte sa-datafil: %s (%x)\n"

#: sadf_misc.c:844
msgid "no"
msgstr "nej"

#: sadf_misc.c:844
msgid "yes"
msgstr "ja"

#: sadf_misc.c:847
#, c-format
msgid "Host: "
msgstr "Vært: "

#: sadf_misc.c:854
#, c-format
msgid "Number of CPU for last samples in file: %u\n"
msgstr "Antallet af CPU for sidste prøver i fil: %u\n"

#: sadf_misc.c:860
#, c-format
msgid "File date: %s\n"
msgstr "Fildato: %s\n"

#: sadf_misc.c:863
#, c-format
msgid "File time: "
msgstr "Filtid: "

#: sadf_misc.c:868
#, c-format
msgid "Size of a long int: %d\n"
msgstr "Størrelse på en lang int: %d\n"

#: sadf_misc.c:874
#, c-format
msgid "List of activities:\n"
msgstr "Oversigt over aktiviteter:\n"

# evt. "Ukendt format for aktivitet"
#: sadf_misc.c:887
#, c-format
msgid "\t[Unknown activity format]"
msgstr "\t[Ukendt aktivitetsformat]"

#~ msgid "\t-R\tMemory statistics\n"
#~ msgstr "\t-R\tHukommelsesstatistik\n"
