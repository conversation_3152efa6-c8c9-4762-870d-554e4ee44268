.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_COMPILE 3PCAP "29 May 2025"
.SH NAME
pcap_compile \- compile a filter expression
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
int pcap_compile(pcap_t *p, struct bpf_program *fp,
    const char *str, int optimize, bpf_u_int32 netmask);
.ft
.fi
.SH DESCRIPTION
.BR pcap_compile ()
is used to compile the string
.I str
into a filter program.  See
.BR \%pcap-filter (@MAN_MISC_INFO@)
for the syntax of that string.
.I fp
is a pointer to a
.I bpf_program
struct and is filled in by
.BR pcap_compile ().
.I optimize
controls whether optimization on the resulting code is performed.
.I netmask
specifies the IPv4 netmask (in host byte order) of the network on which
packets are being
captured; it is used only when checking for IPv4 broadcast addresses in
the filter program.  If the netmask of the network on which packets are
being captured isn't known to the program, or if the network interface
does not have exactly one IPv4 netmask, or if packets are being
captured on the Linux "any" pseudo-interface, which can capture on more
than one network interface, a value of
.B PCAP_NETMASK_UNKNOWN
can be supplied; in this case an attempt to compile a filter expression
that contains the
.B "ip broadcast"
primitive will fail.
.PP
On Linux, if the
.B pcap_t
handle corresponds to a live packet capture, the resulting filter program
may use Linux BPF extensions.  This works transparently if the filter
program is used to filter packets on the same
.B pcap_t
handle, which should be done when possible.  In other use cases trying to
use a filter program with BPF extensions in
.BR \%pcap_offline_filter (3PCAP)
or for filtering an input savefile would reject more packets than expected
because the extensions depend on auxiliary packet data, which would not be
available.  The workaround is to compile the filter without the extensions
by using a
.B pcap_t
handle from
.BR \%pcap_open_dead (3PCAP)
or
.BR \%pcap_open_offline (3PCAP)
rather than a handle from
.BR \%pcap_create (3PCAP)
or
.BR \%pcap_open_live (3PCAP).
.PP
If BPF extensions are disabled as described above or the OS is not Linux,
.BR pcap_compile ()
may start rejecting some filter expressions for some link-layer header types,
this is the expected behaviour.  For example, the
.B ifindex
keyword is valid for any live capture on Linux, but when reading packets
from a savefile, regardless of the OS it is valid for
.B DLT_LINUX_SLL2
only.
.SH RETURN VALUE
.BR pcap_compile ()
returns
.B 0
on success and
.B PCAP_ERROR
on failure. If
.B PCAP_ERROR
is returned,
.BR pcap_geterr (3PCAP)
or
.BR pcap_perror (3PCAP)
may be called with
.I p
as an argument to fetch or display the error text.
.SH BACKWARD COMPATIBILITY
.PP
The
.B PCAP_NETMASK_UNKNOWN
constant became available in libpcap release 1.1.0.
.PP
In libpcap 1.8.0 and later,
.BR pcap_compile ()
can be used in multiple threads within a single process.  However, in
earlier versions of libpcap, it is
.I not
safe to use
.BR pcap_compile ()
in multiple threads in a single process without some form of mutual
exclusion allowing only one thread to call it at any given time.
.SH SEE ALSO
.BR pcap (3PCAP),
.BR pcap_setfilter (3PCAP),
.BR pcap_freecode (3PCAP)
