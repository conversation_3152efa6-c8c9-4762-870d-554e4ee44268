    1  1970-01-01 00:00:00.000000 00:50:56:b4:08:69 > 00:50:56:b4:4c:2a, ethertype IPv4 (0x0800), length 920: (tos 0x0, ttl 64, id 27576, offset 0, flags [DF], proto TCP (6), length 906)
    ***********.33926 > ************.6379: Flags [P.], cksum 0xa129 (incorrect -> 0xaaa0), seq 3839414413:3839415267, ack 2526552240, win 229, options [nop,nop,TS val 2407226 ecr 24894817], length 854: RESP length negative and not -1 invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid "4" "EVAL" invalid invalid invalid invalid "GKMbNZq^@0" "stuubt.pack('<ivdMFG4294967245',^V ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''319', 2',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',',', '-1494241318543828858')'L')N))'r')')~D')')E)')')')')')')')'l')')')')')'M-`'o')')'Pp)U)" invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid invalid "1" [|resp]
