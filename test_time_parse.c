#include <stdio.h>
#include <time.h>
#include <string.h>

int main() {
    char line[] = "2025-07-18 21:26:16 181771 [Warning] Aborted connection 181771 to db: 'unconnected' user: 'unauthenticated' host: '**************' (This connection closed normally without authentication)";
    
    struct tm tm_log = {0};
    time_t current_time = time(NULL);
    time_t sixty_seconds_ago = current_time - 60;
    
    printf("当前时间: %ld (%s", current_time, ctime(&current_time));
    printf("60秒前: %ld (%s", sixty_seconds_ago, ctime(&sixty_seconds_ago));
    printf("\n");
    
    // 解析日志时间
    if (sscanf(line, "%d-%d-%d %d:%d:%d",
              &tm_log.tm_year, &tm_log.tm_mon, &tm_log.tm_mday,
              &tm_log.tm_hour, &tm_log.tm_min, &tm_log.tm_sec) == 6) {
        
        printf("解析到的时间: %d-%d-%d %d:%d:%d\n",
               tm_log.tm_year, tm_log.tm_mon, tm_log.tm_mday,
               tm_log.tm_hour, tm_log.tm_min, tm_log.tm_sec);
        
        tm_log.tm_year -= 1900;  // 年份从1900开始
        tm_log.tm_mon -= 1;      // 月份从0开始
        time_t log_time = mktime(&tm_log);
        
        printf("转换后的时间戳: %ld (%s", log_time, ctime(&log_time));
        printf("时间差: %ld 秒\n", current_time - log_time);
        
        if (log_time < sixty_seconds_ago) {
            printf("❌ 这条日志会被跳过（太旧）\n");
        } else {
            printf("✅ 这条日志会被处理（在60秒内）\n");
        }
    } else {
        printf("❌ 时间解析失败\n");
    }
    
    return 0;
}
