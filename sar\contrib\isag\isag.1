.\" Automatically generated by Pod::Man version 1.02
.\" Mon Mar 22 20:19:18 2004
.\"
.\" Standard preamble:
.\" ======================================================================
.de Sh \" Subsection heading
.br
.if t .Sp
.ne 5
.PP
\fB\\$1\fR
.PP
..
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Ip \" List item
.br
.ie \\n(.$>=3 .ne \\$3
.el .ne 3
.IP "\\$1" \\$2
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R

.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  | will give a
.\" real vertical bar.  \*(C+ will give a nicer C++.  Capital omega is used
.\" to do unbreakable dashes and therefore won't be available.  \*(C` and
.\" \*(C' expand to `' in nroff, nothing in troff, for use with C<>
.tr \(*W-|\(bv\*(Tr
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` `
.    ds C' '
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
'br\}
.\"
.\" If the F register is turned on, we'll generate index entries on stderr
.\" for titles (.TH), headers (.SH), subsections (.Sh), items (.Ip), and
.\" index entries marked with X<> in POD.  Of course, you'll have to process
.\" the output yourself in some meaningful fashion.
.if \nF \{\
.    de IX
.    tm Index:\\$1\t\\n%\t"\\$2"
.    .
.    nr % 0
.    rr F
.\}
.\"
.\" For nroff, turn off justification.  Always turn off hyphenation; it
.\" makes way too many mistakes in technical documents.
.hy 0
.if n .na
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.bd B 3
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ======================================================================
.\"
.IX Title "ISAG 1"
.TH ISAG 1 "0.81.0" "March 2004" "System Activity Grapher"
.UC
.SH "NAME"
isag \- Interactive System Activity Grapher
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
isag [\-p datafiles_path] [\-c config_file] [\-ght gr_height] [\-gwd gr_width]
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
The \fIisag\fR command graphically displays the system activity data stored
in a binary data file by a previous \fIsar\fR run. The \fIisag\fR command invokes
\&\fIsar\fR to extract the data to be plotted.
.PP
The data are processed using \fIsar\fR command and slightly transformed
to tabular format, and then this format is visualized using \fIgnuplot\fR
program.
.SH "OPTIONS"
.IX Header "OPTIONS"
.Ip "\-p datafiles_path" 4
.IX Item "-p datafiles_path"
Specify the pathname where are located the daily data files.
Default path is: \fI/var/log/sa\fR
.Ip "\-c config_file" 4
.IX Item "-c config_file"
Specify the configuration file used by the \fIisag\fR command. 
The contents of this file may depend on \fIisag\fR version number.
Default config file is: \fI$HOME/.isag.cfg\fR.
.Ip "\-ght gr_height" 4
.IX Item "-ght gr_height"
Specify the height of the chart area. 
Default value is: \fI400\fR.
.Ip "\-gwd gr_width" 4
.IX Item "-gwd gr_width"
Specify the width of the chart area. 
Default value is: \fI720\fR.
.SH "CONFIG FILE"
.IX Header "CONFIG FILE"
As mentioned above there is a config file. There are stored following values:
.Ip "last showed graph" 2
.IX Item "last showed graph"
.Ip "y limits for each kind of graph" 2
.IX Item "y limits for each kind of graph"
.PP
It seems usefull, because new run doesn't need new settings to obtain same scale.
.SH "PREREQUSITIES"
.IX Header "PREREQUSITIES"
Here is list of prerequsities including versioning and built-in features.
.Ip "Tcl/Tk"
.IX Item "Tcl/Tk"
Version 8.0 or newer.
.Ip "gnuplot"
.IX Item "gnuplot"
Gnuplot must have a \fBtkcanvas\fR display.
.SH "AUTHOR"
.IX Header "AUTHOR"
D. Doubrava 2000, 2002 e-mail:\ linux_monitor(at)volny(dot)cz
.PP
\&\s-1HTTP\s0 Site: http://www.volny.cz/linux_monitor/isag/index.html
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBsar\fR (1), \fBsadc\fR (8).
