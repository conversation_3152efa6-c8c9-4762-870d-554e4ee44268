README file for the isag - Interactive System Activity Graph - command.

isag is a command that enables you to plot data stored in a daily data file
by a previous sar run.
isag needs a recent version of Tcl/Tk installed, together with the
gnuplot plotting program.

isag is (C) 2000,2001 by <PERSON> <<EMAIL>>.
Send bug reports to <<EMAIL>>.

Note that the path to daily data files is hard coded in isag and
its value is "/var/log/sa".
Also isag assumes that sar is installed in /usr/bin directory.
Update isag script if sar is located elsewhere.

--
<PERSON><PERSON><PERSON> (sysstat <at> wanadoo.fr)

