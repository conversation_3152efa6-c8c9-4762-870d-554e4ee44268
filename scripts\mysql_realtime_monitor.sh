#!/bin/bash

# MySQL实时连接监控脚本
# 持续监控并每隔指定时间显示统计信息

GENERAL_LOG="/tmp/mysql-logs/general.log"
INTERVAL=${1:-10}  # 默认每10秒统计一次

echo "========================================="
echo "MySQL实时连接监控"
echo "========================================="
echo "统计间隔: ${INTERVAL}秒"
echo "日志文件: ${GENERAL_LOG}"
echo "按 Ctrl+C 停止监控"
echo "========================================="

# 检查日志文件
if [ ! -f "$GENERAL_LOG" ]; then
    echo "错误: 通用日志文件不存在: $GENERAL_LOG"
    exit 1
fi

# 信号处理
trap 'echo -e "\n监控已停止"; exit 0' INT

# 初始化计数器
LAST_POSITION=$(stat -c%s "$GENERAL_LOG")

echo "时间\t\t连接/秒\t断开/秒\t查询/秒\t活跃连接\t主要客户端IP"
echo "=============================================================================="

while true; do
    sleep $INTERVAL
    
    # 获取当前时间和日志位置
    CURRENT_TIME=$(date '+%H:%M:%S')
    CURRENT_POSITION=$(stat -c%s "$GENERAL_LOG")
    
    # 如果日志文件没有增长，跳过这次统计
    if [ $CURRENT_POSITION -le $LAST_POSITION ]; then
        printf "%s\t%s\t%s\t%s\t%s\t%s\n" "$CURRENT_TIME" "0.0" "0.0" "0.0" "N/A" "无活动"
        continue
    fi
    
    # 提取新增的日志内容
    tail -c +$((LAST_POSITION + 1)) "$GENERAL_LOG" > /tmp/mysql_realtime_temp.log
    
    # 统计各种事件
    CONNECT_COUNT=$(grep -c "Connect" /tmp/mysql_realtime_temp.log)
    QUIT_COUNT=$(grep -c "Quit" /tmp/mysql_realtime_temp.log)
    QUERY_COUNT=$(grep -c "Query" /tmp/mysql_realtime_temp.log)
    
    # 计算速率
    CONNECT_RATE=$(echo "scale=1; $CONNECT_COUNT / $INTERVAL" | bc -l)
    QUIT_RATE=$(echo "scale=1; $QUIT_COUNT / $INTERVAL" | bc -l)
    QUERY_RATE=$(echo "scale=1; $QUERY_COUNT / $INTERVAL" | bc -l)
    
    # 获取当前活跃连接数
    ACTIVE_CONNECTIONS=$(mysql -u root -e "SHOW STATUS LIKE 'Threads_connected';" 2>/dev/null | tail -n 1 | awk '{print $2}')
    if [ -z "$ACTIVE_CONNECTIONS" ]; then
        ACTIVE_CONNECTIONS="N/A"
    fi
    
    # 获取主要客户端IP
    TOP_CLIENT=$(grep "Connect" /tmp/mysql_realtime_temp.log | \
                sed -n 's/.*Connect.*@\([^[:space:]]*\).*/\1/p' | \
                sort | uniq -c | sort -nr | head -1 | awk '{print $2}')
    
    if [ -z "$TOP_CLIENT" ]; then
        TOP_CLIENT="无"
    fi
    
    # 显示统计信息
    printf "%s\t%s\t%s\t%s\t%s\t\t%s\n" \
           "$CURRENT_TIME" "$CONNECT_RATE" "$QUIT_RATE" "$QUERY_RATE" "$ACTIVE_CONNECTIONS" "$TOP_CLIENT"
    
    # 更新位置
    LAST_POSITION=$CURRENT_POSITION
    
    # 清理临时文件
    rm -f /tmp/mysql_realtime_temp.log
done
