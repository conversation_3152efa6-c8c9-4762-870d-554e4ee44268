/* cmakeconfig.h.in */

/* Define to 1 if arpa/inet.h declares `ether_hostton' */
#cmakedefine ARPA_INET_H_DECLARES_ETHER_HOSTTON 1

/* Enable optimizer debugging */
#cmakedefine BDEBUG 1

/* Define to 1 if remote packet capture is to be supported */
#cmakedefine ENABLE_REMOTE 1

/* define if we have the AIX getnetbyname_r() */
#cmakedefine HAVE_AIX_GETNETBYNAME_R 1

/* define if we have the AIX getprotobyname_r() */
#cmakedefine HAVE_AIX_GETPROTOBYNAME_R 1

/* Define to 1 if you have the `asprintf' function. */
#cmakedefine HAVE_ASPRINTF 1

/* define if you have the DAG API */
#cmakedefine HAVE_DAG_API 1

/* define if you have vdag_set_device_info() */
#cmakedefine HAVE_DAG_VDAG 1

/* Define to 1 if DAG transmit support is enabled */
#cmakedefine ENABLE_DAG_TX 1

/* Define to 1 if you have the declaration of `ether_hostton' */
#cmakedefine HAVE_DECL_ETHER_HOSTTON 1

/* Define to 1 if `dl_module_id_1' is a member of `dl_hp_ppa_info_t'. */
#cmakedefine HAVE_DL_HP_PPA_INFO_T_DL_MODULE_ID_1 1

/* Define to 1 if the system has the type `dl_passive_req_t'. */
#cmakedefine HAVE_DL_PASSIVE_REQ_T 1

/* Define to 1 if you have the `ether_hostton' function. */
#cmakedefine HAVE_ETHER_HOSTTON 1

/* Define to 1 if fseeko (and presumably ftello) exists and is declared. */
#cmakedefine HAVE_FSEEKO 1

/* Define to 1 if you have the `getspnam' function. */
#cmakedefine HAVE_GETSPNAM 1

/* Define to 1 if using GNU libc. */
#cmakedefine HAVE_GLIBC 1

/* Define to 1 if you have a GNU-style `strerror_r' function. */
#cmakedefine HAVE_GNU_STRERROR_R 1

/* on HP-UX 10.20 or later */
#cmakedefine HAVE_HPUX10_20_OR_LATER 1

/* if libdlpi exists */
#cmakedefine HAVE_LIBDLPI 1

/* if libnl exists */
#cmakedefine HAVE_LIBNL 1

/* Define to 1 if you have the <linux/compiler.h> header file. */
#cmakedefine HAVE_LINUX_COMPILER_H 1

/* define if we have the Linux getnetbyname_r() */
#cmakedefine HAVE_LINUX_GETNETBYNAME_R 1

/* define if we have the Linux getprotobyname_r() */
#cmakedefine HAVE_LINUX_GETPROTOBYNAME_R 1

/* Define to 1 if you have the <linux/net_tstamp.h> header file. */
#cmakedefine HAVE_LINUX_NET_TSTAMP_H 1

/* Define to 1 if you have the <linux/usbdevice_fs.h> header file. */
#cmakedefine HAVE_LINUX_USBDEVICE_FS_H 1

/* Define to 1 if you have the <net/bpf.h> header file. */
#cmakedefine HAVE_NET_BPF_H 1

/* Define to 1 if you have the <net/if_media.h> header file. */
#cmakedefine HAVE_NET_IF_MEDIA_H 1

/* Use OpenSSL */
#cmakedefine HAVE_OPENSSL 1

/* if there's an os-proto.h for this platform, to use additional prototypes */
#cmakedefine HAVE_OS_PROTO_H 1

/* Define to 1 if Packet32 API (Npcap driver) is available */
#cmakedefine HAVE_PACKET32 1

/* Define to 1 if Npcap BPF extension definitions are available */
#cmakedefine HAVE_NPCAP_BPF_H 1

/* Define to 1 if NPcap's version.h is available */
#cmakedefine HAVE_VERSION_H 1

/* Define to 1 if you have a POSIX-style `strerror_r' function. */
#cmakedefine HAVE_POSIX_STRERROR_R 1

/* define if you have the Myricom SNF API */
#cmakedefine HAVE_SNF_API 1

/* Define to 1 if you have the `snprintf' function. */
#cmakedefine HAVE_SNPRINTF 1

/* Define to 1 if the system has the type `socklen_t'. */
#cmakedefine HAVE_SOCKLEN_T 1

/* On Solaris */
#cmakedefine HAVE_SOLARIS 1

/* target host supports Solaris "any" device */
#cmakedefine HAVE_SOLARIS_ANY_DEVICE 1

/* define if we have the Solaris getnetbyname_r() */
#cmakedefine HAVE_SOLARIS_GETNETBYNAME_R 1

/* define if we have the Solaris getprotobyname_r() */
#cmakedefine HAVE_SOLARIS_GETPROTOBYNAME_R 1

/* Define to 1 if you have the `strlcat' function. */
#cmakedefine HAVE_STRLCAT 1

/* Define to 1 if you have the `strlcpy' function. */
#cmakedefine HAVE_STRLCPY 1

/* Define to 1 if you have the `strtok_r' function. */
#cmakedefine HAVE_STRTOK_R 1

/* Define to 1 if the system has the type `struct BPF_TIMEVAL'. */
#cmakedefine HAVE_STRUCT_BPF_TIMEVAL 1

/* Define to 1 if the system has the type `struct ether_addr'. */
#cmakedefine HAVE_STRUCT_ETHER_ADDR 1

/* Define to 1 if `msg_control' is a member of `struct msghdr'. */
#cmakedefine HAVE_STRUCT_MSGHDR_MSG_CONTROL 1

/* Define to 1 if `msg_flags' is a member of `struct msghdr'. */
#cmakedefine HAVE_STRUCT_MSGHDR_MSG_FLAGS 1

/* Define to 1 if the system has the type `struct rte_ether_addr'. */
#cmakedefine HAVE_STRUCT_RTE_ETHER_ADDR 1

/* Define to 1 if `hci_channel' is a member of `struct sockaddr_hci'. */
#cmakedefine HAVE_STRUCT_SOCKADDR_HCI_HCI_CHANNEL 1

/* Define to 1 if `sa_len' is a member of `struct sockaddr'. */
#cmakedefine HAVE_STRUCT_SOCKADDR_SA_LEN 1

/* Define to 1 if `tp_vlan_tci' is a member of `struct tpacket_auxdata'. */
#cmakedefine HAVE_STRUCT_TPACKET_AUXDATA_TP_VLAN_TCI 1

/* Define to 1 if you have the declaration of `SKF_AD_VLAN_TAG_PRESENT', and
   to 0 if you don't. */
#cmakedefine01 HAVE_DECL_SKF_AD_VLAN_TAG_PRESENT

/* Define to 1 if `bRequestType' is a member of `struct
   usbdevfs_ctrltransfer'. */
#cmakedefine HAVE_STRUCT_USBDEVFS_CTRLTRANSFER_BREQUESTTYPE 1

/* Define to 1 if you have the <sys/bufmod.h> header file. */
#cmakedefine HAVE_SYS_BUFMOD_H 1

/* Define to 1 if you have the <sys/dlpi_ext.h> header file. */
#cmakedefine HAVE_SYS_DLPI_EXT_H 1

/* Define to 1 if you have the <sys/dlpi.h> header file. */
#cmakedefine HAVE_SYS_DLPI_H 1

/* Define to 1 if you have the <sys/ioccom.h> header file. */
#cmakedefine HAVE_SYS_IOCCOM_H 1

/* Define to 1 if using uclibc(-ng). */
#cmakedefine HAVE_UCLIBC 1

/* Define to 1 if you have the <unistd.h> header file. */
#cmakedefine HAVE_UNISTD_H 1

/* Define to 1 if you have the `vasprintf' function. */
#cmakedefine HAVE_VASPRINTF 1

/* Define to 1 if you have the `vsnprintf' function. */
#cmakedefine HAVE_VSNPRINTF 1

/* Define to 1 if you have the `vsyslog' function. */
#cmakedefine HAVE_VSYSLOG 1

/* Define to 1 if you have the `_wcserror_s' function. */
#cmakedefine HAVE__WCSERROR_S 1

/* define if __atomic_load_n is supported by the compiler */
#cmakedefine HAVE___ATOMIC_LOAD_N 1

/* define if __atomic_store_n is supported by the compiler */
#cmakedefine HAVE___ATOMIC_STORE_N 1

/* Define to 1 if you have the `PacketGetTimestampModes' function. */
#cmakedefine HAVE_PACKET_GET_TIMESTAMP_MODES 1

/* Define to 1 if you have the `PacketIsLoopbackAdapter' function. */
#cmakedefine HAVE_PACKET_IS_LOOPBACK_ADAPTER 1

/* Define to 1 if you have the `PacketGetInfo' function. */
#cmakedefine HAVE_PACKET_GET_INFO 1

/* Define to 1 if netinet/ether.h declares `ether_hostton' */
#cmakedefine NETINET_ETHER_H_DECLARES_ETHER_HOSTTON 1

/* Define to 1 if netinet/if_ether.h declares `ether_hostton' */
#cmakedefine NETINET_IF_ETHER_H_DECLARES_ETHER_HOSTTON 1

/* Define to 1 if net/ethernet.h declares `ether_hostton' */
#cmakedefine NET_ETHERNET_H_DECLARES_ETHER_HOSTTON 1

/* do not use protochain */
#cmakedefine NO_PROTOCHAIN 1

/* Define to the address where bug reports for this package should be sent. */
#cmakedefine PACKAGE_BUGREPORT "@PACKAGE_BUGREPORT@"

/* Define to the DLL-preferred version string of this package. */
#cmakedefine PACKAGE_VERSION_DLL @PACKAGE_VERSION_DLL@

/* Define to the full name of this package. */
#cmakedefine PACKAGE_NAME "@PACKAGE_NAME@"

/* Define to the full name and version of this package. */
#cmakedefine PACKAGE_STRING "@PACKAGE_STRING@"

/* Define to the one symbol short name of this package. */
#cmakedefine PACKAGE_TARNAME "@PACKAGE_TARNAME@"

/* Define to the home page for this package. */
#cmakedefine PACKAGE_URL "@PACKAGE_URL@"

/* Define to the version of this package. */
#cmakedefine PACKAGE_VERSION "@PACKAGE_VERSION@"

/* target host supports Bluetooth sniffing */
#cmakedefine PCAP_SUPPORT_BT 1

/* target host supports Bluetooth Monitor */
#cmakedefine PCAP_SUPPORT_BT_MONITOR 1

/* support D-Bus sniffing */
#cmakedefine PCAP_SUPPORT_DBUS 1

/* target host supports DPDK */
#cmakedefine PCAP_SUPPORT_DPDK 1

/* target host supports Linux usbmon for USB sniffing */
#cmakedefine PCAP_SUPPORT_LINUX_USBMON 1

/* target host supports netfilter sniffing */
#cmakedefine PCAP_SUPPORT_NETFILTER 1

/* target host supports netmap */
#cmakedefine PCAP_SUPPORT_NETMAP 1

/* target host supports RDMA sniffing */
#cmakedefine PCAP_SUPPORT_RDMASNIFF 1

/* The size of `time_t', as computed by sizeof. */
#cmakedefine SIZEOF_TIME_T @SIZEOF_TIME_T@

/* The size of `void *', as computed by sizeof. */
#cmakedefine SIZEOF_VOID_P @SIZEOF_VOID_P@

/* Define to 1 if sys/ethernet.h declares `ether_hostton' */
#cmakedefine SYS_ETHERNET_H_DECLARES_ETHER_HOSTTON 1

/* Enable parser debugging */
#cmakedefine YYDEBUG 1

/* Enable large inode numbers on Mac OS X 10.5.  */
#ifndef _DARWIN_USE_64_BIT_INODE
# define _DARWIN_USE_64_BIT_INODE 1
#endif

/* Number of bits in a file offset, on hosts where this is settable. */
#cmakedefine _FILE_OFFSET_BITS 1

/* Define to 1 to make fseeko visible on some hosts (e.g. glibc 2.2). */
#cmakedefine _LARGEFILE_SOURCE 1

/* Define for large files, on AIX-style hosts. */
#cmakedefine _LARGE_FILES 1
