# Polish NLS support for the sysstat package.
# Copyright (C) 1999 <PERSON><PERSON><PERSON><PERSON> <sysstat [at] orange.fr> (msgids).
# This file is distributed under the same license as the sysstat package.
#
# <PERSON> <<EMAIL>>, 2008 - 2017.
msgid ""
msgstr ""
"Project-Id-Version: sysstat 11.6.0\n"
"Report-Msgid-Bugs-To: sysstat <at> orange.fr\n"
"POT-Creation-Date: 2017-08-14 08:32+0200\n"
"PO-Revision-Date: 2017-08-23 21:35+0100\n"
"Last-Translator: <PERSON> <robe<PERSON>@debian.org>\n"
"Language-Team: Polish <<EMAIL>>\n"
"Language: pl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"X-Generator: Lokalize 2.0\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#: iostat.c:86 cifsiostat.c:70 mpstat.c:126 sar.c:96 tapestat.c:95
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ]\n"
msgstr "Użycie: %s [ opcje ] [ <interwał> [ <liczba> ] ]\n"

#: iostat.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ] [ --debuginfo ]\n"
msgstr ""
"Opcje to:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <nazwa_grupy> ] [ -p [ <urządzenie> [,...] | ALL ] ]\n"
"[ <urządzenie> [...] | ALL ] [ --debuginfo ]\n"

#: iostat.c:95
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ]\n"
msgstr ""
"Opcje to:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <nazwa_grupy> ] [ -p [ <urządzenie> [,...] | ALL ] ]\n"
"[ <urządzenie> [...] | ALL ]\n"

#: iostat.c:326
#, c-format
msgid "Cannot find disk data\n"
msgstr "Nie można znaleźć danych o dyskach\n"

#: iostat.c:1812 sa_common.c:1590
#, c-format
msgid "Invalid type of persistent device name\n"
msgstr "Niepoprawny typ trwałej nazwy urządzenia\n"

#: sadc.c:89
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
msgstr "Użycie: %s [ opcje ] [ <interwał> [ <liczba> ] ] [ <plik_wyjściowy> ]\n"

#: sadc.c:92
#, c-format
msgid ""
"Options are:\n"
"[ -C <comment> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"
msgstr ""
"Opcje to:\n"
"[ -C <komentarz> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"

#: sadc.c:267
#, c-format
msgid "Cannot write data to system activity file: %s\n"
msgstr "Nie można zapisać danych do pliku aktywności systemu: %s\n"

#: sadc.c:563
#, c-format
msgid "Cannot write system activity file header: %s\n"
msgstr "Nie można zapisać nagłówka pliku aktywności systemu: %s\n"

#: sadc.c:763 sadc.c:772 sadc.c:839 ioconf.c:510 rd_stats.c:69
#: sa_common.c:1204 count.c:118
#, c-format
msgid "Cannot open %s: %s\n"
msgstr "Nie można otworzyć %s: %s\n"

#: sadc.c:1019
#, c-format
msgid "Cannot append data to that file (%s)\n"
msgstr "Nie można dopisać danych do tego pliku (%s)\n"

#: common.c:77
#, c-format
msgid "sysstat version %s\n"
msgstr "sysstat w wersji %s\n"

#: cifsiostat.c:74
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"
msgstr ""
"Opcje to:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"

#: cifsiostat.c:77
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
msgstr ""
"Opcje to:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#: mpstat.c:129
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -n ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -N { <node_list> | ALL } ] [ -o JSON ] [ -P { <cpu_list> | ON | ALL } ]\n"
msgstr ""
"Opcje to:\n"
"[ -A ] [ -n ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -N { <lista_węzłów> | ALL } ] [ -o JSON ] [ -P { <lista_cpu> | ON | ALL } ]\n"

# sar.c:
#: mpstat.c:1672 sar.c:358 pidstat.c:2406
msgid "Average:"
msgstr "Średnia:"

#: sadf.c:87
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <datafile> | -[0-9]+ ]\n"
msgstr "Użycie: %s [ opcje ] [ <interwał> [ <liczba> ] ] [ <plik_danych> | -[0-9]+ ]\n"

#: sadf.c:90
#, c-format
msgid ""
"Options are:\n"
"[ -C ] [ -c | -d | -g | -j | -p | -r | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -O <opts> [,...] ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
"[ -- <sar_options> ]\n"
msgstr ""
"Opcje to:\n"
"[ -C ] [ -c | -d | -g | -j | -p | -r | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -O <opcje> [,...] ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
"[ -- <opcje_sar> ]\n"

#: pr_stats.c:2538 pr_stats.c:2549 pr_stats.c:2656 pr_stats.c:2667
msgid "Summary:"
msgstr "Podsumowanie:"

#: pr_stats.c:2591
msgid "Other devices not listed here"
msgstr "Inne urządzenia nie zostały wypisane"

#: sar.c:111
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ MOUNT ] ] [ -H ] [ -h ]\n"
"[ -p ] [ -q ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --human ] [ --sadc ]\n"
"[ -I { <int_list> | SUM | ALL } ] [ -P { <cpu_list> | ALL } ]\n"
"[ -m { <keyword> [,...] | ALL } ] [ -n { <keyword> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filename> ] | -o [ <filename> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
msgstr ""
"Opcje to:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ MOUNT ] ] [ -H ] [ -h ]\n"
"[ -p ] [ -q ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --human ] [ --sadc ]\n"
"[ -I { <lista_przerwań> | SUM | ALL } ] [ -P { <lista_cpu> | ALL } ]\n"
"[ -m { <słowo_kluczowe> [,...] | ALL } ] [ -n { <słowo_kluczowe> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <nazwa_pliku> ] | -o [ <nazwa_pliku> ] | -[0-9]+ ]\n"
"[ -i <interwał> ] [ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"

#: sar.c:134
#, c-format
msgid "Main options and reports:\n"
msgstr "Główne opcje i raporty:\n"

#: sar.c:135
#, c-format
msgid "\t-B\tPaging statistics\n"
msgstr "\t-B\tStatystyki stronicowania\n"

#: sar.c:136
#, c-format
msgid "\t-b\tI/O and transfer rate statistics\n"
msgstr "\t-b\tStatystyki I/O i prędkości transferu danych\n"

#: sar.c:137
#, c-format
msgid "\t-d\tBlock devices statistics\n"
msgstr "\t-d\tStatystyki urządzeń blokowych\n"

#: sar.c:138
#, c-format
msgid "\t-F [ MOUNT ]\n"
msgstr "\t-F [ MOUNT ]\n"

#: sar.c:139
#, c-format
msgid "\t\tFilesystems statistics\n"
msgstr "\t\tStatystyki systemów plików\n"

#: sar.c:140
#, c-format
msgid "\t-H\tHugepages utilization statistics\n"
msgstr "\t-S\tStatystyki wykorzystania wielkich stron\n"

#: sar.c:141
#, c-format
msgid ""
"\t-I { <int_list> | SUM | ALL }\n"
"\t\tInterrupts statistics\n"
msgstr ""
"\t-I { <lista_przerwań> | SUM | ALL | XALL }\n"
"\t\tStatystyki przerwań\n"

#: sar.c:143
#, c-format
msgid ""
"\t-m { <keyword> [,...] | ALL }\n"
"\t\tPower management statistics\n"
"\t\tKeywords are:\n"
"\t\tCPU\tCPU instantaneous clock frequency\n"
"\t\tFAN\tFans speed\n"
"\t\tFREQ\tCPU average clock frequency\n"
"\t\tIN\tVoltage inputs\n"
"\t\tTEMP\tDevices temperature\n"
"\t\tUSB\tUSB devices plugged into the system\n"
msgstr ""
"\t-m { <słowo_kluczowe> [,...] | ALL }\n"
"\t\tStatystyki zarządzania pamięcią\n"
"\t\tDostępne słowa kluczowe:\n"
"\t\tCPU\tBieżąca częstotliwość zegara CPU\n"
"\t\tFAN\tPrędkość wentylatorów\n"
"\t\tFREQ\tŚrednia częstotliwość zegara CPU\n"
"\t\tIN\tNapięcie wejściowe\n"
"\t\tTEMP\tTemperatura urządzeń\n"
"\t\tUSB\tUrządzenia USB podłączone do systemu\n"

#: sar.c:152
#, c-format
msgid ""
"\t-n { <keyword> [,...] | ALL }\n"
"\t\tNetwork statistics\n"
"\t\tKeywords are:\n"
"\t\tDEV\tNetwork interfaces\n"
"\t\tEDEV\tNetwork interfaces (errors)\n"
"\t\tNFS\tNFS client\n"
"\t\tNFSD\tNFS server\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP traffic\t(v4)\n"
"\t\tEIP\tIP traffic\t(v4) (errors)\n"
"\t\tICMP\tICMP traffic\t(v4)\n"
"\t\tEICMP\tICMP traffic\t(v4) (errors)\n"
"\t\tTCP\tTCP traffic\t(v4)\n"
"\t\tETCP\tTCP traffic\t(v4) (errors)\n"
"\t\tUDP\tUDP traffic\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP traffic\t(v6)\n"
"\t\tEIP6\tIP traffic\t(v6) (errors)\n"
"\t\tICMP6\tICMP traffic\t(v6)\n"
"\t\tEICMP6\tICMP traffic\t(v6) (errors)\n"
"\t\tUDP6\tUDP traffic\t(v6)\n"
"\t\tFC\tFibre channel HBAs\n"
"\t\tSOFT\tSoftware-based network processing\n"
msgstr ""
"\t-n { <słowo_kluczowe> [,...] | ALL }\n"
"\t\tStatystyki sieciowe\n"
"\t\tSłowa kluczowe to:\n"
"\t\tDEV\tInterfejsy sieciowe\n"
"\t\tEDEV\tInterfejsy sieciowe (błędy)\n"
"\t\tNFS\tKlient NFS\n"
"\t\tNFSD\tSerwer NFS \n"
"\t\tSOCK\tSokety\t(v4)\n"
"\t\tIP\tRuch IP\t(v4)\n"
"\t\tEIP\tRuch IP\t(v4) (błędy)\n"
"\t\tICMP\tRuch ICMP\t(v4)\n"
"\t\tEICMP\tRuch ICMP\t(v4) (błędy)\n"
"\t\tTCP\tRuch TCP\t(v4)\n"
"\t\tETCP\tRuch TCP\t(v4) (błędy)\n"
"\t\tUDP\tRuch UDP\t(v4)\n"
"\t\tSOCK6\tSokety\t(v6)\n"
"\t\tIP6\tRuch IP\t(v6)\n"
"\t\tEIP6\tRuch IP\t(v6) (błędy)\n"
"\t\tICMP6\tRuch ICMP\t(v6)\n"
"\t\tEICMP6\tRuch ICMP\t(v6) (błędy)\n"
"\t\tUDP6\tRuch UDP\t(v6)\n"
"\t\tFC\tHBA kanału Fibre\n"
"\t\tSOFT\tProgramowe przetwarzanie sieciowe\n"

#: sar.c:175
#, c-format
msgid "\t-q\tQueue length and load average statistics\n"
msgstr "\t-q\tStatystyki kolejkowania procesów i średniego obciążenia systemu\n"

#: sar.c:176
#, c-format
msgid ""
"\t-r [ ALL ]\n"
"\t\tMemory utilization statistics\n"
msgstr ""
"\t-r [ ALL ]\n"
"\t\tStatystyki wykorzystania pamięci\n"

#: sar.c:178
#, c-format
msgid "\t-S\tSwap space utilization statistics\n"
msgstr "\t-S\tStatystyki wykorzystania przestrzeni wymiany\n"

#: sar.c:179
#, c-format
msgid ""
"\t-u [ ALL ]\n"
"\t\tCPU utilization statistics\n"
msgstr ""
"\t-u [ ALL ]\n"
"\t\tStatystyki wykorzystania procesora\n"

#: sar.c:181
#, c-format
msgid "\t-v\tKernel tables statistics\n"
msgstr "\t-v\tStatystyki tabel jądra\n"

#: sar.c:182
#, c-format
msgid "\t-W\tSwapping statistics\n"
msgstr "\t-W\tStatystyki wymiany\n"

#: sar.c:183
#, c-format
msgid "\t-w\tTask creation and system switching statistics\n"
msgstr "\t-w\tStatystyki tworzenia zadań i przełączania systemu\n"

#: sar.c:184
#, c-format
msgid "\t-y\tTTY devices statistics\n"
msgstr "\t-y\tStatystyki terminali\n"

#: sar.c:198
#, c-format
msgid "Data collector will be sought in PATH\n"
msgstr "Program do zbierania danych zostanie wyszukany w PATH\n"

#: sar.c:201
#, c-format
msgid "Data collector found: %s\n"
msgstr "Znaleziono program do zbierania danych: %s\n"

#: sar.c:260
#, c-format
msgid "End of data collecting unexpected\n"
msgstr "Niespodziewany koniec zbieranych danych\n"

#: sar.c:813
#, c-format
msgid "Using a wrong data collector from a different sysstat version\n"
msgstr "Używany program do zbierania danych pochodzi z innej wersji pakietu sysstat\n"

#: sar.c:865
#, c-format
msgid "Inconsistent input data\n"
msgstr "Niespójne dane wejściowe\n"

#: sar.c:1044 pidstat.c:239
#, c-format
msgid "Requested activities not available\n"
msgstr "Żądane statystyki nie są dostępne\n"

#: sar.c:1347
#, c-format
msgid "-f and -o options are mutually exclusive\n"
msgstr "Opcje -f i -o się wykluczają\n"

#: sar.c:1353
#, c-format
msgid "Not reading from a system activity file (use -f option)\n"
msgstr "Czytanie danych nie z pliku aktywności systemu (proszę użyć opcji -f)\n"

#: sar.c:1489
#, c-format
msgid "Cannot find the data collector (%s)\n"
msgstr "Nie można znaleźć programu do zbierania danych (%s)\n"

#: sa_conv.c:69
#, c-format
msgid "Cannot convert the format of this file\n"
msgstr "Nie można przekształcić formatu tego pliku\n"

#: sa_conv.c:562
#, c-format
msgid ""
"\n"
"CPU activity not found in file. Aborting...\n"
msgstr ""
"\n"
"Dane o aktywności CPU nie są obecne w pliku. Przerywanie...\n"

#: sa_conv.c:578
#, c-format
msgid ""
"\n"
"Invalid data found. Aborting...\n"
msgstr ""
"\n"
"Znaleziono niepoprawne dane. Przerywanie...\n"

#: sa_conv.c:897
#, c-format
msgid "Statistics: "
msgstr "Statystyki: "

#: sa_conv.c:1016
#, c-format
msgid ""
"\n"
"File successfully converted to sysstat format version %s\n"
msgstr ""
"\n"
"Poprawnie przekształcono format pliku do wersji %s\n"

#: pidstat.c:87
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ -e <program> <args> ]\n"
msgstr "Użycie: %s [ opcje ] [ <interwał> [ <liczba> ] ] [ -e <program> <argumenty> ]\n"

#: pidstat.c:90
#, c-format
msgid ""
"Options are:\n"
"[ -d ] [ -H ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ] [ -U [ <username> ] ]\n"
"[ -u ] [ -V ] [ -v ] [ -w ] [ -C <command> ] [ -G <process_name> ] [ --human ]\n"
"[ -p { <pid> [,...] | SELF | ALL } ] [ -T { TASK | CHILD | ALL } ]\n"
msgstr ""
"Opcje to:\n"
"[ -d ] [ -H ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ] [ -U [ <użytkownik> ] ]\n"
"[ -u ] [ -V ] [ -v ] [ -w ] [ -C <polecenie> ] [ -G <nazwa_procesu> ] [ --human ]\n"
"[ -p { <pid> [,...] | SELF | ALL } ] [ -T { TASK | CHILD | ALL } ]\n"

#: sa_common.c:1000
#, c-format
msgid "Error while reading system activity file: %s\n"
msgstr "Błąd podczas czytania pliku aktywności systemu: %s\n"

#: sa_common.c:1010
#, c-format
msgid "End of system activity file unexpected\n"
msgstr "Niespodziewany koniec pliku aktywności systemu\n"

#: sa_common.c:1029
#, c-format
msgid "File created by sar/sadc from sysstat version %d.%d.%d"
msgstr "Plik utworzony przez sar/sadc z pakietu sysstat w wersji %d.%d.%d"

#: sa_common.c:1062
#, c-format
msgid "Invalid system activity file: %s\n"
msgstr "Niepoprawny plik aktywności systemu: %s\n"

#: sa_common.c:1074
#, c-format
msgid "Endian format mismatch\n"
msgstr "Niezgodność kolejności bajtów (endian)\n"

#: sa_common.c:1078
#, c-format
msgid "Current sysstat version cannot read the format of this file (%#x)\n"
msgstr "Bieżąca wersja pakietu sysstat nie obsługuje formatu tego pliku (%#x)\n"

#: sa_common.c:1207
#, c-format
msgid "Please check if data collecting is enabled\n"
msgstr "Proszę sprawdzić, czy zbieranie danych jest włączone\n"

#: sa_common.c:1400
#, c-format
msgid "Requested activities not available in file %s\n"
msgstr "Żądane statystyki nie są dostępne w pliku %s\n"

#: tapestat.c:97
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"
msgstr ""
"Options are:\n"
"[ --human ] [ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"

#: tapestat.c:263
#, c-format
msgid "No tape drives with statistics found\n"
msgstr "Nie znaleziono statystyk urządzeń taśmowych\n"

#: count.c:169
#, c-format
msgid "Cannot handle so many processors!\n"
msgstr "Zbyt dużo procesorów!\n"

#: sadf_misc.c:834
#, c-format
msgid "System activity data file: %s (%#x)\n"
msgstr "Plik z danymi o aktywności systemu: %s (%#x)\n"

#: sadf_misc.c:843
#, c-format
msgid "Genuine sa datafile: %s (%x)\n"
msgstr "Prawdziwy plik danych sa: %s (%#x)\n"

#: sadf_misc.c:844
msgid "no"
msgstr "nie"

#: sadf_misc.c:844
msgid "yes"
msgstr "tak"

#: sadf_misc.c:847
#, c-format
msgid "Host: "
msgstr "System: "

#: sadf_misc.c:854
#, c-format
msgid "Number of CPU for last samples in file: %u\n"
msgstr "Liczba procesorów w poprzednim wpisie w pliku: %u\n"

#: sadf_misc.c:860
#, c-format
msgid "File date: %s\n"
msgstr "Data pliku: %s\n"

#: sadf_misc.c:863
#, c-format
msgid "File time: "
msgstr "Czas pliku: "

#: sadf_misc.c:868
#, c-format
msgid "Size of a long int: %d\n"
msgstr "Rozmiar typu long int: %d\n"

#: sadf_misc.c:874
#, c-format
msgid "List of activities:\n"
msgstr "Lista aktywności:\n"

#: sadf_misc.c:887
#, c-format
msgid "\t[Unknown activity format]"
msgstr "\t[Nieznany format aktywności]"

#~ msgid "\t-R\tMemory statistics\n"
#~ msgstr "\t-R\tStatystyki pamięci\n"

#~ msgid "Not that many processors!\n"
#~ msgstr "Nie ma aż tylu procesorów!\n"
