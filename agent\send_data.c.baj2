#include <stdio.h>
#include <stdlib.h>
#include "send.h"
#include <string.h>
#include "top.h"
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <signal.h>
#include "err.h"
#include "readproc.h"
#include <fcntl.h>
#include <pthread.h>
#include "misc.h"
#include "meminfo.h"
#include <limits.h>
#include <errno.h>

extern int o2;
#include <sys/time.h>
//#include "../libpcap/pcap.h"
#include "../libpcap/pcap-int.h"

extern int sadc1();
struct sigaction alrm_act;
os_data *os;//first init hear

#include "pwcache.h"
#include "pids.h"
//#include "readproc.h"

#define container_of(ptr, type, member) \
    (type *)((char *)(ptr) - (char *) &((type *)0)->member)


//add perf 
//#include "../builtin.h"

//add sar
#include "../sar/sa1.h"

//add tcpdump
#include "../tcpdump-4.99.3/my.h"

//add 2024
#include "meminfo.h"
#include "pids.h"

extern int test;
extern int test2;
extern int test3;
extern struct pcap *pd;


//add for hsearch_r
#include <search.h>

//add 2024-11-4 thread pool
#include "thpool.h"
typedef struct Task
{
  void (*function)(void* arg);
  void* arg;
}Task;

struct ThreadPool
{
 // 任务队列
  Task* taskQ;
  int queueCapacity;// 容量
  int queueSize;// 当前任务个数
  int queueFront;// 队头 -> 取数据
  int queueRear;// 队尾 -> 放数据

  pthread_t managerID;// 管理者线程ID
  pthread_t *threadIDs;// 工作的线程ID
  int minNum;// 最小线程数量
  int maxNum;// 最大线程数量
  int busyNum;// 忙的线程的个数
  int liveNum;// 存活的线程的个数
  int exitNum;// 要销毁的线程个数
  pthread_mutex_t mutexPool;// 锁整个的线程池
  pthread_mutex_t mutexBusy;// 锁busyNum变量
  pthread_cond_t notFull;// 任务队列是不是满了
  pthread_cond_t notEmpty;// 任务队列是不是空了

  int shutdown;// 是不是要销毁线程池, 销毁为1, 不销毁为0
};
ThreadPool* pool;



//end thread pool

static void *alloc_r (void *ptr, size_t num) {
   void *pv;

   if (!num) ++num;
   if (!(pv = realloc(ptr, num)))
      perror("alloc_r failed");;
   return pv;
} // end: alloc_r








int sock;

void sigint_handler(int sig) {
//    write(1, "Caught Ctrl+C\n", 14);  // 打印信息
	puts("ctrl-c caught ....\n");
    wait1=1;
}

/*
void ignore_sigpipe() {
    struct sigaction act;
    act.sa_handler = SIG_IGN;
    sigemptyset(&act.sa_mask);
    act.sa_flags = 0;
    if (sigaction(SIGPIPE, &act, 0) == -1) {
        perror("sigaction");
        exit(EXIT_FAILURE);
    }
}
*/

/*
struct utlbuf_s {
    char *buf;     // dynamically grown buffer
    int   siz;     // current len of the above
} utlbuf_s;
*/

#define PROCPATHLEN 64  
static int file2str(const char *directory, const char *what, struct utlbuf_s *ub) {
 #define buffGRW 1024
    char path[PROCPATHLEN];
    int fd, num, tot_read = 0, len;

    /* on first use we preallocate a buffer of minimum size to emulate
       former 'local static' behavior -- even if this read fails, that
       buffer will likely soon be used for another subdirectory anyway
       ( besides, with the calloc call we will never need use memcpy ) */
    if (ub->buf) ub->buf[0] = '\0';
    else {
        ub->buf = calloc(1, (ub->siz = buffGRW));
        if (!ub->buf) return -1;
    }
    len = snprintf(path, sizeof path, "%s/%s", directory, what);
    if (len <= 0 || (size_t)len >= sizeof path) return -1;
    if (-1 == (fd = open(path, O_RDONLY, 0))) return -1;
    while (0 < (num = read(fd, ub->buf + tot_read, ub->siz - tot_read))) {
        tot_read += num;
        if (tot_read < ub->siz) break;
        if (ub->siz >= INT_MAX - buffGRW) {
            tot_read--;
            break;
        }
        if (!(ub->buf = realloc(ub->buf, (ub->siz += buffGRW)))) {
            close(fd);
            return -1;
        }
    };
    ub->buf[tot_read] = '\0';
    close(fd);
    if (tot_read < 1) return -1;
    return tot_read;
 #undef buffGRW
}

static int stat2proc (const char *S, proc_t *restrict P) {
    char buf[64], raw[64];
    size_t num;
    char *tmp;

//ENTER(0x160);

    /* fill in default values for older kernels */
    P->processor = 0;
    P->rtprio = -1;
    P->sched = -1;
    P->nlwp = 0;

    S = strchr(S, '(');
    if (!S) return 0;
    S++;
    tmp = strrchr(S, ')');
    if (!tmp || !tmp[1]) return 0;
#ifdef FALSE_THREADS
    if (!IS_THREAD(P)) {
#endif
    if (!P->cmd) {
       num = tmp - S;
       memcpy(raw, S, num);
       raw[num] = '\0';
       escape_str(buf, raw, sizeof(buf));
       if (!(P->cmd = strdup(buf))) return 1;
    }
#ifdef FALSE_THREADS
     }
#endif
    S = tmp + 2;                 // skip ") "

    sscanf(S,
       "%c "                      // state
       "%d %d %d %d %d "          // ppid, pgrp, sid, tty_nr, tty_pgrp
       "%lu %lu %lu %lu %lu "     // flags, min_flt, cmin_flt, maj_flt, cmaj_flt
       "%llu %llu %llu %llu "     // utime, stime, cutime, cstime
       "%d %d "                   // priority, nice
       "%d "                      // num_threads
       "%lu "                     // 'alarm' == it_real_value (obsolete, always 0)
       "%llu "                    // start_time
       "%lu "                     // vsize
       "%lu "                     // rss
       "%lu %lu %lu %lu %lu %lu " // rsslim, start_code, end_code, start_stack, esp, eip
       "%*s %*s %*s %*s "         // pending, blocked, sigign, sigcatch                      <=== DISCARDED
       "%lu %*u %*u "             // 0 (former wchan), 0, 0                                  <=== Placeholders only
       "%d %d "                   // exit_signal, task_cpu
       "%d %d "                   // rt_priority, policy (sched)
       "%llu %llu %llu",          // blkio_ticks, gtime, cgtime
       &P->state,
       &P->ppid, &P->pgrp, &P->session, &P->tty, &P->tpgid,
       &P->flags, &P->min_flt, &P->cmin_flt, &P->maj_flt, &P->cmaj_flt,
       &P->utime, &P->stime, &P->cutime, &P->cstime,
       &P->priority, &P->nice,
       &P->nlwp,
       &P->alarm,
       &P->start_time,
       &P->vsize,
       &P->rss,
       &P->rss_rlim, &P->start_code, &P->end_code, &P->start_stack, &P->kstk_esp, &P->kstk_eip,
/*     P->signal, P->blocked, P->sigignore, P->sigcatch,   */ /* can't use */
       &P->wchan, /* &P->nswap, &P->cnswap, */  /* nswap and cnswap dead for 2.4.xx and up */
/* -- Linux 2.0.35 ends here -- */
       &P->exit_signal, &P->processor,  /* 2.2.1 ends with "exit_signal" */
/* -- Linux 2.2.8 to 2.5.17 end here -- */
       &P->rtprio, &P->sched,  /* both added to 2.5.18 */
       &P->blkio_tics, &P->gtime, &P->cgtime
    );

    if(!P->nlwp)
      P->nlwp = 1;

    return 0;
LEAVE(0x160);
}

static void statm2proc(const char *s, proc_t *restrict P) {
    sscanf(s, "%lu %lu %lu %lu %lu %lu %lu",
           &P->size, &P->resident, &P->share,
           &P->trs, &P->lrs, &P->drs, &P->dt);
}

static inline void free_acquired (proc_t *p) {
    /*
     * here we free those items that might exist even when not explicitly |
     * requested by our caller.  it is expected that pid.c will then free |
     * any remaining dynamic memory which might be dangling off a proc_t. | */
    if (p->cgname)   free(p->cgname);
    if (p->cgroup)   free(p->cgroup);
    if (p->cmd)      free(p->cmd);
    if (p->sd_mach)  free(p->sd_mach);
    if (p->sd_ouid)  free(p->sd_ouid);
    if (p->sd_seat)  free(p->sd_seat);
    if (p->sd_sess)  free(p->sd_sess);
    if (p->sd_slice) free(p->sd_slice);
    if (p->sd_unit)  free(p->sd_unit);
    if (p->sd_uunit) free(p->sd_uunit);
    if (p->supgid)   free(p->supgid);

    memset(p, '\0', sizeof(proc_t));
}



int check_self(){
  for(;;){
    struct utlbuf_s ub = { NULL, 0 };
    int rc = 0;
    proc_t p;
    double uptime_cur;
    float et;
    static double uptime_sav;

    memset(&p, 0, sizeof(proc_t));
    if(file2str("/proc/self", "stat", &ub) == -1){
        fprintf(stderr, "Error, do this: mount -t proc proc /proc\n");
        _exit(47);
    }
    rc = stat2proc(ub.buf, &p); // parse /proc/self/stat
    int cpu_sav=0; 
    cpu_sav=p.utime+p.stime;

    procps_uptime(&uptime_cur, NULL);
    et = uptime_cur - uptime_sav;
    if (et < 0.01) et = 0.005;
    uptime_sav = uptime_cur;
    long Hertz = procps_hertz_get();
      // if in Solaris mode, adjust our scaling for all cpus
    float Frame_etscale1 = 100.0f / ((float)Hertz * (float)et * 1);
    free_acquired(&p);   
    free(ub.buf);
    sleep(5);
    ub.buf=NULL;
    ub.siz=0;
       memset(&p, 0, sizeof(proc_t));
    if(file2str("/proc/self", "stat", &ub) == -1){
        fprintf(stderr, "Error, do this: mount -t proc proc /proc\n");
        _exit(47);
    }
    rc = stat2proc(ub.buf, &p);
    float cpu_time=(float)p.utime+p.stime-cpu_sav;
    
    procps_uptime(&uptime_cur, NULL);
    et = uptime_cur - uptime_sav;
    if (et < 0.01) et = 0.005;
    uptime_sav = uptime_cur;
    Hertz = procps_hertz_get();
      // if in Solaris mode, adjust our scaling for all cpus
    Frame_etscale1 = 100.0f / ((float)Hertz * (float)et * 1);   

    cpu_time *= Frame_etscale1;
    //char *buf1=calloc(1,7);

    //snprintf(buf1, sizeof(buf1), "%#.1f", cpu_time);
    printf("send_data cpu=%.1f\n",cpu_time);
    fflush(stdout);
    if ( cpu_time > 90.0){

	 char *errlog="send_data process cpu usage% exceed 10% ,program exit";
                         err(errlog,get_time2());
                         exit(1);	

    }
 
    free(ub.buf);
    ub.buf=NULL;
    ub.siz=0; 
    

    if (file2str("/proc/self", "statm", &ub) != -1){
          statm2proc(ub.buf, &p);
    }

     int fd=open("/proc/meminfo", O_RDONLY);
     lseek(fd, 0L, SEEK_SET);
     char *head, *tail;
     int size;
     char buf[100]; 
     
    for (;;) {
        if ((size = read(fd, buf, sizeof(buf)-1)) < 0) {
            return 1;
        }
        break;
    }
    if (size == 0) {
        return 1;
    }
    buf[size] = '\0';

    head = buf;


        tail = strchr(head, ':');
        *tail = '\0';
        head = tail + 1;
        float a2 = strtoul(head, NULL, 10);


    float mem_time=p.resident << 2;
    float m_p;
    m_p=mem_time * 100.0 /a2;
    //char *buf2=calloc(1,7);

    //snprintf(buf2, sizeof(buf2), "%#.1f", m_p);
    printf("send_data mem=%.1f mem_time=%.1f a2=%.1f\n",m_p,mem_time,a2);
    fflush(stdout);
    if ( m_p > 10.0){

         char *errlog="send_data process mem usage% exceed 1% ,program exit";
                         err(errlog,get_time2());
                         exit(1);

    } 
	


    free_acquired(&p);
    free(ub.buf);
    close(fd);
    //free(buf1);
    //free(buf2);
}



}
/* 
int run_perf(){
	        //add perf
        page_size = sysconf(_SC_PAGE_SIZE);

        int i=1;
        const char* s[]={"top"};
	for(;;){
        cmd_top(i,s);	
	}
}
*/

/*
inline my_tcp *free_return_next(my_tcp *x){
	pthread_rwlock_wrlock(&tcp1_rwlock);	
        my_tcp *x1=x->next;
	memset(x->ip_src,'\0',sizeof(x->ip_src)); 
	memset(x->ip_dst,'\0',sizeof(x->ip_dst));
        x->count=NULL;
	x->ip_src1=NULL;
	x->ip_dst1=NULL;
        free(x);
	pthread_rwlock_unlock(&tcp1_rwlock);
	

        return x1;


}

*/

/*
int print_result(){

                int i;
	while (1){
		sleep(1);

			 //check group vs o3
                                  if ( group   >  o3)
                                  {     
                                        os=alloc_r(os,(group+1)*sizeof(os_data));
                                        memset(os+o3,0,(group - o3)*sizeof(os_data));
					max_tcp_count=group;
                                  }  
				  else{

					max_tcp_count=o3;

				 }
			//arry0 
			float g=(float)group/5;
			if ( g > 0 && g < 1) os->group_s=1;
			if ( g >= 1) os->group_s=round(group/5);
			pthread_rwlock_wrlock(&tcp1_rwlock);
			char *t=get_time2();
			printf(" time=%s group=%d\n",t,group);
			free(t);
			if ( os->group_s > 100   ){


				puts("your system has attacked !!!\n");
				group=0;
				return 0;


			}

                        for (i=0;i< os->group_s;i++)
                        {


				pthread_rwlock_wrlock(&tcp1_rwlock);
                                my_tcp *m=&my_tcp1[i];
				os_data *o=&os[i];
                           //     printf("tcp[S.]->connect:m->ip_src=%s m->ip_dst=%s m->count=%d group=%d\n",m->ip_src,m->ip_dst,m->count,os->group_s);
				snprintf(o->m_tcp_s.ip_src,16,"%s",m->ip_src);
				snprintf(o->m_tcp_s.ip_dst,16,"%s",m->ip_dst);
				float c=(float)m->count/5;
				if ( c > 0 && c < 1) o->m_tcp_s.count=1;
                        	if ( c >= 1) o->m_tcp_s.count=round(m->count/5);
				pthread_rwlock_unlock(&tcp1_rwlock);

                        }

				my_tcp *current=list;
                                while (  current!=NULL){
				my_tcp *x1=current->next;
		if ( x1 != NULL){
        memset(current->ip_src,'\0',sizeof(current->ip_src));
        memset(current->ip_dst,'\0',sizeof(current->ip_dst));
        current->count=NULL;
        current->ip_src1=NULL;
        current->ip_dst1=NULL;
        free(current);
	}
	else
	{
		        memset(current->ip_src,'\0',sizeof(current->ip_src));
        memset(current->ip_dst,'\0',sizeof(current->ip_dst));
        current->count=NULL;
        current->ip_src1=NULL;
        current->ip_dst1=NULL;
        free(current);
	break;

	}
	current=x1;


                                }
				if ( my_tcp1 != NULL){
				 
				group=0;
				free(my_tcp1);
				my_tcp1=NULL;
				}
			group=0;
			pthread_rwlock_unlock(&tcp1_rwlock);

}

}
*/

int print_result1(){

        for (;;){
                int i;
                        if ( group1 == 0){

                                puts("no data push  n a second!!\n");
                                goto next1;

                        }
			
			    //check group1 vs max_tcp_count
                                  if ( group1   >  max_tcp_count)
                                  {
                                        os=alloc_r(os,(group1+1)*sizeof(os_data));
                                        memset(os+max_tcp_count,0,(group1 - max_tcp_count)*sizeof(os_data));
                                        max_tcp_count=group1;
                                  }  

                        for (i=0;i< group1;i++)
                        {
				pthread_rwlock_wrlock(&tcp2_rwlock);
                                 my_tcp2 *m=&my_tcp21[i];
                                printf("push date :m->ip_src=%s m->ip_dst=%s m->count=%d group1=%d\n",m->ip_src,m->ip_dst,m->count,group1);
				pthread_rwlock_unlock(&tcp2_rwlock);

                        }
                                if ( my_tcp21 !=NULL){
				pthread_rwlock_wrlock(&tcp2_rwlock);
                                free(my_tcp21);
                               my_tcp21=NULL;
                                group1=0;
				pthread_rwlock_unlock(&tcp2_rwlock);
                                }
        next1:
        sleep(1);

        }



}


int print_result2(){

        for (;;){
                int i;
                        if ( group2 == 0){

                                puts("icmp no data  in a second!!\n");
                                goto next2;

                        }
			   //check group2 vs max_tcp_count
                                  if ( group2   >  max_tcp_count)
                                  {
                                        os=alloc_r(os,(group2+1)*sizeof(os_data));
                                        memset(os+max_tcp_count,0,(group2 - max_tcp_count)*sizeof(os_data));
                                        max_tcp_count=group2;
                                  }

                        for (i=0;i< group2;i++)
                        {
                                pthread_rwlock_wrlock(&tcp3_rwlock);
                                 my_tcp3 *m=&my_tcp31[i];
                                printf("icmp data:m->ip_src=%s m->ip_dst=%s m->count=%d group2=%d\n",m->ip_src,m->ip_dst,m->count,group2);
                                pthread_rwlock_unlock(&tcp3_rwlock);

                        }
				pthread_rwlock_wrlock(&tcp3_rwlock);
                                if ( my_tcp31 !=NULL){
                                free(my_tcp31);
                               my_tcp31=NULL;
                                group2=0;
                                }
                                pthread_rwlock_unlock(&tcp3_rwlock);
        next2:
        sleep(1);

        }



}


int print_result3(){

        for (;;){
                int i;
                        if ( group3 == 0){

                                puts("raw ip no data  in a second!!\n");
                                goto next3;

                        }
			
			            //check group3 vs max_tcp_count
                                  if ( group3   >  max_tcp_count)
                                  {
                                        os=alloc_r(os,(group3+1)*sizeof(os_data));
                                        memset(os+max_tcp_count,0,(group3 - max_tcp_count)*sizeof(os_data));
                                        max_tcp_count=group3;
                                  }

                        for (i=0;i< group3;i++)
                        {
                                pthread_rwlock_wrlock(&tcp4_rwlock);
                                my_tcp4 *m=&my_tcp41[i];
                                printf("rawip data:m->ip_src=%s m->ip_dst=%s m->count=%d group3=%d\n",m->ip_src,m->ip_dst,m->count,group3);
                                pthread_rwlock_unlock(&tcp4_rwlock);

                        }
                                pthread_rwlock_wrlock(&tcp4_rwlock);
                                if ( my_tcp41 !=NULL){
                                free(my_tcp41);
                               my_tcp41=NULL;
                                group3=0;
                                }
                                pthread_rwlock_unlock(&tcp4_rwlock);
        next3:
        sleep(1);

        }



}

int print_result4(){

        for (;;){
                int i;
                        if ( group4 == 0){

                                puts("udp no data  in a second!!\n");
                                goto next4;

                        }
			//check group4 vs max_tcp_count
			if ( group4   >  max_tcp_count)
			{
				os=alloc_r(os,(group4+1)*sizeof(os_data));
				memset(os+max_tcp_count,0,(group4 - max_tcp_count)*sizeof(os_data));
				max_tcp_count=group4;
			}

                        for (i=0;i< group4;i++)
                        {
                                pthread_rwlock_wrlock(&tcp5_rwlock);
                                my_tcp5 *m=&my_tcp51[i];
                                printf("udp data:m->ip_src=%s m->ip_dst=%s m->count=%d group4=%d\n",m->ip_src,m->ip_dst,m->count,group4);
                                pthread_rwlock_unlock(&tcp5_rwlock);

                        }
                                pthread_rwlock_wrlock(&tcp5_rwlock);
                                if ( my_tcp51 !=NULL){
                                free(my_tcp51);
                               my_tcp51=NULL;
                                group4=0;
                                }
                                pthread_rwlock_unlock(&tcp5_rwlock);
        next4:
        sleep(1);

        }



}


int print_result5(){

        for (;;){
                int i;
                        if ( group5 == 0){

                                puts("reconnect no data  in a second!!\n");
                                goto next5;

                        }
			        //check group5 vs max_tcp_count
                        if ( group5   >  max_tcp_count)
                        {
                                os=alloc_r(os,(group5+1)*sizeof(os_data));
                                memset(os+max_tcp_count,0,(group5 - max_tcp_count)*sizeof(os_data));
                                max_tcp_count=group5;
                        }

                        for (i=0;i< group5;i++)
                        {
                                pthread_rwlock_wrlock(&tcp6_rwlock);
                                my_tcp6 *m=&my_tcp61[i];
                                printf("reconnect data:m->ip_src=%s m->ip_dst=%s m->count=%d group5=%d\n",m->ip_src,m->ip_dst,m->count,group5);
                                pthread_rwlock_unlock(&tcp6_rwlock);

                        }
                                pthread_rwlock_wrlock(&tcp6_rwlock);
                                if ( my_tcp61 !=NULL){
                                free(my_tcp61);
                               my_tcp61=NULL;
                                group5=0;
                                }
                                pthread_rwlock_unlock(&tcp6_rwlock);
        next5:
        sleep(1);

        }



}

int print_result6(){

        for (;;){
                int i;
                        if ( group6 == 0){

                                puts("close connection no data  in a second!!\n");
                                goto next6;

                        }
			//check group6 vs max_tcp_count
                        if ( group6   >  max_tcp_count)
                        {
                                os=alloc_r(os,(group6+1)*sizeof(os_data));
                                memset(os+max_tcp_count,0,(group6 - max_tcp_count)*sizeof(os_data));
                                max_tcp_count=group6;
                        }

                        for (i=0;i< group6;i++)
                        {
                                pthread_rwlock_wrlock(&tcp7_rwlock);
                                my_tcp7 *m=&my_tcp71[i];
                                printf("close connection data:m->ip_src=%s m->ip_dst=%s m->count=%d group6=%d\n",m->ip_src,m->ip_dst,m->count,group6);
                                pthread_rwlock_unlock(&tcp7_rwlock);

                        }
                                pthread_rwlock_wrlock(&tcp7_rwlock);
                                if ( my_tcp71 !=NULL){
                                free(my_tcp71);
                               my_tcp71=NULL;
                                group6=0;
                                }
                                pthread_rwlock_unlock(&tcp7_rwlock);
        next6:
        sleep(1);

        }



}

int print_result7(){

        for (;;){
                int i;
                        if ( group7 == 0){

                                puts("URG no data  in a second!!\n");
                                goto next7;

                        }
			//check group7 vs max_tcp_count
                        if ( group7   >  max_tcp_count)
                        {
                                os=alloc_r(os,(group7+1)*sizeof(os_data));
                                memset(os+max_tcp_count,0,(group7 - max_tcp_count)*sizeof(os_data));
                                max_tcp_count=group7;
                        }

                        for (i=0;i< group7;i++)
                        {
                                pthread_rwlock_wrlock(&tcp8_rwlock);
                                my_tcp8 *m=&my_tcp81[i];
                                printf("TCP URG data:m->ip_src=%s　m->dst=%s m->count=%d group7=%d\n",m->ip_src,m->ip_dst,m->count,group7);
                                pthread_rwlock_unlock(&tcp8_rwlock);

                        }
                                pthread_rwlock_wrlock(&tcp8_rwlock);
                                if ( my_tcp81 !=NULL){
                                free(my_tcp81);
                               my_tcp81=NULL;
                                group7=0;
                                }
                                pthread_rwlock_unlock(&tcp8_rwlock);
        next7:
        sleep(1);

        }



}


int print_result8(){

        for (;;){
                int i;
                        if ( group8 == 0){

                                puts("[E] no data  in a second!!\n");
                                goto next8;

                        }
			//check group8 vs max_tcp_count
                        if ( group8   >  max_tcp_count)
                        {
                                os=alloc_r(os,(group8+1)*sizeof(os_data));
                                memset(os+max_tcp_count,0,(group8 - max_tcp_count)*sizeof(os_data));
                                max_tcp_count=group8;
                        }

                        for (i=0;i< group8;i++)
                        {
                                pthread_rwlock_wrlock(&tcp9_rwlock);
                                my_tcp9 *m=&my_tcp91[i];
                                printf("TCP [E] data:m->ip_src=%s m->ip_dst=%s m->count=%d group8=%d\n",m->ip_src,m->ip_dst,m->count,group8);
                                pthread_rwlock_unlock(&tcp9_rwlock);

                        }
                                pthread_rwlock_wrlock(&tcp9_rwlock);
                                if ( my_tcp91 !=NULL){
                                free(my_tcp91);
                               my_tcp91=NULL;
                                group8=0;
                                }
                                pthread_rwlock_unlock(&tcp9_rwlock);
        next8:
        sleep(1);

        }



}


int print_result9(){

        for (;;){
                int i;
                        if ( group9 == 0){

                                puts("[W] no data  in a second!!\n");
                                goto next9;

                        }
			
			//check group9 vs max_tcp_count
                        if ( group9   >  max_tcp_count)
                        {
                                os=alloc_r(os,(group9+1)*sizeof(os_data));
                                memset(os+max_tcp_count,0,(group9 - max_tcp_count)*sizeof(os_data));
                                max_tcp_count=group9;
                        }

                        for (i=0;i< group9;i++)
                        {
                                pthread_rwlock_wrlock(&tcp0_rwlock);
                                my_tcp0 *m=&my_tcp01[i];
                                printf("TCP [W] data:m->ip_src=%s m->ip_dst=%s m->count=%d group9=%d\n",m->ip_src,m->ip_dst,m->count,group9);
                                pthread_rwlock_unlock(&tcp0_rwlock);

                        }
                                pthread_rwlock_wrlock(&tcp0_rwlock);
                                if ( my_tcp01 !=NULL){
                                free(my_tcp01);
                               my_tcp01=NULL;
                                group9=0;
                                }
                                pthread_rwlock_unlock(&tcp0_rwlock);
        next9:
        sleep(1);

        }



}

int print_resulta(){

        for (;;){
                int i;
                        if ( group0 == 0){

                                puts("[none] no data  in a second!!\n");
                                goto nexta;

                        }
		        //check group0 vs max_tcp_count
                        if ( group0   >  max_tcp_count)
                        {
                                os=alloc_r(os,(group0+1)*sizeof(os_data));
                                memset(os+max_tcp_count,0,(group0 - max_tcp_count)*sizeof(os_data));
                                max_tcp_count=group0;
                        }

                        for (i=0;i< group0;i++)
                        {
                                pthread_rwlock_wrlock(&tcpa_rwlock);
                                my_tcpa *m=&my_tcpa1[i];
                                printf("TCP [none] data:m->ip_src=%s m->ip_dst=%s m->count=%d group0=%d\n",m->ip_src,m->ip_dst,m->count,group0);
                                pthread_rwlock_unlock(&tcpa_rwlock);

                        }
                                pthread_rwlock_wrlock(&tcpa_rwlock);
                                if ( my_tcpa1 !=NULL){
                                free(my_tcpa1);
                               my_tcpa1=NULL;
                                group0=0;
                                }
                                pthread_rwlock_unlock(&tcpa_rwlock);
        nexta:
        sleep(1);

        }



}

int print_resultb(){

        for (;;){
                int i;
                        if ( groupa == 0){

                                puts("[S] no data  in a second!!\n");
                                goto nextb;

                        }
			//check groupa vs max_tcp_count
                        if ( groupa   >  max_tcp_count)
                        {
                                os=alloc_r(os,(groupa+1)*sizeof(os_data));
                                memset(os+max_tcp_count,0,(groupa - max_tcp_count)*sizeof(os_data));
                                max_tcp_count=groupa;
                        }

                        for (i=0;i< groupa;i++)
                        {
                                pthread_rwlock_wrlock(&tcpb_rwlock);
                                my_tcpb *m=&my_tcpb1[i];
                                printf("TCP [S] data:m->ip_src=%s m->ip_dst=%s m->count=%d groupa=%d\n",m->ip_src,m->ip_dst,m->count,groupa);
                                pthread_rwlock_unlock(&tcpb_rwlock);

                        }
                                pthread_rwlock_wrlock(&tcpb_rwlock);
                                if ( my_tcpb1 !=NULL){
                                free(my_tcpb1);
                               my_tcpb1=NULL;
                                groupa=0;
                                }
                                pthread_rwlock_unlock(&tcpb_rwlock);
        nextb:
        sleep(1);

        }



}

int print_all(){

	 for(;;){
	       //lock self
                puts("tcpdump :lock start..\n");
                pthread_mutex_lock(&tcpdump);
                pthread_cond_wait(&tcp_d,&tcpdump);
                pthread_cond_signal(&tcp_d);
                pthread_mutex_unlock(&tcpdump);
	puts("tcpdump start print.....\n");
	
	//print_result();
/*
	print_result1();
	print_result2();
	print_result3();
	print_result4();
	print_result5();
	print_result6();
	print_result7();
	print_result8();
	print_result9();
	print_resulta();
	print_resultb();
*/
	
	//unlock main
	puts("unlock main start .... \n");
        pthread_cond_signal(&dblost);
	}
	







}

void alarm_handler1(int sig)
{
        alarm(1);
}

int c_tcp1(){
		timeout1=0;
		while (!wait1){
	        puts("c_tcp1 :lock start..\n");
		c_tcp1_lock1=1;
                pthread_mutex_lock(&xxx);
                pthread_cond_wait(&cc11,&xxx);
                pthread_mutex_unlock(&xxx);	
		c_tcp1_lock1=0;
		  if (wait2==1){
                        release1++;
                        return 0;

                }		
		char *s=get_time2();
		printf("before sleep=%s\n",s);
		free(s);

		puts("*****解锁成功开始睡眠******");
		//sleep(1);

		if (usleep(1000000) != 0) {
			puts("sleppppppppppppppppppppppppp");
    			perror("sleep");
		}
		char *s1=get_time2();
		printf("after sleep=%s\n",s1);
		free(s1);

		//send exit signal to tcp1
	//	puts("*********睡眠结束，开始发送信号给select*****");
		//exit_tcp(pd);
		/*
		if ( epoll_check == 0){
			epoll_check=100;

		}
		*/
			 pthread_mutex_lock(&xxx);
			if ( timeout1 == 0){
				timeout1=1;


			}	
			 pthread_mutex_unlock(&xxx);


		}

		release1++;


}

/*
int p_tcp1(char *args){

	while(!wait1){
	                //lock self
                puts("p_tcp1 :lock start..\n");
                pthread_mutex_lock(&xxx);
                pthread_cond_wait(&tc11,&xxx);
                pthread_mutex_unlock(&xxx);
		//send unlock to c_tcp1
		pthread_cond_signal(&cc11);		
		puts("开始运行 tcp1....\n");
		//如果wait2=1 就不再往下执行了
		if (wait2==1){
			release1++;
			return 0;	

		}
		int ret=100;
		ret=tcp1(args);
		printf("ret=%d\n",ret);
		//退出成功后开始打印数据到主结构体
		if ( ret == 0){
			puts("tcp1 运行完成，开始打印数据到主结构体\n");
			pthread_mutex_lock(&xxx);	
		     if ( group   == o2)
                     {
                        os=alloc_r(os,(group+1)*sizeof(os_data));
                        memset(os+o2,0,1*sizeof(os_data));
                        o2++;
                     }
		     if ( group > o2){

			 os=alloc_r(os,group*sizeof(os_data));
			 memset(os+o2,0,(group-02)*sizeof(os_data));
			 o2=group;


		     }


		int i;
		for (i=0;i<group;i++){
			my_tcp *m=&my_tcp1[i];
			os_data *o=&os[i];
			snprintf(o->m_tcp_s.ip_src,16,"%s",m->ip_src);
                        snprintf(o->m_tcp_s.ip_dst,16,"%s",m->ip_dst);			
			o->m_tcp_s.count=m->count;
		}
		os->group_s=group;
	
		pthread_mutex_unlock(&xxx);
	
		}
	
		//free
		puts("free  my_tcp1\n");
		if ( my_tcp1 !=NULL){
                     free(my_tcp1);
                     my_tcp1=NULL;
                     group=0;
		}
		//这里是线程数量用来给sys()判断是否线程都执行完毕
		ths++;

	}
	//这里是线程是否都退出,先free
	if ( my_tcp1 !=NULL ){
		free(my_tcp1);
		my_tcp1=NULL;

	}
	release1++;


}
*/

/*
 ***************************************************************************
 * SIGINT signal handler.
 *
 * IN:
 * @sig Signal number.
 ***************************************************************************
 */

void int_handler(int sig)
{
        sigint_caught = 1;
        wait1=1;//退出线程
        wait2=1;//当线程为lock时启用
        //new for tcp1
                struct itimerval timer;

        timer.it_interval.tv_sec = 0;
        timer.it_interval.tv_usec = 0;
        timer.it_value.tv_sec = 0;
        timer.it_value.tv_usec = 0;
        setitimer(ITIMER_REAL, &timer, NULL);
         pcap_breakloop(pd);
        printf("ctrl-c 退出所有线程和主进程\n");        /* Skip "^C" displayed on screen */

}

struct pwbuf *free_return_next(struct pwbuf *x){

        struct pwbuf *x1=x->next;
        free(x);

        return x1;


}

struct utlbuf_s *free_return_next1(struct utlbuf_s *x){
                
        struct utlbuf_s *x1=x->next;
        free(x->buf);        
                
        return x1;
                        

}  


int free_all1(){

	if ( os != NULL)
        {

                free(os);
                os=NULL;

        }
       if(free_info !=NULL) procps_stat_unref(&free_info);
       if(Graph_cpus !=NULL) free(Graph_cpus);
       if(Graph_mems !=NULL)  free(Graph_mems);
	if ( free_meminfo !=NULL) procps_meminfo_unref(&free_meminfo);
	if ( Pids_itms !=NULL) free(Pids_itms);
	//free pwcache_get_user
		int pp;
		int m=1;
		struct pwbuf *a1;
		struct pwbuf *head;
		head=NULL;
		//a1=calloc(1,m*sizeof(struct pwbuf));
	        for (pp=0;pp<PIDSmaxt;pp++){
			int continue1=0;
                if ( Pids_reap->stacks[pp]->head[EU_UEN].result.str !=NULL)
                {
			if (strcmp(Pids_reap->stacks[pp]->head[EU_UEN].result.str,"mysql")==0 ){

			//	getchar();
			

			}

                        //free((Pids_reap->stacks[pp]->head[EU_UEN].result.str));
                        char *s=Pids_reap->stacks[pp]->head[EU_UEN].result.str;
                         struct pwbuf *s1tmp = container_of(s, struct pwbuf, name);
			//单链表往前插数据
			struct pwbuf *tmp=head;
			while ( tmp !=NULL){	
				if ( tmp == s1tmp){
					continue1=1;
					break;

				}
				tmp=tmp->next;
			}
			
			if ( continue1 == 1) continue;
			s1tmp->next=head;
			head=s1tmp;

		}

		}
/*
			 struct pwbuf *a2=head;
		//print need free username
		while ( a2 != NULL){
                printf("a2->name=%s\n",a2->name);
                a2=a2->next;
        	}

*/
		puts("********分隔符 *********************");

		int pg;
		for (pg=0;pg<PIDSmaxt1;pg++){
			int continue2=0;
		if ( Pids_reap1->stacks[pg]->head[EU_UEN].result.str !=NULL)
		{

                        char *s=Pids_reap1->stacks[pg]->head[EU_UEN].result.str;
                         struct pwbuf *s1tmp = container_of(s, struct pwbuf, name);
			//继续往前插入单链表
		
			struct pwbuf *tmp1=head;
			while ( tmp1 !=NULL){
				if ( tmp1==s1tmp){
					continue2=1;
					break;
				}
				tmp1=tmp1->next;

			}
			
			if ( continue2 == 1) continue;
			s1tmp->next=head;
			head=s1tmp;	
	
                }

                }
		
/*
		//print username
		a2=head;
		while ( a2 !=NULL){
			printf("a2->name=%s\n",a2->name);
			a2=a2->next;

		}
*/

		//free username

		struct pwbuf *a2=head;
		 while( a2 !=NULL){
			a2=free_return_next(a2);
		}
	  struct utlbuf_s *xx=free_str1;
	  int i=0;
	 while ( i <2) 
	 {
		printf("xx=%p\n",xx);
		xx=free_return_next1(xx);
		i++;
		
	 }

/*
	 if ( free_file2str1 !=NULL)
	 {
		 free ( free_file2str1);
	 }
*/


	if ( src_buffer !=NULL) free(src_buffer);
	if ( dst_buffer !=NULL) free(dst_buffer);
	if ( free_pidsinfo !=NULL) procps_pids_unref(&free_pidsinfo);	
	//free  my_tcp1
	if ( my_tcp1 != NULL) free(my_tcp1);
	//free ip_str
	                //free ip
                int j;
                if ( ip_str !=NULL){
                        if ( ip_count < max_ip){
                                for(j= 0; j < max_ip; j++) {

                                if ( ip_str[j] != NULL){

                                free( ip_str[j]);

                                }
                        }

                }
                else {

                         for(j = 0; j < ip_count; j++) {

                        if ( ip_str[j] != NULL){

                                free( ip_str[j]);

                                }
                        }


                        }
                        free(ip_str);
                }
}

int main(int argc,char *argv[])
{
	//db_mutex = PTHREAD_MUTEX_INITIALIZER;
	//dblost = PTHREAD_COND_INITIALIZER;
	//rdb = PTHREAD_MUTEX_INITIALIZER;
	//rcond = PTHREAD_COND_INITIALIZER;	
	wait1=0;

	        memset(&int_act, 0, sizeof(int_act));
        int_act.sa_handler = (void *) int_handler;
        int_act.sa_flags = SA_RESTART;
        sigaction(SIGINT, &int_act, NULL);

	if ( argc < 3){
	 printf("NO enough args !\n");
	 puts("example:./send_data eth0 *************\n");
	 puts("eth0:need to listen interface; ************* exclude ip you via to logine in ");
	 exit(1);

	}
	
	 //signal(SIGINT, sigint_handler);


	pthread_mutex_init(&db_mutex, NULL);
	pthread_cond_init(&dblost, NULL);
	 pthread_mutex_init(&rdb, NULL);
	 pthread_cond_init(&rcond, NULL);
	pthread_mutex_init(&xxx, NULL);
	pthread_cond_init(&sar_rcond,NULL);

	 pthread_mutex_init(&tcpdump,NULL);
	pthread_cond_init(&tcp_d,NULL);

	//init tcp write lock
	pthread_rwlock_init(&tcp1_rwlock, NULL);
	pthread_rwlock_init(&tcp2_rwlock, NULL);
	pthread_rwlock_init(&tcp3_rwlock, NULL);
	pthread_rwlock_init(&tcp4_rwlock, NULL);
	pthread_rwlock_init(&tcp5_rwlock, NULL);
	pthread_rwlock_init(&tcp6_rwlock, NULL);
	pthread_rwlock_init(&tcp7_rwlock, NULL);
	pthread_rwlock_init(&tcp8_rwlock, NULL);
	pthread_rwlock_init(&tcp9_rwlock, NULL);
	pthread_rwlock_init(&tcp0_rwlock, NULL);
	pthread_rwlock_init(&tcpa_rwlock, NULL);
	pthread_rwlock_init(&tcpb_rwlock, NULL);

    
//	pthread_t perf;
//	pthread_create(&perf, NULL,&run_perf, NULL);
	//pthread_t sar1;
	//pthread_create(&sar1, NULL,&sar, NULL);

//	pthread_t tcpdump1;
//	pthread_create(&tcpdump1, NULL,&tcp1, argv[1]);

	//exit tcp1 after 1s
//	pthread_t cc1;
//	pthread_create(&cc1,NULL,&c_tcp1,NULL);

	//start tcp1 
//	pthread_create(&tcpdump1,NULL,&p_tcp1,argv[1]);

/*
//	pthread_t tcpdump2;

	struct thread_tcp2 *data2 = malloc(sizeof(struct thread_tcp2));
	data2->e=argv[1];
	data2->ex_ip=argv[2];

	pthread_create(&tcpdump2, NULL,&tcp3, argv[1]);

//	pthread_t tcpdump3;
	pthread_create(&tcpdump3, NULL,&tcp2, data2);
	pthread_t tcpdump4;
	pthread_create(&tcpdump4, NULL,&tcp4, argv[1]);
	pthread_t tcpdump5;
	pthread_create(&tcpdump5, NULL,&tcp5, argv[1]);
	pthread_t tcpdump6;
        pthread_create(&tcpdump6, NULL,&tcp6, argv[1]);
	pthread_t tcpdump7;
	pthread_create(&tcpdump7, NULL,&tcp7, argv[1]);
	pthread_t tcpdump8;
	pthread_create(&tcpdump8, NULL,&tcp8, argv[1]);
	pthread_t tcpdump9;
	pthread_create(&tcpdump9, NULL,&tcp9, argv[1]);
*/	
	        pthread_t th1;  
//		pthread_create(&th1, NULL,&print_result, NULL);//all

		 //pthread_join(&tcpdump1, NULL);
	//	pthread_join(&th1, NULL);
		
/*
        pthread_t th2;  
	pthread_t th3;
	pthread_t th4;
	pthread_t th5;
	pthread_t th6;
	pthread_t th7;
	pthread_t th8;
	pthread_t th9;
	pthread_t th0;
	pthread_t tha;
	pthread_t thb;
        pthread_create(&th1, NULL,&print_result, NULL);//[S.]
        pthread_create(&th2, NULL,&print_result1, NULL);//[P]
	pthread_create(&th3, NULL,&print_result2, NULL);//icmp
	pthread_create(&th4, NULL,&print_result3, NULL);//raw ip
	pthread_create(&th5, NULL,&print_result4, NULL);//udp
	pthread_create(&th6, NULL,&print_result5, NULL);//R.
	pthread_create(&th7, NULL,&print_result6, NULL);//F
	pthread_create(&th8, NULL,&print_result7, NULL);//U
	pthread_create(&th9, NULL,&print_result8, NULL);//E
	pthread_create(&th0, NULL,&print_result9, NULL);//W
	pthread_create(&tha, NULL,&print_resulta, NULL);//none
	pthread_create(&thb, NULL,&print_resultb, NULL);//S

*/
	//check self
	pthread_t th;
//	pthread_create(&th, NULL,&check_self, NULL);
      
 //判断server是否能连接，如果能连接跳出循环
  while (1)
  {
  top2:
  //这里if只处理当线程都为lock时，退出进程
  if ( wait2 == 1){
	//解锁线程,让线程正常退出
	                pthread_cond_signal(&sar_rcond);
                pthread_cond_signal(&tc11);
	
		//wait2=2;

        if ( os != NULL)
        {

                free(os);
                os=NULL;

        }
  }
	//当线程全部退出完毕，退出进程
	if ( release1 == 3) 
	{

		puts("top2:退出主进程。。。。");
		return 0;

	}
  sock=get_sock();
  if (connect1() != 0)
  {
     char *errlog="connect failed, remote server is offline!";
        err(errlog,get_time2());	
      close(sock);
      sleep(3);
      continue;
  } 
  break;

  }
	//server 连接成功，才能进行下步操作,启动线程
		                pthread_attr_t attr;
                pthread_attr_init(&attr);
                pthread_attr_setdetachstate(&attr,  PTHREAD_CREATE_DETACHED);
	        pthread_t sar1;
       pthread_create(&sar1, &attr,&sadc1, NULL);
	pthread_t tcpdump1;
	pthread_create(&tcpdump1,&attr,&tcp1,argv[1]);

	        //exit tcp1 after 1s
        pthread_t cc1;
	        pthread_attr_t attr1;
                pthread_attr_init(&attr1);
                pthread_attr_setdetachstate(&attr1,  PTHREAD_CREATE_DETACHED);
        pthread_create(&cc1,&attr1,&c_tcp1,NULL);
	
  top:
  //判断是否退出主程序
  if ( release1 == 3){
	free_all1();
	puts("top:退出主进程。。。");
	close(sock);
        return 0;	
  }

  os= calloc(1,1000 * sizeof(os_data));
  
  for(;;){ 
	sys();
	if ( !abandon(aaa,ab) ){
		break;
	}
	free(os);
	os=NULL;
	os= calloc(1,1000 * sizeof(os_data));
  }
  //memset(arr,0,(max_cores+1)*sizeof(os_data));
/*
  printf("sizeof(buffer).NO1=%d\n",sizeof(buffer));
  printf("strlen(arr->avg_load11)=%d\n",strlen(arr->avg_load11));
  printf("strlen(arr->time1)=%d\n",strlen(arr->time1));
*/
  puts("###################check cpu 0 1 ....");
  int jj;
      for (jj=0;jj < sum_cores; jj++){
         os_data *arr1=&os[jj];
        printf("send data arr1->cpu_p.u_frme_p=%s u_frme=%ld u_f=%ld sum_user=%s sum_sys=%s sum_total=%s\n",arr1->cpu_p.u_frme_p,arr1->cpu_p.u_frme,arr1->cpu_p.u_f,arr1->cpu_p.sum_user_p,arr1->cpu_p.sum_sys_p,arr1->cpu_p.sum_total_p);
	fflush(stdout);
	
      }
  puts("##################check load active time##########");
  os_data *cpu_sum=&os[jj];
  printf("load1=%s active1=%d time1=%s \n",cpu_sum->avg_load11,cpu_sum->active1,cpu_sum->time1);
  fflush(stdout);
  puts("##################check cpu_sum#################");
  printf("send data cpu_sum->cpu_p.u_frme_p=%s u_frme=%ld u_f=%ld sum_user=%s sum_sys=%s sum_total=%s\n",cpu_sum->cpu_p.u_frme_p,cpu_sum->cpu_p.u_frme,cpu_sum->cpu_p.u_f,cpu_sum->cpu_p.sum_user_p,cpu_sum->cpu_p.sum_sys_p,cpu_sum->cpu_p.sum_total_p);
   fflush(stdout);
	puts("################check mem############");
	printf("send data KB_main_free=%lu KB_main_used=%lu KB_main_cached=%lu KB_main_available=%lu pcnt_tot=%s swp_USE_p=%s KB_main_total=%lu KB_swap_free=%lu\n",cpu_sum->meminfo.KB_main_free,cpu_sum->meminfo.KB_main_used,cpu_sum->meminfo.KB_main_cached,cpu_sum->meminfo.KB_main_available,cpu_sum->meminfo.pcnt_tot,cpu_sum->meminfo.swp_USE_p,cpu_sum->meminfo.KB_main_total,cpu_sum->meminfo.KB_swap_free);	
	fflush(stdout);
	
/*
	//check active process
	puts("#############check active process##############");
        os_data *om1=&os[0];
        printf("total_count=%d  count_RUNING=%d count_sleeping=%d count_stopped=%d count_zombie=%d\n",\
        om1->process_count,om1->process_running_count,om1->process_sleeping_count,om1->process_stopped_count,\
        om1->process_zombie_count);
        puts("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n");
	


	      int mm;
        for(mm=0; mm<o;mm++){
        os_data *osm=&os[mm];
  printf("sizeof os_data=%d threads=%d active_threads=%d pid=%d tgid=%lu user=%s PR=%d NI=%d VIRT=%lu\
 RES=%lu SHR=%lu stat=%c CPU_P=%s MEM_P=%s \
CPU_TIME=%s COMMAND=%s\n",sizeof(os_data),osm->process_pid.threads,osm->process_pid.active_threads,\
osm->process_pid.PID,\
osm->process_pid.TGID,osm->process_pid.USER,\
osm->process_pid.PR,osm->process_pid.NI,osm->process_pid.VIRT,osm->process_pid.RES,osm->process_pid.SHR,\
osm->process_pid.S,osm->process_pid.CPU_P,osm->process_pid.MEM_P,osm->process_pid.CPU_TIME,\
osm->process_pid.COMMAND);
	puts("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n");
	fflush(stdout);
	}
	
	//check active threads
	puts("#############check active threads##############");
        os_data *os11=&os[0];
        printf("total_count=%d  count_RUNING=%d count_sleeping=%d count_stopped=%d count_zombie=%d\n",\
        os11->thread_count,os11->thread_running_count,os11->thread_sleeping_count,os11->thread_stopped_count,\
        os11->thread_zombie_count);
        puts("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n");
	int oo;	
	for(oo=0; oo<o1;oo++){
	os_data *os1=&os[oo];
  printf("sizeof os_data=%d pid=%d tgid=%lu user=%s PR=%d NI=%d VIRT=%lu RES=%lu SHR=%lu stat=%c CPU_P=%s MEM_P=%s \
CPU_TIME=%s COMMAND=%s\n",sizeof(os_data),\
os1->thread_pid.PID,os1->thread_pid.TGID,os1->thread_pid.USER,os1->thread_pid.PR,os1->thread_pid.NI,\
os1->thread_pid.VIRT,\
os1->thread_pid.RES,os1->thread_pid.SHR,os1->thread_pid.S,os1->thread_pid.CPU_P,os1->thread_pid.MEM_P,\
os1->thread_pid.CPU_TIME,os1->thread_pid.COMMAND);
puts("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n");
	fflush(stdout);
      }
      puts("*********************End***************");
      fflush(stdout);
	int iperf;
	puts("os_data perf #####################\n");
	for(iperf=0;iperf<perf_count;iperf++)
	{

		os_data *p=&os[iperf];
		printf("percent=%s process_name=%s fun_name=%s pid=%d\n",p->perf_data.percent,p->perf_data.process_name,p->perf_data.fun_name,p->perf_data.pid);
	}
	
*/
	int isar;
	puts("os_data sar #################\n");
	for (isar=0;isar<sar_count;isar++)
	{

		os_data *p=&os[isar];
		printf("interface_name=%s rxpck=%s txpck=%s rxkB=%s txkB=%s ifutil=%s\n",p->net.interface_name,p->net.rxpck,p->net.txpck,p->net.rxkB,p->net.txkB,p->net.ifutil);

	}

        //printf("o=%d o1=%d sum_cores=%d perf_count=%d\n",o,o1,sum_cores,perf_count);
	printf("os->active_process_count=%d os->active_thread_count=%d os->sar_count=%d \n",os->active_process_count,os->active_thread_count,os->int_count);
	
	//check tcpdump
//	puts("check tcpdump data############################\n");
	char *ss=get_time2();
	printf("#####%s check tcpdump data##################\n",ss);
	free(ss);
	int tcd;
		for (tcd=0;tcd < os->group_s;tcd++){

		os_data *t=&os[tcd];
		printf("tcp[S.] ip_src=%s src_port=%d ip_dst=%s dst_port=%d count=%d group=%d\n",t->m_tcp_s.ip_src,t->m_tcp_s.sport,t->m_tcp_s.ip_dst,t->m_tcp_s.dport,t->m_tcp_s.count,os->group_s);
		if ( tcd >10){
			
			 printf("group >=10,IP 太多只打印总数:%d\n",os->group_s);
			 break;
		}

		}

	printf("每S经过传输层次数=%d\n",test);
	printf("每S经过print_packet=%d\n",test2);
	printf("每S经过pcap_read_packet=%d\n",test3);
	test=0;
	test2=0;
	test3=0;

    // 发送消息
    	if (send(sock,os,o2*sizeof(os_data), MSG_NOSIGNAL) < 0) {
        printf("Send failed\n");
	fflush(stdout);
        char *errlog="send failed, remote server is offline!";
        err(errlog,get_time2());
        	free(os);
		os=NULL;
        close(sock);
	goto top2;

    }

    puts("waiting server send successfull signal..\n");
    char s[1];
    while (recv(sock, &s, sizeof(s), 0) > 0) {   
       //printf("s=%c\n",s[0]); 
       if (s[0] == '1')
       {
         s[0]='\0';
         printf("server already apply data.coninue..\n");
          free(os);
         os=NULL;
         goto top; 
       }
       if (s[0] == 'x')
       {
	  puts("server pool is exhausted.please rtry!\n");
	  free(os);
	  os=NULL;
          close(sock); 
          goto top2 ;
          
       }

    }

    
    if ((recv(sock, &s, sizeof(s), 0)) == 0) {
        printf("Client closed connection\n");
	fflush(stdout);
        char *errlog=" remote server is offline!";
        err(errlog,get_time2());
	free(os);
	os=NULL;
        close(sock);
	goto top2;
    } 
  puts("未确定的错误");  
  return 0;

}
