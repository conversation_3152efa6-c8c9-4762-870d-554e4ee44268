    1  2010-12-21 17:24:20.953888 IP6 (hlim 64, next-header ICMPv6 (58), payload length 72) 2001:db8:1:0:a00:27ff:fef4:4dcf > 2001:db8:1:0:a00:27ff:fef4:4dcf: [icmp6 sum ok] ICMP6, router renumbering, command, seq=0[S,seg=0,maxdelay=0] match(add,ord=0,min=0,max=128,fec0::/48) use(mask=0x0,raflags=0x0,vltime=2592000,pltime=604800,3ffe:501:fffe::/48/16)
    2  2010-12-21 17:24:50.966750 IP6 (hlim 64, next-header ICMPv6 (58), payload length 72) 2001:db8:1:0:a00:27ff:fef4:4dcf > 2001:db8:1:0:a00:27ff:fef4:4dcf: [icmp6 sum ok] ICMP6, router renumbering, command, seq=0[S,seg=0,maxdelay=0] match(add,ord=0,min=0,max=128,fec0::/48) use(mask=0x0,raflags=0x0,vltime=2592000,pltime=604800,3ffe:501:fffe::/48/16)
    3  2010-12-21 17:25:20.976267 IP6 (hlim 64, next-header ICMPv6 (58), payload length 72) 2001:db8:1:0:a00:27ff:fef4:4dcf > 2001:db8:1:0:a00:27ff:fef4:4dcf: [icmp6 sum ok] ICMP6, router renumbering, command, seq=0[S,seg=0,maxdelay=0] match(add,ord=0,min=0,max=128,fec0::/48) use(mask=0x0,raflags=0x0,vltime=2592000,pltime=604800,3ffe:501:fffe::/48/16)
    4  2010-12-21 17:25:20.978460 IP6 (hlim 64, next-header ICMPv6 (58), payload length 72) 2001:db8:1:0:a00:27ff:fef4:4dcf > 2001:db8:1:0:a00:27ff:fef4:4dcf: [icmp6 sum ok] ICMP6, router renumbering, command, seq=16777216[S,seg=0,maxdelay=0] match(change,ord=0,min=0,max=128,3ffe:501:ffff::/48) use(mask=0x0,raflags=0x0,vltime=2592000,pltime=604800,3ffe:501:ffff::/48/16)
    5  2010-12-21 17:25:50.997724 IP6 (hlim 64, next-header ICMPv6 (58), payload length 72) 2001:db8:1:0:a00:27ff:fef4:4dcf > 2001:db8:1:0:a00:27ff:fef4:4dcf: [icmp6 sum ok] ICMP6, router renumbering, command, seq=16777216[S,seg=0,maxdelay=0] match(change,ord=0,min=0,max=128,3ffe:501:ffff::/48) use(mask=0x0,raflags=0x0,vltime=2592000,pltime=604800,3ffe:501:ffff::/48/16)
    6  2010-12-21 17:26:21.005793 IP6 (hlim 64, next-header ICMPv6 (58), payload length 72) 2001:db8:1:0:a00:27ff:fef4:4dcf > 2001:db8:1:0:a00:27ff:fef4:4dcf: [icmp6 sum ok] ICMP6, router renumbering, command, seq=16777216[S,seg=0,maxdelay=0] match(change,ord=0,min=0,max=128,3ffe:501:ffff::/48) use(mask=0x0,raflags=0x0,vltime=2592000,pltime=604800,3ffe:501:ffff::/48/16)
