#!/bin/bash
# 简单验证脚本

echo "手动验证几个时间段的连接数:"

# 找最近几个有时间戳的行
TIMES=$(grep "^[0-9].*Connect" /tmp/mysql-logs/general.log | tail -5 | awk '{print $1" "$2}')

echo "$TIMES" | while read time_stamp; do
    echo "时间: $time_stamp"
    
    # 从这个时间戳开始，到下一个时间戳之间的Connect数
    count=$(awk -v ts="$time_stamp" '
    BEGIN { found=0; count=0 }
    $0 ~ "^"ts && /Connect/ { found=1; count=1; next }
    found && /^[0-9].*Connect/ { exit }
    found && /Connect/ { count++ }
    END { print count }
    ' /tmp/mysql-logs/general.log)
    
    echo "连接数: $count"
    echo "---"
done
