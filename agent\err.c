#include <stdio.h>
#include <stdlib.h>
#include "err.h"
#include <unistd.h>
#include "send.h"
#include <string.h>
#include <errno.h>
 
#define BUFFER_SIZE 100000
#define MAX_LINES 1000
FILE *file;
int err(char* string,char *time2) {
     file = fopen("/tmp/err.log", "a"); // 打开文件
    if (file == NULL) {
        perror("Error opening file");
        return -1;
    }
    //fprintf(file,"%s:%s\n",time2, string);
    fputs(time2,file);
    fputs(":",file);
    fputs(string,file);
    fputs("[",file);
    fputs(server_ip,file);
    fputs("]",file);
    fputs("\n",file);
    fclose(file);
    file=NULL;
    free(time2);
    time2=NULL;
    //keep 1000 records.
    file = fopen("/tmp/err.log", "r");
    if (file == NULL) {
        perror("Error opening file");
        return EXIT_FAILURE;
    }
 
    int j=0;
     
    int i = 0;
    int lastChar;
     while ((lastChar = fgetc(file)) != EOF)
    {
       if (lastChar == '\n')
       {
         j++;
       }
    
    }
     fclose(file);
     file=NULL;
    if (j <=MAX_LINES)
     {
       return 0;

     }
    
    char *buffer = malloc(BUFFER_SIZE * sizeof(char));
   // memset(buffer, 0, BUFFER_SIZE);
    //char buffer[BUFFER_SIZE];
    memset(buffer, 0, BUFFER_SIZE);
    if (buffer == NULL) {
    printf("内存分配失败\n");
    // 处理错误，例如退出程序或者尝试其他的内存分配方式
} else {
    //printf("内存分配成功\n");
    // 使用ptr指向的内存
}
   // printf("总行数=%d\n",j);
    file = fopen("/tmp/err.log", "r");
    char lastChar1; 
     int g=0; 
    while ((lastChar1 = fgetc(file)) != EOF) {
 //       printf("lastchar1=%c\n",lastChar1);
        if (lastChar1 == '\n' && g <j-MAX_LINES) {
            g++; 
        }
        if (g == j-MAX_LINES){
              //  buffer[i] = lastChar1;
               //  strcpy(&buffer[i], &lastChar1);
                if (i == 0)
                {
                        i++;
			continue;
                }
 		buffer[i] = lastChar1;
                i++;
                
          }
    
   } 
//   printf("从第几行开始保存数据=%d\n",g);
 //  printf("count of chars=%d\n",i);
   buffer[0]='B';
    //rewind(temp_file);
   //清空原来文件 
  // truncate(file, 0);
   
   //begin with \n
   //buffer[0]='#';
//   buffer[i+1]='\0';
  //将指针移动到开头
//   rewind(file1);
//   fseek(file1, 0, SEEK_SET);
   if (file == NULL)
 {
   perror ("file");
 }
 else
 {
   fclose (file);
   file=NULL;
 }
      file = fopen("/tmp/err.log", "w");
//    printf("buffer=%s\n",buffer);
    //fprintf(file, "%s", buffer);
    fputs(buffer, file);
/*
     // 使用strtok()函数分割字符串
      char *line;
    size_t len = strlen(buffer);
     line = strtok(buffer, "\n"); 
      while(line != NULL) {
        fprintf(file, "%s\n", line);
        line = strtok(NULL, "\n");
    }
*/
    fclose(file);
    file=NULL;
    free(buffer);
    buffer=NULL;
    return EXIT_SUCCESS;
}
