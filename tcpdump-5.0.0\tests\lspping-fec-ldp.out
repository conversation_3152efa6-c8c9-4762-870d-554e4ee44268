    1  2004-06-14 10:17:05.850284 MPLS (label 100656, tc 6, [S], ttl 64) IP ********.4100 > ********.179: Flags [P.], seq 1860641958:1860641977, ack 2969468967, win 16384, options [nop,nop,TS val 84784152 ecr 84770238], length 19: BGP
    2  2004-06-14 10:17:08.118493 MPLS (label 100688, tc 7, [S], ttl 255) IP ********.4786 > 127.0.0.1.3503: LSP-PINGv1, MPLS Echo Request, seq 1, length: 48
    3  2004-06-14 10:17:08.119504 IP *********.3503 > ********.4786: LSP-PINGv1, MPLS Echo Reply, seq 1, length: 32
    4  2004-06-14 10:17:08.878375 MPLS (label 100704, tc 6, [S], ttl 64) IP ********.2006 > ********.179: Flags [P.], seq 399708866:399708885, ack 708613212, win 16384, options [nop,nop,TS val 84784455 ecr 130411], length 19: B<PERSON>
    5  2004-06-14 10:17:08.978295 MPLS (label 100704, tc 6, [S], ttl 64) IP ********.2006 > ********.179: Flags [.], ack 20, win 16384, options [nop,nop,TS val 84784465 ecr 133413], length 0
    6  2004-06-14 10:17:09.128397 MPLS (label 100688, tc 7, [S], ttl 255) IP ********.4786 > 127.0.0.1.3503: LSP-PINGv1, MPLS Echo Request, seq 2, length: 48
    7  2004-06-14 10:17:09.129192 IP *********.3503 > ********.4786: LSP-PINGv1, MPLS Echo Reply, seq 2, length: 32
    8  2004-06-14 10:17:10.128607 MPLS (label 100688, tc 7, [S], ttl 255) IP ********.4786 > 127.0.0.1.3503: LSP-PINGv1, MPLS Echo Request, seq 3, length: 48
    9  2004-06-14 10:17:10.129475 IP *********.3503 > ********.4786: LSP-PINGv1, MPLS Echo Reply, seq 3, length: 32
   10  2004-06-14 10:17:11.128577 MPLS (label 100688, tc 7, [S], ttl 255) IP ********.4786 > 127.0.0.1.3503: LSP-PINGv1, MPLS Echo Request, seq 4, length: 48
   11  2004-06-14 10:17:11.129418 IP *********.3503 > ********.4786: LSP-PINGv1, MPLS Echo Reply, seq 4, length: 32
   12  2004-06-14 10:17:12.128655 MPLS (label 100688, tc 7, [S], ttl 255) IP ********.4786 > 127.0.0.1.3503: LSP-PINGv1, MPLS Echo Request, seq 5, length: 48
   13  2004-06-14 10:17:12.129573 IP *********.3503 > ********.4786: LSP-PINGv1, MPLS Echo Reply, seq 5, length: 32
