/*
 * Copyright (c) 1988, 1989, 1990, 1991, 1992, 1993, 1994, 1995, 1996, 1997
 *	The Regents of the University of California.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that: (1) source code distributions
 * retain the above copyright notice and this paragraph in its entirety, (2)
 * distributions including binary code include the above copyright notice and
 * this paragraph in its entirety in the documentation or other materials
 * provided with the distribution, and (3) all advertising materials mentioning
 * features or use of this software display the following acknowledgement:
 * ``This product includes software developed by the University of California,
 * Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
 * the University nor the names of its contributors may be used to endorse
 * or promote products derived from this software without specific prior
 * written permission.
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
 * WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
 */

/* \summary: IPv4/IPv6 payload printer */

#ifdef HAVE_CONFIG_H
#include <config.h>
#endif

#include "netdissect-stdinc.h"

#include "netdissect.h"
#include "addrtoname.h"
#include "extract.h"

#include "ip.h"
#include "ipproto.h"

void
ip_demux_print(netdissect_options *ndo,
	       const u_char *bp,
	       u_int length, u_int ver, int fragmented, u_int ttl_hl,
	       uint8_t nh, const u_char *iph)
{
	int advance;
	const char *p_name;

	advance = 0;

again:
	switch (nh) {

	case IPPROTO_AH:
		/* 注释掉AH处理以避免潜在的内存泄漏 */
		ND_PRINT("AH packet (processing disabled to avoid memory leak)");
		break;
		/*
		if (!ND_TTEST_1(bp)) {
			ndo->ndo_protocol = "ah";
			nd_print_trunc(ndo);
			break;
		}
		nh = GET_U_1(bp);
		advance = ah_print(ndo, bp);
		if (advance <= 0)
			break;
		bp += advance;
		length -= advance;
		goto again;
		*/

	case IPPROTO_ESP:
	{
		/* 注释掉ESP处理以避免潜在的内存泄漏 */
		ND_PRINT("ESP packet (processing disabled to avoid memory leak)");
		break;
		/*
		esp_print(ndo, bp, length, iph, ver, fragmented, ttl_hl);
		// Either this has decrypted the payload and
		// printed it, in which case there's nothing more
		// to do, or it hasn't, in which case there's
		// nothing more to do.
		*/
	}

	case IPPROTO_IPCOMP:
	{
		/* 注释掉IPCOMP处理以避免潜在的内存泄漏 */
		ND_PRINT("IPCOMP packet (processing disabled to avoid memory leak)");
		break;
		/*
		ipcomp_print(ndo, bp);
		// Either this has decompressed the payload and
		// printed it, in which case there's nothing more
		// to do, or it hasn't, in which case there's
		// nothing more to do.
		*/
	}

	case IPPROTO_SCTP:
		/* 注释掉SCTP处理以避免潜在的内存泄漏 */
		ND_PRINT("SCTP packet (processing disabled to avoid memory leak)");
		break;
		/* sctp_print(ndo, bp, iph, length); */

	case IPPROTO_DCCP:
		/* 注释掉DCCP处理以避免潜在的内存泄漏 */
		ND_PRINT("DCCP packet (processing disabled to avoid memory leak)");
		break;
		/* dccp_print(ndo, bp, iph, length); */

	case IPPROTO_TCP:
		tcp_print(ndo, bp, length, iph, fragmented);
		break;

	case IPPROTO_UDP:
		udp_print(ndo, bp, length, iph, fragmented, ttl_hl);
		break;

	case IPPROTO_ICMP:
		/*
		if (ver == 4)
			icmp_print(ndo, bp, length, iph, fragmented);
		else {
			ND_PRINT("[%s requires IPv4]",
				 tok2str(ipproto_values,"unknown",nh));
			nd_print_invalid(ndo);
		}
		*/
		break;

	case IPPROTO_ICMPV6:
		/* 注释掉ICMPv6处理以避免潜在的内存泄漏 */
		ND_PRINT("ICMPv6 packet (processing disabled to avoid memory leak)");
		break;
		/*
		if (ver == 6)
			icmp6_print(ndo, bp, length, iph, fragmented);
		else {
			ND_PRINT("[%s requires IPv6]",
				 tok2str(ipproto_values,"unknown",nh));
			nd_print_invalid(ndo);
		}
		*/

	case IPPROTO_PIGP:
		/* 注释掉PIGP/IGRP处理以避免潜在的内存泄漏 */
		ND_PRINT("PIGP/IGRP packet (processing disabled to avoid memory leak)");
		break;
		/*
		// XXX - the current IANA protocol number assignments
		// page lists 9 as "any private interior gateway
		// (used by Cisco for their IGRP)" and 88 as
		// "EIGRP" from Cisco.
		//
		// Recent BSD <netinet/in.h> headers define
		// IP_PROTO_PIGP as 9 and IP_PROTO_IGRP as 88.
		// We define IP_PROTO_PIGP as 9 and
		// IP_PROTO_EIGRP as 88; those names better
		// match was the current protocol number
		// assignments say.
		igrp_print(ndo, bp, length);
		*/

	case IPPROTO_EIGRP:
		/* 注释掉EIGRP处理以避免潜在的内存泄漏 */
		ND_PRINT("EIGRP packet (processing disabled to avoid memory leak)");
		break;
		/* eigrp_print(ndo, bp, length); */

	case IPPROTO_ND:
		/* 注释掉ND处理以避免潜在的内存泄漏 */
		ND_PRINT("ND packet (processing disabled to avoid memory leak)");
		break;
		/* ND_PRINT(" nd %u", length); */

	case IPPROTO_EGP:
		/* 注释掉EGP处理以避免潜在的内存泄漏 */
		ND_PRINT("EGP packet (processing disabled to avoid memory leak)");
		break;
		/* egp_print(ndo, bp, length); */

	case IPPROTO_OSPF:
		/* 注释掉OSPF处理以避免潜在的内存泄漏 */
		ND_PRINT("OSPF packet (processing disabled to avoid memory leak)");
		break;
		/*
		if (ver == 6)
			ospf6_print(ndo, bp, length);
		else
			ospf_print(ndo, bp, length, iph);
		*/

	case IPPROTO_IGMP:
		/* 注释掉IGMP处理以避免潜在的内存泄漏 */
		ND_PRINT("IGMP packet (processing disabled to avoid memory leak)");
		break;
		/*
		if (ver == 4)
			igmp_print(ndo, bp, length);
		else {
			ND_PRINT("[%s requires IPv4]",
				 tok2str(ipproto_values,"unknown",nh));
			nd_print_invalid(ndo);
		}
		*/

	case IPPROTO_IPV4:
		/* 注释掉IPv4-in-IP处理以避免潜在的内存泄漏 */
		ND_PRINT("IPv4-in-IP packet (processing disabled to avoid memory leak)");
		break;
		/* ip_print(ndo, bp, length); */

	case IPPROTO_IPV6:
		/* 注释掉IPv6-in-IP处理以避免潜在的内存泄漏 */
		ND_PRINT("IPv6-in-IP packet (processing disabled to avoid memory leak)");
		break;
		/* ip6_print(ndo, bp, length); */

	case IPPROTO_RSVP:
		/* 注释掉RSVP处理以避免潜在的内存泄漏 */
		ND_PRINT("RSVP packet (processing disabled to avoid memory leak)");
		break;
		/* rsvp_print(ndo, bp, length); */

	case IPPROTO_GRE:
		/* 注释掉GRE处理以避免潜在的内存泄漏 */
		ND_PRINT("GRE packet (processing disabled to avoid memory leak)");
		break;
		/* gre_print(ndo, bp, length); */

	case IPPROTO_MOBILE:
		/* 注释掉Mobile IP处理以避免潜在的内存泄漏 */
		ND_PRINT("Mobile IP packet (processing disabled to avoid memory leak)");
		break;
		/* mobile_print(ndo, bp, length); */

	case IPPROTO_PIM:
		/* 注释掉PIM处理以避免潜在的内存泄漏 */
		ND_PRINT("PIM packet (processing disabled to avoid memory leak)");
		break;
		/* pim_print(ndo, bp, length, iph); */

	case IPPROTO_VRRP:
		/* 注释掉VRRP/CARP处理以避免潜在的内存泄漏 */
		ND_PRINT("VRRP/CARP packet (processing disabled to avoid memory leak)");
		break;
		/*
		if (ndo->ndo_packettype == PT_CARP) {
			carp_print(ndo, bp, length, ttl_hl);
		} else {
			vrrp_print(ndo, bp, length, iph, ttl_hl, ver);
		}
		*/

	case IPPROTO_PGM:
		/* 注释掉PGM处理以避免潜在的内存泄漏 */
		ND_PRINT("PGM packet (processing disabled to avoid memory leak)");
		break;
		/* pgm_print(ndo, bp, length, iph); */

	case IPPROTO_ETHERNET:
		/* 注释掉Ethernet处理以避免潜在的内存泄漏 */
		ND_PRINT("Ethernet packet (processing disabled to avoid memory leak)");
		break;
		/*
		if (ver == 6)
			ether_print(ndo, bp, length, ND_BYTES_AVAILABLE_AFTER(bp), NULL, NULL);
		else {
			ND_PRINT("[%s requires IPv6]",
				 tok2str(ipproto_values,"unknown",nh));
			nd_print_invalid(ndo);
		}
		*/

	case IPPROTO_NONE:
		ND_PRINT("no next header");
		break;

	default:
		if (ndo->ndo_nflag==0 && (p_name = netdb_protoname(nh)) != NULL)
			ND_PRINT(" %s", p_name);
		else
			ND_PRINT(" ip-proto-%u", nh);
		ND_PRINT(" %u", length);
		break;
	}
}
