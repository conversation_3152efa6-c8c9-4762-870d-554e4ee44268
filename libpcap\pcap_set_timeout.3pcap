.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_SET_TIMEOUT 3PCAP "6 December 2017"
.SH NAME
pcap_set_timeout \- set the packet buffer timeout for a
not-yet-activated capture handle
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.LP
.ft B
int pcap_set_timeout(pcap_t *p, int to_ms);
.ft
.fi
.SH DESCRIPTION
.BR pcap_set_timeout ()
sets the packet buffer timeout that will be used on a capture handle
when the handle is activated to
.IR to_ms ,
which is in units of milliseconds.  (See
.BR pcap (3PCAP)
for an explanation of the packet buffer timeout.)
.LP
The behavior, if the timeout isn't specified, is undefined, as is the
behavior if the timeout is set to zero or to a negative value.  We
recommend always setting the timeout to a non-zero value unless
immediate mode is set, in which case the timeout has no effect.
.SH RETURN VALUE
.BR pcap_set_timeout ()
returns
.B 0
on success or
.B PCAP_ERROR_ACTIVATED
if called on a capture handle that has been activated.
.SH SEE ALSO
.BR pcap_create (3PCAP),
.BR pcap_activate (3PCAP),
.BR \%pcap_set_immediate_mode (3PCAP)
