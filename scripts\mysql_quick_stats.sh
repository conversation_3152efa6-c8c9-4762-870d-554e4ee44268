#!/bin/bash
# MySQL连接统计分析脚本

GENERAL_LOG="/tmp/mysql-logs/general.log"

echo "========================================="
echo "MySQL连接统计分析"
echo "========================================="
echo "日志文件: $GENERAL_LOG"
echo "分析时间: $(date)"
echo ""

# 统计总体数据
TOTAL_CONNECTS=$(grep -c "Connect" "$GENERAL_LOG")
TOTAL_QUITS=$(grep -c "Quit" "$GENERAL_LOG")
TOTAL_QUERIES=$(grep -c "Query" "$GENERAL_LOG")

echo "总体统计:"
echo "总连接数: $TOTAL_CONNECTS"
echo "总断开数: $TOTAL_QUITS"
echo "总查询数: $TOTAL_QUERIES"
echo ""

# 客户端IP统计 - 按连接数排序
echo "客户端IP连接统计 (按连接数排序):"
echo "连接数    客户端IP"
echo "------------------------"
grep "Connect" "$GENERAL_LOG" | \
    sed -n 's/.*Connect.*@\([^[:space:]]*\).*/\1/p' | \
    sort | uniq -c | sort -nr | \
    awk '{printf "%6d    %s\n", $1, $2}'

echo ""

# 查询类型统计 - 按数量排序
echo "查询类型统计 (按数量排序):"
echo "查询数    查询类型"
echo "------------------------"
grep "Query" "$GENERAL_LOG" | \
    awk '{print $NF}' | \
    sort | uniq -c | sort -nr | head -10 | \
    awk '{printf "%6d    %s\n", $1, $2}'
