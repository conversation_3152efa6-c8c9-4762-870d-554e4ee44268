# The internal git client reads CIRRUS_CLONE_DEPTH.
env:
  CIRRUS_CLONE_DEPTH: 3
  AUTOCONF_WARNINGS: no-obsolete

freebsd_task:
  name: freebsd-amd64
  only_if: $CIRRUS_BRANCH != 'coverity_scan'
  freebsd_instance:
    cpu: 1
    memory: 2G
    image_family: freebsd-13-5
  env:
    IGNORE_OSVERSION: yes
    MATRIX_CC: clang17 gcc13
  script:
    - pkg install -qy autoconf gcc13 llvm17
    - pkg install -qy cmake-core git-tiny # for build_matrix.sh and build.sh
    - ./build_matrix.sh

linux_task:
  name: linux-amd64
  only_if: $CIRRUS_BRANCH != 'coverity_scan'
  container:
    image: ubuntu:24.04
    cpu: 1
    memory: 1G
  env:
    DEBIAN_FRONTEND: noninteractive
    LANG: C
  script:
    - apt-get -qy update >/dev/null
    - apt-get -qy install flex bison autoconf make gcc >/dev/null # for "./configure"
    - apt-get -qy install cmake git >/dev/null # for "make releasecheck"
    - apt-get -qy install shellcheck >/dev/null
    - ./autogen.sh
    - ./configure --quiet # build the Makefile
    - make releasecheck
    - make whitespacecheck
    - make shellcheck

macos_task:
  name: macos-aarch64
  only_if: $CIRRUS_BRANCH != 'coverity_scan'
  macos_instance:
    image: ghcr.io/cirruslabs/macos-runner:sonoma # last 3 versions of Xcode
  env:
    MAKEFLAGS: '-j 4' # macOS VMs run on 4 cores
    TESTRUN_JOBS: '4'
  script:
    - brew update >/dev/null
    - brew install openssl@3
    - ./build_matrix.sh

coverity_task:
  name: Coverity Scan
  only_if: $CIRRUS_BRANCH == 'coverity_scan'
  container:
    image: ubuntu:24.04
    cpu: 1
    memory: 2G
  env:
    DEBIAN_FRONTEND: noninteractive
    COVERITY_SCAN_PROJECT_NAME: $CIRRUS_REPO_FULL_NAME
    COVERITY_SCAN_TOKEN: ENCRYPTED[58bfbfcf624e5b7b85fb9df95dd0b3f9f93642824e6ae94616e4d345af4848580932a6ece02337fee112194b29ce6593]
    COVERITY_SCAN_BUILD_COMMAND_PREPEND: ./configure --enable-remote --enable-optimizer-dbg --enable-yydebug
    COVERITY_SCAN_BUILD_COMMAND: make
    LANG: C
  script:
    - apt-get -qy update >/dev/null
    - apt-get -qy install libdbus-1-dev libbluetooth-dev libnl-genl-3-dev libibverbs-dev libssl-dev >/dev/null
    - apt-get -qy install flex bison autoconf make gcc >/dev/null
    - apt-get -qy install git curl wget ruby rubygems ruby-json >/dev/null # for the coverity script
    - apt list --installed 'lib*-dev'
    - ./autogen.sh
    - ./.ci-coverity-scan-build.sh
