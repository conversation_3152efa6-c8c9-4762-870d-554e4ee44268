.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_DUMP_FTELL 3PCAP "25 July 2018"
.SH NAME
pcap_dump_ftell, pcap_dump_ftell64 \- get the current file offset for a savefile being written
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
long pcap_dump_ftell(pcap_dumper_t *p);
.ft B
int64_t pcap_dump_ftell64(pcap_dumper_t *p);
.ft
.fi
.SH DESCRIPTION
.BR pcap_dump_ftell ()
returns the current file position for the ``savefile'', representing the
number of bytes written by
.BR pcap_dump_open (3PCAP)
and
.BR pcap_dump (3PCAP).
.B PCAP_ERROR
is returned on error. If the current file position does not fit in a
.BR long ,
it will be truncated; this can happen on 32-bit UNIX-like systems with
large file support and on Windows.
.BR pcap_dump_ftell64 ()
returns the current file position in a
.BR int64_t ,
so if file offsets that don't fit in a
.B long
but that fit in a
.B int64_t
are supported, this will return the file offset without truncation.
.B PCAP_ERROR
is returned on error.
.SH BACKWARD COMPATIBILITY
The function
.BR pcap_dump_ftell64 ()
became available in libpcap release 1.9.0.  In previous releases, there
was no mechanism to obtain a file offset that is too large to fit in a
.BR long .
.SH SEE ALSO
.BR pcap (3PCAP)
