    1  15:35:08.640523 IP (tos 0x0, ttl 128, id 376, offset 0, flags [DF], proto TCP (6), length 128)
    *************.445 > **************.49199: Flags [P.], cksum 0x3e2f (incorrect -> 0x3e31), seq 4267808374:4267808462, ack *********, win 63102, length 88 
SMB PACKET: SMBtrans2 (REPLY)
SMB Command   =  0x32
Error class   =  0x0
Error code    =  0 (0x0)
Flags1        =  0x96
Flags2        =  0x7
Tree ID       =  2048 (0x800)
Proc ID       =  2848 (0xb20)
UID           =  4098 (0x1002)
MID           =  1616 (0x650)
Word Count    =  10 (0xa)
TRANSACT2_OPEN param_length=2 data_length=24
TotParam=2 (0x2)
TotData=24 (0x18)
Res1=0x0
ParamCnt=2 (0x2)
ParamOff=56 (0x38)
ParamDisp0 (0x0)
DataCnt=24 (0x18)
DataOff=60 (0x3c)
DataDisp=0 (0x0)
SetupCnt=0 (0x0)
smb_bcc=29
Handle=0 (0x0)
Attrib=Data=
Data: (24 bytes)
[000] 00 00 0B 00 00 00 00 00  00 00 00 00 00 00 00 00  ^@^@^K^@^@^@^@^@ ^@^@^@^@^@^@^@^@
[010] 01 00 00 00 00 00 00 00                           ^A^@^@^@^@^@^@^@ 

    2  15:35:08.640906 IP (tos 0x0, ttl 128, id 632, offset 0, flags [DF], proto TCP (6), length 114)
    **************.49199 > *************.445: Flags [P.], cksum 0x2437 (correct), seq 1:75, ack 88, win 254, length 74 
SMB PACKET: SMBtrans2 (REQUEST)
SMB Command   =  0x32
Error class   =  0x0
Error code    =  0 (0x0)
Flags1        =  0x18
Flags2        =  0x7
Tree ID       =  2048 (0x800)
Proc ID       =  2848 (0xb20)
UID           =  4098 (0x1002)
MID           =  1632 (0x660)
Word Count    =  15 (0xf)
TRANSACT2_QFSINFO param_length=2 data_length=0
TotParam=2 (0x2)
TotData=0 (0x0)
MaxParam=0 (0x0)
MaxData=560 (0x230)
MaxSetup=0 (0x0)
Flags=0x0
TimeOut=0 (0x0)
Res1=0x0
ParamCnt=2 (0x2)
ParamOff=68 (0x44)
DataCnt=0 (0x0)
DataOff=0 (0x0)
SetupCnt=1 (0x1)
smb_bcc=5
InfoLevel=261 (0x105)

    3  15:35:08.641033 IP (tos 0x0, ttl 128, id 377, offset 0, flags [DF], proto TCP (6), length 120)
    *************.445 > **************.49199: Flags [P.], cksum 0x00fb (incorrect -> 0x11f5), seq 88:168, ack 75, win 62978, length 80 
SMB PACKET: SMBtrans2 (REPLY)
SMB Command   =  0x32
Error class   =  0x0
Error code    =  0 (0x0)
Flags1        =  0x98
Flags2        =  0x0
Tree ID       =  2048 (0x800)
Proc ID       =  2848 (0xb20)
UID           =  4098 (0x1002)
MID           =  1632 (0x660)
Word Count    =  10 (0xa)
TRANSACT2_QFSINFO param_length=0 data_length=20
TotParam=0 (0x0)
TotData=20 (0x14)
Res1=0x0
ParamCnt=0 (0x0)
ParamOff=56 (0x38)
ParamDisp0 (0x0)
DataCnt=20 (0x14)
DataOff=56 (0x38)
DataDisp=0 (0x0)
SetupCnt=0 (0x0)
smb_bcc=21
Capabilities=0x700FF
MaxFileLen=255 (0xff)
VolNameLen=4293394440
Volume=M [|smb]
data:
[000] FF 00 07 00 FF 00 00 00  08 00 E8 FF 4D 00 54 00  M-^?^@^G^@M-^?^@^@^@ ^H^@M-hM-^?M^@T^@
[010] 46 00 53 00                                       F^@S^@ 

    4  15:35:08.641358 IP (tos 0x0, ttl 128, id 633, offset 0, flags [DF], proto TCP (6), length 116)
    **************.49199 > *************.445: Flags [P.], cksum 0x2253 (correct), seq 75:151, ack 168, win 253, length 76 
SMB PACKET: SMBtrans2 (REQUEST)
SMB Command   =  0x32
Error class   =  0x0
Error code    =  0 (0x0)
Flags1        =  0x18
Flags2        =  0x7
Tree ID       =  2048 (0x800)
Proc ID       =  2848 (0xb20)
UID           =  4098 (0x1002)
MID           =  1648 (0x670)
Word Count    =  15 (0xf)
TRANSACT2_QFILEINFO param_length=4 data_length=0
TotParam=4 (0x4)
TotData=0 (0x0)
MaxParam=2 (0x2)
MaxData=40 (0x28)
MaxSetup=0 (0x0)
Flags=0x0
TimeOut=0 (0x0)
Res1=0x0
ParamCnt=4 (0x4)
ParamOff=68 (0x44)
DataCnt=0 (0x0)
DataOff=0 (0x0)
SetupCnt=1 (0x1)
smb_bcc=7
Parameters=
Data: (4 bytes)
[000] 0C 40 EC 03                                       ^L@M-l^C 
Data=

