.TH SA2 8 "JUNE 2014" Linux "Linux User's Manual" -*- nroff -*-
.SH NAME
sa2 \- Create a report from the current standard system activity daily data file.
.SH SYNOPSIS
.B @SA_LIB_DIR@/sa2
.SH DESCRIPTION
The
.B sa2
command is a shell procedure variant of the
.B sar
command which writes a daily report in the
.I sarDD
or the
.I sarYYYYMMDD
file, where YYYY stands for the current year, MM for the current month
and DD for the current day.
By default the report is saved in the
.I @SA_DIR@
directory.
The
.B sa2
command will also remove reports more than one week old by default.
You can however keep reports for a longer (or a shorter) period by setting
the
.B HISTORY
environment variable. Read the
.BR sysstat (5)
manual page for details.

The
.B sa2
command accepts most of the flags and parameters of the
.B sar
command.

The
.B sa2
command is designed to be started automatically by the cron command.

.SH EXAMPLES
To run the
.B sa2
command daily, place the following entry in your root crontab file:

.B 5 19 * * 1-5 @SA_LIB_DIR@/sa2 -A &

This will generate by default a daily report called
.I sarDD
in the
.I @SA_DIR@
directory, where the DD parameter is a number representing the day of the
month.
.SH FILES
.I @SA_DIR@/sarDD
.br
.I @SA_DIR@/sarYYYYMMDD
.RS
The standard system activity daily report files and their default location.
YYYY stands for the current year, MM for the current month and DD for the
current day.
.SH AUTHOR
Sebastien Godard (sysstat <at> orange.fr)
.SH SEE ALSO
.BR sar (1),
.BR sadc (8),
.BR sa1 (8),
.BR sadf (1),
.BR sysstat (5)

.I http://pagesperso-orange.fr/sebastien.godard/
