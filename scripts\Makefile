# 数据库连接模拟器 Makefile

CC = gcc
CFLAGS = -Wall -Wextra -O2 -std=c99 -D_GNU_SOURCE
LDFLAGS = -pthread
MYSQL_CFLAGS = $(shell mysql_config --cflags 2>/dev/null || pkg-config --cflags mysqlclient 2>/dev/null || echo "-I/usr/include/mysql")
MYSQL_LIBS = $(shell mysql_config --libs 2>/dev/null || pkg-config --libs mysqlclient 2>/dev/null || echo "-lmysqlclient")

TARGET1 = db_connection_simulator
TARGET2 = db_simple_test
SOURCE1 = db_connection_simulator.c
SOURCE2 = db_simple_test.c

.PHONY: all clean install help simple

all: $(TARGET1) $(TARGET2)

simple: $(TARGET2)

$(TARGET1): $(SOURCE1)
	@echo "编译数据库连接模拟器..."
	$(CC) $(CFLAGS) $(MYSQL_CFLAGS) -o $(TARGET1) $(SOURCE1) $(LDFLAGS) $(MYSQL_LIBS)
	@echo "编译完成: $(TARGET1)"

$(TARGET2): $(SOURCE2)
	@echo "编译简单测试程序..."
	$(CC) $(CFLAGS) $(MYSQL_CFLAGS) -o $(TARGET2) $(SOURCE2) $(LDFLAGS) $(MYSQL_LIBS)
	@echo "编译完成: $(TARGET2)"

clean:
	@echo "清理编译文件..."
	rm -f $(TARGET1) $(TARGET2)
	@echo "清理完成"

install: $(TARGET)
	@echo "安装到 /usr/local/bin/..."
	sudo cp $(TARGET) /usr/local/bin/
	sudo chmod +x /usr/local/bin/$(TARGET)
	@echo "安装完成"

help:
	@echo "数据库连接模拟器 Makefile"
	@echo ""
	@echo "可用目标:"
	@echo "  all     - 编译程序 (默认)"
	@echo "  clean   - 清理编译文件"
	@echo "  install - 安装到系统路径"
	@echo "  help    - 显示此帮助信息"
	@echo ""
	@echo "使用示例:"
	@echo "  make"
	@echo "  make clean"
	@echo "  make install"

# 检查依赖
check-deps:
	@echo "检查编译依赖..."
	@which gcc >/dev/null || (echo "错误: 需要安装 gcc" && exit 1)
	@which mysql_config >/dev/null || pkg-config --exists mysqlclient || (echo "错误: 需要安装 MySQL/MariaDB 开发包" && exit 1)
	@echo "依赖检查通过"
