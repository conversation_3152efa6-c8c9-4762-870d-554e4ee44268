#!/bin/bash
# MySQL正确的连接统计

GENERAL_LOG="/tmp/mysql-logs/general.log"

echo "MySQL连接统计 (修正版)"
echo "====================="

# 统计总连接数
TOTAL_CONNECTS=$(grep -c "Connect" "$GENERAL_LOG")
echo "总连接数: $TOTAL_CONNECTS"

# 只统计连续的每秒数据
echo ""
echo "每秒连接数统计 (只统计连续的秒):"

# 获取所有有时间戳的Connect行的时间
grep "^[0-9].*Connect" "$GENERAL_LOG" | awk '{print $1" "$2}' | while read current_time; do
    # 计算下一秒的时间
    hour=$(echo $current_time | cut -d' ' -f2 | cut -d':' -f1)
    minute=$(echo $current_time | cut -d' ' -f2 | cut -d':' -f2)
    second=$(echo $current_time | cut -d' ' -f2 | cut -d':' -f3)
    
    next_second=$((second + 1))
    next_minute=$minute
    next_hour=$hour
    
    if [ $next_second -eq 60 ]; then
        next_second=0
        next_minute=$((minute + 1))
        if [ $next_minute -eq 60 ]; then
            next_minute=0
            next_hour=$((hour + 1))
        fi
    fi
    
    next_time=$(printf "250719 %02d:%02d:%02d" $next_hour $next_minute $next_second)
    
    # 检查下一秒是否存在
    if grep -q "^$next_time" "$GENERAL_LOG"; then
        # 统计这一秒的连接数
        count=$(awk -v ts="$current_time" -v next_ts="$next_time" '
        BEGIN { found=0; count=0 }
        $0 ~ "^"ts && /Connect/ { found=1; count=1; next }
        found && $0 ~ "^"next_ts { exit }
        found && /Connect/ { count++ }
        END { print count }
        ' "$GENERAL_LOG")
        
        echo "$count $current_time"
    fi
done | sort -nr | head -20 | awk '{printf "%4d连接/秒  %s %s\n", $1, $2, $3}'

echo ""
echo "客户端IP连接统计:"
grep "Connect.*@" "$GENERAL_LOG" | \
    sed 's/.*@\([^[:space:]]*\).*/\1/' | \
    sort | uniq -c | sort -nr | \
    awk '{printf "%8d连接  %s\n", $1, $2}'
