/*
 * network_security.c: Network security monitoring functions
 * 网络安全监控函数
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include <ctype.h>
#include <ctype.h>
#include <arpa/inet.h>
#include <sys/types.h>
#include <dirent.h>

#include "common.h"
#include "rd_stats.h"

// 进程-socket映射结构（轻量级设计）
struct process_socket_map {
    unsigned long inode;
    int pid;
    char process_name[16];  // 限制进程名长度，节省内存
};

// 全局映射表（静态分配，避免动态内存开销）
static struct process_socket_map socket_map[512];  // 限制512个映射，约8KB内存
static int socket_map_count = 0;

/*
 * 快速建立socket inode到进程的映射表
 * 一次性扫描，避免重复文件系统操作
 */
static void build_socket_process_map(void) {
    DIR *proc_dir, *fd_dir;
    struct dirent *proc_entry, *fd_entry;
    char path[256], link_target[256];
    char comm_path[256], comm[32];
    FILE *comm_file;
    int pid;
    unsigned long inode;
    ssize_t link_len;
    int processed_count = 0;

    socket_map_count = 0;  // 重置映射表

    proc_dir = opendir("/proc");
    if (!proc_dir) return;

    // 遍历 /proc 目录，添加处理数量限制
    while ((proc_entry = readdir(proc_dir)) != NULL && socket_map_count < 512 && processed_count < 200) {
        // 只处理数字目录（进程ID）
        pid = atoi(proc_entry->d_name);
        if (pid <= 0) continue;

        processed_count++;  // 限制处理的进程数量

        // 构建 /proc/PID/fd 路径
        snprintf(path, sizeof(path), "/proc/%d/fd", pid);
        fd_dir = opendir(path);
        if (!fd_dir) continue;

        // 获取进程名（只读取一次）
        snprintf(comm_path, sizeof(comm_path), "/proc/%d/comm", pid);
        comm_file = fopen(comm_path, "r");
        if (comm_file) {
            if (fgets(comm, sizeof(comm), comm_file)) {
                // 移除换行符
                char *newline = strchr(comm, '\n');
                if (newline) *newline = '\0';
            } else {
                strcpy(comm, "unknown");
            }
            fclose(comm_file);
        } else {
            strcpy(comm, "unknown");
        }

        // 遍历该进程的文件描述符，限制扫描数量
        int fd_count = 0;
        while ((fd_entry = readdir(fd_dir)) != NULL && socket_map_count < 512 && fd_count < 50) {
            // 跳过非数字的文件描述符
            if (!isdigit(fd_entry->d_name[0])) continue;
            fd_count++;

            // 构建文件描述符路径
            snprintf(path, sizeof(path), "/proc/%d/fd/%s", pid, fd_entry->d_name);

            // 读取符号链接
            link_len = readlink(path, link_target, sizeof(link_target) - 1);
            if (link_len > 0) {
                link_target[link_len] = '\0';

                // 检查是否是socket
                if (sscanf(link_target, "socket:[%lu]", &inode) == 1) {
                    // 添加到映射表
                    socket_map[socket_map_count].inode = inode;
                    socket_map[socket_map_count].pid = pid;
                    strncpy(socket_map[socket_map_count].process_name, comm, 15);
                    socket_map[socket_map_count].process_name[15] = '\0';
                    socket_map_count++;
                }
            }
        }
        closedir(fd_dir);
    }
    closedir(proc_dir);
}

/*
 * 通过socket inode快速查找进程信息
 * O(n)查找，但n通常很小（<512）
 */
static const char* get_process_by_inode(unsigned long inode) {
    static char result[32];

    for (int i = 0; i < socket_map_count; i++) {
        if (socket_map[i].inode == inode) {
            snprintf(result, sizeof(result), "%s(%d)",
                    socket_map[i].process_name, socket_map[i].pid);
            return result;
        }
    }
    return "unknown";
}

// 网络安全统计数据结构（按照sysstat风格定义）
struct stats_net_security {
    // TCP连接统计
    unsigned int total_connections;
    unsigned int established_connections;
    unsigned int syn_recv_connections;
    unsigned int time_wait_connections;
    unsigned int listen_connections;

    // 单IP连接统计
    unsigned int max_connections_per_ip;

    // MySQL数据库连接监控统计
    unsigned int mysql_connections_per_second;   // MySQL连接数/秒
    unsigned int mysql_aborted_connections;      // MySQL断开连接数
    unsigned int max_mysql_conn_per_ip;          // 单IP最大MySQL连接数
    char high_freq_mysql_client_ip[16];          // 高频MySQL客户端IP
    unsigned int mysql_monitoring_enabled;       // MySQL监控是否启用(0=禁用,1=启用)
    char mysql_variant[32];                      // MySQL发行版类型(MariaDB/MySQL/Percona等)
    int mysql_ports[8];                          // MySQL监听的端口列表
    int mysql_port_count;                        // MySQL端口数量
    char mysql_log_status[64];                   // MySQL日志状态说明
    char mysql_log_path[256];                    // MySQL日志文件路径

    // 网络流量统计
    unsigned long long rx_bytes;
    unsigned long long tx_bytes;
    unsigned long long rx_packets;
    unsigned long long tx_packets;
    unsigned long long rx_bytes_per_sec;
    unsigned long long tx_bytes_per_sec;
    unsigned long long rx_packets_per_sec;
    unsigned long long tx_packets_per_sec;

    // 异常检测
    int anomaly_score;
    int trigger_packet_analysis;
    char anomaly_reason[64];

    // 端口统计
    struct {
        int port;                   // 端口号
        int total_connections;      // 总连接数
        int established;            // ESTABLISHED连接数
        int time_wait;              // TIME_WAIT连接数
        int syn_recv;               // SYN_RECV连接数
        int listen;                 // LISTEN连接数
        char service_name[16];      // 服务名称
    } port_stats[10];               // 最多统计10个端口
    int port_stats_count;           // 实际统计的端口数量

    // 详细IP+端口统计
    struct {
        char ip_address[16];        // IP地址
        int port;                   // 端口号
        int connection_count;       // 连接数
        char service_name[16];      // 服务名称
        char process_info[32];      // 进程信息：进程名(PID)
    } ip_port_stats[20];            // 最多统计20个IP+端口组合
    int ip_port_stats_count;        // 实际统计的IP+端口组合数量
};

#define STATS_NET_SECURITY_SIZE (sizeof(struct stats_net_security))

/*
 ***************************************************************************
 * Read network security statistics from /proc/net/tcp and network interfaces.
 *
 * IN:
 * @st_net_security	Structure where stats will be saved.
 *
 * OUT:
 * @st_net_security	Structure with statistics.
 ***************************************************************************
 */
// 动态端口统计结构
struct dynamic_port_stats {
    unsigned int port;
    unsigned int total_connections;
    unsigned int established;
    unsigned int time_wait;
    unsigned int syn_recv;
    char detected_service[16];
};

// IP连接统计结构
struct ip_connection_stats {
    char ip_address[16];
    unsigned int port;
    unsigned int connection_count;
    char process_info[32];  // 进程信息：进程名(PID)
};

// MySQL数据库连接监控结构
struct mysql_connection_stats {
    char ip_address[16];
    unsigned int connection_count;   // 该IP的MySQL连接数
    time_t last_seen;               // 最后一次连接时间
};

// 从配置文件检测MySQL日志路径
static char* get_mysql_log_from_config() {
    FILE* fp;
    char line[512];
    static char log_path[256] = {0};

    // 常见的MySQL配置文件位置
    const char* config_files[] = {
        "/etc/mysql/my.cnf",
        "/etc/my.cnf",
        "/usr/local/mysql/my.cnf",
        "/var/lib/mysql/my.cnf",
        "/etc/mysql/mysql.conf.d/mysqld.cnf",
        "/etc/mysql/mariadb.conf.d/50-server.cnf",
        "/etc/mysql/mariadb.conf.d/99-custom-logging.cnf",
        NULL
    };

    // 扫描配置文件
    for (int i = 0; config_files[i] != NULL; i++) {
        fp = fopen(config_files[i], "r");
        if (fp != NULL) {
            while (fgets(line, sizeof(line), fp)) {
                // 跳过注释和空行
                char* trimmed = line;
                while (*trimmed == ' ' || *trimmed == '\t') trimmed++;
                if (*trimmed == '#' || *trimmed == '\n' || *trimmed == '\0') continue;

                // 查找log_error配置项
                if (strstr(trimmed, "log_error") && strstr(trimmed, "=")) {
                    char* eq = strchr(trimmed, '=');
                    if (eq != NULL) {
                        eq++; // 跳过等号
                        while (*eq == ' ' || *eq == '\t') eq++; // 跳过空格

                        // 提取路径
                        char* end = eq;
                        while (*end != '\0' && *end != '\n' && *end != '\r' && *end != ' ' && *end != '\t') end++;
                        *end = '\0';

                        if (strlen(eq) > 0) {
                            strncpy(log_path, eq, sizeof(log_path) - 1);
                            log_path[sizeof(log_path) - 1] = '\0';
                            fclose(fp);
                            return log_path;
                        }
                    }
                }
            }
            fclose(fp);
        }
    }

    return NULL;
}

// 从数据库变量获取日志路径
static char* get_mysql_log_from_database() {
    FILE* fp;
    char line[512];
    static char log_path[256] = {0};

    fp = popen("mysql -e \"SHOW VARIABLES LIKE 'log_error';\" 2>/dev/null", "r");
    if (fp != NULL) {
        // 跳过表头
        if (fgets(line, sizeof(line), fp) != NULL) {
            // 读取数据行
            if (fgets(line, sizeof(line), fp) != NULL) {
                // 解析格式: log_error	/tmp/mysql-logs/error.log
                char* tab = strchr(line, '\t');
                if (tab != NULL) {
                    tab++; // 跳过制表符
                    char* end = tab;
                    while (*end != '\0' && *end != '\n' && *end != '\r' && *end != '\t') end++;
                    *end = '\0';

                    if (strlen(tab) > 0) {
                        strncpy(log_path, tab, sizeof(log_path) - 1);
                        log_path[sizeof(log_path) - 1] = '\0';
                        pclose(fp);
                        return log_path;
                    }
                }
            }
        }
        pclose(fp);
    }

    return NULL;
}

// 检测MySQL数据库日志位置
static char* detect_mysql_log_path() {
    static char found_path[256];
    char* log_path = NULL;

    // 1. 先从配置文件查找
    log_path = get_mysql_log_from_config();
    if (log_path != NULL && access(log_path, R_OK) == 0) {
        strncpy(found_path, log_path, sizeof(found_path) - 1);
        found_path[sizeof(found_path) - 1] = '\0';
        return found_path;
    }

    // 2. 再从数据库变量查找
    log_path = get_mysql_log_from_database();
    if (log_path != NULL && access(log_path, R_OK) == 0) {
        strncpy(found_path, log_path, sizeof(found_path) - 1);
        found_path[sizeof(found_path) - 1] = '\0';
        return found_path;
    }

    // 3. 最后回退到硬编码路径
    static const char* fallback_paths[] = {
        "/var/log/mysql/error.log",
        "/var/log/mariadb/mariadb.log",
        "/var/log/mysqld.log",
        "/var/lib/mysql/error.log",
        "/usr/local/mysql/data/error.log",
        "/opt/mysql/logs/error.log",
        NULL
    };

    for (int i = 0; fallback_paths[i] != NULL; i++) {
        if (access(fallback_paths[i], R_OK) == 0) {
            strncpy(found_path, fallback_paths[i], sizeof(found_path) - 1);
            found_path[sizeof(found_path) - 1] = '\0';
            return found_path;
        }
    }

    return NULL;
}

// 检测运行中的MySQL进程
static const char* detect_mysql_by_process() {
    FILE* fp;
    char line[256];

    // 检查进程列表中的MySQL相关进程
    fp = popen("ps aux | grep -E '(mysqld|mariadbd)' | grep -v grep | head -1", "r");
    if (fp != NULL) {
        if (fgets(line, sizeof(line), fp) != NULL) {
            pclose(fp);
            // 根据进程名判断发行版
            if (strstr(line, "mariadbd") || strstr(line, "mariadb")) {
                return "MariaDB";
            } else if (strstr(line, "percona")) {
                return "Percona Server";
            } else if (strstr(line, "mysqld")) {
                return "MySQL";
            }
        }
        pclose(fp);
    }
    return NULL;
}

// 获取MySQL实际监听的端口
static int get_mysql_ports(int* ports, int max_ports) {
    FILE* fp;
    char line[512];
    int port_count = 0;

    // 搜索所有MySQL相关进程监听的端口
    fp = popen("ss -tlnp | grep -E '(mysqld|mariadbd|percona)' | awk '{print $4}' | cut -d: -f2 | sort -u", "r");
    if (fp != NULL) {
        while (fgets(line, sizeof(line), fp) && port_count < max_ports) {
            int port = atoi(line);
            if (port > 0 && port < 65536) {
                ports[port_count++] = port;
            }
        }
        pclose(fp);
    }

    // 如果没有找到，尝试常见的MySQL端口
    if (port_count == 0) {
        int common_ports[] = {3306, 3307, 3308, 3309, 33060, 33061};
        int common_count = sizeof(common_ports) / sizeof(common_ports[0]);

        for (int i = 0; i < common_count && port_count < max_ports; i++) {
            char cmd[128];
            snprintf(cmd, sizeof(cmd), "ss -tln | grep ':%d ' >/dev/null 2>&1", common_ports[i]);
            if (system(cmd) == 0) {
                ports[port_count++] = common_ports[i];
            }
        }
    }

    return port_count;
}

// 检测MySQL端口监听情况
static const char* detect_mysql_by_port() {
    FILE* fp;
    char line[512];
    char cmd[256];
    int ports[10];
    int port_count;

    // 获取MySQL监听的端口
    port_count = get_mysql_ports(ports, 10);

    for (int i = 0; i < port_count; i++) {
        snprintf(cmd, sizeof(cmd), "ss -tlnp | grep ':%d ' | head -1", ports[i]);
        fp = popen(cmd, "r");
        if (fp != NULL) {
            if (fgets(line, sizeof(line), fp) != NULL) {
                pclose(fp);
                // 从ss输出中提取进程信息
                if (strstr(line, "mariadbd") || strstr(line, "mariadb")) {
                    return "MariaDB";
                } else if (strstr(line, "percona")) {
                    return "Percona Server";
                } else if (strstr(line, "mysqld")) {
                    return "MySQL";
                } else if (strstr(line, "mysql")) {
                    return "MySQL"; // 通用MySQL进程
                }
            } else {
                pclose(fp);
            }
        }
    }
    return NULL;
}

// 从配置文件检测MySQL端口
static int get_mysql_port_from_config() {
    FILE* fp;
    char line[256];
    int port = 0;

    // 常见的MySQL配置文件位置
    const char* config_files[] = {
        "/etc/mysql/my.cnf",
        "/etc/my.cnf",
        "/usr/local/mysql/my.cnf",
        "/var/lib/mysql/my.cnf",
        "/etc/mysql/mysql.conf.d/mysqld.cnf",
        "/etc/mysql/mariadb.conf.d/50-server.cnf",
        NULL
    };

    for (int i = 0; config_files[i] != NULL; i++) {
        fp = fopen(config_files[i], "r");
        if (fp != NULL) {
            while (fgets(line, sizeof(line), fp)) {
                // 查找port配置项
                if (strstr(line, "port") && strstr(line, "=")) {
                    char* eq = strchr(line, '=');
                    if (eq != NULL) {
                        port = atoi(eq + 1);
                        if (port > 0 && port < 65536) {
                            fclose(fp);
                            return port;
                        }
                    }
                }
            }
            fclose(fp);
        }
    }

    return 3306; // 默认端口
}

// 通过版本信息检测MySQL发行版
static const char* detect_mysql_by_version() {
    FILE* fp;
    char line[512];

    // 尝试执行mysql --version命令
    fp = popen("mysql --version 2>/dev/null", "r");
    if (fp != NULL) {
        if (fgets(line, sizeof(line), fp) != NULL) {
            pclose(fp);
            // 转换为小写便于匹配
            for (int i = 0; line[i]; i++) {
                line[i] = tolower(line[i]);
            }

            if (strstr(line, "mariadb")) {
                return "MariaDB";
            } else if (strstr(line, "percona")) {
                return "Percona Server";
            } else if (strstr(line, "mysql")) {
                return "MySQL";
            }
        } else {
            pclose(fp);
        }
    }
    return NULL;
}

// 检测所有可能的systemd服务
static const char* detect_mysql_by_systemd() {
    const char* services[] = {
        // MariaDB服务名
        "mariadb", "mariadb-server", "mariadb.service",
        // MySQL服务名
        "mysql", "mysqld", "mysql-server", "mysql.service", "mysqld.service",
        // Percona服务名
        "percona-server", "percona-mysql", "percona-server-mysql",
        // MySQL Cluster服务名
        "mysql-cluster", "ndb_mgmd", "ndbd",
        // 自定义服务名模式
        "mysql-*", "mariadb-*", "db-*",
        NULL
    };

    char command[256];
    int ret;

    for (int i = 0; services[i] != NULL; i++) {
        // 跳过通配符模式，这些需要特殊处理
        if (strchr(services[i], '*') != NULL) continue;

        snprintf(command, sizeof(command), "systemctl is-active %s >/dev/null 2>&1", services[i]);
        ret = system(command);

        if (WEXITSTATUS(ret) == 0) {
            // 根据服务名判断发行版
            if (strstr(services[i], "mariadb")) {
                return "MariaDB";
            } else if (strstr(services[i], "percona")) {
                return "Percona Server";
            } else if (strstr(services[i], "cluster") || strstr(services[i], "ndb")) {
                return "MySQL Cluster";
            } else {
                return "MySQL";
            }
        }
    }

    return NULL;
}

// 综合检测MySQL发行版类型
static const char* detect_mysql_variant() {
    const char* variant;

    // 方法1: 通过systemd服务检测（最可靠）
    variant = detect_mysql_by_systemd();
    if (variant != NULL) return variant;

    // 方法2: 通过进程检测（适用于非systemd管理）
    variant = detect_mysql_by_process();
    if (variant != NULL) return variant;

    // 方法3: 通过端口监听检测（适用于容器等）
    variant = detect_mysql_by_port();
    if (variant != NULL) return variant;

    // 方法4: 通过版本命令检测（最后手段）
    variant = detect_mysql_by_version();
    if (variant != NULL) return variant;

    return NULL;
}

// 检测systemd journal是否可用
static int is_systemd_journal_available() {
    return detect_mysql_variant() != NULL;
}

// 获取实际运行的MySQL服务名
static char* get_active_mysql_service() {
    static char service_name[64];
    const char* services[] = {
        "mariadb", "mariadb-server", "mysql", "mysqld", "mysql-server",
        "percona-server", "percona-mysql", "mysql-cluster", "ndb_mgmd",
        NULL
    };

    char command[256];
    int ret;

    for (int i = 0; services[i] != NULL; i++) {
        snprintf(command, sizeof(command), "systemctl is-active %s >/dev/null 2>&1", services[i]);
        ret = system(command);

        if (WEXITSTATUS(ret) == 0) {
            strcpy(service_name, services[i]);
            return service_name;
        }
    }

    return NULL;
}

// 检测MySQL日志配置状态
static const char* check_mysql_log_status(const char* variant) {
    // 首先检查是否有可用的日志文件
    char* log_path = detect_mysql_log_path();
    if (log_path != NULL) {
        return "文件日志正常";
    }

    // 如果没有文件日志，再检查systemd journal
    FILE* fp;
    char line[256];
    char command[256];

    if (strcmp(variant, "MariaDB") == 0) {
        snprintf(command, sizeof(command), "journalctl -u mariadb --since '24 hours ago' 2>/dev/null | wc -l");
    } else {
        snprintf(command, sizeof(command), "journalctl -u mysql --since '24 hours ago' 2>/dev/null | wc -l");
    }

    fp = popen(command, "r");
    if (fp != NULL) {
        if (fgets(line, sizeof(line), fp) != NULL) {
            int log_lines = atoi(line);
            pclose(fp);

            if (log_lines > 100) {
                return "journal日志正常";
            } else if (log_lines > 0) {
                return "journal日志较少";
            } else {
                return "journal无日志记录";
            }
        }
        pclose(fp);
    }

    return "未找到日志配置";
}

// 从systemd journal读取MySQL短连接断开信息
static int read_mysql_short_connections_from_journal(struct mysql_connection_stats* mysql_stats, int* mysql_count, const char* variant) {
    FILE* fp = NULL;
    char line[512];
    char ip_address[16];
    char command[512];
    time_t current_time = time(NULL);

    // 获取实际运行的服务名
    char* active_service = get_active_mysql_service();

    if (active_service != NULL) {
        // 使用实际的服务名
        snprintf(command, sizeof(command),
                "journalctl -u %s --since '1 minute ago' 2>/dev/null | grep 'Aborted connection' | grep -o \"host: '[^']*'\" | sort | uniq -c",
                active_service);
    } else {
        // 回退到通用方法：搜索所有可能的服务
        snprintf(command, sizeof(command),
                "journalctl --since '1 minute ago' 2>/dev/null | grep -E '(mysqld|mariadbd|percona)' | grep 'Aborted connection' | grep -o \"host: '[^']*'\" | sort | uniq -c");
    }

    fp = popen(command, "r");
    if (fp == NULL) return 0;

    *mysql_count = 0;
    while (fgets(line, sizeof(line), fp) && *mysql_count < 50) {
        int count;
        char host_part[64];

        // 解析格式: "   1234 host: '*************'"
        if (sscanf(line, "%d host: '%15[^']'", &count, ip_address) == 2) {
            strcpy(mysql_stats[*mysql_count].ip_address, ip_address);
            mysql_stats[*mysql_count].connection_count = count;
            mysql_stats[*mysql_count].last_seen = current_time;
            (*mysql_count)++;
        }
    }

    pclose(fp);
    return *mysql_count > 0 ? 1 : 0;
}

// 从文件读取MySQL短连接断开信息
static int read_mysql_short_connections_from_file(const char* log_path, struct mysql_connection_stats* mysql_stats, int* mysql_count) {
    FILE* fp;
    char line[512];
    char ip_address[16];
    time_t current_time = time(NULL);
    time_t fifteen_seconds_ago = current_time - 15;  // 使用15秒窗口

    *mysql_count = 0;

    fp = fopen(log_path, "r");
    if (fp == NULL) {
        return 0;
    }

    // 从文件末尾开始读取，只处理最近1分钟的日志
    fseek(fp, 0, SEEK_END);
    long file_size = ftell(fp);

    // 如果文件太大，只读取最后64KB
    long start_pos = file_size > 65536 ? file_size - 65536 : 0;
    fseek(fp, start_pos, SEEK_SET);

    while (fgets(line, sizeof(line), fp) && *mysql_count < 50) {
        // 查找"Aborted connection"记录（短连接断开）
        if (strstr(line, "Aborted connection") && strstr(line, "host:")) {
            // 解析时间戳 (格式: 2025-07-18 20:22:06 或 2025-07-18 20:22:06 495990)
            struct tm tm_log = {0};
            int parsed = sscanf(line, "%d-%d-%d %d:%d:%d",
                               &tm_log.tm_year, &tm_log.tm_mon, &tm_log.tm_mday,
                               &tm_log.tm_hour, &tm_log.tm_min, &tm_log.tm_sec);

            if (parsed == 6) {
                tm_log.tm_year -= 1900;  // 年份从1900开始
                tm_log.tm_mon -= 1;      // 月份从0开始
                time_t log_time = mktime(&tm_log);

                // 只处理最近15秒的日志
                if (log_time < fifteen_seconds_ago) {
                    continue;
                }
            } else {
                // 时间解析失败，跳过这条记录
                continue;
            }

            // 提取IP地址 (格式: host: '*************')
            char* host_start = strstr(line, "host: '");
            if (host_start != NULL) {
                host_start += 7; // 跳过"host: '"
                char* host_end = strchr(host_start, '\'');
                if (host_end != NULL) {
                    int ip_len = host_end - host_start;
                    if (ip_len > 0 && ip_len < 16) {
                        strncpy(ip_address, host_start, ip_len);
                        ip_address[ip_len] = '\0';

                        // 查找是否已存在该IP
                        int found = 0;
                        for (int i = 0; i < *mysql_count; i++) {
                            if (strcmp(mysql_stats[i].ip_address, ip_address) == 0) {
                                mysql_stats[i].connection_count++;
                                mysql_stats[i].last_seen = current_time;
                                found = 1;
                                break;
                            }
                        }

                        // 如果是新IP，添加到列表
                        if (!found && *mysql_count < 50) {
                            strcpy(mysql_stats[*mysql_count].ip_address, ip_address);
                            mysql_stats[*mysql_count].connection_count = 1;
                            mysql_stats[*mysql_count].last_seen = current_time;
                            (*mysql_count)++;
                        }
                    }
                }
            }
        }
    }

    fclose(fp);
    return *mysql_count > 0 ? 1 : 0;
}

// 预定义服务端口结构
struct known_service {
    unsigned int ports[5];  // 支持多个端口
    char service_name[16];
};

void read_net_security(struct stats_net_security *st_net_security, int deep_monitoring_mode)
{
    FILE *fp;
    char line[256];
    unsigned int state;
    char local_addr[16], remote_addr[16];
    unsigned long inode;

    // 优化：只在深度监控模式下构建进程映射表
    static time_t last_full_scan = 0;
    static int quick_scan_counter = 0;
    time_t now = time(NULL);
    quick_scan_counter++;

    // 只在深度监控模式下构建socket-进程映射表
    if (deep_monitoring_mode) {
        // 使用缓存机制减少进程扫描频率（深度模式下每5秒更新一次）
        if (now - last_full_scan > 5) {
            build_socket_process_map();
            last_full_scan = now;
        }
    } else {
        // 普通模式下清空映射表，节省内存和CPU
        socket_map_count = 0;
    }

    unsigned int local_port, remote_port;

    // 预定义服务端口（包括常见的非默认端口）
    struct known_service known_services[] = {
        {{3306, 3307, 3308, 13306, 0}, "MySQL"},      // MySQL常用端口
        {{5432, 5433, 5434, 15432, 0}, "PostgreSQL"}, // PostgreSQL常用端口
        {{6379, 6380, 6381, 16379, 0}, "Redis"},      // Redis常用端口
        {{27017, 27018, 27019, 0, 0}, "MongoDB"},     // MongoDB常用端口
        {{1521, 1522, 1523, 0, 0}, "Oracle"},         // Oracle常用端口
        {{1433, 1434, 0, 0, 0}, "SQLServer"},         // SQL Server
        {{80, 8080, 8081, 8000, 0}, "HTTP"},          // HTTP服务
        {{443, 8443, 0, 0, 0}, "HTTPS"},              // HTTPS服务
        {{22, 2222, 0, 0, 0}, "SSH"},                 // SSH服务
        {{0, 0, 0, 0, 0}, ""}                         // 结束标记
    };

    // 动态端口统计数组
    struct dynamic_port_stats dynamic_ports[50];
    int dynamic_count = 0;

    // 端口连接计数器（用于发现高连接数端口）
    unsigned int port_connections[65536] = {0};  // 支持所有端口
    unsigned int port_established[65536] = {0};
    unsigned int port_time_wait[65536] = {0};
    unsigned int port_syn_recv[65536] = {0};
    unsigned int port_listen[65536] = {0};

    // IP连接统计
    struct ip_connection_stats ip_stats[100];
    int ip_count = 0;

    // MySQL数据库连接监控统计
    struct mysql_connection_stats mysql_stats[50];
    int mysql_count = 0;

    // 初始化结构
    memset(st_net_security, 0, STATS_NET_SECURITY_SIZE);
    memset(dynamic_ports, 0, sizeof(dynamic_ports));
    memset(ip_stats, 0, sizeof(ip_stats));
    memset(mysql_stats, 0, sizeof(mysql_stats));

    // 读取TCP连接统计 (IPv4)
    if ((fp = fopen("/proc/net/tcp", "r")) != NULL) {
        // 跳过标题行
        if (fgets(line, sizeof(line), fp) != NULL) {
            // 统计各种连接状态
            while (fgets(line, sizeof(line), fp) != NULL) {
                if (sscanf(line, "%*d: %8s:%X %8s:%X %X %*X:%*X %*X:%*X %*X %*d %*d %lu",
                          local_addr, &local_port, remote_addr, &remote_port, &state, &inode) == 6) {

                    st_net_security->total_connections++;

                    // 统计每个端口的连接数
                    if (local_port < 65536) {
                        port_connections[local_port]++;
                        if (state == 0x01) port_established[local_port]++;
                        else if (state == 0x06) port_time_wait[local_port]++;
                        else if (state == 0x03) port_syn_recv[local_port]++;
                        else if (state == 0x0A) port_listen[local_port]++;
                    }
                    if (remote_port < 65536 && remote_port != 0 && state != 0x0A) {
                        port_connections[remote_port]++;
                        if (state == 0x01) port_established[remote_port]++;
                        else if (state == 0x06) port_time_wait[remote_port]++;
                        else if (state == 0x03) port_syn_recv[remote_port]++;
                    }

                    switch (state) {
                        case 0x01: // ESTABLISHED
                            st_net_security->established_connections++;
                            break;
                        case 0x03: // SYN_RECV
                            st_net_security->syn_recv_connections++;
                            break;
                        case 0x06: // TIME_WAIT
                            st_net_security->time_wait_connections++;
                            break;
                        case 0x0A: // LISTEN
                            st_net_security->listen_connections++;
                            break;
                    }

                    // 统计IP连接数（只统计ESTABLISHED连接）
                    if (state == 0x01) {
                        char remote_ip[16];
                        char local_ip[16];
                        unsigned int service_port;

                        // 将十六进制地址转换为IP字符串
                        unsigned int remote_addr_int = strtoul(remote_addr, NULL, 16);
                        unsigned int local_addr_int = strtoul(local_addr, NULL, 16);

                        snprintf(remote_ip, sizeof(remote_ip), "%d.%d.%d.%d",
                                remote_addr_int & 0xFF, (remote_addr_int >> 8) & 0xFF,
                                (remote_addr_int >> 16) & 0xFF, (remote_addr_int >> 24) & 0xFF);

                        snprintf(local_ip, sizeof(local_ip), "%d.%d.%d.%d",
                                local_addr_int & 0xFF, (local_addr_int >> 8) & 0xFF,
                                (local_addr_int >> 16) & 0xFF, (local_addr_int >> 24) & 0xFF);

                        // 确定服务端口（通常是本地端口，但也可能是远程端口）
                        // 常见服务端口
                        int common_ports[] = {22, 80, 443, 3306, 5432, 6379, 8080, 8443, 27017, 0};

                        // 默认使用本地端口作为服务端口
                        service_port = local_port;

                        // 检查是否是常见服务端口
                        int is_common_local = 0;
                        int is_common_remote = 0;

                        for (int i = 0; common_ports[i] != 0; i++) {
                            if (local_port == common_ports[i]) {
                                is_common_local = 1;
                                break;
                            }
                        }

                        for (int i = 0; common_ports[i] != 0; i++) {
                            if (remote_port == common_ports[i]) {
                                is_common_remote = 1;
                                break;
                            }
                        }

                        // 如果远程端口是常见服务端口但本地端口不是，使用远程端口
                        if (is_common_remote && !is_common_local) {
                            service_port = remote_port;
                        }

                        // 查找或添加IP统计
                        int found = 0;
                        for (int i = 0; i < ip_count; i++) {
                            if (strcmp(ip_stats[i].ip_address, remote_ip) == 0 &&
                                ip_stats[i].port == service_port) {
                                ip_stats[i].connection_count++;
                                found = 1;
                                break;
                            }
                        }
                        if (!found && ip_count < 100) {
                            strcpy(ip_stats[ip_count].ip_address, remote_ip);
                            ip_stats[ip_count].port = service_port;
                            ip_stats[ip_count].connection_count = 1;
                            // 添加进程信息（根据深度监控模式决定显示格式）
                            if (deep_monitoring_mode) {
                                const char* process_info = get_process_by_inode(inode);
                                strncpy(ip_stats[ip_count].process_info, process_info, 31);
                            } else {
                                snprintf(ip_stats[ip_count].process_info, 32, "inode:%lu", inode);
                            }
                            ip_stats[ip_count].process_info[31] = '\0';

                            ip_count++;
                        }


                    }
                }
            }
        }
        fclose(fp);
    }

    // 读取TCP连接统计 (IPv6)
    if ((fp = fopen("/proc/net/tcp6", "r")) != NULL) {
        // 跳过标题行
        if (fgets(line, sizeof(line), fp) != NULL) {
            // 统计各种连接状态
            while (fgets(line, sizeof(line), fp) != NULL) {
                // IPv6地址格式：32个十六进制字符
                char local_addr6[33], remote_addr6[33];
                if (sscanf(line, "%*d: %32s:%X %32s:%X %X %*X:%*X %*X:%*X %*X %*d %*d %lu",
                          local_addr6, &local_port, remote_addr6, &remote_port, &state, &inode) == 6) {

                    st_net_security->total_connections++;

                    // 统计每个端口的连接数
                    if (local_port < 65536) {
                        port_connections[local_port]++;
                        if (state == 0x01) port_established[local_port]++;
                        else if (state == 0x06) port_time_wait[local_port]++;
                        else if (state == 0x03) port_syn_recv[local_port]++;
                        else if (state == 0x0A) port_listen[local_port]++;
                    }
                    if (remote_port < 65536 && remote_port != 0 && state != 0x0A) {
                        port_connections[remote_port]++;
                        if (state == 0x01) port_established[remote_port]++;
                        else if (state == 0x06) port_time_wait[remote_port]++;
                        else if (state == 0x03) port_syn_recv[remote_port]++;
                    }

                    // 统计连接状态
                    if (state == 0x01) st_net_security->established_connections++;
                    else if (state == 0x06) st_net_security->time_wait_connections++;
                    else if (state == 0x03) st_net_security->syn_recv_connections++;
                    else if (state == 0x0A) st_net_security->listen_connections++;

                    // 处理IPv6连接的IP统计（只处理ESTABLISHED连接）
                    if (state == 0x01 && strcmp(remote_addr6, "00000000000000000000000000000000") != 0) {
                        char remote_ip[64];
                        int service_port;

                        // 简化IPv6地址显示（显示前8个字符）
                        snprintf(remote_ip, sizeof(remote_ip), "%.8s...IPv6", remote_addr6);

                        // 确定服务端口
                        int common_ports[] = {22, 80, 443, 3306, 5432, 6379, 8080, 8443, 27017, 0};
                        service_port = local_port;

                        // 检查是否是常见服务端口
                        int is_common_local = 0;
                        int is_common_remote = 0;

                        for (int i = 0; common_ports[i] != 0; i++) {
                            if (local_port == common_ports[i]) {
                                is_common_local = 1;
                                break;
                            }
                        }

                        for (int i = 0; common_ports[i] != 0; i++) {
                            if (remote_port == common_ports[i]) {
                                is_common_remote = 1;
                                break;
                            }
                        }

                        // 如果远程端口是常见服务端口但本地端口不是，使用远程端口
                        if (is_common_remote && !is_common_local) {
                            service_port = remote_port;
                        }

                        // 查找或添加IP统计
                        int found = 0;
                        for (int i = 0; i < ip_count; i++) {
                            if (strcmp(ip_stats[i].ip_address, remote_ip) == 0 &&
                                ip_stats[i].port == service_port) {
                                ip_stats[i].connection_count++;
                                found = 1;
                                break;
                            }
                        }
                        if (!found && ip_count < 100) {
                            strcpy(ip_stats[ip_count].ip_address, remote_ip);
                            ip_stats[ip_count].port = service_port;
                            ip_stats[ip_count].connection_count = 1;
                            // 添加进程信息（根据深度监控模式决定显示格式）
                            if (deep_monitoring_mode) {
                                const char* process_info = get_process_by_inode(inode);
                                strncpy(ip_stats[ip_count].process_info, process_info, 31);
                            } else {
                                snprintf(ip_stats[ip_count].process_info, 32, "inode:%lu", inode);
                            }
                            ip_stats[ip_count].process_info[31] = '\0';

                            ip_count++;
                        }
                    }
                }
            }
        }
        fclose(fp);
    }

    // 处理动态端口发现
    for (int port = 1; port < 65536; port++) {
        if (port_connections[port] > 5 && dynamic_count < 50) {  // 连接数>5的端口
            dynamic_ports[dynamic_count].port = port;
            dynamic_ports[dynamic_count].total_connections = port_connections[port];
            dynamic_ports[dynamic_count].established = port_established[port];
            dynamic_ports[dynamic_count].time_wait = port_time_wait[port];
            dynamic_ports[dynamic_count].syn_recv = port_syn_recv[port];

            // 智能服务识别
            if (port >= 3306 && port <= 3310) strcpy(dynamic_ports[dynamic_count].detected_service, "MySQL?");
            else if (port >= 5432 && port <= 5440) strcpy(dynamic_ports[dynamic_count].detected_service, "PostgreSQL?");
            else if (port >= 6379 && port <= 6390) strcpy(dynamic_ports[dynamic_count].detected_service, "Redis?");
            else if (port >= 27017 && port <= 27030) strcpy(dynamic_ports[dynamic_count].detected_service, "MongoDB?");
            else if (port_connections[port] > 50) strcpy(dynamic_ports[dynamic_count].detected_service, "Database?");
            else if (port_connections[port] > 20) strcpy(dynamic_ports[dynamic_count].detected_service, "Service?");
            else strcpy(dynamic_ports[dynamic_count].detected_service, "Unknown");

            dynamic_count++;
        }
    }

    // 找出单IP最大连接数
    st_net_security->max_connections_per_ip = 0;
    for (int i = 0; i < ip_count; i++) {
        if (ip_stats[i].connection_count > st_net_security->max_connections_per_ip) {
            st_net_security->max_connections_per_ip = ip_stats[i].connection_count;
        }
    }

    /*
    // MySQL数据库连接监控分析 - 临时注释掉
    st_net_security->mysql_monitoring_enabled = 0;
    st_net_security->mysql_connections_per_second = 0;
    st_net_security->mysql_aborted_connections = 0;
    st_net_security->max_mysql_conn_per_ip = 0;
    strcpy(st_net_security->high_freq_mysql_client_ip, "");
    strcpy(st_net_security->mysql_variant, "");
    st_net_security->mysql_port_count = 0;
    memset(st_net_security->mysql_ports, 0, sizeof(st_net_security->mysql_ports));
    strcpy(st_net_security->mysql_log_status, "");
    strcpy(st_net_security->mysql_log_path, "");

    // 检测MySQL发行版
    const char* variant = detect_mysql_variant();
    if (variant != NULL) {
        strcpy(st_net_security->mysql_variant, variant);
        st_net_security->mysql_monitoring_enabled = 1;

        // 获取MySQL监听的端口
        st_net_security->mysql_port_count = get_mysql_ports(st_net_security->mysql_ports, 8);

        // 检测MySQL日志状态
        const char* log_status = check_mysql_log_status(variant);
        strcpy(st_net_security->mysql_log_status, log_status);

        // 尝试检测MySQL日志文件
        char* log_path = detect_mysql_log_path();
        if (log_path != NULL) {
            // 使用文件日志
            strcpy(st_net_security->mysql_log_path, log_path);
            read_mysql_short_connections_from_file(log_path, mysql_stats, &mysql_count);
        } else {
            // 使用systemd journal
            strcpy(st_net_security->mysql_log_path, "systemd journal");
            read_mysql_short_connections_from_journal(mysql_stats, &mysql_count, variant);
        }

        // 分析MySQL短连接断开统计
        unsigned int total_mysql_short_connections = 0;
        for (int i = 0; i < mysql_count; i++) {
            total_mysql_short_connections += mysql_stats[i].connection_count;

            // 找出短连接断开数最多的客户端IP
            if (mysql_stats[i].connection_count > st_net_security->max_mysql_conn_per_ip) {
                st_net_security->max_mysql_conn_per_ip = mysql_stats[i].connection_count;
                strcpy(st_net_security->high_freq_mysql_client_ip, mysql_stats[i].ip_address);
            }
        }

        // 将15秒内的短连接断开数换算成每秒：除以15
        st_net_security->mysql_connections_per_second = total_mysql_short_connections / 15;
        if (st_net_security->mysql_connections_per_second == 0 && total_mysql_short_connections > 0) {
            st_net_security->mysql_connections_per_second = 1; // 至少显示1
        }
        st_net_security->mysql_aborted_connections = total_mysql_short_connections; // 这里都是短连接断开
    }
    */

    // 临时禁用MySQL监控，设置默认值
    st_net_security->mysql_monitoring_enabled = 0;
    strcpy(st_net_security->mysql_variant, "");
    st_net_security->mysql_port_count = 0;

    // 收集端口统计数据
    st_net_security->port_stats_count = 0;

    // 收集详细IP+端口统计数据
    st_net_security->ip_port_stats_count = 0;

    // 将IP统计数据复制到结构中，并添加服务名称
    for (int i = 0; i < ip_count && st_net_security->ip_port_stats_count < 20; i++) {
        int idx = st_net_security->ip_port_stats_count;
        strcpy(st_net_security->ip_port_stats[idx].ip_address, ip_stats[i].ip_address);
        st_net_security->ip_port_stats[idx].port = ip_stats[i].port;
        st_net_security->ip_port_stats[idx].connection_count = ip_stats[i].connection_count;

        // 根据端口号确定服务名称
        switch (ip_stats[i].port) {
            case 22: strcpy(st_net_security->ip_port_stats[idx].service_name, "SSH"); break;
            case 80: strcpy(st_net_security->ip_port_stats[idx].service_name, "HTTP"); break;
            case 443: strcpy(st_net_security->ip_port_stats[idx].service_name, "HTTPS"); break;
            case 3306: strcpy(st_net_security->ip_port_stats[idx].service_name, "MySQL"); break;
            case 5432: strcpy(st_net_security->ip_port_stats[idx].service_name, "PostgreSQL"); break;
            case 6379: strcpy(st_net_security->ip_port_stats[idx].service_name, "Redis"); break;
            case 8080: strcpy(st_net_security->ip_port_stats[idx].service_name, "HTTP-Alt"); break;
            case 8443: strcpy(st_net_security->ip_port_stats[idx].service_name, "HTTPS-Alt"); break;
            case 27017: strcpy(st_net_security->ip_port_stats[idx].service_name, "MongoDB"); break;
            default:
                if (ip_stats[i].port >= 3306 && ip_stats[i].port <= 3310)
                    strcpy(st_net_security->ip_port_stats[idx].service_name, "MySQL?");
                else if (ip_stats[i].port >= 5432 && ip_stats[i].port <= 5440)
                    strcpy(st_net_security->ip_port_stats[idx].service_name, "PostgreSQL?");
                else if (ip_stats[i].port >= 6379 && ip_stats[i].port <= 6390)
                    strcpy(st_net_security->ip_port_stats[idx].service_name, "Redis?");
                else
                    strcpy(st_net_security->ip_port_stats[idx].service_name, "Unknown");
                break;
        }

        // 复制进程信息
        strncpy(st_net_security->ip_port_stats[idx].process_info, ip_stats[i].process_info, 31);
        st_net_security->ip_port_stats[idx].process_info[31] = '\0';



        st_net_security->ip_port_stats_count++;
    }



    // 统计已知服务端口
    for (int i = 0; known_services[i].ports[0] != 0 && st_net_security->port_stats_count < 10; i++) {
        int total_count = 0;
        int established = 0;
        int time_wait = 0;
        int syn_recv = 0;
        int listen = 0;
        int port = 0;

        // 检查该服务的所有端口
        for (int j = 0; j < 5 && known_services[i].ports[j] != 0; j++) {
            unsigned int p = known_services[i].ports[j];
            if (port_connections[p] > 0) {
                total_count += port_connections[p];
                established += port_established[p];
                time_wait += port_time_wait[p];
                syn_recv += port_syn_recv[p];
                listen += port_listen[p];
                if (port == 0) port = p;  // 记录第一个有连接的端口
            }
        }

        // 如果该服务有连接，添加到统计中
        if (total_count > 0) {
            int idx = st_net_security->port_stats_count;
            st_net_security->port_stats[idx].port = port;
            st_net_security->port_stats[idx].total_connections = total_count;
            st_net_security->port_stats[idx].established = established;
            st_net_security->port_stats[idx].time_wait = time_wait;
            st_net_security->port_stats[idx].syn_recv = syn_recv;
            st_net_security->port_stats[idx].listen = listen;
            strncpy(st_net_security->port_stats[idx].service_name, known_services[i].service_name, 15);
            st_net_security->port_stats[idx].service_name[15] = '\0';  // 确保字符串结束
            st_net_security->port_stats_count++;
        }
    }

    // 添加动态发现的高连接数端口
    for (int port = 1; port < 65536 && st_net_security->port_stats_count < 10; port++) {
        if (port_connections[port] > 5) {
            // 检查是否已经在已知服务中
            int is_known = 0;
            for (int i = 0; known_services[i].ports[0] != 0; i++) {
                for (int j = 0; j < 5 && known_services[i].ports[j] != 0; j++) {
                    if (known_services[i].ports[j] == port) {
                        is_known = 1;
                        break;
                    }
                }
                if (is_known) break;
            }

            // 如果不是已知服务，添加到统计中
            if (!is_known) {
                int idx = st_net_security->port_stats_count;
                st_net_security->port_stats[idx].port = port;
                st_net_security->port_stats[idx].total_connections = port_connections[port];
                st_net_security->port_stats[idx].established = port_established[port];
                st_net_security->port_stats[idx].time_wait = port_time_wait[port];
                st_net_security->port_stats[idx].syn_recv = port_syn_recv[port];
                st_net_security->port_stats[idx].listen = port_listen[port];

                // 智能服务识别
                if (port >= 3306 && port <= 3310)
                    strcpy(st_net_security->port_stats[idx].service_name, "MySQL?");
                else if (port >= 5432 && port <= 5440)
                    strcpy(st_net_security->port_stats[idx].service_name, "PostgreSQL?");
                else if (port >= 6379 && port <= 6390)
                    strcpy(st_net_security->port_stats[idx].service_name, "Redis?");
                else if (port >= 27017 && port <= 27030)
                    strcpy(st_net_security->port_stats[idx].service_name, "MongoDB?");
                else if (port_connections[port] > 50)
                    strcpy(st_net_security->port_stats[idx].service_name, "Database?");
                else if (port_connections[port] > 20)
                    strcpy(st_net_security->port_stats[idx].service_name, "Service?");
                else
                    strcpy(st_net_security->port_stats[idx].service_name, "Unknown");

                st_net_security->port_stats_count++;
            }
        }
    }

    // 读取网络接口统计
    if ((fp = fopen("/sys/class/net/eth0/statistics/rx_bytes", "r")) != NULL) {
        if (fscanf(fp, "%llu", &st_net_security->rx_bytes) != 1) {
            st_net_security->rx_bytes = 0;
        }
        fclose(fp);
    }

    if ((fp = fopen("/sys/class/net/eth0/statistics/tx_bytes", "r")) != NULL) {
        if (fscanf(fp, "%llu", &st_net_security->tx_bytes) != 1) {
            st_net_security->tx_bytes = 0;
        }
        fclose(fp);
    }

    if ((fp = fopen("/sys/class/net/eth0/statistics/rx_packets", "r")) != NULL) {
        if (fscanf(fp, "%llu", &st_net_security->rx_packets) != 1) {
            st_net_security->rx_packets = 0;
        }
        fclose(fp);
    }

    if ((fp = fopen("/sys/class/net/eth0/statistics/tx_packets", "r")) != NULL) {
        if (fscanf(fp, "%llu", &st_net_security->tx_packets) != 1) {
            st_net_security->tx_packets = 0;
        }
        fclose(fp);
    }

    // 简单的异常检测
    st_net_security->anomaly_score = 0;
    strcpy(st_net_security->anomaly_reason, "正常");

    if (st_net_security->syn_recv_connections > 1000) {
        st_net_security->anomaly_score += 50;
        strcpy(st_net_security->anomaly_reason, "SYN_RECV过多");
    }

    if (st_net_security->anomaly_score > 70) {
        st_net_security->trigger_packet_analysis = 1;
    }
}

/*
 ***************************************************************************
 * Print detailed port connection statistics.
 *
 * IN:
 * @st_net_security	Structure with network security statistics.
 ***************************************************************************
 */
void print_port_connection_stats(struct stats_net_security *st_net_security)
{
    FILE *fp;
    char line[256];
    unsigned int state;
    char local_addr[16], remote_addr[16];
    unsigned int local_port, remote_port;

    // 重新读取数据进行详细分析
    struct {
        unsigned int ports[5];
        char service_name[16];
        unsigned int total_count;
        unsigned int established;
        unsigned int time_wait;
        unsigned int syn_recv;
        unsigned int listen;
    } known_services[] = {
        {{3306, 3307, 3308, 13306, 0}, "MySQL", 0, 0, 0, 0, 0},
        {{5432, 5433, 5434, 15432, 0}, "PostgreSQL", 0, 0, 0, 0, 0},
        {{6379, 6380, 6381, 16379, 0}, "Redis", 0, 0, 0, 0, 0},
        {{27017, 27018, 27019, 0, 0}, "MongoDB", 0, 0, 0, 0, 0},
        {{1521, 1522, 1523, 0, 0}, "Oracle", 0, 0, 0, 0, 0},
        {{1433, 1434, 0, 0, 0}, "SQLServer", 0, 0, 0, 0, 0},
        {{80, 8080, 8081, 8000, 0}, "HTTP", 0, 0, 0, 0, 0},
        {{443, 8443, 0, 0, 0}, "HTTPS", 0, 0, 0, 0, 0},
        {{22, 2222, 0, 0, 0}, "SSH", 0, 0, 0, 0, 0},
        {{0, 0, 0, 0, 0}, "", 0, 0, 0, 0, 0}
    };

    struct {
        unsigned int port;
        unsigned int total_count;
        unsigned int established;
        unsigned int time_wait;
        unsigned int syn_recv;
        char service_type[16];
    } dynamic_ports[20];
    int dynamic_count = 0;

    struct {
        char ip_address[16];
        unsigned int port;
        unsigned int connection_count;
    } top_ips[10];
    int ip_count = 0;

    // 端口连接计数器
    unsigned int port_connections[65536] = {0};
    unsigned int port_established[65536] = {0};
    unsigned int port_time_wait[65536] = {0};
    unsigned int port_syn_recv[65536] = {0};
    unsigned int port_listen[65536] = {0};
    unsigned long inode;

    // 重新读取TCP连接进行详细统计
    if ((fp = fopen("/proc/net/tcp", "r")) != NULL) {
        if (fgets(line, sizeof(line), fp) != NULL) {
            while (fgets(line, sizeof(line), fp) != NULL) {
                if (sscanf(line, "%*d: %8s:%X %8s:%X %X %*X:%*X %*X:%*X %*X %*d %*d %lu",
                          local_addr, &local_port, remote_addr, &remote_port, &state, &inode) == 6) {

                    // 统计本地端口
                    if (local_port < 65536) {
                        port_connections[local_port]++;
                        if (state == 0x01) port_established[local_port]++;
                        else if (state == 0x06) port_time_wait[local_port]++;
                        else if (state == 0x03) port_syn_recv[local_port]++;
                        else if (state == 0x0A) {
                            port_listen[local_port]++;
                            // 调试LISTEN状态
                            if (local_port == 3306) {
                                printf("DEBUG: 发现MySQL LISTEN socket，端口:%d\n", local_port);
                            }
                        }
                    }

                    // 统计远程端口（不包括LISTEN状态，因为LISTEN是本地状态）
                    if (remote_port < 65536 && remote_port != 0 && state != 0x0A) {
                        port_connections[remote_port]++;
                        if (state == 0x01) port_established[remote_port]++;
                        else if (state == 0x06) port_time_wait[remote_port]++;
                        else if (state == 0x03) port_syn_recv[remote_port]++;
                    }
                }
            }
        }
        fclose(fp);
    }

    // 统计已知服务端口
    for (int i = 0; known_services[i].ports[0] != 0; i++) {
        for (int j = 0; j < 5 && known_services[i].ports[j] != 0; j++) {
            unsigned int port = known_services[i].ports[j];
            if (port_connections[port] > 0) {
                known_services[i].total_count += port_connections[port];
                known_services[i].established += port_established[port];
                known_services[i].time_wait += port_time_wait[port];
                known_services[i].syn_recv += port_syn_recv[port];
                known_services[i].listen += port_listen[port];
            }
        }
    }

    printf("\n=== 详细端口连接统计 ===\n");
    printf("已知服务端口:\n");
    for (int i = 0; known_services[i].ports[0] != 0; i++) {
        if (known_services[i].total_count > 0) {
            printf("  %s: %d个连接 (%d个ESTABLISHED, %d个TIME_WAIT",
                   known_services[i].service_name,
                   known_services[i].total_count,
                   known_services[i].established,
                   known_services[i].time_wait);
            if (known_services[i].listen > 0) {
                printf(", %d个LISTEN", known_services[i].listen);
            }
            if (known_services[i].syn_recv > 0) {
                printf(", %d个SYN_RECV", known_services[i].syn_recv);
            }
            printf(")\n");

            // 显示具体端口
            printf("    端口: ");
            for (int j = 0; j < 5 && known_services[i].ports[j] != 0; j++) {
                if (port_connections[known_services[i].ports[j]] > 0) {
                    printf("%d(%d) ", known_services[i].ports[j],
                           port_connections[known_services[i].ports[j]]);
                }
            }
            printf("\n");
        }
    }

    // 发现动态端口
    for (int port = 1; port < 65536; port++) {
        if (port_connections[port] > 5 && dynamic_count < 20) {
            // 检查是否已经在已知服务中
            int is_known = 0;
            for (int i = 0; known_services[i].ports[0] != 0; i++) {
                for (int j = 0; j < 5 && known_services[i].ports[j] != 0; j++) {
                    if (known_services[i].ports[j] == port) {
                        is_known = 1;
                        break;
                    }
                }
                if (is_known) break;
            }

            if (!is_known) {
                dynamic_ports[dynamic_count].port = port;
                dynamic_ports[dynamic_count].total_count = port_connections[port];
                dynamic_ports[dynamic_count].established = port_established[port];
                dynamic_ports[dynamic_count].time_wait = port_time_wait[port];
                dynamic_ports[dynamic_count].syn_recv = port_syn_recv[port];

                // 智能服务识别
                if (port >= 3306 && port <= 3310) strcpy(dynamic_ports[dynamic_count].service_type, "MySQL?");
                else if (port >= 5432 && port <= 5440) strcpy(dynamic_ports[dynamic_count].service_type, "PostgreSQL?");
                else if (port >= 6379 && port <= 6390) strcpy(dynamic_ports[dynamic_count].service_type, "Redis?");
                else if (port >= 27017 && port <= 27030) strcpy(dynamic_ports[dynamic_count].service_type, "MongoDB?");
                else if (port_connections[port] > 50) strcpy(dynamic_ports[dynamic_count].service_type, "Database?");
                else if (port_connections[port] > 20) strcpy(dynamic_ports[dynamic_count].service_type, "Service?");
                else strcpy(dynamic_ports[dynamic_count].service_type, "Unknown");

                dynamic_count++;
            }
        }
    }

    if (dynamic_count > 0) {
        printf("\n动态发现的活跃端口:\n");
        for (int i = 0; i < dynamic_count; i++) {
            printf("  端口%d: %d个连接 (%s) - %d个ESTABLISHED, %d个TIME_WAIT",
                   dynamic_ports[i].port,
                   dynamic_ports[i].total_count,
                   dynamic_ports[i].service_type,
                   dynamic_ports[i].established,
                   dynamic_ports[i].time_wait);
            if (dynamic_ports[i].syn_recv > 0) {
                printf(", %d个SYN_RECV", dynamic_ports[i].syn_recv);
            }
            printf("\n");
        }
    }

    // 高连接数端口警告
    printf("\n=== 高连接数端口警告 ===\n");
    int has_warning = 0;
    for (int port = 1; port < 65536; port++) {
        if (port_connections[port] > 30) {
            printf("⚠️  端口%d连接数较高(%d个) - 建议关注\n", port, port_connections[port]);
            has_warning = 1;
        }
    }
    if (!has_warning) {
        printf("✓ 所有端口连接数正常\n");
    }
}

// 文件结束
