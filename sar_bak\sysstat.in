#!/bin/sh
#
# chkconfig: 12345 01 99
# description: Reset the system activity logs
#
# @INIT_DIR@/sysstat
# (C) 2000-2017 <PERSON><PERSON><PERSON> (sysstat <at> orange.fr)
#
### BEGIN INIT INFO
# Provides:		sysstat
# Required-Start:
# Required-Stop:
# Default-Start: 1 2 3 4 5
# Default-Stop: 0 6
# Description: Reset the system activity logs
# Short-Description: Reset the system activity logs
### END INIT INFO
#@(#) @PACKAGE_NAME@-@PACKAGE_VERSION@ startup script:
#@(#)	 Insert a dummy record in current daily data file.
#@(#)	 This indicates that the counters have restarted from 0.

# Source functions library
[ -r @INIT_DIR@/functions ] && . @INIT_DIR@/functions

RETVAL=0
PIDFILE=/var/run/sysstat.pid
[ -z "$UID" ] && UID=`id -u`

# See how we were called.
case "$1" in
  start)
	[ $UID -eq 0 ] || exit 4
	echo $$ > $PIDFILE || exit 1
	echo -n "Calling the system activity data collector (sadc)... "
	@SU_C_OWNER@ @QUOTE@ @SA_LIB_DIR@/sa1 --boot @QUOTE@
	[ $? -eq 0 ] || RETVAL=1
	rm -f $PIDFILE
	echo
	;;

  status)
	[ -f $PIDFILE ] || RETVAL=3
	;;

  stop)
	[ $UID -eq 0 ] || exit 4
	;;

  restart|reload|force-reload|condrestart|try-restart)
	;;

  *)
	echo "Usage: sysstat {start|stop|status|restart|reload|force-reload|condrestart|try-restart}"
	RETVAL=2
esac

exit ${RETVAL}

