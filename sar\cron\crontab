# Crontab sample for root or adm
# Please update this crontab with the proper location
# for sa1 and sa2 shell scripts (replace /usr/local/lib64/sa with
# /usr/lib/sa for example).
#
# 8am-7pm activity reports every 20 minutes during weekdays.
# 0 8-18 * * 1-5 /usr/local/lib64/sa/sa1 1200 3 &
# activity reports every 10 minutes everyday.
0 * * * * /usr/local/lib64/sa/sa1 600 6 &
#
# Activity reports every an hour on Saturday and Sunday.
# 0 * * * 0,6 /usr/local/lib64/sa/sa1 &
#
# 7pm-8am activity reports every an hour during weekdays.
# 0 19-7 * * 1-5 /usr/local/lib64/sa/sa1 &
#
# Daily summary prepared at 19:05.
# 5 19 * * 1-5 /usr/local/lib64/sa/sa2 -A &
5 19 * * * /usr/local/lib64/sa/sa2 -A &
