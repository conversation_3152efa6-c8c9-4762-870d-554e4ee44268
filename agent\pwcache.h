/*
 * pwcache.c - memory cache passwd file handling
 *
 * Copyright © 2011-2023 <PERSON> <<EMAIL>>
 * Copyright © 2015-2023 <PERSON> <<EMAIL>>
 * Copyright © 2002      <PERSON>
 *
 * Older version:
 * Copyright © 1992-1998 <PERSON> <<EMAIL>>
 * Note: most likely none of his code remains
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
 */

#ifndef PROCPS_PROC_PWCACHE_H
#define PROCPS_PROC_PWCACHE_H

#include <sys/types.h>

// used in pwcache and in readproc to set size of username or groupname
#define HASHSIZE  64              /* power of 2 */
#define HASH(x)   ((x) & (HASHSIZE - 1))
#define P_G_SZ 33

struct pwbuf {
    struct pwbuf *next;
    uid_t uid;
    char name[P_G_SZ];
};
extern struct pwbuf *pw7hash[HASHSIZE];

char *pwcache_get_user(uid_t uid);
char *pwcache_get_group(gid_t gid);

// 清理函数
void cleanup_group_cache();

#endif
