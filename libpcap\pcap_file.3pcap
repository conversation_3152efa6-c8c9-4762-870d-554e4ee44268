.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_FILE 3PCAP "3 January 2014"
.SH NAME
pcap_file \- get the standard I/O stream for a savefile being read
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
FILE *pcap_file(pcap_t *p);
.ft
.fi
.SH DESCRIPTION
.BR pcap_file ()
returns the standard I/O stream of the ``savefile'', if a ``savefile''
was opened with
.BR pcap_open_offline (3PCAP),
or
.BR NULL ,
if a network device was opened with
.BR pcap_create (3PCAP)
and
.BR \%pcap_activate (3PCAP),
or with
.BR pcap_open_live (3PCAP).
.PP
Note that the Packet Capture library is usually built with large file
support, so the standard I/O stream of the ``savefile'' might refer to
a file larger than 2 gigabytes; applications that use
.BR pcap_file ()
should, if possible, use calls that support large files on the return
value of
.BR pcap_file ()
or the value returned by
.BR fileno (3)
when passed the return value of
.BR pcap_file ().
.SH SEE ALSO
.BR pcap (3PCAP)
