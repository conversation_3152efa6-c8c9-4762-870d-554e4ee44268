#include <stdint.h>
//ip
extern int  max_ip;
//char *ip_str[max_ip];
extern char **ip_str;
extern int ip_count;


//for pcap_compile
//#include "../libpcap/pcap-bpf.h"
extern struct bpf_program fcode;
extern char *cmdbuf1;
extern int Oflag1;
//bpf_u_int32 netmask1;
extern int netmask1;

//tcp1计时器开关
extern int timeout1;

//c_tcp1 lock开关
extern int c_tcp1_lock1;


//update 2024-10-30
typedef struct {
        char ip_src[15];
	char ip_dst[15];
        int count;
	//加入端口
	uint16_t sport, dport;	


}my_tcp; //tcp S.

//update 2024-9-20
typedef struct {
        char ip_src[16];
	char ip_dst[16];
        int count;


}my_tcp2;//tcp P.

typedef struct {
        char ip_src[16];
	char ip_dst[16];
        int count;
}my_tcp3;//icmp


//update 2024-10-6
typedef struct {
        char ip_src[16];
	char ip_dst[16];
        int count;


}my_tcp4;//raw ip

typedef struct {
        char ip_src[16];
	char ip_dst[16];
        int count;
}my_tcp5;//udp

//update 2024-9-20
typedef struct {
        char ip_src[16];
	char ip_dst[17];
        int count;


}my_tcp6;//[R.]


//update 2024-9-20
typedef struct {
        char ip_src[16];
	char ip_dst[16];
        int count;

}my_tcp7;//[F.]


//update 2024
typedef struct {
        char ip_src[16];
	char ip_dst[16];
        int count;


}my_tcp8;//[U]

//update 2024
typedef struct {
        char ip_src[16];
	char ip_dst[16];
        int count;


}my_tcp9;//[E]

//update 2024
typedef struct {
        char ip_src[16];
	char ip_dst[16];
        int count;

        
}my_tcp0;//[W]

//update 2024
typedef struct {
        char ip_src[16];
	char ip_dst[16];
        int count;
}my_tcpa;//[none]


typedef struct {
        char ip_src[16];
        char ip_dst[16];
        int count;
}my_tcpb;//[S]

//add for tcp2
 struct thread_tcp2 {
                char *e;
                char *ex_ip;
        };

#include <time.h>
extern time_t t1;
extern time_t t2;

extern int group;//tcp-syn
extern int group1;//tcp-push
extern int group2;//icmp
extern int group3;//raw ip
extern int group4;//udp
extern int group5;//R.
extern int group6;//F
extern int group7;//U
extern int group8;//E
extern int group9;//W
extern int group0;//none
extern int groupa;//S

extern int tcp_lock;

#include <pthread.h>
extern pthread_mutex_t tcp_sm;
extern pthread_cond_t tcp_sc;

extern my_tcp *my_tcp1;
extern my_tcp2 *my_tcp21;
extern my_tcp3 *my_tcp31;
extern my_tcp4 *my_tcp41;
extern my_tcp5 *my_tcp51;
extern my_tcp6 *my_tcp61;
extern my_tcp7 *my_tcp71;
extern my_tcp8 *my_tcp81;
extern my_tcp9 *my_tcp91;
extern my_tcp0 *my_tcp01;
extern my_tcpa *my_tcpa1;
extern my_tcpb *my_tcpb1;


//write lock
extern pthread_rwlock_t tcp1_rwlock;
extern pthread_rwlock_t tcp2_rwlock;
extern pthread_rwlock_t tcp3_rwlock;
extern pthread_rwlock_t tcp4_rwlock;
extern pthread_rwlock_t tcp5_rwlock;
extern pthread_rwlock_t tcp6_rwlock;
extern pthread_rwlock_t tcp7_rwlock;
extern pthread_rwlock_t tcp8_rwlock;
extern pthread_rwlock_t tcp9_rwlock;
extern pthread_rwlock_t tcp0_rwlock;
extern pthread_rwlock_t tcpa_rwlock;
extern pthread_rwlock_t tcpb_rwlock;

int tcp1(char *e);//include S.  and udp
int tcp2(void *args);//include P. icmp
//int icmp();
int tcp3(char *e);//R R.
int tcp4(char *e);//F F.
int tcp5(char *e);//U U.
int tcp6(char *e);//E
int tcp7(char *e);//W
int tcp8(char *e);//none
int tcp9(char *e);//S

//add for hsearch_r
#include <search.h>
extern struct hsearch_data *htab;

//add for list
extern my_tcp *list;

//add for c_tcp1
//int cleanup(pcap *pd);

// POSIX定时器相关函数声明
int setup_posix_timer();
void cancel_posix_timer();
void cleanup_posix_timer();
void reset_posix_timer_cleanup();
int get_timer_fd();

// UDP统计数据清理函数声明
void cleanup_udp_stats(void);

// ARP处理控制开关 (设置为0跳过ARP处理，避免内存泄漏)
#define ENABLE_ARP_PROCESSING 0
