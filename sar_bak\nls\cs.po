# Czech translations for sysstat package.
# Copyright (C) 2011 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the sysstat package.
#
# <PERSON><PERSON> <<EMAIL>>, 2010, 2011, 2013, 2015, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: sysstat 11.5.1\n"
"Report-Msgid-Bugs-To: sysstat <at> orange.fr\n"
"POT-Creation-Date: 2016-09-23 15:07+0200\n"
"PO-Revision-Date: 2016-09-24 00:01+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Czech  <<EMAIL>>\n"
"Language: cs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"Plural-Forms: nplurals=3; plural=(n==1) ? 0 : (n>=2 && n<=4) ? 1 : 2;\n"
"X-Generator: Gtranslator 2.91.7\n"

#: iostat.c:86 cifsiostat.c:70 mpstat.c:98 sar.c:96 pidstat.c:87 tapestat.c:89
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ]\n"
msgstr "Použití: %s [přepínače] [<interval> [<počet>]]\n"

#: iostat.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ -o JSON ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ] [ --debuginfo ]\n"
msgstr ""
"Přepínače jsou:\n"
"[-c] [-d] [-h] [-k|-m] [-N] [-t] [-V] [-x] [-y] [-z]\n"
"[-j {ID|LABEL|PATH|UUID|…}] [-o JSON]\n"
"[[-H] -g <název_skupiny>] [-p [<zařízení>[,…]|ALL]]\n"
"[<zařízení> […]|ALL] [--debuginfo]\n"

#: iostat.c:95
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ -o JSON ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ]\n"
msgstr ""
"Přepínače jsou:\n"
"[-c] [-d] [-h] [-k|-m] [-N] [-t] [-V] [-x] [-y] [-z]\n"
"[-j {ID|LABEL|PATH|UUID|…}] [-o JSON]\n"
"[[-H] -g <název_skupiny>] [-p [<zařízení>[,…]|ALL]]\n"
"[<zařízení> […]|ALL]\n"

#: iostat.c:326
#, c-format
msgid "Cannot find disk data\n"
msgstr "Nelze najít data na disku\n"

#: iostat.c:1660 sa_common.c:1650
#, c-format
msgid "Invalid type of persistent device name\n"
msgstr "Neplatný typ trvalého názvu zařízení\n"

#: sadf_misc.c:749
#, c-format
msgid "System activity data file: %s (%#x)\n"
msgstr "Datový soubor se systémovými aktivitami: %s (%#x)\n"

#: sadf_misc.c:758
#, c-format
msgid "Genuine sa datafile: %s (%x)\n"
msgstr "Skutečný datový soubor se systémovými aktivitami: %s (%x)\n"

#: sadf_misc.c:759
msgid "no"
msgstr "ne"

#: sadf_misc.c:759
msgid "yes"
msgstr "ano"

#: sadf_misc.c:762
#, c-format
msgid "Host: "
msgstr "Počítač: "

#: sadf_misc.c:769
#, c-format
msgid "Number of CPU for last samples in file: %u\n"
msgstr "Počet CPU pro poslední vzorky v souboru: %u\n"

#: sadf_misc.c:775
#, c-format
msgid "File date: %s\n"
msgstr "Datum souboru: %s\n"

#: sadf_misc.c:778
#, c-format
msgid "File time: "
msgstr "Čas souboru: "

#: sadf_misc.c:783
#, c-format
msgid "Size of a long int: %d\n"
msgstr "Velikost „long int“: %d\n"

#: sadf_misc.c:789
#, c-format
msgid "List of activities:\n"
msgstr "Seznam aktivit:\n"

#: sadf_misc.c:802
#, c-format
msgid "\t[Unknown activity format]"
msgstr "\t[Neznámý formát aktivity]"

#: sadc.c:89
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
msgstr "Použití: %s [přepínače] [<interval> [<počet>]] [<vstupní_soubor>]\n"

#: sadc.c:92
#, c-format
msgid ""
"Options are:\n"
"[ -C <comment> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"
msgstr ""
"Přepínače jsou:\n"
"[-C <komentář>] [-D] [-F] [-L] [-V]\n"
"[-S {INT|DISK|IPV6|POWER|SNMP|XDISK|ALL|XALL}]\n"

#: sadc.c:267
#, c-format
msgid "Cannot write data to system activity file: %s\n"
msgstr "Nelze zapsat data do souboru se systémovými aktivitami: %s\n"

#: sadc.c:563
#, c-format
msgid "Cannot write system activity file header: %s\n"
msgstr "Nelze zapsat hlavičku souboru se systémovými aktivitami: %s\n"

#: sadc.c:763 sadc.c:772 sadc.c:839 ioconf.c:510 rd_stats.c:69
#: sa_common.c:1264 count.c:118
#, c-format
msgid "Cannot open %s: %s\n"
msgstr "Nelze otevřít %s: %s\n"

#: sadc.c:1019
#, c-format
msgid "Cannot append data to that file (%s)\n"
msgstr "Nelze připojit data do tohoto souboru (%s)\n"

#: common.c:74
#, c-format
msgid "sysstat version %s\n"
msgstr "sysstat verze %s\n"

#: cifsiostat.c:74
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"
msgstr ""
"Přepínače jsou:\n"
"[-h] [-k|-m] [-t] [-V] [--debuginfo]\n"

#: cifsiostat.c:77
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
msgstr ""
"Přepínače jsou:\n"
"[-h] [-k|-m] [-t] [-V]\n"

#: mpstat.c:101
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -o JSON ] [ -P { <cpu> [,...] | ON | ALL } ]\n"
msgstr ""
"Přepínače jsou:\n"
"[-A] [-u] [-V] [-I {SUM|CPU|SCPU|ALL}]\n"
"[-o JSON] [-P {<procesor>[,…]|ON|ALL}]\n"

#: mpstat.c:1210 sar.c:358 pidstat.c:2303
msgid "Average:"
msgstr "Průměr:"

#: mpstat.c:1636
#, c-format
msgid "Not that many processors!\n"
msgstr "Tolik procesorů není!\n"

#: sadf.c:86
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <datafile> | -[0-9]+ ]\n"
msgstr "Použití: %s [přepínače] [<interval> [<počet>]] [<datový_soubor>|-[0-9]+]\n"

#: sadf.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -C ] [ -c | -d | -g | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -O <opts> [,...] ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
"[ -- <sar_options> ]\n"
msgstr ""
"Přepínače jsou:\n"
"[-C] [-c|-d|-g|-j|-p|-x] [-H] [-h] [-T|-t|-U] [-V]\n"
"[-O <volby> [,…] ] [-P {<procesor> [,…]|ALL}]\n"
"[-s[ <hh:mm[:ss]>]] [-e[ <hh:mm[:ss]>]]\n"
"[-- <přepínače_sar>]\n"

#: sar.c:111
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ MOUNT ] ] [ -H ] [ -h ]\n"
"[ -p ] [ -q ] [ -R ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --sadc ]\n"
"[ -I { <int> [,...] | SUM | ALL | XALL } ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -m { <keyword> [,...] | ALL } ] [ -n { <keyword> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filename> ] | -o [ <filename> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
msgstr ""
"Přepínače jsou:\n"
"[-A] [-B] [-b] [-C] [-D] [-d] [-F [MOUNT]] [-H] [-h]\n"
"[-p] [-q] [-R] [-r [ALL]] [-S] [-t] [-u [ALL]] [-V]\n"
"[-v] [-W] [-w] [-y] [--sadc]\n"
"[-I {<přerušení>[,…]|SUM|ALL|XALL}] [-P {<procesor>[,…]|ALL}]\n"
"[-m {<klíčové_slovo>[,…]|ALL}] [-n {<klíčové_slovo>[,…]|ALL}]\n"
"[-j {ID|LABEL|PATH|UUID|…}]\n"
"[-f [<název_souboru>]|-o [<název_souboru>]|-[0-9]+]\n"
"[-i <interval>] [-s[ <hh:mm[:ss]>]] [-e[ <hh:mm[:ss]>]]\n"

#: sar.c:134
#, c-format
msgid "Main options and reports:\n"
msgstr "Hlavní přepínače a výstupní sestavy:\n"

#: sar.c:135
#, c-format
msgid "\t-B\tPaging statistics\n"
msgstr "\t-B\tStatistiky stránkování\n"

#: sar.c:136
#, c-format
msgid "\t-b\tI/O and transfer rate statistics\n"
msgstr "\t-b\tStatistiky přenosové rychlosti V/V\n"

#: sar.c:137
#, c-format
msgid "\t-d\tBlock devices statistics\n"
msgstr "\t-d\tStatistiky blokových zařízení\n"

#: sar.c:138
#, c-format
msgid "\t-F [ MOUNT ]\n"
msgstr "\t-F [MOUNT]\n"

#: sar.c:139
#, c-format
msgid "\t\tFilesystems statistics\n"
msgstr "\t\tStatistiky souborových systémů\n"

#: sar.c:140
#, c-format
msgid "\t-H\tHugepages utilization statistics\n"
msgstr "\t-H\tStatistiky využití velkých paměťových stránek (hugepages)\n"

#: sar.c:141
#, c-format
msgid ""
"\t-I { <int> | SUM | ALL | XALL }\n"
"\t\tInterrupts statistics\n"
msgstr ""
"\t-I {<přerušení>|SUM|ALL|XALL}\n"
"\t\tStatistiky přerušení\n"

#: sar.c:143
#, c-format
msgid ""
"\t-m { <keyword> [,...] | ALL }\n"
"\t\tPower management statistics\n"
"\t\tKeywords are:\n"
"\t\tCPU\tCPU instantaneous clock frequency\n"
"\t\tFAN\tFans speed\n"
"\t\tFREQ\tCPU average clock frequency\n"
"\t\tIN\tVoltage inputs\n"
"\t\tTEMP\tDevices temperature\n"
"\t\tUSB\tUSB devices plugged into the system\n"
msgstr ""
"\t-m {<klíčové_slovo>[,…]|ALL}\n"
"\t\tStatistiky správy napájení\n"
"\t\tKlíčová slova jsou:\n"
"\t\tCPU\tMomentální frekvence procesoru\n"
"\t\tFAN\tRychlost ventilátoru\n"
"\t\tFREQ\tPrůměrná frekvence procesoru\n"
"\t\tIN\tVstupní napětí\n"
"\t\tTEMP\tTeplota zařízení\n"
"\t\tUSB\tZařízení USB připojená do systému\n"

#: sar.c:152
#, c-format
msgid ""
"\t-n { <keyword> [,...] | ALL }\n"
"\t\tNetwork statistics\n"
"\t\tKeywords are:\n"
"\t\tDEV\tNetwork interfaces\n"
"\t\tEDEV\tNetwork interfaces (errors)\n"
"\t\tNFS\tNFS client\n"
"\t\tNFSD\tNFS server\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP traffic\t(v4)\n"
"\t\tEIP\tIP traffic\t(v4) (errors)\n"
"\t\tICMP\tICMP traffic\t(v4)\n"
"\t\tEICMP\tICMP traffic\t(v4) (errors)\n"
"\t\tTCP\tTCP traffic\t(v4)\n"
"\t\tETCP\tTCP traffic\t(v4) (errors)\n"
"\t\tUDP\tUDP traffic\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP traffic\t(v6)\n"
"\t\tEIP6\tIP traffic\t(v6) (errors)\n"
"\t\tICMP6\tICMP traffic\t(v6)\n"
"\t\tEICMP6\tICMP traffic\t(v6) (errors)\n"
"\t\tUDP6\tUDP traffic\t(v6)\n"
"\t\tFC\tFibre channel HBAs\n"
msgstr ""
"\t-n {<klíčové_slovo>[,…]|ALL}\n"
"\t\tStatistiky sítě\n"
"\t\tKlíčová slova jsou:\n"
"\t\tDEV\tSíťové rozhraní\n"
"\t\tEDEV\tSíťové rozhraní (chyby)\n"
"\t\tNFS\tKlient NFS\n"
"\t\tNFSD\tServer NFS\n"
"\t\tSOCK\tSokety\t(v4)\n"
"\t\tIP\tProvoz IP\t(v4)\n"
"\t\tEIP\tProvoz IP\t(v4) (chyby)\n"
"\t\tICMP\tProvoz ICMP\t(v4)\n"
"\t\tEICMP\tProvoz ICMP\t(v4) (chyby)\n"
"\t\tTCP\tProvoz TCP\t(v4)\n"
"\t\tETCP\tProvoz TCP\t(v4) (chyby)\n"
"\t\tUDP\tProvoz UDP\t(v4)\n"
"\t\tSOCK6\tSokety\t(v6)\n"
"\t\tIP6\tProvoz IP\t(v6)\n"
"\t\tEIP6\tProvoz IP\t(v6) (chyby)\n"
"\t\tICMP6\tProvoz ICMP\t(v6)\n"
"\t\tEICMP6\tProvoz ICMP\t(v6) (chyby)\n"
"\t\tUDP6\tProvoz UDP\t(v6)\n"
"\t\tFC\tHBA optických kanálů\n"

#: sar.c:174
#, c-format
msgid "\t-q\tQueue length and load average statistics\n"
msgstr "\t-q\tStatistiky délky fronty a průměrného vytížení\n"

#: sar.c:175
#, c-format
msgid "\t-R\tMemory statistics\n"
msgstr "\t-R\tStatistiky paměti\n"

#: sar.c:176
#, c-format
msgid ""
"\t-r [ ALL ]\n"
"\t\tMemory utilization statistics\n"
msgstr ""
"\t-r [ALL]\n"
"\t\tStatistiky využití paměti\n"

#: sar.c:178
#, c-format
msgid "\t-S\tSwap space utilization statistics\n"
msgstr "\t-S\tStatistiky využití odkládacího prostoru\n"

#: sar.c:179
#, c-format
msgid ""
"\t-u [ ALL ]\n"
"\t\tCPU utilization statistics\n"
msgstr ""
"\t-u [ALL]\n"
"\t\tStatistiky využití procesoru\n"

#: sar.c:181
#, c-format
msgid "\t-v\tKernel tables statistics\n"
msgstr "\t-v\tStatistiky tabulek jádra\n"

#: sar.c:182
#, c-format
msgid "\t-W\tSwapping statistics\n"
msgstr "\t-W\tStatistiky odkládání na disk\n"

#: sar.c:183
#, c-format
msgid "\t-w\tTask creation and system switching statistics\n"
msgstr "\t-w\tStatistiky vytváření úloh a systémového přepínání\n"

#: sar.c:184
#, c-format
msgid "\t-y\tTTY devices statistics\n"
msgstr "\t-y\tStatistiky zařízení TTY\n"

#: sar.c:198
#, c-format
msgid "Data collector will be sought in PATH\n"
msgstr "Sběrač dat bude hledán v cestě PATH\n"

#: sar.c:201
#, c-format
msgid "Data collector found: %s\n"
msgstr "Nalezen sběrač dat: %s\n"

#: sar.c:260
#, c-format
msgid "End of data collecting unexpected\n"
msgstr "Neočekávaný konec sbírání dat\n"

#: sar.c:813
#, c-format
msgid "Using a wrong data collector from a different sysstat version\n"
msgstr "Používání nesprávného sběrače dat z jiné verze sysstat\n"

#: sar.c:865
#, c-format
msgid "Inconsistent input data\n"
msgstr "Nekonzistentní vstupní data\n"

#: sar.c:1044 pidstat.c:239
#, c-format
msgid "Requested activities not available\n"
msgstr "Požadované aktivity nejsou dostupné\n"

#: sar.c:1350
#, c-format
msgid "-f and -o options are mutually exclusive\n"
msgstr "Přepínače -f a -o se navzájem vylučují\n"

#: sar.c:1356
#, c-format
msgid "Not reading from a system activity file (use -f option)\n"
msgstr "Nečte se ze souboru se systémovými aktivitami (použijte přepínač -f)\n"

#: sar.c:1492
#, c-format
msgid "Cannot find the data collector (%s)\n"
msgstr "Nelze najít sběrač dat (%s)\n"

#: pr_stats.c:2494 pr_stats.c:2505 pr_stats.c:2606 pr_stats.c:2617
msgid "Summary:"
msgstr "Souhrn:"

#: pr_stats.c:2547
msgid "Other devices not listed here"
msgstr "Ostatní zde neuvedená zařízení"

#: sa_conv.c:69
#, c-format
msgid "Cannot convert the format of this file\n"
msgstr "Nelze převést formát tohoto souboru\n"

#: sa_conv.c:562
#, c-format
msgid ""
"\n"
"CPU activity not found in file. Aborting...\n"
msgstr ""
"\n"
"V souboru nebyla nalezena aktivita CPU. Končí se…\n"

#: sa_conv.c:578
#, c-format
msgid ""
"\n"
"Invalid data found. Aborting...\n"
msgstr ""
"\n"
"Byla nalezena neplatná data. Končí se…\n"

#: sa_conv.c:897
#, c-format
msgid "Statistics: "
msgstr "Statistiky: "

#: sa_conv.c:1016
#, c-format
msgid ""
"\n"
"File successfully converted to sysstat format version %s\n"
msgstr ""
"\n"
"Soubor byl úspěšně převeden do formátu sysstat verze %s\n"

#: pidstat.c:90
#, c-format
msgid ""
"Options are:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ] [ -U [ <username> ] ]\n"
"[ -u ] [ -V ] [ -v ] [ -w ] [ -C <command> ] [ -G <process_name> ]\n"
"[ -p { <pid> [,...] | SELF | ALL } ] [ -T { TASK | CHILD | ALL } ]\n"
msgstr ""
"Přepínače jsou:\n"
"[-d] [-h] [-I] [-l] [-R] [-r] [-s] [-t] [-U [<uživatelské_jméno>]]\n"
"[-u] [-V] [-v] [-w] [-C <příkaz>] [-G <název_procesu>]\n"
"[-p {<id_procesu>[,…]|SELF|ALL}] [-T {TASK|CHILD|ALL}]\n"

#: sa_common.c:1060
#, c-format
msgid "Error while reading system activity file: %s\n"
msgstr "Chyba při čtení souboru se systémovými aktivitami: %s\n"

#: sa_common.c:1070
#, c-format
msgid "End of system activity file unexpected\n"
msgstr "Neočekávaný konec souboru se systémovými aktivitami\n"

#: sa_common.c:1089
#, c-format
msgid "File created by sar/sadc from sysstat version %d.%d.%d"
msgstr "Soubor vytvořen pomocí sar/sadc z balíku sysstat verze %d.%d.%d"

#: sa_common.c:1122
#, c-format
msgid "Invalid system activity file: %s\n"
msgstr "Neplatný soubor se systémovými aktivitami: %s\n"

#: sa_common.c:1134
#, c-format
msgid "Endian format mismatch\n"
msgstr "Formát endian neodpovídá\n"

#: sa_common.c:1138
#, c-format
msgid "Current sysstat version cannot read the format of this file (%#x)\n"
msgstr "Současná verze sysstat neumí číst formát tohoto souboru (%#x)\n"

#: sa_common.c:1267
#, c-format
msgid "Please check if data collecting is enabled\n"
msgstr "Zkontrolujte prosím, zda je povolen sběr dat\n"

#: sa_common.c:1460
#, c-format
msgid "Requested activities not available in file %s\n"
msgstr "Požadované aktivity nejsou v souboru %s dostupné\n"

#: tapestat.c:91
#, c-format
msgid ""
"Options are:\n"
"[ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"
msgstr ""
"Přepínače jsou:\n"
"[-k|-m] [-t] [-V] [-y] [-z]\n"

#: tapestat.c:257
#, c-format
msgid "No tape drives with statistics found\n"
msgstr "Nebyly nalezeny žádné páskové mechaniky se statistikami\n"

#: count.c:169
#, c-format
msgid "Cannot handle so many processors!\n"
msgstr "Nelze obsloužit tolik procesorů!\n"
