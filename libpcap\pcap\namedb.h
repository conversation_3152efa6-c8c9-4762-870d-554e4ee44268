/*
 * Copyright (c) 1994, 1996
 *	The Regents of the University of California.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *	This product includes software developed by the Computer Systems
 *	Engineering Group at Lawrence Berkeley Laboratory.
 * 4. Neither the name of the University nor of the Laboratory may be used
 *    to endorse or promote products derived from this software without
 *    specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 */

#ifndef lib_pcap_namedb_h
#define lib_pcap_namedb_h

#include <stdio.h>		/* FILE */

#include <pcap/funcattrs.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 * As returned by the pcap_next_etherent()
 * XXX this stuff doesn't belong in this interface, but this
 * library already must do name to address translation, so
 * on systems that don't have support for /etc/ethers, we
 * export these hooks since they're already being used by
 * some applications (such as tcpdump) and already being
 * marked as exported in some OSes offering libpcap (such
 * as Debian).
 */
struct pcap_etherent {
	u_char addr[6];
	char name[122];
};
#ifndef PCAP_ETHERS_FILE
  #ifdef __HAIKU__
    #define PCAP_ETHERS_FILE "/boot/system/settings/network/ethers"
  #else
    #define PCAP_ETHERS_FILE "/etc/ethers"
  #endif
#endif

PCAP_AVAILABLE_0_4
PCAP_API struct	pcap_etherent *pcap_next_etherent(FILE *);

PCAP_AVAILABLE_0_4
PCAP_API u_char *pcap_ether_hostton(const char*);

PCAP_AVAILABLE_0_4
PCAP_API u_char *pcap_ether_aton(const char *);

PCAP_AVAILABLE_0_4
PCAP_API
PCAP_DEPRECATED("this is not reentrant; use 'pcap_nametoaddrinfo' instead")
bpf_u_int32 **pcap_nametoaddr(const char *);

PCAP_AVAILABLE_0_7
PCAP_API struct addrinfo *pcap_nametoaddrinfo(const char *);

PCAP_AVAILABLE_0_4
PCAP_API bpf_u_int32 pcap_nametonetaddr(const char *);

PCAP_AVAILABLE_0_4
PCAP_API int	pcap_nametoport(const char *, int *, int *);

PCAP_AVAILABLE_0_9
PCAP_API int	pcap_nametoportrange(const char *, int *, int *, int *);

PCAP_AVAILABLE_0_4
PCAP_API int	pcap_nametoproto(const char *);

PCAP_AVAILABLE_0_4
PCAP_API int	pcap_nametoeproto(const char *);

PCAP_AVAILABLE_0_9
PCAP_API int	pcap_nametollc(const char *);

/*
 * If a protocol is unknown, PROTO_UNDEF is returned.
 * Also, pcap_nametoport() returns the protocol along with the port number.
 * If there are ambiguous entries in /etc/services (i.e. domain
 * can be either tcp or udp) PROTO_UNDEF is returned.
 */
#define PROTO_UNDEF		-1

#ifdef __cplusplus
}
#endif

#endif
