m4trace:configure.ac:19: -1- AC_INIT([pcap], [m4_esyscmd_s(cat VERSION)], [https://github.com/the-tcpdump-group/libpcap/issues], [libpcap], [https://www.tcpdump.org/])
m4trace:configure.ac:19: -1- m4_pattern_forbid([^_?A[CHUM]_])
m4trace:configure.ac:19: -1- m4_pattern_forbid([_AC_])
m4trace:configure.ac:19: -1- m4_pattern_forbid([^LIBOBJS$], [do not use LIBOBJS directly, use AC_LIBOBJ (see section `AC_LIBOBJ vs LIBOBJS'])
m4trace:configure.ac:19: -1- m4_pattern_allow([^AS_FLAGS$])
m4trace:configure.ac:19: -1- m4_pattern_forbid([^_?m4_])
m4trace:configure.ac:19: -1- m4_pattern_forbid([^dnl$])
m4trace:configure.ac:19: -1- m4_pattern_forbid([^_?AS_])
m4trace:configure.ac:19: -1- AC_SUBST([SHELL])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([SHELL])
m4trace:configure.ac:19: -1- m4_pattern_allow([^SHELL$])
m4trace:configure.ac:19: -1- AC_SUBST([PATH_SEPARATOR])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([PATH_SEPARATOR])
m4trace:configure.ac:19: -1- m4_pattern_allow([^PATH_SEPARATOR$])
m4trace:configure.ac:19: -1- AC_SUBST([PACKAGE_NAME], [m4_ifdef([AC_PACKAGE_NAME],      ['AC_PACKAGE_NAME'])])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([PACKAGE_NAME])
m4trace:configure.ac:19: -1- m4_pattern_allow([^PACKAGE_NAME$])
m4trace:configure.ac:19: -1- AC_SUBST([PACKAGE_TARNAME], [m4_ifdef([AC_PACKAGE_TARNAME],   ['AC_PACKAGE_TARNAME'])])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([PACKAGE_TARNAME])
m4trace:configure.ac:19: -1- m4_pattern_allow([^PACKAGE_TARNAME$])
m4trace:configure.ac:19: -1- AC_SUBST([PACKAGE_VERSION], [m4_ifdef([AC_PACKAGE_VERSION],   ['AC_PACKAGE_VERSION'])])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([PACKAGE_VERSION])
m4trace:configure.ac:19: -1- m4_pattern_allow([^PACKAGE_VERSION$])
m4trace:configure.ac:19: -1- AC_SUBST([PACKAGE_STRING], [m4_ifdef([AC_PACKAGE_STRING],    ['AC_PACKAGE_STRING'])])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([PACKAGE_STRING])
m4trace:configure.ac:19: -1- m4_pattern_allow([^PACKAGE_STRING$])
m4trace:configure.ac:19: -1- AC_SUBST([PACKAGE_BUGREPORT], [m4_ifdef([AC_PACKAGE_BUGREPORT], ['AC_PACKAGE_BUGREPORT'])])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([PACKAGE_BUGREPORT])
m4trace:configure.ac:19: -1- m4_pattern_allow([^PACKAGE_BUGREPORT$])
m4trace:configure.ac:19: -1- AC_SUBST([PACKAGE_URL], [m4_ifdef([AC_PACKAGE_URL],       ['AC_PACKAGE_URL'])])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([PACKAGE_URL])
m4trace:configure.ac:19: -1- m4_pattern_allow([^PACKAGE_URL$])
m4trace:configure.ac:19: -1- AC_SUBST([exec_prefix], [NONE])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([exec_prefix])
m4trace:configure.ac:19: -1- m4_pattern_allow([^exec_prefix$])
m4trace:configure.ac:19: -1- AC_SUBST([prefix], [NONE])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([prefix])
m4trace:configure.ac:19: -1- m4_pattern_allow([^prefix$])
m4trace:configure.ac:19: -1- AC_SUBST([program_transform_name], [s,x,x,])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([program_transform_name])
m4trace:configure.ac:19: -1- m4_pattern_allow([^program_transform_name$])
m4trace:configure.ac:19: -1- AC_SUBST([bindir], ['${exec_prefix}/bin'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([bindir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^bindir$])
m4trace:configure.ac:19: -1- AC_SUBST([sbindir], ['${exec_prefix}/sbin'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([sbindir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^sbindir$])
m4trace:configure.ac:19: -1- AC_SUBST([libexecdir], ['${exec_prefix}/libexec'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([libexecdir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^libexecdir$])
m4trace:configure.ac:19: -1- AC_SUBST([datarootdir], ['${prefix}/share'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([datarootdir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^datarootdir$])
m4trace:configure.ac:19: -1- AC_SUBST([datadir], ['${datarootdir}'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([datadir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^datadir$])
m4trace:configure.ac:19: -1- AC_SUBST([sysconfdir], ['${prefix}/etc'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([sysconfdir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^sysconfdir$])
m4trace:configure.ac:19: -1- AC_SUBST([sharedstatedir], ['${prefix}/com'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([sharedstatedir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^sharedstatedir$])
m4trace:configure.ac:19: -1- AC_SUBST([localstatedir], ['${prefix}/var'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([localstatedir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^localstatedir$])
m4trace:configure.ac:19: -1- AC_SUBST([runstatedir], ['${localstatedir}/run'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([runstatedir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^runstatedir$])
m4trace:configure.ac:19: -1- AC_SUBST([includedir], ['${prefix}/include'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([includedir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^includedir$])
m4trace:configure.ac:19: -1- AC_SUBST([oldincludedir], ['/usr/include'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([oldincludedir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^oldincludedir$])
m4trace:configure.ac:19: -1- AC_SUBST([docdir], [m4_ifset([AC_PACKAGE_TARNAME],
				     ['${datarootdir}/doc/${PACKAGE_TARNAME}'],
				     ['${datarootdir}/doc/${PACKAGE}'])])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([docdir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^docdir$])
m4trace:configure.ac:19: -1- AC_SUBST([infodir], ['${datarootdir}/info'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([infodir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^infodir$])
m4trace:configure.ac:19: -1- AC_SUBST([htmldir], ['${docdir}'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([htmldir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^htmldir$])
m4trace:configure.ac:19: -1- AC_SUBST([dvidir], ['${docdir}'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([dvidir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^dvidir$])
m4trace:configure.ac:19: -1- AC_SUBST([pdfdir], ['${docdir}'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([pdfdir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^pdfdir$])
m4trace:configure.ac:19: -1- AC_SUBST([psdir], ['${docdir}'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([psdir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^psdir$])
m4trace:configure.ac:19: -1- AC_SUBST([libdir], ['${exec_prefix}/lib'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([libdir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^libdir$])
m4trace:configure.ac:19: -1- AC_SUBST([localedir], ['${datarootdir}/locale'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([localedir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^localedir$])
m4trace:configure.ac:19: -1- AC_SUBST([mandir], ['${datarootdir}/man'])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([mandir])
m4trace:configure.ac:19: -1- m4_pattern_allow([^mandir$])
m4trace:configure.ac:19: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_NAME])
m4trace:configure.ac:19: -1- m4_pattern_allow([^PACKAGE_NAME$])
m4trace:configure.ac:19: -1- AH_OUTPUT([PACKAGE_NAME], [/* Define to the full name of this package. */
@%:@undef PACKAGE_NAME])
m4trace:configure.ac:19: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_TARNAME])
m4trace:configure.ac:19: -1- m4_pattern_allow([^PACKAGE_TARNAME$])
m4trace:configure.ac:19: -1- AH_OUTPUT([PACKAGE_TARNAME], [/* Define to the one symbol short name of this package. */
@%:@undef PACKAGE_TARNAME])
m4trace:configure.ac:19: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_VERSION])
m4trace:configure.ac:19: -1- m4_pattern_allow([^PACKAGE_VERSION$])
m4trace:configure.ac:19: -1- AH_OUTPUT([PACKAGE_VERSION], [/* Define to the version of this package. */
@%:@undef PACKAGE_VERSION])
m4trace:configure.ac:19: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_STRING])
m4trace:configure.ac:19: -1- m4_pattern_allow([^PACKAGE_STRING$])
m4trace:configure.ac:19: -1- AH_OUTPUT([PACKAGE_STRING], [/* Define to the full name and version of this package. */
@%:@undef PACKAGE_STRING])
m4trace:configure.ac:19: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_BUGREPORT])
m4trace:configure.ac:19: -1- m4_pattern_allow([^PACKAGE_BUGREPORT$])
m4trace:configure.ac:19: -1- AH_OUTPUT([PACKAGE_BUGREPORT], [/* Define to the address where bug reports for this package should be sent. */
@%:@undef PACKAGE_BUGREPORT])
m4trace:configure.ac:19: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_URL])
m4trace:configure.ac:19: -1- m4_pattern_allow([^PACKAGE_URL$])
m4trace:configure.ac:19: -1- AH_OUTPUT([PACKAGE_URL], [/* Define to the home page for this package. */
@%:@undef PACKAGE_URL])
m4trace:configure.ac:19: -1- AC_SUBST([DEFS])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([DEFS])
m4trace:configure.ac:19: -1- m4_pattern_allow([^DEFS$])
m4trace:configure.ac:19: -1- AC_SUBST([ECHO_C])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([ECHO_C])
m4trace:configure.ac:19: -1- m4_pattern_allow([^ECHO_C$])
m4trace:configure.ac:19: -1- AC_SUBST([ECHO_N])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([ECHO_N])
m4trace:configure.ac:19: -1- m4_pattern_allow([^ECHO_N$])
m4trace:configure.ac:19: -1- AC_SUBST([ECHO_T])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([ECHO_T])
m4trace:configure.ac:19: -1- m4_pattern_allow([^ECHO_T$])
m4trace:configure.ac:19: -1- AC_SUBST([LIBS])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.ac:19: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:19: -1- AC_SUBST([build_alias])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([build_alias])
m4trace:configure.ac:19: -1- m4_pattern_allow([^build_alias$])
m4trace:configure.ac:19: -1- AC_SUBST([host_alias])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([host_alias])
m4trace:configure.ac:19: -1- m4_pattern_allow([^host_alias$])
m4trace:configure.ac:19: -1- AC_SUBST([target_alias])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([target_alias])
m4trace:configure.ac:19: -1- m4_pattern_allow([^target_alias$])
m4trace:configure.ac:26: -1- AC_SUBST([PACKAGE_NAME])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([PACKAGE_NAME])
m4trace:configure.ac:26: -1- m4_pattern_allow([^PACKAGE_NAME$])
m4trace:configure.ac:99: -1- AC_SUBST([V_CCOPT])
m4trace:configure.ac:99: -1- AC_SUBST_TRACE([V_CCOPT])
m4trace:configure.ac:99: -1- m4_pattern_allow([^V_CCOPT$])
m4trace:configure.ac:100: -1- AC_SUBST([V_DEFS])
m4trace:configure.ac:100: -1- AC_SUBST_TRACE([V_DEFS])
m4trace:configure.ac:100: -1- m4_pattern_allow([^V_DEFS$])
m4trace:configure.ac:101: -1- AC_SUBST([V_INCLS])
m4trace:configure.ac:101: -1- AC_SUBST_TRACE([V_INCLS])
m4trace:configure.ac:101: -1- m4_pattern_allow([^V_INCLS$])
m4trace:configure.ac:102: -1- AC_SUBST([LIBS_STATIC])
m4trace:configure.ac:102: -1- AC_SUBST_TRACE([LIBS_STATIC])
m4trace:configure.ac:102: -1- m4_pattern_allow([^LIBS_STATIC$])
m4trace:configure.ac:103: -1- AC_SUBST([REQUIRES_PRIVATE])
m4trace:configure.ac:103: -1- AC_SUBST_TRACE([REQUIRES_PRIVATE])
m4trace:configure.ac:103: -1- m4_pattern_allow([^REQUIRES_PRIVATE$])
m4trace:configure.ac:104: -1- AC_SUBST([LIBS_PRIVATE])
m4trace:configure.ac:104: -1- AC_SUBST_TRACE([LIBS_PRIVATE])
m4trace:configure.ac:104: -1- m4_pattern_allow([^LIBS_PRIVATE$])
m4trace:configure.ac:106: -1- AC_CANONICAL_HOST
m4trace:configure.ac:106: -1- AC_CANONICAL_BUILD
m4trace:configure.ac:106: -1- AC_REQUIRE_AUX_FILE([config.sub])
m4trace:configure.ac:106: -1- AC_REQUIRE_AUX_FILE([config.guess])
m4trace:configure.ac:106: -1- AC_SUBST([build], [$ac_cv_build])
m4trace:configure.ac:106: -1- AC_SUBST_TRACE([build])
m4trace:configure.ac:106: -1- m4_pattern_allow([^build$])
m4trace:configure.ac:106: -1- AC_SUBST([build_cpu], [$[1]])
m4trace:configure.ac:106: -1- AC_SUBST_TRACE([build_cpu])
m4trace:configure.ac:106: -1- m4_pattern_allow([^build_cpu$])
m4trace:configure.ac:106: -1- AC_SUBST([build_vendor], [$[2]])
m4trace:configure.ac:106: -1- AC_SUBST_TRACE([build_vendor])
m4trace:configure.ac:106: -1- m4_pattern_allow([^build_vendor$])
m4trace:configure.ac:106: -1- AC_SUBST([build_os])
m4trace:configure.ac:106: -1- AC_SUBST_TRACE([build_os])
m4trace:configure.ac:106: -1- m4_pattern_allow([^build_os$])
m4trace:configure.ac:106: -1- AC_SUBST([host], [$ac_cv_host])
m4trace:configure.ac:106: -1- AC_SUBST_TRACE([host])
m4trace:configure.ac:106: -1- m4_pattern_allow([^host$])
m4trace:configure.ac:106: -1- AC_SUBST([host_cpu], [$[1]])
m4trace:configure.ac:106: -1- AC_SUBST_TRACE([host_cpu])
m4trace:configure.ac:106: -1- m4_pattern_allow([^host_cpu$])
m4trace:configure.ac:106: -1- AC_SUBST([host_vendor], [$[2]])
m4trace:configure.ac:106: -1- AC_SUBST_TRACE([host_vendor])
m4trace:configure.ac:106: -1- m4_pattern_allow([^host_vendor$])
m4trace:configure.ac:106: -1- AC_SUBST([host_os])
m4trace:configure.ac:106: -1- AC_SUBST_TRACE([host_os])
m4trace:configure.ac:106: -1- m4_pattern_allow([^host_os$])
m4trace:configure.ac:114: -1- _m4_warn([obsolete], [The macro `AC_PROG_CC_C99' is obsolete.
You should run autoupdate.], [./lib/autoconf/c.m4:1659: AC_PROG_CC_C99 is expanded from...
configure.ac:114: the top level])
m4trace:configure.ac:114: -1- AC_SUBST([CC])
m4trace:configure.ac:114: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:114: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:114: -1- AC_SUBST([CFLAGS])
m4trace:configure.ac:114: -1- AC_SUBST_TRACE([CFLAGS])
m4trace:configure.ac:114: -1- m4_pattern_allow([^CFLAGS$])
m4trace:configure.ac:114: -1- AC_SUBST([LDFLAGS])
m4trace:configure.ac:114: -1- AC_SUBST_TRACE([LDFLAGS])
m4trace:configure.ac:114: -1- m4_pattern_allow([^LDFLAGS$])
m4trace:configure.ac:114: -1- AC_SUBST([LIBS])
m4trace:configure.ac:114: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.ac:114: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:114: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.ac:114: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.ac:114: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:114: -1- AC_SUBST([CC])
m4trace:configure.ac:114: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:114: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:114: -1- AC_SUBST([CC])
m4trace:configure.ac:114: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:114: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:114: -1- AC_SUBST([CC])
m4trace:configure.ac:114: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:114: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:114: -1- AC_SUBST([CC])
m4trace:configure.ac:114: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:114: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:114: -1- AC_SUBST([ac_ct_CC])
m4trace:configure.ac:114: -1- AC_SUBST_TRACE([ac_ct_CC])
m4trace:configure.ac:114: -1- m4_pattern_allow([^ac_ct_CC$])
m4trace:configure.ac:114: -1- AC_SUBST([CC])
m4trace:configure.ac:114: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:114: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:114: -1- AC_SUBST([EXEEXT], [$ac_cv_exeext])
m4trace:configure.ac:114: -1- AC_SUBST_TRACE([EXEEXT])
m4trace:configure.ac:114: -1- m4_pattern_allow([^EXEEXT$])
m4trace:configure.ac:114: -1- AC_SUBST([OBJEXT], [$ac_cv_objext])
m4trace:configure.ac:114: -1- AC_SUBST_TRACE([OBJEXT])
m4trace:configure.ac:114: -1- m4_pattern_allow([^OBJEXT$])
m4trace:configure.ac:122: -1- AC_DEFINE_TRACE_LITERAL([_FILE_OFFSET_BITS])
m4trace:configure.ac:122: -1- m4_pattern_allow([^_FILE_OFFSET_BITS$])
m4trace:configure.ac:122: -1- AH_OUTPUT([_FILE_OFFSET_BITS], [/* Number of bits in a file offset, on hosts where this is settable. */
@%:@undef _FILE_OFFSET_BITS])
m4trace:configure.ac:122: -1- AC_DEFINE_TRACE_LITERAL([_LARGE_FILES])
m4trace:configure.ac:122: -1- m4_pattern_allow([^_LARGE_FILES$])
m4trace:configure.ac:122: -1- AH_OUTPUT([_LARGE_FILES], [/* Define for large files, on AIX-style hosts. */
@%:@undef _LARGE_FILES])
m4trace:configure.ac:123: -1- AC_DEFINE_TRACE_LITERAL([_LARGEFILE_SOURCE])
m4trace:configure.ac:123: -1- m4_pattern_allow([^_LARGEFILE_SOURCE$])
m4trace:configure.ac:123: -1- AH_OUTPUT([_LARGEFILE_SOURCE], [/* Define to 1 to make fseeko visible on some hosts (e.g. glibc 2.2). */
@%:@undef _LARGEFILE_SOURCE])
m4trace:configure.ac:123: -1- AC_DEFINE_TRACE_LITERAL([HAVE_FSEEKO])
m4trace:configure.ac:123: -1- m4_pattern_allow([^HAVE_FSEEKO$])
m4trace:configure.ac:123: -1- AH_OUTPUT([HAVE_FSEEKO], [/* Define to 1 if fseeko (and presumably ftello) exists and is declared. */
@%:@undef HAVE_FSEEKO])
m4trace:configure.ac:129: -1- AH_OUTPUT([HAVE_STDIO_H], [/* Define to 1 if you have the <stdio.h> header file. */
@%:@undef HAVE_STDIO_H])
m4trace:configure.ac:129: -1- AH_OUTPUT([HAVE_STDLIB_H], [/* Define to 1 if you have the <stdlib.h> header file. */
@%:@undef HAVE_STDLIB_H])
m4trace:configure.ac:129: -1- AH_OUTPUT([HAVE_STRING_H], [/* Define to 1 if you have the <string.h> header file. */
@%:@undef HAVE_STRING_H])
m4trace:configure.ac:129: -1- AH_OUTPUT([HAVE_INTTYPES_H], [/* Define to 1 if you have the <inttypes.h> header file. */
@%:@undef HAVE_INTTYPES_H])
m4trace:configure.ac:129: -1- AH_OUTPUT([HAVE_STDINT_H], [/* Define to 1 if you have the <stdint.h> header file. */
@%:@undef HAVE_STDINT_H])
m4trace:configure.ac:129: -1- AH_OUTPUT([HAVE_STRINGS_H], [/* Define to 1 if you have the <strings.h> header file. */
@%:@undef HAVE_STRINGS_H])
m4trace:configure.ac:129: -1- AH_OUTPUT([HAVE_SYS_STAT_H], [/* Define to 1 if you have the <sys/stat.h> header file. */
@%:@undef HAVE_SYS_STAT_H])
m4trace:configure.ac:129: -1- AH_OUTPUT([HAVE_SYS_TYPES_H], [/* Define to 1 if you have the <sys/types.h> header file. */
@%:@undef HAVE_SYS_TYPES_H])
m4trace:configure.ac:129: -1- AH_OUTPUT([HAVE_UNISTD_H], [/* Define to 1 if you have the <unistd.h> header file. */
@%:@undef HAVE_UNISTD_H])
m4trace:configure.ac:129: -1- AC_DEFINE_TRACE_LITERAL([STDC_HEADERS])
m4trace:configure.ac:129: -1- m4_pattern_allow([^STDC_HEADERS$])
m4trace:configure.ac:129: -1- AH_OUTPUT([STDC_HEADERS], [/* Define to 1 if all of the C90 standard headers exist (not just the ones
   required in a freestanding environment). This macro is provided for
   backward compatibility; new code need not use it. */
@%:@undef STDC_HEADERS])
m4trace:configure.ac:129: -1- AC_DEFINE_TRACE_LITERAL([SIZEOF_VOID_P])
m4trace:configure.ac:129: -1- m4_pattern_allow([^SIZEOF_VOID_P$])
m4trace:configure.ac:129: -1- AH_OUTPUT([SIZEOF_VOID_P], [/* The size of `void *\', as computed by sizeof. */
@%:@undef SIZEOF_VOID_P])
m4trace:configure.ac:135: -1- AC_DEFINE_TRACE_LITERAL([SIZEOF_TIME_T])
m4trace:configure.ac:135: -1- m4_pattern_allow([^SIZEOF_TIME_T$])
m4trace:configure.ac:135: -1- AH_OUTPUT([SIZEOF_TIME_T], [/* The size of `time_t\', as computed by sizeof. */
@%:@undef SIZEOF_TIME_T])
m4trace:configure.ac:139: -1- AC_DEFINE_TRACE_LITERAL([HAVE___ATOMIC_LOAD_N])
m4trace:configure.ac:139: -1- m4_pattern_allow([^HAVE___ATOMIC_LOAD_N$])
m4trace:configure.ac:139: -1- AH_OUTPUT([HAVE___ATOMIC_LOAD_N], [/* define if __atomic_load_n is supported by the compiler */
@%:@undef HAVE___ATOMIC_LOAD_N])
m4trace:configure.ac:139: -1- AC_DEFINE_TRACE_LITERAL([HAVE___ATOMIC_STORE_N])
m4trace:configure.ac:139: -1- m4_pattern_allow([^HAVE___ATOMIC_STORE_N$])
m4trace:configure.ac:139: -1- AH_OUTPUT([HAVE___ATOMIC_STORE_N], [/* define if __atomic_store_n is supported by the compiler */
@%:@undef HAVE___ATOMIC_STORE_N])
m4trace:configure.ac:172: -1- AC_DEFINE_TRACE_LITERAL([HAVE_DL_HP_PPA_INFO_T_DL_MODULE_ID_1])
m4trace:configure.ac:172: -1- m4_pattern_allow([^HAVE_DL_HP_PPA_INFO_T_DL_MODULE_ID_1$])
m4trace:configure.ac:172: -1- AH_OUTPUT([HAVE_DL_HP_PPA_INFO_T_DL_MODULE_ID_1], [/* Define to 1 if `dl_module_id_1\' is a member of `dl_hp_ppa_info_t\'. */
@%:@undef HAVE_DL_HP_PPA_INFO_T_DL_MODULE_ID_1])
m4trace:configure.ac:188: -1- AC_DEFINE_TRACE_LITERAL([HAVE_GNU_STRERROR_R])
m4trace:configure.ac:188: -1- m4_pattern_allow([^HAVE_GNU_STRERROR_R$])
m4trace:configure.ac:188: -1- AH_OUTPUT([HAVE_GNU_STRERROR_R], [/* Define to 1 if you have a GNU-style `strerror_r\' function. */
@%:@undef HAVE_GNU_STRERROR_R])
m4trace:configure.ac:188: -1- AC_DEFINE_TRACE_LITERAL([HAVE_POSIX_STRERROR_R])
m4trace:configure.ac:188: -1- m4_pattern_allow([^HAVE_POSIX_STRERROR_R$])
m4trace:configure.ac:188: -1- AH_OUTPUT([HAVE_POSIX_STRERROR_R], [/* Define to 1 if you have a POSIX-style `strerror_r\' function. */
@%:@undef HAVE_POSIX_STRERROR_R])
m4trace:configure.ac:188: -1- AH_OUTPUT([HAVE__WCSERROR_S], [/* Define to 1 if you have the `_wcserror_s\' function. */
@%:@undef HAVE__WCSERROR_S])
m4trace:configure.ac:188: -1- AC_DEFINE_TRACE_LITERAL([HAVE__WCSERROR_S])
m4trace:configure.ac:188: -1- m4_pattern_allow([^HAVE__WCSERROR_S$])
m4trace:configure.ac:308: -1- AH_OUTPUT([HAVE_VASPRINTF], [/* Define to 1 if you have the `vasprintf\' function. */
@%:@undef HAVE_VASPRINTF])
m4trace:configure.ac:308: -1- AH_OUTPUT([HAVE_ASPRINTF], [/* Define to 1 if you have the `asprintf\' function. */
@%:@undef HAVE_ASPRINTF])
m4trace:configure.ac:311: -1- AC_SUBST([LIB@&t@OBJS], ["$LIB@&t@OBJS asprintf.$ac_objext"])
m4trace:configure.ac:311: -1- AC_SUBST_TRACE([LIB@&t@OBJS])
m4trace:configure.ac:311: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.ac:311: -1- AC_LIBSOURCE([asprintf.c])
m4trace:configure.ac:315: -1- AH_OUTPUT([HAVE_STRLCAT], [/* Define to 1 if you have the `strlcat\' function. */
@%:@undef HAVE_STRLCAT])
m4trace:configure.ac:315: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRLCAT])
m4trace:configure.ac:315: -1- m4_pattern_allow([^HAVE_STRLCAT$])
m4trace:configure.ac:318: -1- AC_SUBST([LIB@&t@OBJS], ["$LIB@&t@OBJS strlcat.$ac_objext"])
m4trace:configure.ac:318: -1- AC_SUBST_TRACE([LIB@&t@OBJS])
m4trace:configure.ac:318: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.ac:318: -1- AC_LIBSOURCE([strlcat.c])
m4trace:configure.ac:322: -1- AH_OUTPUT([HAVE_STRLCPY], [/* Define to 1 if you have the `strlcpy\' function. */
@%:@undef HAVE_STRLCPY])
m4trace:configure.ac:322: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRLCPY])
m4trace:configure.ac:322: -1- m4_pattern_allow([^HAVE_STRLCPY$])
m4trace:configure.ac:325: -1- AC_SUBST([LIB@&t@OBJS], ["$LIB@&t@OBJS strlcpy.$ac_objext"])
m4trace:configure.ac:325: -1- AC_SUBST_TRACE([LIB@&t@OBJS])
m4trace:configure.ac:325: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.ac:325: -1- AC_LIBSOURCE([strlcpy.c])
m4trace:configure.ac:329: -1- AH_OUTPUT([HAVE_STRTOK_R], [/* Define to 1 if you have the `strtok_r\' function. */
@%:@undef HAVE_STRTOK_R])
m4trace:configure.ac:329: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRTOK_R])
m4trace:configure.ac:329: -1- m4_pattern_allow([^HAVE_STRTOK_R$])
m4trace:configure.ac:332: -1- AC_SUBST([LIB@&t@OBJS], ["$LIB@&t@OBJS strtok_r.$ac_objext"])
m4trace:configure.ac:332: -1- AC_SUBST_TRACE([LIB@&t@OBJS])
m4trace:configure.ac:332: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.ac:332: -1- AC_LIBSOURCE([strtok_r.c])
m4trace:configure.ac:354: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LINUX_GETNETBYNAME_R])
m4trace:configure.ac:354: -1- m4_pattern_allow([^HAVE_LINUX_GETNETBYNAME_R$])
m4trace:configure.ac:354: -1- AH_OUTPUT([HAVE_LINUX_GETNETBYNAME_R], [/* define if we have the Linux getnetbyname_r() */
@%:@undef HAVE_LINUX_GETNETBYNAME_R])
m4trace:configure.ac:354: -1- AC_DEFINE_TRACE_LITERAL([HAVE_SOLARIS_GETNETBYNAME_R])
m4trace:configure.ac:354: -1- m4_pattern_allow([^HAVE_SOLARIS_GETNETBYNAME_R$])
m4trace:configure.ac:354: -1- AH_OUTPUT([HAVE_SOLARIS_GETNETBYNAME_R], [/* define if we have the Solaris getnetbyname_r() */
@%:@undef HAVE_SOLARIS_GETNETBYNAME_R])
m4trace:configure.ac:354: -1- AC_DEFINE_TRACE_LITERAL([HAVE_AIX_GETNETBYNAME_R])
m4trace:configure.ac:354: -1- m4_pattern_allow([^HAVE_AIX_GETNETBYNAME_R$])
m4trace:configure.ac:354: -1- AH_OUTPUT([HAVE_AIX_GETNETBYNAME_R], [/* define if we have the AIX getnetbyname_r() */
@%:@undef HAVE_AIX_GETNETBYNAME_R])
m4trace:configure.ac:426: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LINUX_GETPROTOBYNAME_R])
m4trace:configure.ac:426: -1- m4_pattern_allow([^HAVE_LINUX_GETPROTOBYNAME_R$])
m4trace:configure.ac:426: -1- AH_OUTPUT([HAVE_LINUX_GETPROTOBYNAME_R], [/* define if we have the Linux getprotobyname_r() */
@%:@undef HAVE_LINUX_GETPROTOBYNAME_R])
m4trace:configure.ac:426: -1- AC_DEFINE_TRACE_LITERAL([HAVE_SOLARIS_GETPROTOBYNAME_R])
m4trace:configure.ac:426: -1- m4_pattern_allow([^HAVE_SOLARIS_GETPROTOBYNAME_R$])
m4trace:configure.ac:426: -1- AH_OUTPUT([HAVE_SOLARIS_GETPROTOBYNAME_R], [/* define if we have the Solaris getprotobyname_r() */
@%:@undef HAVE_SOLARIS_GETPROTOBYNAME_R])
m4trace:configure.ac:426: -1- AC_DEFINE_TRACE_LITERAL([HAVE_AIX_GETPROTOBYNAME_R])
m4trace:configure.ac:426: -1- m4_pattern_allow([^HAVE_AIX_GETPROTOBYNAME_R$])
m4trace:configure.ac:426: -1- AH_OUTPUT([HAVE_AIX_GETPROTOBYNAME_R], [/* define if we have the AIX getprotobyname_r() */
@%:@undef HAVE_AIX_GETPROTOBYNAME_R])
m4trace:configure.ac:496: -1- AH_OUTPUT([HAVE_ETHER_HOSTTON], [/* Define to 1 if you have the `ether_hostton\' function. */
@%:@undef HAVE_ETHER_HOSTTON])
m4trace:configure.ac:496: -1- AC_DEFINE_TRACE_LITERAL([HAVE_ETHER_HOSTTON])
m4trace:configure.ac:496: -1- m4_pattern_allow([^HAVE_ETHER_HOSTTON$])
m4trace:configure.ac:504: -1- AC_DEFINE_TRACE_LITERAL([NET_ETHERNET_H_DECLARES_ETHER_HOSTTON])
m4trace:configure.ac:504: -1- m4_pattern_allow([^NET_ETHERNET_H_DECLARES_ETHER_HOSTTON$])
m4trace:configure.ac:504: -1- AH_OUTPUT([NET_ETHERNET_H_DECLARES_ETHER_HOSTTON], [/* Define to 1 if net/ethernet.h declares `ether_hostton\' */
@%:@undef NET_ETHERNET_H_DECLARES_ETHER_HOSTTON])
m4trace:configure.ac:527: -1- AC_DEFINE_TRACE_LITERAL([NETINET_ETHER_H_DECLARES_ETHER_HOSTTON])
m4trace:configure.ac:527: -1- m4_pattern_allow([^NETINET_ETHER_H_DECLARES_ETHER_HOSTTON$])
m4trace:configure.ac:527: -1- AH_OUTPUT([NETINET_ETHER_H_DECLARES_ETHER_HOSTTON], [/* Define to 1 if netinet/ether.h declares `ether_hostton\' */
@%:@undef NETINET_ETHER_H_DECLARES_ETHER_HOSTTON])
m4trace:configure.ac:552: -1- AC_DEFINE_TRACE_LITERAL([SYS_ETHERNET_H_DECLARES_ETHER_HOSTTON])
m4trace:configure.ac:552: -1- m4_pattern_allow([^SYS_ETHERNET_H_DECLARES_ETHER_HOSTTON$])
m4trace:configure.ac:552: -1- AH_OUTPUT([SYS_ETHERNET_H_DECLARES_ETHER_HOSTTON], [/* Define to 1 if sys/ethernet.h declares `ether_hostton\' */
@%:@undef SYS_ETHERNET_H_DECLARES_ETHER_HOSTTON])
m4trace:configure.ac:579: -1- AC_DEFINE_TRACE_LITERAL([ARPA_INET_H_DECLARES_ETHER_HOSTTON])
m4trace:configure.ac:579: -1- m4_pattern_allow([^ARPA_INET_H_DECLARES_ETHER_HOSTTON$])
m4trace:configure.ac:579: -1- AH_OUTPUT([ARPA_INET_H_DECLARES_ETHER_HOSTTON], [/* Define to 1 if arpa/inet.h declares `ether_hostton\' */
@%:@undef ARPA_INET_H_DECLARES_ETHER_HOSTTON])
m4trace:configure.ac:607: -1- AC_DEFINE_TRACE_LITERAL([NETINET_IF_ETHER_H_DECLARES_ETHER_HOSTTON])
m4trace:configure.ac:607: -1- m4_pattern_allow([^NETINET_IF_ETHER_H_DECLARES_ETHER_HOSTTON$])
m4trace:configure.ac:607: -1- AH_OUTPUT([NETINET_IF_ETHER_H_DECLARES_ETHER_HOSTTON], [/* Define to 1 if netinet/if_ether.h declares `ether_hostton\' */
@%:@undef NETINET_IF_ETHER_H_DECLARES_ETHER_HOSTTON])
m4trace:configure.ac:627: -1- AC_DEFINE_TRACE_LITERAL([HAVE_DECL_ETHER_HOSTTON])
m4trace:configure.ac:627: -1- m4_pattern_allow([^HAVE_DECL_ETHER_HOSTTON$])
m4trace:configure.ac:627: -1- AH_OUTPUT([HAVE_DECL_ETHER_HOSTTON], [/* Define to 1 if you have the declaration of `ether_hostton\' */
@%:@undef HAVE_DECL_ETHER_HOSTTON])
m4trace:configure.ac:635: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRUCT_ETHER_ADDR])
m4trace:configure.ac:635: -1- m4_pattern_allow([^HAVE_STRUCT_ETHER_ADDR$])
m4trace:configure.ac:635: -1- AH_OUTPUT([HAVE_STRUCT_ETHER_ADDR], [/* Define to 1 if the system has the type `struct ether_addr\'. */
@%:@undef HAVE_STRUCT_ETHER_ADDR])
m4trace:configure.ac:653: -1- AC_DEFINE_TRACE_LITERAL([HAVE_GLIBC])
m4trace:configure.ac:653: -1- m4_pattern_allow([^HAVE_GLIBC$])
m4trace:configure.ac:653: -1- AH_OUTPUT([HAVE_GLIBC], [/* Define to 1 if using GNU libc. */
@%:@undef HAVE_GLIBC])
m4trace:configure.ac:653: -1- AC_DEFINE_TRACE_LITERAL([HAVE_UCLIBC])
m4trace:configure.ac:653: -1- m4_pattern_allow([^HAVE_UCLIBC$])
m4trace:configure.ac:653: -1- AH_OUTPUT([HAVE_UCLIBC], [/* Define to 1 if using uclibc(-ng). */
@%:@undef HAVE_UCLIBC])
m4trace:configure.ac:751: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_INSTRUMENT_FUNCTIONS])
m4trace:configure.ac:751: -1- m4_pattern_allow([^ENABLE_INSTRUMENT_FUNCTIONS$])
m4trace:configure.ac:751: -1- AH_OUTPUT([ENABLE_INSTRUMENT_FUNCTIONS], [/* define if you want to build the instrument functions code */
@%:@undef ENABLE_INSTRUMENT_FUNCTIONS])
m4trace:configure.ac:774: -1- AC_DEFINE_TRACE_LITERAL([NO_PROTOCHAIN])
m4trace:configure.ac:774: -1- m4_pattern_allow([^NO_PROTOCHAIN$])
m4trace:configure.ac:774: -1- AH_OUTPUT([NO_PROTOCHAIN], [/* do not use protochain */
@%:@undef NO_PROTOCHAIN])
m4trace:configure.ac:804: -1- AH_OUTPUT([HAVE_NET_BPF_H], [/* Define to 1 if you have the <net/bpf.h> header file. */
@%:@undef HAVE_NET_BPF_H])
m4trace:configure.ac:804: -1- AC_DEFINE_TRACE_LITERAL([HAVE_NET_BPF_H])
m4trace:configure.ac:804: -1- m4_pattern_allow([^HAVE_NET_BPF_H$])
m4trace:configure.ac:811: -1- AH_OUTPUT([HAVE_SYS_IOCCOM_H], [/* Define to 1 if you have the <sys/ioccom.h> header file. */
@%:@undef HAVE_SYS_IOCCOM_H])
m4trace:configure.ac:811: -1- AC_DEFINE_TRACE_LITERAL([HAVE_SYS_IOCCOM_H])
m4trace:configure.ac:811: -1- m4_pattern_allow([^HAVE_SYS_IOCCOM_H$])
m4trace:configure.ac:875: -1- AH_OUTPUT([HAVE_SYS_DLPI_H], [/* Define to 1 if you have the <sys/dlpi.h> header file. */
@%:@undef HAVE_SYS_DLPI_H])
m4trace:configure.ac:875: -1- AC_DEFINE_TRACE_LITERAL([HAVE_SYS_DLPI_H])
m4trace:configure.ac:875: -1- m4_pattern_allow([^HAVE_SYS_DLPI_H$])
m4trace:configure.ac:910: -1- AC_SUBST([VALGRINDTEST_SRC])
m4trace:configure.ac:910: -1- AC_SUBST_TRACE([VALGRINDTEST_SRC])
m4trace:configure.ac:910: -1- m4_pattern_allow([^VALGRINDTEST_SRC$])
m4trace:configure.ac:915: -1- m4_pattern_forbid([^_?PKG_[A-Z_]+$])
m4trace:configure.ac:915: -1- m4_pattern_allow([^PKG_CONFIG(_(PATH|LIBDIR|SYSROOT_DIR|ALLOW_SYSTEM_(CFLAGS|LIBS)))?$])
m4trace:configure.ac:915: -1- m4_pattern_allow([^PKG_CONFIG_(DISABLE_UNINSTALLED|TOP_BUILD_DIR|DEBUG_SPEW)$])
m4trace:configure.ac:915: -1- AC_SUBST([PKG_CONFIG])
m4trace:configure.ac:915: -1- AC_SUBST_TRACE([PKG_CONFIG])
m4trace:configure.ac:915: -1- m4_pattern_allow([^PKG_CONFIG$])
m4trace:configure.ac:915: -1- AC_SUBST([PKG_CONFIG_PATH])
m4trace:configure.ac:915: -1- AC_SUBST_TRACE([PKG_CONFIG_PATH])
m4trace:configure.ac:915: -1- m4_pattern_allow([^PKG_CONFIG_PATH$])
m4trace:configure.ac:915: -1- AC_SUBST([PKG_CONFIG_LIBDIR])
m4trace:configure.ac:915: -1- AC_SUBST_TRACE([PKG_CONFIG_LIBDIR])
m4trace:configure.ac:915: -1- m4_pattern_allow([^PKG_CONFIG_LIBDIR$])
m4trace:configure.ac:915: -1- AC_SUBST([PKG_CONFIG])
m4trace:configure.ac:915: -1- AC_SUBST_TRACE([PKG_CONFIG])
m4trace:configure.ac:915: -1- m4_pattern_allow([^PKG_CONFIG$])
m4trace:configure.ac:922: -1- AC_SUBST([BREW])
m4trace:configure.ac:922: -1- AC_SUBST_TRACE([BREW])
m4trace:configure.ac:922: -1- m4_pattern_allow([^BREW$])
m4trace:configure.ac:991: -1- AH_OUTPUT([HAVE_SYS_BUFMOD_H], [/* Define to 1 if you have the <sys/bufmod.h> header file. */
@%:@undef HAVE_SYS_BUFMOD_H])
m4trace:configure.ac:991: -1- AC_DEFINE_TRACE_LITERAL([HAVE_SYS_BUFMOD_H])
m4trace:configure.ac:991: -1- m4_pattern_allow([^HAVE_SYS_BUFMOD_H$])
m4trace:configure.ac:991: -1- AH_OUTPUT([HAVE_SYS_DLPI_EXT_H], [/* Define to 1 if you have the <sys/dlpi_ext.h> header file. */
@%:@undef HAVE_SYS_DLPI_EXT_H])
m4trace:configure.ac:991: -1- AC_DEFINE_TRACE_LITERAL([HAVE_SYS_DLPI_EXT_H])
m4trace:configure.ac:991: -1- m4_pattern_allow([^HAVE_SYS_DLPI_EXT_H$])
m4trace:configure.ac:1006: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LIBDLPI])
m4trace:configure.ac:1006: -1- m4_pattern_allow([^HAVE_LIBDLPI$])
m4trace:configure.ac:1006: -1- AH_OUTPUT([HAVE_LIBDLPI], [/* if libdlpi exists */
@%:@undef HAVE_LIBDLPI])
m4trace:configure.ac:1057: -1- AC_DEFINE_TRACE_LITERAL([HAVE_DL_PASSIVE_REQ_T])
m4trace:configure.ac:1057: -1- m4_pattern_allow([^HAVE_DL_PASSIVE_REQ_T$])
m4trace:configure.ac:1057: -1- AH_OUTPUT([HAVE_DL_PASSIVE_REQ_T], [/* Define to 1 if the system has the type `dl_passive_req_t\'. */
@%:@undef HAVE_DL_PASSIVE_REQ_T])
m4trace:configure.ac:1094: -1- AC_SUBST([LIBNL_CFLAGS])
m4trace:configure.ac:1094: -1- AC_SUBST_TRACE([LIBNL_CFLAGS])
m4trace:configure.ac:1094: -1- m4_pattern_allow([^LIBNL_CFLAGS$])
m4trace:configure.ac:1094: -1- AC_SUBST([LIBNL_LIBS])
m4trace:configure.ac:1094: -1- AC_SUBST_TRACE([LIBNL_LIBS])
m4trace:configure.ac:1094: -1- m4_pattern_allow([^LIBNL_LIBS$])
m4trace:configure.ac:1094: -1- AC_SUBST([LIBNL_LIBS_STATIC])
m4trace:configure.ac:1094: -1- AC_SUBST_TRACE([LIBNL_LIBS_STATIC])
m4trace:configure.ac:1094: -1- m4_pattern_allow([^LIBNL_LIBS_STATIC$])
m4trace:configure.ac:1094: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LIBNL])
m4trace:configure.ac:1094: -1- m4_pattern_allow([^HAVE_LIBNL$])
m4trace:configure.ac:1094: -1- AH_OUTPUT([HAVE_LIBNL], [/* if libnl exists */
@%:@undef HAVE_LIBNL])
m4trace:configure.ac:1108: -1- AC_SUBST([LIBNL_CFLAGS])
m4trace:configure.ac:1108: -1- AC_SUBST_TRACE([LIBNL_CFLAGS])
m4trace:configure.ac:1108: -1- m4_pattern_allow([^LIBNL_CFLAGS$])
m4trace:configure.ac:1108: -1- AC_SUBST([LIBNL_LIBS])
m4trace:configure.ac:1108: -1- AC_SUBST_TRACE([LIBNL_LIBS])
m4trace:configure.ac:1108: -1- m4_pattern_allow([^LIBNL_LIBS$])
m4trace:configure.ac:1108: -1- AC_SUBST([LIBNL_LIBS_STATIC])
m4trace:configure.ac:1108: -1- AC_SUBST_TRACE([LIBNL_LIBS_STATIC])
m4trace:configure.ac:1108: -1- m4_pattern_allow([^LIBNL_LIBS_STATIC$])
m4trace:configure.ac:1108: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LIBNL])
m4trace:configure.ac:1108: -1- m4_pattern_allow([^HAVE_LIBNL$])
m4trace:configure.ac:1108: -1- AH_OUTPUT([HAVE_LIBNL], [/* if libnl exists */
@%:@undef HAVE_LIBNL])
m4trace:configure.ac:1139: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LIBNL])
m4trace:configure.ac:1139: -1- m4_pattern_allow([^HAVE_LIBNL$])
m4trace:configure.ac:1139: -1- AH_OUTPUT([HAVE_LIBNL], [/* if libnl exists */
@%:@undef HAVE_LIBNL])
m4trace:configure.ac:1170: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRUCT_TPACKET_AUXDATA_TP_VLAN_TCI])
m4trace:configure.ac:1170: -1- m4_pattern_allow([^HAVE_STRUCT_TPACKET_AUXDATA_TP_VLAN_TCI$])
m4trace:configure.ac:1170: -1- AH_OUTPUT([HAVE_STRUCT_TPACKET_AUXDATA_TP_VLAN_TCI], [/* Define to 1 if `tp_vlan_tci\' is a member of `struct tpacket_auxdata\'. */
@%:@undef HAVE_STRUCT_TPACKET_AUXDATA_TP_VLAN_TCI])
m4trace:configure.ac:1177: -1- AC_DEFINE_TRACE_LITERAL([HAVE_DECL_SKF_AD_VLAN_TAG_PRESENT])
m4trace:configure.ac:1177: -1- m4_pattern_allow([^HAVE_DECL_SKF_AD_VLAN_TAG_PRESENT$])
m4trace:configure.ac:1177: -1- AH_OUTPUT([HAVE_DECL_SKF_AD_VLAN_TAG_PRESENT], [/* Define to 1 if you have the declaration of `SKF_AD_VLAN_TAG_PRESENT\', and
   to 0 if you don\'t. */
@%:@undef HAVE_DECL_SKF_AD_VLAN_TAG_PRESENT])
m4trace:configure.ac:1189: -1- AH_OUTPUT([HAVE_NET_IF_MEDIA_H], [/* Define to 1 if you have the <net/if_media.h> header file. */
@%:@undef HAVE_NET_IF_MEDIA_H])
m4trace:configure.ac:1189: -1- AC_DEFINE_TRACE_LITERAL([HAVE_NET_IF_MEDIA_H])
m4trace:configure.ac:1189: -1- m4_pattern_allow([^HAVE_NET_IF_MEDIA_H$])
m4trace:configure.ac:1194: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRUCT_BPF_TIMEVAL])
m4trace:configure.ac:1194: -1- m4_pattern_allow([^HAVE_STRUCT_BPF_TIMEVAL$])
m4trace:configure.ac:1194: -1- AH_OUTPUT([HAVE_STRUCT_BPF_TIMEVAL], [/* Define to 1 if the system has the type `struct BPF_TIMEVAL\'. */
@%:@undef HAVE_STRUCT_BPF_TIMEVAL])
m4trace:configure.ac:1219: -1- AC_DEFINE_TRACE_LITERAL([HAVE_SOLARIS_ANY_DEVICE])
m4trace:configure.ac:1219: -1- m4_pattern_allow([^HAVE_SOLARIS_ANY_DEVICE$])
m4trace:configure.ac:1219: -1- AH_OUTPUT([HAVE_SOLARIS_ANY_DEVICE], [/* target host supports Solaris "any" device */
@%:@undef HAVE_SOLARIS_ANY_DEVICE])
m4trace:configure.ac:1344: -1- AH_OUTPUT([HAVE_LINUX_NET_TSTAMP_H], [/* Define to 1 if you have the <linux/net_tstamp.h> header file. */
@%:@undef HAVE_LINUX_NET_TSTAMP_H])
m4trace:configure.ac:1344: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LINUX_NET_TSTAMP_H])
m4trace:configure.ac:1344: -1- m4_pattern_allow([^HAVE_LINUX_NET_TSTAMP_H$])
m4trace:configure.ac:1354: -1- AC_DEFINE_TRACE_LITERAL([HAVE_SOCKLEN_T])
m4trace:configure.ac:1354: -1- m4_pattern_allow([^HAVE_SOCKLEN_T$])
m4trace:configure.ac:1354: -1- AH_OUTPUT([HAVE_SOCKLEN_T], [/* Define to 1 if the system has the type `socklen_t\'. */
@%:@undef HAVE_SOCKLEN_T])
m4trace:configure.ac:1447: -1- AH_OUTPUT([HAVE_DAGAPI_H], [/* Define to 1 if you have the <dagapi.h> header file. */
@%:@undef HAVE_DAGAPI_H])
m4trace:configure.ac:1447: -1- AC_DEFINE_TRACE_LITERAL([HAVE_DAGAPI_H])
m4trace:configure.ac:1447: -1- m4_pattern_allow([^HAVE_DAGAPI_H$])
m4trace:configure.ac:1488: -1- AC_DEFINE_TRACE_LITERAL([HAVE_DAG_VDAG])
m4trace:configure.ac:1488: -1- m4_pattern_allow([^HAVE_DAG_VDAG$])
m4trace:configure.ac:1488: -1- AH_OUTPUT([HAVE_DAG_VDAG], [/* define if you have vdag_set_device_info() */
@%:@undef HAVE_DAG_VDAG])
m4trace:configure.ac:1499: -1- AC_DEFINE_TRACE_LITERAL([HAVE_DAG_API])
m4trace:configure.ac:1499: -1- m4_pattern_allow([^HAVE_DAG_API$])
m4trace:configure.ac:1499: -1- AH_OUTPUT([HAVE_DAG_API], [/* define if you have the DAG API */
@%:@undef HAVE_DAG_API])
m4trace:configure.ac:1508: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_DAG_TX])
m4trace:configure.ac:1508: -1- m4_pattern_allow([^ENABLE_DAG_TX$])
m4trace:configure.ac:1508: -1- AH_OUTPUT([ENABLE_DAG_TX], [/* Define to 1 if DAG transmit support is enabled */
@%:@undef ENABLE_DAG_TX])
m4trace:configure.ac:1636: -1- AC_DEFINE_TRACE_LITERAL([HAVE_SNF_API])
m4trace:configure.ac:1636: -1- m4_pattern_allow([^HAVE_SNF_API$])
m4trace:configure.ac:1636: -1- AH_OUTPUT([HAVE_SNF_API], [/* define if you have the Myricom SNF API */
@%:@undef HAVE_SNF_API])
m4trace:configure.ac:1695: -1- AH_OUTPUT([HAVE_GETSPNAM], [/* Define to 1 if you have the `getspnam\' function. */
@%:@undef HAVE_GETSPNAM])
m4trace:configure.ac:1695: -1- AC_DEFINE_TRACE_LITERAL([HAVE_GETSPNAM])
m4trace:configure.ac:1695: -1- m4_pattern_allow([^HAVE_GETSPNAM$])
m4trace:configure.ac:1700: -1- AH_OUTPUT([HAVE_VSYSLOG], [/* Define to 1 if you have the `vsyslog\' function. */
@%:@undef HAVE_VSYSLOG])
m4trace:configure.ac:1700: -1- AC_DEFINE_TRACE_LITERAL([HAVE_VSYSLOG])
m4trace:configure.ac:1700: -1- m4_pattern_allow([^HAVE_VSYSLOG$])
m4trace:configure.ac:1705: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRUCT_MSGHDR_MSG_CONTROL])
m4trace:configure.ac:1705: -1- m4_pattern_allow([^HAVE_STRUCT_MSGHDR_MSG_CONTROL$])
m4trace:configure.ac:1705: -1- AH_OUTPUT([HAVE_STRUCT_MSGHDR_MSG_CONTROL], [/* Define to 1 if `msg_control\' is a member of `struct msghdr\'. */
@%:@undef HAVE_STRUCT_MSGHDR_MSG_CONTROL])
m4trace:configure.ac:1710: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRUCT_MSGHDR_MSG_FLAGS])
m4trace:configure.ac:1710: -1- m4_pattern_allow([^HAVE_STRUCT_MSGHDR_MSG_FLAGS$])
m4trace:configure.ac:1710: -1- AH_OUTPUT([HAVE_STRUCT_MSGHDR_MSG_FLAGS], [/* Define to 1 if `msg_flags\' is a member of `struct msghdr\'. */
@%:@undef HAVE_STRUCT_MSGHDR_MSG_FLAGS])
m4trace:configure.ac:1742: -1- AC_SUBST([OPENSSL_CFLAGS])
m4trace:configure.ac:1742: -1- AC_SUBST_TRACE([OPENSSL_CFLAGS])
m4trace:configure.ac:1742: -1- m4_pattern_allow([^OPENSSL_CFLAGS$])
m4trace:configure.ac:1742: -1- AC_SUBST([OPENSSL_LIBS])
m4trace:configure.ac:1742: -1- AC_SUBST_TRACE([OPENSSL_LIBS])
m4trace:configure.ac:1742: -1- m4_pattern_allow([^OPENSSL_LIBS$])
m4trace:configure.ac:1742: -1- AC_SUBST([OPENSSL_LIBS_STATIC])
m4trace:configure.ac:1742: -1- AC_SUBST_TRACE([OPENSSL_LIBS_STATIC])
m4trace:configure.ac:1742: -1- m4_pattern_allow([^OPENSSL_LIBS_STATIC$])
m4trace:configure.ac:1862: -1- AC_DEFINE_TRACE_LITERAL([HAVE_OPENSSL])
m4trace:configure.ac:1862: -1- m4_pattern_allow([^HAVE_OPENSSL$])
m4trace:configure.ac:1862: -1- AH_OUTPUT([HAVE_OPENSSL], [/* Use OpenSSL */
@%:@undef HAVE_OPENSSL])
m4trace:configure.ac:1873: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_REMOTE])
m4trace:configure.ac:1873: -1- m4_pattern_allow([^ENABLE_REMOTE$])
m4trace:configure.ac:1873: -1- AH_OUTPUT([ENABLE_REMOTE], [/* Define to 1 if remote packet capture is to be supported */
@%:@undef ENABLE_REMOTE])
m4trace:configure.ac:1887: -1- AC_DEFINE_TRACE_LITERAL([BDEBUG])
m4trace:configure.ac:1887: -1- m4_pattern_allow([^BDEBUG$])
m4trace:configure.ac:1887: -1- AH_OUTPUT([BDEBUG], [/* Enable optimizer debugging */
@%:@undef BDEBUG])
m4trace:configure.ac:1895: -1- AC_DEFINE_TRACE_LITERAL([YYDEBUG])
m4trace:configure.ac:1895: -1- m4_pattern_allow([^YYDEBUG$])
m4trace:configure.ac:1895: -1- AH_OUTPUT([YYDEBUG], [/* Enable parser debugging */
@%:@undef YYDEBUG])
m4trace:configure.ac:1902: -1- AC_SUBST([LEX])
m4trace:configure.ac:1902: -1- AC_SUBST_TRACE([LEX])
m4trace:configure.ac:1902: -1- m4_pattern_allow([^LEX$])
m4trace:configure.ac:1902: -1- AC_SUBST([LEX_OUTPUT_ROOT], [$ac_cv_prog_lex_root])
m4trace:configure.ac:1902: -1- AC_SUBST_TRACE([LEX_OUTPUT_ROOT])
m4trace:configure.ac:1902: -1- m4_pattern_allow([^LEX_OUTPUT_ROOT$])
m4trace:configure.ac:1902: -1- AC_SUBST([LEXLIB])
m4trace:configure.ac:1902: -1- AC_SUBST_TRACE([LEXLIB])
m4trace:configure.ac:1902: -1- m4_pattern_allow([^LEXLIB$])
m4trace:configure.ac:1902: -1- AC_DEFINE_TRACE_LITERAL([YYTEXT_POINTER])
m4trace:configure.ac:1902: -1- m4_pattern_allow([^YYTEXT_POINTER$])
m4trace:configure.ac:1902: -1- AH_OUTPUT([YYTEXT_POINTER], [/* Define to 1 if `lex\' declares `yytext\' as a `char *\' by default, not a
   `char@<:@@:>@\'. */
@%:@undef YYTEXT_POINTER])
m4trace:configure.ac:1940: -1- AC_SUBST([BISON_BYACC])
m4trace:configure.ac:1940: -1- AC_SUBST_TRACE([BISON_BYACC])
m4trace:configure.ac:1940: -1- m4_pattern_allow([^BISON_BYACC$])
m4trace:configure.ac:1962: -1- AC_SUBST([BISON_BYACC])
m4trace:configure.ac:1962: -1- AC_SUBST_TRACE([BISON_BYACC])
m4trace:configure.ac:1962: -1- m4_pattern_allow([^BISON_BYACC$])
m4trace:configure.ac:2002: -1- AC_SUBST([BISON_BYACC])
m4trace:configure.ac:2002: -1- AC_SUBST_TRACE([BISON_BYACC])
m4trace:configure.ac:2002: -1- m4_pattern_allow([^BISON_BYACC$])
m4trace:configure.ac:2003: -1- AC_SUBST([REENTRANT_PARSER])
m4trace:configure.ac:2003: -1- AC_SUBST_TRACE([REENTRANT_PARSER])
m4trace:configure.ac:2003: -1- m4_pattern_allow([^REENTRANT_PARSER$])
m4trace:configure.ac:2296: -1- AC_DEFINE_TRACE_LITERAL([HAVE_HPUX10_20_OR_LATER])
m4trace:configure.ac:2296: -1- m4_pattern_allow([^HAVE_HPUX10_20_OR_LATER$])
m4trace:configure.ac:2296: -1- AH_OUTPUT([HAVE_HPUX10_20_OR_LATER], [/* on HP-UX 10.20 or later */
@%:@undef HAVE_HPUX10_20_OR_LATER])
m4trace:configure.ac:2321: -1- AC_DEFINE_TRACE_LITERAL([HAVE_SOLARIS])
m4trace:configure.ac:2321: -1- m4_pattern_allow([^HAVE_SOLARIS$])
m4trace:configure.ac:2321: -1- AH_OUTPUT([HAVE_SOLARIS], [/* On Solaris */
@%:@undef HAVE_SOLARIS])
m4trace:configure.ac:2351: -1- AC_SUBST([V_LIB_CCOPT_FAT])
m4trace:configure.ac:2351: -1- AC_SUBST_TRACE([V_LIB_CCOPT_FAT])
m4trace:configure.ac:2351: -1- m4_pattern_allow([^V_LIB_CCOPT_FAT$])
m4trace:configure.ac:2352: -1- AC_SUBST([V_LIB_LDFLAGS_FAT])
m4trace:configure.ac:2352: -1- AC_SUBST_TRACE([V_LIB_LDFLAGS_FAT])
m4trace:configure.ac:2352: -1- m4_pattern_allow([^V_LIB_LDFLAGS_FAT$])
m4trace:configure.ac:2353: -1- AC_SUBST([V_PROG_CCOPT_FAT])
m4trace:configure.ac:2353: -1- AC_SUBST_TRACE([V_PROG_CCOPT_FAT])
m4trace:configure.ac:2353: -1- m4_pattern_allow([^V_PROG_CCOPT_FAT$])
m4trace:configure.ac:2354: -1- AC_SUBST([V_PROG_LDFLAGS_FAT])
m4trace:configure.ac:2354: -1- AC_SUBST_TRACE([V_PROG_LDFLAGS_FAT])
m4trace:configure.ac:2354: -1- m4_pattern_allow([^V_PROG_LDFLAGS_FAT$])
m4trace:configure.ac:2355: -1- AC_SUBST([DYEXT])
m4trace:configure.ac:2355: -1- AC_SUBST_TRACE([DYEXT])
m4trace:configure.ac:2355: -1- m4_pattern_allow([^DYEXT$])
m4trace:configure.ac:2356: -1- AC_SUBST([MAN_DEVICES])
m4trace:configure.ac:2356: -1- AC_SUBST_TRACE([MAN_DEVICES])
m4trace:configure.ac:2356: -1- m4_pattern_allow([^MAN_DEVICES$])
m4trace:configure.ac:2357: -1- AC_SUBST([MAN_FILE_FORMATS])
m4trace:configure.ac:2357: -1- AC_SUBST_TRACE([MAN_FILE_FORMATS])
m4trace:configure.ac:2357: -1- m4_pattern_allow([^MAN_FILE_FORMATS$])
m4trace:configure.ac:2358: -1- AC_SUBST([MAN_MISC_INFO])
m4trace:configure.ac:2358: -1- AC_SUBST_TRACE([MAN_MISC_INFO])
m4trace:configure.ac:2358: -1- m4_pattern_allow([^MAN_MISC_INFO$])
m4trace:configure.ac:2359: -1- AC_SUBST([MAN_ADMIN_COMMANDS])
m4trace:configure.ac:2359: -1- AC_SUBST_TRACE([MAN_ADMIN_COMMANDS])
m4trace:configure.ac:2359: -1- m4_pattern_allow([^MAN_ADMIN_COMMANDS$])
m4trace:configure.ac:2365: -1- AC_SUBST([RANLIB])
m4trace:configure.ac:2365: -1- AC_SUBST_TRACE([RANLIB])
m4trace:configure.ac:2365: -1- m4_pattern_allow([^RANLIB$])
m4trace:configure.ac:2366: -1- AC_SUBST([AR])
m4trace:configure.ac:2366: -1- AC_SUBST_TRACE([AR])
m4trace:configure.ac:2366: -1- m4_pattern_allow([^AR$])
m4trace:configure.ac:2368: -1- AC_SUBST([LN_S], [$as_ln_s])
m4trace:configure.ac:2368: -1- AC_SUBST_TRACE([LN_S])
m4trace:configure.ac:2368: -1- m4_pattern_allow([^LN_S$])
m4trace:configure.ac:2369: -1- AC_SUBST([LN_S])
m4trace:configure.ac:2369: -1- AC_SUBST_TRACE([LN_S])
m4trace:configure.ac:2369: -1- m4_pattern_allow([^LN_S$])
m4trace:configure.ac:2371: -1- AC_SUBST([DEPENDENCY_CFLAG])
m4trace:configure.ac:2371: -1- AC_SUBST_TRACE([DEPENDENCY_CFLAG])
m4trace:configure.ac:2371: -1- m4_pattern_allow([^DEPENDENCY_CFLAG$])
m4trace:configure.ac:2371: -1- AC_SUBST([MKDEP])
m4trace:configure.ac:2371: -1- AC_SUBST_TRACE([MKDEP])
m4trace:configure.ac:2371: -1- m4_pattern_allow([^MKDEP$])
m4trace:configure.ac:2371: -1- AC_DEFINE_TRACE_LITERAL([HAVE_OS_PROTO_H])
m4trace:configure.ac:2371: -1- m4_pattern_allow([^HAVE_OS_PROTO_H$])
m4trace:configure.ac:2371: -1- AH_OUTPUT([HAVE_OS_PROTO_H], [/* if there\'s an os-proto.h for this platform, to use additional prototypes */
@%:@undef HAVE_OS_PROTO_H])
m4trace:configure.ac:2376: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRUCT_SOCKADDR_SA_LEN])
m4trace:configure.ac:2376: -1- m4_pattern_allow([^HAVE_STRUCT_SOCKADDR_SA_LEN$])
m4trace:configure.ac:2376: -1- AH_OUTPUT([HAVE_STRUCT_SOCKADDR_SA_LEN], [/* Define to 1 if `sa_len\' is a member of `struct sockaddr\'. */
@%:@undef HAVE_STRUCT_SOCKADDR_SA_LEN])
m4trace:configure.ac:2400: -1- AC_DEFINE_TRACE_LITERAL([PCAP_SUPPORT_LINUX_USBMON])
m4trace:configure.ac:2400: -1- m4_pattern_allow([^PCAP_SUPPORT_LINUX_USBMON$])
m4trace:configure.ac:2400: -1- AH_OUTPUT([PCAP_SUPPORT_LINUX_USBMON], [/* target host supports Linux usbmon for USB sniffing */
@%:@undef PCAP_SUPPORT_LINUX_USBMON])
m4trace:configure.ac:2418: -1- AH_OUTPUT([HAVE_LINUX_COMPILER_H], [/* Define to 1 if you have the <linux/compiler.h> header file. */
@%:@undef HAVE_LINUX_COMPILER_H])
m4trace:configure.ac:2418: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LINUX_COMPILER_H])
m4trace:configure.ac:2418: -1- m4_pattern_allow([^HAVE_LINUX_COMPILER_H$])
m4trace:configure.ac:2423: -1- AH_OUTPUT([HAVE_LINUX_USBDEVICE_FS_H], [/* Define to 1 if you have the <linux/usbdevice_fs.h> header file. */
@%:@undef HAVE_LINUX_USBDEVICE_FS_H])
m4trace:configure.ac:2423: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LINUX_USBDEVICE_FS_H])
m4trace:configure.ac:2423: -1- m4_pattern_allow([^HAVE_LINUX_USBDEVICE_FS_H$])
m4trace:configure.ac:2425: -1- AH_OUTPUT([HAVE_LINUX_USBDEVICE_FS_H], [/* Define to 1 if you have the <linux/usbdevice_fs.h> header file. */
@%:@undef HAVE_LINUX_USBDEVICE_FS_H])
m4trace:configure.ac:2425: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LINUX_USBDEVICE_FS_H])
m4trace:configure.ac:2425: -1- m4_pattern_allow([^HAVE_LINUX_USBDEVICE_FS_H$])
m4trace:configure.ac:2434: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRUCT_USBDEVFS_CTRLTRANSFER_BREQUESTTYPE])
m4trace:configure.ac:2434: -1- m4_pattern_allow([^HAVE_STRUCT_USBDEVFS_CTRLTRANSFER_BREQUESTTYPE$])
m4trace:configure.ac:2434: -1- AH_OUTPUT([HAVE_STRUCT_USBDEVFS_CTRLTRANSFER_BREQUESTTYPE], [/* Define to 1 if `bRequestType\' is a member of `struct
   usbdevfs_ctrltransfer\'. */
@%:@undef HAVE_STRUCT_USBDEVFS_CTRLTRANSFER_BREQUESTTYPE])
m4trace:configure.ac:2473: -1- AC_DEFINE_TRACE_LITERAL([PCAP_SUPPORT_NETFILTER])
m4trace:configure.ac:2473: -1- m4_pattern_allow([^PCAP_SUPPORT_NETFILTER$])
m4trace:configure.ac:2473: -1- AH_OUTPUT([PCAP_SUPPORT_NETFILTER], [/* target host supports netfilter sniffing */
@%:@undef PCAP_SUPPORT_NETFILTER])
m4trace:configure.ac:2480: -1- AC_SUBST([PCAP_SUPPORT_LINUX_USBMON])
m4trace:configure.ac:2480: -1- AC_SUBST_TRACE([PCAP_SUPPORT_LINUX_USBMON])
m4trace:configure.ac:2480: -1- m4_pattern_allow([^PCAP_SUPPORT_LINUX_USBMON$])
m4trace:configure.ac:2481: -1- AC_SUBST([PCAP_SUPPORT_NETFILTER])
m4trace:configure.ac:2481: -1- AC_SUBST_TRACE([PCAP_SUPPORT_NETFILTER])
m4trace:configure.ac:2481: -1- m4_pattern_allow([^PCAP_SUPPORT_NETFILTER$])
m4trace:configure.ac:2516: -1- AC_DEFINE_TRACE_LITERAL([PCAP_SUPPORT_NETMAP])
m4trace:configure.ac:2516: -1- m4_pattern_allow([^PCAP_SUPPORT_NETMAP$])
m4trace:configure.ac:2516: -1- AH_OUTPUT([PCAP_SUPPORT_NETMAP], [/* target host supports netmap */
@%:@undef PCAP_SUPPORT_NETMAP])
m4trace:configure.ac:2520: -1- AC_SUBST([PCAP_SUPPORT_NETMAP])
m4trace:configure.ac:2520: -1- AC_SUBST_TRACE([PCAP_SUPPORT_NETMAP])
m4trace:configure.ac:2520: -1- m4_pattern_allow([^PCAP_SUPPORT_NETMAP$])
m4trace:configure.ac:2607: -1- AC_SUBST([DPDK_CFLAGS])
m4trace:configure.ac:2607: -1- AC_SUBST_TRACE([DPDK_CFLAGS])
m4trace:configure.ac:2607: -1- m4_pattern_allow([^DPDK_CFLAGS$])
m4trace:configure.ac:2607: -1- AC_SUBST([DPDK_LIBS])
m4trace:configure.ac:2607: -1- AC_SUBST_TRACE([DPDK_LIBS])
m4trace:configure.ac:2607: -1- m4_pattern_allow([^DPDK_LIBS$])
m4trace:configure.ac:2607: -1- AC_SUBST([DPDK_LIBS_STATIC])
m4trace:configure.ac:2607: -1- AC_SUBST_TRACE([DPDK_LIBS_STATIC])
m4trace:configure.ac:2607: -1- m4_pattern_allow([^DPDK_LIBS_STATIC$])
m4trace:configure.ac:2642: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRUCT_RTE_ETHER_ADDR])
m4trace:configure.ac:2642: -1- m4_pattern_allow([^HAVE_STRUCT_RTE_ETHER_ADDR$])
m4trace:configure.ac:2642: -1- AH_OUTPUT([HAVE_STRUCT_RTE_ETHER_ADDR], [/* Define to 1 if the system has the type `struct rte_ether_addr\'. */
@%:@undef HAVE_STRUCT_RTE_ETHER_ADDR])
m4trace:configure.ac:2655: -1- AC_DEFINE_TRACE_LITERAL([PCAP_SUPPORT_DPDK])
m4trace:configure.ac:2655: -1- m4_pattern_allow([^PCAP_SUPPORT_DPDK$])
m4trace:configure.ac:2655: -1- AH_OUTPUT([PCAP_SUPPORT_DPDK], [/* target host supports DPDK */
@%:@undef PCAP_SUPPORT_DPDK])
m4trace:configure.ac:2751: -1- AC_SUBST([PCAP_SUPPORT_DPDK])
m4trace:configure.ac:2751: -1- AC_SUBST_TRACE([PCAP_SUPPORT_DPDK])
m4trace:configure.ac:2751: -1- m4_pattern_allow([^PCAP_SUPPORT_DPDK$])
m4trace:configure.ac:2768: -1- AC_DEFINE_TRACE_LITERAL([PCAP_SUPPORT_BT])
m4trace:configure.ac:2768: -1- m4_pattern_allow([^PCAP_SUPPORT_BT$])
m4trace:configure.ac:2768: -1- AH_OUTPUT([PCAP_SUPPORT_BT], [/* target host supports Bluetooth sniffing */
@%:@undef PCAP_SUPPORT_BT])
m4trace:configure.ac:2768: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRUCT_SOCKADDR_HCI_HCI_CHANNEL])
m4trace:configure.ac:2768: -1- m4_pattern_allow([^HAVE_STRUCT_SOCKADDR_HCI_HCI_CHANNEL$])
m4trace:configure.ac:2768: -1- AH_OUTPUT([HAVE_STRUCT_SOCKADDR_HCI_HCI_CHANNEL], [/* Define to 1 if `hci_channel\' is a member of `struct sockaddr_hci\'. */
@%:@undef HAVE_STRUCT_SOCKADDR_HCI_HCI_CHANNEL])
m4trace:configure.ac:2768: -2- AC_DEFINE_TRACE_LITERAL([PCAP_SUPPORT_BT_MONITOR])
m4trace:configure.ac:2768: -2- m4_pattern_allow([^PCAP_SUPPORT_BT_MONITOR$])
m4trace:configure.ac:2768: -2- AH_OUTPUT([PCAP_SUPPORT_BT_MONITOR], [/* target host supports Bluetooth Monitor */
@%:@undef PCAP_SUPPORT_BT_MONITOR])
m4trace:configure.ac:2833: -1- AC_SUBST([PCAP_SUPPORT_BT])
m4trace:configure.ac:2833: -1- AC_SUBST_TRACE([PCAP_SUPPORT_BT])
m4trace:configure.ac:2833: -1- m4_pattern_allow([^PCAP_SUPPORT_BT$])
m4trace:configure.ac:2880: -1- AC_SUBST([DBUS_CFLAGS])
m4trace:configure.ac:2880: -1- AC_SUBST_TRACE([DBUS_CFLAGS])
m4trace:configure.ac:2880: -1- m4_pattern_allow([^DBUS_CFLAGS$])
m4trace:configure.ac:2880: -1- AC_SUBST([DBUS_LIBS])
m4trace:configure.ac:2880: -1- AC_SUBST_TRACE([DBUS_LIBS])
m4trace:configure.ac:2880: -1- m4_pattern_allow([^DBUS_LIBS$])
m4trace:configure.ac:2880: -1- AC_SUBST([DBUS_LIBS_STATIC])
m4trace:configure.ac:2880: -1- AC_SUBST_TRACE([DBUS_LIBS_STATIC])
m4trace:configure.ac:2880: -1- m4_pattern_allow([^DBUS_LIBS_STATIC$])
m4trace:configure.ac:2880: -1- AC_DEFINE_TRACE_LITERAL([PCAP_SUPPORT_DBUS])
m4trace:configure.ac:2880: -1- m4_pattern_allow([^PCAP_SUPPORT_DBUS$])
m4trace:configure.ac:2880: -1- AH_OUTPUT([PCAP_SUPPORT_DBUS], [/* support D-Bus sniffing */
@%:@undef PCAP_SUPPORT_DBUS])
m4trace:configure.ac:2916: -1- AC_SUBST([PCAP_SUPPORT_DBUS])
m4trace:configure.ac:2916: -1- AC_SUBST_TRACE([PCAP_SUPPORT_DBUS])
m4trace:configure.ac:2916: -1- m4_pattern_allow([^PCAP_SUPPORT_DBUS$])
m4trace:configure.ac:2943: -1- AC_SUBST([LIBIBVERBS_CFLAGS])
m4trace:configure.ac:2943: -1- AC_SUBST_TRACE([LIBIBVERBS_CFLAGS])
m4trace:configure.ac:2943: -1- m4_pattern_allow([^LIBIBVERBS_CFLAGS$])
m4trace:configure.ac:2943: -1- AC_SUBST([LIBIBVERBS_LIBS])
m4trace:configure.ac:2943: -1- AC_SUBST_TRACE([LIBIBVERBS_LIBS])
m4trace:configure.ac:2943: -1- m4_pattern_allow([^LIBIBVERBS_LIBS$])
m4trace:configure.ac:2943: -1- AC_SUBST([LIBIBVERBS_LIBS_STATIC])
m4trace:configure.ac:2943: -1- AC_SUBST_TRACE([LIBIBVERBS_LIBS_STATIC])
m4trace:configure.ac:2943: -1- m4_pattern_allow([^LIBIBVERBS_LIBS_STATIC$])
m4trace:configure.ac:3005: -1- AC_DEFINE_TRACE_LITERAL([PCAP_SUPPORT_RDMASNIFF])
m4trace:configure.ac:3005: -1- m4_pattern_allow([^PCAP_SUPPORT_RDMASNIFF$])
m4trace:configure.ac:3005: -1- AH_OUTPUT([PCAP_SUPPORT_RDMASNIFF], [/* target host supports RDMA sniffing */
@%:@undef PCAP_SUPPORT_RDMASNIFF])
m4trace:configure.ac:3013: -1- AC_SUBST([PCAP_SUPPORT_RDMASNIFF])
m4trace:configure.ac:3013: -1- AC_SUBST_TRACE([PCAP_SUPPORT_RDMASNIFF])
m4trace:configure.ac:3013: -1- m4_pattern_allow([^PCAP_SUPPORT_RDMASNIFF$])
m4trace:configure.ac:3065: -1- AC_REQUIRE_AUX_FILE([install-sh])
m4trace:configure.ac:3065: -1- AC_SUBST([INSTALL_PROGRAM])
m4trace:configure.ac:3065: -1- AC_SUBST_TRACE([INSTALL_PROGRAM])
m4trace:configure.ac:3065: -1- m4_pattern_allow([^INSTALL_PROGRAM$])
m4trace:configure.ac:3065: -1- AC_SUBST([INSTALL_SCRIPT])
m4trace:configure.ac:3065: -1- AC_SUBST_TRACE([INSTALL_SCRIPT])
m4trace:configure.ac:3065: -1- m4_pattern_allow([^INSTALL_SCRIPT$])
m4trace:configure.ac:3065: -1- AC_SUBST([INSTALL_DATA])
m4trace:configure.ac:3065: -1- AC_SUBST_TRACE([INSTALL_DATA])
m4trace:configure.ac:3065: -1- m4_pattern_allow([^INSTALL_DATA$])
m4trace:configure.ac:3067: -1- AC_CONFIG_HEADERS([config.h])
m4trace:configure.ac:3069: -1- AC_SUBST([V_SHLIB_CCOPT])
m4trace:configure.ac:3069: -1- AC_SUBST_TRACE([V_SHLIB_CCOPT])
m4trace:configure.ac:3069: -1- m4_pattern_allow([^V_SHLIB_CCOPT$])
m4trace:configure.ac:3070: -1- AC_SUBST([V_SHLIB_CMD])
m4trace:configure.ac:3070: -1- AC_SUBST_TRACE([V_SHLIB_CMD])
m4trace:configure.ac:3070: -1- m4_pattern_allow([^V_SHLIB_CMD$])
m4trace:configure.ac:3071: -1- AC_SUBST([V_SHLIB_OPT])
m4trace:configure.ac:3071: -1- AC_SUBST_TRACE([V_SHLIB_OPT])
m4trace:configure.ac:3071: -1- m4_pattern_allow([^V_SHLIB_OPT$])
m4trace:configure.ac:3072: -1- AC_SUBST([V_SONAME_OPT])
m4trace:configure.ac:3072: -1- AC_SUBST_TRACE([V_SONAME_OPT])
m4trace:configure.ac:3072: -1- m4_pattern_allow([^V_SONAME_OPT$])
m4trace:configure.ac:3073: -1- AC_SUBST([RPATH])
m4trace:configure.ac:3073: -1- AC_SUBST_TRACE([RPATH])
m4trace:configure.ac:3073: -1- m4_pattern_allow([^RPATH$])
m4trace:configure.ac:3074: -1- AC_SUBST([ADDLOBJS])
m4trace:configure.ac:3074: -1- AC_SUBST_TRACE([ADDLOBJS])
m4trace:configure.ac:3074: -1- m4_pattern_allow([^ADDLOBJS$])
m4trace:configure.ac:3075: -1- AC_SUBST([ADDLARCHIVEOBJS])
m4trace:configure.ac:3075: -1- AC_SUBST_TRACE([ADDLARCHIVEOBJS])
m4trace:configure.ac:3075: -1- m4_pattern_allow([^ADDLARCHIVEOBJS$])
m4trace:configure.ac:3076: -1- AC_SUBST([PLATFORM_C_SRC])
m4trace:configure.ac:3076: -1- AC_SUBST_TRACE([PLATFORM_C_SRC])
m4trace:configure.ac:3076: -1- m4_pattern_allow([^PLATFORM_C_SRC$])
m4trace:configure.ac:3077: -1- AC_SUBST([MODULE_C_SRC])
m4trace:configure.ac:3077: -1- AC_SUBST_TRACE([MODULE_C_SRC])
m4trace:configure.ac:3077: -1- m4_pattern_allow([^MODULE_C_SRC$])
m4trace:configure.ac:3078: -1- AC_SUBST([REMOTE_C_SRC])
m4trace:configure.ac:3078: -1- AC_SUBST_TRACE([REMOTE_C_SRC])
m4trace:configure.ac:3078: -1- m4_pattern_allow([^REMOTE_C_SRC$])
m4trace:configure.ac:3079: -1- AC_SUBST([PTHREAD_LIBS])
m4trace:configure.ac:3079: -1- AC_SUBST_TRACE([PTHREAD_LIBS])
m4trace:configure.ac:3079: -1- m4_pattern_allow([^PTHREAD_LIBS$])
m4trace:configure.ac:3080: -1- AC_SUBST([BUILD_RPCAPD])
m4trace:configure.ac:3080: -1- AC_SUBST_TRACE([BUILD_RPCAPD])
m4trace:configure.ac:3080: -1- m4_pattern_allow([^BUILD_RPCAPD$])
m4trace:configure.ac:3081: -1- AC_SUBST([INSTALL_RPCAPD])
m4trace:configure.ac:3081: -1- AC_SUBST_TRACE([INSTALL_RPCAPD])
m4trace:configure.ac:3081: -1- m4_pattern_allow([^INSTALL_RPCAPD$])
m4trace:configure.ac:3082: -1- AC_SUBST([RPCAPD_LIBS])
m4trace:configure.ac:3082: -1- AC_SUBST_TRACE([RPCAPD_LIBS])
m4trace:configure.ac:3082: -1- m4_pattern_allow([^RPCAPD_LIBS$])
m4trace:configure.ac:3096: -1- AC_CONFIG_FILES([Makefile grammar.y pcap-filter.manmisc pcap-linktype.manmisc
	pcap-tstamp.manmisc cbpf-savefile.manfile pcap-savefile.manfile pcap.3pcap
	pcap_compile.3pcap pcap_datalink.3pcap pcap_dump_open.3pcap
	pcap_get_tstamp_precision.3pcap pcap_list_datalinks.3pcap
	pcap_list_tstamp_types.3pcap pcap_open_dead.3pcap
	pcap_open_offline.3pcap pcap_set_immediate_mode.3pcap
	pcap_set_tstamp_precision.3pcap pcap_set_tstamp_type.3pcap
	rpcapd/Makefile rpcapd/rpcapd.manadmin rpcapd/rpcapd-config.manfile
	testprogs/Makefile])
m4trace:configure.ac:3105: -1- AC_SUBST([LIB@&t@OBJS], [$ac_libobjs])
m4trace:configure.ac:3105: -1- AC_SUBST_TRACE([LIB@&t@OBJS])
m4trace:configure.ac:3105: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.ac:3105: -1- AC_SUBST([LTLIBOBJS], [$ac_ltlibobjs])
m4trace:configure.ac:3105: -1- AC_SUBST_TRACE([LTLIBOBJS])
m4trace:configure.ac:3105: -1- m4_pattern_allow([^LTLIBOBJS$])
m4trace:configure.ac:3105: -1- AC_SUBST_TRACE([top_builddir])
m4trace:configure.ac:3105: -1- AC_SUBST_TRACE([top_build_prefix])
m4trace:configure.ac:3105: -1- AC_SUBST_TRACE([srcdir])
m4trace:configure.ac:3105: -1- AC_SUBST_TRACE([abs_srcdir])
m4trace:configure.ac:3105: -1- AC_SUBST_TRACE([top_srcdir])
m4trace:configure.ac:3105: -1- AC_SUBST_TRACE([abs_top_srcdir])
m4trace:configure.ac:3105: -1- AC_SUBST_TRACE([builddir])
m4trace:configure.ac:3105: -1- AC_SUBST_TRACE([abs_builddir])
m4trace:configure.ac:3105: -1- AC_SUBST_TRACE([abs_top_builddir])
m4trace:configure.ac:3105: -1- AC_SUBST_TRACE([INSTALL])
