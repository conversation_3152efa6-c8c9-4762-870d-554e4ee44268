#
# Auto-regenerate configure script or Makefile when things change.
# From autoconf.info .  Works best with GNU Make.
#
${srcdir}/configure: configure.ac aclocal.m4
	cd ${srcdir} && autoconf

# autoheader might not change config.h.in, so touch a stamp file.
${srcdir}/config.h.in: ${srcdir}/stamp-h.in
${srcdir}/stamp-h.in: configure.ac aclocal.m4
	cd ${srcdir} && autoheader
	echo timestamp > ${srcdir}/stamp-h.in

config.h: stamp-h
stamp-h: ${srcdir}/config.h.in config.status
	./config.status

Makefile: Makefile.in config.status
	./config.status

config.status: ${srcdir}/configure
	./config.status --recheck
