    1  2019-10-23 20:58:40.639715 IP (tos 0x0, ttl 64, id 42311, offset 0, flags [none], proto UDP (17), length 57)
    *********.46225 > *********.53: [bad udp cksum 0xcd13 -> 0xc573!] 13784+ A? example.com. (29)
    2  2019-10-23 20:58:40.661836 IP (tos 0x0, ttl 48, id 43077, offset 0, flags [none], proto UDP (17), length 73)
    *********.53 > *********.46225: [udp sum ok] 13784*- q: A? example.com. 1/0/0 example.com. A ************* (45)
    3  2019-10-23 20:58:41.671700 IP (tos 0x0, ttl 64, id 42441, offset 0, flags [none], proto UDP (17), length 68)
    *********.46225 > *********.53: [bad udp cksum 0xcd1e -> 0x9191!] 47424+ [1au] A? example.com. ar: . OPT UDPsize=12345 DO (40)
    4  2019-10-23 20:58:41.693577 IP (tos 0x0, ttl 48, id 64719, offset 0, flags [none], proto UDP (17), length 255)
    *********.53 > *********.46225: [udp sum ok] 47424*- q: A? example.com. 2/0/1 example.com. A *************, example.com. RRSIG ar: . OPT UDPsize=4096 DO (227)
    5  2019-10-23 20:58:42.703534 IP (tos 0x0, ttl 64, id 42575, offset 0, flags [none], proto UDP (17), length 79)
    *********.46225 > *********.53: [bad udp cksum 0xcd29 -> 0x6dce!] 41739+ [1au] A? example.com. ar: . OPT UDPsize=4096 [ECS *********/24/0] (51)
    6  2019-10-23 20:58:42.725559 IP (tos 0x0, ttl 48, id 21779, offset 0, flags [none], proto UDP (17), length 95)
    *********.53 > *********.46225: [udp sum ok] 41739*- q: A? example.com. 1/0/1 example.com. A ************* ar: . OPT UDPsize=4096 [ECS *********/24/0] (67)
    7  2019-10-23 20:58:43.734693 IP (tos 0x0, ttl 64, id 42674, offset 0, flags [none], proto UDP (17), length 80)
    *********.46225 > *********.53: [bad udp cksum 0xcd2a -> 0xc240!] 18065+ [1au] A? example.com. ar: . OPT UDPsize=4096 [ECS *********/32/0] (52)
    8  2019-10-23 20:58:43.756707 IP (tos 0x0, ttl 48, id 43697, offset 0, flags [none], proto UDP (17), length 96)
    *********.53 > *********.46225: [udp sum ok] 18065*- q: A? example.com. 1/0/1 example.com. A ************* ar: . OPT UDPsize=4096 [ECS *********/32/0] (68)
    9  2019-10-23 20:58:44.766528 IP (tos 0x0, ttl 64, id 42890, offset 0, flags [none], proto UDP (17), length 89)
    *********.46225 > *********.53: [bad udp cksum 0xcd33 -> 0xc368!] 34237+ [1au] A? example.com. ar: . OPT UDPsize=4096 [ECS 2001:db8:85a3::8a2e:0:0/100/0] (61)
   10  2019-10-23 20:58:44.788502 IP (tos 0x0, ttl 48, id 65435, offset 0, flags [none], proto UDP (17), length 105)
    *********.53 > *********.46225: [udp sum ok] 34237*- q: A? example.com. 1/0/1 example.com. A ************* ar: . OPT UDPsize=4096 [ECS 2001:db8:85a3::8a2e:0:0/100/0] (77)
   11  2019-10-23 20:58:45.797638 IP (tos 0x0, ttl 64, id 43148, offset 0, flags [none], proto UDP (17), length 80)
    *********.46225 > *********.53: [bad udp cksum 0xcd2a -> 0x750a!] 30225+ [1au] A? example.com. ar: . OPT UDPsize=4096 [COOKIE a954d29208767b5c] (52)
   12  2019-10-23 20:58:45.819504 IP (tos 0x0, ttl 48, id 21512, offset 0, flags [none], proto UDP (17), length 112)
    *********.53 > *********.46225: [udp sum ok] 30225*- q: A? example.com. 1/0/1 example.com. A ************* ar: . OPT UDPsize=4096 [COOKIE a954d29208767b5c cf75f75b5db0bf0575c9f2d76c962883] (84)
   13  2019-10-23 20:58:46.829454 IP (tos 0x0, ttl 64, id 43211, offset 0, flags [none], proto UDP (17), length 74)
    *********.46225 > *********.53: [bad udp cksum 0xcd24 -> 0x971a!] 52688+ [1au] A? example.com. ar: . OPT UDPsize=4096 DO [DAU DSA-NSEC3-SHA1 RSASHA1-NSEC3-SHA1] (46)
   14  2019-10-23 20:58:46.851441 IP (tos 0x0, ttl 48, id 43346, offset 0, flags [none], proto UDP (17), length 255)
    *********.53 > *********.46225: [udp sum ok] 52688*- q: A? example.com. 2/0/1 example.com. A *************, example.com. RRSIG ar: . OPT UDPsize=4096 DO (227)
   15  2019-10-23 20:58:47.860858 IP (tos 0x0, ttl 64, id 43235, offset 0, flags [none], proto UDP (17), length 83)
    *********.46225 > *********.53: [bad udp cksum 0xcd2d -> 0x83fc!] 57808+ [1au] A? example.com. ar: . OPT UDPsize=4096 DO [DAU PRIVATEDNS,DHU SHA-256,N3U SHA-1] (55)
   16  2019-10-23 20:58:47.882669 IP (tos 0x0, ttl 48, id 64510, offset 0, flags [none], proto UDP (17), length 255)
    *********.53 > *********.46225: [udp sum ok] 57808*- q: A? example.com. 2/0/1 example.com. A *************, example.com. RRSIG ar: . OPT UDPsize=4096 DO (227)
   17  2019-10-23 20:58:48.892587 IP (tos 0x0, ttl 64, id 43380, offset 0, flags [none], proto UDP (17), length 89)
    *********.46225 > *********.53: [bad udp cksum 0xcd33 -> 0xaa4d!] 33054+ [1au] A? example.com. ar: . OPT UDPsize=4096 DO [CHAIN foo.example.com.] (61)
   18  2019-10-23 20:58:48.914406 IP (tos 0x0, ttl 48, id 20611, offset 0, flags [none], proto UDP (17), length 255)
    *********.53 > *********.46225: [udp sum ok] 33054*- q: A? example.com. 2/0/1 example.com. A *************, example.com. RRSIG ar: . OPT UDPsize=4096 DO (227)
   19  2019-10-23 20:58:49.924625 IP (tos 0x0, ttl 64, id 43587, offset 0, flags [none], proto UDP (17), length 74)
    *********.46225 > *********.53: [bad udp cksum 0xcd24 -> 0xae09!] 14353+ [1au] A? example.com. ar: . OPT UDPsize=4096 [KEEPALIVE 123.4 sec] (46)
   20  2019-10-23 20:58:49.946523 IP (tos 0x0, ttl 48, id 42366, offset 0, flags [none], proto UDP (17), length 84)
    *********.53 > *********.46225: [udp sum ok] 14353*- q: A? example.com. 1/0/1 example.com. A ************* ar: . OPT UDPsize=4096 (56)
   21  2019-10-23 20:58:50.956601 IP (tos 0x0, ttl 64, id 43603, offset 0, flags [none], proto UDP (17), length 74)
    *********.46225 > *********.53: [bad udp cksum 0xcd24 -> 0x8c36!] 17010+ [1au] A? example.com. ar: . OPT UDPsize=4096 DO [KEY-TAG 40000] (46)
   22  2019-10-23 20:58:50.978366 IP (tos 0x0, ttl 48, id 64034, offset 0, flags [none], proto UDP (17), length 255)
    *********.53 > *********.46225: [udp sum ok] 17010*- q: A? example.com. 2/0/1 example.com. A *************, example.com. RRSIG ar: . OPT UDPsize=4096 DO (227)
   23  2019-10-23 20:58:51.988387 IP (tos 0x0, ttl 64, id 43789, offset 0, flags [none], proto UDP (17), length 76)
    *********.46225 > *********.53: [bad udp cksum 0xcd26 -> 0xfc19!] 3894+ [1au] A? example.com. ar: . OPT UDPsize=4096 DO [KEY-TAG 30000 60000] (48)
   24  2019-10-23 20:58:52.010258 IP (tos 0x0, ttl 48, id 20169, offset 0, flags [none], proto UDP (17), length 255)
    *********.53 > *********.46225: [udp sum ok] 3894*- q: A? example.com. 2/0/1 example.com. A *************, example.com. RRSIG ar: . OPT UDPsize=4096 DO (227)
   25  2019-10-23 20:58:53.015716 IP (tos 0x0, ttl 64, id 43925, offset 0, flags [none], proto UDP (17), length 72)
    *********.46225 > *********.53: [bad udp cksum 0xcd22 -> 0xc9da!] 8476+ [1au] A? example.com. ar: . OPT UDPsize=4096 [EXPIRE] (44)
   26  2019-10-23 20:58:53.037529 IP (tos 0x0, ttl 48, id 42142, offset 0, flags [none], proto UDP (17), length 84)
    *********.53 > *********.46225: [udp sum ok] 8476*- q: A? example.com. 1/0/1 example.com. A ************* ar: . OPT UDPsize=4096 (56)
   27  2019-10-23 20:58:54.047412 IP (tos 0x0, ttl 64, id 44065, offset 0, flags [none], proto UDP (17), length 76)
    *********.46225 > *********.53: [bad udp cksum 0xcd26 -> 0x6656!] 3966+ [1au] A? example.com. ar: . OPT UDPsize=4096 [EXPIRE 1209600 sec] (48)
   28  2019-10-23 20:58:54.069358 IP (tos 0x0, ttl 48, id 64128, offset 0, flags [none], proto UDP (17), length 84)
    *********.53 > *********.46225: [udp sum ok] 3966*- q: A? example.com. 1/0/1 example.com. A ************* ar: . OPT UDPsize=4096 (56)
   29  2019-10-23 20:58:55.078435 IP (tos 0x0, ttl 64, id 44256, offset 0, flags [none], proto UDP (17), length 72)
    *********.46225 > *********.53: [bad udp cksum 0xcd22 -> 0x8328!] 26580+ [1au] A? example.com. ar: . OPT UDPsize=4096 [NSID] (44)
   30  2019-10-23 20:58:55.100224 IP (tos 0x0, ttl 48, id 18983, offset 0, flags [none], proto UDP (17), length 84)
    *********.53 > *********.46225: [udp sum ok] 26580*- q: A? example.com. 1/0/1 example.com. A ************* ar: . OPT UDPsize=4096 (56)
   31  2019-10-23 20:58:56.110237 IP (tos 0x0, ttl 64, id 44374, offset 0, flags [none], proto UDP (17), length 82)
    *********.46225 > *********.53: [bad udp cksum 0xcd2c -> 0x8c9c!] 2190+ [1au] A? example.com. ar: . OPT UDPsize=4096 [NSID 00112233445566778899] (54)
   32  2019-10-23 20:58:56.132070 IP (tos 0x0, ttl 48, id 39653, offset 0, flags [none], proto UDP (17), length 84)
    *********.53 > *********.46225: [udp sum ok] 2190*- q: A? example.com. 1/0/1 example.com. A ************* ar: . OPT UDPsize=4096 (56)
   33  2019-10-23 20:58:57.142215 IP (tos 0x0, ttl 64, id 44395, offset 0, flags [none], proto UDP (17), length 76)
    *********.46225 > *********.53: [bad udp cksum 0xcd26 -> 0x0d03!] 16386+ [1au] A? example.com. ar: . OPT UDPsize=4096 [Opt77 deadbeef] (48)
   34  2019-10-23 20:58:57.164050 IP (tos 0x0, ttl 48, id 60845, offset 0, flags [none], proto UDP (17), length 84)
    *********.53 > *********.46225: [udp sum ok] 16386*- q: A? example.com. 1/0/1 example.com. A ************* ar: . OPT UDPsize=4096 (56)
   35  2019-10-23 20:58:58.174193 IP (tos 0x0, ttl 64, id 44643, offset 0, flags [none], proto UDP (17), length 78)
    *********.46225 > *********.53: [bad udp cksum 0xcd28 -> 0xd1f6!] 6373+ [1au] A? example.com. ar: . OPT UDPsize=4096 [PADDING (6)] (50)
   36  2019-10-23 20:58:58.195911 IP (tos 0x0, ttl 48, id 16078, offset 0, flags [none], proto UDP (17), length 84)
    *********.53 > *********.46225: [udp sum ok] 6373*- q: A? example.com. 1/0/1 example.com. A ************* ar: . OPT UDPsize=4096 (56)
   37  2019-10-23 20:58:59.206181 IP (tos 0x0, ttl 64, id 44899, offset 0, flags [none], proto UDP (17), length 108)
    *********.46225 > *********.53: [bad udp cksum 0xcd46 -> 0x2f3b!] 29267+ [1au] A? example.com. ar: . OPT UDPsize=4096 [NSID 0123456789abcdef,PADDING (12),COOKIE aaaaaaaaaaaaaaaa] (80)
   38  2019-10-23 20:58:59.228465 IP (tos 0x0, ttl 48, id 37094, offset 0, flags [none], proto UDP (17), length 112)
    *********.53 > *********.46225: [udp sum ok] 29267*- q: A? example.com. 1/0/1 example.com. A ************* ar: . OPT UDPsize=4096 [COOKIE aaaaaaaaaaaaaaaa b5a8ca325db0bf132274faa81cef9aa4] (84)
   39  2019-10-23 20:59:00.238274 IP (tos 0x0, ttl 64, id 45092, offset 0, flags [none], proto UDP (17), length 90)
    *********.46225 > *********.53: [bad udp cksum 0xcd34 -> 0x883d!] 59326+ [1au] A? example.com. ar: . OPT UDPsize=4096 [ECS *********/16/0,COOKIE aaaaaaaaaaaaaaaa] (62)
   40  2019-10-23 20:59:00.260209 IP (tos 0x0, ttl 48, id 58611, offset 0, flags [none], proto UDP (17), length 122)
    *********.53 > *********.46225: [udp sum ok] 59326*- q: A? example.com. 1/0/1 example.com. A ************* ar: . OPT UDPsize=4096 [COOKIE aaaaaaaaaaaaaaaa 70a50ca25db0bf14d523a3f77957f788,ECS *********/16/0] (94)
   41  2019-10-23 20:59:01.269390 IP (tos 0x0, ttl 64, id 45316, offset 0, flags [none], proto UDP (17), length 100)
    *********.46225 > *********.53: [bad udp cksum 0xcd3e -> 0x4731!] 17122+ [1au] A? example.com. ar: . OPT UDPsize=4096 DO [CHAIN com.,DHU GOST_R_34.11-94,PADDING (4),NSID aabbccddeeff] (72)
   42  2019-10-23 20:59:01.291167 IP (tos 0x0, ttl 48, id 14192, offset 0, flags [none], proto UDP (17), length 255)
    *********.53 > *********.46225: [udp sum ok] 17122*- q: A? example.com. 2/0/1 example.com. A *************, example.com. RRSIG ar: . OPT UDPsize=4096 DO (227)
