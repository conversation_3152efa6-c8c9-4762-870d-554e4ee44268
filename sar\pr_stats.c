/*
 * pr_stats.c: Functions used by sar to display statistics
 * (C) 1999-2014 by <PERSON><PERSON><PERSON> (sysstat <at> orange.fr)
 *
 ***************************************************************************
 * This program is free software; you can redistribute it and/or modify it *
 * under the terms of the GNU General Public License as published  by  the *
 * Free Software Foundation; either version 2 of the License, or (at  your *
 * option) any later version.                                              *
 *                                                                         *
 * This program is distributed in the hope that it  will  be  useful,  but *
 * WITHOUT ANY WARRANTY; without the implied warranty  of  ME<PERSON>HANT<PERSON><PERSON>ITY *
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License *
 * for more details.                                                       *
 *                                                                         *
 * You should have received a copy of the GNU General Public License along *
 * with this program; if not, write to the Free Software Foundation, Inc., *
 * 59 Temple Place, Suite 330, Boston, MA 02111-1307 USA                   *
 ***************************************************************************
 */

#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <stdlib.h>

#include "sa.h"
#include "ioconf.h"
#include "pr_stats.h"
// add 2024-8-18
#include "sa1.h"
// add 2024-8-19
#include "../agent/top.h"

extern int o2;
extern os_data *os;

#define NO_UNIT -1

#ifdef USE_NLS
#include <locale.h>
#include <libintl.h>
#define _(string) gettext(string)
#else
#define _(string) (string)
#endif

extern unsigned int flags;
extern unsigned int dm_major;
extern int dis;
extern char timestamp[][TIMESTAMP_LEN];
extern unsigned long avg_count;

// add 2024
// extern int sar_lock1;
// extern int update_sar_lock1(int i);
unsigned long avg_count = 0;
extern int sadc1();
/*
 ***************************************************************************
 * Display CPU statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @prev	Index in array where stats used as reference are.
 * @curr	Index in array for current sample statistics.
 * @g_itv	Interval of time in jiffies multiplied by the number
 *		of processors.
 ***************************************************************************
 */

/*
 ***************************************************************************
 * Display tasks creation and context switches statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @prev	Index in array where stats used as reference are.
 * @curr	Index in array for current sample statistics.
 * @itv		Interval of time in jiffies.
 ***************************************************************************
 */

/*
 ***************************************************************************
 * Display interrupts statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @prev	Index in array where stats used as reference are.
 * @curr	Index in array for current sample statistics.
 * @itv		Interval of time in jiffies.
 ***************************************************************************
 */

/*
 ***************************************************************************
 * Display swapping statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @prev	Index in array where stats used as reference are.
 * @curr	Index in array for current sample statistics.
 * @itv		Interval of time in jiffies.
 ***************************************************************************
 */

/*
 ***************************************************************************
 * Display paging statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @prev	Index in array where stats used as reference are.
 * @curr	Index in array for current sample statistics.
 * @itv		Interval of time in jiffies.
 ***************************************************************************
 */
/*
 ***************************************************************************
 * Display I/O and transfer rate statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @prev	Index in array where stats used as reference are.
 * @curr	Index in array for current sample statistics.
 * @itv		Interval of time in jiffies.
 ***************************************************************************
 */

/*
 ***************************************************************************
 * Display memory and swap statistics. This function is used to
 * display instantaneous and average statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @prev	Index in array where stats used as reference are.
 * @curr	Index in array for current sample statistics.
 * @itv		Interval of time in jiffies.
 * @dispavg	TRUE if displaying average statistics.
 ***************************************************************************
 */

/*
 ***************************************************************************
 * Display memory and swap statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @prev	Index in array where stats used as reference are.
 * @curr	Index in array for current sample statistics.
 * @itv		Interval of time in jiffies.
 ***************************************************************************
 */
/*
 */

/*
 ***************************************************************************
 * Display average memory statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @prev	Index in array where stats used as reference are.
 * @curr	Index in array for current sample statistics.
 * @itv		Interval of time in jiffies.
 ***************************************************************************
 */

/*
 ***************************************************************************
 * Display kernel tables statistics. This function is used to display
 * instantaneous and average statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @curr	Index in array for current sample statistics.
 * @dispavg	True if displaying average statistics.
 ***************************************************************************
 */

static void *alloc_r(void *ptr, size_t num)
{
	void *pv;

	if (!num)
		++num;
	if (!(pv = realloc(ptr, num)))
		perror("alloc_r failed");
	;
	return pv;
} // end: alloc_r

/*
 ***************************************************************************
 * Display queue and load statistics. This function is used to display
 * instantaneous and average statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @curr	Index in array for current sample statistics.
 * @dispavg	TRUE if displaying average statistics.
 ***************************************************************************
 */
void stub_print_queue_stats(struct activity *a, int curr, int dispavg)
{
	struct stats_queue
		*sqc = (struct stats_queue *)a->buf[curr];
	/*
	if (dis) {
		printf("\n%-11s   runq-sz  plist-sz   ldavg-1   ldavg-5  ldavg-15   blocked\n",
			   timestamp[!curr]);
	} */

	if (!dispavg)
	{
		/* Display instantaneous values */
		os_data *os_sar = &os[0];

		// 将统计值格式化并存储到 os_sar->load_avg
		snprintf(os_sar->load_avg.nr_running, sizeof(os_sar->load_avg.nr_running), "%lu", sqc->nr_running);
		snprintf(os_sar->load_avg.nr_threads, sizeof(os_sar->load_avg.nr_threads), "%u", sqc->nr_threads);
		snprintf(os_sar->load_avg.load_avg_1, sizeof(os_sar->load_avg.load_avg_1), "%.2f", (double)sqc->load_avg_1 / 100);
		snprintf(os_sar->load_avg.load_avg_5, sizeof(os_sar->load_avg.load_avg_5), "%.2f", (double)sqc->load_avg_5 / 100);
		snprintf(os_sar->load_avg.load_avg_15, sizeof(os_sar->load_avg.load_avg_15), "%.2f", (double)sqc->load_avg_15 / 100);
		snprintf(os_sar->load_avg.procs_blocked, sizeof(os_sar->load_avg.procs_blocked), "%lu", sqc->procs_blocked);
	}
	else
	{
	}
}

/*
 ***************************************************************************
 * Display queue and load statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @prev	Index in array where stats used as reference are.
 * @curr	Index in array for current sample statistics.
 * @itv		Interval of time in jiffies.
 ***************************************************************************
 */
__print_funct_t print_queue_stats(struct activity *a, int prev, int curr,
								  unsigned long long itv)
{
	stub_print_queue_stats(a, curr, FALSE);
}

/*
 ***************************************************************************
 * Display filesystems statistics. This function is used to
 * display instantaneous and average statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @curr	Index in array for current sample statistics.
 * @dispavg	TRUE if displaying average statistics.
 ***************************************************************************
 */
__print_funct_t stub_print_filesystem_stats(struct activity *a, int curr, int dispavg)
{
	int i, j;
	struct stats_filesystem *sfc, *sfm;

	int sar_count = 0;
	for (i = 0; i < a->nr; i++)
	{
		pthread_mutex_lock(&xxx);
		if (sar_count == o2)
		{
			os = alloc_r(os, (sar_count + 1) * sizeof(os_data));
			memset(os + o2, 0, 1 * sizeof(os_data));
			o2++;
		}
		pthread_mutex_unlock(&xxx);

		os_data *os_sar = &os[sar_count];

		sfc = (struct stats_filesystem *)((char *)a->buf[curr] + i * a->msize);

		if (!sfc->f_blocks)
			/* Size of filesystem is null: We are at the end of the list */
			break;

		// 将文件系统名称保存到结构体中
		snprintf(os_sar->full_filesystem.fs_name, sizeof(os_sar->full_filesystem.fs_name), "%s", sfc->fs_name);
		snprintf(os_sar->full_filesystem.fs_mount, sizeof(os_sar->full_filesystem.fs_mount), "%s", sfc->fs_mount);
		// 计算各种统计值并格式化为字符串
		// double mbfsfree = (double)sfc->f_bfree / 1024 / 1024;
		// 计算available
		// double mbfsavailable = (double)sfc->f_bavail / 1024 / 1024;

		double mbfsused = (double)(sfc->f_blocks - sfc->f_bfree) / 1024 / 1024;

		// 计算使用率百分比
		// float fsused = sfc->f_blocks ? SP_VALUE(sfc->f_bfree, sfc->f_blocks, sfc->f_blocks) : 0.0;
		// 计算使用率百分比 - 与df命令一致的算法
		// df算法: used / (used + available) * 100
		// 其中 used = f_blocks - f_bfree, available = f_bavail
		double total_available_mb = (double)sfc->f_bavail / 1024 / 1024;
		double ufsused = (mbfsused + total_available_mb) > 0 ?
		                 (mbfsused / (mbfsused + total_available_mb)) * 100 : 0.0;
		// float iused = sfc->f_files ? SP_VALUE(sfc->f_ffree, sfc->f_files, sfc->f_files) : 0.0;

		// 计算已使用的inode数
		// unsigned long long inode_used = sfc->f_files - sfc->f_ffree;
		//计算索引节点使用率
		double iusedpct = (sfc->f_files ? SP_VALUE(sfc->f_ffree, sfc->f_files, sfc->f_files)
					 : 0.0);
		// 将统计值格式化为字符串并存储到结构体中
		snprintf(os_sar->full_filesystem.f_total_MB, sizeof(os_sar->full_filesystem.f_total_MB), "%.2f", (double)sfc->f_blocks / 1024 / 1024);
		snprintf(os_sar->full_filesystem.f_available_MB, sizeof(os_sar->full_filesystem.f_available_MB), "%.2f", (double)sfc->f_bavail / 1024 / 1024);

		snprintf(os_sar->full_filesystem.f_used_MB, sizeof(os_sar->full_filesystem.f_used_MB), "%.2f", mbfsused);
		snprintf(os_sar->full_filesystem.f_used_percent, sizeof(os_sar->full_filesystem.f_used_percent), "%.2f", ufsused);
		snprintf(os_sar->full_filesystem.f_files, sizeof(os_sar->full_filesystem.f_files), "%llu", sfc->f_files);
		snprintf(os_sar->full_filesystem.f_ffree, sizeof(os_sar->full_filesystem.f_ffree), "%llu", sfc->f_ffree);
		snprintf(os_sar->full_filesystem.f_iusedpct, sizeof(os_sar->full_filesystem.f_iusedpct), "%.2f", iusedpct);

		sar_count++;
	}
	os->file_count = sar_count;
}

/*
 ***************************************************************************
 * Display filesystems statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @prev	Index in array where stats used as reference are.
 * @curr	Index in array for current sample statistics.
 * @itv		Interval of time in jiffies.
 ***************************************************************************
 */
__print_funct_t print_filesystem_stats(struct activity *a, int prev, int curr,
									   unsigned long long itv)
{
	stub_print_filesystem_stats(a, curr, FALSE);
}

/*
 ***************************************************************************
 * Display average filesystems statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @prev	Index in array where stats used as reference are.
 * @curr	Index in array for current sample statistics.
 * @itv		Interval of time in jiffies.
 ***************************************************************************
 */
__print_funct_t print_avg_filesystem_stats(struct activity *a, int prev, int curr,
										   unsigned long long itv)
{
	stub_print_filesystem_stats(a, 2, TRUE);
}

/*
 ***************************************************************************
 * Display I/O and transfer rate statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @prev	Index in array where stats used as reference are.
 * @curr	Index in array for current sample statistics.
 * @itv		Interval of time in jiffies.
 ***************************************************************************
 */
__print_funct_t print_io_stats(struct activity *a, int prev, int curr,
							   unsigned long long itv)
{
	struct stats_io
		*sic = (struct stats_io *)a->buf[curr],
		*sip = (struct stats_io *)a->buf[prev];

	/*
	if (dis) {
		printf("\n%-11s       tps      rtps      wtps   bread/s   bwrtn/s\n",
			   timestamp[!curr]);
	}

	printf("%-11s %9.2f %9.2f %9.2f %9.2f %9.2f\n", timestamp[curr],
		   S_VALUE(sip->dk_drive,      sic->dk_drive,      itv),
		   S_VALUE(sip->dk_drive_rio,  sic->dk_drive_rio,  itv),
		   S_VALUE(sip->dk_drive_wio,  sic->dk_drive_wio,  itv),
		   S_VALUE(sip->dk_drive_rblk, sic->dk_drive_rblk, itv),
		   S_VALUE(sip->dk_drive_wblk, sic->dk_drive_wblk, itv));
	*/

	os_data *os_sar = &os[0];

	// 计算每秒平均值并格式化为字符串存储到os_sar->full_io结构体中
	float tps = S_VALUE(sip->dk_drive, sic->dk_drive, itv);
	float rtps = S_VALUE(sip->dk_drive_rio, sic->dk_drive_rio, itv);
	float wtps = S_VALUE(sip->dk_drive_wio, sic->dk_drive_wio, itv);
	float dtps = S_VALUE(sip->dk_drive_dio, sic->dk_drive_dio, itv);
	float bread = S_VALUE(sip->dk_drive_rblk, sic->dk_drive_rblk, itv);
	float bwrtn = S_VALUE(sip->dk_drive_wblk, sic->dk_drive_wblk, itv);
	float bdscd = S_VALUE(sip->dk_drive_dblk, sic->dk_drive_dblk, itv);

	// 将浮点值格式化为字符串
	snprintf(os_sar->full_io.dk_drive, sizeof(os_sar->full_io.dk_drive), "%.2f", tps);
	snprintf(os_sar->full_io.dk_drive_rio, sizeof(os_sar->full_io.dk_drive_rio), "%.2f", rtps);
	snprintf(os_sar->full_io.dk_drive_wio, sizeof(os_sar->full_io.dk_drive_wio), "%.2f", wtps);
	snprintf(os_sar->full_io.dk_drive_dio, sizeof(os_sar->full_io.dk_drive_dio), "%.2f", dtps);
	snprintf(os_sar->full_io.dk_drive_rblk, sizeof(os_sar->full_io.dk_drive_rblk), "%.2f", bread);
	snprintf(os_sar->full_io.dk_drive_wblk, sizeof(os_sar->full_io.dk_drive_wblk), "%.2f", bwrtn);
	snprintf(os_sar->full_io.dk_drive_dblk, sizeof(os_sar->full_io.dk_drive_dblk), "%.2f", bdscd);
}

/*
 ***************************************************************************
 * Display disks statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @prev	Index in array where stats used as reference are.
 * @curr	Index in array for current sample statistics.
 * @itv		Interval of time in jiffies.
 ***************************************************************************
 */
__print_funct_t print_disk_stats(struct activity *a, int prev, int curr,
								 unsigned long long itv)
{
	int i, j;
	struct stats_disk *sdc, *sdp;
	struct ext_disk_stats xds;
	char *dev_name, *persist_dev_name;

	int sar_count = 0;
	for (i = 0; i < a->nr; i++)
	{
		// 打印当前时间，使用get_time2

		pthread_mutex_lock(&xxx);
		if (sar_count == o2)
		{
			os = alloc_r(os, (sar_count + 1) * sizeof(os_data));
			memset(os + o2, 0, 1 * sizeof(os_data));
			o2++;
		}
		pthread_mutex_unlock(&xxx);

		os_data *os_sar = &os[sar_count];

		sdc = (struct stats_disk *)((char *)a->buf[curr] + i * a->msize);

		if (!(sdc->major + sdc->minor))
			continue;

		j = check_disk_reg(a, curr, prev, i);
		sdp = (struct stats_disk *)((char *)a->buf[prev] + j * a->msize);

		/* Compute service time, etc. */
		compute_ext_disk_stats(sdc, sdp, itv, &xds);

		dev_name = NULL;

		// 访问/dev/mapper;主要是lvm或者多路径
		dev_name = transform_devmapname(sdc->major, sdc->minor);

		if (!dev_name)
		{
			//优先访问/sys/dev/block，rel6以上
			//如果rel5及以下，访问 /etc/sysconfig/sysstat.ioconf
			dev_name = get_devname(sdc->major, sdc->minor,
								   1);
		}
		if (!dev_name)
		{
			//如果还没获取device,直接从sdc->device获取，也就是/proc/diskstats的原始设备名
			dev_name = sdc->device;

		}

		// 将设备名称存储到结构体中
		snprintf(os_sar->full_disk.dev_name, sizeof(os_sar->full_disk.dev_name), "%s", dev_name);

		// 将各个指标格式化为字符串并存储到结构体中
		float tps = S_VALUE(sdp->nr_ios, sdc->nr_ios, itv);
		snprintf(os_sar->full_disk.tps, sizeof(os_sar->full_disk.tps), "%.2f", tps);

		// 每秒读KB (rkB/s) - 需要将扇区数除以2得到KB
		float rd_kb = S_VALUE(sdp->rd_sect, sdc->rd_sect, itv) / 2;
		snprintf(os_sar->full_disk.rkB_s, sizeof(os_sar->full_disk.rkB_s), "%.2f", rd_kb);

		// 每秒写KB (wkB/s) - 需要将扇区数除以2得到KB
		float wr_kb = S_VALUE(sdp->wr_sect, sdc->wr_sect, itv) / 2;
		snprintf(os_sar->full_disk.wkB_s, sizeof(os_sar->full_disk.wkB_s), "%.2f", wr_kb);

		// 每秒丢弃KB (dkB/s) - 如果内核版本支持丢弃操作
		float dc_kb = 0.0;
		if (sdc->dc_sect != 0)
		{
			// 丢弃的扇区数除以2得到KB
			dc_kb = S_VALUE(sdp->dc_sect, sdc->dc_sect, itv) / 2;
		}
		snprintf(os_sar->full_disk.dkB_s, sizeof(os_sar->full_disk.dkB_s), "%.2f", dc_kb);

		// 平均请求大小 (areq-sz)
		snprintf(os_sar->full_disk.areq_sz, sizeof(os_sar->full_disk.areq_sz), "%.2f", xds.arqsz / 2);

		// 平均队列长度 (aqu-sz)

		float aqu_sz = S_VALUE(sdp->rq_ticks, sdc->rq_ticks, itv) / 1000.0;
		snprintf(os_sar->full_disk.aqu_sz, sizeof(os_sar->full_disk.aqu_sz), "%.2f", aqu_sz);

		// 平均等待时间 (await)
		snprintf(os_sar->full_disk.await, sizeof(os_sar->full_disk.await), "%.2f", xds.await);

		// 设备利用率百分比 (%util)
		float util = xds.util / 10.0;
		snprintf(os_sar->full_disk.util, sizeof(os_sar->full_disk.util), "%.2f", util);

		sar_count++;
	}
	os->disk_count = sar_count;
}

/*
 ***************************************************************************
 * Display network interfaces statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @prev	Index in array where stats used as reference are.
 * @curr	Index in array for current sample statistics.
 * @itv		Interval of time in jiffies.
 ***************************************************************************
 */
__print_funct_t print_net_dev_stats(struct activity *a, int prev, int curr,
									unsigned long long itv)
{
	int i, j;
	struct stats_net_dev *sndc, *sndp;
	double rxb, txb, ifutil;

	/*
		if (dis) {
			printf("\n%-11s     IFACE   rxpck/s   txpck/s    rxkB/s    txkB/s"
				   "   rxcmp/s   txcmp/s  rxmcst/s   %%ifutil\n", timestamp[!curr]);
		}
	*/
	int sar_count = 0;
	for (i = 0; i < a->nr; i++)
	{
		pthread_mutex_lock(&xxx);
		if (sar_count == o2)
		{
			os = alloc_r(os, (sar_count + 1) * sizeof(os_data));
			memset(os + o2, 0, 1 * sizeof(os_data));
			o2++;
		}
		pthread_mutex_unlock(&xxx);

		os_data *os_sar = &os[sar_count];

		sndc = (struct stats_net_dev *)((char *)a->buf[curr] + i * a->msize);

		if (!strcmp(sndc->interface, ""))
			continue;

		j = check_net_dev_reg(a, curr, prev, i);
		sndp = (struct stats_net_dev *)((char *)a->buf[prev] + j * a->msize);

		//	printf("%-11s %9s", timestamp[curr], sndc->interface);
		// add 2024-8-21 update
		// strncpy(os_sar->net.interface_name,sndc->interface,15);
		snprintf(os_sar->net.interface_name, sizeof(os_sar->net.interface_name), "%s", sndc->interface);

		rxb = S_VALUE(sndp->rx_bytes, sndc->rx_bytes, itv);
		txb = S_VALUE(sndp->tx_bytes, sndc->tx_bytes, itv);
		double rxp = S_VALUE(sndp->rx_packets, sndc->rx_packets, itv);
		double txp = S_VALUE(sndp->tx_packets, sndc->tx_packets, itv);
		snprintf(os_sar->net.rxpck, 50, "%.2f", rxp);
		snprintf(os_sar->net.txpck, 50, "%.2f", txp);
		double rxKB = rxb / 1024;
		snprintf(os_sar->net.rxkB, 8, "%.2f", rxKB);
		double txKB = txb / 1024;
		snprintf(os_sar->net.txkB, 8, "%.2f", txKB);
		double rxcmp = S_VALUE(sndp->rx_compressed, sndc->rx_compressed, itv);
		snprintf(os_sar->net.rxcmp, 8, "%.2f", rxcmp);
		double txcmp = S_VALUE(sndp->tx_compressed, sndc->tx_compressed, itv);
		snprintf(os_sar->net.txcmp, 8, "%.2f", txcmp);
		double rxmcst = S_VALUE(sndp->multicast, sndc->multicast, itv);
		snprintf(os_sar->net.rxmcst, 8, "%.2f", rxmcst);

		ifutil = compute_ifutil(sndc, rxb, txb);
		snprintf(os_sar->net.ifutil, 6, "%.2f", ifutil);
		// printf("    %6.2f\n", ifutil);
		sar_count++;
	}
	os->int_count = sar_count;
}

/*
 ***************************************************************************
 * Display network interfaces statistics.
 *
 * IN:
 * @a           Activity structure with statistics.
 * @prev        Index in array where stats used as reference are.
 * @curr        Index in array for current sample statistics.
 * @itv         Interval of time in jiffies.
 ***************************************************************************
 */
__print_funct_t print_net_dev_stats1(struct activity *a, int prev, int curr,
									 unsigned long long itv)
{
	int i, j;
	struct stats_net_dev *sndc, *sndp;
	double rxb, txb, ifutil;

	for (i = 0; i < a->nr; i++)
	{

		sndc = (struct stats_net_dev *)((char *)a->buf[curr] + i * a->msize);

		if (!strcmp(sndc->interface, ""))
			continue;

		j = check_net_dev_reg(a, curr, prev, i);
		sndp = (struct stats_net_dev *)((char *)a->buf[prev] + j * a->msize);

		// printf(" %9s",  sndc->interface);

		rxb = S_VALUE(sndp->rx_bytes, sndc->rx_bytes, itv);
		txb = S_VALUE(sndp->tx_bytes, sndc->tx_bytes, itv);

		printf(" %9.2f %9.2f %9.2f %9.2f %9.2f %9.2f %9.2f",
			   S_VALUE(sndp->rx_packets, sndc->rx_packets, itv),
			   S_VALUE(sndp->tx_packets, sndc->tx_packets, itv),
			   rxb / 1024,
			   txb / 1024,
			   S_VALUE(sndp->rx_compressed, sndc->rx_compressed, itv),
			   S_VALUE(sndp->tx_compressed, sndc->tx_compressed, itv),
			   S_VALUE(sndp->multicast, sndc->multicast, itv));

		ifutil = compute_ifutil(sndc, rxb, txb);
		printf("    %6.2f\n", ifutil);
	}
}

/*
 ***************************************************************************
 * Display network interface errors statistics.
 *
 * IN:
 * @a		Activity structure with statistics.
 * @prev	Index in array where stats used as reference are.
 * @curr	Index in array for current sample statistics.
 * @itv		Interval of time in jiffies.
 ***************************************************************************
 */
__print_funct_t print_net_edev_stats(struct activity *a, int prev, int curr,
									 unsigned long long itv)
{
	int i, j;
	struct stats_net_edev *snedc, *snedp;
	/*

		if (dis) {
			printf("\n%-11s     IFACE   rxerr/s   txerr/s    coll/s  rxdrop/s"
				   "  txdrop/s  txcarr/s  rxfram/s  rxfifo/s  txfifo/s\n",
				   timestamp[!curr]);
		}
	*/

	int sar_count = 0;
	for (i = 0; i < a->nr; i++)
	{
		pthread_mutex_lock(&xxx);
		if (sar_count == o2)
		{
			os = alloc_r(os, (sar_count + 1) * sizeof(os_data));
			memset(os + o2, 0, 1 * sizeof(os_data));
			o2++;
		}
		pthread_mutex_unlock(&xxx);

		os_data *os_sar = &os[sar_count];

		snedc = (struct stats_net_edev *)((char *)a->buf[curr] + i * a->msize);

		if (!strcmp(snedc->interface, ""))
			continue;

		j = check_net_edev_reg(a, curr, prev, i);
		snedp = (struct stats_net_edev *)((char *)a->buf[prev] + j * a->msize);

		// printf("%9s", snedc->interface);

		// 计算每秒平均值
		float rx_errors = S_VALUE(snedp->rx_errors, snedc->rx_errors, itv);
		float tx_errors = S_VALUE(snedp->tx_errors, snedc->tx_errors, itv);
		float collisions = S_VALUE(snedp->collisions, snedc->collisions, itv);
		float rx_dropped = S_VALUE(snedp->rx_dropped, snedc->rx_dropped, itv);
		float tx_dropped = S_VALUE(snedp->tx_dropped, snedc->tx_dropped, itv);
		float tx_carrier_errors = S_VALUE(snedp->tx_carrier_errors, snedc->tx_carrier_errors, itv);
		float rx_frame_errors = S_VALUE(snedp->rx_frame_errors, snedc->rx_frame_errors, itv);
		float rx_fifo_errors = S_VALUE(snedp->rx_fifo_errors, snedc->rx_fifo_errors, itv);
		float tx_fifo_errors = S_VALUE(snedp->tx_fifo_errors, snedc->tx_fifo_errors, itv);
		/*
				printf(" %9.2f %9.2f %9.2f %9.2f %9.2f %9.2f %9.2f %9.2f %9.2f\n",
					   rx_errors, tx_errors, collisions, rx_dropped, tx_dropped,
					   tx_carrier_errors, rx_frame_errors, rx_fifo_errors, tx_fifo_errors);
				*/
		// 复制接口名称到os_sar的enet字段

		snprintf(os_sar->enet.interface_name, sizeof(os_sar->enet.interface_name), "%s", snedc->interface);

		// 将原始计数器数据保留在unsigned long long字段中
		// 把float rx_errors 使用snprintf 格式化为字符串
		snprintf(os_sar->enet.rx_errors, sizeof(os_sar->enet.rx_errors), "%.2f", rx_errors);
		snprintf(os_sar->enet.tx_errors, sizeof(os_sar->enet.tx_errors), "%.2f", tx_errors);
		snprintf(os_sar->enet.collisions, sizeof(os_sar->enet.collisions), "%.2f", collisions);
		snprintf(os_sar->enet.rx_dropped, sizeof(os_sar->enet.rx_dropped), "%.2f", rx_dropped);
		snprintf(os_sar->enet.tx_dropped, sizeof(os_sar->enet.tx_dropped), "%.2f", tx_dropped);
		snprintf(os_sar->enet.tx_carrier_errors, sizeof(os_sar->enet.tx_carrier_errors), "%.2f", tx_carrier_errors);
		snprintf(os_sar->enet.rx_frame_errors, sizeof(os_sar->enet.rx_frame_errors), "%.2f", rx_frame_errors);
		snprintf(os_sar->enet.rx_fifo_errors, sizeof(os_sar->enet.rx_fifo_errors), "%.2f", rx_fifo_errors);
		snprintf(os_sar->enet.tx_fifo_errors, sizeof(os_sar->enet.tx_fifo_errors), "%.2f", tx_fifo_errors);

		sar_count++;
	}
	os->enet_count = sar_count;
}
