tcpdump.o: tcpdump.c my.h ../sar/sa1.h ../libpcap/pcap-int.h \
 ../libpcap/pcap/pcap.h ../libpcap/pcap/funcattrs.h \
 ../libpcap/pcap/compiler-tests.h ../libpcap/pcap/pcap-inttypes.h \
 ../libpcap/pcap/socket.h ../libpcap/pcap/bpf.h ../libpcap/pcap/dlt.h \
 ../libpcap/varattrs.h ../libpcap/fmtutils.h ../libpcap/pcap/funcattrs.h \
 ../libpcap/portability.h config.h netdissect-stdinc.h ftmacros.h \
 compiler-tests.h varattrs.h funcattrs.h ../libpcap/pcap.h netdissect.h \
 status-exit-codes.h diag-control.h ip.h ip6.h interface.h addrtoname.h \
 extract.h machdep.h pcap-missing.h ascii_strcasecmp.h print.h fptype.h
my.h:
../sar/sa1.h:
../libpcap/pcap-int.h:
../libpcap/pcap/pcap.h:
../libpcap/pcap/funcattrs.h:
../libpcap/pcap/compiler-tests.h:
../libpcap/pcap/pcap-inttypes.h:
../libpcap/pcap/socket.h:
../libpcap/pcap/bpf.h:
../libpcap/pcap/dlt.h:
../libpcap/varattrs.h:
../libpcap/fmtutils.h:
../libpcap/pcap/funcattrs.h:
../libpcap/portability.h:
config.h:
netdissect-stdinc.h:
ftmacros.h:
compiler-tests.h:
varattrs.h:
funcattrs.h:
../libpcap/pcap.h:
netdissect.h:
status-exit-codes.h:
diag-control.h:
ip.h:
ip6.h:
interface.h:
addrtoname.h:
extract.h:
machdep.h:
pcap-missing.h:
ascii_strcasecmp.h:
print.h:
fptype.h:
