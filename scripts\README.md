# 数据库连接模拟器

这是一个用于模拟真实生产环境数据库连接的工具，可以创建多个长连接并执行真实的业务操作，用于测试网络安全监控系统的连接数检测功能。

## 功能特性

- **多线程并发连接**: 支持1-1000个并发数据库连接
- **长连接模拟**: 保持连接并持续执行操作
- **真实业务操作**: 执行SELECT、CREATE、INSERT等SQL操作
- **实时统计监控**: 每5秒输出连接数和操作统计
- **优雅关闭**: 支持Ctrl+C信号优雅关闭所有连接
- **详细日志**: 记录每个连接的建立、操作和断开过程

## 编译要求

### Debian/Ubuntu
```bash
# 安装编译依赖
sudo apt update
sudo apt install gcc make libmariadb-dev-compat libmariadb-dev

# 或者安装MySQL开发包
sudo apt install gcc make libmysqlclient-dev
```

### CentOS/RHEL
```bash
# 安装编译依赖
sudo yum install gcc make mariadb-devel
# 或者
sudo yum install gcc make mysql-devel
```

## 编译安装

```bash
# 进入scripts目录
cd /app/scripts

# 检查依赖
make check-deps

# 编译
make

# 可选：安装到系统路径
make install
```

## 使用方法

### 基本用法
```bash
# 使用默认参数（localhost:3306, 10个连接, 运行60秒）
./db_connection_simulator

# 指定数据库服务器
./db_connection_simulator -h *************

# 指定连接数和运行时间
./db_connection_simulator -c 50 -t 300

# 详细输出模式
./db_connection_simulator -v
```

### 参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| -h HOST | 数据库主机地址 | localhost |
| -P PORT | 数据库端口 | 3306 |
| -u USER | 数据库用户名 | root |
| -p PASSWORD | 数据库密码 | 空 |
| -d DATABASE | 数据库名 | mysql |
| -c CONNECTIONS | 最大并发连接数 (1-1000) | 10 |
| -t DURATION | 运行时长(秒) | 60 |
| -i INTERVAL | 操作间隔(毫秒) | 1000 |
| -v | 详细输出模式 | 关闭 |
| --help | 显示帮助信息 | - |

### 使用示例

```bash
# 模拟高并发场景（50个连接，运行5分钟）
./db_connection_simulator -h ************* -c 50 -t 300 -i 500

# 连接到特定数据库
./db_connection_simulator -u testuser -p testpass -d testdb -c 20 -v

# 模拟低频操作（每2秒一次操作）
./db_connection_simulator -c 30 -i 2000 -t 600

# 压力测试（100个连接，每100ms一次操作）
./db_connection_simulator -c 100 -i 100 -t 120 -v
```

## 输出说明

### 启动信息
```
=== 数据库连接模拟器启动 ===
目标主机: localhost:3306
数据库: mysql
用户: root
最大连接数: 10
运行时长: 60 秒
操作间隔: 1000 毫秒
详细模式: 关闭
==============================
```

### 连接日志
```
[2025-07-16 20:45:01] 线程1: 连接成功 (连接ID: 123)
[2025-07-16 20:45:01] 线程2: 连接成功 (连接ID: 124)
```

### 统计信息（每5秒输出）
```
[2025-07-16 20:45:05] === 连接统计 ===
活跃连接数: 10/10
总操作数: 240 (新增: 60)
失败操作数: 0
连接错误数: 0
操作速率: 12.00 ops/sec
========================
```

### 最终统计
```
=== 最终统计 ===
总操作数: 2400
失败操作数: 0
连接错误数: 0
成功率: 100.00%
================
```

## 业务操作说明

每个连接会循环执行以下操作：

1. **系统状态查询**: `SELECT CONNECTION_ID(), NOW(), VERSION()`
2. **进程列表查询**: `SHOW PROCESSLIST`
3. **连接数查询**: `SHOW STATUS LIKE 'Threads_connected'`
4. **临时表操作**: 创建临时表、插入数据、查询数据

这些操作模拟了真实应用程序的数据库访问模式。

## 配合网络监控测试

在其他服务器上运行此模拟器，然后在监控服务器上使用send_data程序：

```bash
# 在监控服务器上运行
./send_data any notask

# 观察详细IP+端口连接统计中的连接数是否与模拟器的连接数一致
```

## 故障排除

### 编译错误
- 确保安装了MySQL/MariaDB开发包
- 检查mysql_config命令是否可用

### 连接失败
- 检查数据库服务器是否运行
- 验证用户名密码是否正确
- 确认防火墙设置允许连接

### 权限错误
- 确保数据库用户有足够权限
- 检查是否能创建临时表

## 注意事项

1. **资源消耗**: 大量连接会消耗服务器资源，请根据服务器性能调整连接数
2. **网络带宽**: 频繁操作会产生网络流量
3. **数据库负载**: 避免在生产数据库上进行高强度测试
4. **连接限制**: 注意数据库服务器的最大连接数限制

## 清理

```bash
# 清理编译文件
make clean

# 卸载（如果已安装到系统）
sudo rm -f /usr/local/bin/db_connection_simulator
```
