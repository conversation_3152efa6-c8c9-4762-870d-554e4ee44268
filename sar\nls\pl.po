# Polish NLS support for the sysstat package.
# Copyright (C) 1999 <PERSON><PERSON><PERSON><PERSON> <sysstat [at] orange.fr> (msgids).
# This file is distributed under the same license as the sysstat package.
#
# <PERSON> <<EMAIL>>, 2008 - 2013.
msgid ""
msgstr ""
"Project-Id-Version: sysstat 10.1.6\n"
"Report-Msgid-Bugs-To: sysstat <at> orange.fr\n"
"POT-Creation-Date: 2013-06-08 09:01+0200\n"
"PO-Revision-Date: 2013-06-10 08:56+0200\n"
"Last-Translator: <PERSON> <robe<PERSON>@debian.org>\n"
"Language-Team: Polish <<EMAIL>>\n"
"Language: pl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Lokalize 1.4\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#: iostat.c:86 cifsiostat.c:71 mpstat.c:90 sar.c:94 pidstat.c:83
#: nfsiostat.c:70
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ]\n"
msgstr "Użycie: %s [ opcje ] [ <interwał> [ <liczba> ] ]\n"

#: iostat.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ] [ --debuginfo ]\n"
msgstr ""
"Opcje to:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <nazwa_grupy> ] [ -p [ <urządzenie> [,...] | ALL ] ]\n"
"[ <urządzenie> [...] | ALL ] [ --debuginfo ]\n"

#: iostat.c:95
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ]\n"
msgstr ""
"Opcje to:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <nazwa_grupy> ] [ -p [ <urządzenie> [,...] | ALL ] ]\n"
"[ <urządzenie> [...] | ALL ]\n"

#: iostat.c:330
#, c-format
msgid "Cannot find disk data\n"
msgstr "Nie można znaleźć danych o dyskach\n"

#: iostat.c:1394 sa_common.c:1303
#, c-format
msgid "Invalid type of persistent device name\n"
msgstr "Niepoprawny typ trwałej nazwy urządzenia\n"

#: sadf_misc.c:596
#, c-format
msgid "System activity data file: %s (%#x)\n"
msgstr "Plik z danymi o aktywności systemu: %s (%#x)\n"

#: sadf_misc.c:605
#, c-format
msgid "Host: "
msgstr "System: "

#: sadf_misc.c:611
#, c-format
msgid "Size of a long int: %d\n"
msgstr "Rozmiar typu long int: %d\n"

#: sadf_misc.c:613
#, c-format
msgid "List of activities:\n"
msgstr "Lista aktywności:\n"

#: sadf_misc.c:626
#, c-format
msgid "\t[Unknown activity format]"
msgstr "\t[Nieznany format aktywności]"

#: sadc.c:84
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
msgstr "Użycie: %s [ opcje ] [ <interwał> [ <liczba> ] ] [ <plik_wyjściowy> ]\n"

#: sadc.c:87
#, c-format
msgid ""
"Options are:\n"
"[ -C <comment> ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"
msgstr ""
"Opcje to:\n"
"[ -C <komentarz> ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"

#: sadc.c:250
#, c-format
msgid "Cannot write data to system activity file: %s\n"
msgstr "Nie można zapisać danych do pliku aktywności systemu: %s\n"

#: sadc.c:537
#, c-format
msgid "Cannot write system activity file header: %s\n"
msgstr "Nie można zapisać nagłówka pliku aktywności systemu: %s\n"

#: sadc.c:650 sadc.c:659 sadc.c:720 ioconf.c:491 rd_stats.c:105
#: sa_common.c:1109 count.c:275
#, c-format
msgid "Cannot open %s: %s\n"
msgstr "Nie można otworzyć %s: %s\n"

#: sadc.c:842
#, c-format
msgid "Cannot append data to that file (%s)\n"
msgstr "Nie można dopisać danych do tego pliku (%s)\n"

#: common.c:62
#, c-format
msgid "sysstat version %s\n"
msgstr "sysstat w wersji %s\n"

#: cifsiostat.c:75 nfsiostat.c:74
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"
msgstr ""
"Opcje to:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"

#: cifsiostat.c:78 nfsiostat.c:77
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
msgstr ""
"Opcje to:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#: mpstat.c:93
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -P { <cpu> [,...] | ON | ALL } ]\n"
msgstr ""
"Opcje to:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -P { <cpu> [,...] | ON | ALL } ]\n"

# sar.c:
#: mpstat.c:609 sar.c:402 pidstat.c:1857
msgid "Average:"
msgstr "Średnia:"

#: mpstat.c:976
#, c-format
msgid "Not that many processors!\n"
msgstr "Nie ma aż tylu procesorów!\n"

#: sadf.c:86
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <datafile> ]\n"
msgstr "Użycie: %s [ opcje ] [ <interwał> [ <liczba> ] ] [ <plik_danych> ]\n"

#: sadf.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -C ] [ -d | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -P { <cpu> [,...] | ALL } ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"
"[ -- <sar_options> ]\n"
msgstr ""
"Opcje to:\n"
"[ -C ] [ -d | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -P { <cpu> [,...] | ALL } ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"
"[ -- <opcje_sar> ]\n"

#: sar.c:109
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -d ] [ -F ] [ -H ] [ -h ] [ -p ] [ -q ] [ -R ]\n"
"[ -r ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ] [ -v ] [ -W ] [ -w ] [ -y ]\n"
"[ -I { <int> [,...] | SUM | ALL | XALL } ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -m { <keyword> [,...] | ALL } ] [ -n { <keyword> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filename> ] | -o [ <filename> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"
msgstr ""
"Opcje to:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -d ] [ -F ] [ -H ] [ -h ] [ -p ] [ -q ] [ -R ]\n"
"[ -r ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ] [ -v ] [ -W ] [ -w ] [ -y ]\n"
"[ -I { <przerwanie> [,...] | SUM | ALL | XALL } ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -m { <słowo_kluczowe> [,...] | ALL } ] [ -n { <słowo_kluczowe> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <nazwa_pliku> ] | -o [ <nazwa_pliku> ] | -[0-9]+ ]\n"
"[ -i <interwał> ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"

#: sar.c:131
#, c-format
msgid "Main options and reports:\n"
msgstr "Główne opcje i raporty:\n"

#: sar.c:132
#, c-format
msgid "\t-b\tI/O and transfer rate statistics\n"
msgstr "\t-b\tStatystyki I/O i prędkości transferu danych\n"

#: sar.c:133
#, c-format
msgid "\t-B\tPaging statistics\n"
msgstr "\t-B\tStatystyki stronicowania\n"

#: sar.c:134
#, c-format
msgid "\t-d\tBlock devices statistics\n"
msgstr "\t-d\tStatystyki urządzeń blokowych\n"

#: sar.c:135
#, c-format
msgid "\t-F\tFilesystems statistics\n"
msgstr "\t-F\tStatystyki systemów plików\n"

#: sar.c:136
#, c-format
msgid "\t-H\tHugepages utilization statistics\n"
msgstr "\t-S\tStatystyki wykorzystania wielkich stron\n"

#: sar.c:137
#, c-format
msgid ""
"\t-I { <int> | SUM | ALL | XALL }\n"
"\t\tInterrupts statistics\n"
msgstr ""
"\t-I { <przerwanie> | SUM | ALL | XALL }\n"
"\t\tStatystyki przerwań\n"

#: sar.c:139
#, c-format
msgid ""
"\t-m { <keyword> [,...] | ALL }\n"
"\t\tPower management statistics\n"
"\t\tKeywords are:\n"
"\t\tCPU\tCPU instantaneous clock frequency\n"
"\t\tFAN\tFans speed\n"
"\t\tFREQ\tCPU average clock frequency\n"
"\t\tIN\tVoltage inputs\n"
"\t\tTEMP\tDevices temperature\n"
"\t\tUSB\tUSB devices plugged into the system\n"
msgstr ""
"\t-m { <słowo_kluczowe> [,...] | ALL }\n"
"\t\tStatystyki zarządzania pamięcią\n"
"\t\tDostępne słowa kluczowe:\n"
"\t\tCPU\tBieżąca częstotliwość zegara CPU\n"
"\t\tFAN\tPrędkość wentylatorów\n"
"\t\tFREQ\tŚrednia częstotliwość zegara CPU\n"
"\t\tIN\tNapięcie wejściowe\n"
"\t\tTEMP\tTemperatura urządzeń\n"
"\t\tUSB\tUrządzenia USB podłączone do systemu\n"

#: sar.c:148
#, c-format
msgid ""
"\t-n { <keyword> [,...] | ALL }\n"
"\t\tNetwork statistics\n"
"\t\tKeywords are:\n"
"\t\tDEV\tNetwork interfaces\n"
"\t\tEDEV\tNetwork interfaces (errors)\n"
"\t\tNFS\tNFS client\n"
"\t\tNFSD\tNFS server\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP traffic\t(v4)\n"
"\t\tEIP\tIP traffic\t(v4) (errors)\n"
"\t\tICMP\tICMP traffic\t(v4)\n"
"\t\tEICMP\tICMP traffic\t(v4) (errors)\n"
"\t\tTCP\tTCP traffic\t(v4)\n"
"\t\tETCP\tTCP traffic\t(v4) (errors)\n"
"\t\tUDP\tUDP traffic\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP traffic\t(v6)\n"
"\t\tEIP6\tIP traffic\t(v6) (errors)\n"
"\t\tICMP6\tICMP traffic\t(v6)\n"
"\t\tEICMP6\tICMP traffic\t(v6) (errors)\n"
"\t\tUDP6\tUDP traffic\t(v6)\n"
msgstr ""
"\t-n { <słowo_kluczowe> [,...] | ALL }\n"
"\t\tStatystyki sieciowe\n"
"\t\tSłowa kluczowe to:\n"
"\t\tDEV\tInterfejsy sieciowe\n"
"\t\tEDEV\tInterfejsy sieciowe (błędy)\n"
"\t\tNFS\tKlient NFS\n"
"\t\tNFSD\tSerwer NFS \n"
"\t\tSOCK\tSokety\t(v4)\n"
"\t\tIP\tRuch IP\t(v4)\n"
"\t\tEIP\tRuch IP\t(v4) (błędy)\n"
"\t\tICMP\tRuch ICMP\t(v4)\n"
"\t\tEICMP\tRuch ICMP\t(v4) (błędy)\n"
"\t\tTCP\tRuch TCP\t(v4)\n"
"\t\tETCP\tRuch TCP\t(v4) (błędy)\n"
"\t\tUDP\tRuch UDP\t(v4)\n"
"\t\tSOCK6\tSokety\t(v6)\n"
"\t\tIP6\tRuch IP\t(v6)\n"
"\t\tEIP6\tRuch IP\t(v6) (błędy)\n"
"\t\tICMP6\tRuch ICMP\t(v6)\n"
"\t\tEICMP6\tRuch ICMP\t(v6) (błędy)\n"
"\t\tUDP6\tRuch UDP\t(v6)\n"

#: sar.c:169
#, c-format
msgid "\t-q\tQueue length and load average statistics\n"
msgstr "\t-q\tStatystyki kolejkowania procesów i średniego obciążenia systemu\n"

#: sar.c:170
#, c-format
msgid "\t-r\tMemory utilization statistics\n"
msgstr "\t-r\tStatystyki wykorzystania pamięci\n"

#: sar.c:171
#, c-format
msgid "\t-R\tMemory statistics\n"
msgstr "\t-R\tStatystyki pamięci\n"

#: sar.c:172
#, c-format
msgid "\t-S\tSwap space utilization statistics\n"
msgstr "\t-S\tStatystyki wykorzystania przestrzeni wymiany\n"

#: sar.c:173
#, c-format
msgid ""
"\t-u [ ALL ]\n"
"\t\tCPU utilization statistics\n"
msgstr ""
"\t-u [ ALL ]\n"
"\t\tStatystyki wykorzystania procesora\n"

#: sar.c:175
#, c-format
msgid "\t-v\tKernel tables statistics\n"
msgstr "\t-v\tStatystyki tabel jądra\n"

#: sar.c:176
#, c-format
msgid "\t-w\tTask creation and system switching statistics\n"
msgstr "\t-w\tStatystyki tworzenia zadań i przełączania systemu\n"

#: sar.c:177
#, c-format
msgid "\t-W\tSwapping statistics\n"
msgstr "\t-W\tStatystyki wymiany\n"

#: sar.c:178
#, c-format
msgid "\t-y\tTTY devices statistics\n"
msgstr "\t-y\tStatystyki terminali\n"

#: sar.c:236
#, c-format
msgid "End of data collecting unexpected\n"
msgstr "Niespodziewany koniec zbieranych danych\n"

#: sar.c:823
#, c-format
msgid "Invalid data format\n"
msgstr "Niepoprawny format danych\n"

#: sar.c:827
#, c-format
msgid "Using a wrong data collector from a different sysstat version\n"
msgstr "Używany program do zbierania danych pochodzi z innej wersji pakietu sysstat\n"

#: sar.c:851
#, c-format
msgid "Inconsistent input data\n"
msgstr "Niespójne dane wejściowe\n"

#: sar.c:1034 pidstat.c:216
#, c-format
msgid "Requested activities not available\n"
msgstr "Żądane statystyki nie są dostępne\n"

#: sar.c:1304
#, c-format
msgid "-f and -o options are mutually exclusive\n"
msgstr "Opcje -f i -o się wykluczają\n"

#: sar.c:1310
#, c-format
msgid "Not reading from a system activity file (use -f option)\n"
msgstr "Czytanie danych nie z pliku aktywności systemu (proszę użyć opcji -f)\n"

#: sar.c:1442
#, c-format
msgid "Cannot find the data collector (%s)\n"
msgstr "Nie można znaleźć programu do zbierania danych (%s)\n"

#: sa_common.c:917
#, c-format
msgid "Error while reading system activity file: %s\n"
msgstr "Błąd podczas czytania pliku aktywności systemu: %s\n"

#: sa_common.c:927
#, c-format
msgid "End of system activity file unexpected\n"
msgstr "Niespodziewany koniec pliku aktywności systemu\n"

#: sa_common.c:946
#, c-format
msgid "File created by sar/sadc from sysstat version %d.%d.%d"
msgstr "Plik utworzony przez sar/sadc z pakietu sysstat w wersji %d.%d.%d"

#: sa_common.c:977
#, c-format
msgid "Invalid system activity file: %s\n"
msgstr "Niepoprawny plik aktywności systemu: %s\n"

#: sa_common.c:984
#, c-format
msgid "Current sysstat version can no longer read the format of this file (%#x)\n"
msgstr "Bieżąca wersja pakietu sysstat nie obsługuje już formatu tego pliku (%#x)\n"

#: sa_common.c:1216
#, c-format
msgid "Requested activities not available in file %s\n"
msgstr "Żądane statystyki nie są dostępne w pliku %s\n"

#: pidstat.c:86
#, c-format
msgid ""
"Options are:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -r ] [ -s ] [ -t ] [ -U [ <username> ] ] [ -u ]\n"
"[ -V ] [ -w ] [ -C <command> ] [ -p { <pid> [,...] | SELF | ALL } ]\n"
"[ -T { TASK | CHILD | ALL } ]\n"
msgstr ""
"Opcje to:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -r ] [ -s ] [ -t ] [ -U [ <username> ] ] [ -u ]\n"
"[ -V ] [ -w ] [ -C <polecenie> ] [ -p { <pid> [,...] | SELF | ALL } ]\n"
"[ -T { TASK | CHILD | ALL } ]\n"

#: count.c:321
#, c-format
msgid "Cannot handle so many processors!\n"
msgstr "Zbyt dużo procesorów!\n"

#: pr_stats.c:2348 pr_stats.c:2361 pr_stats.c:2461 pr_stats.c:2473
msgid "Summary"
msgstr "Podsumowanie"

#: pr_stats.c:2399
msgid "Other devices not listed here"
msgstr "Inne urządzenia nie zostały wypisane"
