.\"
.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_TSTAMP_TYPE_VAL_TO_NAME 3PCAP "22 August 2018"
.SH NAME
pcap_tstamp_type_val_to_name, pcap_tstamp_type_val_to_description \- get
a name or description for a time stamp type value
.SH SYNOPSIS
.nf
.ft B
#include <pcap.h>
.ft
.LP
.ft B
const char *pcap_tstamp_type_val_to_name(int tstamp_type);
const char *pcap_tstamp_type_val_to_description(int tstamp_type);
.ft
.fi
.SH DESCRIPTION
.BR pcap_tstamp_type_val_to_name ()
translates a time stamp type value to the corresponding time stamp type
name.
.B NULL
is returned on failure.
.PP
.BR pcap_tstamp_type_val_to_description ()
translates a time stamp type value to a short description of that time
stamp type.
.B NULL
is returned on failure.
.SH BACKWARD COMPATIBILITY
.PP
These functions became available in libpcap release 1.2.1.
.SH SEE ALSO
.BR pcap (3PCAP),
.BR pcap_list_tstamp_types (3PCAP),
.BR pcap_tstamp_type_name_to_val (3PCAP)
