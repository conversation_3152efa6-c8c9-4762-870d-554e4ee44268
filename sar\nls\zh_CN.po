# Chinese translations for sysstat package
# sysstat 软件包的简体中文翻译.
# Copyright (C) 2010 THE sysstat'S COPYRIGHT HOLDER
# This file is distributed under the same license as the sysstat package.
# <PERSON> <<EMAIL>>, 2008, 2009, 2010, 2011, 2012, 2013
msgid ""
msgstr ""
"Project-Id-Version: sysstat 10.1.3\n"
"Report-Msgid-Bugs-To: sysstat <at> orange.fr\n"
"POT-Creation-Date: 2012-12-10 22:07+0100\n"
"PO-Revision-Date: 2013-01-31 15:17+0800\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Chinese (simplified) <<EMAIL>>\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: sadf_misc.c:596
#, c-format
msgid "System activity data file: %s (%#x)\n"
msgstr "系统运行记录数据文件: %s (%#x)\n"

#: sadf_misc.c:605
#, c-format
msgid "Host: "
msgstr "主机:"

#: sadf_misc.c:611
#, c-format
msgid "Size of a long int: %d\n"
msgstr "长整型数的字节大小: %d\n"

#: sadf_misc.c:613
#, c-format
msgid "List of activities:\n"
msgstr "运行记录列表:\n"

#: sadf_misc.c:626
#, c-format
msgid "\t[Unknown activity format]"
msgstr "\\t[未知的记录格式]"

#: iostat.c:84 cifsiostat.c:69 mpstat.c:86 sar.c:90 pidstat.c:78
#: nfsiostat.c:68
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ]\n"
msgstr "用法: %s [ 选项 ] [ <时间间隔> [ <次数> ] ]\n"

#: iostat.c:87
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ] [ --debuginfo ]\n"
msgstr ""
"选项：\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <用户组名> ] [ -p [ <设备> [,...] | ALL ] ]\n"
"[ <设备> [...] | ALL ] [ --debuginfo ]\n"

#: iostat.c:93
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ]\n"
msgstr ""
"选项:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <用户组名> ] [ -p [ <设备> [,...] | ALL ] ]\n"
"[ <设备> [...] | ALL ]\n"

#: iostat.c:329
#, c-format
msgid "Cannot find disk data\n"
msgstr "无法找到磁盘数据\n"

#: iostat.c:1392 sa_common.c:1300
#, fuzzy, c-format
msgid "Invalid type of persistent device name\n"
msgstr "永久设备的命名类型无效\n"

#: sadc.c:81
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
msgstr "用法: %s [ 选项 ] [ <时间间隔> [ <次数> ] ] [ <输出文件> ]\n"

#: sadc.c:84
#, c-format
msgid ""
"Options are:\n"
"[ -C <comment> ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"
msgstr ""
"选项:\n"
"[ -C <注释> ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"

#: sadc.c:223
#, c-format
msgid "Cannot write data to system activity file: %s\n"
msgstr "无法将数据写入系统运行记录文件: %s\n"

#: sadc.c:510
#, c-format
msgid "Cannot write system activity file header: %s\n"
msgstr "无法写系统运行记录文件开始部分: %s\n"

#: sadc.c:623 sadc.c:632 sadc.c:693 ioconf.c:491 rd_stats.c:68 rd_stats.c:2124
#: sa_common.c:1109
#, c-format
msgid "Cannot open %s: %s\n"
msgstr "无法打开 %s: %s\n"

#: sadc.c:815
#, c-format
msgid "Cannot append data to that file (%s)\n"
msgstr "无法向文件 (%s) 中追加记录\n"

#: common.c:62
#, c-format
msgid "sysstat version %s\n"
msgstr "sysstat 版本 %s\n"

#: cifsiostat.c:73 nfsiostat.c:72
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"
msgstr ""
"选项:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"

#: cifsiostat.c:76 nfsiostat.c:75
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
msgstr ""
"选项:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#: sadf.c:86
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <datafile> ]\n"
msgstr "用法: %s [ 选项 ] [ <时间间隔> [ <次数> ] ] [ <数据文件> ]\n"

#: sadf.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -C ] [ -d | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -P { <cpu> [,...] | ALL } ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"
"[ -- <sar_options> ]\n"
msgstr ""
"选项:\n"
"[ -C ] [ -d | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -P { <cpu> [,...] | ALL } ] [ -s [ <时:分:秒> ] ] [ -e [ <时:分:秒> ] ]\n"
"[ -- <sar_options> ]\n"

#: mpstat.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -P { <cpu> [,...] | ON | ALL } ]\n"
msgstr ""
"选项:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -P { <cpu> [,...] | ON | ALL } ]\n"

#: mpstat.c:593 sar.c:382 pidstat.c:1822
msgid "Average:"
msgstr "平均时间:"

#: mpstat.c:951
#, c-format
msgid "Not that many processors!\n"
msgstr "处理器实在太多！\n"

#: sar.c:105
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -d ] [ -H ] [ -h ] [ -p ] [ -q ] [ -R ]\n"
"[ -r ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ] [ -v ] [ -W ] [ -w ] [ -y ]\n"
"[ -I { <int> [,...] | SUM | ALL | XALL } ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -m { <keyword> [,...] | ALL } ] [ -n { <keyword> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filename> ] | -o [ <filename> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"
msgstr ""
"选项:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -d ] [ -H ] [ -h ] [ -p ] [ -q ] [ -R ]\n"
"[ -r ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ] [ -v ] [ -W ] [ -w ] [ -y ]\n"
"[ -I { <中断> [,...] | SUM | ALL | XALL } ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -m { <关键词> [,...] | ALL } ] [ -n { <关键词> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <文件名> ] | -o [ <文件名> ] | -[0-9]+ ]\n"
"[ -i <间隔> ] [ -s [ <时:分:秒> ] ] [ -e [ <时:分:秒> ] ]\n"

#: sar.c:127
#, c-format
msgid "Main options and reports:\n"
msgstr "主选项和报告：\n"

#: sar.c:128
#, c-format
msgid "\t-b\tI/O and transfer rate statistics\n"
msgstr "\t-b\tI/O 和传输速率信息状况\n"

#: sar.c:129
#, c-format
msgid "\t-B\tPaging statistics\n"
msgstr "\t-B\t分页状况\n"

#: sar.c:130
#, c-format
msgid "\t-d\tBlock device statistics\n"
msgstr "\t-d\t块设备状况\n"

#: sar.c:131
#, c-format
msgid "\t-H\tHugepages utilization statistics\n"
msgstr "\t-H\t交换空间利用率\n"

#: sar.c:132
#, c-format
msgid ""
"\t-I { <int> | SUM | ALL | XALL }\n"
"\t\tInterrupts statistics\n"
msgstr ""
"\t-I { <中断> | SUM | ALL | XALL }\n"
"\t\t中断信息状况\n"

#: sar.c:134
#, c-format
msgid ""
"\t-m { <keyword> [,...] | ALL }\n"
"\t\tPower management statistics\n"
"\t\tKeywords are:\n"
"\t\tCPU\tCPU instantaneous clock frequency\n"
"\t\tFAN\tFans speed\n"
"\t\tFREQ\tCPU average clock frequency\n"
"\t\tIN\tVoltage inputs\n"
"\t\tTEMP\tDevices temperature\n"
"\t\tUSB\tUSB devices plugged into the system\n"
msgstr ""
"\t-m { <关键词> [,...] | ALL }\n"
"\t\t电源管理统计信息\n"
"\t\t关键字:\n"
"\t\tCPU\tCPU 频率\n"
"\t\tFAN\t风扇速度\n"
"\\t\\tFREQ\\tCPU 平均时钟频率\n"
"\t\tIN\t输入电压\n"
"\t\tTEMP\t设备温度\n"
"\\t\\tUSB\\t连接的USB 设备\n"

#: sar.c:143
#, c-format
msgid ""
"\t-n { <keyword> [,...] | ALL }\n"
"\t\tNetwork statistics\n"
"\t\tKeywords are:\n"
"\t\tDEV\tNetwork interfaces\n"
"\t\tEDEV\tNetwork interfaces (errors)\n"
"\t\tNFS\tNFS client\n"
"\t\tNFSD\tNFS server\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP traffic\t(v4)\n"
"\t\tEIP\tIP traffic\t(v4) (errors)\n"
"\t\tICMP\tICMP traffic\t(v4)\n"
"\t\tEICMP\tICMP traffic\t(v4) (errors)\n"
"\t\tTCP\tTCP traffic\t(v4)\n"
"\t\tETCP\tTCP traffic\t(v4) (errors)\n"
"\t\tUDP\tUDP traffic\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP traffic\t(v6)\n"
"\t\tEIP6\tIP traffic\t(v6) (errors)\n"
"\t\tICMP6\tICMP traffic\t(v6)\n"
"\t\tEICMP6\tICMP traffic\t(v6) (errors)\n"
"\t\tUDP6\tUDP traffic\t(v6)\n"
msgstr ""
"\t-n { <关键词> [,...] | ALL }\n"
"\t\t网络统计信息\n"
"\t\t关键词可以是：\n"
"\t\tDEV\t网卡\n"
"\t\tEDEV\t网卡 (错误)\n"
"\t\tNFS\tNFS 客户端\n"
"\t\tNFSD\tNFS 服务器\n"
"\t\tSOCK\tSockets (套接字)\t(v4)\n"
"\t\tIP\tIP 流\t(v4)\n"
"\t\tEIP\tIP 流\t(v4) (错误)\n"
"\t\tICMP\tICMP 流\t(v4)\n"
"\t\tEICMP\tICMP 流\t(v4) (错误)\n"
"\t\tTCP\tTCP 流\t(v4)\n"
"\t\tETCP\tTCP 流\t(v4) (错误)\n"
"\t\tUDP\tUDP 流\t(v4)\n"
"\t\tSOCK6\tSockets (套接字)\t(v6)\n"
"\t\tIP6\tIP 流\t(v6)\n"
"\t\tEIP6\tIP 流\t(v6) (错误)\n"
"\t\tICMP6\tICMP 流\t(v6)\n"
"\t\tEICMP6\tICMP 流\t(v6) (错误)\n"
"\t\tUDP6\tUDP 流\t(v6)\n"

#: sar.c:164
#, c-format
msgid "\t-q\tQueue length and load average statistics\n"
msgstr "\t-q\t队列长度和平均负载\n"

#: sar.c:165
#, c-format
msgid "\t-r\tMemory utilization statistics\n"
msgstr "\t-r\t内存利用率\n"

#: sar.c:166
#, c-format
msgid "\t-R\tMemory statistics\n"
msgstr "\t-R\t内存状况\n"

#: sar.c:167
#, c-format
msgid "\t-S\tSwap space utilization statistics\n"
msgstr "\t-S\t交换空间利用率\n"

#: sar.c:168
#, c-format
msgid ""
"\t-u [ ALL ]\n"
"\t\tCPU utilization statistics\n"
msgstr ""
"\t-u [ ALL ]\n"
"\t\tCPU 利用率\n"

#: sar.c:170
#, c-format
msgid "\t-v\tKernel table statistics\n"
msgstr "\t-v\tKernel table 状况\n"

#: sar.c:171
#, c-format
msgid "\t-w\tTask creation and system switching statistics\n"
msgstr "\t-w\t任务创建与系统转换统计信息\n"

#: sar.c:172
#, c-format
msgid "\t-W\tSwapping statistics\n"
msgstr "\t-W\t交换信息\n"

#: sar.c:173
#, c-format
msgid "\t-y\tTTY device statistics\n"
msgstr "\t-y\tTTY 设备状况\n"

#: sar.c:216
#, c-format
msgid "End of data collecting unexpected\n"
msgstr "数据流结尾有未知错误\n"

#: sar.c:803
#, c-format
msgid "Invalid data format\n"
msgstr "无效的数据格式\n"

#: sar.c:807
#, c-format
msgid "Using a wrong data collector from a different sysstat version\n"
msgstr "正在使用来自不同版本 sysstat 的错误的数据收集器\n"

#: sar.c:831
#, c-format
msgid "Inconsistent input data\n"
msgstr "所取数据前后不一致\n"

#: sar.c:1014 pidstat.c:199
#, c-format
msgid "Requested activities not available\n"
msgstr "所需的运行记录无法获得\n"

#: sar.c:1272
#, c-format
msgid "-f and -o options are mutually exclusive\n"
msgstr "-f 和 -o 选项不能同时使用\n"

#: sar.c:1278
#, c-format
msgid "Not reading from a system activity file (use -f option)\n"
msgstr "无法查看系统活动记录文件 (用 -f 选项)\n"

#: sar.c:1410
#, c-format
msgid "Cannot find the data collector (%s)\n"
msgstr "无法找到数据收集器 (%s)\n"

#: rd_stats.c:2170
#, c-format
msgid "Cannot handle so many processors!\n"
msgstr "处理器太多，无法处理！\n"

#: pr_stats.c:2348 pr_stats.c:2361
msgid "Summary"
msgstr "总计"

#: pr_stats.c:2399
msgid "Other devices not listed here"
msgstr "未在此列出的其它设备"

#: sa_common.c:917
#, c-format
msgid "Error while reading system activity file: %s\n"
msgstr "查看系统运行记录时出错: %s\n"

#: sa_common.c:927
#, c-format
msgid "End of system activity file unexpected\n"
msgstr "系统运行记录文件的结尾有未知错误\n"

#: sa_common.c:946
#, c-format
msgid "File created by sar/sadc from sysstat version %d.%d.%d"
msgstr "文件由 sysstat 版本 %d.%d.%d 中的 sar/sadc 创建。"

#: sa_common.c:977
#, c-format
msgid "Invalid system activity file: %s\n"
msgstr "无效的系统运行记录文件: %s\n"

#: sa_common.c:984
#, c-format
msgid "Current sysstat version can no longer read the format of this file (%#x)\n"
msgstr "当前版本的 sysstat 已无法读取此文件格式 (%#x)\n"

#: sa_common.c:1216
#, c-format
msgid "Requested activities not available in file %s\n"
msgstr "所需的运行记录在此文件 %s 中无法获得\n"

#: pidstat.c:81
#, c-format
msgid ""
"Options are:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -r ] [ -s ] [ -t ] [ -u ] [ -V ]\n"
"[ -w ] [ -C <command> ] [ -p { <pid> [,...] | SELF | ALL } ]\n"
"[ -T { TASK | CHILD | ALL } ]\n"
msgstr ""
"选项：\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -r ] [ -s ] [ -t ] [ -u ] [ -V ]\n"
"[ -w ] [ -C <命令> ] [ -p { <进程号> [,...] | SELF | ALL } ]\n"
"[ -T { TASK | CHILD | ALL } ]\n"

#~ msgid ""
#~ "Options are:\n"
#~ "[ --debuginfo ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
#~ msgstr ""
#~ "选项:\n"
#~ "[  --debuginfo ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#~ msgid "\t-m\tPower management statistics\n"
#~ msgstr "\t-m\t电源管理信息状况\n"

#~ msgid "-x and -p options are mutually exclusive\n"
#~ msgstr "-x 和 -p 选项不能同时使用\n"

#~ msgid ""
#~ "\t-n { DEV | EDEV | NFS | NFSD | SOCK |\n"
#~ "\t     IP | EIP | ICMP | EICMP | TCP | ETCP | UDP | ALL }\n"
#~ "\t\tNetwork statistics\n"
#~ msgstr ""
#~ "\t-n { DEV | EDEV | NFS | NFSD | SOCK |\n"
#~ "\t     IP | EIP | ICMP | EICMP | TCP | ETCP | UDP | ALL }\n"
#~ "\t\t网络信息状况\n"

#~ msgid "Time: %s\n"
#~ msgstr "时间: %s\n"

#~ msgid "Usage:"
#~ msgstr "用法:"

#~ msgid "options..."
#~ msgstr "选项..."

#~ msgid "interval"
#~ msgstr "间隔"

#~ msgid "count"
#~ msgstr "次数"

#~ msgid "device"
#~ msgstr "设备名"

#~ msgid "cpu"
#~ msgstr "cpu"

#~ msgid "command"
#~ msgstr "命令"

#~ msgid "outfile"
#~ msgstr "输出文件"

#~ msgid "comment"
#~ msgstr "注释"

#~ msgid "datafile"
#~ msgstr "数据文件"

#~ msgid "hh:mm:ss"
#~ msgstr "时:分:秒"

#~ msgid "sar_options..."
#~ msgstr "sar 选项..."

#~ msgid "Activity flag: %#x\n"
#~ msgstr "运行标记: %#x\n"

#~ msgid "Number of CPU: %u\n"
#~ msgstr "CPU 数量: %u\n"

#~ msgid "Number of interrupts per CPU: %u\n"
#~ msgstr "每个 CPU 的中断数目: %u\n"

#~ msgid "Number of disks: %u\n"
#~ msgstr "磁盘数量: %u\n"

#~ msgid "Number of serial lines: %u\n"
#~ msgstr "串行数量: %u\n"

#~ msgid "Number of network interfaces: %u\n"
#~ msgstr "网卡数量: %u\n"

#~ msgid "int"
#~ msgstr "整型数"

#~ msgid "filename"
#~ msgstr "文件名"

#~ msgid "Not an SMP machine...\n"
#~ msgstr "非对称多处理机器...\n"
