/*
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that: (1) source code
 * distributions retain the above copyright notice and this paragraph
 * in its entirety, and (2) distributions including binary code include
 * the above copyright notice and this paragraph in its entirety in
 * the documentation or other materials provided with the distribution.
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND
 * WITHOUT ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, WITHOUT
 * LIMITATION, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE.
 *
 * Original code by <PERSON><PERSON> (<EMAIL>)
 * Turned into common "text protocol" code, which this uses, by
 * <PERSON>.
 */

/* \summary: Session Initiation Protocol (SIP) printer */

#ifdef HAVE_CONFIG_H
#include <config.h>
#endif

#include "netdissect-stdinc.h"

#include "netdissect.h"

static const char *sipcmds[] = {
	"ACK",
	"BY<PERSON>",
	"<PERSON><PERSON><PERSON>",
	"<PERSON>O",
	"<PERSON>F<PERSON>",
	"INVITE",
	"<PERSON><PERSON><PERSON><PERSON>",
	"NOTIFY",
	"OPTIONS",
	"PRAC<PERSON>",
	"QAUTH",
	"REF<PERSON>",
	"REGISTER",
	"SPRACK",
	"SUBSCRIBE",
	"UPDATE",
	"PUBLISH",
	NULL
};

void
sip_print(netdissect_options *ndo, const u_char *pptr, u_int len)
{
	ndo->ndo_protocol = "sip";
	txtproto_print(ndo, pptr, len, sipcmds, RESP_CODE_SECOND_TOKEN);
}
