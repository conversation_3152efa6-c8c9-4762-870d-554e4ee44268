# Compiling libpcap on AIX

* Autoconf is expected to work everywhere.
* Neither AIX lex nor AIX yacc nor AIX m4 are suitable.

## AIX 7.1

* libpcap build fails with rpcapd enabled.
* GNU M4 1.4.17 works.
* flex 2.6.4 and GNU Bison 3.5.1 work.
* CMake 3.16.0 works.
* GCC 8.3.0 works, XL C 12.1.0 works.

## AIX 7.2

* libpcap build fails with rpcapd enabled.
* GNU M4 1.4.17 works.
* flex 2.5.35 and GNU Bison 3.0.4 work.
* GCC 7.2.0 works, XL C 13.1.3 works.

## Other AIX-related information

Using BPF:

(1) AIX version of BPF is undocumented and somewhat unstandard; the
    current BPF support code includes changes that should work around
    that; however, it is no longer possible to verify this is the case.

    Note that the BPF driver and the "/dev/bpf" devices might not exist
    on your machine; AIX's tcpdump loads the driver and creates the
    devices if they don't already exist.  Our libpcap should do the
    same, and the configure script should detect that it's on an AIX
    system and choose BPF even if the devices aren't there.

(2) If libpcap doesn't compile on your machine when configured to use
    BPF, or if the workarounds fail to make it work correctly, you
    should <NAME_EMAIL> a detailed bug
    report (if the compile fails, send us the compile error messages;
    if it compiles but fails to work correctly, send us as detailed as
    possible a description of the symptoms, including indications of the
    network link-layer type being wrong or time stamps being wrong).

    If you fix the problems yourself, please submit a patch by forking
    the branch at

	https://github.com/the-tcpdump-group/libpcap/tree/master

    and issuing a pull request, so we can incorporate the fixes into the
    next release.

    If you don't fix the problems yourself, you can, as a workaround,
    make libpcap use DLPI instead of BPF.

    This can be done by specifying the flag:

       --with-pcap=dlpi

    to the "configure" script for libpcap.

If you use DLPI:

(1) It is a good idea to have the latest version of the DLPI driver on
    your system, since certain versions may be buggy and cause your AIX
    system to crash.  DLPI is included in the fileset bos.rte.tty.

(2) After compiling libpcap, you need to make sure that the DLPI driver
    is loaded.  Type:

	    strload -q -d dlpi

    If the result is:

	    dlpi: yes

    then the DLPI driver is loaded correctly.

    If it is:

	    dlpi: no

    Then you need to type:

	    strload -f /etc/dlpi.conf

    Check again with strload -q -d dlpi that the dlpi driver is loaded.

    Alternatively, you can uncomment the lines for DLPI in
    /etc/pse.conf and reboot the machine; this way DLPI will always
    be loaded when you boot your system.

(3) There appears to be a problem in the DLPI code in some versions of
    AIX, causing a warning about DL_PROMISC_MULTI failing; this might
    be responsible for DLPI not being able to capture outgoing packets.
