# @PACKA<PERSON>_NAME@-@PACKAGE_VERSION@ configuration file.

# How long to keep log files (in days).
# If value is greater than 28, then use sadc's option -D to prevent older
# data files from being overwritten. See sadc(8) and sysstat(5) manual pages.
HISTORY=@HISTORY@

# Compress (using xz, gzip or bzip2) sa and sar files older than (in days):
COMPRESSAFTER=@COMPRESSAFTER@

# Parameters for the system activity data collector (see sadc manual page)
# which are used for the generation of log files.
SADC_OPTIONS="@COLLECT_ALL@ @SADC_OPT@"

# Directory where sa and sar files are saved. The directory must exist.
SA_DIR=@SA_DIR@

# Compression program to use.
ZIP="@ZIP@"

# By default sa2 script generates yesterday's summary, since the cron job
# usually runs right after midnight. If you want sa2 to generate the summary
# of the same day (for example when cron job runs at 23:53) set this variable.
#YESTERDAY=no

# By default sa2 script generates reports files (the so called sarDD files).
# Set this variable to false to disable reports generation.
#REPORTS=false
