    1  2016-12-08 12:28:41.189402 IP (tos 0x0, ttl 255, id 9459, offset 0, flags [none], proto UDP (17), length 422)
    *************.67 > *************.67: [udp sum ok] BOOTP/DHCP, Request from b8:27:eb:b8:53:c8, length 394, hops 1, xid 0x68c4847, Flags [none] (0x0000)
	  Client-IP *************
	  Gateway-IP *************
	  Client-Ethernet-Address b8:27:eb:b8:53:c8
	  Vendor-rfc1048 Extensions
	    Magic Cookie 0x63825363
	    DHCP-Message (53), length 1: Request
	    Client-ID (61), length 7: ether b8:27:eb:b8:53:c8
	    MSZ (57), length 2: 1472
	    MUD-URL (161), length 54: "https://mudctl.example.com/.well-known/mud/v1/rasbp101"
	    Vendor-Class (60), length 45: "dhcpcd-6.11.5:Linux-4.1.18-v7+:armv7l:BCM2709"
	    Hostname (12), length 11: "raspberry<PERSON>"
	    Unknown (145), length 1: 1
	    Parameter-Request (55), length 16: 
	      Subnet-Mask (1), Classless-Static-Route (121), Static-Route (33), Default-Gateway (3)
	      Domain-Name-Server (6), Hostname (12), Domain-Name (15), BR (28)
	      NTP (42), Lease-Time (51), Server-ID (54), RN (58)
	      RB (59), POSIX-TZ (100), TZ-Name (101), Unknown (119)
    2  2016-12-08 12:28:41.318491 IP (tos 0x0, ttl 64, id 10305, offset 0, flags [DF], proto UDP (17), length 338)
    *************.67 > *************.67: [udp sum ok] BOOTP/DHCP, Reply, length 310, hops 1, xid 0x68c4847, Flags [none] (0x0000)
	  Client-IP *************
	  Your-IP *************
	  Server-IP *************
	  Gateway-IP *************
	  Client-Ethernet-Address b8:27:eb:b8:53:c8
	  Vendor-rfc1048 Extensions
	    Magic Cookie 0x63825363
	    DHCP-Message (53), length 1: ACK
	    Server-ID (54), length 4: *************
	    Lease-Time (51), length 4: 600
	    Subnet-Mask (1), length 4: ***************
	    Default-Gateway (3), length 4: *************
	    Domain-Name-Server (6), length 4: *************
	    Domain-Name (15), length 19: "ofcourseimright.com"
	    TZ-Name (101), length 13: "Europe/Berlin"
