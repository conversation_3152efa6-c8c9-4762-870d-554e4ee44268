#include <ctype.h>
#include <curses.h>
#ifndef NUMA_DISABLE
#include <dlfcn.h>
#endif
#include <errno.h>
#include <fcntl.h>
#include <float.h>
#include <limits.h>
#include <pwd.h>
#include <signal.h>
#include <stdarg.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <term.h>            // foul sob, defines all sorts of stuff...
#include <math.h>
#undef    tab
#undef    TTY
#include <termios.h>
#include <time.h>
#include <unistd.h>

#include <sys/ioctl.h>
#include <sys/resource.h>
#include <sys/select.h>      // also available via <sys/types.h>
#include <sys/time.h>
#include <sys/types.h>       // also available via <stdlib.h>
typedef          long long SIC_t;
 #define TRIMz(x)  ((tz = (SIC_t)(x)) < 0 ? 0 : tz)
   SIC_t u_frme, s_frme, n_frme, i_frme, w_frme, x_frme, y_frme, z_frme, tot_frme, tz;
   float scale;

#ifndef OFF_NUMASKIP
static int Numa_node_of_cpu(int num) { return (1 == (num % 4)) ? 0 : (num % 4); }
#else
static int Numa_node_of_cpu(int num) { return (num % 4); }
#endif
int smp_num_cpus;
#define CAPBUFSIZ    32
static int         Cpu_faux_tot;
typedef unsigned char FLG_t;
#define CAPTABMAX  9    
#define WINNAMSIZ  4 
#define CLRBUFSIZ    64
#define PFLAGSSIZ    80
#define GROUPSMAX  4     
#define SCREENMAX   512
static int Numa_node_tot;
typedef unsigned long long TIC_t;
typedef          long long SIC_t;
#define TICS_EDGE  20
#define CHKw(q,f)    (int)((q)->rc.winflags & (f))
#define View_CPUNOD  0x400000 
#define GRPNAMSIZ  WINNAMSIZ+2 
#define SIGNAL_STRING
#define QUICK_THREADS

typedef struct RCW_t {  // the 'window' portion of an rcfile
   int    sortindx,               // sort field (represented as procflag)
          winflags,               // 'view', 'show' and 'sort' mode flags
          maxtasks,               // user requested maximum, 0 equals all
          graph_cpus,             // 't' - View_STATES supplementary vals
          graph_mems,             // 'm' - View_MEMORY supplememtary vals
          summclr,                // a colors 'number' used for summ info
          msgsclr,                //             "           in msgs/pmts
          headclr,                //             "           in cols head
          taskclr;                //             "           in task rows
   char   winname [WINNAMSIZ],    // name for the window, user changeable
          fieldscur [PFLAGSSIZ];  // the fields for display & their order
} RCW_t;

typedef struct RCF_t {
   char   id;                   // rcfile version id
   int    mode_altscr;          // 'A' - Alt display mode (multi task windows)
   int    mode_irixps;          // 'I' - Irix vs. Solaris mode (SMP-only)
   float  delay_time;           // 'd'/'s' - How long to sleep twixt updates
   int    win_index;            // Curwin, as index
   RCW_t  win [GROUPSMAX];      // a 'WIN_t.rc' for each window
   int    fixed_widest;         // 'X' - wider non-scalable col addition
   int    summ_mscale;          // 'E' - scaling of summary memory values
   int    task_mscale;          // 'e' - scaling of process memory values
   int    zero_suppress;        // '0' - suppress scaled zeros toggle
} RCF_t;

        /* This structure stores configurable information for each window.
           By expending a little effort in its creation and user requested
           maintenance, the only real additional per frame cost of having
           windows is an extra sort -- but that's just on pointers! */
/*
typedef struct WIN_t {
   FLG_t  pflgsall [PFLAGSSIZ],        // all 'active/on' fieldscur, as enum
          procflgs [PFLAGSSIZ];        // fieldscur subset, as enum
   RCW_t  rc;                          // stuff that gets saved in the rcfile
   int    winnum,          // a window's number (array pos + 1)
          winlines,        // current task window's rows (volatile)
          maxpflgs,        // number of displayed procflgs ("on" in fieldscur)
          totpflgs,        // total of displayable procflgs in pflgsall array
          begpflg,         // scrolled beginning pos into pflgsall array
          endpflg,         // scrolled ending pos into pflgsall array
          begtask,         // scrolled beginning pos into Frame_maxtask
#ifndef SCROLLVAR_NO
          varcolbeg,       // scrolled position within variable width col
#endif
          varcolsz,        // max length of variable width column(s)
          usrseluid,       // validated uid for 'u/U' user selection
          usrseltyp,       // the basis for matching above uid
          usrselflg,       // flag denoting include/exclude matches
          hdrcaplen;       // column header xtra caps len, if any
   char   capclr_sum [CLRBUFSIZ],      // terminfo strings built from
          capclr_msg [CLRBUFSIZ],      //   RCW_t colors (& rebuilt too),
          capclr_pmt [CLRBUFSIZ],      //   but NO recurring costs !
          capclr_hdr [CLRBUFSIZ],      //   note: sum, msg and pmt strs
          capclr_rowhigh [CLRBUFSIZ],  //         are only used when this
          capclr_rownorm [CLRBUFSIZ],  //         window is the 'Curwin'!
          cap_bold [CAPBUFSIZ],        // support for View_NOBOLD toggle
          grpname [GRPNAMSIZ],         // window number:name, printable
#ifdef USE_X_COLHDR
          columnhdr [ROWMINSIZ],       // column headings for procflgs
#else
          columnhdr [SCREENMAX],       // column headings for procflgs
#endif
         *captab [CAPTABMAX];          // captab needed by show_special()
   struct osel_s *osel_1st;            // other selection criteria anchor
   int    osel_tot;                    // total of other selection criteria
   char  *osel_prt;                    // other stuff printable as status line
   char  *findstr;                     // window's current/active search string
   int    findlen;                     // above's strlen, without call overhead
   proc_t **ppt;                       // this window's proc_t ptr array
   struct WIN_t *next,                 // next window in window stack
                *prev;                 // prior window in window stack
} WIN_t;
*/

//static WIN_t *Curwin;
 typedef struct CT_t {
   /* other kernels: u == user/us, n == nice/ni, s == system/sy, i == idle/id
      2.5.41 kernel: w == IO-wait/wa (io wait time)
      2.6.0  kernel: x == hi (hardware irq time), y == si (software irq time)
      2.6.11 kernel: z == st (virtual steal time) */
   TIC_t u, n, s, i, w, x, y, z;  // as represented in /proc/stat
#ifndef CPU_ZEROTICS
   SIC_t tot;                     // total from /proc/stat line 1
#endif
} CT_t;


 typedef struct CPU_t {
   CT_t cur;                      // current frame's cpu tics
   CT_t sav;                      // prior frame's cpu tics
#ifndef CPU_ZEROTICS
   SIC_t edge;                    // tics adjustment threshold boundary
#endif
   int id;                        // cpu number (0 - nn), or numa active flag
#ifndef NUMA_DISABLE
   int node;                      // the numa node it belongs to
#endif
} CPU_t;

#define MALLOC __attribute__ ((__malloc__))

static void *alloc_c (size_t num) MALLOC;
static void *alloc_c (size_t num) {
   void *pv;

   if (!num) ++num;
   if (!(pv = calloc(1, num)))
      perror("alloc_c failed");
   return pv;
} // end: alloc_c


static void *alloc_r (void *ptr, size_t num) MALLOC;
static void *alloc_r (void *ptr, size_t num) {
   void *pv;

   if (!num) ++num;
   if (!(pv = realloc(ptr, num)))
      perror("alloc_r failed");;
   return pv;
} // end: alloc_r

        /*
         * This guy's modeled on libproc's 'eight_cpu_numbers' function except
         * we preserve all cpu data in our CPU_t array which is organized
         * as follows:
         *    cpus[0] thru cpus[n] == tics for each separate cpu
         *    cpus[sumSLOT]        == tics from the 1st /proc/stat line
         *  [ and beyond sumSLOT   == tics for each cpu NUMA node ] */
static CPU_t *cpus_refresh (CPU_t *cpus) {
 #define sumSLOT ( smp_num_cpus )
 #define totSLOT ( 1 + smp_num_cpus + Numa_node_tot)
   static FILE *fp = NULL;
   static int siz, sav_slot = -1;
   static char *buf;
   CPU_t *sum_ptr;                               // avoid gcc subscript bloat
   int i, num, tot_read;
#ifndef NUMA_DISABLE
   int node;
#endif
   char *bp;

   /*** hotplug_acclimated ***/
   if (sav_slot != sumSLOT) {
      sav_slot = sumSLOT;
      //zap_fieldstab();
      if (fp) { fclose(fp); fp = NULL; }
      if (cpus) { free(cpus); cpus = NULL; }
   }

   /* by opening this file once, we'll avoid the hit on minor page faults
      (sorry Linux, but you'll have to close it for us) */
   if (!fp) {
      if (!(fp = fopen("/proc/stat", "r")))
 		 perror("fopen /proc/stat failed");
      /* note: we allocate one more CPU_t via totSLOT than 'cpus' so that a
               slot can hold tics representing the /proc/stat cpu summary */
      cpus = alloc_c(totSLOT * sizeof(CPU_t));
   }
   rewind(fp);
   fflush(fp);

 #define buffGRW 1024
   /* we slurp in the entire directory thus avoiding repeated calls to fgets,
      especially in a massively parallel environment.  additionally, each cpu
      line is then frozen in time rather than changing until we get around to
      accessing it.  this helps to minimize (not eliminate) most distortions. */
   tot_read = 0;
   if (buf) buf[0] = '\0';
   else buf = alloc_c((siz = buffGRW));
   while (0 < (num = fread(buf + tot_read, 1, (siz - tot_read), fp))) {
      tot_read += num;
      if (tot_read < siz) break;
      buf = alloc_r(buf, (siz += buffGRW));
   };
   buf[tot_read] = '\0';
   bp = buf;
 #undef buffGRW

   // remember from last time around
   sum_ptr = &cpus[sumSLOT];
   memcpy(&sum_ptr->sav, &sum_ptr->cur, sizeof(CT_t));
   // then value the last slot with the cpu summary line
   if (4 > sscanf(bp, "cpu %Lu %Lu %Lu %Lu %Lu %Lu %Lu %Lu"
      , &sum_ptr->cur.u, &sum_ptr->cur.n, &sum_ptr->cur.s
      , &sum_ptr->cur.i, &sum_ptr->cur.w, &sum_ptr->cur.x
      , &sum_ptr->cur.y, &sum_ptr->cur.z))
         perror("scanf failed");;
#ifndef CPU_ZEROTICS
   sum_ptr->cur.tot = sum_ptr->cur.u + sum_ptr->cur.s
      + sum_ptr->cur.n + sum_ptr->cur.i + sum_ptr->cur.w
      + sum_ptr->cur.x + sum_ptr->cur.y + sum_ptr->cur.z;
   /* if a cpu has registered substantially fewer tics than those expected,
      we'll force it to be treated as 'idle' so as not to present misleading
      percentages. */
   sum_ptr->edge =
      ((sum_ptr->cur.tot - sum_ptr->sav.tot) / smp_num_cpus) / (100 / TICS_EDGE);
#endif

#ifndef NUMA_DISABLE
   // forget all of the prior node statistics (maybe)
   //if (CHKw(Curwin, View_CPUNOD))
      memset(sum_ptr + 1, 0, Numa_node_tot * sizeof(CPU_t));
#endif

   // now value each separate cpu's tics...
   for (i = 0; i < sumSLOT; i++) {
      CPU_t *cpu_ptr = &cpus[i];               // avoid gcc subscript bloat
#ifdef PRETEND8CPUS
      bp = buf;
#endif
      bp = 1 + strchr(bp, '\n');
      // remember from last time around
      memcpy(&cpu_ptr->sav, &cpu_ptr->cur, sizeof(CT_t));
      if (4 > sscanf(bp, "cpu%d %Lu %Lu %Lu %Lu %Lu %Lu %Lu %Lu", &cpu_ptr->id
         , &cpu_ptr->cur.u, &cpu_ptr->cur.n, &cpu_ptr->cur.s
         , &cpu_ptr->cur.i, &cpu_ptr->cur.w, &cpu_ptr->cur.x
         , &cpu_ptr->cur.y, &cpu_ptr->cur.z)) {
            memmove(cpu_ptr, sum_ptr, sizeof(CPU_t));
            break;        // tolerate cpus taken offline
      }

#ifndef CPU_ZEROTICS
      cpu_ptr->edge = sum_ptr->edge;
#endif
#ifdef PRETEND8CPUS
      cpu_ptr->id = i;
#endif
#ifndef NUMA_DISABLE
      /* henceforth, with just a little more arithmetic we can avoid
         maintaining *any* node stats unless they're actually needed */
      if (0
      && Numa_node_tot
      && -1 < (node = Numa_node_of_cpu(cpu_ptr->id))) {
         // use our own pointer to avoid gcc subscript bloat
         CPU_t *nod_ptr = sum_ptr + 1 + node;
         nod_ptr->cur.u += cpu_ptr->cur.u; nod_ptr->sav.u += cpu_ptr->sav.u;
         nod_ptr->cur.n += cpu_ptr->cur.n; nod_ptr->sav.n += cpu_ptr->sav.n;
         nod_ptr->cur.s += cpu_ptr->cur.s; nod_ptr->sav.s += cpu_ptr->sav.s;
         nod_ptr->cur.i += cpu_ptr->cur.i; nod_ptr->sav.i += cpu_ptr->sav.i;
         nod_ptr->cur.w += cpu_ptr->cur.w; nod_ptr->sav.w += cpu_ptr->sav.w;
         nod_ptr->cur.x += cpu_ptr->cur.x; nod_ptr->sav.x += cpu_ptr->sav.x;
         nod_ptr->cur.y += cpu_ptr->cur.y; nod_ptr->sav.y += cpu_ptr->sav.y;
         nod_ptr->cur.z += cpu_ptr->cur.z; nod_ptr->sav.z += cpu_ptr->sav.z;
#ifndef CPU_ZEROTICS
         /* yep, we re-value this repeatedly for each cpu encountered, but we
            can then avoid a prior loop to selectively initialize each node */
         nod_ptr->edge = sum_ptr->edge;
#endif
         cpu_ptr->node = node;
#ifndef OFF_NUMASKIP
         nod_ptr->id = -1;
#endif
      }
#endif
   } // end: for each cpu

   Cpu_faux_tot = i;      // tolerate cpus taken offline

   return cpus;
 #undef sumSLOT
 #undef totSLOT
} // end: cpus_refresh







        /*
         * State display *Helper* function to calc and display the state
         * percentages for a single cpu.  In this way, we can support
         * the following environments without the usual code bloat.
         *    1) single cpu machines
         *    2) modest smp boxes with room for each cpu's percentages
         *    3) massive smp guys leaving little or no room for process
         *       display and thus requiring the cpu summary toggle */
static void summary_hlp (CPU_t *cpu) {
   /* we'll trim to zero if we get negative time ticks,
      which has happened with some SMP kernels (pre-2.4?)
      and when cpus are dynamically added or removed */

   u_frme = TRIMz(cpu->cur.u - cpu->sav.u);
   s_frme = TRIMz(cpu->cur.s - cpu->sav.s);
   n_frme = TRIMz(cpu->cur.n - cpu->sav.n);
   i_frme = TRIMz(cpu->cur.i - cpu->sav.i);
   w_frme = TRIMz(cpu->cur.w - cpu->sav.w);
   x_frme = TRIMz(cpu->cur.x - cpu->sav.x);
   y_frme = TRIMz(cpu->cur.y - cpu->sav.y);
   z_frme = TRIMz(cpu->cur.z - cpu->sav.z);
   tot_frme = u_frme + s_frme + n_frme + i_frme + w_frme + x_frme + y_frme + z_frme;
   printf("cpu->id=%d tot_frme=%ld u_frme=%ld\n",cpu->id,tot_frme,u_frme);
#ifdef CPU_ZEROTICS
   if (1 > tot_frme) tot_frme = 1;
#else
   if (tot_frme < cpu->edge)
      tot_frme = u_frme = s_frme = n_frme = i_frme = w_frme = x_frme = y_frme = z_frme = 0;
   if (1 > tot_frme) i_frme = tot_frme = 1;
#endif
   scale = 100.0 / (float)tot_frme;
 //printf("(float)u_frme * scale=%f\n",(float)round(u_frme * scale*10)/10);      
   char s[4];
   sprintf(s, "%.1f", (float)round(u_frme * scale*10)/10);
   printf("s=%s\n",s);


   /* display some kinda' cpu state percentages
      (who or what is explained by the passed prefix) */
/*
   if (0) {
      static struct {
         const char *user, *syst, *type;
      } gtab[] = {
         { "%-.*s~7", "%-.*s~8", Graph_bars },
         { "%-.*s~4", "%-.*s~6", Graph_blks }
      };
      char user[SMLBUFSIZ], syst[SMLBUFSIZ], dual[MEDBUFSIZ];
      int ix = Curwin->rc.graph_cpus - 1;
      float pct_user = (float)(u_frme + n_frme) * scale,
            pct_syst = (float)s_frme * scale;
#ifndef QUICK_GRAPHS
      int num_user = (int)((pct_user * Graph_adj) + .5),
          num_syst = (int)((pct_syst * Graph_adj) + .5);
      if (num_user + num_syst > Graph_len) --num_syst;
      snprintf(user, sizeof(user), gtab[ix].user, num_user, gtab[ix].type);
      snprintf(syst, sizeof(syst), gtab[ix].syst, num_syst, gtab[ix].type);
#else
      snprintf(user, sizeof(user), gtab[ix].user, (int)((pct_user * Graph_adj) + .5), gtab[ix].type);
      snprintf(syst, sizeof(syst), gtab[ix].syst, (int)((pct_syst * Graph_adj) + .4), gtab[ix].type);
#endif
      snprintf(dual, sizeof(dual), "%s%s", user, syst);

      show_special(0, fmtmk("%%%s ~3%#5.1f~2/%-#5.1f~3 %3.0f[~1%-*s]~1\n"
         , pfx, pct_user, pct_syst, pct_user + pct_syst, Graph_len +4, dual));
   } else {
      show_special(0, fmtmk(Cpu_States_fmts, pfx
         , (float)u_frme * scale, (float)s_frme * scale
         , (float)n_frme * scale, (float)i_frme * scale
         , (float)w_frme * scale, (float)x_frme * scale
         , (float)y_frme * scale, (float)z_frme * scale));
   }
 #undef TRIMz
*/
} // end: summary_hlp

int main (){
   	  int ii;
	  smp_num_cpus = sysconf(_SC_NPROCESSORS_ONLN);
	 static CPU_t *smpcpu = NULL;
	 smpcpu = cpus_refresh(smpcpu); //返回一个已经采集完信息的指针
   	 summary_hlp(&smpcpu[smp_num_cpus]);
            // display each cpu's states separately, screen height permitting...
         for (ii = 0; ii < Cpu_faux_tot;ii++ ) {
             printf("i=%d\n ", ii);
            summary_hlp(&smpcpu[ii]);
         }
         




}
