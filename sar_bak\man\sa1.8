.TH SA1 8 "JUNE 2014" Linux "Linux User's Manual" -*- nroff -*-
.SH NAME
sa1 \- Collect and store binary data in the system activity daily data file.
.SH SYNOPSIS
.B /usr/local/lib64/sa/sa1 [ --boot |
.I interval
.I count
.B ]
.SH DESCRIPTION
The
.B sa1
command is a shell procedure variant of the
.B sadc
command and handles all of the flags and parameters of that command. The
.B sa1
command collects and stores binary data in the current standard
system activity daily data file.

The standard system activity daily data file is named
.I saDD
unless
.BR sadc 's
option
.B -D
is used, in which case its name is
.IR saYYYYMMDD ,
where YYYY stands for the current year, MM for the current month
and DD for the current day. By default it is located in the
.I /var/log/sa
directory.

The
.I interval
and
.I count
parameters specify that the record should be written
.I count
times at
.I interval
seconds. If no arguments are given to
.B sa1
then a single record is written.

The
.B sa1
command is designed to be started automatically by the cron command.

.SH OPTIONS
.IP --boot
This option tells
.B sa1
that the
.B sadc
command should be called without specifying the
.I interval
and
.I count
parameters in order to insert a dummy record, marking the time when the counters
restart from 0.

.SH EXAMPLE
To collect data (including those from disks) every 10 minutes,
place the following entry in your root crontab file:

.B 0,10,20,30,40,50 * * * * /usr/local/lib64/sa/sa1 1 1 -S DISK

.SH FILES
.I /var/log/sa/saDD
.br
.I /var/log/sa/saYYYYMMDD
.RS
The standard system activity daily data files and their default location.
YYYY stands for the current year, MM for the current month and DD for the
current day.
.SH AUTHOR
Sebastien Godard (sysstat <at> orange.fr)
.SH SEE ALSO
.BR sar (1),
.BR sadc (8),
.BR sa2 (8),
.BR sadf (1),
.BR sysstat (5)

.I http://pagesperso-orange.fr/sebastien.godard/
