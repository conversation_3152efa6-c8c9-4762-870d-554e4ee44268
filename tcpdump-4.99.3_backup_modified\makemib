#!/bin/sh
#
# Copyright (c) 1990, 1996
#	<PERSON>. All rights reserved.
# SMIv2 parsing copyright (c) 1999
#	<PERSON>.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
#
# 1. Redistributions of source code must retain the above copyright
#    notices, this list of conditions and the following disclaimer.
#
# 2. Redistributions in binary form must reproduce the above copyright
#    notices, this list of conditions and the following disclaimer in the
#    documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY THE AUTHORS ``AS IS'' AND ANY EXPRESS OR
# IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
# OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
# IN NO EVENT SHALL THE AUTHORS BE LIABLE FOR ANY DIRECT, INDIRECT,
# INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
# NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
# DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
# THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
# THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

#
# This script will read either ASN.1-style MIB files or the ".defs" files
# created by the ISODE "mosy" program on such files.
#
# The output of this script is the "mib.h" file used by tcpdumps' ASN.1/SNMP
# decoding code.
#
# This script needs to be run by "gawk" (GNU awk).  "nawk" will work, but
# dump will get a recursion error if you process LARGE mibs.  While it would
# by farily easy to rewrite this not to use recursion (and also easy to
# eliminate use of gsub and functions to use classic "awk"), you have to
# order the structure declarations in defined-first order for the compiler
# not to barf; too bad tsort doesn't take arguments.
#

cat << EOF
/*
 * This file was generated by tcpdump/makemib on `date`
 * You probably don't want to edit this by hand!
 *
 * struct mib somename = { desc, oid-octet, type, child-pointer, next-pointer
};
 */

EOF

awk '
BEGIN {
	debug=0;
	# for sanity, we prep the namespace with objects from RFC-1155
	# (we manually establish the root)
	oid["iso"]=1
	oidadd("org", "iso", 3)
	oidadd("dod", "org", 6)
	oidadd("internet", "dod", 1)
	oidadd("directory", "internet", 1)
	oidadd("mgmt", "internet", 2)
#XXX	oidadd("mib", "mgmt", 1)
	oidadd("mib-2", "mgmt", 1)
	oidadd("experimental", "internet", 3)
	oidadd("private", "internet", 4)
	oidadd("enterprises", "private", 1)
	oidadd("ip", "mib-2", 4)
	oidadd("transmission", "mib-2", 10)

	holddesc="none"
}

#
# Read mosy "*.defs" file.  mosy does all the parsing work; we just read
# its simple and straightforward output.  It would not be too hard to make
# tcpdump directly read mosy output, but...
#
# Ignore these unless the current file is called something.defs; false
# positives are too common in DESCRIPTIONs.

NF > 1 && index($2,".")>0 && FILENAME ~ /\.defs/ {
	# currently ignore items of the form "{ iso.3.6.1 }"
	if (split($2, p, ".") == 2) {
		oidadd($1, p[1], p[2])
	}
	next
}

#
# Must be a MIB file
# Make it easier to parse - used to be done by sed
{ sub(/--\*.*\*--/, ""); sub(/--.*/, ""); gsub(/[{}]/, " & "); }

#
# this next section is simple and naive, but does the job ok
#

# foo OBJECT IDENTIFIER ::= { baz 17 }
# or
# foo OBJECT IDENTIFIER ::=
# { baz 17 }
$2$3$4 == "OBJECTIDENTIFIER::=" {
	holddesc="none"
	if (NF == 8)
		oidadd($1, $6, $7)
	if (NF == 4)
		holddesc=$1
	next
}
$1 == "{" && holddesc != "none" && NF == 4 {
	oidadd(holddesc, $2, $3)
	holddesc="none"
}
#
# foo OBJECT IDENTIFIER
#  ::= { bar 1 }
$2$3 == "OBJECTIDENTIFIER" && $1 != "SYNTAX" && NF == 3 {
	holddesc=$1
}
#
# foo
# OBJECT IDENTIFIER ::= { bar 1 }
# a couple of heuristics to exclude single words in e.g. long
#  DESCRIPTION clauses
NF == 1 && $1 ~ "[a-z][a-z]*[A-Z]" && $1 !~ /[(){}.,]/ && holddesc == "none" {
	holddesc=$1
}
$1$2$3 == "OBJECTIDENTIFIER::=" && holddesc != "none" {
	oidadd(holddesc, $5, $6)
	holddesc="none"
}
#
# "normal" style
# foo OBJECT-TYPE ...
# ...
#   ::= { baz 5 }
$2 == "MODULE-IDENTITY" || $2 == "MODULE-COMPLIANCE" ||
	$2 == "OBJECT-IDENTITY" || $2 == "OBJECT-TYPE" ||
	$2 == "OBJECT-GROUP" ||
	$2 == "NOTIFICATION-TYPE" || $2 == "NOTIFICATION-GROUP" {
	holddesc=$1
}
$1 == "::=" && holddesc != "none" && NF == 5 {
	oidadd(holddesc, $3, $4)
	holddesc="none"
}
#
# foo ::= { baz 17 }
$2$3 == "::={" {
	oidadd($1,$4,$5)
	holddesc="none"
}


#
# End of the road - output the data.
#

END {
	print "struct obj"
	dump("iso")
	print "*mibroot = &_iso_obj;"
}

function inn(file) {
	if (file == "" || file == "-")
		return ""
	return " in " file
}

#
# add a new object to the tree
#
#		new OBJECT IDENTIFIER ::= { parent value }
#

function oidadd(new, parent, value) {
	# Ignore 0.0
	if (parent == "0" && value == 0)
		return
	if (debug)
		print "/* oidadd" inn(FILENAME) ":", new, "in", parent, "as", value, "line", $0, "*/"
	# use safe C identifiers
	gsub(/[-&\/]/,"",new)
	gsub(/[-&\/]/,"",parent)
	# check if parent missing
	if (oid[parent] == "") {
		printf "/* parse problem%s: no parent for %s.%s(%d) */\n", \
			inn(FILENAME), parent, new, value
		return
	}
	# check if parent.value already exists
	if (oid[new] > 0 && oid[new] != value) {
		printf "/* parse problem%s: dup %s.%s(%d) != old (%d) */\n", \
			inn(FILENAME), parent, new, value, oid[new]
		return
	}
	# check for new name for parent.value
	if (child[parent] != "") {
		for (sib = child[parent]; sib != ""; sib = sibling[sib])
			if (oid[sib] == value) {
				if (new != sib)
					printf "/* parse problem%s: new name" \
						" \"%s\"" \
						" for %s.%s(%d) ignored */\n", \
						inn(FILENAME), new, parent, \
						sib, value
				return
			}
	}

	oid[new]=value
	if (child[parent] == "") {
		child[parent] = new
	} else {
		sibling[new] = child[parent]
		child[parent] = new
	}
}

#
# old(?) routine to recurse down the tree (in postfix order for convenience)
#

function dump(item, c, s) {
#	newitem=sofar"."item"("oid[item]")"
#	printf "/* %s c=%s s=%s */\n", newitem, child[item], sibling[item]
	c="NULL"
	if (child[item] != "") {
		dump(child[item])
		c = "&_"child[item]"_obj"
	}
	s="NULL"
	if (sibling[item] != "") {
		dump(sibling[item])
		s = "&_"sibling[item]"_obj"
	}
	printf "_%s_obj = {\n\t\"%s\", %d, 0,\n\t%s, %s\n},\n", \
		item, item, oid[item], c, s
}
' $@
exit 0
