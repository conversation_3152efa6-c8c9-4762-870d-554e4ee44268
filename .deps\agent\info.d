agent/info.o: agent/info.c agent/sysinfo.h agent/kernel.h \
 agent/../sar/sa1.h agent/pwcache.h agent/../libpcap/pcap-int.h \
 libpcap/pcap/pcap.h libpcap/pcap/funcattrs.h \
 libpcap/pcap/compiler-tests.h libpcap/pcap/pcap-inttypes.h \
 libpcap/pcap/socket.h libpcap/pcap/bpf.h libpcap/pcap/dlt.h \
 agent/../libpcap/varattrs.h agent/../libpcap/fmtutils.h \
 agent/../libpcap/pcap/funcattrs.h agent/../libpcap/portability.h \
 agent/../libpcap/pcap.h agent/../tcpdump-5.0.0/my.h agent/top.h \
 agent/stat.h agent/procps-private.h agent/top_nls.h agent/meminfo.h \
 agent/search.h agent/nls.h agent/config.h agent/pids.h agent/readproc.h \
 agent/misc.h
agent/sysinfo.h:
agent/kernel.h:
agent/../sar/sa1.h:
agent/pwcache.h:
agent/../libpcap/pcap-int.h:
libpcap/pcap/pcap.h:
libpcap/pcap/funcattrs.h:
libpcap/pcap/compiler-tests.h:
libpcap/pcap/pcap-inttypes.h:
libpcap/pcap/socket.h:
libpcap/pcap/bpf.h:
libpcap/pcap/dlt.h:
agent/../libpcap/varattrs.h:
agent/../libpcap/fmtutils.h:
agent/../libpcap/pcap/funcattrs.h:
agent/../libpcap/portability.h:
agent/../libpcap/pcap.h:
agent/../tcpdump-5.0.0/my.h:
agent/top.h:
agent/stat.h:
agent/procps-private.h:
agent/top_nls.h:
agent/meminfo.h:
agent/search.h:
agent/nls.h:
agent/config.h:
agent/pids.h:
agent/readproc.h:
agent/misc.h:
