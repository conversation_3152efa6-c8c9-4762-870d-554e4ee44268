.TH SA2 8 "AUGUST 2013" Linux "Linux User's Manual" -*- nroff -*-
.SH NAME
sa2 \- Write a daily report in the /var/log/sa directory.
.SH SYNOPSIS
.B /usr/local/lib64/sa/sa2
.SH DESCRIPTION
The
.B sa2
command is a shell procedure variant of the
.B sar
command which writes a daily report in the
.IR /var/log/sa/sar dd
file, where the dd parameter indicates the current day.
It will also remove reports more than one week old by default.
You can however keep reports for a longer (or a shorter) period by setting
the HISTORY environment variable. Read the
.BR sysstat (5)
manual page for details.

The
.B sa2
command accepts most of the flags and parameters of the
.B sar
command.

The
.B sa2
command is designed to be started automatically by the cron command.

.SH EXAMPLES
To run the
.B sa2
command daily, place the following entry in your root crontab file:

.B 5 19 * * 1-5 /usr/local/lib64/sa/sa2 -A &

This will generate a daily report called
.IR /var/log/sa/sar dd.
.SH FILES
.IR /var/log/sa/sar dd
.RS
Indicate the daily report file, where the
.B dd
parameter is a number representing the day of the month.
.SH AUTHOR
Sebastien <PERSON> (sysstat <at> orange.fr)
.SH SEE ALSO
.BR sar (1),
.BR sadc (8),
.BR sa1 (8),
.BR sadf (1),
.BR sysstat (5)

.I http://pagesperso-orange.fr/sebastien.godard/
