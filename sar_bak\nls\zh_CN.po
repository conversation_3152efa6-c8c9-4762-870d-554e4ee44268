# Chinese translations for sysstat package
# sysstat 软件包的简体中文翻译.
# Copyright (C) 2010 THE sysstat'S COPYRIGHT HOLDER
# This file is distributed under the same license as the sysstat package.
# <PERSON> <<EMAIL>>, 2016.
msgid ""
msgstr ""
"Project-Id-Version: sysstat 11.5.1\n"
"Report-Msgid-Bugs-To: sysstat <at> orange.fr\n"
"POT-Creation-Date: 2016-09-23 15:07+0200\n"
"PO-Revision-Date: 2016-10-11 08:14China Standard Time\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Chinese (simplified) <<EMAIL>>\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"

#: iostat.c:86 cifsiostat.c:70 mpstat.c:98 sar.c:96 pidstat.c:87 tapestat.c:89
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ]\n"
msgstr "用法: %s [ 选项 ] [ <时间间隔> [ <次数> ] ]\n"

#: iostat.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ -o JSON ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ] [ --debuginfo ]\n"
msgstr ""
"选项:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ -o JSON ]\n"
"[ [ -H ] -g <用户组名> ] [ -p [ <设备> [,...] | ALL ] ]\n"
"[ <设备> [...] | ALL ] [ --debuginfo ]\n"

#: iostat.c:95
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ -o JSON ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ]\n"
msgstr ""
"选项:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ -o JSON ]\n"
"[ [ -H ] -g <用户组名> ] [ -p [ <设备> [,...] | ALL ] ]\n"
"[ <设备> [...] | ALL ]\n"

#: iostat.c:326
#, c-format
msgid "Cannot find disk data\n"
msgstr "无法找到磁盘数据\n"

#: iostat.c:1660 sa_common.c:1650
#, c-format
msgid "Invalid type of persistent device name\n"
msgstr "固有设备名类型无效\n"

#: sadf_misc.c:749
#, c-format
msgid "System activity data file: %s (%#x)\n"
msgstr "系统运行记录数据文件: %s (%#x)\n"

#: sadf_misc.c:758
#, c-format
msgid "Genuine sa datafile: %s (%x)\n"
msgstr "Genuine sa 数据文件: %s (%x)\n"

#: sadf_misc.c:759
msgid "no"
msgstr "否"

#: sadf_misc.c:759
msgid "yes"
msgstr "是"

#: sadf_misc.c:762
#, c-format
msgid "Host: "
msgstr "主机:"

#: sadf_misc.c:769
#, c-format
msgid "Number of CPU for last samples in file: %u\n"
msgstr "文件中最后一次取样的CPU 数: %u\n"

#: sadf_misc.c:775
#, c-format
msgid "File date: %s\n"
msgstr "文件日期: %s\n"

#: sadf_misc.c:778
#, c-format
msgid "File time: "
msgstr "文件时间: "

#: sadf_misc.c:783
#, c-format
msgid "Size of a long int: %d\n"
msgstr "长整型数的字节大小: %d\n"

#: sadf_misc.c:789
#, c-format
msgid "List of activities:\n"
msgstr "运行记录列表:\n"

#: sadf_misc.c:802
#, c-format
msgid "\t[Unknown activity format]"
msgstr "\\t[未知的记录格式]"

#: sadc.c:89
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
msgstr "用法: %s [ 选项 ] [ <时间间隔> [ <次数> ] ] [ <输出文件> ]\n"

#: sadc.c:92
#, c-format
msgid ""
"Options are:\n"
"[ -C <comment> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"
msgstr ""
"选项:\n"
"[ -C <注释> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"

#: sadc.c:267
#, c-format
msgid "Cannot write data to system activity file: %s\n"
msgstr "无法将数据写入系统运行记录文件: %s\n"

#: sadc.c:563
#, c-format
msgid "Cannot write system activity file header: %s\n"
msgstr "无法写系统运行记录文件开始部分: %s\n"

#: sadc.c:763 sadc.c:772 sadc.c:839 ioconf.c:510 rd_stats.c:69
#: sa_common.c:1264 count.c:118
#, c-format
msgid "Cannot open %s: %s\n"
msgstr "无法打开 %s: %s\n"

#: sadc.c:1019
#, c-format
msgid "Cannot append data to that file (%s)\n"
msgstr "无法向文件 (%s) 中追加记录\n"

#: common.c:74
#, c-format
msgid "sysstat version %s\n"
msgstr "sysstat 版本 %s\n"

#: cifsiostat.c:74
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"
msgstr ""
"选项:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"

#: cifsiostat.c:77
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
msgstr ""
"选项:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#: mpstat.c:101
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -o JSON ] [ -P { <cpu> [,...] | ON | ALL } ]\n"
msgstr ""
"选项:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -o JSON ] [ -P { <cpu> [,...] | ON | ALL } ]\n"

#: mpstat.c:1210 sar.c:358 pidstat.c:2303
msgid "Average:"
msgstr "平均时间:"

#: mpstat.c:1636
#, c-format
msgid "Not that many processors!\n"
msgstr "处理器实在太多！\n"

#: sadf.c:86
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <datafile> | -[0-9]+ ]\n"
msgstr "用法: %s [ 选项 ] [ <时间间隔> [ <次数> ] ] [ <数据文件> | -[0-9]+ ]\n"

#: sadf.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -C ] [ -c | -d | -g | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -O <opts> [,...] ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
"[ -- <sar_options> ]\n"
msgstr ""
"Options are:\n"
"[ -C ] [ -c | -d | -g | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -O <选项> [,...] ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -s [ <时:分[:秒]> ] ] [ -e [ <时:分[:秒]> ] ]\n"
"[ -- <sar_options> ]\n"

#: sar.c:111
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ MOUNT ] ] [ -H ] [ -h ]\n"
"[ -p ] [ -q ] [ -R ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --sadc ]\n"
"[ -I { <int> [,...] | SUM | ALL | XALL } ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -m { <keyword> [,...] | ALL } ] [ -n { <keyword> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filename> ] | -o [ <filename> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
msgstr ""
"选项:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ MOUNT ] ] [ -H ] [ -h ]\n"
"[ -p ] [ -q ] [ -R ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --sadc ]\n"
"[ -I { <中断> [,...] | SUM | ALL | XALL } ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -m { <关键字> [,...] | ALL } ] [ -n { <关键字> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <文件名> ] | -o [ <设备名> ] | -[0-9]+ ]\n"
"[ -i <时间间隔> ] [ -s [ <时:分[:秒]> ] ] [ -e [ <时:分[:秒]> ] ]\n"

#: sar.c:134
#, c-format
msgid "Main options and reports:\n"
msgstr "主选项和报告：\n"

#: sar.c:135
#, c-format
msgid "\t-B\tPaging statistics\n"
msgstr "\t-B\t分页状况\n"

#: sar.c:136
#, c-format
msgid "\t-b\tI/O and transfer rate statistics\n"
msgstr "\t-b\tI/O 和传输速率信息状况\n"

#: sar.c:137
#, c-format
msgid "\t-d\tBlock devices statistics\n"
msgstr "\t-d\t块设备状况\n"

#: sar.c:138
#, c-format
msgid "\t-F [ MOUNT ]\n"
msgstr "\t-F [ MOUNT ]\n"

#: sar.c:139
#, c-format
msgid "\t\tFilesystems statistics\n"
msgstr "\t\t文件系统统计信息\n"

#: sar.c:140
#, c-format
msgid "\t-H\tHugepages utilization statistics\n"
msgstr "\t-H\t交换空间利用率\n"

#: sar.c:141
#, c-format
msgid ""
"\t-I { <int> | SUM | ALL | XALL }\n"
"\t\tInterrupts statistics\n"
msgstr ""
"\t-I { <中断> | SUM | ALL | XALL }\n"
"\t\t中断信息状况\n"

#: sar.c:143
#, c-format
msgid ""
"\t-m { <keyword> [,...] | ALL }\n"
"\t\tPower management statistics\n"
"\t\tKeywords are:\n"
"\t\tCPU\tCPU instantaneous clock frequency\n"
"\t\tFAN\tFans speed\n"
"\t\tFREQ\tCPU average clock frequency\n"
"\t\tIN\tVoltage inputs\n"
"\t\tTEMP\tDevices temperature\n"
"\t\tUSB\tUSB devices plugged into the system\n"
msgstr ""
"\t-m { <关键字> [,...] | ALL }\n"
"\t\t电源管理统计信息\n"
"\t\t关键字:\n"
"\t\tCPU\tCPU 频率\n"
"\t\tFAN\t风扇速度\n"
"\\t\\tFREQ\\tCPU 平均时钟频率\n"
"\t\tIN\t输入电压\n"
"\t\tTEMP\t设备温度\n"
"\\t\\tUSB\\t连接的USB 设备\n"

#: sar.c:152
#, c-format
msgid ""
"\t-n { <keyword> [,...] | ALL }\n"
"\t\tNetwork statistics\n"
"\t\tKeywords are:\n"
"\t\tDEV\tNetwork interfaces\n"
"\t\tEDEV\tNetwork interfaces (errors)\n"
"\t\tNFS\tNFS client\n"
"\t\tNFSD\tNFS server\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP traffic\t(v4)\n"
"\t\tEIP\tIP traffic\t(v4) (errors)\n"
"\t\tICMP\tICMP traffic\t(v4)\n"
"\t\tEICMP\tICMP traffic\t(v4) (errors)\n"
"\t\tTCP\tTCP traffic\t(v4)\n"
"\t\tETCP\tTCP traffic\t(v4) (errors)\n"
"\t\tUDP\tUDP traffic\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP traffic\t(v6)\n"
"\t\tEIP6\tIP traffic\t(v6) (errors)\n"
"\t\tICMP6\tICMP traffic\t(v6)\n"
"\t\tEICMP6\tICMP traffic\t(v6) (errors)\n"
"\t\tUDP6\tUDP traffic\t(v6)\n"
"\t\tFC\tFibre channel HBAs\n"
msgstr ""
"\t-n { <关键字> [,...] | ALL }\n"
"\t\t网络统计信息\n"
"\t\t关键字:\n"
"\t\tDEV\t网卡\n"
"\t\tEDEV\t网卡(错误)\n"
"\t\tNFS\tNFS 客户端\n"
"\t\tNFSD\tNFS 服务端\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP 流\t(v4)\n"
"\t\tEIP\tIP 流\t(v4) (错误)\n"
"\t\tICMP\tICMP 流\t(v4)\n"
"\t\tEICMP\tICMP 流\t(v4) (错误)\n"
"\t\tTCP\tTCP 流\t(v4)\n"
"\t\tETCP\tTCP 流\t(v4) (错误)\n"
"\t\tUDP\tUDP 流\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP 流\t(v6)\n"
"\t\tEIP6\tIP 流\t(v6) (错误)\n"
"\t\tICMP6\tICMP 流\t(v6)\n"
"\t\tEICMP6\tICMP 流\t(v6) (错误)\n"
"\t\tUDP6\tUDP 流\t(v6)\n"
"\t\tFC\tFibre channel HBAs\n"

#: sar.c:174
#, c-format
msgid "\t-q\tQueue length and load average statistics\n"
msgstr "\t-q\t队列长度和平均负载\n"

#: sar.c:175
#, c-format
msgid "\t-R\tMemory statistics\n"
msgstr "\t-R\t内存状况\n"

#: sar.c:176
#, c-format
msgid ""
"\t-r [ ALL ]\n"
"\t\tMemory utilization statistics\n"
msgstr ""
"\t-r [ ALL ]\n"
"\t\t内存利用率信息\n"

#: sar.c:178
#, c-format
msgid "\t-S\tSwap space utilization statistics\n"
msgstr "\t-S\t交换空间利用率信息\n"

#: sar.c:179
#, c-format
msgid ""
"\t-u [ ALL ]\n"
"\t\tCPU utilization statistics\n"
msgstr ""
"\t-u [ ALL ]\n"
"\t\tCPU 利用率信息\n"

#: sar.c:181
#, c-format
msgid "\t-v\tKernel tables statistics\n"
msgstr "\t-v\t内核表统计信息\n"

#: sar.c:182
#, c-format
msgid "\t-W\tSwapping statistics\n"
msgstr "\t-W\t交换信息\n"

#: sar.c:183
#, c-format
msgid "\t-w\tTask creation and system switching statistics\n"
msgstr "\t-w\t任务创建与系统转换信息\n"

#: sar.c:184
#, c-format
msgid "\t-y\tTTY devices statistics\n"
msgstr "\t-y\tTTY 设备信息\n"

#: sar.c:198
#, c-format
msgid "Data collector will be sought in PATH\n"
msgstr "将在PATH 中查找数据收集器\n"

#: sar.c:201
#, c-format
msgid "Data collector found: %s\n"
msgstr "找到数据收集器: %s\n"

#: sar.c:260
#, c-format
msgid "End of data collecting unexpected\n"
msgstr "数据流结尾有未知错误\n"

#: sar.c:813
#, c-format
msgid "Using a wrong data collector from a different sysstat version\n"
msgstr "正在使用来自不同版本 sysstat 的错误的数据收集器\n"

#: sar.c:865
#, c-format
msgid "Inconsistent input data\n"
msgstr "所取数据前后不一致\n"

#: sar.c:1044 pidstat.c:239
#, c-format
msgid "Requested activities not available\n"
msgstr "所需的运行记录无法获得\n"

#: sar.c:1350
#, c-format
msgid "-f and -o options are mutually exclusive\n"
msgstr "-f 和 -o 选项不能同时使用\n"

#: sar.c:1356
#, c-format
msgid "Not reading from a system activity file (use -f option)\n"
msgstr "无法查看系统活动记录文件 (用 -f 选项)\n"

#: sar.c:1492
#, c-format
msgid "Cannot find the data collector (%s)\n"
msgstr "无法找到数据收集器 (%s)\n"

#: pr_stats.c:2494 pr_stats.c:2505 pr_stats.c:2606 pr_stats.c:2617
msgid "Summary:"
msgstr "总计:"

#: pr_stats.c:2547
msgid "Other devices not listed here"
msgstr "未在此列出的其它设备"

#: sa_conv.c:69
#, c-format
msgid "Cannot convert the format of this file\n"
msgstr "无法轮换此文件的格式\n"

#: sa_conv.c:562
#, c-format
msgid ""
"\n"
"CPU activity not found in file. Aborting...\n"
msgstr ""
"\n"
"未在文件中找到CPU 活动记录。退出...\n"

#: sa_conv.c:578
#, c-format
msgid ""
"\n"
"Invalid data found. Aborting...\n"
msgstr ""
"\n"
"发现无效信息。退出...\n"

#: sa_conv.c:897
#, c-format
msgid "Statistics: "
msgstr "统计信息:"

#: sa_conv.c:1016
#, c-format
msgid ""
"\n"
"File successfully converted to sysstat format version %s\n"
msgstr ""
"\n"
"文件已成功转化为sysstat 格式版本 %s\n"

#: pidstat.c:90
#, c-format
msgid ""
"Options are:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ] [ -U [ <username> ] ]\n"
"[ -u ] [ -V ] [ -v ] [ -w ] [ -C <command> ] [ -G <process_name> ]\n"
"[ -p { <pid> [,...] | SELF | ALL } ] [ -T { TASK | CHILD | ALL } ]\n"
msgstr ""
"选项:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ] [ -U [ <用户名> ] ]\n"
"[ -u ] [ -V ] [ -v ] [ -w ] [ -C <命令> ] [ -G <进程名> ]\n"
"[ -p { <pid> [,...] | SELF | ALL } ] [ -T { TASK | CHILD | ALL } ]\n"

#: sa_common.c:1060
#, c-format
msgid "Error while reading system activity file: %s\n"
msgstr "查看系统运行记录时出错: %s\n"

#: sa_common.c:1070
#, c-format
msgid "End of system activity file unexpected\n"
msgstr "系统运行记录文件的结尾有未知错误\n"

#: sa_common.c:1089
#, c-format
msgid "File created by sar/sadc from sysstat version %d.%d.%d"
msgstr "文件由 sysstat 版本 %d.%d.%d 中的 sar/sadc 创建。"

#: sa_common.c:1122
#, c-format
msgid "Invalid system activity file: %s\n"
msgstr "无效的系统运行记录文件: %s\n"

#: sa_common.c:1134
#, c-format
msgid "Endian format mismatch\n"
msgstr "Endian 格式不匹配\n"

#: sa_common.c:1138
#, c-format
msgid "Current sysstat version cannot read the format of this file (%#x)\n"
msgstr "当前版本的 sysstat 无法读取此文件格式 (%#x)\n"

#: sa_common.c:1267
#, c-format
msgid "Please check if data collecting is enabled\n"
msgstr "请检查是否允许数据收集\n"

#: sa_common.c:1460
#, c-format
msgid "Requested activities not available in file %s\n"
msgstr "所需的运行记录在此文件 %s 中无法获得\n"

#: tapestat.c:91
#, c-format
msgid ""
"Options are:\n"
"[ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"
msgstr ""
"选项:\n"
"[ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"

#: tapestat.c:257
#, c-format
msgid "No tape drives with statistics found\n"
msgstr "未找到带统计信息的磁带设备\n"

#: count.c:169
#, c-format
msgid "Cannot handle so many processors!\n"
msgstr "处理器太多，无法处理！\n"

#~ msgid ""
#~ "Options are:\n"
#~ "[ --debuginfo ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
#~ msgstr ""
#~ "选项:\n"
#~ "[  --debuginfo ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#~ msgid "\t-m\tPower management statistics\n"
#~ msgstr "\t-m\t电源管理信息状况\n"

#~ msgid "-x and -p options are mutually exclusive\n"
#~ msgstr "-x 和 -p 选项不能同时使用\n"

#~ msgid ""
#~ "\t-n { DEV | EDEV | NFS | NFSD | SOCK |\n"
#~ "\t     IP | EIP | ICMP | EICMP | TCP | ETCP | UDP | ALL }\n"
#~ "\t\tNetwork statistics\n"
#~ msgstr ""
#~ "\t-n { DEV | EDEV | NFS | NFSD | SOCK |\n"
#~ "\t     IP | EIP | ICMP | EICMP | TCP | ETCP | UDP | ALL }\n"
#~ "\t\t网络信息状况\n"

#~ msgid "Usage:"
#~ msgstr "用法:"

#~ msgid "options..."
#~ msgstr "选项..."

#~ msgid "interval"
#~ msgstr "间隔"

#~ msgid "count"
#~ msgstr "次数"

#~ msgid "device"
#~ msgstr "设备名"

#~ msgid "cpu"
#~ msgstr "cpu"

#~ msgid "command"
#~ msgstr "命令"

#~ msgid "outfile"
#~ msgstr "输出文件"

#~ msgid "comment"
#~ msgstr "注释"

#~ msgid "datafile"
#~ msgstr "数据文件"

#~ msgid "hh:mm:ss"
#~ msgstr "时:分:秒"

#~ msgid "sar_options..."
#~ msgstr "sar 选项..."

#~ msgid "Activity flag: %#x\n"
#~ msgstr "运行标记: %#x\n"

#~ msgid "Number of CPU: %u\n"
#~ msgstr "CPU 数量: %u\n"

#~ msgid "Number of interrupts per CPU: %u\n"
#~ msgstr "每个 CPU 的中断数目: %u\n"

#~ msgid "Number of disks: %u\n"
#~ msgstr "磁盘数量: %u\n"

#~ msgid "Number of network interfaces: %u\n"
#~ msgstr "网卡数量: %u\n"

#~ msgid "int"
#~ msgstr "整型数"

#~ msgid "filename"
#~ msgstr "文件名"

#~ msgid "Not an SMP machine...\n"
#~ msgstr "非对称多处理机器...\n"
