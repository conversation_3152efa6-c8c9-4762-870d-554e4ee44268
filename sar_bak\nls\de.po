# NLS support for the sysstat package.
# Copyright (C) 1999 Free Software Foundation, Inc.
# This file is distributed under the same license as the sysstat package.
# <PERSON><PERSON><PERSON><PERSON> GODARD <sysstat [at] orange.fr>, 1999.
# <PERSON> <<EMAIL>>, 2009-2017.
#
msgid ""
msgstr ""
"Project-Id-Version: sysstat 11.6.0\n"
"Report-Msgid-Bugs-To: sysstat <at> orange.fr\n"
"POT-Creation-Date: 2017-08-14 08:32+0200\n"
"PO-Revision-Date: 2017-08-14 21:37+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: German <<EMAIL>>\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"X-Generator: Poedit 2.0.3\n"

#: iostat.c:86 cifsiostat.c:70 mpstat.c:126 sar.c:96 tapestat.c:95
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ]\n"
msgstr "Aufruf: %s [ Optionen … ] [ <Intervall> [ <Anzahl> ] ]\n"

#: iostat.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ] [ --debuginfo ]\n"
msgstr ""
"Optionen sind:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PFAD | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <Gruppenname> ] [ -p [ <Gerät> [,...] | ALL ] ]\n"
"[ <Gerät> [...] | ALL ] [ --debuginfo ]\n"

#: iostat.c:95
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ]\n"
msgstr ""
"Optionen sind:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PFAD | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <Gruppenname> ] [ -p [ <Gerät> [,...] | ALL ] ]\n"
"[ <Gerät> [...] | ALL ]\n"

#: iostat.c:326
#, c-format
msgid "Cannot find disk data\n"
msgstr "Plattendaten können nicht gefunden werden\n"

#: iostat.c:1812 sa_common.c:1590
#, c-format
msgid "Invalid type of persistent device name\n"
msgstr "Ungültige Art eines persistenten Gerätenamens\n"

#: sadc.c:89
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
msgstr "Aufruf: %s [ Optionen … ] [ <Intervall> [ <Anzahl> ] ] [ <Ausgabedatei> ]\n"

#: sadc.c:92
#, c-format
msgid ""
"Options are:\n"
"[ -C <comment> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"
msgstr ""
"Optionen sind:\n"
"[ -C <comment> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"

#: sadc.c:267
#, c-format
msgid "Cannot write data to system activity file: %s\n"
msgstr "Daten können nicht in die Systemaktivitätendatei geschrieben werden: %s\n"

#: sadc.c:563
#, c-format
msgid "Cannot write system activity file header: %s\n"
msgstr "Kopf der Systemaktivitätendatei kann nicht geschrieben werden: %s\n"

#: sadc.c:763 sadc.c:772 sadc.c:839 ioconf.c:510 rd_stats.c:69
#: sa_common.c:1204 count.c:118
#, c-format
msgid "Cannot open %s: %s\n"
msgstr "%s kann nicht geöffnet werden: %s\n"

#: sadc.c:1019
#, c-format
msgid "Cannot append data to that file (%s)\n"
msgstr "An die Datei »%s« können keine Daten angehängt werden\n"

#: common.c:77
#, c-format
msgid "sysstat version %s\n"
msgstr "sysstat Version %s\n"

#: cifsiostat.c:74
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"
msgstr ""
"Optionen sind:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"

#: cifsiostat.c:77
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
msgstr ""
"Optionen sind:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#: mpstat.c:129
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -n ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -N { <node_list> | ALL } ] [ -o JSON ] [ -P { <cpu_list> | ON | ALL } ]\n"
msgstr ""
"Optionen sind:\n"
"[ -A ] [ -u ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -N { <Knoten-Liste> | ALL } ] [ -o JSON ] [ -P { <CPU-Liste> | ON | ALL } ]\n"

# sar.c:
#: mpstat.c:1672 sar.c:358 pidstat.c:2406
msgid "Average:"
msgstr "Durchschn.:"

#: sadf.c:87
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <datafile> | -[0-9]+ ]\n"
msgstr "Aufruf: %s [ Optionen ] [ <Intervall> [ <Anzahl> ] ] [ <Datendatei> | -[0-9]+ ]\n"

#: sadf.c:90
#, c-format
msgid ""
"Options are:\n"
"[ -C ] [ -c | -d | -g | -j | -p | -r | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -O <opts> [,...] ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
"[ -- <sar_options> ]\n"
msgstr ""
"Optionen sind:\n"
"[ -C ] [ -c | -d | -g | -j | -p | -r | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -O <Optionen> [,...] ] [ -P { <CPU> [,...] | ALL } ]\n"
"[ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
"[ -- <Sar-Optionen> ]\n"

#: pr_stats.c:2538 pr_stats.c:2549 pr_stats.c:2656 pr_stats.c:2667
msgid "Summary:"
msgstr "Zusammenfassung:"

#: pr_stats.c:2591
msgid "Other devices not listed here"
msgstr "Andere Geräte, die hier nicht aufgelistet sind"

#: sar.c:111
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ MOUNT ] ] [ -H ] [ -h ]\n"
"[ -p ] [ -q ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --human ] [ --sadc ]\n"
"[ -I { <int_list> | SUM | ALL } ] [ -P { <cpu_list> | ALL } ]\n"
"[ -m { <keyword> [,...] | ALL } ] [ -n { <keyword> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filename> ] | -o [ <filename> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
msgstr ""
"Optionen sind:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ <Einhängepunkt> ] ] [ -H ]\n"
"[ -h ] [ -p ] [ -q ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --human ] [ --sadc ]\n"
"[ -I { <Zahlen> | SUM | ALL } ] [ -P { <CPU-Liste> | ALL } ]\n"
"[ -m { <Schlüsselwort> [,...] | ALL } ] [ -n { <Schlüsselwort> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PFAD | UUID | ... } ]\n"
"[ -f [ <Dateiname> ] | -o [ <Dateiname> ] | -[0-9]+ ]\n"
"[ -i <Intervall> ] [ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"

#: sar.c:134
#, c-format
msgid "Main options and reports:\n"
msgstr "Hauptoptionen und Berichte:\n"

#: sar.c:135
#, c-format
msgid "\t-B\tPaging statistics\n"
msgstr "\t-B\tPaging-Statistik\n"

#: sar.c:136
#, c-format
msgid "\t-b\tI/O and transfer rate statistics\n"
msgstr "\t-b\tI/O- und Transferraten-Statistik\n"

#: sar.c:137
#, c-format
msgid "\t-d\tBlock devices statistics\n"
msgstr "\t-d\tBlockgeräte-Statistik\n"

#: sar.c:138
#, c-format
msgid "\t-F [ MOUNT ]\n"
msgstr "\t-F [ <Einhängepunkt> ]\n"

#: sar.c:139
#, c-format
msgid "\t\tFilesystems statistics\n"
msgstr "\t\tDateisystem-Statistik\n"

#: sar.c:140
#, c-format
msgid "\t-H\tHugepages utilization statistics\n"
msgstr "\t-H\tRiesenseiten-Benutzungs-Statistik\n"

#: sar.c:141
#, c-format
msgid ""
"\t-I { <int_list> | SUM | ALL }\n"
"\t\tInterrupts statistics\n"
msgstr ""
"\t-I { <Zahlenliste> | SUM | ALL }\n"
"\t\tInterrupt-Statistik\n"

#: sar.c:143
#, c-format
msgid ""
"\t-m { <keyword> [,...] | ALL }\n"
"\t\tPower management statistics\n"
"\t\tKeywords are:\n"
"\t\tCPU\tCPU instantaneous clock frequency\n"
"\t\tFAN\tFans speed\n"
"\t\tFREQ\tCPU average clock frequency\n"
"\t\tIN\tVoltage inputs\n"
"\t\tTEMP\tDevices temperature\n"
"\t\tUSB\tUSB devices plugged into the system\n"
msgstr ""
"\t-m { <Schlüsselwort> [,...] | ALL }\n"
"\t\tPower-Management-Statistik\n"
"\t\tSchlüsselwörter sind:\n"
"\t\tCPU\tCPU-Taktfrequenz\n"
"\t\tFAN\tLüftergeschwindigkeit\n"
"\t\tFREQ\tDurchschnittliche CPU-Takfrequenz\n"
"\t\tIN\tEingangsspannung\n"
"\t\tTEMP\tGerätetemperatur\n"
"\t\tUSB\tUSB-Geräte, die an das System angeschlossen sind\n"

#: sar.c:152
#, c-format
msgid ""
"\t-n { <keyword> [,...] | ALL }\n"
"\t\tNetwork statistics\n"
"\t\tKeywords are:\n"
"\t\tDEV\tNetwork interfaces\n"
"\t\tEDEV\tNetwork interfaces (errors)\n"
"\t\tNFS\tNFS client\n"
"\t\tNFSD\tNFS server\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP traffic\t(v4)\n"
"\t\tEIP\tIP traffic\t(v4) (errors)\n"
"\t\tICMP\tICMP traffic\t(v4)\n"
"\t\tEICMP\tICMP traffic\t(v4) (errors)\n"
"\t\tTCP\tTCP traffic\t(v4)\n"
"\t\tETCP\tTCP traffic\t(v4) (errors)\n"
"\t\tUDP\tUDP traffic\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP traffic\t(v6)\n"
"\t\tEIP6\tIP traffic\t(v6) (errors)\n"
"\t\tICMP6\tICMP traffic\t(v6)\n"
"\t\tEICMP6\tICMP traffic\t(v6) (errors)\n"
"\t\tUDP6\tUDP traffic\t(v6)\n"
"\t\tFC\tFibre channel HBAs\n"
"\t\tSOFT\tSoftware-based network processing\n"
msgstr ""
"\t-n { <Schlüsselwort> [,...] | ALL }\n"
"\t\tNetzwerk-Statistik\n"
"\t\tSchlüsselwörter sind:\n"
"\t\tDEV\tNetzwerkschnittstellen\n"
"\t\tEDEV\tNetzwerkschnittstellen (Fehler)\n"
"\t\tNFS\tNFS-Client\n"
"\t\tNFSD\tNFS-Server\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP-Verkehr\t(v4)\n"
"\t\tEIP\tIP-Verkehr\t(v4) (Fehler)\n"
"\t\tICMP\tICMP-Verkehr\t(v4)\n"
"\t\tEICMP\tICMP-Verkehr\t(v4) (Fehler)\n"
"\t\tTCP\tTCP-Verkehr\t(v4)\n"
"\t\tETCP\tTCP-Verkehr\t(v4) (Fehler)\n"
"\t\tUDP\tUDP-Verkehr\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP-Verkehr\t(v6)\n"
"\t\tEIP6\tIP-Verkehr\t(v6) (Fehler)\n"
"\t\tICMP6\tICMP-Verkehr\t(v6)\n"
"\t\tEICMP6\tICMP-Verkehr\t(v6) (Fehler)\n"
"\t\tUDP6\tUDP-Verkehr\t(v6)\n"
"\t\tFC\tFibre Channel HBAs\n"
"\t\tSOFT\tSoftware-basierte Netzwerkverarbeitung\n"

#: sar.c:175
#, c-format
msgid "\t-q\tQueue length and load average statistics\n"
msgstr "\t-q\tWarteschlangen- und Systemauslastungs-Statistik\n"

#: sar.c:176
#, c-format
msgid ""
"\t-r [ ALL ]\n"
"\t\tMemory utilization statistics\n"
msgstr ""
"\t-r [ ALL ]\n"
"\t\tSpeicherverbrauchs-Statistik\n"

#: sar.c:178
#, c-format
msgid "\t-S\tSwap space utilization statistics\n"
msgstr "\t-S\tAuslagerungsspeicher-Statistik\n"

#: sar.c:179
#, c-format
msgid ""
"\t-u [ ALL ]\n"
"\t\tCPU utilization statistics\n"
msgstr ""
"\t-u [ ALL ]\n"
"\t\tCPU-Nutzungs-Statistik\n"

#: sar.c:181
#, c-format
msgid "\t-v\tKernel tables statistics\n"
msgstr "\t-v\tKernel-Tabellen-Statistik\n"

#: sar.c:182
#, c-format
msgid "\t-W\tSwapping statistics\n"
msgstr "\t-W\tAuslagerungs-Statistik\n"

#: sar.c:183
#, c-format
msgid "\t-w\tTask creation and system switching statistics\n"
msgstr "\t-w\tTask-Erzeugungs- und Systemwechsel-Statistik\n"

#: sar.c:184
#, c-format
msgid "\t-y\tTTY devices statistics\n"
msgstr "\t-y\tTTY-Geräte-Statistik\n"

#: sar.c:198
#, c-format
msgid "Data collector will be sought in PATH\n"
msgstr "Datensammler wird in PATH gesucht\n"

#: sar.c:201
#, c-format
msgid "Data collector found: %s\n"
msgstr "Datensammler gefunden: %s\n"

#: sar.c:260
#, c-format
msgid "End of data collecting unexpected\n"
msgstr "Unerwartetes Ende der gesammelten Daten\n"

#: sar.c:813
#, c-format
msgid "Using a wrong data collector from a different sysstat version\n"
msgstr "Datensammler von einer anderen sysstat-Version in Benutzung\n"

#: sar.c:865
#, c-format
msgid "Inconsistent input data\n"
msgstr "Inkonsistente Eingabedaten\n"

#: sar.c:1044 pidstat.c:239
#, c-format
msgid "Requested activities not available\n"
msgstr "Die angeforderte Aktion ist nicht verfügbar.\n"

#: sar.c:1347
#, c-format
msgid "-f and -o options are mutually exclusive\n"
msgstr "Die Optionen -f und -o schließen sich gegenseitig aus\n"

#: sar.c:1353
#, c-format
msgid "Not reading from a system activity file (use -f option)\n"
msgstr "Bitte -f Option zur Angabe der Systemaktivitätendatei verwenden\n"

#: sar.c:1489
#, c-format
msgid "Cannot find the data collector (%s)\n"
msgstr "Datensammler »%s« kann nicht gefunden werden\n"

#: sa_conv.c:69
#, c-format
msgid "Cannot convert the format of this file\n"
msgstr "Das Format dieser Datei kann nicht konvertiert werden\n"

#: sa_conv.c:562
#, c-format
msgid ""
"\n"
"CPU activity not found in file. Aborting...\n"
msgstr ""
"\n"
"CPU-Aktivität nicht in Datei gefunden. Abbruch …\n"

#: sa_conv.c:578
#, c-format
msgid ""
"\n"
"Invalid data found. Aborting...\n"
msgstr ""
"\n"
"Ungültige Daten gefunden. Abbruch …\n"

#: sa_conv.c:897
#, c-format
msgid "Statistics: "
msgstr "Statistik: "

#: sa_conv.c:1016
#, c-format
msgid ""
"\n"
"File successfully converted to sysstat format version %s\n"
msgstr ""
"\n"
"Datei erfolgreich in sysstat-Format, Version %s, konvertiert.\n"

#: pidstat.c:87
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ -e <program> <args> ]\n"
msgstr "Aufruf: %s [ Optionen ] [ <Intervall> [ <Anzahl> ] ] [ -e <Programm> <Ausgabedatei> ]\n"

#: pidstat.c:90
#, c-format
msgid ""
"Options are:\n"
"[ -d ] [ -H ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ] [ -U [ <username> ] ]\n"
"[ -u ] [ -V ] [ -v ] [ -w ] [ -C <command> ] [ -G <process_name> ] [ --human ]\n"
"[ -p { <pid> [,...] | SELF | ALL } ] [ -T { TASK | CHILD | ALL } ]\n"
msgstr ""
"Optionen sind:\n"
"[ -d ] [ -H ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ] [ -U [ <Benutzername> ] ]\n"
"[ -u ] [ -V ] [ -v ] [ -w ] [ -C <Befehl> ] [ -G <Prozessname> ] [ --human ]\n"
"[ -p { <PID> [,...] | SELF | ALL } ] [ -T { TASK | CHILD | ALL } ]\n"

#: sa_common.c:1000
#, c-format
msgid "Error while reading system activity file: %s\n"
msgstr "Fehler beim Lesen der Systemaktivitätendatei: %s\n"

#: sa_common.c:1010
#, c-format
msgid "End of system activity file unexpected\n"
msgstr "Unerwartetes Ende der Systemaktivitätendatei\n"

#: sa_common.c:1029
#, c-format
msgid "File created by sar/sadc from sysstat version %d.%d.%d"
msgstr "Diese Datei wurde erzeugt mit sar/sadc von sysstat Version %d.%d.%d"

#: sa_common.c:1062
#, c-format
msgid "Invalid system activity file: %s\n"
msgstr "Ungültige Systemaktivitätendatei: %s\n"

#: sa_common.c:1074
#, c-format
msgid "Endian format mismatch\n"
msgstr "Endian-Format passt nicht zusammen\n"

#: sa_common.c:1078
#, c-format
msgid "Current sysstat version cannot read the format of this file (%#x)\n"
msgstr "Diese Version von sysstat kann das Format dieser Datei (%#x) nicht mehr lesen\n"

#: sa_common.c:1207
#, c-format
msgid "Please check if data collecting is enabled\n"
msgstr "Bitte prüfen Sie, ob die Datensammlung aktiviert ist\n"

#: sa_common.c:1400
#, c-format
msgid "Requested activities not available in file %s\n"
msgstr "Angeforderte Aktivität ist nicht verfügbar in Datei %s\n"

#: tapestat.c:97
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"
msgstr ""
"Optionen sind:\n"
"[ --human ] [ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"

#: tapestat.c:263
#, c-format
msgid "No tape drives with statistics found\n"
msgstr "Keine Bandlaufwerke mit Statistik gefunden.\n"

#: count.c:169
#, c-format
msgid "Cannot handle so many processors!\n"
msgstr "Es sind zu viele Prozessoren vorhanden!\n"

#: sadf_misc.c:834
#, c-format
msgid "System activity data file: %s (%#x)\n"
msgstr "Systemaktivitäts-Datendatei: %s (%#x)\n"

# "sa" means "system activity". This should be mentioned in the source code.
#: sadf_misc.c:843
#, c-format
msgid "Genuine sa datafile: %s (%x)\n"
msgstr "Systemaktivitäts-Datendatei: %s (%x)\n"

#: sadf_misc.c:844
msgid "no"
msgstr "nein"

#: sadf_misc.c:844
msgid "yes"
msgstr "ja"

#: sadf_misc.c:847
#, c-format
msgid "Host: "
msgstr "Rechner: "

#: sadf_misc.c:854
#, c-format
msgid "Number of CPU for last samples in file: %u\n"
msgstr "Anzahl der CPUs für letzte Messwerte in Datei: %u\n"

#: sadf_misc.c:860
#, c-format
msgid "File date: %s\n"
msgstr "Dateizeit: %s\n"

#: sadf_misc.c:863
#, c-format
msgid "File time: "
msgstr "Dateizeit: "

#: sadf_misc.c:868
#, c-format
msgid "Size of a long int: %d\n"
msgstr "Größe eines Longint: %d\n"

#: sadf_misc.c:874
#, c-format
msgid "List of activities:\n"
msgstr "Liste der Aktivitäten:\n"

#: sadf_misc.c:887
#, c-format
msgid "\t[Unknown activity format]"
msgstr "\t[Unbekanntes Aktivitätsformat]"

#~ msgid ""
#~ "Options are:\n"
#~ "[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
#~ msgstr ""
#~ "Optionen sind:\n"
#~ "[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#~ msgid "Not that many processors!\n"
#~ msgstr "Nicht so viele Prozessoren!\n"

#~ msgid "\t-R\tMemory statistics\n"
#~ msgstr "\t-R\tSpeicher-Statistik\n"

#~ msgid "Invalid data format\n"
#~ msgstr "ungültiges Datenformat\n"

#~ msgid "\t-m\tPower management statistics\n"
#~ msgstr "\t-m\tEnergieverwaltungs-Statistik\n"

#~ msgid "Time: %s\n"
#~ msgstr "Zeit: %s\n"

#~ msgid "-x and -p options are mutually exclusive\n"
#~ msgstr "Die Optionen -x und -p schließen sich gegenseitig aus\n"

#~ msgid ""
#~ "Usage: %s [ options... ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
#~ "Options are:\n"
#~ "[ -C <comment> ] [ -d ] [ -F ] [ -I ] [ -V ]\n"
#~ msgstr ""
#~ "Aufruf: %s [ optionen... ] [ <intervall> [ <anzahl> ] ] [ <dateiname> ]\n"
#~ "mögliche Optionen:\n"
#~ "[ -C <comment> ] [ -d ] [ -F ] [ -I ] [ -V ]\n"

#~ msgid "Not an SMP machine...\n"
#~ msgstr "Keine SMP-Maschine...\n"
