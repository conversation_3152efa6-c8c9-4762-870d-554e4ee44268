/*
 * sadc: system activity data collector
 * (C) 1999-2014 by <PERSON><PERSON><PERSON> (sysstat <at> orange.fr)
 *
 ***************************************************************************
 * This program is free software; you can redistribute it and/or modify it *
 * under the terms of the GNU General Public License as published  by  the *
 * Free Software Foundation; either version 2 of the License, or (at  your *
 * option) any later version.                                              *
 *                                                                         *
 * This program is distributed in the hope that it  will  be  useful,  but *
 * WITHOUT ANY WARRANTY; without the implied warranty  of  <PERSON><PERSON><PERSON><PERSON><PERSON>ILITY *
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License *
 * for more details.                                                       *
 *                                                                         *
 * You should have received a copy of the GNU General Public License along *
 * with this program; if not, write to the Free Software Foundation, Inc., *
 * 59 Temple Place, Suite 330, Boston, MA 02111-1307 USA                   *
 ***************************************************************************
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <ctype.h>
#include <unistd.h>
#include <fcntl.h>
#include <time.h>
#include <errno.h>
#include <signal.h>
#include <dirent.h>
#include <sys/file.h>
#include <sys/stat.h>
#include <sys/utsname.h>
#include <pthread.h>

#include "version.h"
#include "sa.h"
#include "rd_stats.h"
#include "common.h"
#include "ioconf.h"
#include "pr_stats.h"

// 网络安全监控函数声明
struct stats_net_security;
extern void read_net_security(struct stats_net_security *st_net_security, int deep_monitoring_mode);

// 这些变量在agent/send_data.c中定义，这里只声明
extern int o2;
extern pthread_mutex_t xxx;
extern int release1;
extern int deep_monitoring_mode;  // 深度监控模式标志
extern int sar_lock1;
extern int sigint_caught;

// 高效同步机制相关变量（简化版本）
typedef struct {
    pthread_mutex_t mutex;
    pthread_cond_t cond;
    int count;
    int waiting;
    int generation;
} efficient_barrier_t;

// 这些变量在libpcap/pcap.c中定义，这里只声明
extern efficient_barrier_t barrier_a_done;
extern efficient_barrier_t barrier_bc_done;
extern int efficient_barrier_wait(efficient_barrier_t *barrier);

// write_log函数在agent/send_data.c中定义，这里只声明
extern void write_log(const char *format, ...);

#ifdef USE_NLS
#include <locale.h>
#include <libintl.h>
#define _(string) gettext(string)
#else
#define _(string) (string)
#endif

#ifdef HAVE_SENSORS
#include "sensors/sensors.h"
#include "sensors/error.h"
#endif
// add 2025-3-20 全局同步
#include "../agent/top.h"

// 网络安全监控相关
#include "network_security.h"

// 全局网络安全统计变量
struct stats_net_security net_security_stats;

// 在top.h包含之后声明os变量（extern）
extern os_data *os;  // 在sadc中不使用，但pr_stats.c需要

// 函数声明已在network_security.h中

// 打印网络安全统计的函数
void print_net_security_stats(struct stats_net_security *st_net_security) {
    printf("\n=== 网络安全监控统计 ===\n");
    printf("TCP连接统计:\n");
    printf("  总连接数: %u\n", st_net_security->total_connections);
    printf("  已建立连接: %u\n", st_net_security->established_connections);
    printf("  SYN_RECV连接: %u\n", st_net_security->syn_recv_connections);
    printf("  TIME_WAIT连接: %u\n", st_net_security->time_wait_connections);
    printf("  LISTEN连接: %u\n", st_net_security->listen_connections);

    printf("\n网络流量统计:\n");
    printf("  接收字节数: %llu\n", st_net_security->rx_bytes);
    printf("  发送字节数: %llu\n", st_net_security->tx_bytes);
    printf("  接收包数: %llu\n", st_net_security->rx_packets);
    printf("  发送包数: %llu\n", st_net_security->tx_packets);

    printf("\n异常检测:\n");
    printf("  异常评分: %d/100\n", st_net_security->anomaly_score);
    printf("  触发包分析: %s\n", st_net_security->trigger_packet_analysis ? "是" : "否");
    printf("  异常原因: %s\n", st_net_security->anomaly_reason);

    if (st_net_security->anomaly_score > 70) {
        printf("  *** 警告：检测到高风险网络异常！***\n");
    } else if (st_net_security->anomaly_score > 50) {
        printf("  *** 注意：检测到中等风险网络异常 ***\n");
    } else {
        printf("  ✓ 网络状态正常\n");
    }
    printf("========================\n\n");
}

// add 2024
#include "sa1.h"
extern int ths;
extern void write_log(const char *format, ...);
extern FILE *g_logfile;

// 移除重复的typedef定义，在上面已经定义过了
// 移除重复的extern声明，函数和变量在上面已经定义
// extern int efficient_barrier_wait(efficient_barrier_t *barrier);
// extern efficient_barrier_t barrier_a_done;
// extern efficient_barrier_t barrier_bc_done;
#define SCCSID "@(#)sysstat-" VERSION ": " __FILE__ " compiled " __DATE__ " " __TIME__
char *sccsid(void) { return (SCCSID); }

long interval = 0;
unsigned int flags = 0;

int dis;
int optz = 0;
char timestamp[2][TIMESTAMP_LEN];

struct file_header file_hdr;
struct record_header record_hdr;
char comment[MAX_COMMENT_LEN];
unsigned int id_seq[NR_ACT];
// int sadc1();
extern struct activity *act[];

struct sigaction alrm_act, int_act, lock1;
// add 2025-3-20
int sar_lk;
/*
 ***************************************************************************
 * Print usage and exit.
 *
 * IN:
 * @progname	Name of sysstat command
 ***************************************************************************
 */
void usage(char *progname)
{
	fprintf(stderr, _("Usage: %s [ options ] [ <interval> [ <count> ] ] [ <outfile> ]\n"),
			progname);

	fprintf(stderr, _("Options are:\n"
					  "[ -C <comment> ] [ -F ] [ -L ] [ -V ]\n"
					  "[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"));
	exit(1);
}

/*
 ***************************************************************************
 * Collect all activities belonging to a group.
 *
 * IN:
 * @group_id	Group identification number.
 * @opt_f	Optionnal flag to set.
 ***************************************************************************
 */
void collect_group_activities(unsigned int group_id, unsigned int opt_f)
{
	int i;

	for (i = 0; i < NR_ACT; i++)
	{
		if (act[i]->group & group_id)
		{
			act[i]->options |= AO_COLLECTED;
			if (opt_f)
			{
				act[i]->opt_flags |= opt_f;
			}
		}
	}
}

/*
 ***************************************************************************
 * Parse option -S, indicating which activities are to be collected.
 *
 * IN:
 * @argv	Arguments list.
 * @opt		Index in list of arguments.
 ***************************************************************************
 */
void parse_sadc_S_option(char *argv[], int opt)
{
	char *p;
	int i;

	for (p = strtok(argv[opt], ","); p; p = strtok(NULL, ","))
	{
		if (!strcmp(p, K_INT))
		{
			/* Select group of interrupt activities */
			collect_group_activities(G_INT, AO_F_NULL);
		}
		else if (!strcmp(p, K_DISK))
		{
			/* Select group of disk activities */
			collect_group_activities(G_DISK, AO_F_NULL);
		}
		else if (!strcmp(p, K_XDISK))
		{
			/* Select group of disk and partition/filesystem activities */
			collect_group_activities(G_DISK + G_XDISK, AO_F_DISK_PART);
		}
		else if (!strcmp(p, K_SNMP))
		{
			/* Select group of SNMP activities */
			collect_group_activities(G_SNMP, AO_F_NULL);
		}
		else if (!strcmp(p, K_IPV6))
		{
			/* Select group of IPv6 activities */
			collect_group_activities(G_IPV6, AO_F_NULL);
		}
		else if (!strcmp(p, K_POWER))
		{
			/* Select group of activities related to power management */
			collect_group_activities(G_POWER, AO_F_NULL);
		}
		else if (!strcmp(p, K_ALL) || !strcmp(p, K_XALL))
		{
			/* Select all activities */
			for (i = 0; i < NR_ACT; i++)
			{

				if (!strcmp(p, K_ALL) && (act[i]->group & G_XDISK))
					/*
					 * Don't select G_XDISK activities
					 * when option -S ALL is used.
					 */
					continue;

				act[i]->options |= AO_COLLECTED;
			}
			if (!strcmp(p, K_XALL))
			{
				/* Tell sadc to also collect partition statistics */
				collect_group_activities(G_DISK + G_XDISK, AO_F_DISK_PART);
			}
		}
		else if (strspn(argv[opt], DIGITS) == strlen(argv[opt]))
		{
			/*
			 * Although undocumented, option -S followed by a numerical value
			 * enables the user to select each activity that should be
			 * collected. "-S 0" unselects all activities but CPU.
			 * A value greater than 255 enables the user to select groups
			 * of activities.
			 */
			int act_id;

			act_id = atoi(argv[opt]);
			if (act_id > 255)
			{
				act_id >>= 8;
				for (i = 0; i < NR_ACT; i++)
				{
					if (act[i]->group & act_id)
					{
						act[i]->options |= AO_COLLECTED;
					}
				}
			}
			else if ((act_id < 0) || (act_id > NR_ACT))
			{
				usage(argv[0]);
			}
			else if (!act_id)
			{
				/* Unselect all activities but CPU */
				for (i = 0; i < NR_ACT; i++)
				{
					act[i]->options &= ~AO_COLLECTED;
				}
				COLLECT_ACTIVITY(A_CPU);
			}
			else
			{
				/* Select chosen activity */
				COLLECT_ACTIVITY(act_id);
			}
		}
		else
		{
			usage(argv[0]);
		}
	}
}

/*
 ***************************************************************************
 * SIGALRM signal handler. No need to reset handler here.
 *
 * IN:
 * @sig	Signal number.
 ***************************************************************************
 */
void alarm_handler(int sig)
{
	alarm(interval);
}

/*
 ***************************************************************************
 * SIGINT signal handler.
 *
 * IN:
 * @sig	Signal number.
 ***************************************************************************
 */
void int_handler1(int sig)
{
	if (!optz)
	{
		/* sadc hasn't been called by sar */
		exit(1);
	}

	/*
	 * When starting sar then pressing ctrl/c, SIGINT is received
	 * by sadc, not sar. So send SIGINT to sar so that average stats
	 * can be displayed.
	 */
	if (kill(getppid(), SIGINT) < 0)
	{
		exit(1);
	}
	exit(1);
}

/*
 ***************************************************************************
 * Display an error message.
 ***************************************************************************
 */
void p_write_error(void)
{
	fprintf(stderr, _("Cannot write data to system activity file: %s\n"),
			strerror(errno));
	exit(2);
}

/*
 ***************************************************************************
 * Init structures. All of them are init'ed first when they are allocated
 * (done by SREALLOC() macro in sa_sys_init() function).
 * Then, they are init'ed again each time before reading the various system
 * stats to make sure that no stats from a previous reading will remain (eg.
 * if some network interfaces or block devices have been unregistered).
 ***************************************************************************
 */
void reset_stats(int curr)
{
	if (curr == 0)
	{
		int i;

		for (i = 0; i < NR_ACT; i++)
		{
			if ((act[i]->nr > 0) && act[i]->_buf0)
			{
				memset(act[i]->_buf0, 0, act[i]->msize * act[i]->nr * act[i]->nr2);
			}
		}
	}

	if (curr == 1)
	{

		int i;

		for (i = 0; i < NR_ACT; i++)
		{
			if ((act[i]->nr > 0) && act[i]->_buf1)
			{
				memset(act[i]->_buf1, 0, act[i]->msize * act[i]->nr * act[i]->nr2);
			}
		}
	}
}

/*
 ***************************************************************************
 * Allocate and init structures, according to system state.
 ***************************************************************************
 */
void sa_sys_init(void)
{
	int i;

	for (i = 0; i < NR_ACT; i++)
	{

		if (act[i]->f_count)
		{
			/* Number of items is not a constant and should be calculated */
			act[i]->nr = (*act[i]->f_count)(act[i]);
		}

		if (act[i]->nr > 0)
		{
			if (act[i]->f_count2)
			{
				act[i]->nr2 = (*act[i]->f_count2)(act[i]);
			}
			/* else act[i]->nr2 is a constant and doesn't need to be calculated */

			if (!act[i]->nr2)
			{
				act[i]->nr = 0;
			}
		}

		if (act[i]->nr > 0)
		{
			/* Allocate structures for current activity */
			SREALLOC(act[i]->_buf0, void, act[i]->msize *act[i]->nr *act[i]->nr2);
			// add 2024
			SREALLOC(act[i]->_buf1, void, act[i]->msize *act[i]->nr *act[i]->nr2);
		}
		else
		{
			/* No items found: Invalidate current activity */
			act[i]->options &= ~AO_COLLECTED;
		}

		/* Set default activity list */
		id_seq[i] = act[i]->id;
	}
}

/*
 ***************************************************************************
 * Free structures.
 ***************************************************************************
 */
void sa_sys_free(void)
{
	int i;

	for (i = 0; i < NR_ACT; i++)
	{

		if (act[i]->nr > 0)
		{
			if (act[i]->_buf0)
			{
				free(act[i]->_buf0);
				free(act[i]->_buf1);
				act[i]->_buf0 = NULL;
				act[i]->_buf1 = NULL;
			}
		}
	}
}

/*
 ***************************************************************************
 * Write data to file. If the write() call was interrupted by a signal, try
 * again so that the whole buffer can be written.
 *
 * IN:
 * @fd		Output file descriptor.
 * @buf		Data buffer.
 * @nr_bytes	Number of bytes to write.
 *
 * RETURNS:
 * Number of bytes written to file, or -1 on error.
 ***************************************************************************
 */
int write_all(int fd, const void *buf, int nr_bytes)
{
	int block, offset = 0;
	char *buffer = (char *)buf;

	while (nr_bytes > 0)
	{
		block = write(fd, &buffer[offset], nr_bytes);

		if (block < 0)
		{
			if (errno == EINTR)
				continue;
			return block;
		}
		if (block == 0)
			return offset;

		offset += block;
		nr_bytes -= block;
	}

	return offset;
}

/*
 ***************************************************************************
 * If -L option used, request a non-blocking, exclusive lock on the file.
 * If lock would block, then another process (possibly sadc) has already
 * opened that file => exit.
 *
 * IN:
 * @fd		Output file descriptor.
 * @fatal	Indicate if failing to lock file should be fatal or not.
 * 		If it's not fatal then we'll wait for next iteration and
 * 		try again.
 *
 * RETURNS:
 * 0 on success, or 1 if file couldn't be locked.
 ***************************************************************************
 */
int ask_for_flock(int fd, int fatal)
{
	/* Option -L may be used only if an outfile was specified on the command line */
	if (LOCK_FILE(flags))
	{
		/*
		 * Yes: Try to lock file. To make code portable, check for both EWOULDBLOCK
		 * and EAGAIN return codes, and treat them the same (glibc documentation).
		 * Indeed, some Linux ports (e.g. hppa-linux) do not equate EWOULDBLOCK and
		 * EAGAIN like every other Linux port.
		 */
		if (flock(fd, LOCK_EX | LOCK_NB) < 0)
		{
			if ((((errno == EWOULDBLOCK) || (errno == EAGAIN)) && (fatal == FATAL)) ||
				((errno != EWOULDBLOCK) && (errno != EAGAIN)))
			{
				perror("flock");
				exit(1);
			}
			/* Was unable to lock file: Lock would have blocked... */
			return 1;
		}
		else
		{
			/* File successfully locked */
			flags |= S_F_FILE_LOCKED;
		}
	}
	return 0;
}

/*
 ***************************************************************************
 * Fill system activity file magic header.
 *
 * IN:
 * @file_magic	System activity file magic header.
 ***************************************************************************
 */
void fill_magic_header(struct file_magic *file_magic)
{
	char *v;
	char version[16];

	memset(file_magic, 0, FILE_MAGIC_SIZE);

	file_magic->sysstat_magic = SYSSTAT_MAGIC;
	file_magic->format_magic = FORMAT_MAGIC;
	file_magic->sysstat_extraversion = 0;

	strcpy(version, VERSION);

	/* Get version number */
	if ((v = strtok(version, ".")) == NULL)
		return;
	file_magic->sysstat_version = atoi(v) & 0xff;

	/* Get patchlevel number */
	if ((v = strtok(NULL, ".")) == NULL)
		return;
	file_magic->sysstat_patchlevel = atoi(v) & 0xff;

	/* Get sublevel number */
	if ((v = strtok(NULL, ".")) == NULL)
		return;
	file_magic->sysstat_sublevel = atoi(v) & 0xff;

	/* Get extraversion number. Don't necessarily exist */
	if ((v = strtok(NULL, ".")) == NULL)
		return;
	file_magic->sysstat_extraversion = atoi(v) & 0xff;
}

/*
 ***************************************************************************
 * Fill system activity file header, then write it (or print it if stdout).
 *
 * IN:
 * @fd	Output file descriptor. May be stdout.
 ***************************************************************************
 */
void setup_file_hdr(int fd)
{
	int n, i, p;
	struct tm rectime;
	struct utsname header;
	struct file_magic file_magic;
	struct file_activity file_act;

	/* Fill then write file magic header */
	fill_magic_header(&file_magic);

	if ((n = write_all(fd, &file_magic, FILE_MAGIC_SIZE)) != FILE_MAGIC_SIZE)
		goto write_error;

	/* First reset the structure */
	memset(&file_hdr, 0, FILE_HEADER_SIZE);

	/* Then get current date */
	file_hdr.sa_ust_time = get_time(&rectime, 0);

	/* OK, now fill the header */
	file_hdr.sa_nr_act = get_activity_nr(act, AO_COLLECTED, COUNT_ACTIVITIES);
	file_hdr.sa_day = rectime.tm_mday;
	file_hdr.sa_month = rectime.tm_mon;
	file_hdr.sa_year = rectime.tm_year;
	file_hdr.sa_sizeof_long = sizeof(long);

	/* Get system name, release number, hostname and machine architecture */
	uname(&header);
	strncpy(file_hdr.sa_sysname, header.sysname, UTSNAME_LEN);
	file_hdr.sa_sysname[UTSNAME_LEN - 1] = '\0';
	strncpy(file_hdr.sa_nodename, header.nodename, UTSNAME_LEN);
	file_hdr.sa_nodename[UTSNAME_LEN - 1] = '\0';
	strncpy(file_hdr.sa_release, header.release, UTSNAME_LEN);
	file_hdr.sa_release[UTSNAME_LEN - 1] = '\0';
	strncpy(file_hdr.sa_machine, header.machine, UTSNAME_LEN);
	file_hdr.sa_machine[UTSNAME_LEN - 1] = '\0';

	/* Write file header */
	if ((n = write_all(fd, &file_hdr, FILE_HEADER_SIZE)) != FILE_HEADER_SIZE)
		goto write_error;

	/* Write activity list */
	for (i = 0; i < NR_ACT; i++)
	{

		/*
		 * Activity sequence given by id_seq array.
		 * Sequence must be the same for stdout as for output file.
		 */
		if (!id_seq[i])
			continue;
		if ((p = get_activity_position(act, id_seq[i])) < 0)
			continue;

		if (IS_COLLECTED(act[p]->options))
		{
			file_act.id = act[p]->id;
			file_act.magic = act[p]->magic;
			file_act.nr = act[p]->nr;
			file_act.nr2 = act[p]->nr2;
			file_act.size = act[p]->fsize;

			if ((n = write_all(fd, &file_act, FILE_ACTIVITY_SIZE)) != FILE_ACTIVITY_SIZE)
				goto write_error;
		}
	}

	return;

write_error:

	fprintf(stderr, _("Cannot write system activity file header: %s\n"),
			strerror(errno));
	exit(2);
}

/*
 ***************************************************************************
 * sadc called with interval and count parameters not set:
 * Write a dummy record notifying a system restart, or insert a comment in
 * binary data file if option -C has been used.
 * Writing a dummy record should typically be done at boot time,
 * before the cron daemon is started to avoid conflict with sa1/sa2 scripts.
 *
 * IN:
 * @ofd		Output file descriptor.
 * @rtype	Record type to write (dummy or comment).
 ***************************************************************************
 */
void write_special_record(int ofd, int rtype)
{
	int n;
	struct tm rectime;

	/* Check if file is locked */
	if (!FILE_LOCKED(flags))
	{
		ask_for_flock(ofd, FATAL);
	}

	/* Reset the structure (not compulsory, but a bit cleaner) */
	memset(&record_hdr, 0, RECORD_HEADER_SIZE);

	/* Set record type */
	record_hdr.record_type = rtype;

	/* Save time */
	record_hdr.ust_time = get_time(&rectime, 0);

	record_hdr.hour = rectime.tm_hour;
	record_hdr.minute = rectime.tm_min;
	record_hdr.second = rectime.tm_sec;

	/* Write record now */
	if ((n = write_all(ofd, &record_hdr, RECORD_HEADER_SIZE)) != RECORD_HEADER_SIZE)
	{
		p_write_error();
	}

	if (rtype == R_COMMENT)
	{
		/* Also write the comment */
		if ((n = write_all(ofd, comment, MAX_COMMENT_LEN)) != MAX_COMMENT_LEN)
		{
			p_write_error();
		}
	}
}

/*
 ***************************************************************************
 * Write stats (or print them if stdout).
 *
 * IN:
 * @ofd		Output file descriptor. May be stdout.
 ***************************************************************************
 */
void write_stats(int ofd)
{
	int i, n, p;

	/* Try to lock file */
	if (!FILE_LOCKED(flags))
	{
		if (ask_for_flock(ofd, NON_FATAL))
			/*
			 * Unable to lock file:
			 * Wait for next iteration to try again to save data.
			 */
			return;
	}

	/* Write record header */
	if ((n = write_all(ofd, &record_hdr, RECORD_HEADER_SIZE)) != RECORD_HEADER_SIZE)
	{
		p_write_error();
	}

	/* Then write all statistics */
	for (i = 0; i < NR_ACT; i++)
	{

		if (!id_seq[i])
			continue;
		if ((p = get_activity_position(act, id_seq[i])) < 0)
			continue;

		if (IS_COLLECTED(act[p]->options))
		{
			if ((n = write_all(ofd, act[p]->_buf0, act[p]->fsize * act[p]->nr * act[p]->nr2)) !=
				(act[p]->fsize * act[p]->nr * act[p]->nr2))
			{
				p_write_error();
			}
		}
	}
}

/*
 ***************************************************************************
 * Create a system activity daily data file.
 *
 * IN:
 * @ofile	Name of output file.
 *
 * OUT:
 * @ofd		Output file descriptor.
 ***************************************************************************
 */
void create_sa_file(int *ofd, char *ofile)
{
	if ((*ofd = open(ofile, O_CREAT | O_WRONLY,
					 S_IRUSR | S_IWUSR | S_IRGRP | S_IROTH)) < 0)
	{
		fprintf(stderr, _("Cannot open %s: %s\n"), ofile, strerror(errno));
		exit(2);
	}

	/* Try to lock file */
	ask_for_flock(*ofd, FATAL);

	/* Truncate file */
	if (ftruncate(*ofd, 0) < 0)
	{
		fprintf(stderr, _("Cannot open %s: %s\n"), ofile, strerror(errno));
		exit(2);
	}

	/* Write file header */
	setup_file_hdr(*ofd);
}

/*
 ***************************************************************************
 * Get descriptor for stdout.
 *
 * IN:
 * @stdfd	A value >= 0 indicates that stats data should also
 *		be written to stdout.
 *
 * OUT:
 * @stdfd	Stdout file descriptor.
 ***************************************************************************
 */
void open_stdout(int *stdfd)
{
	if (*stdfd >= 0)
	{
		if ((*stdfd = dup(STDOUT_FILENO)) < 0)
		{
			perror("dup");
			exit(4);
		}
		/* Write file header on STDOUT */
		setup_file_hdr(*stdfd);
	}
}

/*
 ***************************************************************************
 * Get descriptor for output file and write its header.
 * We may enter this function several times (when we rotate a file).
 *
 * IN:
 * @ofile	Name of output file.
 *
 * OUT:
 * @ofd		Output file descriptor.
 ***************************************************************************
 */
void open_ofile(int *ofd, char ofile[])
{
	struct file_magic file_magic;
	struct file_activity file_act[NR_ACT];
	struct tm rectime;
	ssize_t sz;
	int i, p;

	if (ofile[0])
	{
		/* Does file exist? */
		if (access(ofile, F_OK) < 0)
		{
			/* NO: Create it */
			create_sa_file(ofd, ofile);
		}
		else
		{
			/* YES: Append data to it if possible */
			if ((*ofd = open(ofile, O_APPEND | O_RDWR)) < 0)
			{
				fprintf(stderr, _("Cannot open %s: %s\n"), ofile, strerror(errno));
				exit(2);
			}

			/* Read file magic header */
			sz = read(*ofd, &file_magic, FILE_MAGIC_SIZE);
			if (!sz)
			{
				close(*ofd);
				/* This is an empty file: Create it again */
				create_sa_file(ofd, ofile);
				return;
			}
			if ((sz != FILE_MAGIC_SIZE) ||
				(file_magic.sysstat_magic != SYSSTAT_MAGIC) ||
				(file_magic.format_magic != FORMAT_MAGIC))
			{
				if (FORCE_FILE(flags))
				{
					close(*ofd);
					/* -F option used: Truncate file */
					create_sa_file(ofd, ofile);
					return;
				}
				/* Display error message and exit */
				handle_invalid_sa_file(ofd, &file_magic, ofile, sz);
			}

			/* Read file standard header */
			if (read(*ofd, &file_hdr, FILE_HEADER_SIZE) != FILE_HEADER_SIZE)
			{
				/* Display error message and exit */
				handle_invalid_sa_file(ofd, &file_magic, ofile, 0);
			}

			/*
			 * If we are using the standard daily data file (file specified
			 * as "-" on the command line) and it is from a past month,
			 * then overwrite (truncate) it.
			 */
			get_time(&rectime, 0);

			if (((file_hdr.sa_month != rectime.tm_mon) ||
				 (file_hdr.sa_year != rectime.tm_year)) &&
				WANT_SA_ROTAT(flags))
			{
				close(*ofd);
				create_sa_file(ofd, ofile);
				return;
			}

			/* OK: It's a true system activity file */
			if (!file_hdr.sa_nr_act || (file_hdr.sa_nr_act > NR_ACT))
				/*
				 * No activities at all or at least one unknown activity:
				 * Cannot append data to such a file.
				 */
				goto append_error;

			for (i = 0; i < file_hdr.sa_nr_act; i++)
			{

				/* Read current activity in list */
				if (read(*ofd, &file_act[i], FILE_ACTIVITY_SIZE) != FILE_ACTIVITY_SIZE)
				{
					handle_invalid_sa_file(ofd, &file_magic, ofile, 0);
				}

				p = get_activity_position(act, file_act[i].id);

				if ((p < 0) || (act[p]->fsize != file_act[i].size) ||
					(act[p]->magic != file_act[i].magic))
					/*
					 * Unknown activity in list or item size has changed or
					 * unknown activity format.
					 */
					goto append_error;

				if ((act[p]->nr != file_act[i].nr) || (act[p]->nr2 != file_act[i].nr2))
				{
					if (IS_REMANENT(act[p]->options) || !file_act[i].nr || !file_act[i].nr2)
						/*
						 * Remanent structures cannot have a different number of items.
						 * Also number of items and subitems should never be null.
						 */
						goto append_error;
				}
			}

			/*
			 * OK: All tests successfully passed.
			 * List of activities from the file prevails over that of the user.
			 * So unselect all of them. And reset activity sequence.
			 */
			for (i = 0; i < NR_ACT; i++)
			{
				act[i]->options &= ~AO_COLLECTED;
				id_seq[i] = 0;
			}

			for (i = 0; i < file_hdr.sa_nr_act; i++)
			{

				p = get_activity_position(act, file_act[i].id);

				if ((act[p]->nr != file_act[i].nr) || (act[p]->nr2 != file_act[i].nr2))
				{
					/*
					 * Force number of items (serial lines, network interfaces...)
					 * and sub-items to that of the file, and reallocate structures.
					 */
					act[p]->nr = file_act[i].nr;
					act[p]->nr2 = file_act[i].nr2;
					SREALLOC(act[p]->_buf0, void, act[p]->msize *act[p]->nr *act[p]->nr2);
				}

				/* Save activity sequence */
				id_seq[i] = file_act[i].id;
				act[p]->options |= AO_COLLECTED;
			}
		}
	}

	return;

append_error:

	close(*ofd);
	if (FORCE_FILE(flags))
	{
		/* Truncate file */
		create_sa_file(ofd, ofile);
	}
	else
	{
		fprintf(stderr, _("Cannot append data to that file (%s)\n"), ofile);
		exit(1);
	}
}

/*
 ***************************************************************************
 * Read statistics from various system files.
 ***************************************************************************
 */
void read_stats(int curr, unsigned long long *uptime)
{
	int i;
	//	__nr_t cpu_nr = act[get_activity_position(act, A_CPU)]->nr;

	// 确保os变量被初始化
	if (!os) {
		os = calloc(1, sizeof(os_data));
		if (!os) {
			write_log("错误：无法分配os内存\n");
			return;
		}
	}

	/*
	 * Init uptime0. So if /proc/uptime cannot fill it,
	 * this will be done by /proc/stat.
	 * If cpu_nr = 2, force /proc/stat to fill it.
	 * If cpu_nr = 1, uptime0 and uptime are equal.
	 * NB: uptime0 is always filled.
	 * Remember that cpu_nr = 1 means one CPU and no SMP kernel
	 * (one structure for CPU "all") and cpu_nr = 2 means one CPU
	 * and an SMP kernel (two structures for CPUs "all" and "0").
	 */
	/*
		record_hdr.uptime0 = 0;
		if (cpu_nr > 2) {
			read_uptime(&(record_hdr.uptime0));
		}
	*/
	read_uptime(uptime);

	for (i = 0; i < NR_ACT; i++)
	{
		// i=12是wrap_read_net_dev
		if (i == 12)
		{
			/* Read statistics for current activity */

			(*act[i]->f_read)(act[i], curr);
		}
		// i=13是wrap_read_net_edev
		else if (i == 13)
		{
			/* Read statistics for current activity (i=13) */

			(*act[i]->f_read)(act[i], curr);
		}
		// i=5是wrap_read_io
		else if (i == 5)
		{
			/* Read statistics for current activity (i=5) */

			(*act[i]->f_read)(act[i], curr);
		}
		// i=11是wrap_read_disk
		else if (i == 11)
		{
			/* Read statistics for current activity (i=11) */

			(*act[i]->f_read)(act[i], curr);
		}
		// print_filesystem_stats
		else if (i == 36)
		{
			/* Read statistics for current activity (i=38) */
			(*act[i]->f_read)(act[i], curr);
		}
		// wrap_read_loadavg
		else if (i == 9)
		{
			(*act[i]->f_read)(act[i], curr);
		}
	}

	// 添加网络安全监控
	read_net_security(&net_security_stats, deep_monitoring_mode);

	// 将网络安全数据传递到os结构中（如果os存在）
	if (os && os[0].network_security.total_connections == 0) {  // 只在第一次或数据为空时更新
		os[0].network_security.total_connections = net_security_stats.total_connections;
		os[0].network_security.established_connections = net_security_stats.established_connections;
		os[0].network_security.syn_recv_connections = net_security_stats.syn_recv_connections;
		os[0].network_security.time_wait_connections = net_security_stats.time_wait_connections;
		os[0].network_security.listen_connections = net_security_stats.listen_connections;
		os[0].network_security.max_connections_per_ip = net_security_stats.max_connections_per_ip;
		// MySQL数据库连接监控数据复制
		os[0].network_security.mysql_connections_per_second = net_security_stats.mysql_connections_per_second;
		os[0].network_security.mysql_aborted_connections = net_security_stats.mysql_aborted_connections;
		os[0].network_security.max_mysql_conn_per_ip = net_security_stats.max_mysql_conn_per_ip;
		strcpy(os[0].network_security.high_freq_mysql_client_ip, net_security_stats.high_freq_mysql_client_ip);
		os[0].network_security.mysql_monitoring_enabled = net_security_stats.mysql_monitoring_enabled;
		strcpy(os[0].network_security.mysql_variant, net_security_stats.mysql_variant);
		os[0].network_security.mysql_port_count = net_security_stats.mysql_port_count;
		memcpy(os[0].network_security.mysql_ports, net_security_stats.mysql_ports, sizeof(net_security_stats.mysql_ports));
		strcpy(os[0].network_security.mysql_log_status, net_security_stats.mysql_log_status);
		strcpy(os[0].network_security.mysql_log_path, net_security_stats.mysql_log_path);
		os[0].network_security.rx_bytes = net_security_stats.rx_bytes;
		os[0].network_security.tx_bytes = net_security_stats.tx_bytes;
		os[0].network_security.rx_packets = net_security_stats.rx_packets;
		os[0].network_security.tx_packets = net_security_stats.tx_packets;
		os[0].network_security.rx_bytes_per_sec = net_security_stats.rx_bytes_per_sec;
		os[0].network_security.tx_bytes_per_sec = net_security_stats.tx_bytes_per_sec;
		os[0].network_security.rx_packets_per_sec = net_security_stats.rx_packets_per_sec;
		os[0].network_security.tx_packets_per_sec = net_security_stats.tx_packets_per_sec;
		os[0].network_security.anomaly_score = net_security_stats.anomaly_score;
		os[0].network_security.trigger_packet_analysis = net_security_stats.trigger_packet_analysis;
		strcpy(os[0].network_security.anomaly_reason, net_security_stats.anomaly_reason);

		// 传递端口统计数据
		os[0].network_security.port_stats_count = net_security_stats.port_stats_count;
		for (int i = 0; i < net_security_stats.port_stats_count && i < 10; i++) {
			os[0].network_security.port_stats[i].port = net_security_stats.port_stats[i].port;
			os[0].network_security.port_stats[i].total_connections = net_security_stats.port_stats[i].total_connections;
			os[0].network_security.port_stats[i].established = net_security_stats.port_stats[i].established;
			os[0].network_security.port_stats[i].time_wait = net_security_stats.port_stats[i].time_wait;
			os[0].network_security.port_stats[i].syn_recv = net_security_stats.port_stats[i].syn_recv;
			os[0].network_security.port_stats[i].listen = net_security_stats.port_stats[i].listen;
			strcpy(os[0].network_security.port_stats[i].service_name, net_security_stats.port_stats[i].service_name);
		}

		// 传递详细IP+端口统计数据
		os[0].network_security.ip_port_stats_count = net_security_stats.ip_port_stats_count;
		for (int i = 0; i < net_security_stats.ip_port_stats_count && i < 20; i++) {
			strcpy(os[0].network_security.ip_port_stats[i].ip_address, net_security_stats.ip_port_stats[i].ip_address);
			os[0].network_security.ip_port_stats[i].port = net_security_stats.ip_port_stats[i].port;
			os[0].network_security.ip_port_stats[i].connection_count = net_security_stats.ip_port_stats[i].connection_count;
			strcpy(os[0].network_security.ip_port_stats[i].service_name, net_security_stats.ip_port_stats[i].service_name);
			strcpy(os[0].network_security.ip_port_stats[i].process_info, net_security_stats.ip_port_stats[i].process_info);
		}
	}
}

/*
 ***************************************************************************
 * Main loop: Read stats from the relevant sources and display them.
 *
 * IN:
 * @count		Number of lines of stats to display.
 * @rectime		Current date and time.
 * @stdfd		Stdout file descriptor.
 * @ofd			Output file descriptor.
 * @ofile		Name of output file.
 ***************************************************************************
 */
void rw_sa_stat_loop(long count, struct tm *rectime, int stdfd, int ofd,
					 char ofile[])
{
	int do_sa_rotat = 0;
	unsigned int save_flags;
	char new_ofile[MAX_FILE_LEN];

	new_ofile[0] = '\0';

	/* Set a handler for SIGINT */
	/*
	memset(&int_act, 0, sizeof(int_act));
	int_act.sa_handler = (void *) int_handler1;
	sigaction(SIGINT, &int_act, NULL);
	*/
	read_stats(0, &(record_hdr.uptime0));
	int curr = 1;
	int itv;
	/* Main loop */
	do
	{

		
		sar_lk = 1;
		
		//char *s=get_time2();
		
		//打印itv
		
		//printf("########################sadc1线程等待启动信号... itv=%d 当前时间: %s\n", itv, s);
		//free(s);
		// B线程：首先到门禁1等待A完成工作
		write_log("B线程(sadc1)到达门禁1，等待A线程完成工作...\n");
		int barrier_ret = efficient_barrier_wait(&barrier_a_done);
		if (barrier_ret < 0) {
			write_log("B线程：门禁1等待失败\n");
			goto cleanup_and_exit;
		}
		write_log("B线程：通过门禁1，A线程已完成工作，开始自己的工作...\n");

		// 在开始处理前再次检查退出信号
		if (sigint_caught) {
			write_log("sadc1线程收到退出信号，准备退出...\n");
			goto cleanup_and_exit;
		}

		write_log("sadc1线程收到启动信号，开始执行...\n");

		/* Read then write stats */
		read_stats(curr, &(record_hdr.uptime));
		// 更新itv
		itv = get_interval(record_hdr.uptime0, record_hdr.uptime);

		// 直接打印结果
		int i;
		for (i = 0; i < NR_ACT; i++)
		{

			if (i == 12)
			{
				/* Display current activity statistics */
				(*act[i]->f_print)(act[i], !curr, curr, itv);
			}
			else if (i == 13)
			{
				/* Display current activity statistics (i=13) */
				(*act[i]->f_print)(act[i], !curr, curr, itv);
			}
			else if (i == 5)
			{
				/* Display current activity statistics (i=5) */
				(*act[i]->f_print)(act[i], !curr, curr, itv);
			}
			else if (i == 11)
			{
				/* Display current activity statistics (i=11) */
				(*act[i]->f_print)(act[i], !curr, curr, itv);
			}
			else if (i == 36)
			{
				/* Read statistics for current activity (i=38) */
				(*act[i]->f_print)(act[i], !curr, curr, itv);
			}
			else if (i == 9)
			{
				(*act[i]->f_print)(act[i], !curr, curr, itv);
			}
		}



		// B线程：完成工作后到门禁2通知A线程
		write_log("B线程：完成工作，到达门禁2通知A线程...\n");
		barrier_ret = efficient_barrier_wait(&barrier_bc_done);
		if (barrier_ret < 0) {
			write_log("B线程：门禁2通知失败\n");
		} else {
			write_log("B线程：已通过门禁2，A线程收到完成通知\n");
		}
		sar_lk = 0;

		curr ^= 1;
		record_hdr.uptime0 = record_hdr.uptime;

		/*
		 * Init all structures.
		 * Exception for individual CPUs structures which must not be
		 * init'ed to keep values for CPU before they were disabled.
		 */
		reset_stats(curr);


	} while (!sigint_caught);

cleanup_and_exit:
	write_log("sadc1线程开始清理并退出...\n");
	// B线程：退出时也要通过门禁2
	write_log("B线程：退出时通过门禁2...\n");
	int barrier_ret = efficient_barrier_wait(&barrier_bc_done);
	if (barrier_ret < 0) {
		write_log("B线程：退出时门禁2失败\n");
	}
	return;
}

void handle_signal(int signum)
{
	// printf("Caught signal %d\n", signum);
	// 处理信号...
	sleep(1);
	if (sar_lock1 == 0)
	{
		sar_lock1 = 1;
	}
	else
		sar_lock1 = 0;
}

/*
 ***************************************************************************
 * Main entry to the program.
 ***************************************************************************
 */
int sadc1()
{

	int argc = 3;
	char *args1[] = {"xx", "-z", "1"};
	char **argv = args1;
	int opt = 0;
	char ofile[MAX_FILE_LEN];
	struct tm rectime;
	int stdfd = 0, ofd = -1;
	long count = 0;

	/* Get HZ */
	get_HZ();

	/* Compute page shift in kB */
	get_kb_shift();

	ofile[0] = comment[0] = '\0';

#ifdef HAVE_SENSORS
	/* Initialize sensors, let it use the default cfg file */
	int err = sensors_init(NULL);
	if (err)
	{
		fprintf(stderr, "sensors_init: %s\n", sensors_strerror(err));
	}
#endif /* HAVE_SENSORS */

#ifdef USE_NLS
	/* Init National Language Support */
	init_nls();
#endif

	while (++opt < argc)
	{

		if (!strcmp(argv[opt], "-S"))
		{
			if (argv[++opt])
			{
				parse_sadc_S_option(argv, opt);
			}
			else
			{
				usage(argv[0]);
			}
		}

		else if (!strcmp(argv[opt], "-F"))
		{
			flags |= S_F_FORCE_FILE;
		}

		else if (!strcmp(argv[opt], "-L"))
		{
			flags |= S_F_LOCK_FILE;
		}

		else if (!strcmp(argv[opt], "-V"))
		{
			print_version();
		}

		else if (!strcmp(argv[opt], "-z"))
		{
			/* Set by sar command */
			optz = 1;
		}

		else if (!strcmp(argv[opt], "-C"))
		{
			if (argv[++opt])
			{
				strncpy(comment, argv[opt], MAX_COMMENT_LEN);
				comment[MAX_COMMENT_LEN - 1] = '\0';
				if (!strlen(comment))
				{
					usage(argv[0]);
				}
			}
			else
			{
				usage(argv[0]);
			}
		}

		else if (strspn(argv[opt], DIGITS) != strlen(argv[opt]))
		{
			if (!ofile[0])
			{
				stdfd = -1; /* Don't write to STDOUT */
				if (!strcmp(argv[opt], "-"))
				{
					/* File name set to '-' */
					set_default_file(&rectime, ofile, 0);
					flags |= S_F_SA_ROTAT;
				}
				else if (!strncmp(argv[opt], "-", 1))
				{
					/* Bad option */
					usage(argv[0]);
				}
				else
				{
					/* Write data to file */
					strncpy(ofile, argv[opt], MAX_FILE_LEN);
					ofile[MAX_FILE_LEN - 1] = '\0';
				}
			}
			else
			{
				/* Outfile already specified */
				usage(argv[0]);
			}
		}

		else if (!interval)
		{
			/* Get interval */
			interval = atol(argv[opt]);
			if (interval < 1)
			{
				usage(argv[0]);
			}
			count = -1;
		}

		else if (count <= 0)
		{
			/* Get count value */
			count = atol(argv[opt]);
			if (count < 1)
			{
				usage(argv[0]);
			}
		}

		else
		{
			usage(argv[0]);
		}
	}

	/*
	 * If option -z used, write to STDOUT even if a filename
	 * has been entered on the command line.
	 */
	if (optz)
	{
		stdfd = 0;
	}

	if (!ofile[0])
	{
		/* -L option ignored when writing to STDOUT */
		flags &= ~S_F_LOCK_FILE;
	}

	/* Init structures according to machine architecture */
	sa_sys_init();

	/*
	 * Open output file then STDOUT. Write header for each of them.
	 * NB: Output file must be opened first, because we may change
	 * the activities collected AND the activity sequence to that
	 * of the file, and the activities collected and activity sequence
	 * written on STDOUT must be consistent to those of the file.
	 */
	//	open_ofile(&ofd, ofile);
	//	open_stdout(&stdfd);

	if (!interval)
	{
		if (ofd >= 0)
		{
			/*
			 * Interval (and count) not set:
			 * Write a dummy record, or insert a comment, then exit.
			 * NB: Never write such a dummy record on stdout since
			 * sar never expects it.
			 */
			if (comment[0])
			{
				write_special_record(ofd, R_COMMENT);
			}
			else
			{
				write_special_record(ofd, R_RESTART);
			}

			/* Close file descriptor */
			CLOSE(ofd);
		}

		/* Free structures */
		sa_sys_free();
		exit(0);
	}

	/* Set a handler for SIGALRM */
	/*
		memset(&alrm_act, 0, sizeof(alrm_act));
		alrm_act.sa_handler = (void *) alarm_handler;
		sigaction(SIGALRM, &alrm_act, NULL);
		alarm(interval);
	*/
	// add 2024
	/*
				struct sigaction sa;
			sa.sa_handler = &handle_signal;
			sigemptyset(&sa.sa_mask);
			sa.sa_flags = 0;
			if (sigaction(SIGUSR1, &sa, NULL) == -1) {
				perror("sigaction failed");
				exit(EXIT_FAILURE);
			}

	*/

	/* Set a handler for SIGUSR1 */
	/* memset(&lock1, 0, sizeof(lock1));
	lock1.sa_handler = (void *)handle_signal;
	sigaction(SIGUSR1, &lock1, NULL);
 */
	/* Main loop */
	count = 1;
	rw_sa_stat_loop(count, &rectime, stdfd, ofd, ofile);

#ifdef HAVE_SENSORS
	/* Cleanup sensors */
	sensors_cleanup();
#endif /* HAVE_SENSORS */

	/* Free structures */
	sa_sys_free();
	pthread_mutex_lock(&xxx);
	release1++;
	pthread_mutex_unlock(&xxx);
	// sem_post(&completion_sem); // 发送完成信号
	//  打印线程退出
	write_log("***********sadc1线程退出\n");
	
	return 0;
}
