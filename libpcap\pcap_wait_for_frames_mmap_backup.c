/*
 * 备份的 pcap_wait_for_frames_mmap 函数
 * 备份时间: 当前时间
 * 备份原因: 在 poll 调用前添加 epoll 监控，怀疑 poll 对 timerfd 没有生效
 */

static int pcap_wait_for_frames_mmap_backup(pcap_t *handle, int timer_fd)
{
	struct pcap_linux *handlep = handle->priv;
	int timeout;
	struct ifreq ifr;
	int ret;
	struct pollfd pollinfo[3];  // 增加一个位置给定时器
	int numpollinfo;
	extern volatile int pcap_timeout1;  // POSIX定时器全局标志
	extern int current_timer_type;

	pollinfo[0].fd = handle->fd;
	pollinfo[0].events = POLLIN;

	if ( handlep->poll_breakloop_fd == -1 ) {
		numpollinfo = 1;
		pollinfo[1].revents = 0;
		pollinfo[2].revents = 0;
		/*
		 * We set pollinfo[1].revents to zero, even though
		 * numpollinfo = 1 meaning that poll() doesn't see
		 * pollinfo[1], so that we do not have to add a
		 * conditional of numpollinfo > 1 below when we
		 * test pollinfo[1].revents.
		 */
	} else {
		pollinfo[1].fd = handlep->poll_breakloop_fd;
		pollinfo[1].events = POLLIN;
		numpollinfo = 2;
		pollinfo[2].revents = 0;
	}

	// 根据定时器类型决定是否添加到poll列表
	if (current_timer_type == 1 && timer_fd >= 0) {
		// timerfd模式：添加到poll列表中
		pollinfo[numpollinfo].fd = timer_fd;
		pollinfo[numpollinfo].events = POLLIN;
		numpollinfo++;
	}

	for (;;) {
		/*
		 * Check POSIX timer signal flag at the beginning of each loop iteration
		 */
		if (current_timer_type == 2 && pcap_timeout1) {
			write_log("pcap_wait_for_frames_mmap: POSIX定时器信号触发(循环开始)，1秒时间到\n");
			close(handle->fd);
			return 88;  // 返回88表示定时器超时
		}

		timeout = handlep->poll_timeout;

		if (handlep->netdown) {
			if (timeout != 0)
				timeout = 1;
		}
		ret = poll(pollinfo, numpollinfo, timeout);
		if (ret < 0) {
			if (errno != EINTR) {
				pcapint_fmt_errmsg_for_errno(handle->errbuf,
				    PCAP_ERRBUF_SIZE, errno,
				    "can't poll on packet socket");
				return PCAP_ERROR;
			}

			if (handle->break_loop) {
				handle->break_loop = 0;
				return PCAP_ERROR_BREAK;
			}
		} else if (ret > 0) {
			// 检查POSIX定时器信号标志
			if (current_timer_type == 2 && pcap_timeout1) {
				write_log("pcap_wait_for_frames_mmap: POSIX定时器信号触发，1秒时间到\n");
				close(handle->fd);
				return 88;  // 返回88表示定时器超时
			}

			// 检查timerfd事件
			if (current_timer_type == 1 && timer_fd >= 0 && numpollinfo >= 3 && pollinfo[2].revents & POLLIN) {
				write_log("pcap_wait_for_frames_mmap: timerfd事件触发，1秒时间到\n");
				// 读取定时器数据以清除事件
				uint64_t timer_data;
				if (read(timer_fd, &timer_data, sizeof(timer_data)) > 0) {
					write_log("timerfd读取成功，触发次数=%llu\n", (unsigned long long)timer_data);
				}
				close(handle->fd);
				return 88;  // 返回88表示定时器超时
			} else if (current_timer_type == 1 && timer_fd >= 0 && numpollinfo == 2 && pollinfo[1].revents & POLLIN && pollinfo[1].fd == timer_fd) {
				write_log("pcap_wait_for_frames_mmap: timerfd事件触发(位置1)，1秒时间到\n");
				// 读取定时器数据以清除事件
				uint64_t timer_data;
				if (read(timer_fd, &timer_data, sizeof(timer_data)) > 0) {
					write_log("timerfd读取成功，触发次数=%llu\n", (unsigned long long)timer_data);
				}
				close(handle->fd);
				return 88;  // 返回88表示定时器超时
			}

			if (pollinfo[0].revents == POLLIN) {
				break;
			}
			// ... 其他错误处理代码省略 ...
		}

		if (handlep->poll_timeout == 0)
			break;
	}
	return 0;
}