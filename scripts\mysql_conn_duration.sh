#!/bin/bash

# MySQL连接持续时间分析脚本
# 分析最近的连接，计算每个连接的持续时间

GENERAL_LOG="/tmp/mysql-logs/general.log"
SAMPLE_SIZE=${1:-100}  # 默认分析最近100个连接

echo "========================================="
echo "MySQL连接持续时间分析"
echo "========================================="
echo "分析样本: 最近${SAMPLE_SIZE}个连接"
echo "日志文件: ${GENERAL_LOG}"
echo "========================================="

# 检查日志文件
if [ ! -f "$GENERAL_LOG" ]; then
    echo "错误: 通用日志文件不存在: $GENERAL_LOG"
    exit 1
fi

# 提取最近的连接数据
echo "正在分析连接数据..."

# 创建临时文件
TEMP_FILE="/tmp/mysql_conn_analysis.tmp"
tail -n 10000 "$GENERAL_LOG" | grep -E "(Connect|Quit)" > "$TEMP_FILE"

# 分析连接持续时间
awk '
BEGIN {
    print "连接ID\t客户端\t\t连接时间\t\t断开时间\t\t持续时间(秒)"
    print "=================================================================="
}
/Connect/ {
    # 提取连接ID和客户端信息
    conn_id = $2
    client = $0
    gsub(/.*Connect[[:space:]]+/, "", client)
    gsub(/[[:space:]]+on.*/, "", client)
    
    # 提取时间戳
    time_str = $1 " " substr($0, index($0, $1) + length($1) + 1, 8)
    connect_time[conn_id] = time_str
    connect_client[conn_id] = client
}
/Quit/ {
    conn_id = $2
    if (conn_id in connect_time) {
        # 提取断开时间
        time_str = $1 " " substr($0, index($0, $1) + length($1) + 1, 8)
        quit_time = time_str
        
        # 计算持续时间（简化计算，只考虑秒数差异）
        split(connect_time[conn_id], ct, ":")
        split(quit_time, qt, ":")
        
        # 提取秒数
        if (length(ct) >= 3 && length(qt) >= 3) {
            connect_sec = ct[3]
            quit_sec = qt[3]
            
            # 简单的秒数差计算
            duration = quit_sec - connect_sec
            if (duration < 0) duration += 60  # 跨分钟处理
            
            printf "%s\t%-20s\t%s\t%s\t%d\n", conn_id, connect_client[conn_id], connect_time[conn_id], quit_time, duration
        }
        
        # 清理已处理的连接
        delete connect_time[conn_id]
        delete connect_client[conn_id]
    }
}
' "$TEMP_FILE" | tail -n $SAMPLE_SIZE

echo ""
echo "========================================="
echo "持续时间统计分析"
echo "========================================="

# 统计持续时间分布
awk '
/Connect/ {
    conn_id = $2
    connect_time[conn_id] = NR
}
/Quit/ {
    conn_id = $2
    if (conn_id in connect_time) {
        duration = NR - connect_time[conn_id]
        if (duration <= 2) short_conn++
        else if (duration <= 5) medium_conn++
        else long_conn++
        total_conn++
        delete connect_time[conn_id]
    }
}
END {
    if (total_conn > 0) {
        printf "短连接 (≤2行): %d (%.1f%%)\n", short_conn, short_conn*100/total_conn
        printf "中等连接(3-5行): %d (%.1f%%)\n", medium_conn, medium_conn*100/total_conn  
        printf "长连接 (>5行): %d (%.1f%%)\n", long_conn, long_conn*100/total_conn
        printf "总连接数: %d\n", total_conn
    }
}
' "$TEMP_FILE"

# 清理临时文件
rm -f "$TEMP_FILE"

echo ""
echo "========================================="
echo "分析完成: $(date)"
echo "========================================="
