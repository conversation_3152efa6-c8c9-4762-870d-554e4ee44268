.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_FILENO 3PCAP "25 July 2018"
.SH NAME
pcap_fileno \- get the file descriptor for a live capture
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
int pcap_fileno(pcap_t *p);
.ft
.fi
.SH DESCRIPTION
If
.I p
refers to a network device that was opened for a live capture using
a combination of
.BR pcap_create (3PCAP)
and
.BR pcap_activate (3PCAP),
or using
.BR pcap_open_live (3PCAP),
.BR pcap_fileno ()
returns the file descriptor from which captured packets are read.
.LP
If
.I p
refers to a ``savefile'' that was opened using functions such as
.BR pcap_open_offline (3PCAP)
or
.BR pcap_fopen_offline (3PCAP),
a ``dead''
.B pcap_t
opened using
.BR pcap_open_dead (3PCAP),
or a
.B pcap_t
that was created with
.BR pcap_create ()
but that has not yet been activated with
.BR pcap_activate (),
it returns
.BR PCAP_ERROR .
.SH SEE ALSO
.BR pcap (3PCAP)
