.\" @(#) $Header: /tcpdump/master/libpcap/pcap.3,v 1.64.2.8 2005/09/07 08:29:17 guy Exp $
.\"
.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP 3 "27 February 2004"
.SH NAME
pcap \- Packet Capture library
.SH SYNOPSIS
.nf
.ft B
#include <pcap.h>
.ft
.LP
.nf
.ft B
char errbuf[PCAP_ERRBUF_SIZE];
.ft
.LP
.ft B
pcap_t *pcap_open_live(const char *device, int snaplen,
.ti +8
int promisc, int to_ms, char *errbuf)
pcap_t *pcap_open_dead(int linktype, int snaplen)
pcap_t *pcap_open_offline(const char *fname, char *errbuf)
pcap_t *pcap_fopen_offline(FILE *fp, char *errbuf)
pcap_dumper_t *pcap_dump_open(pcap_t *p, const char *fname)
pcap_dumper_t *pcap_dump_fopen(pcap_t *p, FILE *fp)
.ft
.LP
.ft B
int pcap_setnonblock(pcap_t *p, int nonblock, char *errbuf);
int pcap_getnonblock(pcap_t *p, char *errbuf);
.ft
.LP
.ft B
int pcap_findalldevs(pcap_if_t **alldevsp, char *errbuf)
void pcap_freealldevs(pcap_if_t *alldevs)
char *pcap_lookupdev(char *errbuf)
int pcap_lookupnet(const char *device, bpf_u_int32 *netp,
.ti +8
bpf_u_int32 *maskp, char *errbuf)
.ft
.LP
.ft B
typedef void (*pcap_handler)(u_char *user, const struct pcap_pkthdr *h,
.ti +8
			     const u_char *bytes);
.ft B
int pcap_dispatch(pcap_t *p, int cnt,
.ti +8
pcap_handler callback, u_char *user)
int pcap_loop(pcap_t *p, int cnt,
.ti +8
pcap_handler callback, u_char *user)
void pcap_dump(u_char *user, struct pcap_pkthdr *h,
.ti +8
u_char *sp)
.ft
.LP
.ft B
int pcap_compile(pcap_t *p, struct bpf_program *fp,
.ti +8
char *str, int optimize, bpf_u_int32 netmask)
int pcap_setfilter(pcap_t *p, struct bpf_program *fp)
void pcap_freecode(struct bpf_program *)
int pcap_setdirection(pcap_t *p, pcap_direction_t d)
.ft
.LP
.ft B
const u_char *pcap_next(pcap_t *p, struct pcap_pkthdr *h)
int pcap_next_ex(pcap_t *p, struct pcap_pkthdr **pkt_header,
.ti +8
const u_char **pkt_data)
.ft
.LP
.ft B
void pcap_breakloop(pcap_t *)
.ft
.LP
.ft B
int pcap_inject(pcap_t *p, const void *buf, size_t size)
int pcap_sendpacket(pcap_t *p, const u_char *buf, int size)
.ft
.LP
.ft B
int pcap_datalink(pcap_t *p)
int pcap_list_datalinks(pcap_t *p, int **dlt_buf);
int pcap_set_datalink(pcap_t *p, int dlt);
int pcap_datalink_name_to_val(const char *name);
const char *pcap_datalink_val_to_name(int dlt);
const char *pcap_datalink_val_to_description(int dlt);
int pcap_snapshot(pcap_t *p)
int pcap_is_swapped(pcap_t *p)
int pcap_major_version(pcap_t *p)
int pcap_minor_version(pcap_t *p)
int pcap_stats(pcap_t *p, struct pcap_stat *ps)
FILE *pcap_file(pcap_t *p)
int pcap_fileno(pcap_t *p)
int pcap_get_selectable_fd(pcap_t *p);
void pcap_perror(pcap_t *p, char *prefix)
char *pcap_geterr(pcap_t *p)
char *pcap_strerror(int error)
const char *pcap_lib_version(void)
.ft
.LP
.ft B
void pcap_close(pcap_t *p)
int pcap_dump_flush(pcap_dumper_t *p)
long pcap_dump_ftell(pcap_dumper_t *p)
FILE *pcap_dump_file(pcap_dumper_t *p)
void pcap_dump_close(pcap_dumper_t *p)
.ft
.fi
.SH DESCRIPTION
The Packet Capture library
provides a high level interface to packet capture systems. All packets
on the network, even those destined for other hosts, are accessible
through this mechanism.
.PP
.SH ROUTINES
NOTE:
.I errbuf
in
.BR pcap_open_live() ,
.BR pcap_open_dead() ,
.BR pcap_open_offline() ,
.BR pcap_fopen_offline() ,
.BR pcap_setnonblock() ,
.BR pcap_getnonblock() ,
.BR pcap_findalldevs() ,
.BR pcap_lookupdev() ,
and
.B pcap_lookupnet()
is assumed to be able to hold at least
.B PCAP_ERRBUF_SIZE
chars.
.PP
.B pcap_open_live()
is used to obtain a packet capture descriptor to look
at packets on the network.
.I device
is a string that specifies the network device to open; on Linux systems
with 2.2 or later kernels, a
.I device
argument of "any" or
.B NULL
can be used to capture packets from all interfaces.
.I snaplen
specifies the maximum number of bytes to capture.  If this value is less
than the size of a packet that is captured, only the first
.I snaplen
bytes of that packet will be captured and provided as packet data.  A
value of 65535 should be sufficient, on most if not all networks, to
capture all the data available from the packet.
.I promisc
specifies if the interface is to be put into promiscuous mode.
(Note that even if this parameter is false, the interface
could well be in promiscuous mode for some other reason.)  For now, this
doesn't work on the "any" device; if an argument of "any" or NULL is
supplied, the
.I promisc
flag is ignored.
.I to_ms
specifies the read timeout in milliseconds.  The read timeout is used to
arrange that the read not necessarily return immediately when a packet
is seen, but that it wait for some amount of time to allow more packets
to arrive and to read multiple packets from the OS kernel in one
operation.  Not all platforms support a read timeout; on platforms that
don't, the read timeout is ignored.  A zero value for
.IR to_ms ,
on platforms that support a read timeout,
will cause a read to wait forever to allow enough packets to
arrive, with no timeout.
.I errbuf
is used to return error or warning text.  It will be set to error text when
.B pcap_open_live()
fails and returns
.BR NULL .
.I errbuf
may also be set to warning text when
.B pcap_open_live()
succeds; to detect this case the caller should store a zero-length string in
.I errbuf
before calling
.B pcap_open_live()
and display the warning to the user if
.I errbuf
is no longer a zero-length string.
.PP
.B pcap_open_dead()
is used for creating a
.B pcap_t
structure to use when calling the other functions in libpcap.  It is
typically used when just using libpcap for compiling BPF code.
.PP
.B pcap_open_offline()
is called to open a ``savefile'' for reading.
.I fname
specifies the name of the file to open. The file has
the same format as those used by
.B tcpdump(1)
and
.BR tcpslice(1) .
The name "-" in a synonym for
.BR stdin .
Alternatively, you may call
.B pcap_fopen_offline()
to read dumped data from an existing open stream
.IR fp .
Note that on Windows, that stream should be opened in binary mode.
.I errbuf
is used to return error text and is only set when
.B pcap_open_offline()
or
.B pcap_fopen_offline()
fails and returns
.BR NULL .
.PP
.B pcap_dump_open()
is called to open a ``savefile'' for writing. The name "-" in a synonym
for
.BR stdout .
.B NULL
is returned on failure.
.I p
is a
.I pcap
struct as returned by
.B pcap_open_offline()
or
.BR pcap_open_live() .
.I fname
specifies the name of the file to open. Alternatively, you may call
.B pcap_dump_fopen()
to write data to an existing open stream
.IR fp .
Note that on Windows, that stream should be opened in binary mode.
If
.B NULL
is returned,
.B pcap_geterr()
can be used to get the error text.
.PP
.PP
.B pcap_setnonblock()
puts a capture descriptor, opened with
.BR pcap_open_live() ,
into ``non-blocking'' mode, or takes it out of ``non-blocking'' mode,
depending on whether the
.I nonblock
argument is non-zero or zero.  It has no effect on ``savefiles''.
If there is an error, \-1 is returned and
.I errbuf
is filled in with an appropriate error message; otherwise, 0 is
returned.
In
``non-blocking'' mode, an attempt to read from the capture descriptor
with
.B pcap_dispatch()
will, if no packets are currently available to be read, return 0
immediately rather than blocking waiting for packets to arrive.
.B pcap_loop()
and
.B pcap_next()
will not work in ``non-blocking'' mode.
.PP
.B pcap_getnonblock()
returns the current ``non-blocking'' state of the capture descriptor; it
always returns 0 on ``savefiles''.
If there is an error, \-1 is returned and
.I errbuf
is filled in with an appropriate error message.
.PP
.B pcap_findalldevs()
constructs a list of network devices that can be opened with
.BR pcap_open_live() .
(Note that there may be network devices that cannot be opened with
.BR pcap_open_live()
by the
process calling
.BR pcap_findalldevs() ,
because, for example, that process might not have sufficient privileges
to open them for capturing; if so, those devices will not appear on the
list.)
.I alldevsp
is set to point to the first element of the list; each element of the
list is of type
.BR pcap_if_t ,
and has the following members:
.RS
.TP
.B next
if not
.BR NULL ,
a pointer to the next element in the list;
.B NULL
for the last element of the list
.TP
.B name
a pointer to a string giving a name for the device to pass to
.B pcap_open_live()
.TP
.B description
if not
.BR NULL ,
a pointer to a string giving a human-readable description of the device
.TP
.B addresses
a pointer to the first element of a list of addresses for the interface
.TP
.B flags
interface flags:
.RS
.TP
.B PCAP_IF_LOOPBACK
set if the interface is a loopback interface
.RE
.RE
.PP
Each element of the list of addresses is of type
.BR pcap_addr_t ,
and has the following members:
.RS
.TP
.B next
if not
.BR NULL ,
a pointer to the next element in the list;
.B NULL
for the last element of the list
.TP
.B addr
a pointer to a
.B "struct sockaddr"
containing an address
.TP
.B netmask
if not
.BR NULL ,
a pointer to a
.B "struct sockaddr"
that contains the netmask corresponding to the address pointed to by
.B addr
.TP
.B broadaddr
if not
.BR NULL ,
a pointer to a
.B "struct sockaddr"
that contains the broadcast address corresponding to the address pointed
to by
.BR addr ;
may be null if the interface doesn't support broadcasts
.TP
.B dstaddr
if not
.BR NULL ,
a pointer to a
.B "struct sockaddr"
that contains the destination address corresponding to the address pointed
to by
.BR addr ;
may be null if the interface isn't a point-to-point interface
.RE
.PP
Note that not all the addresses in the list of addresses are
necessarily IPv4 or IPv6 addresses - you must check the
.B sa_family
member of the
.B "struct sockaddr"
before interpreting the contents of the address.
.PP
.B \-1
is returned on failure, in which case
.B errbuf
is filled in with an appropriate error message;
.B 0
is returned on success.
.PP
.B pcap_freealldevs()
is used to free a list allocated by
.BR pcap_findalldevs() .
.PP
.B pcap_lookupdev()
returns a pointer to a network device suitable for use with
.B pcap_open_live()
and
.BR pcap_lookupnet() .
If there is an error,
.B NULL
is returned and
.I errbuf
is filled in with an appropriate error message.
.PP
.B pcap_lookupnet()
is used to determine the network number and mask
associated with the network device
.BR device .
Both
.I netp
and
.I maskp
are
.I bpf_u_int32
pointers.
A return of \-1 indicates an error in which case
.I errbuf
is filled in with an appropriate error message.
.PP
.B pcap_dispatch()
is used to collect and process packets.
.I cnt
specifies the maximum number of packets to process before returning.
This is not a minimum number; when reading a live capture, only one
bufferful of packets is read at a time, so fewer than
.I cnt
packets may be processed. A
.I cnt
of \-1 processes all the packets received in one buffer when reading a
live capture, or all the packets in the file when reading a
``savefile''.
.I callback
specifies a routine to be called with three arguments:
a
.I u_char
pointer which is passed in from
.BR pcap_dispatch() ,
a
.I const struct pcap_pkthdr
pointer to a structure with the following members:
.RS
.TP
.B ts
a
.I struct timeval
containing the time when the packet was captured
.TP
.B caplen
a
.I bpf_u_int32
giving the number of bytes of the packet that are available from the
capture
.TP
.B len
a
.I bpf_u_int32
giving the length of the packet, in bytes (which might be more than the
number of bytes available from the capture, if the length of the packet
is larger than the maximum number of bytes to capture)
.RE
.PP
and a
.I const u_char
pointer to the first
.B caplen
(as given in the
.I struct pcap_pkthdr
a pointer to which is passed to the callback routine)
bytes of data from the packet (which won't necessarily be the entire
packet; to capture the entire packet, you will have to provide a value
for
.I snaplen
in your call to
.B pcap_open_live()
that is sufficiently large to get all of the packet's data - a value of
65535 should be sufficient on most if not all networks).
.PP
The number of packets read is returned.
0 is returned if no packets were read from a live capture (if, for
example, they were discarded because they didn't pass the packet filter,
or if, on platforms that support a read timeout that starts before any
packets arrive, the timeout expires before any packets arrive, or if the
file descriptor for the capture device is in non-blocking mode and no
packets were available to be read) or if no more packets are available
in a ``savefile.'' A return of \-1 indicates
an error in which case
.B pcap_perror()
or
.B pcap_geterr()
may be used to display the error text.
A return of \-2 indicates that the loop terminated due to a call to
.B pcap_breakloop()
before any packets were processed.
.ft B
If your application uses pcap_breakloop(),
make sure that you explicitly check for \-1 and \-2, rather than just
checking for a return value < 0.
.ft R
.PP
.BR NOTE :
when reading a live capture,
.B pcap_dispatch()
will not necessarily return when the read times out; on some platforms,
the read timeout isn't supported, and, on other platforms, the timer
doesn't start until at least one packet arrives.  This means that the
read timeout should
.B NOT
be used in, for example, an interactive application, to allow the packet
capture loop to ``poll'' for user input periodically, as there's no
guarantee that
.B pcap_dispatch()
will return after the timeout expires.
.PP
.B pcap_loop()
is similar to
.B pcap_dispatch()
except it keeps reading packets until
.I cnt
packets are processed or an error occurs.
It does
.B not
return when live read timeouts occur.
Rather, specifying a non-zero read timeout to
.B pcap_open_live()
and then calling
.B pcap_dispatch()
allows the reception and processing of any packets that arrive when the
timeout occurs.
A negative
.I cnt
causes
.B pcap_loop()
to loop forever (or at least until an error occurs).  \-1 is returned on
an error; 0 is returned if
.I cnt
is exhausted; \-2 is returned if the loop terminated due to a call to
.B pcap_breakloop()
before any packets were processed.
.ft B
If your application uses pcap_breakloop(),
make sure that you explicitly check for \-1 and \-2, rather than just
checking for a return value < 0.
.ft R
.PP
.B pcap_next()
reads the next packet (by calling
.B pcap_dispatch()
with a
.I cnt
of 1) and returns a
.I u_char
pointer to the data in that packet.  (The
.I pcap_pkthdr
struct for that packet is not supplied.)
.B NULL
is returned if an error occured, or if no packets were read from a live
capture (if, for example, they were discarded because they didn't pass
the packet filter, or if, on platforms that support a read timeout that
starts before any packets arrive, the timeout expires before any packets
arrive, or if the file descriptor for the capture device is in
non-blocking mode and no packets were available to be read), or if no
more packets are available in a ``savefile.''  Unfortunately, there is
no way to determine whether an error occured or not.
.PP
.B pcap_next_ex()
reads the next packet and returns a success/failure indication:
.RS
.TP
1
the packet was read without problems
.TP
0
packets are being read from a live capture, and the timeout expired
.TP
\-1
an error occurred while reading the packet
.TP
\-2
packets are being read from a ``savefile'', and there are no more
packets to read from the savefile.
.RE
.PP
If the packet was read without problems, the pointer pointed to by the
.I pkt_header
argument is set to point to the
.I pcap_pkthdr
struct for the packet, and the
pointer pointed to by the
.I pkt_data
argument is set to point to the data in the packet.
.PP
.B pcap_breakloop()
sets a flag that will force
.B pcap_dispatch()
or
.B pcap_loop()
to return rather than looping; they will return the number of packets
that have been processed so far, or \-2 if no packets have been
processed so far.
.PP
This routine is safe to use inside a signal handler on UNIX or a console
control handler on Windows, as it merely sets a flag that is checked
within the loop.
.PP
The flag is checked in loops reading packets from the OS - a signal by
itself will not necessarily terminate those loops - as well as in loops
processing a set of packets returned by the OS.
.ft B
Note that if you are catching signals on UNIX systems that support
restarting system calls after a signal, and calling pcap_breakloop()
in the signal handler, you must specify, when catching those signals,
that system calls should NOT be restarted by that signal.  Otherwise,
if the signal interrupted a call reading packets in a live capture,
when your signal handler returns after calling pcap_breakloop(), the
call will be restarted, and the loop will not terminate until more
packets arrive and the call completes.
.PP
Note also that, in a multi-threaded application, if one thread is
blocked in
.BR pcap_dispatch() ,
.BR pcap_loop() ,
.BR pcap_next() ,
or
.BR pcap_next_ex() ,
a call to
.B pcap_breakloop()
in a different thread will not unblock that thread; you will need to use
whatever mechanism the OS provides for breaking a thread out of blocking
calls in order to unblock the thread, such as thread cancellation in
systems that support POSIX threads.
.ft R
.PP
Note that
.B pcap_next()
will, on some platforms, loop reading packets from the OS; that loop
will not necessarily be terminated by a signal, so
.B pcap_breakloop()
should be used to terminate packet processing even if
.B pcap_next()
is being used.
.PP
.B pcap_breakloop()
does not guarantee that no further packets will be processed by
.B pcap_dispatch()
or
.B pcap_loop()
after it is called; at most one more packet might be processed.
.PP
If \-2 is returned from
.B pcap_dispatch()
or
.BR pcap_loop() ,
the flag is cleared, so a subsequent call will resume reading packets. 
If a positive number is returned, the flag is not cleared, so a
subsequent call will return \-2 and clear the flag.
.PP
.B pcap_inject()
sends a raw packet through the network interface;
.I buf
points to the data of the packet, including the link-layer header, and
.I size
is the number of bytes in the packet.
It returns the number of bytes written on success.  A return of \-1
indicates an error in which case
.B pcap_perror()
or
.B pcap_geterr()
may be used to display the error text.
Note that, even if you successfully open the network interface, you
might not have permission to send packets on it, or it might not support
sending packets; as
.I pcap_open_live()
doesn't have a flag to indicate whether to open for capturing, sending,
or capturing and sending, you cannot request an open that supports
sending and be notified at open time whether sending will be possible.
Note also that some devices might not support sending packets.
.PP
Note that, on some platforms, the link-layer header of the packet that's
sent might not be the same as the link-layer header of the packet
supplied to
.BR pcap_inject() ,
as the source link-layer address, if the header contains such an
address, might be changed to be the address assigned to the interface on
which the packet it sent, if the platform doesn't support sending
completely raw and unchanged packets.  Even worse, some drivers on some
platforms might change the link-layer type field to whatever value
libpcap used when attaching to the device, even on platforms that
.I do
nominally support sending completely raw and unchanged packets.
.PP
.B pcap_sendpacket()
is like
.BR pcap_inject() ,
but it returns 0 on success and \-1 on failure.
.RB ( pcap_inject()
comes from OpenBSD;
.B pcap_sendpacket()
comes from WinPcap.  Both are provided for compatibility.)
.PP
.B pcap_dump()
outputs a packet to the ``savefile'' opened with
.BR pcap_dump_open() .
Note that its calling arguments are suitable for use with
.B pcap_dispatch()
or
.BR pcap_loop() .
If called directly, the 
.I user
parameter is of type 
.I pcap_dumper_t
as returned by
.BR pcap_dump_open() .
.PP
.B pcap_compile()
is used to compile the string
.I str
into a filter program.
.I program
is a pointer to a
.I bpf_program
struct and is filled in by
.BR pcap_compile() .
.I optimize
controls whether optimization on the resulting code is performed.
.I netmask
specifies the IPv4 netmask of the network on which packets are being
captured; it is used only when checking for IPv4 broadcast addresses in
the filter program.  If the netmask of the network on which packets are
being captured isn't known to the program, or if packets are being
captured on the Linux "any" pseudo-interface that can capture on more
than one network, a value of 0 can be supplied; tests for IPv4 broadcast
addreses won't be done correctly, but all other tests in the filter
program will be OK.  A return of \-1 indicates an error in which case
.BR pcap_geterr()
may be used to display the error text.
.PP
.B pcap_compile_nopcap()
is similar to
.B pcap_compile()
except that instead of passing a pcap structure, one passes the
snaplen and linktype explicitly.  It is intended to be used for
compiling filters for direct BPF usage, without necessarily having
called
.BR pcap_open() .
A return of \-1 indicates an error; the error text is unavailable.
.RB ( pcap_compile_nopcap()
is a wrapper around
.BR pcap_open_dead() ,
.BR pcap_compile() ,
and
.BR pcap_close() ;
the latter three routines can be used directly in order to get the error
text for a compilation error.)
.B
.PP
.B pcap_setfilter()
is used to specify a filter program.
.I fp
is a pointer to a
.I bpf_program
struct, usually the result of a call to
.BR pcap_compile() .
.B \-1
is returned on failure, in which case
.BR pcap_geterr()
may be used to display the error text;
.B 0
is returned on success.
.PP
.B pcap_freecode()
is used to free up allocated memory pointed to by a
.I bpf_program
struct generated by
.B pcap_compile()
when that BPF program is no longer needed, for example after it
has been made the filter program for a pcap structure by a call to
.BR pcap_setfilter() .
.PP
.B pcap_setdirection()
is used to specify a direction that packets will be captured.
.I pcap_direction_t 
is one of the constants
.BR PCAP_D_IN ,
.B PCAP_D_OUT
or 
.BR PCAP_D_INOUT .
.B PCAP_D_IN
will only capture packets received by the device,
.B PCAP_D_OUT
will only capture packets sent by the device and
.B PCAP_D_INOUT
will capture packets received by or sent by the device.
.B PCAP_D_INOUT
is the default setting if this function is not called.  This isn't
necessarily supported on all platforms; some platforms might return an
error, and some other platforms might not support
.BR PCAP_D_OUT .
This operation is not supported if a ``savefile'' is being read.
.B \-1
is returned on failure,
.B 0
is returned on success.
.PP
.B pcap_datalink()
returns the link layer type; link layer types it can return include:
.PP
.RS 5
.TP 5
.B DLT_NULL
BSD loopback encapsulation; the link layer header is a 4-byte field, in
.I host
byte order, containing a PF_ value from
.B socket.h
for the network-layer protocol of the packet.
.IP
Note that ``host byte order'' is the byte order of the machine on which
the packets are captured, and the PF_ values are for the OS of the
machine on which the packets are captured; if a live capture is being
done, ``host byte order'' is the byte order of the machine capturing the
packets, and the PF_ values are those of the OS of the machine capturing
the packets, but if a ``savefile'' is being read, the byte order and PF_
values are
.I not
necessarily those of the machine reading the capture file.
.TP 5
.B DLT_EN10MB
Ethernet (10Mb, 100Mb, 1000Mb, and up)
.TP 5
.B DLT_IEEE802
IEEE 802.5 Token Ring
.TP 5
.B DLT_ARCNET
ARCNET
.TP 5
.B DLT_SLIP
SLIP; the link layer header contains, in order:
.RS 10
.LP
a 1-byte flag, which is 0 for packets received by the machine and 1 for
packets sent by the machine;
.LP
a 1-byte field, the upper 4 bits of which indicate the type of packet,
as per RFC 1144:
.RS 5
.TP 5
0x40
an unmodified IP datagram (TYPE_IP);
.TP 5
0x70
an uncompressed-TCP IP datagram (UNCOMPRESSED_TCP), with that byte being
the first byte of the raw IP header on the wire, containing the
connection number in the protocol field;
.TP 5
0x80
a compressed-TCP IP datagram (COMPRESSED_TCP), with that byte being the
first byte of the compressed TCP/IP datagram header;
.RE
.LP
for UNCOMPRESSED_TCP, the rest of the modified IP header, and for
COMPRESSED_TCP, the compressed TCP/IP datagram header;
.RE
.RS 5
.LP
for a total of 16 bytes; the uncompressed IP datagram follows the header.
.RE
.TP 5
.B DLT_PPP
PPP; if the first 2 bytes are 0xff and 0x03, it's PPP in HDLC-like
framing, with the PPP header following those two bytes, otherwise it's
PPP without framing, and the packet begins with the PPP header.
.TP 5
.B DLT_FDDI
FDDI
.TP 5
.B DLT_ATM_RFC1483
RFC 1483 LLC/SNAP-encapsulated ATM; the packet begins with an IEEE 802.2
LLC header.
.TP 5
.B DLT_RAW
raw IP; the packet begins with an IP header.
.TP 5
.B DLT_PPP_SERIAL
PPP in HDLC-like framing, as per RFC 1662, or Cisco PPP with HDLC
framing, as per section 4.3.1 of RFC 1547; the first byte will be 0xFF
for PPP in HDLC-like framing, and will be 0x0F or 0x8F for Cisco PPP
with HDLC framing.
.TP 5
.B DLT_PPP_ETHER
PPPoE; the packet begins with a PPPoE header, as per RFC 2516.
.TP 5
.B DLT_C_HDLC
Cisco PPP with HDLC framing, as per section 4.3.1 of RFC 1547.
.TP 5
.B DLT_IEEE802_11
IEEE 802.11 wireless LAN
.TP 5
.B DLT_FRELAY
Frame Relay
.TP 5
.B DLT_LOOP
OpenBSD loopback encapsulation; the link layer header is a 4-byte field, in
.I network
byte order, containing a PF_ value from OpenBSD's
.B socket.h
for the network-layer protocol of the packet.
.IP
Note that, if a ``savefile'' is being read, those PF_ values are
.I not
necessarily those of the machine reading the capture file.
.TP 5
.B DLT_LINUX_SLL
Linux "cooked" capture encapsulation; the link layer header contains, in
order:
.RS 10
.LP
a 2-byte "packet type", in network byte order, which is one of:
.RS 5
.TP 5
0
packet was sent to us by somebody else
.TP 5
1
packet was broadcast by somebody else
.TP 5
2
packet was multicast, but not broadcast, by somebody else
.TP 5
3
packet was sent by somebody else to somebody else
.TP 5
4
packet was sent by us
.RE
.LP
a 2-byte field, in network byte order, containing a Linux ARPHRD_ value
for the link layer device type;
.LP
a 2-byte field, in network byte order, containing the length of the
link layer address of the sender of the packet (which could be 0);
.LP
an 8-byte field containing that number of bytes of the link layer header
(if there are more than 8 bytes, only the first 8 are present);
.LP
a 2-byte field containing an Ethernet protocol type, in network byte
order, or containing 1 for Novell 802.3 frames without an 802.2 LLC
header or 4 for frames beginning with an 802.2 LLC header.
.RE
.TP 5
.B DLT_LTALK
Apple LocalTalk; the packet begins with an AppleTalk LLAP header.
.TP 5
.B DLT_PFLOG
OpenBSD pflog; the link layer header contains, in order:
.RS 10
.LP
a 1-byte header length, in host byte order;
.LP
a 4-byte PF_ value, in host byte order;
.LP
a 2-byte action code, in network byte order, which is one of:
.RS 5
.TP 5
0
passed
.TP 5
1
dropped
.TP 5
2
scrubbed
.RE
.LP
a 2-byte reason code, in network byte order, which is one of:
.RS 5
.TP 5
0
match
.TP 5
1
bad offset
.TP 5
2
fragment
.TP 5
3
short
.TP 5
4
normalize
.TP 5
5
memory
.RE
.LP
a 16-character interface name;
.LP
a 16-character ruleset name (only meaningful if subrule is set);
.LP
a 4-byte rule number, in network byte order;
.LP
a 4-byte subrule number, in network byte order;
.LP
a 1-byte direction, in network byte order, which is one of:
.RS 5
.TP 5
0
incoming or outgoing
.TP 5
1
incoming
.TP 5
2
outgoing
.RE
.RE
.TP 5
.B DLT_PRISM_HEADER
Prism monitor mode information followed by an 802.11 header.
.TP 5
.B DLT_IP_OVER_FC
RFC 2625 IP-over-Fibre Channel, with the link-layer header being the
Network_Header as described in that RFC.
.TP 5
.B DLT_SUNATM
SunATM devices; the link layer header contains, in order:
.RS 10
.LP
a 1-byte flag field, containing a direction flag in the uppermost bit,
which is set for packets transmitted by the machine and clear for
packets received by the machine, and a 4-byte traffic type in the
low-order 4 bits, which is one of:
.RS 5
.TP 5
0
raw traffic
.TP 5
1
LANE traffic
.TP 5
2
LLC-encapsulated traffic
.TP 5
3
MARS traffic
.TP 5
4
IFMP traffic
.TP 5
5
ILMI traffic
.TP 5
6
Q.2931 traffic
.RE
.LP
a 1-byte VPI value;
.LP
a 2-byte VCI field, in network byte order.
.RE
.TP 5
.B DLT_IEEE802_11_RADIO
link-layer information followed by an 802.11 header - see
http://www.shaftnet.org/~pizza/software/capturefrm.txt for a description
of the link-layer information.
.TP 5
.B DLT_ARCNET_LINUX
ARCNET, with no exception frames, reassembled packets rather than raw
frames, and an extra 16-bit offset field between the destination host
and type bytes.
.TP 5
.B DLT_LINUX_IRDA
Linux-IrDA packets, with a
.B DLT_LINUX_SLL
header followed by the IrLAP header.
.RE
.PP
.B pcap_list_datalinks()
is used to get a list of the supported data link types of the interface
associated with the pcap descriptor.
.B pcap_list_datalinks()
allocates an array to hold the list and sets
.IR *dlt_buf .
The caller is responsible for freeing the array.
.B \-1
is returned on failure;
otherwise, the number of data link types in the array is returned.
.PP
.B pcap_set_datalink()
is used to set the current data link type of the pcap descriptor
to the type specified by
.IR dlt .
.B \-1
is returned on failure.
.PP
.B pcap_datalink_name_to_val()
translates a data link type name, which is a
.B DLT_
name with the
.B DLT_
removed, to the corresponding data link type value.  The translation
is case-insensitive.  
.B \-1
is returned on failure.
.PP
.B pcap_datalink_val_to_name()
translates a data link type value to the corresponding data link type
name.  NULL is returned on failure.
.PP
.B pcap_datalink_val_to_description()
translates a data link type value to a short description of that data
link type.  NULL is returned on failure.
.PP
.B pcap_snapshot()
returns the snapshot length specified when
.B pcap_open_live()
was called.
.PP
.B pcap_is_swapped()
returns true if the current ``savefile'' uses a different byte order
than the current system.
.PP
.B pcap_major_version()
returns the major number of the file format of the savefile;
.B pcap_minor_version()
returns the minor number of the file format of the savefile.  The
version number is stored in the header of the savefile.
.PP
.B pcap_file()
returns the standard I/O stream of the ``savefile,'' if a ``savefile''
was opened with
.BR pcap_open_offline() ,
or NULL, if a network device was opened with
.BR pcap_open_live() .
.PP
.B pcap_stats()
returns 0 and fills in a
.B pcap_stat
struct. The values represent packet statistics from the start of the
run to the time of the call. If there is an error or the underlying
packet capture doesn't support packet statistics, \-1 is returned and
the error text can be obtained with
.B pcap_perror()
or
.BR pcap_geterr() .
.B pcap_stats()
is supported only on live captures, not on ``savefiles''; no statistics
are stored in ``savefiles'', so no statistics are available when reading
from a ``savefile''.
.PP
.B pcap_fileno()
returns the file descriptor number from which captured packets are read,
if a network device was opened with
.BR pcap_open_live() ,
or \-1, if a ``savefile'' was opened with
.BR pcap_open_offline() .
.PP
.B pcap_get_selectable_fd()
returns, on UNIX, a file descriptor number for a file descriptor on
which one can
do a
.B select()
or
.B poll()
to wait for it to be possible to read packets without blocking, if such
a descriptor exists, or \-1, if no such descriptor exists.  Some network
devices opened with
.B pcap_open_live()
do not support
.B select()
or
.B poll()
(for example, regular network devices on FreeBSD 4.3 and 4.4, and Endace
DAG devices), so \-1 is returned for those devices.
.PP
Note that on most versions of most BSDs (including Mac OS X)
.B select()
and
.B poll()
do not work correctly on BPF devices;
.B pcap_get_selectable_fd()
will return a file descriptor on most of those versions (the exceptions
being FreeBSD 4.3 and 4.4), a simple
.B select()
or
.B poll()
will not return even after a timeout specified in
.B pcap_open_live()
expires.  To work around this, an application that uses
.B select()
or
.B poll()
to wait for packets to arrive must put the
.B pcap_t
in non-blocking mode, and must arrange that the
.B select()
or
.B poll()
have a timeout less than or equal to the timeout specified in
.BR pcap_open_live() ,
and must try to read packets after that timeout expires, regardless of
whether
.B select()
or
.B poll()
indicated that the file descriptor for the
.B pcap_t
is ready to be read or not.  (That workaround will not work in FreeBSD
4.3 and later; however, in FreeBSD 4.6 and later,
.B select()
and
.B poll()
work correctly on BPF devices, so the workaround isn't necessary,
although it does no harm.)
.PP
.B pcap_get_selectable_fd()
is not available on Windows.
.PP
.B pcap_perror()
prints the text of the last pcap library error on
.BR stderr ,
prefixed by
.IR prefix .
.PP
.B pcap_geterr()
returns the error text pertaining to the last pcap library error.
.BR NOTE :
the pointer it returns will no longer point to a valid error message
string after the
.B pcap_t
passed to it is closed; you must use or copy the string before closing
the
.BR pcap_t .
.PP
.B pcap_strerror()
is provided in case
.BR strerror (1)
isn't available.
.PP
.B pcap_lib_version()
returns a pointer to a string giving information about the version of
the libpcap library being used; note that it contains more information
than just a version number.
.PP
.B pcap_close()
closes the files associated with
.I p
and deallocates resources.
.PP
.B pcap_dump_file()
returns the standard I/O stream of the ``savefile'' opened by
.BR pcap_dump_open().
.PP
.B pcap_dump_flush()
flushes the output buffer to the ``savefile,'' so that any packets
written with
.B pcap_dump()
but not yet written to the ``savefile'' will be written.
.B \-1
is returned on error, 0 on success.
.PP
.B pcap_dump_ftell()
returns the current file position for the ``savefile'', representing the
number of bytes written by
.B pcap_dump_open()
and
.BR pcap_dump() .
.B \-1
is returned on error.
.PP
.B pcap_dump_close()
closes the ``savefile.''
.PP
.SH SEE ALSO
tcpdump(1), tcpslice(1)
.SH AUTHORS
The original authors are:
.LP
Van Jacobson,
Craig Leres and
Steven McCanne, all of the
Lawrence Berkeley National Laboratory, University of California, Berkeley, CA.
.LP
The current version is available from "The Tcpdump Group"'s Web site at
.LP
.RS
.I http://www.tcpdump.org/
.RE
.SH BUGS
Please send problems, bugs, questions, desirable enhancements, etc. to:
.LP
.RS
<EMAIL>
.RE
.LP
Please send source code contributions, etc. to:
.LP
.RS
<EMAIL>
.RE
