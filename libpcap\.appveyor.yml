version: '{build}'

clone_depth: 5

branches:
  except:
    - coverity_scan

matrix:
  fast_finish: true

install:
  - choco install winflexbison
  - win_flex --version
  - win_bison --version
  - appveyor DownloadFile https://github.com/the-tcpdump-group/tcpdump-htdocs/raw/master/depends/WpdPack_4_1_2.zip
  - 7z x .\WpdPack_4_1_2.zip -oc:\projects\libpcap\Win32
  - appveyor DownloadFile https://github.com/the-tcpdump-group/tcpdump-htdocs/raw/master/depends/npcap-sdk-1.15.zip
  - 7z x .\npcap-sdk-1.15.zip -oc:\projects\libpcap\Win32\npcap-sdk

environment:
  #
  # The OpenSSL library on the current AppVeyor Visual Studio 2019
  # images has a weird opensslv.h that claims its 1.0.2, even though
  # it's 3.0.  This causes... problems.
  #
  # For now, we disable the remote capture build there.
  #
  matrix:
      # VS 2017, WinPcap, 32-bit and 64-bit x86, with remote
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
      GENERATOR: "Visual Studio 15 2017"
      PLATFORM: Win32
      SDK: WpdPack
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
      GENERATOR: "Visual Studio 15 2017"
      PLATFORM: x64
      SDK: WpdPack
      # VS 2017, Npcap, 32-bit and 64-bit x86, with remote
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
      GENERATOR: "Visual Studio 15 2017"
      PLATFORM: Win32
      SDK: npcap-sdk
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
      GENERATOR: "Visual Studio 15 2017"
      PLATFORM: x64
      SDK: npcap-sdk
      # VS 2019, WinPcap, 32-bit and 64-bit x86, without remote
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      GENERATOR: "Visual Studio 16 2019"
      PLATFORM: Win32
      SDK: WpdPack
      REMOTE: -DENABLE_REMOTE=NO
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      GENERATOR: "Visual Studio 16 2019"
      PLATFORM: x64
      SDK: WpdPack
      REMOTE: -DENABLE_REMOTE=NO
      # VS 2019, Winpcap, 32-bit and 64-bit x86, with remote
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      GENERATOR: "Visual Studio 16 2019"
      PLATFORM: Win32
      SDK: WpdPack
      OPENSSL_ROOT_DIR: -DOPENSSL_ROOT_DIR=C:\OpenSSL-v33-Win32\bin
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      GENERATOR: "Visual Studio 16 2019"
      PLATFORM: x64
      SDK: WpdPack
      OPENSSL_ROOT_DIR: -DOPENSSL_ROOT_DIR=C:\OpenSSL-v33-Win64\bin
      # VS 2019, Npcap, 32-bit and 64-bit x86, without remote
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      GENERATOR: "Visual Studio 16 2019"
      PLATFORM: Win32
      SDK: npcap-sdk
      REMOTE: -DENABLE_REMOTE=NO
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      GENERATOR: "Visual Studio 16 2019"
      PLATFORM: x64
      SDK: npcap-sdk
      REMOTE: -DENABLE_REMOTE=NO
      # VS 2019, Npcap, 32-bit and 64-bit x86, with remote
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      GENERATOR: "Visual Studio 16 2019"
      PLATFORM: Win32
      SDK: npcap-sdk
      OPENSSL_ROOT_DIR: -DOPENSSL_ROOT_DIR=C:\OpenSSL-v33-Win32\bin
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      GENERATOR: "Visual Studio 16 2019"
      PLATFORM: x64
      SDK: npcap-sdk
      OPENSSL_ROOT_DIR: -DOPENSSL_ROOT_DIR=C:\OpenSSL-v33-Win64\bin
      # VS 2019, Npcap, 64-bit ARM, without remote
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      GENERATOR: "Visual Studio 16 2019"
      PLATFORM: ARM64
      SDK: npcap-sdk
      REMOTE: -DENABLE_REMOTE=NO
      # VS 2019, Npcap, 64-bit ARM, with remote
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      GENERATOR: "Visual Studio 16 2019"
      PLATFORM: ARM64
      SDK: npcap-sdk
      OPENSSL_ROOT_DIR: -DOPENSSL_ROOT_DIR=C:\OpenSSL-v33-Win64\bin
      # VS 2022, WinPcap, 32-bit and 64-bit x86, without remote
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2022
      GENERATOR: "Visual Studio 17 2022"
      PLATFORM: Win32
      SDK: WpdPack
      REMOTE: -DENABLE_REMOTE=NO
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2022
      GENERATOR: "Visual Studio 17 2022"
      PLATFORM: x64
      SDK: WpdPack
      REMOTE: -DENABLE_REMOTE=NO
      # VS 2022, WinPcap, 32-bit and 64-bit x86, with remote
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2022
      GENERATOR: "Visual Studio 17 2022"
      PLATFORM: Win32
      SDK: WpdPack
      OPENSSL_ROOT_DIR: -DOPENSSL_ROOT_DIR=C:\OpenSSL-v33-Win32\bin
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2022
      GENERATOR: "Visual Studio 17 2022"
      PLATFORM: x64
      SDK: WpdPack
      OPENSSL_ROOT_DIR: -DOPENSSL_ROOT_DIR=C:\OpenSSL-v33-Win64\bin
      # VS 2022, Npcap, 32-bit and 64-bit x86, without remote
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2022
      GENERATOR: "Visual Studio 17 2022"
      PLATFORM: Win32
      SDK: npcap-sdk
      REMOTE: -DENABLE_REMOTE=NO
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2022
      GENERATOR: "Visual Studio 17 2022"
      PLATFORM: x64
      SDK: npcap-sdk
      REMOTE: -DENABLE_REMOTE=NO
      # VS 2022, Npcap, 32-bit and 64-bit x86, with remote
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2022
      GENERATOR: "Visual Studio 17 2022"
      PLATFORM: Win32
      SDK: npcap-sdk
      OPENSSL_ROOT_DIR: -DOPENSSL_ROOT_DIR=C:\OpenSSL-v33-Win32\bin
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2022
      GENERATOR: "Visual Studio 17 2022"
      PLATFORM: x64
      SDK: npcap-sdk
      OPENSSL_ROOT_DIR: -DOPENSSL_ROOT_DIR=C:\OpenSSL-v33-Win64\bin
      # VS 2022, Npcap, 64-bit ARM, without remote
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2022
      GENERATOR: "Visual Studio 17 2022"
      PLATFORM: ARM64
      SDK: npcap-sdk
      REMOTE: -DENABLE_REMOTE=NO
      # VS 2022, Npcap, 64-bit ARM, with remote
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2022
      GENERATOR: "Visual Studio 17 2022"
      PLATFORM: ARM64
      SDK: npcap-sdk
      OPENSSL_ROOT_DIR: -DOPENSSL_ROOT_DIR=C:\OpenSSL-v33-Win64\bin

build_script:
  #
  # Appveyor defaults to cmd.exe, so use cmd.exe syntax.
  #
  - git show --oneline -s
  - type NUL >.devel
  - md build
  - cd build
  - cmake --version
  - cmake %REMOTE% %OPENSSL_ROOT_DIR% -DPacket_ROOT=c:\projects\libpcap\Win32\%SDK% -G"%GENERATOR%" -DPacket_ROOT=c:\projects\libpcap\Win32\%SDK% -DPacket_ROOT=c:\projects\libpcap\Win32\%SDK% -G"%GENERATOR%" -G"%GENERATOR%" -A %PLATFORM% ..
  - msbuild /m /nologo /p:Configuration=Release pcap.sln
