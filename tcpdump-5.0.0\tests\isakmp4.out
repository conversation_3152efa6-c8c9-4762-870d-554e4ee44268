    1  1970-01-01 00:00:00.000000 ARP, Request who-has ********** tell ***********, length 28
    2  1970-01-01 00:00:00.000000 ARP, Reply ********** is-at 10:00:00:64:64:23, length 28
    3  1970-01-01 00:00:00.000000 IP ***********.500 > **********.500: isakmp: phase 1 I ident
    4  1970-01-01 00:00:00.000000 IP **********.500 > ***********.500: isakmp: phase 1 R ident
    5  1970-01-01 00:00:00.000000 IP ***********.500 > **********.500: isakmp: phase 1 I ident
    6  1970-01-01 00:00:00.000000 IP **********.500 > ***********.500: isakmp: phase 1 R ident
    7  1970-01-01 00:00:00.000000 IP ***********.4500 > **********.4500: NONESP-encap: isakmp: phase 1 I ident[E]
    8  1970-01-01 00:00:00.000000 IP **********.4500 > ***********.4500: NONESP-encap: isakmp: phase 1 R ident[E]
    9  1970-01-01 00:00:00.000000 IP ***********.4500 > **********.4500: NONESP-encap: isakmp: phase 2/others I oakley-quick[E]
   10  1970-01-01 00:00:00.000000 IP **********.4500 > ***********.4500: NONESP-encap: isakmp: phase 2/others R oakley-quick[E]
   11  1970-01-01 00:00:00.000000 IP ***********.4500 > **********.4500: NONESP-encap: isakmp: phase 2/others I oakley-quick[E]
   12  1970-01-01 00:00:00.000000 IP ***********.4500 > **********.4500: UDP-encap: ESP(spi=0xf4dc0ae5,seq=0x1), length 132
   13  1970-01-01 00:00:00.000000 ARP, Request who-has *********** tell **********, length 28
   14  1970-01-01 00:00:00.000000 ARP, Reply *********** is-at 10:00:00:de:ad:ba, length 28
   15  1970-01-01 00:00:00.000000 IP **********.4500 > ***********.4500: NONESP-encap: isakmp: phase 2/others R oakley-quick[E]
   16  1970-01-01 00:00:00.000000 IP ***********.4500 > **********.4500: NONESP-encap: isakmp: phase 2/others I oakley-quick[E]
   17  1970-01-01 00:00:00.000000 IP ***********.4500 > **********.4500: UDP-encap: ESP(spi=0xf4dc0ae5,seq=0x2), length 132
   18  1970-01-01 00:00:00.000000 IP ***********.4500 > **********.4500: isakmp-nat-keep-alive
   19  1970-01-01 00:00:00.000000 IP ***********.4500 > **********.4500: UDP-encap: ESP(spi=0xf4dc0ae5,seq=0x3), length 132
   20  1970-01-01 00:00:00.000000 IP **********.4500 > ***********.4500: NONESP-encap: isakmp: phase 2/others R oakley-quick[E]
   21  1970-01-01 00:00:00.000000 IP ***********.4500 > **********.4500: NONESP-encap: isakmp: phase 2/others I oakley-quick[E]
   22  1970-01-01 00:00:00.000000 IP ***********.4500 > **********.4500: UDP-encap: ESP(spi=0xf4dc0ae5,seq=0x4), length 132
   23  1970-01-01 00:00:00.000000 IP ***********.4500 > **********.4500: isakmp-nat-keep-alive
   24  1970-01-01 00:00:00.000000 IP ***********.4500 > **********.4500: UDP-encap: ESP(spi=0xf4dc0ae5,seq=0x5), length 132
   25  1970-01-01 00:00:00.000000 IP ***********.4500 > **********.4500: UDP-encap: ESP(spi=0xf4dc0ae5,seq=0x6), length 132
   26  1970-01-01 00:00:00.000000 ARP, Request who-has ********** tell ***********, length 28
   27  1970-01-01 00:00:00.000000 ARP, Reply ********** is-at 10:00:00:64:64:23, length 28
   28  1970-01-01 00:00:00.000000 IP ***********.4500 > **********.4500: isakmp-nat-keep-alive
   29  1970-01-01 00:00:00.000000 IP ***********.4500 > **********.4500: UDP-encap: ESP(spi=0xf4dc0ae5,seq=0x7), length 132
   30  1970-01-01 00:00:00.000000 IP **********.4500 > ***********.4500: NONESP-encap: isakmp: phase 2/others R oakley-quick[E]
   31  1970-01-01 00:00:00.000000 IP ***********.4500 > **********.4500: UDP-encap: ESP(spi=0xf4dc0ae5,seq=0x8), length 132
   32  1970-01-01 00:00:00.000000 ARP, Request who-has *********** tell **********, length 28
   33  1970-01-01 00:00:00.000000 ARP, Reply *********** is-at 10:00:00:de:ad:ba, length 28
   34  1970-01-01 00:00:00.000000 IP ***********.4500 > **********.4500: isakmp-nat-keep-alive
   35  1970-01-01 00:00:00.000000 IP **********.4500 > ***********.4500: NONESP-encap: isakmp: phase 2/others R inf[E]
