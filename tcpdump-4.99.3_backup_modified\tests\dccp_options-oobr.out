    1  16:59:25.816632 IP (tos 0x0, ttl 64, id 65312, offset 0, flags [DF], proto DCCP (33), length 52)
    ***************.39420 > **************.5001: DCCP (CCVal 0, CsCov 0, cksum 0xaaf3 (incorrect -> 0x8bf3)) DCCP-Request (service=4105078398) seq 8 <nop, nop, nop, nop, change_l ack_ratio 2, change_r ccid 2, change_l ccid 2>
    2  14:27:00.817006 IP (tos 0x0, ttl 64, id 0, offset 0, flags [DF], proto DCCP (33), length 68)
    **************.5001 > ***************.39420: DCCP (CCVal 0, CsCov 0) DCCP-Response (service=0) (ack=38464816766) seq 1960341146 <nop, nop, change_l ack_ratio 2,  [|dccp]>
    3  14:27:00.817125 IP (tos 0x0, ttl 64, id 65313, offset 0, flags [DF], proto DCCP (33), length 56)
    ***************.39420 > **************.5001: DCCP (CCVal 0, CsCov 0, cksum 0xf53a (incorrect -> 0xf551)) DCCP-Ack (ack=1960341146) seq 38464816767 <nop, confirm_r ack_ratio 2, ack_vector0 0xe9, timestamp_echo [optlen != 6 or 8 or 10]>
    4  14:27:00.829614 IP (tos 0x0, ttl 64, id 65314, offset 0, flags [DF], proto DCCP (33), length 152)
    ***************.46076 > **************.48009: DCCP (CCVal 0, CsCov 6) DCCP-DataAck (ack=1960341146) seq 38464816768 <nop, nop, ack_vector0 0x00, elapsed_time 1249, ndp_count 1>
    5  14:27:00.830145 IP (tos 0x0, ttl 64, id 3176, offset 0, flags [DF], proto DCCP (33), length 52)
    **************.5001 > ***************.39420: DCCP (CCVal 0, CsCov 0, cksum 0xfc63 (correct)) DCCP-Ack (ack=38464816768) seq 1960341147 <nop, ack_vector0 0x01, elapsed_time 1>
    6  14:27:00.831060 IP (tos 0x0, ttl 64, id 65315, offset 0, flags [DF], proto DCCP (33), length 148)
    ***************.39420 > **************.5001: DCCP (CCVal 0, CsCov 6) DCCP-DataAck (ack=1960341147) seq 38464816769 <nop, ack_vector0 0x00, elapsed_time 84>
    7  14:27:00.831421 IP (tos 0x0, ttl 64, id 3177, offset 0, flags [DF], proto DCCP (33), length 52)
    **************.5001 > ***************.39420: DCCP (CCVal 0, CsCov 0, cksum 0x0165 (correct)) DCCP-Ack (ack=38464816769) seq 1960341148 <nop, nop, ack_vector0 0x00, ndp_count 1>
    8  14:27:00.832055 00:07:00:42:00:00 > 00:14:22:59:55:51 Null Information, send seq 0, rcv seq 0, Flags [Command], length 66
	0x0000:  0000 0000 1422 5955 5100 07e9 bd5d 1f08  ....."YUQ....]..
	0x0010:  0045 0000 34ff 2040 0040 2181 8b8b 85d1  .E..4..@.@!.....
	0x0020:  b08b 85d1 4199 fc13 8908 00aa f320 0000  ....A...........
	0x0030:  08f4 ae86 7e00 0000                      ....~...
