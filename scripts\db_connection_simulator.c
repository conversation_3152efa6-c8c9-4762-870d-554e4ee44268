#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <mysql/mysql.h>
#include <time.h>
#include <signal.h>
#include <sys/time.h>
#include <errno.h>

// 全局配置
typedef struct {
    char host[256];
    char user[64];
    char password[64];
    char database[64];
    int port;
    int max_connections;
    int duration_seconds;
    int operation_interval_ms;
    int verbose;
} config_t;

// 连接统计
typedef struct {
    int active_connections;
    int total_operations;
    int failed_operations;
    int connection_errors;
    pthread_mutex_t mutex;
} stats_t;

// 线程参数
typedef struct {
    int thread_id;
    config_t *config;
    stats_t *stats;
    int should_stop;
} thread_param_t;

static volatile int g_should_stop = 0;
static stats_t g_stats = {0, 0, 0, 0, PTHREAD_MUTEX_INITIALIZER};

// 信号处理函数
void signal_handler(int sig) {
    printf("\n收到信号 %d，正在优雅关闭...\n", sig);
    g_should_stop = 1;
}

// 获取当前时间戳字符串
void get_timestamp(char *buffer, size_t size) {
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);
    strftime(buffer, size, "%Y-%m-%d %H:%M:%S", tm_info);
}

// 模拟业务操作
int perform_business_operations(MYSQL *conn, int thread_id) {
    char query[512];
    MYSQL_RES *result;
    int operations = 0;
    
    // 操作1: 查询系统状态
    if (mysql_query(conn, "SELECT CONNECTION_ID(), NOW(), VERSION()")) {
        return -1;
    }
    result = mysql_store_result(conn);
    if (result) {
        mysql_free_result(result);
        operations++;
    }
    
    // 操作2: 查询进程列表（模拟监控查询）
    if (mysql_query(conn, "SHOW PROCESSLIST")) {
        return -1;
    }
    result = mysql_store_result(conn);
    if (result) {
        mysql_free_result(result);
        operations++;
    }
    
    // 操作3: 查询数据库状态
    if (mysql_query(conn, "SHOW STATUS LIKE 'Threads_connected'")) {
        return -1;
    }
    result = mysql_store_result(conn);
    if (result) {
        mysql_free_result(result);
        operations++;
    }
    
    // 操作4: 模拟应用查询（创建临时表进行操作）
    snprintf(query, sizeof(query), 
        "CREATE TEMPORARY TABLE temp_test_%d (id INT, data VARCHAR(100), created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP)",
        thread_id);
    if (mysql_query(conn, query) == 0) {
        operations++;
        
        // 插入测试数据
        snprintf(query, sizeof(query),
            "INSERT INTO temp_test_%d (id, data) VALUES (%d, 'test_data_%d')",
            thread_id, rand() % 1000, thread_id);
        if (mysql_query(conn, query) == 0) {
            operations++;
        }
        
        // 查询测试数据
        snprintf(query, sizeof(query),
            "SELECT COUNT(*) FROM temp_test_%d", thread_id);
        if (mysql_query(conn, query) == 0) {
            result = mysql_store_result(conn);
            if (result) {
                mysql_free_result(result);
                operations++;
            }
        }
    }
    
    return operations;
}

// 工作线程函数
void* worker_thread(void* arg) {
    thread_param_t *param = (thread_param_t*)arg;
    MYSQL *conn = NULL;
    char timestamp[64];
    int connection_id = 0;
    int local_operations = 0;
    int local_failures = 0;
    
    // 初始化MySQL连接
    conn = mysql_init(NULL);
    if (!conn) {
        pthread_mutex_lock(&param->stats->mutex);
        param->stats->connection_errors++;
        pthread_mutex_unlock(&param->stats->mutex);
        return NULL;
    }
    
    // 设置连接选项
    unsigned int timeout = 10;
    mysql_options(conn, MYSQL_OPT_CONNECT_TIMEOUT, &timeout);
    mysql_options(conn, MYSQL_OPT_READ_TIMEOUT, &timeout);
    mysql_options(conn, MYSQL_OPT_WRITE_TIMEOUT, &timeout);
    
    // 连接到数据库
    if (!mysql_real_connect(conn, param->config->host, param->config->user,
                           param->config->password, param->config->database,
                           param->config->port, NULL, 0)) {
        get_timestamp(timestamp, sizeof(timestamp));
        printf("[%s] 线程%d: 连接失败 - %s\n", 
               timestamp, param->thread_id, mysql_error(conn));
        
        pthread_mutex_lock(&param->stats->mutex);
        param->stats->connection_errors++;
        pthread_mutex_unlock(&param->stats->mutex);
        
        mysql_close(conn);
        return NULL;
    }
    
    // 获取连接ID
    MYSQL_RES *result = NULL;
    if (mysql_query(conn, "SELECT CONNECTION_ID()") == 0) {
        result = mysql_store_result(conn);
        if (result) {
            MYSQL_ROW row = mysql_fetch_row(result);
            if (row) {
                connection_id = atoi(row[0]);
            }
            mysql_free_result(result);
        }
    }
    
    // 更新连接统计
    pthread_mutex_lock(&param->stats->mutex);
    param->stats->active_connections++;
    pthread_mutex_unlock(&param->stats->mutex);
    
    get_timestamp(timestamp, sizeof(timestamp));
    printf("[%s] 线程%d: 连接成功 (连接ID: %d)\n", 
           timestamp, param->thread_id, connection_id);
    
    // 主工作循环
    while (!g_should_stop && !param->should_stop) {
        // 执行业务操作
        int ops = perform_business_operations(conn, param->thread_id);
        if (ops > 0) {
            local_operations += ops;

            // 更新全局统计（实时更新）
            pthread_mutex_lock(&param->stats->mutex);
            param->stats->total_operations += ops;
            pthread_mutex_unlock(&param->stats->mutex);

            if (param->config->verbose && local_operations % 10 == 0) {
                get_timestamp(timestamp, sizeof(timestamp));
                printf("[%s] 线程%d: 累计完成 %d 个操作\n",
                       timestamp, param->thread_id, local_operations);
            }
        } else {
            local_failures++;
            get_timestamp(timestamp, sizeof(timestamp));
            printf("[%s] 线程%d: 操作失败 - %s\n", 
                   timestamp, param->thread_id, mysql_error(conn));
        }
        
        // 等待指定间隔
        usleep(param->config->operation_interval_ms * 1000);
    }
    
    // 更新统计信息
    pthread_mutex_lock(&param->stats->mutex);
    param->stats->active_connections--;
    param->stats->failed_operations += local_failures;
    pthread_mutex_unlock(&param->stats->mutex);
    
    get_timestamp(timestamp, sizeof(timestamp));
    printf("[%s] 线程%d: 断开连接 (连接ID: %d, 操作: %d, 失败: %d)\n", 
           timestamp, param->thread_id, connection_id, local_operations, local_failures);
    
    mysql_close(conn);
    return NULL;
}

// 统计监控线程
void* monitor_thread(void* arg) {
    config_t *config = (config_t*)arg;
    char timestamp[64];
    int last_operations = 0;
    
    while (!g_should_stop) {
        sleep(10); // 每10秒打印一次统计
        
        pthread_mutex_lock(&g_stats.mutex);
        int current_connections = g_stats.active_connections;
        int total_ops = g_stats.total_operations;
        int failed_ops = g_stats.failed_operations;
        int conn_errors = g_stats.connection_errors;
        pthread_mutex_unlock(&g_stats.mutex);
        
        get_timestamp(timestamp, sizeof(timestamp));
        printf("\n[%s] === 连接统计 ===\n", timestamp);
        printf("活跃连接数: %d/%d\n", current_connections, config->max_connections);
        printf("总操作数: %d (新增: %d)\n", total_ops, total_ops - last_operations);
        printf("失败操作数: %d\n", failed_ops);
        printf("连接错误数: %d\n", conn_errors);
        printf("操作速率: %.2f ops/sec\n", (total_ops - last_operations) / 10.0);
        printf("========================\n\n");
        
        last_operations = total_ops;
    }

    return NULL;
}

// 打印使用说明
void print_usage(const char *program_name) {
    printf("数据库连接模拟器 - 模拟真实生产环境的数据库连接\n\n");
    printf("用法: %s [选项]\n\n", program_name);
    printf("选项:\n");
    printf("  -h HOST        数据库主机地址 (默认: localhost)\n");
    printf("  -P PORT        数据库端口 (默认: 3306)\n");
    printf("  -u USER        数据库用户名 (默认: root)\n");
    printf("  -p PASSWORD    数据库密码 (默认: 空)\n");
    printf("  -d DATABASE    数据库名 (默认: mysql)\n");
    printf("  -c CONNECTIONS 最大并发连接数 (默认: 10)\n");
    printf("  -t DURATION    运行时长(秒) (默认: 60)\n");
    printf("  -i INTERVAL    操作间隔(毫秒) (默认: 1000)\n");
    printf("  -v             详细输出模式\n");
    printf("  --help         显示此帮助信息\n\n");
    printf("示例:\n");
    printf("  %s -h ************* -c 50 -t 300 -i 500\n", program_name);
    printf("  %s -u testuser -p testpass -d testdb -c 20 -v\n", program_name);
}

// 主函数
int main(int argc, char *argv[]) {
    config_t config = {
        .host = "localhost",
        .user = "root",
        .password = "",
        .database = "mysql",
        .port = 3306,
        .max_connections = 10,
        .duration_seconds = 60,
        .operation_interval_ms = 1000,
        .verbose = 0
    };

    // 解析命令行参数
    int opt;
    while ((opt = getopt(argc, argv, "h:P:u:p:d:c:t:i:v")) != -1) {
        switch (opt) {
            case 'h':
                strncpy(config.host, optarg, sizeof(config.host) - 1);
                break;
            case 'P':
                config.port = atoi(optarg);
                break;
            case 'u':
                strncpy(config.user, optarg, sizeof(config.user) - 1);
                break;
            case 'p':
                strncpy(config.password, optarg, sizeof(config.password) - 1);
                break;
            case 'd':
                strncpy(config.database, optarg, sizeof(config.database) - 1);
                break;
            case 'c':
                config.max_connections = atoi(optarg);
                break;
            case 't':
                config.duration_seconds = atoi(optarg);
                break;
            case 'i':
                config.operation_interval_ms = atoi(optarg);
                break;
            case 'v':
                config.verbose = 1;
                break;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }

    // 检查help参数
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "--help") == 0) {
            print_usage(argv[0]);
            return 0;
        }
    }

    // 参数验证
    if (config.max_connections <= 0 || config.max_connections > 1000) {
        printf("错误: 连接数必须在1-1000之间\n");
        return 1;
    }

    if (config.duration_seconds <= 0) {
        printf("错误: 运行时长必须大于0\n");
        return 1;
    }

    if (config.operation_interval_ms < 100) {
        printf("错误: 操作间隔不能小于100毫秒\n");
        return 1;
    }

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 初始化MySQL库
    if (mysql_library_init(0, NULL, NULL)) {
        printf("错误: 无法初始化MySQL库\n");
        return 1;
    }

    printf("=== 数据库连接模拟器启动 ===\n");
    printf("目标主机: %s:%d\n", config.host, config.port);
    printf("数据库: %s\n", config.database);
    printf("用户: %s\n", config.user);
    printf("最大连接数: %d\n", config.max_connections);
    printf("运行时长: %d 秒\n", config.duration_seconds);
    printf("操作间隔: %d 毫秒\n", config.operation_interval_ms);
    printf("详细模式: %s\n", config.verbose ? "开启" : "关闭");
    printf("==============================\n\n");

    // 创建线程数组
    pthread_t *threads = malloc(config.max_connections * sizeof(pthread_t));
    thread_param_t *params = malloc(config.max_connections * sizeof(thread_param_t));
    pthread_t monitor_tid;

    if (!threads || !params) {
        printf("错误: 内存分配失败\n");
        return 1;
    }

    // 启动监控线程
    if (pthread_create(&monitor_tid, NULL, monitor_thread, &config) != 0) {
        printf("错误: 无法创建监控线程\n");
        return 1;
    }

    // 创建工作线程
    for (int i = 0; i < config.max_connections; i++) {
        params[i].thread_id = i + 1;
        params[i].config = &config;
        params[i].stats = &g_stats;
        params[i].should_stop = 0;

        if (pthread_create(&threads[i], NULL, worker_thread, &params[i]) != 0) {
            printf("错误: 无法创建工作线程 %d\n", i + 1);
            g_should_stop = 1;
            break;
        }

        // 稍微延迟以避免连接风暴
        usleep(50000); // 50ms
    }

    // 等待指定时间或信号
    for (int i = 0; i < config.duration_seconds && !g_should_stop; i++) {
        sleep(1);
    }

    // 设置停止标志
    g_should_stop = 1;

    printf("\n正在关闭所有连接...\n");

    // 等待所有工作线程结束
    for (int i = 0; i < config.max_connections; i++) {
        pthread_join(threads[i], NULL);
    }

    // 等待监控线程结束
    pthread_join(monitor_tid, NULL);

    // 打印最终统计
    printf("\n=== 最终统计 ===\n");
    printf("总操作数: %d\n", g_stats.total_operations);
    printf("失败操作数: %d\n", g_stats.failed_operations);
    printf("连接错误数: %d\n", g_stats.connection_errors);
    printf("成功率: %.2f%%\n",
           g_stats.total_operations > 0 ?
           (double)(g_stats.total_operations - g_stats.failed_operations) / g_stats.total_operations * 100 : 0);
    printf("================\n");

    // 清理资源
    free(threads);
    free(params);
    mysql_library_end();

    return 0;
}
