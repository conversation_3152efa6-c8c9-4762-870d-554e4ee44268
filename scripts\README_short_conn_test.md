# 短连接测试程序使用说明

本目录包含三个短连接测试程序，用于测试网络安全监控系统的短连接监控功能。

## 程序列表

### 1. mysql_short_conn_test.c
- **用途**: 专门针对MySQL的短连接测试
- **特点**: 支持MySQL客户端库（可选）
- **适用场景**: 测试MySQL数据库的短连接监控

### 2. simple_short_conn_test.c  
- **用途**: 简单的短连接测试程序
- **特点**: 纯socket实现，无外部依赖
- **适用场景**: 通用短连接测试，适合任何TCP服务

### 3. remote_short_conn_test.c ⭐ **推荐**
- **用途**: 专门用于远程短连接测试
- **特点**: 支持主机名解析、源端口范围、超时控制
- **适用场景**: 从远程服务器测试目标服务器的短连接监控

## 编译方法

### 在远程服务器上编译

```bash
# 1. 复制源代码到远程服务器
scp scripts/remote_short_conn_test.c user@remote-server:/tmp/

# 2. 在远程服务器上编译
ssh user@remote-server
cd /tmp
gcc -o remote_short_conn_test remote_short_conn_test.c

# 3. 给予执行权限
chmod +x remote_short_conn_test
```

### 本地编译（如果需要）

```bash
cd scripts/
gcc -o simple_short_conn_test simple_short_conn_test.c
gcc -o remote_short_conn_test remote_short_conn_test.c
gcc -o mysql_short_conn_test mysql_short_conn_test.c  # 可能需要 -lmysqlclient
```

## 使用方法

### 远程短连接测试（推荐）

```bash
# 基本用法
./remote_short_conn_test <target_host> <target_port> <connections_per_second> <duration_seconds>

# 示例1: 向目标服务器的MySQL发起30连接/秒，持续60秒
./remote_short_conn_test ************* 3306 30 60

# 示例2: 向目标服务器的HTTP发起50连接/秒，持续120秒
./remote_short_conn_test ********* 80 50 120

# 示例3: 指定源端口范围（避免端口耗尽）
./remote_short_conn_test ************* 3306 100 30 20000-30000

# 示例4: 使用主机名
./remote_short_conn_test db.example.com 5432 25 180
```

### 简单短连接测试

```bash
# 基本用法
./simple_short_conn_test <host> <port> <connections_per_second> <duration_seconds>

# 示例
./simple_short_conn_test ************* 3306 20 60
```

## 测试场景建议

### 1. 轻量级测试
```bash
# 10连接/秒，持续30秒 = 300个短连接
./remote_short_conn_test ************* 3306 10 30
```

### 2. 中等强度测试
```bash
# 50连接/秒，持续60秒 = 3000个短连接
./remote_short_conn_test ************* 3306 50 60
```

### 3. 高强度测试
```bash
# 100连接/秒，持续30秒 = 3000个短连接
./remote_short_conn_test ************* 3306 100 30 20000-30000
```

### 4. 持续监控测试
```bash
# 20连接/秒，持续10分钟 = 12000个短连接
./remote_short_conn_test ************* 3306 20 600
```

## 输出示例

```
========================================
远程短连接测试程序
========================================
目标主机: ************* (*************)
目标端口: 3306
连接速率: 30 连接/秒
测试时长: 60 秒
预计总连接: 1800
按 Ctrl+C 可提前停止
========================================
开始向 *************:3306 发起短连接测试...

[ 45.2s] *************:3306 | 总计: 1360 | 成功: 1355 | 失败:   3 | 超时:  2 | 目标: 30/s | 实际: 30.1/s

========================================
远程短连接测试完成！
========================================
目标服务器: *************:3306
实际耗时: 60.02 秒
总连接数: 1802
成功连接: 1798
失败连接: 3
超时连接: 1
成功率: 99.8%
平均速率: 30.0 连接/秒

网络安全监控验证:
----------------------------------------
在目标服务器 ************* 上检查网络安全监控:
1. TIME_WAIT连接数应该增加约: 1798
2. 短连接比率应该显著上升
3. 高频短连接IP应该显示本机IP
4. 单IP最大TIME_WAIT数应该接近: 1798
5. 估算短连接/秒应该接近: 30
========================================
```

## 验证网络安全监控

测试完成后，在目标服务器上运行网络安全监控程序：

```bash
# 在目标服务器上运行
./send_data any notask
```

预期看到的变化：
1. **TIME_WAIT连接总数**显著增加
2. **短连接比率**上升到较高百分比
3. **高频短连接IP**显示测试机器的IP地址
4. **单IP最大TIME_WAIT数**接近测试程序的成功连接数

## 注意事项

1. **防火墙**: 确保目标端口在防火墙中开放
2. **端口耗尽**: 高频测试时建议指定源端口范围
3. **系统限制**: 注意系统的文件描述符限制
4. **网络带宽**: 高频测试可能消耗较多网络资源
5. **TIME_WAIT**: 连接关闭后会保持TIME_WAIT状态约60秒

## 故障排除

### 连接失败率高
- 检查目标服务器是否可达
- 检查目标端口是否开放
- 检查防火墙设置

### 程序编译失败
- 确保gcc已安装
- 检查系统头文件是否完整

### 权限问题
- 确保程序有执行权限：`chmod +x program_name`
- 某些端口可能需要root权限

## 清理

测试完成后，TIME_WAIT连接会在约60秒后自动清理，无需手动操作。
