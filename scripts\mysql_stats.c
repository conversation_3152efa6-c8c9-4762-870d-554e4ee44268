#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

#define MAX_LINE 1024
#define MAX_STATS 1000

struct time_stats {
    char timestamp[20];
    int connect_count;
    char client_ip[64];
};

struct ip_stats {
    char ip[64];
    int total_connections;
};

// 解析时间戳，返回秒数
int parse_timestamp(const char* timestamp) {
    int day, hour, min, sec;
    if (sscanf(timestamp, "%*6d %d:%d:%d", &hour, &min, &sec) == 3) {
        return hour * 3600 + min * 60 + sec;
    }
    return -1;
}

// 提取IP地址
void extract_ip(const char* line, char* ip) {
    char* at_pos = strstr(line, "@");
    if (at_pos) {
        at_pos++; // 跳过@
        char* space_pos = strchr(at_pos, ' ');
        if (space_pos) {
            int len = space_pos - at_pos;
            if (len < 63) {
                strncpy(ip, at_pos, len);
                ip[len] = '\0';
                return;
            }
        }
    }
    strcpy(ip, "unknown");
}

int main() {
    FILE* fp = fopen("/tmp/mysql-logs/general.log", "r");
    if (!fp) {
        printf("错误: 无法打开日志文件\n");
        return 1;
    }
    
    char line[MAX_LINE];
    struct time_stats stats[MAX_STATS];
    struct ip_stats ip_stats[100];
    int stats_count = 0;
    int ip_count = 0;
    int total_connects = 0;
    
    char current_timestamp[20] = "";
    int current_connects = 0;
    int last_timestamp_sec = -1;
    
    printf("MySQL连接统计分析 (C版本)\n");
    printf("========================\n");
    
    while (fgets(line, sizeof(line), fp)) {
        // 检查是否是Connect行
        if (strstr(line, "Connect")) {
            total_connects++;
            
            // 检查是否有时间戳
            if (line[0] >= '0' && line[0] <= '9' && line[6] == ' ') {
                // 有时间戳的Connect行
                if (strlen(current_timestamp) > 0 && current_connects > 0) {
                    // 保存前一个时间段的统计
                    if (stats_count < MAX_STATS) {
                        strcpy(stats[stats_count].timestamp, current_timestamp);
                        stats[stats_count].connect_count = current_connects;
                        extract_ip(line, stats[stats_count].client_ip);
                        stats_count++;
                    }
                }
                
                // 开始新的时间段
                sscanf(line, "%19s", current_timestamp);
                current_connects = 1;
                last_timestamp_sec = parse_timestamp(current_timestamp);
            } else {
                // 没有时间戳的Connect行，累加到当前时间段
                current_connects++;
            }
            
            // 统计IP
            char ip[64];
            extract_ip(line, ip);
            
            int found = 0;
            for (int i = 0; i < ip_count; i++) {
                if (strcmp(ip_stats[i].ip, ip) == 0) {
                    ip_stats[i].total_connections++;
                    found = 1;
                    break;
                }
            }
            if (!found && ip_count < 100) {
                strcpy(ip_stats[ip_count].ip, ip);
                ip_stats[ip_count].total_connections = 1;
                ip_count++;
            }
        }
    }
    
    // 保存最后一个时间段
    if (strlen(current_timestamp) > 0 && current_connects > 0 && stats_count < MAX_STATS) {
        strcpy(stats[stats_count].timestamp, current_timestamp);
        stats[stats_count].connect_count = current_connects;
        strcpy(stats[stats_count].client_ip, "last_segment");
        stats_count++;
    }
    
    fclose(fp);
    
    printf("总连接数: %d\n\n", total_connects);
    
    // 按连接数排序时间统计
    for (int i = 0; i < stats_count - 1; i++) {
        for (int j = i + 1; j < stats_count; j++) {
            if (stats[i].connect_count < stats[j].connect_count) {
                struct time_stats temp = stats[i];
                stats[i] = stats[j];
                stats[j] = temp;
            }
        }
    }
    
    printf("每秒连接数统计 (前20名):\n");
    for (int i = 0; i < stats_count && i < 20; i++) {
        printf("%4d连接/秒  %s\n", stats[i].connect_count, stats[i].timestamp);
    }
    
    // 按连接数排序IP统计
    for (int i = 0; i < ip_count - 1; i++) {
        for (int j = i + 1; j < ip_count; j++) {
            if (ip_stats[i].total_connections < ip_stats[j].total_connections) {
                struct ip_stats temp = ip_stats[i];
                ip_stats[i] = ip_stats[j];
                ip_stats[j] = temp;
            }
        }
    }
    
    printf("\n客户端IP连接统计:\n");
    for (int i = 0; i < ip_count; i++) {
        printf("%8d连接  %s\n", ip_stats[i].total_connections, ip_stats[i].ip);
    }
    
    return 0;
}
