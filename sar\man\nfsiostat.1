.TH NFSIOSTAT 1 "JULY 2012" Linux "Linux User's Manual" -*- nroff -*-
.SH NAME
nfsiostat \- Report input/output statistics for network filesystems (NFS).
.SH SYNOPSIS
.ie 'yes'no' \{
.B nfsiostat [ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ] [
.I interval
.B [
.I count
.B ] ]
.\}
.el \{
.B nfsiostat [ -h ] [ -k | -m ] [ -t ] [ -V ] [
.I interval
.B [
.I count
.B ] ]
.\}
.SH DESCRIPTION
The
.B nfsiostat
command displays statistics about read and write operations
on NFS filesystems.

The
.I interval
parameter specifies the amount of time in seconds between
each report. The first report contains statistics for the time since
system startup (boot). Each subsequent report contains statistics
collected during the interval since the previous report.
A report consists of an NFS header row followed by
a line of statistics for each network filesystem that is mounted.
The
.I count
parameter can be specified in conjunction with the
.I interval
parameter. If the
.I count
parameter is specified, the value of
.I count
determines the number of reports generated at
.I interval
seconds apart. If the
.I interval
parameter is specified without the
.I count
parameter, the
.B nfsiostat
command generates reports continuously.

.SH REPORT
The Network Filesystem (NFS) report provides statistics for each mounted network filesystem.
Transfer rates are shown in 1K blocks by default, unless the environment
variable POSIXLY_CORRECT is set, in which case 512-byte blocks are used.
The report shows the following fields:

.B Filesystem:
.RS
This columns shows the hostname of the NFS server followed by a colon and
by the directory name where the network filesystem is mounted.

.RE
.B rBlk_nor/s (rkB_nor/s, rMB_nor)
.RS
Indicate the number of blocks (kilobytes, megabytes) read by applications
via the read(2) system
call interface. A block has a size of 512 bytes.

.RE
.B wBlk_nor/s (wkB_nor/s, wMB_nor/s)
.RS
Indicate the number of blocks (kilobytes, megabytes) written by applications
via the write(2) system
call interface.

.RE
.B rBlk_dir/s (rkB_dir/s, rMB_dir/s)
.RS
Indicate the number of blocks (kilobytes, megabytes) read from files
opened with the O_DIRECT flag.

.RE
.B wBlk_dir/s (wkB_dir/s, wMB_dir/s)
.RS
Indicate the number of blocks (kilobytes, megabytes) written to files
opened with the O_DIRECT flag.

.RE
.B rBlk_svr/s (rkB_svr/s, rMB_svr/s)
.RS
Indicate the number of blocks (kilobytes, megabytes) read from the server
by the NFS client via an NFS READ request.

.RE
.B wBlk_svr/s (wkB_svr/s, wMB_svr/s)
.RS
Indicate the number of blocks (kilobytes, megabytes) written to the server
by the NFS client via an NFS WRITE request.

.RE
.B ops/s
.RS
Indicate the number of operations that were issued to the filesystem per second.

.RE
.B rops/s
.RS
Indicate the number of 'read' operations that were issued to the filesystem 
per second.

.RE
.B wops/s
.RS
Indicate the number of 'write' operations that were issued to the filesystem
per second.
.RE
.RE
.SH OPTIONS
.if 'yes'no' \{
.IP --debuginfo
Print debug output to stderr.
.\}
.IP -h
Make the NFS report easier to read by a human.
.IP -k
Display statistics in kilobytes per second.
.IP -m
Display statistics in megabytes per second.
.IP -t
Print the time for each report displayed. The timestamp format may depend
on the value of the S_TIME_FORMAT environment variable (see below).
.IP -V
Print version number then exit.

.SH ENVIRONMENT
The
.B nfsiostat
command takes into account the following environment variables:

.IP S_TIME_FORMAT
If this variable exists and its value is
.BR ISO
then the current locale will be ignored when printing the date in the report
header. The
.B nfsiostat
command will use the ISO 8601 format (YYYY-MM-DD) instead.
The timestamp displayed with option -t will also be compliant with ISO 8601
format.

.IP POSIXLY_CORRECT
When this variable is set, transfer rates are shown in 512-byte blocks instead
of the default 1K blocks.

.SH BUG
.I /proc
filesystem must be mounted for
.B nfsiostat
to work.

.SH FILE
.I /proc/self/mountstats
contains statistics for network filesystems.
.SH AUTHORS
Written by Ivana Varekova (varekova <at> redhat.com)

Maintained by Sebastien Godard (sysstat <at> orange.fr)
.SH SEE ALSO
.BR sar (1),
.BR pidstat (1),
.BR mpstat (1),
.BR vmstat (8),
.BR iostat (1),
.BR cifsiostat (1)

.I http://pagesperso-orange.fr/sebastien.godard/
