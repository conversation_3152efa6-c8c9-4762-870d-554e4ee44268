# translation of sysstat messages to Russian
# NLS support for the sysstat package.
# Copyright (C) 1999, 2009, 2010 Free Software Foundation, Inc.
# This file is distributed under the same license as the sysstat package.
#
# <PERSON><PERSON><PERSON><PERSON> GODARD <sysstat [at] orange.fr>, 1999.
# <PERSON> <<EMAIL>>, 2002.
# <PERSON> <<EMAIL>>, 2009, 2010, 2011, 2012, 2013, 2014, 2015, 2016, 2017.
# <PERSON> <<EMAIL>>, 2011.
msgid ""
msgstr ""
"Project-Id-Version: sysstat 11.6.0\n"
"Report-Msgid-Bugs-To: sysstat <at> orange.fr\n"
"POT-Creation-Date: 2017-08-14 08:32+0200\n"
"PO-Revision-Date: 2017-08-19 07:32+0300\n"
"Last-Translator: <PERSON> <y<PERSON>@komyakino.ru>\n"
"Language-Team: Russian <<EMAIL>>\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"X-Generator: Lokalize 2.0\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#: iostat.c:86 cifsiostat.c:70 mpstat.c:126 sar.c:96 tapestat.c:95
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ]\n"
msgstr "Использование: %s [ параметры ] [ <интервал> [ <счётчик> ] ]\n"

#: iostat.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ] [ --debuginfo ]\n"
msgstr ""
"Параметры:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | … } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <имя_группы> ] [ -p [ <устройство> [,…] | ALL ] ]\n"
"[ <устройство> […] | ALL ] [ --debuginfo ]\n"

#: iostat.c:95
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ]\n"
msgstr ""
"Параметры:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | … } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <имя_группы> ] [ -p [ <устройство> [,…] | ALL ] ]\n"
"[ <устройство> […] | ALL ]\n"

#: iostat.c:326
#, c-format
msgid "Cannot find disk data\n"
msgstr "Не удалось найти данные диска\n"

#: iostat.c:1812 sa_common.c:1590
#, c-format
msgid "Invalid type of persistent device name\n"
msgstr "Некорректный тип постоянного имени устройства\n"

#: sadc.c:89
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
msgstr "Использование: %s [ параметры ] [ <интервал> [ <счётчик> ] ] [ <вых_файл> ]\n"

#: sadc.c:92
#, c-format
msgid ""
"Options are:\n"
"[ -C <comment> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"
msgstr ""
"Параметры:\n"
"[ -C <комментарий> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"

#: sadc.c:267
#, c-format
msgid "Cannot write data to system activity file: %s\n"
msgstr "Не удалось записать данные в файл системных показателей: %s\n"

#: sadc.c:563
#, c-format
msgid "Cannot write system activity file header: %s\n"
msgstr "Не удалось записать заголовок в файл системных показателей: %s\n"

#: sadc.c:763 sadc.c:772 sadc.c:839 ioconf.c:510 rd_stats.c:69
#: sa_common.c:1204 count.c:118
#, c-format
msgid "Cannot open %s: %s\n"
msgstr "Не удалось открыть %s: %s\n"

#: sadc.c:1019
#, c-format
msgid "Cannot append data to that file (%s)\n"
msgstr "Не удалось добавить данные в этот файл (%s)\n"

#: common.c:77
#, c-format
msgid "sysstat version %s\n"
msgstr "sysstat, версия %s\n"

#: cifsiostat.c:74
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"
msgstr ""
"Параметры:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"

#: cifsiostat.c:77
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
msgstr ""
"Параметры:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#: mpstat.c:129
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -n ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -N { <node_list> | ALL } ] [ -o JSON ] [ -P { <cpu_list> | ON | ALL } ]\n"
msgstr ""
"Параметры:\n"
"[ -A ] [ -n ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -N { <список_узлов> | ALL } ] [ -o JSON ] [ -P { <список_ЦП> | ON | ALL } ]\n"

# sar.c:
#: mpstat.c:1672 sar.c:358 pidstat.c:2406
msgid "Average:"
msgstr "Среднее:"

#: sadf.c:87
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <datafile> | -[0-9]+ ]\n"
msgstr "Использование: %s [ параметры ] [ <интервал> [ <счётчик> ] ] [ <файл_данных> | -[0-9]+ ]\n"

#: sadf.c:90
#, c-format
msgid ""
"Options are:\n"
"[ -C ] [ -c | -d | -g | -j | -p | -r | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -O <opts> [,...] ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
"[ -- <sar_options> ]\n"
msgstr ""
"Параметры:\n"
"[ -C ] [ -c | -d | -g | -j | -p | -r | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -O <параметры> [,…] ] [ -P { <ЦП> [,…] | ALL } ]\n"
"[ -s [ <чч:мм[:сс]> ] ] [ -e [ <чч:мм[:сс]> ] ]\n"
"[ -- <параметры_sar> ]\n"

#: pr_stats.c:2538 pr_stats.c:2549 pr_stats.c:2656 pr_stats.c:2667
msgid "Summary:"
msgstr "Сводка:"

#: pr_stats.c:2591
msgid "Other devices not listed here"
msgstr "Другие устройства здесь не перечислены"

#: sar.c:111
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ MOUNT ] ] [ -H ] [ -h ]\n"
"[ -p ] [ -q ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --human ] [ --sadc ]\n"
"[ -I { <int_list> | SUM | ALL } ] [ -P { <cpu_list> | ALL } ]\n"
"[ -m { <keyword> [,...] | ALL } ] [ -n { <keyword> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filename> ] | -o [ <filename> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
msgstr ""
"Параметры:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ MOUNT ] ] [ -H ] [ -h ]\n"
"[ -p ] [ -q ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --human ] [ --sadc ]\n"
"[ -I { <список_целых> | SUM | ALL } ] [ -P { <список_ЦП> | ALL } ]\n"
"[ -m { <ключ_слово> [,…] | ALL } ] [ -n { <ключ_слово> [,…] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | … } ]\n"
"[ -f [ <имя_файла> ] | -o [ <имя_файла> ]| -[0-9]+ ]\n"
"[ -i <интервал> ] [ -s [ <чч:мм[:сс]> ] ] [ -e [ <чч:мм[:сс]> ] ]\n"

#: sar.c:134
#, c-format
msgid "Main options and reports:\n"
msgstr "Основные параметры и отчёты:\n"

#: sar.c:135
#, c-format
msgid "\t-B\tPaging statistics\n"
msgstr "\t-B\tСтатистика обмена страниц\n"

#: sar.c:136
#, c-format
msgid "\t-b\tI/O and transfer rate statistics\n"
msgstr "\t-b\tСтатистика ввода-вывода и скорости передачи\n"

#: sar.c:137
#, c-format
msgid "\t-d\tBlock devices statistics\n"
msgstr "\t-d\tСтатистика блочных устройств\n"

#: sar.c:138
#, c-format
msgid "\t-F [ MOUNT ]\n"
msgstr "\t-F [ MOUNT ]\n"

#: sar.c:139
#, c-format
msgid "\t\tFilesystems statistics\n"
msgstr "\t\tСтатистика файловых систем\n"

#: sar.c:140
#, c-format
msgid "\t-H\tHugepages utilization statistics\n"
msgstr "\t-H\tСтатистика использования огромных страниц\n"

#: sar.c:141
#, c-format
msgid ""
"\t-I { <int_list> | SUM | ALL }\n"
"\t\tInterrupts statistics\n"
msgstr ""
"\t-I { <список_целых> | SUM | ALL }\n"
"\t\tСтатистика прерываний\n"

#: sar.c:143
#, c-format
msgid ""
"\t-m { <keyword> [,...] | ALL }\n"
"\t\tPower management statistics\n"
"\t\tKeywords are:\n"
"\t\tCPU\tCPU instantaneous clock frequency\n"
"\t\tFAN\tFans speed\n"
"\t\tFREQ\tCPU average clock frequency\n"
"\t\tIN\tVoltage inputs\n"
"\t\tTEMP\tDevices temperature\n"
"\t\tUSB\tUSB devices plugged into the system\n"
msgstr ""
"\t-m { <ключ_слово> [,…] | ALL }\n"
"\t\tСтатистика управления питанием\n"
"\t\tКлючевые слова:\n"
"\t\tCPU\tтекущая частота работы ЦП\n"
"\t\tFAN\tскорость вращения вентиляторов\n"
"\t\tFREQ\tсредняя частота работы ЦП\n"
"\t\tIN\tВходные напряжения\n"
"\t\tTEMP\tтемпература устройств\n"
"\t\tUSB\tподключённые USB-устройства\n"

#: sar.c:152
#, c-format
msgid ""
"\t-n { <keyword> [,...] | ALL }\n"
"\t\tNetwork statistics\n"
"\t\tKeywords are:\n"
"\t\tDEV\tNetwork interfaces\n"
"\t\tEDEV\tNetwork interfaces (errors)\n"
"\t\tNFS\tNFS client\n"
"\t\tNFSD\tNFS server\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP traffic\t(v4)\n"
"\t\tEIP\tIP traffic\t(v4) (errors)\n"
"\t\tICMP\tICMP traffic\t(v4)\n"
"\t\tEICMP\tICMP traffic\t(v4) (errors)\n"
"\t\tTCP\tTCP traffic\t(v4)\n"
"\t\tETCP\tTCP traffic\t(v4) (errors)\n"
"\t\tUDP\tUDP traffic\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP traffic\t(v6)\n"
"\t\tEIP6\tIP traffic\t(v6) (errors)\n"
"\t\tICMP6\tICMP traffic\t(v6)\n"
"\t\tEICMP6\tICMP traffic\t(v6) (errors)\n"
"\t\tUDP6\tUDP traffic\t(v6)\n"
"\t\tFC\tFibre channel HBAs\n"
"\t\tSOFT\tSoftware-based network processing\n"
msgstr ""
"\t-n { <ключ_слово> [,…] | ALL }\n"
"\t\tСетевая статистика\n"
"\t\tКлючевые слова:\n"
"\t\tDEV\tСетевые интерфейсы\n"
"\t\tEDEV\tСетевые интерфейсы (ошибки)\n"
"\t\tNFS\tКлиент NFS\n"
"\t\tNFSD\tСервер NFS\n"
"\t\tSOCK\tСокеты\t(v4)\n"
"\t\tIP\tIP трафик\t(v4)\n"
"\t\tEIP\tIP трафик\t(v4) (errors)\n"
"\t\tICMP\tICMP трафик\t(v4)\n"
"\t\tEICMP\tICMP трафик\t(v4) (errors)\n"
"\t\tTCP\tTCP трафик\t(v4)\n"
"\t\tETCP\tTCP трафик\t(v4) (errors)\n"
"\t\tUDP\tUDP трафик\t(v4)\n"
"\t\tSOCK6\tСокеты\t(v6)\n"
"\t\tIP6\tIP трафик\t(v6)\n"
"\t\tEIP6\tIP трафик\t(v6) (errors)\n"
"\t\tICMP6\tICMP трафик\t(v6)\n"
"\t\tEICMP6\tICMP трафик\t(v6) (errors)\n"
"\t\tUDP6\tUDP трафик\t(v6)\n"
"\t\tFC\tАдаптер Fibre channel\n"
"\t\tSOFT\tПрограммная сетевая обработка\n"

#: sar.c:175
#, c-format
msgid "\t-q\tQueue length and load average statistics\n"
msgstr "\t-q\tСтатистика длины и средней загрузки очереди\n"

#: sar.c:176
#, c-format
msgid ""
"\t-r [ ALL ]\n"
"\t\tMemory utilization statistics\n"
msgstr ""
"\t-r [ ALL ]\n"
"\t\tСтатистика использования памяти\n"

#: sar.c:178
#, c-format
msgid "\t-S\tSwap space utilization statistics\n"
msgstr "\t-S\tСтатистика использования области подкачки\n"

#: sar.c:179
#, c-format
msgid ""
"\t-u [ ALL ]\n"
"\t\tCPU utilization statistics\n"
msgstr ""
"\t-u [ ALL ]\n"
"\t\tСтатистика использования ЦП\n"

#: sar.c:181
#, c-format
msgid "\t-v\tKernel tables statistics\n"
msgstr "\t-v\tСтатистика таблиц ядра\n"

#: sar.c:182
#, c-format
msgid "\t-W\tSwapping statistics\n"
msgstr "\t-W\tСтатистика раздела подкачки\n"

#: sar.c:183
#, c-format
msgid "\t-w\tTask creation and system switching statistics\n"
msgstr "\t-w\tСтатистика создания задач и системных переключений\n"

#: sar.c:184
#, c-format
msgid "\t-y\tTTY devices statistics\n"
msgstr "\t-y\tСтатистика устройств TTY\n"

#: sar.c:198
#, c-format
msgid "Data collector will be sought in PATH\n"
msgstr "Поиск системы сбора данных будет выполнен в PATH\n"

#: sar.c:201
#, c-format
msgid "Data collector found: %s\n"
msgstr "Обнаружена система сбора данных: %s\n"

#: sar.c:260
#, c-format
msgid "End of data collecting unexpected\n"
msgstr "Неожиданно закончились собираемые данные\n"

#: sar.c:813
#, c-format
msgid "Using a wrong data collector from a different sysstat version\n"
msgstr "Используется ошибочное средство сбора данных от другой версии sysstat\n"

#: sar.c:865
#, c-format
msgid "Inconsistent input data\n"
msgstr "Несогласованные входные данные\n"

#: sar.c:1044 pidstat.c:239
#, c-format
msgid "Requested activities not available\n"
msgstr "Запрошенный показатель недоступен\n"

#: sar.c:1347
#, c-format
msgid "-f and -o options are mutually exclusive\n"
msgstr "Параметры -f и -o являются взаимоисключающими\n"

#: sar.c:1353
#, c-format
msgid "Not reading from a system activity file (use -f option)\n"
msgstr "Не выполняется чтение из файла системных показателей (используйте параметр -f)\n"

#: sar.c:1489
#, c-format
msgid "Cannot find the data collector (%s)\n"
msgstr "Не удалось найти средство сбора данных (%s)\n"

#: sa_conv.c:69
#, c-format
msgid "Cannot convert the format of this file\n"
msgstr "Невозможно преобразовать формат этого файла\n"

#: sa_conv.c:562
#, c-format
msgid ""
"\n"
"CPU activity not found in file. Aborting...\n"
msgstr ""
"\n"
"В файле не найдены данные об активности ЦП. Прекращение работы…\n"

#: sa_conv.c:578
#, c-format
msgid ""
"\n"
"Invalid data found. Aborting...\n"
msgstr ""
"\n"
"Обнаружены некорректные данные. Прекращение работы…\n"

#: sa_conv.c:897
#, c-format
msgid "Statistics: "
msgstr "Статистика: "

#: sa_conv.c:1016
#, c-format
msgid ""
"\n"
"File successfully converted to sysstat format version %s\n"
msgstr ""
"\n"
"Файл успешно преобразован в формат sysstat версии %s\n"

#: pidstat.c:87
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ -e <program> <args> ]\n"
msgstr "Использование: %s [ параметры ] [ <интервал> [ <счётчик> ] ] [ -e <программа> <аргументы> ]\n"

#: pidstat.c:90
#, c-format
msgid ""
"Options are:\n"
"[ -d ] [ -H ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ] [ -U [ <username> ] ]\n"
"[ -u ] [ -V ] [ -v ] [ -w ] [ -C <command> ] [ -G <process_name> ] [ --human ]\n"
"[ -p { <pid> [,...] | SELF | ALL } ] [ -T { TASK | CHILD | ALL } ]\n"
msgstr ""
"Параметры:\n"
"[ -d ] [ -H ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ]\n"
"[ -U [ <имя_пользователя> ] ] [ -u ] [ -V ] [ -v ] [ -w ] [ -C <команда> ]\n"
"[ -G <имя_процесса> ] [ --human ] [ -p { <pid> [,…] | SELF | ALL } ]\n"
"[ -T { TASK | CHILD | ALL } ]\n"

#: sa_common.c:1000
#, c-format
msgid "Error while reading system activity file: %s\n"
msgstr "Ошибка чтения файла системных показателей: %s\n"

#: sa_common.c:1010
#, c-format
msgid "End of system activity file unexpected\n"
msgstr "Неожиданный конец файла системных показателей\n"

#: sa_common.c:1029
#, c-format
msgid "File created by sar/sadc from sysstat version %d.%d.%d"
msgstr "Файл создан с помощью sar/sadc из sysstat версии %d.%d.%d"

#: sa_common.c:1062
#, c-format
msgid "Invalid system activity file: %s\n"
msgstr "Недопустимый файл системных показателей: %s\n"

#: sa_common.c:1074
#, c-format
msgid "Endian format mismatch\n"
msgstr "Несовпадение формата порядка байт\n"

#: sa_common.c:1078
#, c-format
msgid "Current sysstat version cannot read the format of this file (%#x)\n"
msgstr "Текущая версия sysstat не может читать формат этого файла (%#x)\n"

#: sa_common.c:1207
#, c-format
msgid "Please check if data collecting is enabled\n"
msgstr "Проверьте, включён ли сбор данных\n"

#: sa_common.c:1400
#, c-format
msgid "Requested activities not available in file %s\n"
msgstr "Запрашиваемые показатели из файла %s недоступны\n"

#: tapestat.c:97
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"
msgstr ""
"Параметры:\n"
"[ --human ] [ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"

#: tapestat.c:263
#, c-format
msgid "No tape drives with statistics found\n"
msgstr "Ленточные устройства со статистикой не найдены\n"

#: count.c:169
#, c-format
msgid "Cannot handle so many processors!\n"
msgstr "Не удаётся обработать так много процессоров!\n"

#: sadf_misc.c:834
#, c-format
msgid "System activity data file: %s (%#x)\n"
msgstr "Недопустимый файл системных показателей: %s (%#x)\n"

#: sadf_misc.c:843
#, c-format
msgid "Genuine sa datafile: %s (%x)\n"
msgstr "Файл данных оригинальной программы sa: %s (%x)\n"

#: sadf_misc.c:844
msgid "no"
msgstr "нет"

#: sadf_misc.c:844
msgid "yes"
msgstr "да"

#: sadf_misc.c:847
#, c-format
msgid "Host: "
msgstr "Узел: "

#: sadf_misc.c:854
#, c-format
msgid "Number of CPU for last samples in file: %u\n"
msgstr "Количество ЦП в последних измерениях в файле: %u\n"

#: sadf_misc.c:860
#, c-format
msgid "File date: %s\n"
msgstr "Дата файла: %s\n"

#: sadf_misc.c:863
#, c-format
msgid "File time: "
msgstr "Время файла: "

#: sadf_misc.c:868
#, c-format
msgid "Size of a long int: %d\n"
msgstr "Размер long int: %d\n"

#: sadf_misc.c:874
#, c-format
msgid "List of activities:\n"
msgstr "Список показателей:\n"

#: sadf_misc.c:887
#, c-format
msgid "\t[Unknown activity format]"
msgstr "\t[Неизвестный формат показателя]"

#~ msgid "\t-R\tMemory statistics\n"
#~ msgstr "\t-R\tСтатистика памяти\n"

#~ msgid ""
#~ "Options are:\n"
#~ "[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
#~ msgstr ""
#~ "Параметры:\n"
#~ "[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#~ msgid "Not that many processors!\n"
#~ msgstr "Нет такого количества процессоров в системе!\n"

#~ msgid "Invalid data format\n"
#~ msgstr "Недопустимый формат данных\n"

#~ msgid "\t-m\tPower management statistics\n"
#~ msgstr "\t-B\tСтатистика по управлению питанием\n"
