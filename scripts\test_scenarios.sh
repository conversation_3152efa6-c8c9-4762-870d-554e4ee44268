#!/bin/bash

# 数据库连接模拟器测试场景脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASS=""
DB_NAME="mysql"

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查程序是否存在
check_program() {
    if [ ! -f "./db_connection_simulator" ]; then
        print_error "db_connection_simulator 程序不存在，请先编译"
        echo "运行: make"
        exit 1
    fi
}

# 检查数据库连接
check_database() {
    print_info "检查数据库连接..."
    
    if command -v mysql >/dev/null 2>&1; then
        if mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" ${DB_PASS:+-p"$DB_PASS"} -e "SELECT 1;" >/dev/null 2>&1; then
            print_success "数据库连接正常"
            return 0
        else
            print_error "无法连接到数据库 $DB_HOST:$DB_PORT"
            return 1
        fi
    else
        print_warning "mysql客户端未安装，跳过连接测试"
        return 0
    fi
}

# 显示使用说明
show_usage() {
    echo "数据库连接模拟器测试场景脚本"
    echo ""
    echo "用法: $0 [选项] [场景]"
    echo ""
    echo "选项:"
    echo "  -h HOST     数据库主机 (默认: localhost)"
    echo "  -P PORT     数据库端口 (默认: 3306)"
    echo "  -u USER     数据库用户 (默认: root)"
    echo "  -p PASS     数据库密码 (默认: 空)"
    echo "  -d DB       数据库名 (默认: mysql)"
    echo "  --help      显示此帮助"
    echo ""
    echo "测试场景:"
    echo "  1. basic     - 基础测试 (10个连接, 30秒)"
    echo "  2. medium    - 中等负载 (30个连接, 60秒)"
    echo "  3. high      - 高负载 (50个连接, 120秒)"
    echo "  4. stress    - 压力测试 (100个连接, 60秒)"
    echo "  5. long      - 长时间测试 (20个连接, 300秒)"
    echo "  6. custom    - 自定义参数"
    echo "  7. all       - 运行所有预设场景"
    echo ""
    echo "示例:"
    echo "  $0 basic"
    echo "  $0 -h ************* high"
    echo "  $0 -u testuser -p testpass custom"
}

# 运行测试场景
run_scenario() {
    local scenario=$1
    local connections=$2
    local duration=$3
    local interval=$4
    local description=$5
    
    print_info "开始测试场景: $description"
    print_info "连接数: $connections, 持续时间: ${duration}秒, 操作间隔: ${interval}ms"
    
    echo "按 Ctrl+C 可以提前停止测试"
    echo "3秒后开始..."
    sleep 3
    
    ./db_connection_simulator \
        -h "$DB_HOST" \
        -P "$DB_PORT" \
        -u "$DB_USER" \
        ${DB_PASS:+-p "$DB_PASS"} \
        -d "$DB_NAME" \
        -c "$connections" \
        -t "$duration" \
        -i "$interval" \
        -v
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        print_success "场景 '$description' 完成"
    else
        print_error "场景 '$description' 异常退出 (退出码: $exit_code)"
    fi
    
    echo ""
    echo "按回车键继续..."
    read -r
}

# 自定义场景
run_custom_scenario() {
    echo "=== 自定义测试场景 ==="
    
    read -p "连接数 (1-1000): " connections
    connections=${connections:-10}
    
    read -p "持续时间(秒): " duration
    duration=${duration:-60}
    
    read -p "操作间隔(毫秒): " interval
    interval=${interval:-1000}
    
    run_scenario "custom" "$connections" "$duration" "$interval" "自定义场景"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h)
            DB_HOST="$2"
            shift 2
            ;;
        -P)
            DB_PORT="$2"
            shift 2
            ;;
        -u)
            DB_USER="$2"
            shift 2
            ;;
        -p)
            DB_PASS="$2"
            shift 2
            ;;
        -d)
            DB_NAME="$2"
            shift 2
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            SCENARIO="$1"
            shift
            ;;
    esac
done

# 主程序
main() {
    echo "=== 数据库连接模拟器测试工具 ==="
    echo "目标数据库: $DB_USER@$DB_HOST:$DB_PORT/$DB_NAME"
    echo ""
    
    check_program
    check_database || print_warning "数据库连接检查失败，但继续执行测试"
    
    case "${SCENARIO:-menu}" in
        "basic")
            run_scenario "basic" 10 30 1000 "基础测试"
            ;;
        "medium")
            run_scenario "medium" 30 60 800 "中等负载测试"
            ;;
        "high")
            run_scenario "high" 50 120 500 "高负载测试"
            ;;
        "stress")
            run_scenario "stress" 100 60 200 "压力测试"
            ;;
        "long")
            run_scenario "long" 20 300 1500 "长时间测试"
            ;;
        "custom")
            run_custom_scenario
            ;;
        "all")
            print_info "运行所有预设测试场景"
            run_scenario "basic" 10 30 1000 "基础测试"
            run_scenario "medium" 30 60 800 "中等负载测试"
            run_scenario "high" 50 120 500 "高负载测试"
            run_scenario "stress" 100 60 200 "压力测试"
            print_success "所有测试场景完成"
            ;;
        "menu"|*)
            echo "请选择测试场景:"
            echo "1) 基础测试 (10个连接, 30秒)"
            echo "2) 中等负载 (30个连接, 60秒)"
            echo "3) 高负载 (50个连接, 120秒)"
            echo "4) 压力测试 (100个连接, 60秒)"
            echo "5) 长时间测试 (20个连接, 300秒)"
            echo "6) 自定义参数"
            echo "7) 运行所有场景"
            echo "0) 退出"
            echo ""
            read -p "请输入选择 (0-7): " choice
            
            case $choice in
                1) run_scenario "basic" 10 30 1000 "基础测试" ;;
                2) run_scenario "medium" 30 60 800 "中等负载测试" ;;
                3) run_scenario "high" 50 120 500 "高负载测试" ;;
                4) run_scenario "stress" 100 60 200 "压力测试" ;;
                5) run_scenario "long" 20 300 1500 "长时间测试" ;;
                6) run_custom_scenario ;;
                7) 
                    run_scenario "basic" 10 30 1000 "基础测试"
                    run_scenario "medium" 30 60 800 "中等负载测试"
                    run_scenario "high" 50 120 500 "高负载测试"
                    run_scenario "stress" 100 60 200 "压力测试"
                    print_success "所有测试场景完成"
                    ;;
                0) 
                    print_info "退出测试"
                    exit 0
                    ;;
                *)
                    print_error "无效选择"
                    exit 1
                    ;;
            esac
            ;;
    esac
}

# 信号处理
trap 'print_warning "测试被中断"; exit 130' INT TERM

# 运行主程序
main
