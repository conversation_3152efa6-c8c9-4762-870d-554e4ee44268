@%:@! /bin/sh
@%:@ Guess values for system-dependent variables and create Makefiles.
@%:@ Generated by GNU Autoconf 2.71 for tcpdump 5.0.0-PRE-GIT.
@%:@
@%:@ Report bugs to <https://github.com/the-tcpdump-group/tcpdump/issues>.
@%:@ 
@%:@ 
@%:@ Copyright (C) 1992-1996, 1998-2017, 2020-2021 Free Software Foundation,
@%:@ Inc.
@%:@ 
@%:@ 
@%:@ This configure script is free software; the Free Software Foundation
@%:@ gives unlimited permission to copy, distribute and modify it.
## -------------------- ##
## M4sh Initialization. ##
## -------------------- ##

# Be more Bourne compatible
DUALCASE=1; export DUALCASE # for MKS sh
as_nop=:
if test ${ZSH_VERSION+y} && (emulate sh) >/dev/null 2>&1
then :
  emulate sh
  NULLCMD=:
  # Pre-4.2 versions of Zsh do word splitting on ${1+"$@"}, which
  # is contrary to our usage.  Disable this feature.
  alias -g '${1+"$@"}'='"$@"'
  setopt NO_GLOB_SUBST
else $as_nop
  case `(set -o) 2>/dev/null` in @%:@(
  *posix*) :
    set -o posix ;; @%:@(
  *) :
     ;;
esac
fi



# Reset variables that may have inherited troublesome values from
# the environment.

# IFS needs to be set, to space, tab, and newline, in precisely that order.
# (If _AS_PATH_WALK were called with IFS unset, it would have the
# side effect of setting IFS to empty, thus disabling word splitting.)
# Quoting is to prevent editors from complaining about space-tab.
as_nl='
'
export as_nl
IFS=" ""	$as_nl"

PS1='$ '
PS2='> '
PS4='+ '

# Ensure predictable behavior from utilities with locale-dependent output.
LC_ALL=C
export LC_ALL
LANGUAGE=C
export LANGUAGE

# We cannot yet rely on "unset" to work, but we need these variables
# to be unset--not just set to an empty or harmless value--now, to
# avoid bugs in old shells (e.g. pre-3.0 UWIN ksh).  This construct
# also avoids known problems related to "unset" and subshell syntax
# in other old shells (e.g. bash 2.01 and pdksh 5.2.14).
for as_var in BASH_ENV ENV MAIL MAILPATH CDPATH
do eval test \${$as_var+y} \
  && ( (unset $as_var) || exit 1) >/dev/null 2>&1 && unset $as_var || :
done

# Ensure that fds 0, 1, and 2 are open.
if (exec 3>&0) 2>/dev/null; then :; else exec 0</dev/null; fi
if (exec 3>&1) 2>/dev/null; then :; else exec 1>/dev/null; fi
if (exec 3>&2)            ; then :; else exec 2>/dev/null; fi

# The user is always right.
if ${PATH_SEPARATOR+false} :; then
  PATH_SEPARATOR=:
  (PATH='/bin;/bin'; FPATH=$PATH; sh -c :) >/dev/null 2>&1 && {
    (PATH='/bin:/bin'; FPATH=$PATH; sh -c :) >/dev/null 2>&1 ||
      PATH_SEPARATOR=';'
  }
fi


# Find who we are.  Look in the path if we contain no directory separator.
as_myself=
case $0 in @%:@((
  *[\\/]* ) as_myself=$0 ;;
  *) as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    test -r "$as_dir$0" && as_myself=$as_dir$0 && break
  done
IFS=$as_save_IFS

     ;;
esac
# We did not find ourselves, most probably we were run as `sh COMMAND'
# in which case we are not to be found in the path.
if test "x$as_myself" = x; then
  as_myself=$0
fi
if test ! -f "$as_myself"; then
  printf "%s\n" "$as_myself: error: cannot find myself; rerun with an absolute file name" >&2
  exit 1
fi


# Use a proper internal environment variable to ensure we don't fall
  # into an infinite loop, continuously re-executing ourselves.
  if test x"${_as_can_reexec}" != xno && test "x$CONFIG_SHELL" != x; then
    _as_can_reexec=no; export _as_can_reexec;
    # We cannot yet assume a decent shell, so we have to provide a
# neutralization value for shells without unset; and this also
# works around shells that cannot unset nonexistent variables.
# Preserve -v and -x to the replacement shell.
BASH_ENV=/dev/null
ENV=/dev/null
(unset BASH_ENV) >/dev/null 2>&1 && unset BASH_ENV ENV
case $- in @%:@ ((((
  *v*x* | *x*v* ) as_opts=-vx ;;
  *v* ) as_opts=-v ;;
  *x* ) as_opts=-x ;;
  * ) as_opts= ;;
esac
exec $CONFIG_SHELL $as_opts "$as_myself" ${1+"$@"}
# Admittedly, this is quite paranoid, since all the known shells bail
# out after a failed `exec'.
printf "%s\n" "$0: could not re-execute with $CONFIG_SHELL" >&2
exit 255
  fi
  # We don't want this to propagate to other subprocesses.
          { _as_can_reexec=; unset _as_can_reexec;}
if test "x$CONFIG_SHELL" = x; then
  as_bourne_compatible="as_nop=:
if test \${ZSH_VERSION+y} && (emulate sh) >/dev/null 2>&1
then :
  emulate sh
  NULLCMD=:
  # Pre-4.2 versions of Zsh do word splitting on \${1+\"\$@\"}, which
  # is contrary to our usage.  Disable this feature.
  alias -g '\${1+\"\$@\"}'='\"\$@\"'
  setopt NO_GLOB_SUBST
else \$as_nop
  case \`(set -o) 2>/dev/null\` in @%:@(
  *posix*) :
    set -o posix ;; @%:@(
  *) :
     ;;
esac
fi
"
  as_required="as_fn_return () { (exit \$1); }
as_fn_success () { as_fn_return 0; }
as_fn_failure () { as_fn_return 1; }
as_fn_ret_success () { return 0; }
as_fn_ret_failure () { return 1; }

exitcode=0
as_fn_success || { exitcode=1; echo as_fn_success failed.; }
as_fn_failure && { exitcode=1; echo as_fn_failure succeeded.; }
as_fn_ret_success || { exitcode=1; echo as_fn_ret_success failed.; }
as_fn_ret_failure && { exitcode=1; echo as_fn_ret_failure succeeded.; }
if ( set x; as_fn_ret_success y && test x = \"\$1\" )
then :
  
else \$as_nop
  exitcode=1; echo positional parameters were not saved.
fi
test x\$exitcode = x0 || exit 1
blah=\$(echo \$(echo blah))
test x\"\$blah\" = xblah || exit 1
test -x / || exit 1"
  as_suggested="  as_lineno_1=";as_suggested=$as_suggested$LINENO;as_suggested=$as_suggested" as_lineno_1a=\$LINENO
  as_lineno_2=";as_suggested=$as_suggested$LINENO;as_suggested=$as_suggested" as_lineno_2a=\$LINENO
  eval 'test \"x\$as_lineno_1'\$as_run'\" != \"x\$as_lineno_2'\$as_run'\" &&
  test \"x\`expr \$as_lineno_1'\$as_run' + 1\`\" = \"x\$as_lineno_2'\$as_run'\"' || exit 1
test \$(( 1 + 1 )) = 2 || exit 1"
  if (eval "$as_required") 2>/dev/null
then :
  as_have_required=yes
else $as_nop
  as_have_required=no
fi
  if test x$as_have_required = xyes && (eval "$as_suggested") 2>/dev/null
then :
  
else $as_nop
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
as_found=false
for as_dir in /bin$PATH_SEPARATOR/usr/bin$PATH_SEPARATOR$PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
  as_found=:
  case $as_dir in @%:@(
	 /*)
	   for as_base in sh bash ksh sh5; do
	     # Try only shells that exist, to save several forks.
	     as_shell=$as_dir$as_base
	     if { test -f "$as_shell" || test -f "$as_shell.exe"; } &&
		    as_run=a "$as_shell" -c "$as_bourne_compatible""$as_required" 2>/dev/null
then :
  CONFIG_SHELL=$as_shell as_have_required=yes
		   if as_run=a "$as_shell" -c "$as_bourne_compatible""$as_suggested" 2>/dev/null
then :
  break 2
fi
fi
	   done;;
       esac
  as_found=false
done
IFS=$as_save_IFS
if $as_found
then :
  
else $as_nop
  if { test -f "$SHELL" || test -f "$SHELL.exe"; } &&
	      as_run=a "$SHELL" -c "$as_bourne_compatible""$as_required" 2>/dev/null
then :
  CONFIG_SHELL=$SHELL as_have_required=yes
fi
fi


      if test "x$CONFIG_SHELL" != x
then :
  export CONFIG_SHELL
             # We cannot yet assume a decent shell, so we have to provide a
# neutralization value for shells without unset; and this also
# works around shells that cannot unset nonexistent variables.
# Preserve -v and -x to the replacement shell.
BASH_ENV=/dev/null
ENV=/dev/null
(unset BASH_ENV) >/dev/null 2>&1 && unset BASH_ENV ENV
case $- in @%:@ ((((
  *v*x* | *x*v* ) as_opts=-vx ;;
  *v* ) as_opts=-v ;;
  *x* ) as_opts=-x ;;
  * ) as_opts= ;;
esac
exec $CONFIG_SHELL $as_opts "$as_myself" ${1+"$@"}
# Admittedly, this is quite paranoid, since all the known shells bail
# out after a failed `exec'.
printf "%s\n" "$0: could not re-execute with $CONFIG_SHELL" >&2
exit 255
fi

    if test x$as_have_required = xno
then :
  printf "%s\n" "$0: This script requires a shell more modern than all"
  printf "%s\n" "$0: the shells that I found on your system."
  if test ${ZSH_VERSION+y} ; then
    printf "%s\n" "$0: In particular, zsh $ZSH_VERSION has bugs and should"
    printf "%s\n" "$0: be upgraded to zsh 4.3.4 or later."
  else
    printf "%s\n" "$0: <NAME_EMAIL> and
$0: https://github.com/the-tcpdump-group/tcpdump/issues
$0: about your system, including any error possibly output
$0: before this message. Then install a modern shell, or
$0: manually run the script under such a shell if you do
$0: have one."
  fi
  exit 1
fi
fi
fi
SHELL=${CONFIG_SHELL-/bin/sh}
export SHELL
# Unset more variables known to interfere with behavior of common tools.
CLICOLOR_FORCE= GREP_OPTIONS=
unset CLICOLOR_FORCE GREP_OPTIONS

## --------------------- ##
## M4sh Shell Functions. ##
## --------------------- ##
@%:@ as_fn_unset VAR
@%:@ ---------------
@%:@ Portably unset VAR.
as_fn_unset ()
{
  { eval $1=; unset $1;}
}
as_unset=as_fn_unset


@%:@ as_fn_set_status STATUS
@%:@ -----------------------
@%:@ Set @S|@? to STATUS, without forking.
as_fn_set_status ()
{
  return $1
} @%:@ as_fn_set_status

@%:@ as_fn_exit STATUS
@%:@ -----------------
@%:@ Exit the shell with STATUS, even in a "trap 0" or "set -e" context.
as_fn_exit ()
{
  set +e
  as_fn_set_status $1
  exit $1
} @%:@ as_fn_exit
@%:@ as_fn_nop
@%:@ ---------
@%:@ Do nothing but, unlike ":", preserve the value of @S|@?.
as_fn_nop ()
{
  return $?
}
as_nop=as_fn_nop

@%:@ as_fn_mkdir_p
@%:@ -------------
@%:@ Create "@S|@as_dir" as a directory, including parents if necessary.
as_fn_mkdir_p ()
{

  case $as_dir in #(
  -*) as_dir=./$as_dir;;
  esac
  test -d "$as_dir" || eval $as_mkdir_p || {
    as_dirs=
    while :; do
      case $as_dir in #(
      *\'*) as_qdir=`printf "%s\n" "$as_dir" | sed "s/'/'\\\\\\\\''/g"`;; #'(
      *) as_qdir=$as_dir;;
      esac
      as_dirs="'$as_qdir' $as_dirs"
      as_dir=`$as_dirname -- "$as_dir" ||
$as_expr X"$as_dir" : 'X\(.*[^/]\)//*[^/][^/]*/*$' \| \
	 X"$as_dir" : 'X\(//\)[^/]' \| \
	 X"$as_dir" : 'X\(//\)$' \| \
	 X"$as_dir" : 'X\(/\)' \| . 2>/dev/null ||
printf "%s\n" X"$as_dir" |
    sed '/^X\(.*[^/]\)\/\/*[^/][^/]*\/*$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)[^/].*/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`
      test -d "$as_dir" && break
    done
    test -z "$as_dirs" || eval "mkdir $as_dirs"
  } || test -d "$as_dir" || as_fn_error $? "cannot create directory $as_dir"


} @%:@ as_fn_mkdir_p

@%:@ as_fn_executable_p FILE
@%:@ -----------------------
@%:@ Test if FILE is an executable regular file.
as_fn_executable_p ()
{
  test -f "$1" && test -x "$1"
} @%:@ as_fn_executable_p
@%:@ as_fn_append VAR VALUE
@%:@ ----------------------
@%:@ Append the text in VALUE to the end of the definition contained in VAR. Take
@%:@ advantage of any shell optimizations that allow amortized linear growth over
@%:@ repeated appends, instead of the typical quadratic growth present in naive
@%:@ implementations.
if (eval "as_var=1; as_var+=2; test x\$as_var = x12") 2>/dev/null
then :
  eval 'as_fn_append ()
  {
    eval $1+=\$2
  }'
else $as_nop
  as_fn_append ()
  {
    eval $1=\$$1\$2
  }
fi # as_fn_append

@%:@ as_fn_arith ARG...
@%:@ ------------------
@%:@ Perform arithmetic evaluation on the ARGs, and store the result in the
@%:@ global @S|@as_val. Take advantage of shells that can avoid forks. The arguments
@%:@ must be portable across @S|@(()) and expr.
if (eval "test \$(( 1 + 1 )) = 2") 2>/dev/null
then :
  eval 'as_fn_arith ()
  {
    as_val=$(( $* ))
  }'
else $as_nop
  as_fn_arith ()
  {
    as_val=`expr "$@" || test $? -eq 1`
  }
fi # as_fn_arith

@%:@ as_fn_nop
@%:@ ---------
@%:@ Do nothing but, unlike ":", preserve the value of @S|@?.
as_fn_nop ()
{
  return $?
}
as_nop=as_fn_nop

@%:@ as_fn_error STATUS ERROR [LINENO LOG_FD]
@%:@ ----------------------------------------
@%:@ Output "`basename @S|@0`: error: ERROR" to stderr. If LINENO and LOG_FD are
@%:@ provided, also output the error to LOG_FD, referencing LINENO. Then exit the
@%:@ script with STATUS, using 1 if that was 0.
as_fn_error ()
{
  as_status=$1; test $as_status -eq 0 && as_status=1
  if test "$4"; then
    as_lineno=${as_lineno-"$3"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: $2" >&$4
  fi
  printf "%s\n" "$as_me: error: $2" >&2
  as_fn_exit $as_status
} @%:@ as_fn_error

if expr a : '\(a\)' >/dev/null 2>&1 &&
   test "X`expr 00001 : '.*\(...\)'`" = X001; then
  as_expr=expr
else
  as_expr=false
fi

if (basename -- /) >/dev/null 2>&1 && test "X`basename -- / 2>&1`" = "X/"; then
  as_basename=basename
else
  as_basename=false
fi

if (as_dir=`dirname -- /` && test "X$as_dir" = X/) >/dev/null 2>&1; then
  as_dirname=dirname
else
  as_dirname=false
fi

as_me=`$as_basename -- "$0" ||
$as_expr X/"$0" : '.*/\([^/][^/]*\)/*$' \| \
	 X"$0" : 'X\(//\)$' \| \
	 X"$0" : 'X\(/\)' \| . 2>/dev/null ||
printf "%s\n" X/"$0" |
    sed '/^.*\/\([^/][^/]*\)\/*$/{
	    s//\1/
	    q
	  }
	  /^X\/\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\/\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`

# Avoid depending upon Character Ranges.
as_cr_letters='abcdefghijklmnopqrstuvwxyz'
as_cr_LETTERS='ABCDEFGHIJKLMNOPQRSTUVWXYZ'
as_cr_Letters=$as_cr_letters$as_cr_LETTERS
as_cr_digits='0123456789'
as_cr_alnum=$as_cr_Letters$as_cr_digits


  as_lineno_1=$LINENO as_lineno_1a=$LINENO
  as_lineno_2=$LINENO as_lineno_2a=$LINENO
  eval 'test "x$as_lineno_1'$as_run'" != "x$as_lineno_2'$as_run'" &&
  test "x`expr $as_lineno_1'$as_run' + 1`" = "x$as_lineno_2'$as_run'"' || {
  # Blame Lee E. McMahon (1931-1989) for sed's syntax.  :-)
  sed -n '
    p
    /[$]LINENO/=
  ' <$as_myself |
    sed '
      s/[$]LINENO.*/&-/
      t lineno
      b
      :lineno
      N
      :loop
      s/[$]LINENO\([^'$as_cr_alnum'_].*\n\)\(.*\)/\2\1\2/
      t loop
      s/-\n.*//
    ' >$as_me.lineno &&
  chmod +x "$as_me.lineno" ||
    { printf "%s\n" "$as_me: error: cannot create $as_me.lineno; rerun with a POSIX shell" >&2; as_fn_exit 1; }

  # If we had to re-execute with $CONFIG_SHELL, we're ensured to have
  # already done that, so ensure we don't try to do so again and fall
  # in an infinite loop.  This has already happened in practice.
  _as_can_reexec=no; export _as_can_reexec
  # Don't try to exec as it changes $[0], causing all sort of problems
  # (the dirname of $[0] is not the place where we might find the
  # original and so on.  Autoconf is especially sensitive to this).
  . "./$as_me.lineno"
  # Exit status is that of the last command.
  exit
}


# Determine whether it's possible to make 'echo' print without a newline.
# These variables are no longer used directly by Autoconf, but are AC_SUBSTed
# for compatibility with existing Makefiles.
ECHO_C= ECHO_N= ECHO_T=
case `echo -n x` in @%:@(((((
-n*)
  case `echo 'xy\c'` in
  *c*) ECHO_T='	';;	# ECHO_T is single tab character.
  xy)  ECHO_C='\c';;
  *)   echo `echo ksh88 bug on AIX 6.1` > /dev/null
       ECHO_T='	';;
  esac;;
*)
  ECHO_N='-n';;
esac

# For backward compatibility with old third-party macros, we provide
# the shell variables $as_echo and $as_echo_n.  New code should use
# AS_ECHO(["message"]) and AS_ECHO_N(["message"]), respectively.
as_@&t@echo='printf %s\n'
as_@&t@echo_n='printf %s'


rm -f conf$$ conf$$.exe conf$$.file
if test -d conf$$.dir; then
  rm -f conf$$.dir/conf$$.file
else
  rm -f conf$$.dir
  mkdir conf$$.dir 2>/dev/null
fi
if (echo >conf$$.file) 2>/dev/null; then
  if ln -s conf$$.file conf$$ 2>/dev/null; then
    as_ln_s='ln -s'
    # ... but there are two gotchas:
    # 1) On MSYS, both `ln -s file dir' and `ln file dir' fail.
    # 2) DJGPP < 2.04 has no symlinks; `ln -s' creates a wrapper executable.
    # In both cases, we have to default to `cp -pR'.
    ln -s conf$$.file conf$$.dir 2>/dev/null && test ! -f conf$$.exe ||
      as_ln_s='cp -pR'
  elif ln conf$$.file conf$$ 2>/dev/null; then
    as_ln_s=ln
  else
    as_ln_s='cp -pR'
  fi
else
  as_ln_s='cp -pR'
fi
rm -f conf$$ conf$$.exe conf$$.dir/conf$$.file conf$$.file
rmdir conf$$.dir 2>/dev/null

if mkdir -p . 2>/dev/null; then
  as_mkdir_p='mkdir -p "$as_dir"'
else
  test -d ./-p && rmdir ./-p
  as_mkdir_p=false
fi

as_test_x='test -x'
as_executable_p=as_fn_executable_p

# Sed expression to map a string onto a valid CPP name.
as_tr_cpp="eval sed 'y%*$as_cr_letters%P$as_cr_LETTERS%;s%[^_$as_cr_alnum]%_%g'"

# Sed expression to map a string onto a valid variable name.
as_tr_sh="eval sed 'y%*+%pp%;s%[^_$as_cr_alnum]%_%g'"


test -n "$DJDIR" || exec 7<&0 </dev/null
exec 6>&1

# Name of the host.
# hostname on some systems (SVR3.2, old GNU/Linux) returns a bogus exit status,
# so uname gets run too.
ac_hostname=`(hostname || uname -n) 2>/dev/null | sed 1q`

#
# Initializations.
#
ac_default_prefix=/usr/local
ac_clean_files=
ac_config_libobj_dir=.
LIB@&t@OBJS=
cross_compiling=no
subdirs=
MFLAGS=
MAKEFLAGS=

# Identity of this package.
PACKAGE_NAME='tcpdump'
PACKAGE_TARNAME='tcpdump'
PACKAGE_VERSION='5.0.0-PRE-GIT'
PACKAGE_STRING='tcpdump 5.0.0-PRE-GIT'
PACKAGE_BUGREPORT='https://github.com/the-tcpdump-group/tcpdump/issues'
PACKAGE_URL='https://www.tcpdump.org/'

ac_unique_file="tcpdump.c"
# Factoring default headers for most tests.
ac_includes_default="\
#include <stddef.h>
#ifdef HAVE_STDIO_H
# include <stdio.h>
#endif
#ifdef HAVE_STDLIB_H
# include <stdlib.h>
#endif
#ifdef HAVE_STRING_H
# include <string.h>
#endif
#ifdef HAVE_INTTYPES_H
# include <inttypes.h>
#endif
#ifdef HAVE_STDINT_H
# include <stdint.h>
#endif
#ifdef HAVE_STRINGS_H
# include <strings.h>
#endif
#ifdef HAVE_SYS_TYPES_H
# include <sys/types.h>
#endif
#ifdef HAVE_SYS_STAT_H
# include <sys/stat.h>
#endif
#ifdef HAVE_UNISTD_H
# include <unistd.h>
#endif"

ac_header_c_list=
ac_subst_vars='LTLIBOBJS
INSTALL_DATA
INSTALL_SCRIPT
INSTALL_PROGRAM
MAN_MISC_INFO
MAN_FILE_FORMATS
LOCALSRC
V_PCAPDEP
V_INCLS
V_DEFS
V_CCOPT
LIBCRYPTO_LIBS_STATIC
LIBCRYPTO_LIBS
LIBCRYPTO_CFLAGS
MKDEP
DEPENDENCY_CFLAG
AR
RANLIB
PCAP_CONFIG
LIBPCAP_LIBS_STATIC
LIBPCAP_LIBS
LIBPCAP_CFLAGS
EGREP
GREP
LIB@&t@OBJS
BREW
PKG_CONFIG_LIBDIR
PKG_CONFIG_PATH
PKG_CONFIG
OBJEXT
EXEEXT
ac_ct_CC
CPPFLAGS
LDFLAGS
CFLAGS
CC
host_os
host_vendor
host_cpu
host
build_os
build_vendor
build_cpu
build
target_alias
host_alias
build_alias
LIBS
ECHO_T
ECHO_N
ECHO_C
DEFS
mandir
localedir
libdir
psdir
pdfdir
dvidir
htmldir
infodir
docdir
oldincludedir
includedir
runstatedir
localstatedir
sharedstatedir
sysconfdir
datadir
datarootdir
libexecdir
sbindir
bindir
program_transform_name
prefix
exec_prefix
PACKAGE_URL
PACKAGE_BUGREPORT
PACKAGE_STRING
PACKAGE_VERSION
PACKAGE_TARNAME
PACKAGE_NAME
PATH_SEPARATOR
SHELL'
ac_subst_files=''
ac_user_opts='
enable_option_checking
with_gcc
enable_universal
with_smi
enable_instrument_functions
enable_smb
with_user
with_chroot
with_sandbox_capsicum
enable_local_libpcap
with_crypto
with_cap_ng
'
      ac_precious_vars='build_alias
host_alias
target_alias
CC
CFLAGS
LDFLAGS
LIBS
CPPFLAGS
PKG_CONFIG
PKG_CONFIG_PATH
PKG_CONFIG_LIBDIR
LIBPCAP_CFLAGS
LIBPCAP_LIBS
LIBPCAP_LIBS_STATIC
LIBCRYPTO_CFLAGS
LIBCRYPTO_LIBS
LIBCRYPTO_LIBS_STATIC'


# Initialize some variables set by options.
ac_init_help=
ac_init_version=false
ac_unrecognized_opts=
ac_unrecognized_sep=
# The variables have the same names as the options, with
# dashes changed to underlines.
cache_file=/dev/null
exec_prefix=NONE
no_create=
no_recursion=
prefix=NONE
program_prefix=NONE
program_suffix=NONE
program_transform_name=s,x,x,
silent=
site=
srcdir=
verbose=
x_includes=NONE
x_libraries=NONE

# Installation directory options.
# These are left unexpanded so users can "make install exec_prefix=/foo"
# and all the variables that are supposed to be based on exec_prefix
# by default will actually change.
# Use braces instead of parens because sh, perl, etc. also accept them.
# (The list follows the same order as the GNU Coding Standards.)
bindir='${exec_prefix}/bin'
sbindir='${exec_prefix}/sbin'
libexecdir='${exec_prefix}/libexec'
datarootdir='${prefix}/share'
datadir='${datarootdir}'
sysconfdir='${prefix}/etc'
sharedstatedir='${prefix}/com'
localstatedir='${prefix}/var'
runstatedir='${localstatedir}/run'
includedir='${prefix}/include'
oldincludedir='/usr/include'
docdir='${datarootdir}/doc/${PACKAGE_TARNAME}'
infodir='${datarootdir}/info'
htmldir='${docdir}'
dvidir='${docdir}'
pdfdir='${docdir}'
psdir='${docdir}'
libdir='${exec_prefix}/lib'
localedir='${datarootdir}/locale'
mandir='${datarootdir}/man'

ac_prev=
ac_dashdash=
for ac_option
do
  # If the previous option needs an argument, assign it.
  if test -n "$ac_prev"; then
    eval $ac_prev=\$ac_option
    ac_prev=
    continue
  fi

  case $ac_option in
  *=?*) ac_optarg=`expr "X$ac_option" : '[^=]*=\(.*\)'` ;;
  *=)   ac_optarg= ;;
  *)    ac_optarg=yes ;;
  esac

  case $ac_dashdash$ac_option in
  --)
    ac_dashdash=yes ;;

  -bindir | --bindir | --bindi | --bind | --bin | --bi)
    ac_prev=bindir ;;
  -bindir=* | --bindir=* | --bindi=* | --bind=* | --bin=* | --bi=*)
    bindir=$ac_optarg ;;

  -build | --build | --buil | --bui | --bu)
    ac_prev=build_alias ;;
  -build=* | --build=* | --buil=* | --bui=* | --bu=*)
    build_alias=$ac_optarg ;;

  -cache-file | --cache-file | --cache-fil | --cache-fi \
  | --cache-f | --cache- | --cache | --cach | --cac | --ca | --c)
    ac_prev=cache_file ;;
  -cache-file=* | --cache-file=* | --cache-fil=* | --cache-fi=* \
  | --cache-f=* | --cache-=* | --cache=* | --cach=* | --cac=* | --ca=* | --c=*)
    cache_file=$ac_optarg ;;

  --config-cache | -C)
    cache_file=config.cache ;;

  -datadir | --datadir | --datadi | --datad)
    ac_prev=datadir ;;
  -datadir=* | --datadir=* | --datadi=* | --datad=*)
    datadir=$ac_optarg ;;

  -datarootdir | --datarootdir | --datarootdi | --datarootd | --dataroot \
  | --dataroo | --dataro | --datar)
    ac_prev=datarootdir ;;
  -datarootdir=* | --datarootdir=* | --datarootdi=* | --datarootd=* \
  | --dataroot=* | --dataroo=* | --dataro=* | --datar=*)
    datarootdir=$ac_optarg ;;

  -disable-* | --disable-*)
    ac_useropt=`expr "x$ac_option" : 'x-*disable-\(.*\)'`
    # Reject names that are not valid shell variable names.
    expr "x$ac_useropt" : ".*[^-+._$as_cr_alnum]" >/dev/null &&
      as_fn_error $? "invalid feature name: \`$ac_useropt'"
    ac_useropt_orig=$ac_useropt
    ac_useropt=`printf "%s\n" "$ac_useropt" | sed 's/[-+.]/_/g'`
    case $ac_user_opts in
      *"
"enable_$ac_useropt"
"*) ;;
      *) ac_unrecognized_opts="$ac_unrecognized_opts$ac_unrecognized_sep--disable-$ac_useropt_orig"
	 ac_unrecognized_sep=', ';;
    esac
    eval enable_$ac_useropt=no ;;

  -docdir | --docdir | --docdi | --doc | --do)
    ac_prev=docdir ;;
  -docdir=* | --docdir=* | --docdi=* | --doc=* | --do=*)
    docdir=$ac_optarg ;;

  -dvidir | --dvidir | --dvidi | --dvid | --dvi | --dv)
    ac_prev=dvidir ;;
  -dvidir=* | --dvidir=* | --dvidi=* | --dvid=* | --dvi=* | --dv=*)
    dvidir=$ac_optarg ;;

  -enable-* | --enable-*)
    ac_useropt=`expr "x$ac_option" : 'x-*enable-\([^=]*\)'`
    # Reject names that are not valid shell variable names.
    expr "x$ac_useropt" : ".*[^-+._$as_cr_alnum]" >/dev/null &&
      as_fn_error $? "invalid feature name: \`$ac_useropt'"
    ac_useropt_orig=$ac_useropt
    ac_useropt=`printf "%s\n" "$ac_useropt" | sed 's/[-+.]/_/g'`
    case $ac_user_opts in
      *"
"enable_$ac_useropt"
"*) ;;
      *) ac_unrecognized_opts="$ac_unrecognized_opts$ac_unrecognized_sep--enable-$ac_useropt_orig"
	 ac_unrecognized_sep=', ';;
    esac
    eval enable_$ac_useropt=\$ac_optarg ;;

  -exec-prefix | --exec_prefix | --exec-prefix | --exec-prefi \
  | --exec-pref | --exec-pre | --exec-pr | --exec-p | --exec- \
  | --exec | --exe | --ex)
    ac_prev=exec_prefix ;;
  -exec-prefix=* | --exec_prefix=* | --exec-prefix=* | --exec-prefi=* \
  | --exec-pref=* | --exec-pre=* | --exec-pr=* | --exec-p=* | --exec-=* \
  | --exec=* | --exe=* | --ex=*)
    exec_prefix=$ac_optarg ;;

  -gas | --gas | --ga | --g)
    # Obsolete; use --with-gas.
    with_gas=yes ;;

  -help | --help | --hel | --he | -h)
    ac_init_help=long ;;
  -help=r* | --help=r* | --hel=r* | --he=r* | -hr*)
    ac_init_help=recursive ;;
  -help=s* | --help=s* | --hel=s* | --he=s* | -hs*)
    ac_init_help=short ;;

  -host | --host | --hos | --ho)
    ac_prev=host_alias ;;
  -host=* | --host=* | --hos=* | --ho=*)
    host_alias=$ac_optarg ;;

  -htmldir | --htmldir | --htmldi | --htmld | --html | --htm | --ht)
    ac_prev=htmldir ;;
  -htmldir=* | --htmldir=* | --htmldi=* | --htmld=* | --html=* | --htm=* \
  | --ht=*)
    htmldir=$ac_optarg ;;

  -includedir | --includedir | --includedi | --included | --include \
  | --includ | --inclu | --incl | --inc)
    ac_prev=includedir ;;
  -includedir=* | --includedir=* | --includedi=* | --included=* | --include=* \
  | --includ=* | --inclu=* | --incl=* | --inc=*)
    includedir=$ac_optarg ;;

  -infodir | --infodir | --infodi | --infod | --info | --inf)
    ac_prev=infodir ;;
  -infodir=* | --infodir=* | --infodi=* | --infod=* | --info=* | --inf=*)
    infodir=$ac_optarg ;;

  -libdir | --libdir | --libdi | --libd)
    ac_prev=libdir ;;
  -libdir=* | --libdir=* | --libdi=* | --libd=*)
    libdir=$ac_optarg ;;

  -libexecdir | --libexecdir | --libexecdi | --libexecd | --libexec \
  | --libexe | --libex | --libe)
    ac_prev=libexecdir ;;
  -libexecdir=* | --libexecdir=* | --libexecdi=* | --libexecd=* | --libexec=* \
  | --libexe=* | --libex=* | --libe=*)
    libexecdir=$ac_optarg ;;

  -localedir | --localedir | --localedi | --localed | --locale)
    ac_prev=localedir ;;
  -localedir=* | --localedir=* | --localedi=* | --localed=* | --locale=*)
    localedir=$ac_optarg ;;

  -localstatedir | --localstatedir | --localstatedi | --localstated \
  | --localstate | --localstat | --localsta | --localst | --locals)
    ac_prev=localstatedir ;;
  -localstatedir=* | --localstatedir=* | --localstatedi=* | --localstated=* \
  | --localstate=* | --localstat=* | --localsta=* | --localst=* | --locals=*)
    localstatedir=$ac_optarg ;;

  -mandir | --mandir | --mandi | --mand | --man | --ma | --m)
    ac_prev=mandir ;;
  -mandir=* | --mandir=* | --mandi=* | --mand=* | --man=* | --ma=* | --m=*)
    mandir=$ac_optarg ;;

  -nfp | --nfp | --nf)
    # Obsolete; use --without-fp.
    with_fp=no ;;

  -no-create | --no-create | --no-creat | --no-crea | --no-cre \
  | --no-cr | --no-c | -n)
    no_create=yes ;;

  -no-recursion | --no-recursion | --no-recursio | --no-recursi \
  | --no-recurs | --no-recur | --no-recu | --no-rec | --no-re | --no-r)
    no_recursion=yes ;;

  -oldincludedir | --oldincludedir | --oldincludedi | --oldincluded \
  | --oldinclude | --oldinclud | --oldinclu | --oldincl | --oldinc \
  | --oldin | --oldi | --old | --ol | --o)
    ac_prev=oldincludedir ;;
  -oldincludedir=* | --oldincludedir=* | --oldincludedi=* | --oldincluded=* \
  | --oldinclude=* | --oldinclud=* | --oldinclu=* | --oldincl=* | --oldinc=* \
  | --oldin=* | --oldi=* | --old=* | --ol=* | --o=*)
    oldincludedir=$ac_optarg ;;

  -prefix | --prefix | --prefi | --pref | --pre | --pr | --p)
    ac_prev=prefix ;;
  -prefix=* | --prefix=* | --prefi=* | --pref=* | --pre=* | --pr=* | --p=*)
    prefix=$ac_optarg ;;

  -program-prefix | --program-prefix | --program-prefi | --program-pref \
  | --program-pre | --program-pr | --program-p)
    ac_prev=program_prefix ;;
  -program-prefix=* | --program-prefix=* | --program-prefi=* \
  | --program-pref=* | --program-pre=* | --program-pr=* | --program-p=*)
    program_prefix=$ac_optarg ;;

  -program-suffix | --program-suffix | --program-suffi | --program-suff \
  | --program-suf | --program-su | --program-s)
    ac_prev=program_suffix ;;
  -program-suffix=* | --program-suffix=* | --program-suffi=* \
  | --program-suff=* | --program-suf=* | --program-su=* | --program-s=*)
    program_suffix=$ac_optarg ;;

  -program-transform-name | --program-transform-name \
  | --program-transform-nam | --program-transform-na \
  | --program-transform-n | --program-transform- \
  | --program-transform | --program-transfor \
  | --program-transfo | --program-transf \
  | --program-trans | --program-tran \
  | --progr-tra | --program-tr | --program-t)
    ac_prev=program_transform_name ;;
  -program-transform-name=* | --program-transform-name=* \
  | --program-transform-nam=* | --program-transform-na=* \
  | --program-transform-n=* | --program-transform-=* \
  | --program-transform=* | --program-transfor=* \
  | --program-transfo=* | --program-transf=* \
  | --program-trans=* | --program-tran=* \
  | --progr-tra=* | --program-tr=* | --program-t=*)
    program_transform_name=$ac_optarg ;;

  -pdfdir | --pdfdir | --pdfdi | --pdfd | --pdf | --pd)
    ac_prev=pdfdir ;;
  -pdfdir=* | --pdfdir=* | --pdfdi=* | --pdfd=* | --pdf=* | --pd=*)
    pdfdir=$ac_optarg ;;

  -psdir | --psdir | --psdi | --psd | --ps)
    ac_prev=psdir ;;
  -psdir=* | --psdir=* | --psdi=* | --psd=* | --ps=*)
    psdir=$ac_optarg ;;

  -q | -quiet | --quiet | --quie | --qui | --qu | --q \
  | -silent | --silent | --silen | --sile | --sil)
    silent=yes ;;

  -runstatedir | --runstatedir | --runstatedi | --runstated \
  | --runstate | --runstat | --runsta | --runst | --runs \
  | --run | --ru | --r)
    ac_prev=runstatedir ;;
  -runstatedir=* | --runstatedir=* | --runstatedi=* | --runstated=* \
  | --runstate=* | --runstat=* | --runsta=* | --runst=* | --runs=* \
  | --run=* | --ru=* | --r=*)
    runstatedir=$ac_optarg ;;

  -sbindir | --sbindir | --sbindi | --sbind | --sbin | --sbi | --sb)
    ac_prev=sbindir ;;
  -sbindir=* | --sbindir=* | --sbindi=* | --sbind=* | --sbin=* \
  | --sbi=* | --sb=*)
    sbindir=$ac_optarg ;;

  -sharedstatedir | --sharedstatedir | --sharedstatedi \
  | --sharedstated | --sharedstate | --sharedstat | --sharedsta \
  | --sharedst | --shareds | --shared | --share | --shar \
  | --sha | --sh)
    ac_prev=sharedstatedir ;;
  -sharedstatedir=* | --sharedstatedir=* | --sharedstatedi=* \
  | --sharedstated=* | --sharedstate=* | --sharedstat=* | --sharedsta=* \
  | --sharedst=* | --shareds=* | --shared=* | --share=* | --shar=* \
  | --sha=* | --sh=*)
    sharedstatedir=$ac_optarg ;;

  -site | --site | --sit)
    ac_prev=site ;;
  -site=* | --site=* | --sit=*)
    site=$ac_optarg ;;

  -srcdir | --srcdir | --srcdi | --srcd | --src | --sr)
    ac_prev=srcdir ;;
  -srcdir=* | --srcdir=* | --srcdi=* | --srcd=* | --src=* | --sr=*)
    srcdir=$ac_optarg ;;

  -sysconfdir | --sysconfdir | --sysconfdi | --sysconfd | --sysconf \
  | --syscon | --sysco | --sysc | --sys | --sy)
    ac_prev=sysconfdir ;;
  -sysconfdir=* | --sysconfdir=* | --sysconfdi=* | --sysconfd=* | --sysconf=* \
  | --syscon=* | --sysco=* | --sysc=* | --sys=* | --sy=*)
    sysconfdir=$ac_optarg ;;

  -target | --target | --targe | --targ | --tar | --ta | --t)
    ac_prev=target_alias ;;
  -target=* | --target=* | --targe=* | --targ=* | --tar=* | --ta=* | --t=*)
    target_alias=$ac_optarg ;;

  -v | -verbose | --verbose | --verbos | --verbo | --verb)
    verbose=yes ;;

  -version | --version | --versio | --versi | --vers | -V)
    ac_init_version=: ;;

  -with-* | --with-*)
    ac_useropt=`expr "x$ac_option" : 'x-*with-\([^=]*\)'`
    # Reject names that are not valid shell variable names.
    expr "x$ac_useropt" : ".*[^-+._$as_cr_alnum]" >/dev/null &&
      as_fn_error $? "invalid package name: \`$ac_useropt'"
    ac_useropt_orig=$ac_useropt
    ac_useropt=`printf "%s\n" "$ac_useropt" | sed 's/[-+.]/_/g'`
    case $ac_user_opts in
      *"
"with_$ac_useropt"
"*) ;;
      *) ac_unrecognized_opts="$ac_unrecognized_opts$ac_unrecognized_sep--with-$ac_useropt_orig"
	 ac_unrecognized_sep=', ';;
    esac
    eval with_$ac_useropt=\$ac_optarg ;;

  -without-* | --without-*)
    ac_useropt=`expr "x$ac_option" : 'x-*without-\(.*\)'`
    # Reject names that are not valid shell variable names.
    expr "x$ac_useropt" : ".*[^-+._$as_cr_alnum]" >/dev/null &&
      as_fn_error $? "invalid package name: \`$ac_useropt'"
    ac_useropt_orig=$ac_useropt
    ac_useropt=`printf "%s\n" "$ac_useropt" | sed 's/[-+.]/_/g'`
    case $ac_user_opts in
      *"
"with_$ac_useropt"
"*) ;;
      *) ac_unrecognized_opts="$ac_unrecognized_opts$ac_unrecognized_sep--without-$ac_useropt_orig"
	 ac_unrecognized_sep=', ';;
    esac
    eval with_$ac_useropt=no ;;

  --x)
    # Obsolete; use --with-x.
    with_x=yes ;;

  -x-includes | --x-includes | --x-include | --x-includ | --x-inclu \
  | --x-incl | --x-inc | --x-in | --x-i)
    ac_prev=x_includes ;;
  -x-includes=* | --x-includes=* | --x-include=* | --x-includ=* | --x-inclu=* \
  | --x-incl=* | --x-inc=* | --x-in=* | --x-i=*)
    x_includes=$ac_optarg ;;

  -x-libraries | --x-libraries | --x-librarie | --x-librari \
  | --x-librar | --x-libra | --x-libr | --x-lib | --x-li | --x-l)
    ac_prev=x_libraries ;;
  -x-libraries=* | --x-libraries=* | --x-librarie=* | --x-librari=* \
  | --x-librar=* | --x-libra=* | --x-libr=* | --x-lib=* | --x-li=* | --x-l=*)
    x_libraries=$ac_optarg ;;

  -*) as_fn_error $? "unrecognized option: \`$ac_option'
Try \`$0 --help' for more information"
    ;;

  *=*)
    ac_envvar=`expr "x$ac_option" : 'x\([^=]*\)='`
    # Reject names that are not valid shell variable names.
    case $ac_envvar in #(
      '' | [0-9]* | *[!_$as_cr_alnum]* )
      as_fn_error $? "invalid variable name: \`$ac_envvar'" ;;
    esac
    eval $ac_envvar=\$ac_optarg
    export $ac_envvar ;;

  *)
    # FIXME: should be removed in autoconf 3.0.
    printf "%s\n" "$as_me: WARNING: you should use --build, --host, --target" >&2
    expr "x$ac_option" : ".*[^-._$as_cr_alnum]" >/dev/null &&
      printf "%s\n" "$as_me: WARNING: invalid host type: $ac_option" >&2
    : "${build_alias=$ac_option} ${host_alias=$ac_option} ${target_alias=$ac_option}"
    ;;

  esac
done

if test -n "$ac_prev"; then
  ac_option=--`echo $ac_prev | sed 's/_/-/g'`
  as_fn_error $? "missing argument to $ac_option"
fi

if test -n "$ac_unrecognized_opts"; then
  case $enable_option_checking in
    no) ;;
    fatal) as_fn_error $? "unrecognized options: $ac_unrecognized_opts" ;;
    *)     printf "%s\n" "$as_me: WARNING: unrecognized options: $ac_unrecognized_opts" >&2 ;;
  esac
fi

# Check all directory arguments for consistency.
for ac_var in	exec_prefix prefix bindir sbindir libexecdir datarootdir \
		datadir sysconfdir sharedstatedir localstatedir includedir \
		oldincludedir docdir infodir htmldir dvidir pdfdir psdir \
		libdir localedir mandir runstatedir
do
  eval ac_val=\$$ac_var
  # Remove trailing slashes.
  case $ac_val in
    */ )
      ac_val=`expr "X$ac_val" : 'X\(.*[^/]\)' \| "X$ac_val" : 'X\(.*\)'`
      eval $ac_var=\$ac_val;;
  esac
  # Be sure to have absolute directory names.
  case $ac_val in
    [\\/$]* | ?:[\\/]* )  continue;;
    NONE | '' ) case $ac_var in *prefix ) continue;; esac;;
  esac
  as_fn_error $? "expected an absolute directory name for --$ac_var: $ac_val"
done

# There might be people who depend on the old broken behavior: `$host'
# used to hold the argument of --host etc.
# FIXME: To remove some day.
build=$build_alias
host=$host_alias
target=$target_alias

# FIXME: To remove some day.
if test "x$host_alias" != x; then
  if test "x$build_alias" = x; then
    cross_compiling=maybe
  elif test "x$build_alias" != "x$host_alias"; then
    cross_compiling=yes
  fi
fi

ac_tool_prefix=
test -n "$host_alias" && ac_tool_prefix=$host_alias-

test "$silent" = yes && exec 6>/dev/null


ac_pwd=`pwd` && test -n "$ac_pwd" &&
ac_ls_di=`ls -di .` &&
ac_pwd_ls_di=`cd "$ac_pwd" && ls -di .` ||
  as_fn_error $? "working directory cannot be determined"
test "X$ac_ls_di" = "X$ac_pwd_ls_di" ||
  as_fn_error $? "pwd does not report name of working directory"


# Find the source files, if location was not specified.
if test -z "$srcdir"; then
  ac_srcdir_defaulted=yes
  # Try the directory containing this script, then the parent directory.
  ac_confdir=`$as_dirname -- "$as_myself" ||
$as_expr X"$as_myself" : 'X\(.*[^/]\)//*[^/][^/]*/*$' \| \
	 X"$as_myself" : 'X\(//\)[^/]' \| \
	 X"$as_myself" : 'X\(//\)$' \| \
	 X"$as_myself" : 'X\(/\)' \| . 2>/dev/null ||
printf "%s\n" X"$as_myself" |
    sed '/^X\(.*[^/]\)\/\/*[^/][^/]*\/*$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)[^/].*/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`
  srcdir=$ac_confdir
  if test ! -r "$srcdir/$ac_unique_file"; then
    srcdir=..
  fi
else
  ac_srcdir_defaulted=no
fi
if test ! -r "$srcdir/$ac_unique_file"; then
  test "$ac_srcdir_defaulted" = yes && srcdir="$ac_confdir or .."
  as_fn_error $? "cannot find sources ($ac_unique_file) in $srcdir"
fi
ac_msg="sources are in $srcdir, but \`cd $srcdir' does not work"
ac_abs_confdir=`(
	cd "$srcdir" && test -r "./$ac_unique_file" || as_fn_error $? "$ac_msg"
	pwd)`
# When building in place, set srcdir=.
if test "$ac_abs_confdir" = "$ac_pwd"; then
  srcdir=.
fi
# Remove unnecessary trailing slashes from srcdir.
# Double slashes in file names in object file debugging info
# mess up M-x gdb in Emacs.
case $srcdir in
*/) srcdir=`expr "X$srcdir" : 'X\(.*[^/]\)' \| "X$srcdir" : 'X\(.*\)'`;;
esac
for ac_var in $ac_precious_vars; do
  eval ac_env_${ac_var}_set=\${${ac_var}+set}
  eval ac_env_${ac_var}_value=\$${ac_var}
  eval ac_cv_env_${ac_var}_set=\${${ac_var}+set}
  eval ac_cv_env_${ac_var}_value=\$${ac_var}
done

#
# Report the --help message.
#
if test "$ac_init_help" = "long"; then
  # Omit some internal or obsolete options to make the list less imposing.
  # This message is too long to be a string in the A/UX 3.1 sh.
  cat <<_ACEOF
\`configure' configures tcpdump 5.0.0-PRE-GIT to adapt to many kinds of systems.

Usage: $0 [OPTION]... [VAR=VALUE]...

To assign environment variables (e.g., CC, CFLAGS...), specify them as
VAR=VALUE.  See below for descriptions of some of the useful variables.

Defaults for the options are specified in brackets.

Configuration:
  -h, --help              display this help and exit
      --help=short        display options specific to this package
      --help=recursive    display the short help of all the included packages
  -V, --version           display version information and exit
  -q, --quiet, --silent   do not print \`checking ...' messages
      --cache-file=FILE   cache test results in FILE [disabled]
  -C, --config-cache      alias for \`--cache-file=config.cache'
  -n, --no-create         do not create output files
      --srcdir=DIR        find the sources in DIR [configure dir or \`..']

Installation directories:
  --prefix=PREFIX         install architecture-independent files in PREFIX
                          @<:@@S|@ac_default_prefix@:>@
  --exec-prefix=EPREFIX   install architecture-dependent files in EPREFIX
                          @<:@PREFIX@:>@

By default, \`make install' will install all the files in
\`$ac_default_prefix/bin', \`$ac_default_prefix/lib' etc.  You can specify
an installation prefix other than \`$ac_default_prefix' using \`--prefix',
for instance \`--prefix=\$HOME'.

For better control, use the options below.

Fine tuning of the installation directories:
  --bindir=DIR            user executables [EPREFIX/bin]
  --sbindir=DIR           system admin executables [EPREFIX/sbin]
  --libexecdir=DIR        program executables [EPREFIX/libexec]
  --sysconfdir=DIR        read-only single-machine data [PREFIX/etc]
  --sharedstatedir=DIR    modifiable architecture-independent data [PREFIX/com]
  --localstatedir=DIR     modifiable single-machine data [PREFIX/var]
  --runstatedir=DIR       modifiable per-process data [LOCALSTATEDIR/run]
  --libdir=DIR            object code libraries [EPREFIX/lib]
  --includedir=DIR        C header files [PREFIX/include]
  --oldincludedir=DIR     C header files for non-gcc [/usr/include]
  --datarootdir=DIR       read-only arch.-independent data root [PREFIX/share]
  --datadir=DIR           read-only architecture-independent data [DATAROOTDIR]
  --infodir=DIR           info documentation [DATAROOTDIR/info]
  --localedir=DIR         locale-dependent data [DATAROOTDIR/locale]
  --mandir=DIR            man documentation [DATAROOTDIR/man]
  --docdir=DIR            documentation root @<:@DATAROOTDIR/doc/tcpdump@:>@
  --htmldir=DIR           html documentation [DOCDIR]
  --dvidir=DIR            dvi documentation [DOCDIR]
  --pdfdir=DIR            pdf documentation [DOCDIR]
  --psdir=DIR             ps documentation [DOCDIR]
_ACEOF

  cat <<\_ACEOF

System types:
  --build=BUILD     configure for building on BUILD [guessed]
  --host=HOST       cross-compile to build programs to run on HOST [BUILD]
_ACEOF
fi

if test -n "$ac_init_help"; then
  case $ac_init_help in
     short | recursive ) echo "Configuration of tcpdump 5.0.0-PRE-GIT:";;
   esac
  cat <<\_ACEOF

Optional Features:
  --disable-option-checking  ignore unrecognized --enable/--with options
  --disable-FEATURE       do not include FEATURE (same as --enable-FEATURE=no)
  --enable-FEATURE[=ARG]  include FEATURE [ARG=yes]
  --disable-universal     don't build universal on macOS
  --enable-instrument-functions 
                          enable instrument functions code @<:@default=no@:>@
  --enable-smb            enable possibly-buggy SMB printer @<:@default=no@:>@
  --disable-local-libpcap don't look for a local libpcap @<:@default=check for a
                          local libpcap@:>@

Optional Packages:
  --with-PACKAGE[=ARG]    use PACKAGE [ARG=yes]
  --without-PACKAGE       do not use PACKAGE (same as --with-PACKAGE=no)
  --without-gcc           don't use gcc
  --with-smi              link with libsmi (allows to load MIBs on the fly to
                          decode SNMP packets) @<:@default=yes, if available@:>@
  --with-user=USERNAME    drop privileges by default to USERNAME
  --with-chroot=DIRECTORY when dropping privileges, chroot to DIRECTORY
  --with-sandbox-capsicum use Capsicum security functions @<:@default=yes, if
                          available@:>@
  --with-crypto@<:@=DIR@:>@     use OpenSSL/libressl libcrypto (located in directory
                          DIR, if specified) @<:@default=yes, if available@:>@
  --with-cap-ng           use libcap-ng @<:@default=yes, if available@:>@

Some influential environment variables:
  CC          C compiler command
  CFLAGS      C compiler flags
  LDFLAGS     linker flags, e.g. -L<lib dir> if you have libraries in a
              nonstandard directory <lib dir>
  LIBS        libraries to pass to the linker, e.g. -l<library>
  CPPFLAGS    (Objective) C/C++ preprocessor flags, e.g. -I<include dir> if
              you have headers in a nonstandard directory <include dir>
  PKG_CONFIG  path to pkg-config utility
  PKG_CONFIG_PATH 
              directories to add to pkg-config's search path
  PKG_CONFIG_LIBDIR 
              path overriding pkg-config's built-in search path
  LIBPCAP_CFLAGS 
              C compiler flags for libpcap, overriding pkg-config
  LIBPCAP_LIBS 
              linker flags for libpcap, overriding pkg-config
  LIBPCAP_LIBS_STATIC 
              static-link linker flags for libpcap, overriding pkg-config
  LIBCRYPTO_CFLAGS 
              C compiler flags for libcrypto, overriding pkg-config
  LIBCRYPTO_LIBS 
              linker flags for libcrypto, overriding pkg-config
  LIBCRYPTO_LIBS_STATIC 
              static-link linker flags for libcrypto, overriding pkg-config

Use these variables to override the choices made by `configure' or to help
it to find libraries and programs with nonstandard names/locations.

Report bugs to <https://github.com/the-tcpdump-group/tcpdump/issues>.
tcpdump home page: <https://www.tcpdump.org/>.
_ACEOF
ac_status=$?
fi

if test "$ac_init_help" = "recursive"; then
  # If there are subdirs, report their specific --help.
  for ac_dir in : $ac_subdirs_all; do test "x$ac_dir" = x: && continue
    test -d "$ac_dir" ||
      { cd "$srcdir" && ac_pwd=`pwd` && srcdir=. && test -d "$ac_dir"; } ||
      continue
    ac_builddir=.

case "$ac_dir" in
.) ac_dir_suffix= ac_top_builddir_sub=. ac_top_build_prefix= ;;
*)
  ac_dir_suffix=/`printf "%s\n" "$ac_dir" | sed 's|^\.[\\/]||'`
  # A ".." for each directory in $ac_dir_suffix.
  ac_top_builddir_sub=`printf "%s\n" "$ac_dir_suffix" | sed 's|/[^\\/]*|/..|g;s|/||'`
  case $ac_top_builddir_sub in
  "") ac_top_builddir_sub=. ac_top_build_prefix= ;;
  *)  ac_top_build_prefix=$ac_top_builddir_sub/ ;;
  esac ;;
esac
ac_abs_top_builddir=$ac_pwd
ac_abs_builddir=$ac_pwd$ac_dir_suffix
# for backward compatibility:
ac_top_builddir=$ac_top_build_prefix

case $srcdir in
  .)  # We are building in place.
    ac_srcdir=.
    ac_top_srcdir=$ac_top_builddir_sub
    ac_abs_top_srcdir=$ac_pwd ;;
  [\\/]* | ?:[\\/]* )  # Absolute name.
    ac_srcdir=$srcdir$ac_dir_suffix;
    ac_top_srcdir=$srcdir
    ac_abs_top_srcdir=$srcdir ;;
  *) # Relative name.
    ac_srcdir=$ac_top_build_prefix$srcdir$ac_dir_suffix
    ac_top_srcdir=$ac_top_build_prefix$srcdir
    ac_abs_top_srcdir=$ac_pwd/$srcdir ;;
esac
ac_abs_srcdir=$ac_abs_top_srcdir$ac_dir_suffix

    cd "$ac_dir" || { ac_status=$?; continue; }
    # Check for configure.gnu first; this name is used for a wrapper for
    # Metaconfig's "Configure" on case-insensitive file systems.
    if test -f "$ac_srcdir/configure.gnu"; then
      echo &&
      $SHELL "$ac_srcdir/configure.gnu" --help=recursive
    elif test -f "$ac_srcdir/configure"; then
      echo &&
      $SHELL "$ac_srcdir/configure" --help=recursive
    else
      printf "%s\n" "$as_me: WARNING: no configuration information is in $ac_dir" >&2
    fi || ac_status=$?
    cd "$ac_pwd" || { ac_status=$?; break; }
  done
fi

test -n "$ac_init_help" && exit $ac_status
if $ac_init_version; then
  cat <<\_ACEOF
tcpdump configure 5.0.0-PRE-GIT
generated by GNU Autoconf 2.71

Copyright (C) 2021 Free Software Foundation, Inc.
This configure script is free software; the Free Software Foundation
gives unlimited permission to copy, distribute and modify it.
_ACEOF
  exit
fi

## ------------------------ ##
## Autoconf initialization. ##
## ------------------------ ##

@%:@ ac_fn_c_try_compile LINENO
@%:@ --------------------------
@%:@ Try to compile conftest.@S|@ac_ext, and return whether this succeeded.
ac_fn_c_try_compile ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  rm -f conftest.$ac_objext conftest.beam
  if { { ac_try="$ac_compile"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_compile") 2>conftest.err
  ac_status=$?
  if test -s conftest.err; then
    grep -v '^ *+' conftest.err >conftest.er1
    cat conftest.er1 >&5
    mv -f conftest.er1 conftest.err
  fi
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; } && {
	 test -z "$ac_c_werror_flag" ||
	 test ! -s conftest.err
       } && test -s conftest.$ac_objext
then :
  ac_retval=0
else $as_nop
  printf "%s\n" "$as_me: failed program was:" >&5
sed 's/^/| /' conftest.$ac_ext >&5

	ac_retval=1
fi
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno
  as_fn_set_status $ac_retval

} @%:@ ac_fn_c_try_compile

@%:@ ac_fn_c_check_header_compile LINENO HEADER VAR INCLUDES
@%:@ -------------------------------------------------------
@%:@ Tests whether HEADER exists and can be compiled using the include files in
@%:@ INCLUDES, setting the cache variable VAR accordingly.
ac_fn_c_check_header_compile ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $2" >&5
printf %s "checking for $2... " >&6; }
if eval test \${$3+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
@%:@include <$2>
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  eval "$3=yes"
else $as_nop
  eval "$3=no"
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
eval ac_res=\$$3
	       { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_res" >&5
printf "%s\n" "$ac_res" >&6; }
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno

} @%:@ ac_fn_c_check_header_compile

@%:@ ac_fn_c_try_run LINENO
@%:@ ----------------------
@%:@ Try to run conftest.@S|@ac_ext, and return whether this succeeded. Assumes that
@%:@ executables *can* be run.
ac_fn_c_try_run ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  if { { ac_try="$ac_link"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_link") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; } && { ac_try='./conftest$ac_exeext'
  { { case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_try") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }; }
then :
  ac_retval=0
else $as_nop
  printf "%s\n" "$as_me: program exited with status $ac_status" >&5
       printf "%s\n" "$as_me: failed program was:" >&5
sed 's/^/| /' conftest.$ac_ext >&5

       ac_retval=$ac_status
fi
  rm -rf conftest.dSYM conftest_ipa8_conftest.oo
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno
  as_fn_set_status $ac_retval

} @%:@ ac_fn_c_try_run

@%:@ ac_fn_c_compute_int LINENO EXPR VAR INCLUDES
@%:@ --------------------------------------------
@%:@ Tries to find the compile-time value of EXPR in a program that includes
@%:@ INCLUDES, setting VAR accordingly. Returns whether the value could be
@%:@ computed
ac_fn_c_compute_int ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  if test "$cross_compiling" = yes; then
    # Depending upon the size, compute the lo and hi bounds.
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
static int test_array @<:@1 - 2 * !(($2) >= 0)@:>@;
test_array @<:@0@:>@ = 0;
return test_array @<:@0@:>@;

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_lo=0 ac_mid=0
  while :; do
    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
static int test_array @<:@1 - 2 * !(($2) <= $ac_mid)@:>@;
test_array @<:@0@:>@ = 0;
return test_array @<:@0@:>@;

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_hi=$ac_mid; break
else $as_nop
  as_fn_arith $ac_mid + 1 && ac_lo=$as_val
			if test $ac_lo -le $ac_mid; then
			  ac_lo= ac_hi=
			  break
			fi
			as_fn_arith 2 '*' $ac_mid + 1 && ac_mid=$as_val
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
  done
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
static int test_array @<:@1 - 2 * !(($2) < 0)@:>@;
test_array @<:@0@:>@ = 0;
return test_array @<:@0@:>@;

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_hi=-1 ac_mid=-1
  while :; do
    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
static int test_array @<:@1 - 2 * !(($2) >= $ac_mid)@:>@;
test_array @<:@0@:>@ = 0;
return test_array @<:@0@:>@;

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_lo=$ac_mid; break
else $as_nop
  as_fn_arith '(' $ac_mid ')' - 1 && ac_hi=$as_val
			if test $ac_mid -le $ac_hi; then
			  ac_lo= ac_hi=
			  break
			fi
			as_fn_arith 2 '*' $ac_mid && ac_mid=$as_val
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
  done
else $as_nop
  ac_lo= ac_hi=
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
# Binary search between lo and hi bounds.
while test "x$ac_lo" != "x$ac_hi"; do
  as_fn_arith '(' $ac_hi - $ac_lo ')' / 2 + $ac_lo && ac_mid=$as_val
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
static int test_array @<:@1 - 2 * !(($2) <= $ac_mid)@:>@;
test_array @<:@0@:>@ = 0;
return test_array @<:@0@:>@;

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_hi=$ac_mid
else $as_nop
  as_fn_arith '(' $ac_mid ')' + 1 && ac_lo=$as_val
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
done
case $ac_lo in @%:@((
?*) eval "$3=\$ac_lo"; ac_retval=0 ;;
'') ac_retval=1 ;;
esac
  else
    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
static long int longval (void) { return $2; }
static unsigned long int ulongval (void) { return $2; }
@%:@include <stdio.h>
@%:@include <stdlib.h>
int
main (void)
{

  FILE *f = fopen ("conftest.val", "w");
  if (! f)
    return 1;
  if (($2) < 0)
    {
      long int i = longval ();
      if (i != ($2))
	return 1;
      fprintf (f, "%ld", i);
    }
  else
    {
      unsigned long int i = ulongval ();
      if (i != ($2))
	return 1;
      fprintf (f, "%lu", i);
    }
  /* Do not output a trailing newline, as this causes \r\n confusion
     on some platforms.  */
  return ferror (f) || fclose (f) != 0;

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_run "$LINENO"
then :
  echo >>conftest.val; read $3 <conftest.val; ac_retval=0
else $as_nop
  ac_retval=1
fi
rm -f core *.core core.conftest.* gmon.out bb.out conftest$ac_exeext \
  conftest.$ac_objext conftest.beam conftest.$ac_ext
rm -f conftest.val

  fi
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno
  as_fn_set_status $ac_retval

} @%:@ ac_fn_c_compute_int

@%:@ ac_fn_c_try_link LINENO
@%:@ -----------------------
@%:@ Try to link conftest.@S|@ac_ext, and return whether this succeeded.
ac_fn_c_try_link ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  rm -f conftest.$ac_objext conftest.beam conftest$ac_exeext
  if { { ac_try="$ac_link"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_link") 2>conftest.err
  ac_status=$?
  if test -s conftest.err; then
    grep -v '^ *+' conftest.err >conftest.er1
    cat conftest.er1 >&5
    mv -f conftest.er1 conftest.err
  fi
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; } && {
	 test -z "$ac_c_werror_flag" ||
	 test ! -s conftest.err
       } && test -s conftest$ac_exeext && {
	 test "$cross_compiling" = yes ||
	 test -x conftest$ac_exeext
       }
then :
  ac_retval=0
else $as_nop
  printf "%s\n" "$as_me: failed program was:" >&5
sed 's/^/| /' conftest.$ac_ext >&5

	ac_retval=1
fi
  # Delete the IPA/IPO (Inter Procedural Analysis/Optimization) information
  # created by the PGI compiler (conftest_ipa8_conftest.oo), as it would
  # interfere with the next link command; also delete a directory that is
  # left behind by Apple's compiler.  We do this before executing the actions.
  rm -rf conftest.dSYM conftest_ipa8_conftest.oo
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno
  as_fn_set_status $ac_retval

} @%:@ ac_fn_c_try_link

@%:@ ac_fn_c_check_func LINENO FUNC VAR
@%:@ ----------------------------------
@%:@ Tests whether FUNC exists, setting the cache variable VAR accordingly
ac_fn_c_check_func ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $2" >&5
printf %s "checking for $2... " >&6; }
if eval test \${$3+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
/* Define $2 to an innocuous variant, in case <limits.h> declares $2.
   For example, HP-UX 11i <limits.h> declares gettimeofday.  */
#define $2 innocuous_$2

/* System header to define __stub macros and hopefully few prototypes,
   which can conflict with char $2 (); below.  */

#include <limits.h>
#undef $2

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
#ifdef __cplusplus
extern "C"
#endif
char $2 ();
/* The GNU C library defines this for functions which it implements
    to always fail with ENOSYS.  Some functions are actually named
    something starting with __ and the normal name is an alias.  */
#if defined __stub_$2 || defined __stub___$2
choke me
#endif

int
main (void)
{
return $2 ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  eval "$3=yes"
else $as_nop
  eval "$3=no"
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
fi
eval ac_res=\$$3
	       { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_res" >&5
printf "%s\n" "$ac_res" >&6; }
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno

} @%:@ ac_fn_c_check_func

@%:@ ac_fn_check_decl LINENO SYMBOL VAR INCLUDES EXTRA-OPTIONS FLAG-VAR
@%:@ ------------------------------------------------------------------
@%:@ Tests whether SYMBOL is declared in INCLUDES, setting cache variable VAR
@%:@ accordingly. Pass EXTRA-OPTIONS to the compiler, using FLAG-VAR.
ac_fn_check_decl ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  as_decl_name=`echo $2|sed 's/ *(.*//'`
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether $as_decl_name is declared" >&5
printf %s "checking whether $as_decl_name is declared... " >&6; }
if eval test \${$3+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  as_decl_use=`echo $2|sed -e 's/(/((/' -e 's/)/) 0&/' -e 's/,/) 0& (/g'`
  eval ac_save_FLAGS=\$$6
  as_fn_append $6 " $5"
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
#ifndef $as_decl_name
#ifdef __cplusplus
  (void) $as_decl_use;
#else
  (void) $as_decl_name;
#endif
#endif

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  eval "$3=yes"
else $as_nop
  eval "$3=no"
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
  eval $6=\$ac_save_FLAGS

fi
eval ac_res=\$$3
	       { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_res" >&5
printf "%s\n" "$ac_res" >&6; }
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno

} @%:@ ac_fn_check_decl

@%:@ ac_fn_c_check_type LINENO TYPE VAR INCLUDES
@%:@ -------------------------------------------
@%:@ Tests whether TYPE exists after having included INCLUDES, setting cache
@%:@ variable VAR accordingly.
ac_fn_c_check_type ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $2" >&5
printf %s "checking for $2... " >&6; }
if eval test \${$3+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  eval "$3=no"
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
if (sizeof ($2))
	 return 0;
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
if (sizeof (($2)))
	    return 0;
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
else $as_nop
  eval "$3=yes"
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
eval ac_res=\$$3
	       { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_res" >&5
printf "%s\n" "$ac_res" >&6; }
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno

} @%:@ ac_fn_c_check_type
ac_configure_args_raw=
for ac_arg
do
  case $ac_arg in
  *\'*)
    ac_arg=`printf "%s\n" "$ac_arg" | sed "s/'/'\\\\\\\\''/g"` ;;
  esac
  as_fn_append ac_configure_args_raw " '$ac_arg'"
done

case $ac_configure_args_raw in
  *$as_nl*)
    ac_safe_unquote= ;;
  *)
    ac_unsafe_z='|&;<>()$`\\"*?@<:@ ''	' # This string ends in space, tab.
    ac_unsafe_a="$ac_unsafe_z#~"
    ac_safe_unquote="s/ '\\([^$ac_unsafe_a][^$ac_unsafe_z]*\\)'/ \\1/g"
    ac_configure_args_raw=`      printf "%s\n" "$ac_configure_args_raw" | sed "$ac_safe_unquote"`;;
esac

cat >config.log <<_ACEOF
This file contains any messages produced by compilers while
running configure, to aid debugging if configure makes a mistake.

It was created by tcpdump $as_me 5.0.0-PRE-GIT, which was
generated by GNU Autoconf 2.71.  Invocation command line was

  $ $0$ac_configure_args_raw

_ACEOF
exec 5>>config.log
{
cat <<_ASUNAME
## --------- ##
## Platform. ##
## --------- ##

hostname = `(hostname || uname -n) 2>/dev/null | sed 1q`
uname -m = `(uname -m) 2>/dev/null || echo unknown`
uname -r = `(uname -r) 2>/dev/null || echo unknown`
uname -s = `(uname -s) 2>/dev/null || echo unknown`
uname -v = `(uname -v) 2>/dev/null || echo unknown`

/usr/bin/uname -p = `(/usr/bin/uname -p) 2>/dev/null || echo unknown`
/bin/uname -X     = `(/bin/uname -X) 2>/dev/null     || echo unknown`

/bin/arch              = `(/bin/arch) 2>/dev/null              || echo unknown`
/usr/bin/arch -k       = `(/usr/bin/arch -k) 2>/dev/null       || echo unknown`
/usr/convex/getsysinfo = `(/usr/convex/getsysinfo) 2>/dev/null || echo unknown`
/usr/bin/hostinfo      = `(/usr/bin/hostinfo) 2>/dev/null      || echo unknown`
/bin/machine           = `(/bin/machine) 2>/dev/null           || echo unknown`
/usr/bin/oslevel       = `(/usr/bin/oslevel) 2>/dev/null       || echo unknown`
/bin/universe          = `(/bin/universe) 2>/dev/null          || echo unknown`

_ASUNAME

as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    printf "%s\n" "PATH: $as_dir"
  done
IFS=$as_save_IFS

} >&5

cat >&5 <<_ACEOF


## ----------- ##
## Core tests. ##
## ----------- ##

_ACEOF


# Keep a trace of the command line.
# Strip out --no-create and --no-recursion so they do not pile up.
# Strip out --silent because we don't want to record it for future runs.
# Also quote any args containing shell meta-characters.
# Make two passes to allow for proper duplicate-argument suppression.
ac_configure_args=
ac_configure_args0=
ac_configure_args1=
ac_must_keep_next=false
for ac_pass in 1 2
do
  for ac_arg
  do
    case $ac_arg in
    -no-create | --no-c* | -n | -no-recursion | --no-r*) continue ;;
    -q | -quiet | --quiet | --quie | --qui | --qu | --q \
    | -silent | --silent | --silen | --sile | --sil)
      continue ;;
    *\'*)
      ac_arg=`printf "%s\n" "$ac_arg" | sed "s/'/'\\\\\\\\''/g"` ;;
    esac
    case $ac_pass in
    1) as_fn_append ac_configure_args0 " '$ac_arg'" ;;
    2)
      as_fn_append ac_configure_args1 " '$ac_arg'"
      if test $ac_must_keep_next = true; then
	ac_must_keep_next=false # Got value, back to normal.
      else
	case $ac_arg in
	  *=* | --config-cache | -C | -disable-* | --disable-* \
	  | -enable-* | --enable-* | -gas | --g* | -nfp | --nf* \
	  | -q | -quiet | --q* | -silent | --sil* | -v | -verb* \
	  | -with-* | --with-* | -without-* | --without-* | --x)
	    case "$ac_configure_args0 " in
	      "$ac_configure_args1"*" '$ac_arg' "* ) continue ;;
	    esac
	    ;;
	  -* ) ac_must_keep_next=true ;;
	esac
      fi
      as_fn_append ac_configure_args " '$ac_arg'"
      ;;
    esac
  done
done
{ ac_configure_args0=; unset ac_configure_args0;}
{ ac_configure_args1=; unset ac_configure_args1;}

# When interrupted or exit'd, cleanup temporary files, and complete
# config.log.  We remove comments because anyway the quotes in there
# would cause problems or look ugly.
# WARNING: Use '\'' to represent an apostrophe within the trap.
# WARNING: Do not start the trap code with a newline, due to a FreeBSD 4.0 bug.
trap 'exit_status=$?
  # Sanitize IFS.
  IFS=" ""	$as_nl"
  # Save into config.log some information that might help in debugging.
  {
    echo

    printf "%s\n" "## ---------------- ##
## Cache variables. ##
## ---------------- ##"
    echo
    # The following way of writing the cache mishandles newlines in values,
(
  for ac_var in `(set) 2>&1 | sed -n '\''s/^\([a-zA-Z_][a-zA-Z0-9_]*\)=.*/\1/p'\''`; do
    eval ac_val=\$$ac_var
    case $ac_val in #(
    *${as_nl}*)
      case $ac_var in #(
      *_cv_*) { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: cache variable $ac_var contains a newline" >&5
printf "%s\n" "$as_me: WARNING: cache variable $ac_var contains a newline" >&2;} ;;
      esac
      case $ac_var in #(
      _ | IFS | as_nl) ;; #(
      BASH_ARGV | BASH_SOURCE) eval $ac_var= ;; #(
      *) { eval $ac_var=; unset $ac_var;} ;;
      esac ;;
    esac
  done
  (set) 2>&1 |
    case $as_nl`(ac_space='\'' '\''; set) 2>&1` in #(
    *${as_nl}ac_space=\ *)
      sed -n \
	"s/'\''/'\''\\\\'\'''\''/g;
	  s/^\\([_$as_cr_alnum]*_cv_[_$as_cr_alnum]*\\)=\\(.*\\)/\\1='\''\\2'\''/p"
      ;; #(
    *)
      sed -n "/^[_$as_cr_alnum]*_cv_[_$as_cr_alnum]*=/p"
      ;;
    esac |
    sort
)
    echo

    printf "%s\n" "## ----------------- ##
## Output variables. ##
## ----------------- ##"
    echo
    for ac_var in $ac_subst_vars
    do
      eval ac_val=\$$ac_var
      case $ac_val in
      *\'\''*) ac_val=`printf "%s\n" "$ac_val" | sed "s/'\''/'\''\\\\\\\\'\'''\''/g"`;;
      esac
      printf "%s\n" "$ac_var='\''$ac_val'\''"
    done | sort
    echo

    if test -n "$ac_subst_files"; then
      printf "%s\n" "## ------------------- ##
## File substitutions. ##
## ------------------- ##"
      echo
      for ac_var in $ac_subst_files
      do
	eval ac_val=\$$ac_var
	case $ac_val in
	*\'\''*) ac_val=`printf "%s\n" "$ac_val" | sed "s/'\''/'\''\\\\\\\\'\'''\''/g"`;;
	esac
	printf "%s\n" "$ac_var='\''$ac_val'\''"
      done | sort
      echo
    fi

    if test -s confdefs.h; then
      printf "%s\n" "## ----------- ##
## confdefs.h. ##
## ----------- ##"
      echo
      cat confdefs.h
      echo
    fi
    test "$ac_signal" != 0 &&
      printf "%s\n" "$as_me: caught signal $ac_signal"
    printf "%s\n" "$as_me: exit $exit_status"
  } >&5
  rm -f core *.core core.conftest.* &&
    rm -f -r conftest* confdefs* conf$$* $ac_clean_files &&
    exit $exit_status
' 0
for ac_signal in 1 2 13 15; do
  trap 'ac_signal='$ac_signal'; as_fn_exit 1' $ac_signal
done
ac_signal=0

# confdefs.h avoids OS command line length limits that DEFS can exceed.
rm -f -r conftest* confdefs.h

printf "%s\n" "/* confdefs.h */" > confdefs.h

# Predefined preprocessor variables.

printf "%s\n" "@%:@define PACKAGE_NAME \"$PACKAGE_NAME\"" >>confdefs.h

printf "%s\n" "@%:@define PACKAGE_TARNAME \"$PACKAGE_TARNAME\"" >>confdefs.h

printf "%s\n" "@%:@define PACKAGE_VERSION \"$PACKAGE_VERSION\"" >>confdefs.h

printf "%s\n" "@%:@define PACKAGE_STRING \"$PACKAGE_STRING\"" >>confdefs.h

printf "%s\n" "@%:@define PACKAGE_BUGREPORT \"$PACKAGE_BUGREPORT\"" >>confdefs.h

printf "%s\n" "@%:@define PACKAGE_URL \"$PACKAGE_URL\"" >>confdefs.h


# Let the site file select an alternate cache file if it wants to.
# Prefer an explicitly selected file to automatically selected ones.
if test -n "$CONFIG_SITE"; then
  ac_site_files="$CONFIG_SITE"
elif test "x$prefix" != xNONE; then
  ac_site_files="$prefix/share/config.site $prefix/etc/config.site"
else
  ac_site_files="$ac_default_prefix/share/config.site $ac_default_prefix/etc/config.site"
fi

for ac_site_file in $ac_site_files
do
  case $ac_site_file in @%:@(
  */*) :
     ;; @%:@(
  *) :
    ac_site_file=./$ac_site_file ;;
esac
  if test -f "$ac_site_file" && test -r "$ac_site_file"; then
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: loading site script $ac_site_file" >&5
printf "%s\n" "$as_me: loading site script $ac_site_file" >&6;}
    sed 's/^/| /' "$ac_site_file" >&5
    . "$ac_site_file" \
      || { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error $? "failed to load site script $ac_site_file
See \`config.log' for more details" "$LINENO" 5; }
  fi
done

if test -r "$cache_file"; then
  # Some versions of bash will fail to source /dev/null (special files
  # actually), so we avoid doing that.  DJGPP emulates it as a regular file.
  if test /dev/null != "$cache_file" && test -f "$cache_file"; then
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: loading cache $cache_file" >&5
printf "%s\n" "$as_me: loading cache $cache_file" >&6;}
    case $cache_file in
      [\\/]* | ?:[\\/]* ) . "$cache_file";;
      *)                      . "./$cache_file";;
    esac
  fi
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: creating cache $cache_file" >&5
printf "%s\n" "$as_me: creating cache $cache_file" >&6;}
  >$cache_file
fi

# Test code for whether the C compiler supports C89 (global declarations)
ac_c_conftest_c89_globals='
/* Does the compiler advertise C89 conformance?
   Do not test the value of __STDC__, because some compilers set it to 0
   while being otherwise adequately conformant. */
#if !defined __STDC__
# error "Compiler does not advertise C89 conformance"
#endif

#include <stddef.h>
#include <stdarg.h>
struct stat;
/* Most of the following tests are stolen from RCS 5.7 src/conf.sh.  */
struct buf { int x; };
struct buf * (*rcsopen) (struct buf *, struct stat *, int);
static char *e (p, i)
     char **p;
     int i;
{
  return p[i];
}
static char *f (char * (*g) (char **, int), char **p, ...)
{
  char *s;
  va_list v;
  va_start (v,p);
  s = g (p, va_arg (v,int));
  va_end (v);
  return s;
}

/* OSF 4.0 Compaq cc is some sort of almost-ANSI by default.  It has
   function prototypes and stuff, but not \xHH hex character constants.
   These do not provoke an error unfortunately, instead are silently treated
   as an "x".  The following induces an error, until -std is added to get
   proper ANSI mode.  Curiously \x00 != x always comes out true, for an
   array size at least.  It is necessary to write \x00 == 0 to get something
   that is true only with -std.  */
int osf4_cc_array ['\''\x00'\'' == 0 ? 1 : -1];

/* IBM C 6 for AIX is almost-ANSI by default, but it replaces macro parameters
   inside strings and character constants.  */
#define FOO(x) '\''x'\''
int xlc6_cc_array[FOO(a) == '\''x'\'' ? 1 : -1];

int test (int i, double x);
struct s1 {int (*f) (int a);};
struct s2 {int (*f) (double a);};
int pairnames (int, char **, int *(*)(struct buf *, struct stat *, int),
               int, int);'

# Test code for whether the C compiler supports C89 (body of main).
ac_c_conftest_c89_main='
ok |= (argc == 0 || f (e, argv, 0) != argv[0] || f (e, argv, 1) != argv[1]);
'

# Test code for whether the C compiler supports C99 (global declarations)
ac_c_conftest_c99_globals='
// Does the compiler advertise C99 conformance?
#if !defined __STDC_VERSION__ || __STDC_VERSION__ < 199901L
# error "Compiler does not advertise C99 conformance"
#endif

#include <stdbool.h>
extern int puts (const char *);
extern int printf (const char *, ...);
extern int dprintf (int, const char *, ...);
extern void *malloc (size_t);

// Check varargs macros.  These examples are taken from C99 6.10.3.5.
// dprintf is used instead of fprintf to avoid needing to declare
// FILE and stderr.
#define debug(...) dprintf (2, __VA_ARGS__)
#define showlist(...) puts (#__VA_ARGS__)
#define report(test,...) ((test) ? puts (#test) : printf (__VA_ARGS__))
static void
test_varargs_macros (void)
{
  int x = 1234;
  int y = 5678;
  debug ("Flag");
  debug ("X = %d\n", x);
  showlist (The first, second, and third items.);
  report (x>y, "x is %d but y is %d", x, y);
}

// Check long long types.
#define BIG64 18446744073709551615ull
#define BIG32 4294967295ul
#define BIG_OK (BIG64 / BIG32 == 4294967297ull && BIG64 % BIG32 == 0)
#if !BIG_OK
  #error "your preprocessor is broken"
#endif
#if BIG_OK
#else
  #error "your preprocessor is broken"
#endif
static long long int bignum = -9223372036854775807LL;
static unsigned long long int ubignum = BIG64;

struct incomplete_array
{
  int datasize;
  double data[];
};

struct named_init {
  int number;
  const wchar_t *name;
  double average;
};

typedef const char *ccp;

static inline int
test_restrict (ccp restrict text)
{
  // See if C++-style comments work.
  // Iterate through items via the restricted pointer.
  // Also check for declarations in for loops.
  for (unsigned int i = 0; *(text+i) != '\''\0'\''; ++i)
    continue;
  return 0;
}

// Check varargs and va_copy.
static bool
test_varargs (const char *format, ...)
{
  va_list args;
  va_start (args, format);
  va_list args_copy;
  va_copy (args_copy, args);

  const char *str = "";
  int number = 0;
  float fnumber = 0;

  while (*format)
    {
      switch (*format++)
	{
	case '\''s'\'': // string
	  str = va_arg (args_copy, const char *);
	  break;
	case '\''d'\'': // int
	  number = va_arg (args_copy, int);
	  break;
	case '\''f'\'': // float
	  fnumber = va_arg (args_copy, double);
	  break;
	default:
	  break;
	}
    }
  va_end (args_copy);
  va_end (args);

  return *str && number && fnumber;
}
'

# Test code for whether the C compiler supports C99 (body of main).
ac_c_conftest_c99_main='
  // Check bool.
  _Bool success = false;
  success |= (argc != 0);

  // Check restrict.
  if (test_restrict ("String literal") == 0)
    success = true;
  char *restrict newvar = "Another string";

  // Check varargs.
  success &= test_varargs ("s, d'\'' f .", "string", 65, 34.234);
  test_varargs_macros ();

  // Check flexible array members.
  struct incomplete_array *ia =
    malloc (sizeof (struct incomplete_array) + (sizeof (double) * 10));
  ia->datasize = 10;
  for (int i = 0; i < ia->datasize; ++i)
    ia->data[i] = i * 1.234;

  // Check named initializers.
  struct named_init ni = {
    .number = 34,
    .name = L"Test wide string",
    .average = 543.34343,
  };

  ni.number = 58;

  int dynamic_array[ni.number];
  dynamic_array[0] = argv[0][0];
  dynamic_array[ni.number - 1] = 543;

  // work around unused variable warnings
  ok |= (!success || bignum == 0LL || ubignum == 0uLL || newvar[0] == '\''x'\''
	 || dynamic_array[ni.number - 1] != 543);
'

# Test code for whether the C compiler supports C11 (global declarations)
ac_c_conftest_c11_globals='
// Does the compiler advertise C11 conformance?
#if !defined __STDC_VERSION__ || __STDC_VERSION__ < 201112L
# error "Compiler does not advertise C11 conformance"
#endif

// Check _Alignas.
char _Alignas (double) aligned_as_double;
char _Alignas (0) no_special_alignment;
extern char aligned_as_int;
char _Alignas (0) _Alignas (int) aligned_as_int;

// Check _Alignof.
enum
{
  int_alignment = _Alignof (int),
  int_array_alignment = _Alignof (int[100]),
  char_alignment = _Alignof (char)
};
_Static_assert (0 < -_Alignof (int), "_Alignof is signed");

// Check _Noreturn.
int _Noreturn does_not_return (void) { for (;;) continue; }

// Check _Static_assert.
struct test_static_assert
{
  int x;
  _Static_assert (sizeof (int) <= sizeof (long int),
                  "_Static_assert does not work in struct");
  long int y;
};

// Check UTF-8 literals.
#define u8 syntax error!
char const utf8_literal[] = u8"happens to be ASCII" "another string";

// Check duplicate typedefs.
typedef long *long_ptr;
typedef long int *long_ptr;
typedef long_ptr long_ptr;

// Anonymous structures and unions -- taken from C11 6.7.2.1 Example 1.
struct anonymous
{
  union {
    struct { int i; int j; };
    struct { int k; long int l; } w;
  };
  int m;
} v1;
'

# Test code for whether the C compiler supports C11 (body of main).
ac_c_conftest_c11_main='
  _Static_assert ((offsetof (struct anonymous, i)
		   == offsetof (struct anonymous, w.k)),
		  "Anonymous union alignment botch");
  v1.i = 2;
  v1.w.k = 5;
  ok |= v1.i != 5;
'

# Test code for whether the C compiler supports C11 (complete).
ac_c_conftest_c11_program="${ac_c_conftest_c89_globals}
${ac_c_conftest_c99_globals}
${ac_c_conftest_c11_globals}

int
main (int argc, char **argv)
{
  int ok = 0;
  ${ac_c_conftest_c89_main}
  ${ac_c_conftest_c99_main}
  ${ac_c_conftest_c11_main}
  return ok;
}
"

# Test code for whether the C compiler supports C99 (complete).
ac_c_conftest_c99_program="${ac_c_conftest_c89_globals}
${ac_c_conftest_c99_globals}

int
main (int argc, char **argv)
{
  int ok = 0;
  ${ac_c_conftest_c89_main}
  ${ac_c_conftest_c99_main}
  return ok;
}
"

# Test code for whether the C compiler supports C89 (complete).
ac_c_conftest_c89_program="${ac_c_conftest_c89_globals}

int
main (int argc, char **argv)
{
  int ok = 0;
  ${ac_c_conftest_c89_main}
  return ok;
}
"

as_fn_append ac_header_c_list " stdio.h stdio_h HAVE_STDIO_H"
as_fn_append ac_header_c_list " stdlib.h stdlib_h HAVE_STDLIB_H"
as_fn_append ac_header_c_list " string.h string_h HAVE_STRING_H"
as_fn_append ac_header_c_list " inttypes.h inttypes_h HAVE_INTTYPES_H"
as_fn_append ac_header_c_list " stdint.h stdint_h HAVE_STDINT_H"
as_fn_append ac_header_c_list " strings.h strings_h HAVE_STRINGS_H"
as_fn_append ac_header_c_list " sys/stat.h sys_stat_h HAVE_SYS_STAT_H"
as_fn_append ac_header_c_list " sys/types.h sys_types_h HAVE_SYS_TYPES_H"
as_fn_append ac_header_c_list " unistd.h unistd_h HAVE_UNISTD_H"

# Auxiliary files required by this configure script.
ac_aux_files="install-sh config.guess config.sub"

# Locations in which to look for auxiliary files.
ac_aux_dir_candidates="${srcdir}${PATH_SEPARATOR}${srcdir}/..${PATH_SEPARATOR}${srcdir}/../.."

# Search for a directory containing all of the required auxiliary files,
# $ac_aux_files, from the $PATH-style list $ac_aux_dir_candidates.
# If we don't find one directory that contains all the files we need,
# we report the set of missing files from the *first* directory in
# $ac_aux_dir_candidates and give up.
ac_missing_aux_files=""
ac_first_candidate=:
printf "%s\n" "$as_me:${as_lineno-$LINENO}: looking for aux files: $ac_aux_files" >&5
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
as_found=false
for as_dir in $ac_aux_dir_candidates
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
  as_found=:
  
  printf "%s\n" "$as_me:${as_lineno-$LINENO}:  trying $as_dir" >&5
  ac_aux_dir_found=yes
  ac_install_sh=
  for ac_aux in $ac_aux_files
  do
    # As a special case, if "install-sh" is required, that requirement
    # can be satisfied by any of "install-sh", "install.sh", or "shtool",
    # and $ac_install_sh is set appropriately for whichever one is found.
    if test x"$ac_aux" = x"install-sh"
    then
      if test -f "${as_dir}install-sh"; then
        printf "%s\n" "$as_me:${as_lineno-$LINENO}:   ${as_dir}install-sh found" >&5
        ac_install_sh="${as_dir}install-sh -c"
      elif test -f "${as_dir}install.sh"; then
        printf "%s\n" "$as_me:${as_lineno-$LINENO}:   ${as_dir}install.sh found" >&5
        ac_install_sh="${as_dir}install.sh -c"
      elif test -f "${as_dir}shtool"; then
        printf "%s\n" "$as_me:${as_lineno-$LINENO}:   ${as_dir}shtool found" >&5
        ac_install_sh="${as_dir}shtool install -c"
      else
        ac_aux_dir_found=no
        if $ac_first_candidate; then
          ac_missing_aux_files="${ac_missing_aux_files} install-sh"
        else
          break
        fi
      fi
    else
      if test -f "${as_dir}${ac_aux}"; then
        printf "%s\n" "$as_me:${as_lineno-$LINENO}:   ${as_dir}${ac_aux} found" >&5
      else
        ac_aux_dir_found=no
        if $ac_first_candidate; then
          ac_missing_aux_files="${ac_missing_aux_files} ${ac_aux}"
        else
          break
        fi
      fi
    fi
  done
  if test "$ac_aux_dir_found" = yes; then
    ac_aux_dir="$as_dir"
    break
  fi
  ac_first_candidate=false

  as_found=false
done
IFS=$as_save_IFS
if $as_found
then :
  
else $as_nop
  as_fn_error $? "cannot find required auxiliary files:$ac_missing_aux_files" "$LINENO" 5
fi


# These three variables are undocumented and unsupported,
# and are intended to be withdrawn in a future Autoconf release.
# They can cause serious problems if a builder's source tree is in a directory
# whose full name contains unusual characters.
if test -f "${ac_aux_dir}config.guess"; then
  ac_@&t@config_guess="$SHELL ${ac_aux_dir}config.guess"
fi
if test -f "${ac_aux_dir}config.sub"; then
  ac_@&t@config_sub="$SHELL ${ac_aux_dir}config.sub"
fi
if test -f "$ac_aux_dir/configure"; then
  ac_@&t@configure="$SHELL ${ac_aux_dir}configure"
fi

# Check that the precious variables saved in the cache have kept the same
# value.
ac_cache_corrupted=false
for ac_var in $ac_precious_vars; do
  eval ac_old_set=\$ac_cv_env_${ac_var}_set
  eval ac_new_set=\$ac_env_${ac_var}_set
  eval ac_old_val=\$ac_cv_env_${ac_var}_value
  eval ac_new_val=\$ac_env_${ac_var}_value
  case $ac_old_set,$ac_new_set in
    set,)
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: \`$ac_var' was set to \`$ac_old_val' in the previous run" >&5
printf "%s\n" "$as_me: error: \`$ac_var' was set to \`$ac_old_val' in the previous run" >&2;}
      ac_cache_corrupted=: ;;
    ,set)
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: \`$ac_var' was not set in the previous run" >&5
printf "%s\n" "$as_me: error: \`$ac_var' was not set in the previous run" >&2;}
      ac_cache_corrupted=: ;;
    ,);;
    *)
      if test "x$ac_old_val" != "x$ac_new_val"; then
	# differences in whitespace do not lead to failure.
	ac_old_val_w=`echo x $ac_old_val`
	ac_new_val_w=`echo x $ac_new_val`
	if test "$ac_old_val_w" != "$ac_new_val_w"; then
	  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: \`$ac_var' has changed since the previous run:" >&5
printf "%s\n" "$as_me: error: \`$ac_var' has changed since the previous run:" >&2;}
	  ac_cache_corrupted=:
	else
	  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: warning: ignoring whitespace changes in \`$ac_var' since the previous run:" >&5
printf "%s\n" "$as_me: warning: ignoring whitespace changes in \`$ac_var' since the previous run:" >&2;}
	  eval $ac_var=\$ac_old_val
	fi
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}:   former value:  \`$ac_old_val'" >&5
printf "%s\n" "$as_me:   former value:  \`$ac_old_val'" >&2;}
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}:   current value: \`$ac_new_val'" >&5
printf "%s\n" "$as_me:   current value: \`$ac_new_val'" >&2;}
      fi;;
  esac
  # Pass precious variables to config.status.
  if test "$ac_new_set" = set; then
    case $ac_new_val in
    *\'*) ac_arg=$ac_var=`printf "%s\n" "$ac_new_val" | sed "s/'/'\\\\\\\\''/g"` ;;
    *) ac_arg=$ac_var=$ac_new_val ;;
    esac
    case " $ac_configure_args " in
      *" '$ac_arg' "*) ;; # Avoid dups.  Use of quotes ensures accuracy.
      *) as_fn_append ac_configure_args " '$ac_arg'" ;;
    esac
  fi
done
if $ac_cache_corrupted; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: changes in the environment can compromise the build" >&5
printf "%s\n" "$as_me: error: changes in the environment can compromise the build" >&2;}
  as_fn_error $? "run \`${MAKE-make} distclean' and/or \`rm $cache_file'
	    and start over" "$LINENO" 5
fi
## -------------------- ##
## Main body of script. ##
## -------------------- ##

ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu






  
  # Make sure we can run config.sub.
$SHELL "${ac_aux_dir}config.sub" sun4 >/dev/null 2>&1 ||
  as_fn_error $? "cannot run $SHELL ${ac_aux_dir}config.sub" "$LINENO" 5

{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking build system type" >&5
printf %s "checking build system type... " >&6; }
if test ${ac_cv_build+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_build_alias=$build_alias
test "x$ac_build_alias" = x &&
  ac_build_alias=`$SHELL "${ac_aux_dir}config.guess"`
test "x$ac_build_alias" = x &&
  as_fn_error $? "cannot guess build type; you must specify one" "$LINENO" 5
ac_cv_build=`$SHELL "${ac_aux_dir}config.sub" $ac_build_alias` ||
  as_fn_error $? "$SHELL ${ac_aux_dir}config.sub $ac_build_alias failed" "$LINENO" 5

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_build" >&5
printf "%s\n" "$ac_cv_build" >&6; }
case $ac_cv_build in
*-*-*) ;;
*) as_fn_error $? "invalid value of canonical build" "$LINENO" 5;;
esac
build=$ac_cv_build
ac_save_IFS=$IFS; IFS='-'
set x $ac_cv_build
shift
build_cpu=$1
build_vendor=$2
shift; shift
# Remember, the first character of IFS is used to create $*,
# except with old shells:
build_os=$*
IFS=$ac_save_IFS
case $build_os in *\ *) build_os=`echo "$build_os" | sed 's/ /-/g'`;; esac


{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking host system type" >&5
printf %s "checking host system type... " >&6; }
if test ${ac_cv_host+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test "x$host_alias" = x; then
  ac_cv_host=$ac_cv_build
else
  ac_cv_host=`$SHELL "${ac_aux_dir}config.sub" $host_alias` ||
    as_fn_error $? "$SHELL ${ac_aux_dir}config.sub $host_alias failed" "$LINENO" 5
fi

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_host" >&5
printf "%s\n" "$ac_cv_host" >&6; }
case $ac_cv_host in
*-*-*) ;;
*) as_fn_error $? "invalid value of canonical host" "$LINENO" 5;;
esac
host=$ac_cv_host
ac_save_IFS=$IFS; IFS='-'
set x $ac_cv_host
shift
host_cpu=$1
host_vendor=$2
shift; shift
# Remember, the first character of IFS is used to create $*,
# except with old shells:
host_os=$*
IFS=$ac_save_IFS
case $host_os in *\ *) host_os=`echo "$host_os" | sed 's/ /-/g'`;; esac




    
    
    
    
@%:@ Check whether --with-gcc was given.
if test ${with_gcc+y}
then :
  withval=$with_gcc; 
fi

    V_INCLS=""
    if test "${srcdir}" != "." ; then
	    V_INCLS="-I$srcdir"
    fi
    if test "${CFLAGS+set}" = set; then
	    LBL_CFLAGS="$CFLAGS"
    fi
    if test -z "$CC" -a "$with_gcc" = no ; then
	    CC=cc
	    export CC
    fi

#
# Try to enable as many C99 features as we can.
# At minimum, we want C++/C99-style // comments.
#









ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu
if test -n "$ac_tool_prefix"; then
  # Extract the first word of "${ac_tool_prefix}gcc", so it can be a program name with args.
set dummy ${ac_tool_prefix}gcc; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$CC"; then
  ac_cv_prog_CC="$CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_CC="${ac_tool_prefix}gcc"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
CC=$ac_cv_prog_CC
if test -n "$CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $CC" >&5
printf "%s\n" "$CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$ac_cv_prog_CC"; then
  ac_ct_CC=$CC
  # Extract the first word of "gcc", so it can be a program name with args.
set dummy gcc; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_ac_ct_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$ac_ct_CC"; then
  ac_cv_prog_ac_ct_CC="$ac_ct_CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_ac_ct_CC="gcc"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
ac_ct_CC=$ac_cv_prog_ac_ct_CC
if test -n "$ac_ct_CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_ct_CC" >&5
printf "%s\n" "$ac_ct_CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

  if test "x$ac_ct_CC" = x; then
    CC=""
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    CC=$ac_ct_CC
  fi
else
  CC="$ac_cv_prog_CC"
fi

if test -z "$CC"; then
          if test -n "$ac_tool_prefix"; then
    # Extract the first word of "${ac_tool_prefix}cc", so it can be a program name with args.
set dummy ${ac_tool_prefix}cc; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$CC"; then
  ac_cv_prog_CC="$CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_CC="${ac_tool_prefix}cc"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
CC=$ac_cv_prog_CC
if test -n "$CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $CC" >&5
printf "%s\n" "$CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


  fi
fi
if test -z "$CC"; then
  # Extract the first word of "cc", so it can be a program name with args.
set dummy cc; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$CC"; then
  ac_cv_prog_CC="$CC" # Let the user override the test.
else
  ac_prog_rejected=no
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    if test "$as_dir$ac_word$ac_exec_ext" = "/usr/ucb/cc"; then
       ac_prog_rejected=yes
       continue
     fi
    ac_cv_prog_CC="cc"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

if test $ac_prog_rejected = yes; then
  # We found a bogon in the path, so make sure we never use it.
  set dummy $ac_cv_prog_CC
  shift
  if test $@%:@ != 0; then
    # We chose a different compiler from the bogus one.
    # However, it has the same basename, so the bogon will be chosen
    # first if we set CC to just the basename; use the full file name.
    shift
    ac_cv_prog_CC="$as_dir$ac_word${1+' '}$@"
  fi
fi
fi
fi
CC=$ac_cv_prog_CC
if test -n "$CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $CC" >&5
printf "%s\n" "$CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$CC"; then
  if test -n "$ac_tool_prefix"; then
  for ac_prog in cl.exe
  do
    # Extract the first word of "$ac_tool_prefix$ac_prog", so it can be a program name with args.
set dummy $ac_tool_prefix$ac_prog; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$CC"; then
  ac_cv_prog_CC="$CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_CC="$ac_tool_prefix$ac_prog"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
CC=$ac_cv_prog_CC
if test -n "$CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $CC" >&5
printf "%s\n" "$CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


    test -n "$CC" && break
  done
fi
if test -z "$CC"; then
  ac_ct_CC=$CC
  for ac_prog in cl.exe
do
  # Extract the first word of "$ac_prog", so it can be a program name with args.
set dummy $ac_prog; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_ac_ct_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$ac_ct_CC"; then
  ac_cv_prog_ac_ct_CC="$ac_ct_CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_ac_ct_CC="$ac_prog"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
ac_ct_CC=$ac_cv_prog_ac_ct_CC
if test -n "$ac_ct_CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_ct_CC" >&5
printf "%s\n" "$ac_ct_CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


  test -n "$ac_ct_CC" && break
done

  if test "x$ac_ct_CC" = x; then
    CC=""
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    CC=$ac_ct_CC
  fi
fi

fi
if test -z "$CC"; then
  if test -n "$ac_tool_prefix"; then
  # Extract the first word of "${ac_tool_prefix}clang", so it can be a program name with args.
set dummy ${ac_tool_prefix}clang; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$CC"; then
  ac_cv_prog_CC="$CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_CC="${ac_tool_prefix}clang"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
CC=$ac_cv_prog_CC
if test -n "$CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $CC" >&5
printf "%s\n" "$CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$ac_cv_prog_CC"; then
  ac_ct_CC=$CC
  # Extract the first word of "clang", so it can be a program name with args.
set dummy clang; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_ac_ct_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$ac_ct_CC"; then
  ac_cv_prog_ac_ct_CC="$ac_ct_CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_ac_ct_CC="clang"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
ac_ct_CC=$ac_cv_prog_ac_ct_CC
if test -n "$ac_ct_CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_ct_CC" >&5
printf "%s\n" "$ac_ct_CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

  if test "x$ac_ct_CC" = x; then
    CC=""
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    CC=$ac_ct_CC
  fi
else
  CC="$ac_cv_prog_CC"
fi

fi


test -z "$CC" && { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error $? "no acceptable C compiler found in \$PATH
See \`config.log' for more details" "$LINENO" 5; }

# Provide some information about the compiler.
printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for C compiler version" >&5
set X $ac_compile
ac_compiler=$2
for ac_option in --version -v -V -qversion -version; do
  { { ac_try="$ac_compiler $ac_option >&5"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_compiler $ac_option >&5") 2>conftest.err
  ac_status=$?
  if test -s conftest.err; then
    sed '10a\
... rest of stderr output deleted ...
         10q' conftest.err >conftest.er1
    cat conftest.er1 >&5
  fi
  rm -f conftest.er1 conftest.err
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }
done

cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
ac_clean_files_save=$ac_clean_files
ac_clean_files="$ac_clean_files a.out a.out.dSYM a.exe b.out"
# Try to create an executable without -o first, disregard a.out.
# It will help us diagnose broken compilers, and finding out an intuition
# of exeext.
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the C compiler works" >&5
printf %s "checking whether the C compiler works... " >&6; }
ac_link_default=`printf "%s\n" "$ac_link" | sed 's/ -o *conftest[^ ]*//'`

# The possible output files:
ac_files="a.out conftest.exe conftest a.exe a_out.exe b.out conftest.*"

ac_rmfiles=
for ac_file in $ac_files
do
  case $ac_file in
    *.$ac_ext | *.xcoff | *.tds | *.d | *.pdb | *.xSYM | *.bb | *.bbg | *.map | *.inf | *.dSYM | *.o | *.obj ) ;;
    * ) ac_rmfiles="$ac_rmfiles $ac_file";;
  esac
done
rm -f $ac_rmfiles

if { { ac_try="$ac_link_default"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_link_default") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }
then :
  # Autoconf-2.13 could set the ac_cv_exeext variable to `no'.
# So ignore a value of `no', otherwise this would lead to `EXEEXT = no'
# in a Makefile.  We should not override ac_cv_exeext if it was cached,
# so that the user can short-circuit this test for compilers unknown to
# Autoconf.
for ac_file in $ac_files ''
do
  test -f "$ac_file" || continue
  case $ac_file in
    *.$ac_ext | *.xcoff | *.tds | *.d | *.pdb | *.xSYM | *.bb | *.bbg | *.map | *.inf | *.dSYM | *.o | *.obj )
	;;
    [ab].out )
	# We found the default executable, but exeext='' is most
	# certainly right.
	break;;
    *.* )
	if test ${ac_cv_exeext+y} && test "$ac_cv_exeext" != no;
	then :; else
	   ac_cv_exeext=`expr "$ac_file" : '[^.]*\(\..*\)'`
	fi
	# We set ac_cv_exeext here because the later test for it is not
	# safe: cross compilers may not add the suffix if given an `-o'
	# argument, so we may need to know it at that point already.
	# Even if this section looks crufty: it has the advantage of
	# actually working.
	break;;
    * )
	break;;
  esac
done
test "$ac_cv_exeext" = no && ac_cv_exeext=

else $as_nop
  ac_file=''
fi
if test -z "$ac_file"
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
printf "%s\n" "$as_me: failed program was:" >&5
sed 's/^/| /' conftest.$ac_ext >&5

{ { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error 77 "C compiler cannot create executables
See \`config.log' for more details" "$LINENO" 5; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for C compiler default output file name" >&5
printf %s "checking for C compiler default output file name... " >&6; }
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_file" >&5
printf "%s\n" "$ac_file" >&6; }
ac_exeext=$ac_cv_exeext

rm -f -r a.out a.out.dSYM a.exe conftest$ac_cv_exeext b.out
ac_clean_files=$ac_clean_files_save
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for suffix of executables" >&5
printf %s "checking for suffix of executables... " >&6; }
if { { ac_try="$ac_link"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_link") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }
then :
  # If both `conftest.exe' and `conftest' are `present' (well, observable)
# catch `conftest.exe'.  For instance with Cygwin, `ls conftest' will
# work properly (i.e., refer to `conftest.exe'), while it won't with
# `rm'.
for ac_file in conftest.exe conftest conftest.*; do
  test -f "$ac_file" || continue
  case $ac_file in
    *.$ac_ext | *.xcoff | *.tds | *.d | *.pdb | *.xSYM | *.bb | *.bbg | *.map | *.inf | *.dSYM | *.o | *.obj ) ;;
    *.* ) ac_cv_exeext=`expr "$ac_file" : '[^.]*\(\..*\)'`
	  break;;
    * ) break;;
  esac
done
else $as_nop
  { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error $? "cannot compute suffix of executables: cannot compile and link
See \`config.log' for more details" "$LINENO" 5; }
fi
rm -f conftest conftest$ac_cv_exeext
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_exeext" >&5
printf "%s\n" "$ac_cv_exeext" >&6; }

rm -f conftest.$ac_ext
EXEEXT=$ac_cv_exeext
ac_exeext=$EXEEXT
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
@%:@include <stdio.h>
int
main (void)
{
FILE *f = fopen ("conftest.out", "w");
 return ferror (f) || fclose (f) != 0;

  ;
  return 0;
}
_ACEOF
ac_clean_files="$ac_clean_files conftest.out"
# Check that the compiler produces executables we can run.  If not, either
# the compiler is broken, or we cross compile.
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether we are cross compiling" >&5
printf %s "checking whether we are cross compiling... " >&6; }
if test "$cross_compiling" != yes; then
  { { ac_try="$ac_link"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_link") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }
  if { ac_try='./conftest$ac_cv_exeext'
  { { case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_try") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }; }; then
    cross_compiling=no
  else
    if test "$cross_compiling" = maybe; then
	cross_compiling=yes
    else
	{ { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error 77 "cannot run C compiled programs.
If you meant to cross compile, use \`--host'.
See \`config.log' for more details" "$LINENO" 5; }
    fi
  fi
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $cross_compiling" >&5
printf "%s\n" "$cross_compiling" >&6; }

rm -f conftest.$ac_ext conftest$ac_cv_exeext conftest.out
ac_clean_files=$ac_clean_files_save
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for suffix of object files" >&5
printf %s "checking for suffix of object files... " >&6; }
if test ${ac_cv_objext+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
rm -f conftest.o conftest.obj
if { { ac_try="$ac_compile"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_compile") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }
then :
  for ac_file in conftest.o conftest.obj conftest.*; do
  test -f "$ac_file" || continue;
  case $ac_file in
    *.$ac_ext | *.xcoff | *.tds | *.d | *.pdb | *.xSYM | *.bb | *.bbg | *.map | *.inf | *.dSYM ) ;;
    *) ac_cv_objext=`expr "$ac_file" : '.*\.\(.*\)'`
       break;;
  esac
done
else $as_nop
  printf "%s\n" "$as_me: failed program was:" >&5
sed 's/^/| /' conftest.$ac_ext >&5

{ { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error $? "cannot compute suffix of object files: cannot compile
See \`config.log' for more details" "$LINENO" 5; }
fi
rm -f conftest.$ac_cv_objext conftest.$ac_ext
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_objext" >&5
printf "%s\n" "$ac_cv_objext" >&6; }
OBJEXT=$ac_cv_objext
ac_objext=$OBJEXT
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports GNU C" >&5
printf %s "checking whether the compiler supports GNU C... " >&6; }
if test ${ac_cv_c_compiler_gnu+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{
#ifndef __GNUC__
       choke me
#endif

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_compiler_gnu=yes
else $as_nop
  ac_compiler_gnu=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
ac_cv_c_compiler_gnu=$ac_compiler_gnu

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_c_compiler_gnu" >&5
printf "%s\n" "$ac_cv_c_compiler_gnu" >&6; }
ac_compiler_gnu=$ac_cv_c_compiler_gnu

if test $ac_compiler_gnu = yes; then
  GCC=yes
else
  GCC=
fi
ac_test_CFLAGS=${CFLAGS+y}
ac_save_CFLAGS=$CFLAGS
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether $CC accepts -g" >&5
printf %s "checking whether $CC accepts -g... " >&6; }
if test ${ac_cv_prog_cc_g+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_save_c_werror_flag=$ac_c_werror_flag
   ac_c_werror_flag=yes
   ac_cv_prog_cc_g=no
   CFLAGS="-g"
   cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_prog_cc_g=yes
else $as_nop
  CFLAGS=""
      cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
else $as_nop
  ac_c_werror_flag=$ac_save_c_werror_flag
	 CFLAGS="-g"
	 cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_prog_cc_g=yes
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
   ac_c_werror_flag=$ac_save_c_werror_flag
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_prog_cc_g" >&5
printf "%s\n" "$ac_cv_prog_cc_g" >&6; }
if test $ac_test_CFLAGS; then
  CFLAGS=$ac_save_CFLAGS
elif test $ac_cv_prog_cc_g = yes; then
  if test "$GCC" = yes; then
    CFLAGS="-g -O2"
  else
    CFLAGS="-g"
  fi
else
  if test "$GCC" = yes; then
    CFLAGS="-O2"
  else
    CFLAGS=
  fi
fi
ac_prog_cc_stdc=no
if test x$ac_prog_cc_stdc = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $CC option to enable C11 features" >&5
printf %s "checking for $CC option to enable C11 features... " >&6; }
if test ${ac_cv_prog_cc_c11+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_cv_prog_cc_c11=no
ac_save_CC=$CC
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$ac_c_conftest_c11_program
_ACEOF
for ac_arg in '' -std=gnu11
do
  CC="$ac_save_CC $ac_arg"
  if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_prog_cc_c11=$ac_arg
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam
  test "x$ac_cv_prog_cc_c11" != "xno" && break
done
rm -f conftest.$ac_ext
CC=$ac_save_CC
fi

if test "x$ac_cv_prog_cc_c11" = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: unsupported" >&5
printf "%s\n" "unsupported" >&6; }
else $as_nop
  if test "x$ac_cv_prog_cc_c11" = x
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: none needed" >&5
printf "%s\n" "none needed" >&6; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_prog_cc_c11" >&5
printf "%s\n" "$ac_cv_prog_cc_c11" >&6; }
     CC="$CC $ac_cv_prog_cc_c11"
fi
  ac_cv_prog_cc_stdc=$ac_cv_prog_cc_c11
  ac_prog_cc_stdc=c11
fi
fi
if test x$ac_prog_cc_stdc = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $CC option to enable C99 features" >&5
printf %s "checking for $CC option to enable C99 features... " >&6; }
if test ${ac_cv_prog_cc_c99+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_cv_prog_cc_c99=no
ac_save_CC=$CC
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$ac_c_conftest_c99_program
_ACEOF
for ac_arg in '' -std=gnu99 -std=c99 -c99 -qlanglvl=extc1x -qlanglvl=extc99 -AC99 -D_STDC_C99=
do
  CC="$ac_save_CC $ac_arg"
  if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_prog_cc_c99=$ac_arg
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam
  test "x$ac_cv_prog_cc_c99" != "xno" && break
done
rm -f conftest.$ac_ext
CC=$ac_save_CC
fi

if test "x$ac_cv_prog_cc_c99" = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: unsupported" >&5
printf "%s\n" "unsupported" >&6; }
else $as_nop
  if test "x$ac_cv_prog_cc_c99" = x
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: none needed" >&5
printf "%s\n" "none needed" >&6; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_prog_cc_c99" >&5
printf "%s\n" "$ac_cv_prog_cc_c99" >&6; }
     CC="$CC $ac_cv_prog_cc_c99"
fi
  ac_cv_prog_cc_stdc=$ac_cv_prog_cc_c99
  ac_prog_cc_stdc=c99
fi
fi
if test x$ac_prog_cc_stdc = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $CC option to enable C89 features" >&5
printf %s "checking for $CC option to enable C89 features... " >&6; }
if test ${ac_cv_prog_cc_c89+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_cv_prog_cc_c89=no
ac_save_CC=$CC
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$ac_c_conftest_c89_program
_ACEOF
for ac_arg in '' -qlanglvl=extc89 -qlanglvl=ansi -std -Ae "-Aa -D_HPUX_SOURCE" "-Xc -D__EXTENSIONS__"
do
  CC="$ac_save_CC $ac_arg"
  if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_prog_cc_c89=$ac_arg
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam
  test "x$ac_cv_prog_cc_c89" != "xno" && break
done
rm -f conftest.$ac_ext
CC=$ac_save_CC
fi

if test "x$ac_cv_prog_cc_c89" = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: unsupported" >&5
printf "%s\n" "unsupported" >&6; }
else $as_nop
  if test "x$ac_cv_prog_cc_c89" = x
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: none needed" >&5
printf "%s\n" "none needed" >&6; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_prog_cc_c89" >&5
printf "%s\n" "$ac_cv_prog_cc_c89" >&6; }
     CC="$CC $ac_cv_prog_cc_c89"
fi
  ac_cv_prog_cc_stdc=$ac_cv_prog_cc_c89
  ac_prog_cc_stdc=c89
fi
fi

ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu


if test "$ac_cv_prog_cc_c99" = "no"; then
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: The C compiler does not support C99; there may be compiler errors" >&5
printf "%s\n" "$as_me: WARNING: The C compiler does not support C99; there may be compiler errors" >&2;}
fi

    
    if test "$GCC" = yes ; then
	    #
	    # -Werror forces warnings to be errors.
	    #
	    ac_lbl_cc_force_warning_errors=-Werror
    else
	    V_INCLS="$V_INCLS -I/usr/local/include"
	    LDFLAGS="$LDFLAGS -L/usr/local/lib"

	    case "$host_os" in

	    darwin*)
		    #
		    # This is assumed either to be GCC or clang, both
		    # of which use -Werror to force warnings to be errors.
		    #
		    # XXX - they also both cause GCC to be set to yes,
		    # so we should never get here in the first place.
		    #
		    ac_lbl_cc_force_warning_errors=-Werror
		    ;;

	    hpux*)
		    #
		    # HP C, which is what we presume we're using, doesn't
		    # exit with a non-zero exit status if we hand it an
		    # invalid -W flag, can't be forced to do so even with
		    # +We, and doesn't handle GCC-style -W flags, so we
		    # don't want to try using GCC-style -W flags.
		    #
		    ac_lbl_cc_dont_try_gcc_dashW=yes
		    ;;

	    solaris*)
		    #
		    # Assumed to be Sun C, which requires -errwarn to force
		    # warnings to be treated as errors.
		    #
		    ac_lbl_cc_force_warning_errors=-errwarn
		    ;;
	    esac
	    V_CCOPT="$V_CCOPT -O"
    fi


#
# Test for ONC RPC headers.
#
# XXX - we should supply copies of these, as I think Sun released them
# under a sufficiently permissive license, and that would be one less
# place where we rely on OS headers, rather than our own headers, for
# a protocol that is specified independent of any particular OS.
#

ac_header= ac_cache=
for ac_item in $ac_header_c_list
do
  if test $ac_cache; then
    ac_fn_c_check_header_compile "$LINENO" $ac_header ac_cv_header_$ac_cache "$ac_includes_default"
    if eval test \"x\$ac_cv_header_$ac_cache\" = xyes; then
      printf "%s\n" "#define $ac_item 1" >> confdefs.h
    fi
    ac_header= ac_cache=
  elif test $ac_header; then
    ac_cache=$ac_item
  else
    ac_header=$ac_item
  fi
done








if test $ac_cv_header_stdlib_h = yes && test $ac_cv_header_string_h = yes
then :
  
printf "%s\n" "@%:@define STDC_HEADERS 1" >>confdefs.h

fi
ac_fn_c_check_header_compile "$LINENO" "rpc/rpc.h" "ac_cv_header_rpc_rpc_h" "$ac_includes_default"
if test "x$ac_cv_header_rpc_rpc_h" = xyes
then :
  printf "%s\n" "@%:@define HAVE_RPC_RPC_H 1" >>confdefs.h

fi
ac_fn_c_check_header_compile "$LINENO" "rpc/rpcent.h" "ac_cv_header_rpc_rpcent_h" "$ac_includes_default"
if test "x$ac_cv_header_rpc_rpcent_h" = xyes
then :
  printf "%s\n" "@%:@define HAVE_RPC_RPCENT_H 1" >>confdefs.h

fi


# On Linux, if Autoconf version >= 2.72 and GNU C Library version >= 2.34,
# uncomment AC_SYS_YEAR2038_RECOMMENDED to ensure time_t is Y2038-safe.
# (Can be done by autogen.sh)
# AC_SYS_YEAR2038_RECOMMENDED
#
# Get the size of a void *, to know whether this is a 32-bit or 64-bit build.
#
# The cast to long int works around a bug in the HP C Compiler
# version HP92453-01 B.11.11.23709.GP, which incorrectly rejects
# declarations like `int a3[[(sizeof (unsigned char)) >= 0]];'.
# This bug is HP SR number 8606223364.
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking size of void *" >&5
printf %s "checking size of void *... " >&6; }
if test ${ac_cv_sizeof_void_p+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if ac_fn_c_compute_int "$LINENO" "(long int) (sizeof (void *))" "ac_cv_sizeof_void_p"        "$ac_includes_default"
then :
  
else $as_nop
  if test "$ac_cv_type_void_p" = yes; then
     { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error 77 "cannot compute sizeof (void *)
See \`config.log' for more details" "$LINENO" 5; }
   else
     ac_cv_sizeof_void_p=0
   fi
fi

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_sizeof_void_p" >&5
printf "%s\n" "$ac_cv_sizeof_void_p" >&6; }



printf "%s\n" "@%:@define SIZEOF_VOID_P $ac_cv_sizeof_void_p" >>confdefs.h



#
# Get the size of a time_t, to know whether it's 32-bit or 64-bit.
#
# The cast to long int works around a bug in the HP C Compiler
# version HP92453-01 B.11.11.23709.GP, which incorrectly rejects
# declarations like `int a3[[(sizeof (unsigned char)) >= 0]];'.
# This bug is HP SR number 8606223364.
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking size of time_t" >&5
printf %s "checking size of time_t... " >&6; }
if test ${ac_cv_sizeof_time_t+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if ac_fn_c_compute_int "$LINENO" "(long int) (sizeof (time_t))" "ac_cv_sizeof_time_t"        "#include <time.h>
"
then :
  
else $as_nop
  if test "$ac_cv_type_time_t" = yes; then
     { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error 77 "cannot compute sizeof (time_t)
See \`config.log' for more details" "$LINENO" 5; }
   else
     ac_cv_sizeof_time_t=0
   fi
fi

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_sizeof_time_t" >&5
printf "%s\n" "$ac_cv_sizeof_time_t" >&6; }



printf "%s\n" "@%:@define SIZEOF_TIME_T $ac_cv_sizeof_time_t" >>confdefs.h



case "$host_os" in

darwin*)
	@%:@ Check whether --enable-universal was given.
if test ${enable_universal+y}
then :
  enableval=$enable_universal; 
fi

	if test "$enable_universal" != "no"; then
		case "$host_os" in

		darwin9.*)
			#
			# Leopard.  Build for x86 and 32-bit PowerPC, with
			# x86 first.  (That's what Apple does.)
			#
			V_CCOPT="$V_CCOPT -arch i386 -arch ppc"
			LDFLAGS="$LDFLAGS -arch i386 -arch ppc"
			;;

		darwin10.*)
			#
			# Snow Leopard.  Build for x86-64 and x86, with
			# x86-64 first.  (That's what Apple does.)
			#
			V_CCOPT="$V_CCOPT -arch x86_64 -arch i386"
			LDFLAGS="$LDFLAGS -arch x86_64 -arch i386"
			;;
		esac
	fi
	;;
esac

#
# Do we have pkg-config?
#







if test "x$ac_cv_env_PKG_CONFIG_set" != "xset"; then
	if test -n "$ac_tool_prefix"; then
  # Extract the first word of "${ac_tool_prefix}pkg-config", so it can be a program name with args.
set dummy ${ac_tool_prefix}pkg-config; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_PKG_CONFIG+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $PKG_CONFIG in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_PKG_CONFIG="$PKG_CONFIG" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_PKG_CONFIG="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
PKG_CONFIG=$ac_cv_path_PKG_CONFIG
if test -n "$PKG_CONFIG"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $PKG_CONFIG" >&5
printf "%s\n" "$PKG_CONFIG" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$ac_cv_path_PKG_CONFIG"; then
  ac_pt_PKG_CONFIG=$PKG_CONFIG
  # Extract the first word of "pkg-config", so it can be a program name with args.
set dummy pkg-config; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_ac_pt_PKG_CONFIG+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $ac_pt_PKG_CONFIG in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_ac_pt_PKG_CONFIG="$ac_pt_PKG_CONFIG" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_ac_pt_PKG_CONFIG="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
ac_pt_PKG_CONFIG=$ac_cv_path_ac_pt_PKG_CONFIG
if test -n "$ac_pt_PKG_CONFIG"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_pt_PKG_CONFIG" >&5
printf "%s\n" "$ac_pt_PKG_CONFIG" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

  if test "x$ac_pt_PKG_CONFIG" = x; then
    PKG_CONFIG=""
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    PKG_CONFIG=$ac_pt_PKG_CONFIG
  fi
else
  PKG_CONFIG="$ac_cv_path_PKG_CONFIG"
fi

fi
if test -n "$PKG_CONFIG"; then
	_pkg_min_version=0.17.0
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking pkg-config is at least version $_pkg_min_version" >&5
printf %s "checking pkg-config is at least version $_pkg_min_version... " >&6; }
	if $PKG_CONFIG --atleast-pkgconfig-version $_pkg_min_version; then
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
	else
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		PKG_CONFIG=""
	fi
fi

#
# Do we have the brew command from Homebrew?
#
case "$host_os" in
darwin*|linux*)
	# Extract the first word of "brew", so it can be a program name with args.
set dummy brew; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_BREW+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $BREW in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_BREW="$BREW" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_BREW="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
BREW=$ac_cv_path_BREW
if test -n "$BREW"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $BREW" >&5
printf "%s\n" "$BREW" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


	;;
esac


@%:@ Check whether --with-smi was given.
if test ${with_smi+y}
then :
  withval=$with_smi; 
else $as_nop
  with_smi=yes
fi


if test "x$with_smi" != "xno" ; then
	ac_fn_c_check_header_compile "$LINENO" "smi.h" "ac_cv_header_smi_h" "$ac_includes_default"
if test "x$ac_cv_header_smi_h" = xyes
then :
  
		#
		# OK, we found smi.h.  Do we have libsmi with smiInit?
		#
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for smiInit in -lsmi" >&5
printf %s "checking for smiInit in -lsmi... " >&6; }
if test ${ac_cv_lib_smi_smiInit+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lsmi  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char smiInit ();
int
main (void)
{
return smiInit ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_smi_smiInit=yes
else $as_nop
  ac_cv_lib_smi_smiInit=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_smi_smiInit" >&5
printf "%s\n" "$ac_cv_lib_smi_smiInit" >&6; }
if test "x$ac_cv_lib_smi_smiInit" = xyes
then :
  
			#
			# OK, we have libsmi with smiInit.  Can we use it?
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether to enable libsmi" >&5
printf %s "checking whether to enable libsmi... " >&6; }
			savedlibs="$LIBS"
			LIBS="-lsmi $LIBS"
			if test "$cross_compiling" = yes
then :
  
					{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: not when cross-compiling" >&5
printf "%s\n" "not when cross-compiling" >&6; }
					LIBS="$savedlibs"
				
			
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* libsmi available check */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <smi.h>
int main()
{
  int current, revision, age, n;
  const int required = 2;
  if (smiInit(""))
    exit(1);
  if (strcmp(SMI_LIBRARY_VERSION, smi_library_version))
    exit(2);
  n = sscanf(smi_library_version, "%d:%d:%d", &current, &revision, &age);
  if (n != 3)
    exit(3);
  if (required < current - age || required > current)
    exit(4);
  exit(0);
}
				
				
_ACEOF
if ac_fn_c_try_run "$LINENO"
then :
  
					{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
					
printf "%s\n" "@%:@define USE_LIBSMI 1" >>confdefs.h

				
else $as_nop
  
																																			case $? in
					  1) { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no - smiInit failed" >&5
printf "%s\n" "no - smiInit failed" >&6; } ;;
					  2) { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no - header/library version mismatch" >&5
printf "%s\n" "no - header/library version mismatch" >&6; } ;;
					  3) { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no - can't determine library version" >&5
printf "%s\n" "no - can't determine library version" >&6; } ;;
					  4) { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no - too old" >&5
printf "%s\n" "no - too old" >&6; } ;;
					  *) { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; } ;;
					esac
					LIBS="$savedlibs"
				
fi
rm -f core *.core core.conftest.* gmon.out bb.out conftest$ac_exeext \
  conftest.$ac_objext conftest.beam conftest.$ac_ext
fi

		
fi

	
fi

fi

{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether to enable the instrument functions code" >&5
printf %s "checking whether to enable the instrument functions code... " >&6; }
@%:@ Check whether --enable-instrument-functions was given.
if test ${enable_instrument_functions+y}
then :
  enableval=$enable_instrument_functions; 
else $as_nop
  enableval=no
fi

case "$enableval" in
yes)	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for bfd_init in -lbfd" >&5
printf %s "checking for bfd_init in -lbfd... " >&6; }
if test ${ac_cv_lib_bfd_bfd_init+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lbfd  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char bfd_init ();
int
main (void)
{
return bfd_init ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_bfd_bfd_init=yes
else $as_nop
  ac_cv_lib_bfd_bfd_init=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_bfd_bfd_init" >&5
printf "%s\n" "$ac_cv_lib_bfd_bfd_init" >&6; }
if test "x$ac_cv_lib_bfd_bfd_init" = xyes
then :
  true
else $as_nop
  as_fn_error $? "--enable-instrument-functions was given, but test for library libbfd failed. Please install the 'binutils-dev' package." "$LINENO" 5
fi

	
printf "%s\n" "@%:@define ENABLE_INSTRUMENT_FUNCTIONS 1" >>confdefs.h

	LOCALSRC="$LOCALSRC instrument-functions.c"
	# Add '-finstrument-functions' instrumentation option to generate
	# instrumentation calls for entry and exit to functions.
	# Try to avoid Address Space Layout Randomization (ALSR).
	CFLAGS="$CFLAGS -O0 -ggdb -finstrument-functions -fno-stack-protector -fno-pic"
	LDFLAGS="$LDFLAGS -O0 -ggdb -fno-stack-protector -no-pie"
	LIBS="$LIBS -lbfd"
	;;
*)	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
	;;
esac

{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether to enable the possibly-buggy SMB printer" >&5
printf %s "checking whether to enable the possibly-buggy SMB printer... " >&6; }
@%:@ Check whether --enable-smb was given.
if test ${enable_smb+y}
then :
  enableval=$enable_smb; 
else $as_nop
  enableval=no
fi

case "$enableval" in
yes)	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
	
printf "%s\n" "@%:@define ENABLE_SMB 1" >>confdefs.h

	LOCALSRC="print-smb.c smbutil.c $LOCALSRC"
	;;
*)	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
	;;
esac

{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether to drop root privileges by default" >&5
printf %s "checking whether to drop root privileges by default... " >&6; }

@%:@ Check whether --with-user was given.
if test ${with_user+y}
then :
  withval=$with_user; 
else $as_nop
  withval=no
fi

case "$withval" in @%:@(
  no) :
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; } ;; @%:@(
  ''|yes) :
    as_fn_error $? "--with-user requires a username" "$LINENO" 5 ;; @%:@(
  *) :
    
		
printf "%s\n" "@%:@define WITH_USER \"$withval\"" >>confdefs.h

		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes, to user \"$withval\"" >&5
printf "%s\n" "yes, to user \"$withval\"" >&6; }
	
 ;;
esac

{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether to chroot" >&5
printf %s "checking whether to chroot... " >&6; }

@%:@ Check whether --with-chroot was given.
if test ${with_chroot+y}
then :
  withval=$with_chroot; 
else $as_nop
  withval=no

fi

case "$withval" in @%:@(
  no) :
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; } ;; @%:@(
  ''|yes) :
    as_fn_error $? "--with-chroot requires a directory" "$LINENO" 5 ;; @%:@(
  *) :
    
		
printf "%s\n" "@%:@define WITH_CHROOT \"$withval\"" >>confdefs.h

		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes, to directory \"$withval\"" >&5
printf "%s\n" "yes, to directory \"$withval\"" >&6; }
	
 ;;
esac


@%:@ Check whether --with-sandbox-capsicum was given.
if test ${with_sandbox_capsicum+y}
then :
  withval=$with_sandbox_capsicum; 
fi


# Capsicum is available on FreeBSD only.
expr "$host_os" : freebsd >/dev/null || with_sandbox_capsicum=no

#
# Check whether various functions are available.  If any are, set
# ac_lbl_capsicum_function_seen to yes; if any are not, set
# ac_lbl_capsicum_function_not_seen to yes.
#
# We don't check cap_rights_init(), as it's a macro, wrapping another
# function, in at least some versions of FreeBSD, and AC_CHECK_FUNCS()
# doesn't handle that.
#
# All of the ones we check for must be available in order to enable
# capsicum sandboxing.
#
# XXX - do we need to check for all of them, or are there some that, if
# present, imply others are present?
#
if test -z "$with_sandbox_capsicum" || test "$with_sandbox_capsicum" != "no" ; then
	#
	# First, make sure we have the required header.
	#
	ac_fn_c_check_header_compile "$LINENO" "sys/capsicum.h" "ac_cv_header_sys_capsicum_h" "$ac_includes_default"
if test "x$ac_cv_header_sys_capsicum_h" = xyes
then :
  
		#
		# We do; now make sure we have the required functions.
		#
		
  for ac_func in cap_enter cap_rights_limit cap_ioctls_limit openat
do :
  as_ac_var=`printf "%s\n" "ac_cv_func_$ac_func" | $as_tr_sh`
ac_fn_c_check_func "$LINENO" "$ac_func" "$as_ac_var"
if eval test \"x\$"$as_ac_var"\" = x"yes"
then :
  cat >>confdefs.h <<_ACEOF
@%:@define `printf "%s\n" "HAVE_$ac_func" | $as_tr_cpp` 1
_ACEOF
 ac_lbl_capsicum_function_seen=yes
else $as_nop
  ac_lbl_capsicum_function_not_seen=yes
fi

done
	
fi

	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for cap_init in -lcasper" >&5
printf %s "checking for cap_init in -lcasper... " >&6; }
if test ${ac_cv_lib_casper_cap_init+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lcasper  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char cap_init ();
int
main (void)
{
return cap_init ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_casper_cap_init=yes
else $as_nop
  ac_cv_lib_casper_cap_init=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_casper_cap_init" >&5
printf "%s\n" "$ac_cv_lib_casper_cap_init" >&6; }
if test "x$ac_cv_lib_casper_cap_init" = xyes
then :
  LIBS="$LIBS -lcasper"
fi

	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for cap_gethostbyaddr in -lcap_dns" >&5
printf %s "checking for cap_gethostbyaddr in -lcap_dns... " >&6; }
if test ${ac_cv_lib_cap_dns_cap_gethostbyaddr+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lcap_dns  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char cap_gethostbyaddr ();
int
main (void)
{
return cap_gethostbyaddr ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_cap_dns_cap_gethostbyaddr=yes
else $as_nop
  ac_cv_lib_cap_dns_cap_gethostbyaddr=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_cap_dns_cap_gethostbyaddr" >&5
printf "%s\n" "$ac_cv_lib_cap_dns_cap_gethostbyaddr" >&6; }
if test "x$ac_cv_lib_cap_dns_cap_gethostbyaddr" = xyes
then :
  LIBS="$LIBS -lcap_dns"
fi

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether to sandbox using capsicum" >&5
printf %s "checking whether to sandbox using capsicum... " >&6; }
if test "x$ac_lbl_capsicum_function_seen" = "xyes" -a "x$ac_lbl_capsicum_function_not_seen" != "xyes"; then
	
printf "%s\n" "@%:@define HAVE_CAPSICUM 1" >>confdefs.h

	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
else
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether to sandbox using Casper library" >&5
printf %s "checking whether to sandbox using Casper library... " >&6; }
if test "x$ac_cv_lib_casper_cap_init" = "xyes" -a "x$ac_cv_lib_cap_dns_cap_gethostbyaddr" = "xyes"; then
	
printf "%s\n" "@%:@define HAVE_CASPER 1" >>confdefs.h

	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
else
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

#
# XXX - we used to check this before checking whether to check the OS's
# IPv6, support because, on some platforms (such as SunOS 5.x), the test
# program requires the extra networking libraries.  We no longer
# test for IPv6 support, we just assume it's present with the
# standard API, so perhaps this can be moved.
#

    ac_fn_c_check_func "$LINENO" "gethostbyaddr" "ac_cv_func_gethostbyaddr"
if test "x$ac_cv_func_gethostbyaddr" = xyes
then :
  
else $as_nop
  
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for gethostbyaddr in -lsocket" >&5
printf %s "checking for gethostbyaddr in -lsocket... " >&6; }
if test ${ac_cv_lib_socket_gethostbyaddr+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lsocket -lnsl $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char gethostbyaddr ();
int
main (void)
{
return gethostbyaddr ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_socket_gethostbyaddr=yes
else $as_nop
  ac_cv_lib_socket_gethostbyaddr=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_socket_gethostbyaddr" >&5
printf "%s\n" "$ac_cv_lib_socket_gethostbyaddr" >&6; }
if test "x$ac_cv_lib_socket_gethostbyaddr" = xyes
then :
  
	    LIBS="-lsocket -lnsl $LIBS"
	
else $as_nop
  
	    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for gethostbyaddr in -lnetwork" >&5
printf %s "checking for gethostbyaddr in -lnetwork... " >&6; }
if test ${ac_cv_lib_network_gethostbyaddr+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lnetwork  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char gethostbyaddr ();
int
main (void)
{
return gethostbyaddr ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_network_gethostbyaddr=yes
else $as_nop
  ac_cv_lib_network_gethostbyaddr=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_network_gethostbyaddr" >&5
printf "%s\n" "$ac_cv_lib_network_gethostbyaddr" >&6; }
if test "x$ac_cv_lib_network_gethostbyaddr" = xyes
then :
  
		LIBS="-lnetwork $LIBS"
	    
else $as_nop
  
		as_fn_error $? "gethostbyaddr is required, but wasn't found" "$LINENO" 5
	    
fi

	
fi

    
fi



ac_fn_c_check_func "$LINENO" "strlcat" "ac_cv_func_strlcat"
if test "x$ac_cv_func_strlcat" = xyes
then :
  printf "%s\n" "@%:@define HAVE_STRLCAT 1" >>confdefs.h

else $as_nop
  case " $LIB@&t@OBJS " in
  *" strlcat.$ac_objext "* ) ;;
  *) LIB@&t@OBJS="$LIB@&t@OBJS strlcat.$ac_objext"
 ;;
esac

fi
ac_fn_c_check_func "$LINENO" "strlcpy" "ac_cv_func_strlcpy"
if test "x$ac_cv_func_strlcpy" = xyes
then :
  printf "%s\n" "@%:@define HAVE_STRLCPY 1" >>confdefs.h

else $as_nop
  case " $LIB@&t@OBJS " in
  *" strlcpy.$ac_objext "* ) ;;
  *) LIB@&t@OBJS="$LIB@&t@OBJS strlcpy.$ac_objext"
 ;;
esac

fi
ac_fn_c_check_func "$LINENO" "strsep" "ac_cv_func_strsep"
if test "x$ac_cv_func_strsep" = xyes
then :
  printf "%s\n" "@%:@define HAVE_STRSEP 1" >>confdefs.h

else $as_nop
  case " $LIB@&t@OBJS " in
  *" strsep.$ac_objext "* ) ;;
  *) LIB@&t@OBJS="$LIB@&t@OBJS strsep.$ac_objext"
 ;;
esac

fi
ac_fn_c_check_func "$LINENO" "getservent" "ac_cv_func_getservent"
if test "x$ac_cv_func_getservent" = xyes
then :
  printf "%s\n" "@%:@define HAVE_GETSERVENT 1" >>confdefs.h

else $as_nop
  case " $LIB@&t@OBJS " in
  *" getservent.$ac_objext "* ) ;;
  *) LIB@&t@OBJS="$LIB@&t@OBJS getservent.$ac_objext"
 ;;
esac

fi
ac_fn_c_check_func "$LINENO" "getopt_long" "ac_cv_func_getopt_long"
if test "x$ac_cv_func_getopt_long" = xyes
then :
  printf "%s\n" "@%:@define HAVE_GETOPT_LONG 1" >>confdefs.h

else $as_nop
  case " $LIB@&t@OBJS " in
  *" getopt_long.$ac_objext "* ) ;;
  *) LIB@&t@OBJS="$LIB@&t@OBJS getopt_long.$ac_objext"
 ;;
esac

fi

ac_fn_c_check_func "$LINENO" "fork" "ac_cv_func_fork"
if test "x$ac_cv_func_fork" = xyes
then :
  printf "%s\n" "@%:@define HAVE_FORK 1" >>confdefs.h

fi
ac_fn_c_check_func "$LINENO" "vfork" "ac_cv_func_vfork"
if test "x$ac_cv_func_vfork" = xyes
then :
  printf "%s\n" "@%:@define HAVE_VFORK 1" >>confdefs.h

fi


#
# It became apparent at some point that using a suitable C99 compiler does not
# automatically mean snprintf(3) implementation in the libc supports all the
# modifiers and specifiers used in the project, so let's test that before the
# build, not after.
#
# Testing the sizeof_t length modifier takes making an snprintf() call and
# comparing the actual result with the expected result.  If this fails, it will
# most likely happen at run time, not compile time.
#
# Testing the 64-bit conversion specifiers in addition to that requires the
# <inttypes.h> header to be present and the macros to be defined, so if this
# fails, it will more likely happen at compile time.
#
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether snprintf is suitable" >&5
printf %s "checking whether snprintf is suitable... " >&6; }
if test "$cross_compiling" = yes
then :
  
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: not while cross-compiling" >&5
printf "%s\n" "not while cross-compiling" >&6; }
    

else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

        
#include <stdio.h>
#include <string.h>
#include <inttypes.h>
#include <sys/types.h>

#if defined(_WIN32) && !defined(_SSIZE_T_DEFINED)
/*
 * On UN*Xes, this is a signed integer type of the same size as size_t.
 *
 * It's not defined by Visual Studio; we assume that ptrdiff_t will
 * be a type that is a signed integer type of the same size as size_t.
 */
typedef ptrdiff_t ssize_t;
#endif

/*
 * Avoid trying to cast negative values to unsigned types, or doing
 * shifts of signed types, in order not to have the test program fail
 * if we're building with undefined-behavior sanitizers enabled.
 */
int main()
{
  char buf[100];
  unsigned int ui = sizeof(buf);
  int i = sizeof(buf);
  int64_t i64 = INT64_C(0x100000000);
  uint64_t ui64 = UINT64_C(0x100000000);

  snprintf(buf, sizeof(buf), "%zu", (size_t)ui);
  if (strncmp(buf, "100", sizeof(buf)))
    return 1;

  snprintf(buf, sizeof(buf), "%zd", (ssize_t)(-i));
  if (strncmp(buf, "-100", sizeof(buf)))
    return 2;

  snprintf(buf, sizeof(buf), "%" PRId64, -i64);
  if (strncmp(buf, "-4294967296", sizeof(buf)))
    return 3;

  snprintf(buf, sizeof(buf), "0o%" PRIo64, ui64);
  if (strncmp(buf, "0o40000000000", sizeof(buf)))
    return 4;

  snprintf(buf, sizeof(buf), "0x%" PRIx64, ui64);
  if (strncmp(buf, "0x100000000", sizeof(buf)))
    return 5;

  snprintf(buf, sizeof(buf), "%" PRIu64, ui64);
  if (strncmp(buf, "4294967296", sizeof(buf)))
    return 6;

  return 0;
}
        
    
_ACEOF
if ac_fn_c_try_run "$LINENO"
then :
  
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
    
else $as_nop
  
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
        as_fn_error $? "The snprintf(3) implementation in this libc is not suitable,
tcpdump would not work correctly even if it managed to compile." "$LINENO" 5
    
fi
rm -f core *.core core.conftest.* gmon.out bb.out conftest$ac_exeext \
  conftest.$ac_objext conftest.beam conftest.$ac_ext
fi


{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for main in -lrpc" >&5
printf %s "checking for main in -lrpc... " >&6; }
if test ${ac_cv_lib_rpc_main+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lrpc  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */


int
main (void)
{
return main ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_rpc_main=yes
else $as_nop
  ac_cv_lib_rpc_main=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_rpc_main" >&5
printf "%s\n" "$ac_cv_lib_rpc_main" >&6; }
if test "x$ac_cv_lib_rpc_main" = xyes
then :
  printf "%s\n" "@%:@define HAVE_LIBRPC 1" >>confdefs.h

  LIBS="-lrpc $LIBS"

fi
		
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for library containing getrpcbynumber" >&5
printf %s "checking for library containing getrpcbynumber... " >&6; }
if test ${ac_cv_search_getrpcbynumber+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_func_search_save_LIBS=$LIBS
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char getrpcbynumber ();
int
main (void)
{
return getrpcbynumber ();
  ;
  return 0;
}
_ACEOF
for ac_lib in '' nsl
do
  if test -z "$ac_lib"; then
    ac_res="none required"
  else
    ac_res=-l$ac_lib
    LIBS="-l$ac_lib  $ac_func_search_save_LIBS"
  fi
  if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_search_getrpcbynumber=$ac_res
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext
  if test ${ac_cv_search_getrpcbynumber+y}
then :
  break
fi
done
if test ${ac_cv_search_getrpcbynumber+y}
then :
  
else $as_nop
  ac_cv_search_getrpcbynumber=no
fi
rm conftest.$ac_ext
LIBS=$ac_func_search_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_search_getrpcbynumber" >&5
printf "%s\n" "$ac_cv_search_getrpcbynumber" >&6; }
ac_res=$ac_cv_search_getrpcbynumber
if test "$ac_res" != no
then :
  test "$ac_res" = "none required" || LIBS="$ac_res $LIBS"
  
printf "%s\n" "@%:@define HAVE_GETRPCBYNUMBER 1" >>confdefs.h

fi


{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for grep that handles long lines and -e" >&5
printf %s "checking for grep that handles long lines and -e... " >&6; }
if test ${ac_cv_path_GREP+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -z "$GREP"; then
  ac_path_GREP_found=false
  # Loop through the user's path and test for each of PROGNAME-LIST
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH$PATH_SEPARATOR/usr/xpg4/bin
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_prog in grep ggrep
   do
    for ac_exec_ext in '' $ac_executable_extensions; do
      ac_path_GREP="$as_dir$ac_prog$ac_exec_ext"
      as_fn_executable_p "$ac_path_GREP" || continue
# Check for GNU ac_path_GREP and select it if it is found.
  # Check for GNU $ac_path_GREP
case `"$ac_path_GREP" --version 2>&1` in
*GNU*)
  ac_cv_path_GREP="$ac_path_GREP" ac_path_GREP_found=:;;
*)
  ac_count=0
  printf %s 0123456789 >"conftest.in"
  while :
  do
    cat "conftest.in" "conftest.in" >"conftest.tmp"
    mv "conftest.tmp" "conftest.in"
    cp "conftest.in" "conftest.nl"
    printf "%s\n" 'GREP' >> "conftest.nl"
    "$ac_path_GREP" -e 'GREP$' -e '-(cannot match)-' < "conftest.nl" >"conftest.out" 2>/dev/null || break
    diff "conftest.out" "conftest.nl" >/dev/null 2>&1 || break
    as_fn_arith $ac_count + 1 && ac_count=$as_val
    if test $ac_count -gt ${ac_path_GREP_max-0}; then
      # Best one so far, save it but keep looking for a better one
      ac_cv_path_GREP="$ac_path_GREP"
      ac_path_GREP_max=$ac_count
    fi
    # 10*(2^10) chars as input seems more than enough
    test $ac_count -gt 10 && break
  done
  rm -f conftest.in conftest.tmp conftest.nl conftest.out;;
esac

      $ac_path_GREP_found && break 3
    done
  done
  done
IFS=$as_save_IFS
  if test -z "$ac_cv_path_GREP"; then
    as_fn_error $? "no acceptable grep could be found in $PATH$PATH_SEPARATOR/usr/xpg4/bin" "$LINENO" 5
  fi
else
  ac_cv_path_GREP=$GREP
fi

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_path_GREP" >&5
printf "%s\n" "$ac_cv_path_GREP" >&6; }
 GREP="$ac_cv_path_GREP"
 

{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for egrep" >&5
printf %s "checking for egrep... " >&6; }
if test ${ac_cv_path_EGREP+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if echo a | $GREP -E '(a|b)' >/dev/null 2>&1
   then ac_cv_path_EGREP="$GREP -E"
   else
     if test -z "$EGREP"; then
  ac_path_EGREP_found=false
  # Loop through the user's path and test for each of PROGNAME-LIST
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH$PATH_SEPARATOR/usr/xpg4/bin
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_prog in egrep
   do
    for ac_exec_ext in '' $ac_executable_extensions; do
      ac_path_EGREP="$as_dir$ac_prog$ac_exec_ext"
      as_fn_executable_p "$ac_path_EGREP" || continue
# Check for GNU ac_path_EGREP and select it if it is found.
  # Check for GNU $ac_path_EGREP
case `"$ac_path_EGREP" --version 2>&1` in
*GNU*)
  ac_cv_path_EGREP="$ac_path_EGREP" ac_path_EGREP_found=:;;
*)
  ac_count=0
  printf %s 0123456789 >"conftest.in"
  while :
  do
    cat "conftest.in" "conftest.in" >"conftest.tmp"
    mv "conftest.tmp" "conftest.in"
    cp "conftest.in" "conftest.nl"
    printf "%s\n" 'EGREP' >> "conftest.nl"
    "$ac_path_EGREP" 'EGREP$' < "conftest.nl" >"conftest.out" 2>/dev/null || break
    diff "conftest.out" "conftest.nl" >/dev/null 2>&1 || break
    as_fn_arith $ac_count + 1 && ac_count=$as_val
    if test $ac_count -gt ${ac_path_EGREP_max-0}; then
      # Best one so far, save it but keep looking for a better one
      ac_cv_path_EGREP="$ac_path_EGREP"
      ac_path_EGREP_max=$ac_count
    fi
    # 10*(2^10) chars as input seems more than enough
    test $ac_count -gt 10 && break
  done
  rm -f conftest.in conftest.tmp conftest.nl conftest.out;;
esac

      $ac_path_EGREP_found && break 3
    done
  done
  done
IFS=$as_save_IFS
  if test -z "$ac_cv_path_EGREP"; then
    as_fn_error $? "no acceptable egrep could be found in $PATH$PATH_SEPARATOR/usr/xpg4/bin" "$LINENO" 5
  fi
else
  ac_cv_path_EGREP=$EGREP
fi

   fi
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_path_EGREP" >&5
printf "%s\n" "$ac_cv_path_EGREP" >&6; }
 EGREP="$ac_cv_path_EGREP"
 


    
    
    libpcap=FAIL
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether to look for a local libpcap" >&5
printf %s "checking whether to look for a local libpcap... " >&6; }
    @%:@ Check whether --enable-local-libpcap was given.
if test ${enable_local_libpcap+y}
then :
  enableval=$enable_local_libpcap; 
else $as_nop
  enableval=yes
fi

    case "$enableval" in

    no)
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
        #
        # Don't look for a local libpcap.
        #
        using_local_libpcap=no
        ;;

    *)
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
        #
        # Look for a local pcap library.
        #
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for local pcap library" >&5
printf %s "checking for local pcap library... " >&6; }
        lastdir=FAIL
        places=`ls $srcdir/.. | sed -e 's,/$,,' -e "s,^,$srcdir/../," | \
            $EGREP '/libpcap-[0-9]+\.[0-9]+(\.[0-9]*)?([ab][0-9]*|-PRE-GIT|rc.)?$'`
        places2=`ls .. | sed -e 's,/$,,' -e "s,^,../," | \
            $EGREP '/libpcap-[0-9]+\.[0-9]+(\.[0-9]*)?([ab][0-9]*|-PRE-GIT|rc.)?$'`
        for dir in $places $srcdir/../libpcap ../libpcap $srcdir/libpcap $places2 ; do
            basedir=`echo $dir | sed -e 's/[ab][0-9]*$//' | \
                sed -e 's/-PRE-GIT$//' `
            if test $lastdir = $basedir ; then
                                continue;
            fi
            lastdir=$dir
            if test -r $dir/libpcap.a ; then
                libpcap=$dir/libpcap.a
                local_pcap_dir=$dir
                            fi
        done
        if test $libpcap = FAIL ; then
            #
            # We didn't find a local libpcap.
            #
            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: not found" >&5
printf "%s\n" "not found" >&6; }
            using_local_libpcap=no;
        else
            #
            # We found a local libpcap.
            #
            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $libpcap" >&5
printf "%s\n" "$libpcap" >&6; }
            using_local_libpcap=yes
        fi
        ;;
    esac

    if test $using_local_libpcap = no ; then
        #
        # We didn't find a local libpcap.
        # First, try finding it with pkg-config.
        #
        
if test -n "$PKG_CONFIG"; then
            
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for libpcap with pkg-config" >&5
printf %s "checking for libpcap with pkg-config... " >&6; }
    if { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$PKG_CONFIG --exists --print-errors \"libpcap\""; } >&5
  ($PKG_CONFIG --exists --print-errors "libpcap") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }; then
	#
	# The package was found, so try to get its C flags and
	# libraries.
	#
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: found" >&5
printf "%s\n" "found" >&6; }
	if test ! -n "$LIBPCAP_CFLAGS"; then
    LIBPCAP_CFLAGS=`$PKG_CONFIG --cflags "libpcap" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --cflags "libpcap" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --cflags "libpcap" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --cflags \"libpcap\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
	if test ! -n "$LIBPCAP_LIBS"; then
    LIBPCAP_LIBS=`$PKG_CONFIG --libs "libpcap" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --libs "libpcap" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --libs "libpcap" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --libs \"libpcap\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
	if test ! -n "$LIBPCAP_LIBS_STATIC"; then
    LIBPCAP_LIBS_STATIC=`$PKG_CONFIG --libs --static "libpcap" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --libs --static "libpcap" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --libs --static "libpcap" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --libs --static \"libpcap\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
        
            #
            # We found it; use the results as configuration information
            # for libpcap.
            #
            V_INCLS="$LIBPCAP_CFLAGS $V_INCLS"
            libpcap="$LIBPCAP_LIBS"
        
    else
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: not found" >&5
printf "%s\n" "not found" >&6; }
        
            #
            # We didn't find it; look for an installed pcap-config.
            #
            if test -n "$ac_tool_prefix"; then
  # Extract the first word of "${ac_tool_prefix}pcap-config", so it can be a program name with args.
set dummy ${ac_tool_prefix}pcap-config; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_PCAP_CONFIG+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $PCAP_CONFIG in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_PCAP_CONFIG="$PCAP_CONFIG" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_PCAP_CONFIG="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
PCAP_CONFIG=$ac_cv_path_PCAP_CONFIG
if test -n "$PCAP_CONFIG"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $PCAP_CONFIG" >&5
printf "%s\n" "$PCAP_CONFIG" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$ac_cv_path_PCAP_CONFIG"; then
  ac_pt_PCAP_CONFIG=$PCAP_CONFIG
  # Extract the first word of "pcap-config", so it can be a program name with args.
set dummy pcap-config; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_ac_pt_PCAP_CONFIG+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $ac_pt_PCAP_CONFIG in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_ac_pt_PCAP_CONFIG="$ac_pt_PCAP_CONFIG" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_ac_pt_PCAP_CONFIG="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
ac_pt_PCAP_CONFIG=$ac_cv_path_ac_pt_PCAP_CONFIG
if test -n "$ac_pt_PCAP_CONFIG"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_pt_PCAP_CONFIG" >&5
printf "%s\n" "$ac_pt_PCAP_CONFIG" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

  if test "x$ac_pt_PCAP_CONFIG" = x; then
    PCAP_CONFIG=""
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    PCAP_CONFIG=$ac_pt_PCAP_CONFIG
  fi
else
  PCAP_CONFIG="$ac_cv_path_PCAP_CONFIG"
fi

            if test -n "$PCAP_CONFIG" ; then
                #
                # Found - use it to get the include flags for
                # libpcap and the flags to link with libpcap.
                #
                # If this is a vendor-supplied pcap-config, which
                # we define as being "a pcap-config in /usr/bin
                # or /usr/ccs/bin" (the latter is for Solaris and
                # Sun/Oracle Studio), there are some issues.  Work
                # around them.
                #
                if test \( "$PCAP_CONFIG" = "/usr/bin/pcap-config" \) -o \
                        \( "$PCAP_CONFIG" = "/usr/ccs/bin/pcap-config" \) ; then
                    #
                    # It's vendor-supplied.
                    #
                    case "$host_os" in

                    darwin*)
                        #
                        # This is macOS or another Darwin-based OS.
                        #
                        # That means that /usr/bin/pcap-config it
                        # may provide -I/usr/local/include with --cflags
                        # and -L/usr/local/lib with --libs, rather than
                        # pointing to the OS-supplied library and
                        # Xcode-supplied headers.  Remember that, so we
                        # ignore those values.
                        #
                        _broken_apple_pcap_config=yes

                        #
                        # Furthermore:
                        #
                        # macOS Sonoma's libpcap includes stub versions
                        # of the remote-capture APIs.  They are exported
                        # as "weakly linked symbols".
                        #
                        # Xcode 15 offers only a macOS Sonoma SDK, which
                        # has a .tbd file for libpcap that claims it
                        # includes those APIs.  (Newer versions of macOS
                        # don't provide the system shared libraries,
                        # they only provide the dyld shared cache
                        # containing those libraries, so the OS provides
                        # SDKs that include a .tbd file to use when
                        # linking.)
                        #
                        # This means that AC_CHECK_FUNCS() will think
                        # that the remote-capture APIs are present,
                        # including pcap_open() and
                        # pcap_findalldevs_ex().
                        #
                        # However, they are *not* present in macOS
                        # Ventura and earlier, which means that building
                        # on Ventura with Xcode 15 produces executables
                        # that fail to start because one of those APIs
                        # isn't found in the system libpcap.
                        #
                        # Protecting calls to those APIs with
                        # __builtin_available() does not appear to
                        # prevent this, for some unknown reason, and it
                        # doesn't even allow the program to compile with
                        # versions of Xcode prior to Xcode 15, as the
                        # pcap.h file doesn't specify minimum OS
                        # versions for those functions.
                        #
                        # Given all that, and given that the versions of
                        # the remote-capture APIs in Sonoma are stubs
                        # that always fail, there doesn't seem to be any
                        # point in checking for pcap_open() if we're
                        # linking against the Apple libpcap.
                        #
                        # However, if we're *not* linking against the
                        # Apple libpcap, we should check for it, so that
                        # we can use it if it's present.
                        #
                        # We know this is macOS and that we're using
                        # the system-provided pcap-config to find
                        # libpcap, so we know it'll be the system
                        # libpcap, and note that we should not search
                        # for remote-capture APIs.
                        #
                        _dont_check_for_remote_apis=yes
                        ;;

                    solaris*)
                        #
                        # This is Solaris 2 or later, i.e. SunOS 5.x.
                        #
                        # At least on Solaris 11; there's /usr/bin/pcap-config,
                        # which reports -L/usr/lib with --libs, causing
                        # the 32-bit libraries to be found, and there's
                        # /usr/bin/{64bitarch}/pcap-config, where {64bitarch}
                        # is a name for the 64-bit version of the instruction
                        # set, which reports -L /usr/lib/{64bitarch}, causing
                        # the 64-bit libraries to be found.
                        #
                        # So if we're building 64-bit targets, we replace
                        # PCAP_CONFIG with /usr/bin/{64bitarch}; we get
                        # {64bitarch} as the output of "isainfo -n".
                        #
                        # Are we building 32-bit or 64-bit?  Get the
                        # size of void *, and check that.
                        #
                        # The cast to long int works around a bug in the HP C Compiler
# version HP92453-01 B.11.11.23709.GP, which incorrectly rejects
# declarations like `int a3[[(sizeof (unsigned char)) >= 0]];'.
# This bug is HP SR number 8606223364.
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking size of void *" >&5
printf %s "checking size of void *... " >&6; }
if test ${ac_cv_sizeof_void_p+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if ac_fn_c_compute_int "$LINENO" "(long int) (sizeof (void *))" "ac_cv_sizeof_void_p"        "$ac_includes_default"
then :
  
else $as_nop
  if test "$ac_cv_type_void_p" = yes; then
     { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error 77 "cannot compute sizeof (void *)
See \`config.log' for more details" "$LINENO" 5; }
   else
     ac_cv_sizeof_void_p=0
   fi
fi

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_sizeof_void_p" >&5
printf "%s\n" "$ac_cv_sizeof_void_p" >&6; }



printf "%s\n" "@%:@define SIZEOF_VOID_P $ac_cv_sizeof_void_p" >>confdefs.h


                        if test ac_cv_sizeof_void_p -eq 8 ; then
                            isainfo_output=`isainfo -n`
                            if test ! -z "$isainfo_output" ; then
                                #
                                # Success - change PCAP_CONFIG.
                                #
                                PCAP_CONFIG=`echo $PCAP_CONFIG | sed "s;/bin/;/bin/$isainfo_output/;"`
                            fi
                        fi
                        ;;
                    esac
                fi
                #
                # Please read section 11.6 "Shell Substitutions"
                # in the autoconf manual before doing anything
                # to this that involves quoting.  Especially note
                # the statement "There is just no portable way to use
                # double-quoted strings inside double-quoted back-quoted
                # expressions (pfew!)."
                #
                cflags=`"$PCAP_CONFIG" --cflags`
                #
                # Work around macOS (and probably other Darwin) brokenness,
                # by not adding /usr/local/include if it's from the broken
                # Apple pcap-config.
                #
                if test "$_broken_apple_pcap_config" = "yes" ; then
                    #
                    # Strip -I/usr/local/include with sed.
                    #
                    cflags=`echo $cflags | sed 's;-I/usr/local/include;;'`
                fi
                V_INCLS="$cflags $V_INCLS"
                libpcap=`"$PCAP_CONFIG" --libs`
                #
                # Work around macOS (and probably other Darwin) brokenness,
                # by not adding /usr/local/lib if it's from the broken
                # Apple pcap-config.
                #
                if test "$_broken_apple_pcap_config" = "yes" ; then
                    #
                    # Strip -L/usr/local/lib with sed.
                    #
                    libpcap=`echo $libpcap | sed 's;-L/usr/local/lib;;'`
                fi
            else
                #
                # Not found; look for an installed pcap.
                #
                { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for main in -lpcap" >&5
printf %s "checking for main in -lpcap... " >&6; }
if test ${ac_cv_lib_pcap_main+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lpcap  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */


int
main (void)
{
return main ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_pcap_main=yes
else $as_nop
  ac_cv_lib_pcap_main=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_pcap_main" >&5
printf "%s\n" "$ac_cv_lib_pcap_main" >&6; }
if test "x$ac_cv_lib_pcap_main" = xyes
then :
  libpcap="-lpcap"
fi

                if test $libpcap = FAIL ; then
                    as_fn_error $? "see the INSTALL.md file for more info" "$LINENO" 5
                fi
                                                                                                                                                                                                                                                                                                                                { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for extraneous pcap header directories" >&5
printf %s "checking for extraneous pcap header directories... " >&6; }
                if test \( ! -r /usr/local/include/pcap.h \) -a \
                        \( ! -r /usr/include/pcap.h \); then
                    if test -r /usr/local/include/pcap/pcap.h; then
                        d="/usr/local/include/pcap"
                    elif test -r /usr/include/pcap/pcap.h; then
                        d="/usr/include/pcap"
                    fi
                fi
                if test -z "$d" ; then
                    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: not found" >&5
printf "%s\n" "not found" >&6; }
                else
                    V_INCLS="-I$d $V_INCLS"
                    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: found -- -I$d added" >&5
printf "%s\n" "found -- -I$d added" >&6; }
                fi
            fi
        
    fi
else
    # No pkg-config, so obviously not found with pkg-config.
    
            #
            # We didn't find it; look for an installed pcap-config.
            #
            if test -n "$ac_tool_prefix"; then
  # Extract the first word of "${ac_tool_prefix}pcap-config", so it can be a program name with args.
set dummy ${ac_tool_prefix}pcap-config; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_PCAP_CONFIG+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $PCAP_CONFIG in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_PCAP_CONFIG="$PCAP_CONFIG" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_PCAP_CONFIG="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
PCAP_CONFIG=$ac_cv_path_PCAP_CONFIG
if test -n "$PCAP_CONFIG"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $PCAP_CONFIG" >&5
printf "%s\n" "$PCAP_CONFIG" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$ac_cv_path_PCAP_CONFIG"; then
  ac_pt_PCAP_CONFIG=$PCAP_CONFIG
  # Extract the first word of "pcap-config", so it can be a program name with args.
set dummy pcap-config; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_ac_pt_PCAP_CONFIG+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $ac_pt_PCAP_CONFIG in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_ac_pt_PCAP_CONFIG="$ac_pt_PCAP_CONFIG" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_ac_pt_PCAP_CONFIG="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
ac_pt_PCAP_CONFIG=$ac_cv_path_ac_pt_PCAP_CONFIG
if test -n "$ac_pt_PCAP_CONFIG"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_pt_PCAP_CONFIG" >&5
printf "%s\n" "$ac_pt_PCAP_CONFIG" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

  if test "x$ac_pt_PCAP_CONFIG" = x; then
    PCAP_CONFIG=""
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    PCAP_CONFIG=$ac_pt_PCAP_CONFIG
  fi
else
  PCAP_CONFIG="$ac_cv_path_PCAP_CONFIG"
fi

            if test -n "$PCAP_CONFIG" ; then
                #
                # Found - use it to get the include flags for
                # libpcap and the flags to link with libpcap.
                #
                # If this is a vendor-supplied pcap-config, which
                # we define as being "a pcap-config in /usr/bin
                # or /usr/ccs/bin" (the latter is for Solaris and
                # Sun/Oracle Studio), there are some issues.  Work
                # around them.
                #
                if test \( "$PCAP_CONFIG" = "/usr/bin/pcap-config" \) -o \
                        \( "$PCAP_CONFIG" = "/usr/ccs/bin/pcap-config" \) ; then
                    #
                    # It's vendor-supplied.
                    #
                    case "$host_os" in

                    darwin*)
                        #
                        # This is macOS or another Darwin-based OS.
                        #
                        # That means that /usr/bin/pcap-config it
                        # may provide -I/usr/local/include with --cflags
                        # and -L/usr/local/lib with --libs, rather than
                        # pointing to the OS-supplied library and
                        # Xcode-supplied headers.  Remember that, so we
                        # ignore those values.
                        #
                        _broken_apple_pcap_config=yes

                        #
                        # Furthermore:
                        #
                        # macOS Sonoma's libpcap includes stub versions
                        # of the remote-capture APIs.  They are exported
                        # as "weakly linked symbols".
                        #
                        # Xcode 15 offers only a macOS Sonoma SDK, which
                        # has a .tbd file for libpcap that claims it
                        # includes those APIs.  (Newer versions of macOS
                        # don't provide the system shared libraries,
                        # they only provide the dyld shared cache
                        # containing those libraries, so the OS provides
                        # SDKs that include a .tbd file to use when
                        # linking.)
                        #
                        # This means that AC_CHECK_FUNCS() will think
                        # that the remote-capture APIs are present,
                        # including pcap_open() and
                        # pcap_findalldevs_ex().
                        #
                        # However, they are *not* present in macOS
                        # Ventura and earlier, which means that building
                        # on Ventura with Xcode 15 produces executables
                        # that fail to start because one of those APIs
                        # isn't found in the system libpcap.
                        #
                        # Protecting calls to those APIs with
                        # __builtin_available() does not appear to
                        # prevent this, for some unknown reason, and it
                        # doesn't even allow the program to compile with
                        # versions of Xcode prior to Xcode 15, as the
                        # pcap.h file doesn't specify minimum OS
                        # versions for those functions.
                        #
                        # Given all that, and given that the versions of
                        # the remote-capture APIs in Sonoma are stubs
                        # that always fail, there doesn't seem to be any
                        # point in checking for pcap_open() if we're
                        # linking against the Apple libpcap.
                        #
                        # However, if we're *not* linking against the
                        # Apple libpcap, we should check for it, so that
                        # we can use it if it's present.
                        #
                        # We know this is macOS and that we're using
                        # the system-provided pcap-config to find
                        # libpcap, so we know it'll be the system
                        # libpcap, and note that we should not search
                        # for remote-capture APIs.
                        #
                        _dont_check_for_remote_apis=yes
                        ;;

                    solaris*)
                        #
                        # This is Solaris 2 or later, i.e. SunOS 5.x.
                        #
                        # At least on Solaris 11; there's /usr/bin/pcap-config,
                        # which reports -L/usr/lib with --libs, causing
                        # the 32-bit libraries to be found, and there's
                        # /usr/bin/{64bitarch}/pcap-config, where {64bitarch}
                        # is a name for the 64-bit version of the instruction
                        # set, which reports -L /usr/lib/{64bitarch}, causing
                        # the 64-bit libraries to be found.
                        #
                        # So if we're building 64-bit targets, we replace
                        # PCAP_CONFIG with /usr/bin/{64bitarch}; we get
                        # {64bitarch} as the output of "isainfo -n".
                        #
                        # Are we building 32-bit or 64-bit?  Get the
                        # size of void *, and check that.
                        #
                        # The cast to long int works around a bug in the HP C Compiler
# version HP92453-01 B.11.11.23709.GP, which incorrectly rejects
# declarations like `int a3[[(sizeof (unsigned char)) >= 0]];'.
# This bug is HP SR number 8606223364.
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking size of void *" >&5
printf %s "checking size of void *... " >&6; }
if test ${ac_cv_sizeof_void_p+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if ac_fn_c_compute_int "$LINENO" "(long int) (sizeof (void *))" "ac_cv_sizeof_void_p"        "$ac_includes_default"
then :
  
else $as_nop
  if test "$ac_cv_type_void_p" = yes; then
     { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error 77 "cannot compute sizeof (void *)
See \`config.log' for more details" "$LINENO" 5; }
   else
     ac_cv_sizeof_void_p=0
   fi
fi

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_sizeof_void_p" >&5
printf "%s\n" "$ac_cv_sizeof_void_p" >&6; }



printf "%s\n" "@%:@define SIZEOF_VOID_P $ac_cv_sizeof_void_p" >>confdefs.h


                        if test ac_cv_sizeof_void_p -eq 8 ; then
                            isainfo_output=`isainfo -n`
                            if test ! -z "$isainfo_output" ; then
                                #
                                # Success - change PCAP_CONFIG.
                                #
                                PCAP_CONFIG=`echo $PCAP_CONFIG | sed "s;/bin/;/bin/$isainfo_output/;"`
                            fi
                        fi
                        ;;
                    esac
                fi
                #
                # Please read section 11.6 "Shell Substitutions"
                # in the autoconf manual before doing anything
                # to this that involves quoting.  Especially note
                # the statement "There is just no portable way to use
                # double-quoted strings inside double-quoted back-quoted
                # expressions (pfew!)."
                #
                cflags=`"$PCAP_CONFIG" --cflags`
                #
                # Work around macOS (and probably other Darwin) brokenness,
                # by not adding /usr/local/include if it's from the broken
                # Apple pcap-config.
                #
                if test "$_broken_apple_pcap_config" = "yes" ; then
                    #
                    # Strip -I/usr/local/include with sed.
                    #
                    cflags=`echo $cflags | sed 's;-I/usr/local/include;;'`
                fi
                V_INCLS="$cflags $V_INCLS"
                libpcap=`"$PCAP_CONFIG" --libs`
                #
                # Work around macOS (and probably other Darwin) brokenness,
                # by not adding /usr/local/lib if it's from the broken
                # Apple pcap-config.
                #
                if test "$_broken_apple_pcap_config" = "yes" ; then
                    #
                    # Strip -L/usr/local/lib with sed.
                    #
                    libpcap=`echo $libpcap | sed 's;-L/usr/local/lib;;'`
                fi
            else
                #
                # Not found; look for an installed pcap.
                #
                { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for main in -lpcap" >&5
printf %s "checking for main in -lpcap... " >&6; }
if test ${ac_cv_lib_pcap_main+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lpcap  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */


int
main (void)
{
return main ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_pcap_main=yes
else $as_nop
  ac_cv_lib_pcap_main=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_pcap_main" >&5
printf "%s\n" "$ac_cv_lib_pcap_main" >&6; }
if test "x$ac_cv_lib_pcap_main" = xyes
then :
  libpcap="-lpcap"
fi

                if test $libpcap = FAIL ; then
                    as_fn_error $? "see the INSTALL.md file for more info" "$LINENO" 5
                fi
                                                                                                                                                                                                                                                                                                                                { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for extraneous pcap header directories" >&5
printf %s "checking for extraneous pcap header directories... " >&6; }
                if test \( ! -r /usr/local/include/pcap.h \) -a \
                        \( ! -r /usr/include/pcap.h \); then
                    if test -r /usr/local/include/pcap/pcap.h; then
                        d="/usr/local/include/pcap"
                    elif test -r /usr/include/pcap/pcap.h; then
                        d="/usr/include/pcap"
                    fi
                fi
                if test -z "$d" ; then
                    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: not found" >&5
printf "%s\n" "not found" >&6; }
                else
                    V_INCLS="-I$d $V_INCLS"
                    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: found -- -I$d added" >&5
printf "%s\n" "found -- -I$d added" >&6; }
                fi
            fi
        
fi

    else
        #
        # We found a local libpcap.  Add it to the dependencies for
        # tcpdump.
        #
        V_PCAPDEP=$libpcap

        #
        # Look for its pcap-config script.
        #
        # Extract the first word of "pcap-config", so it can be a program name with args.
set dummy pcap-config; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_PCAP_CONFIG+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $PCAP_CONFIG in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_PCAP_CONFIG="$PCAP_CONFIG" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $local_pcap_dir
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_PCAP_CONFIG="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
PCAP_CONFIG=$ac_cv_path_PCAP_CONFIG
if test -n "$PCAP_CONFIG"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $PCAP_CONFIG" >&5
printf "%s\n" "$PCAP_CONFIG" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi



        if test -n "$PCAP_CONFIG"; then
            #
            # We don't want its --cflags or --libs output, because
            # those presume it's installed.  For the C compiler flags,
            # we add the source directory for the local libpcap, so
            # we pick up its header files.
            #
            # We do, however, want its additional libraries, as required
            # when linking statically, because it makes calls to
            # routines in those libraries, so we'll need to link with
            # them, because we'll be linking statically with it.
            #
            # If it supports --static-pcap-only. use that, as we will be
            # linking with a static libpcap but won't be linking
            # statically with any of the libraries on which it depends;
            # those libraries might not even have static versions
            # installed.
            #
            # That means we need to find out the libraries on which
            # libpcap directly depends, so we can link with them, but we
            # don't need to link with the libraries on which those
            # libraries depend as, on all UN*Xes with which I'm
            # familiar, the libraries on which a shared library depends
            # are stored in the library and are automatically loaded by
            # the run-time linker, without the executable having to be
            # linked with those libraries.  (This allows a library to be
            # changed to depend on more libraries without breaking that
            # library's ABI.)
            #
            # The only way to test for that support is to see if the
            # script contains the string "static-pcap-only"; we can't
            # try using that flag and checking for errors, as the
            # versions of the script that didn't have that flag wouldn't
            # report or return an error for an unsupported command-line
            # flag.  Those older versions provided, with --static, only
            # the libraries on which libpcap depends, not the
            # dependencies of those libraries; the versions with
            # --static-pcap-only provide all the dependencies with
            # --static, for the benefit of programs that are completely
            # statically linked, and provide only the direct
            # dependencies with --static-pcap-only.
            #
            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether $PCAP_CONFIG supports --static-pcap-only" >&5
printf %s "checking whether $PCAP_CONFIG supports --static-pcap-only... " >&6; }
            #
            # On Solaris 10, only /usr/xpg4/bin/grep supports the -s and
            # -q flags, so we silence it by sending the standard output
            # and error to /dev/null. The autoconf documentation
            # recmmends avoiding those options in favor of redirecting
            # to /dev/null.
            #
            if grep "static-pcap-only" "$PCAP_CONFIG" >/dev/null 2>&1
            then
                { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
                static_opt="--static-pcap-only"
            else
                { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
                static_opt="--static"
            fi
            V_INCLS="-I$local_pcap_dir $V_INCLS"
            additional_libs=`"$PCAP_CONFIG" $static_opt --additional-libs`
            libpcap="$libpcap $additional_libs"
        else
            #
            # It doesn't have a pcap-config script.
            # Make sure it has a pcap.h file.
            #
            places=`ls $srcdir/.. | sed -e 's,/$,,' -e "s,^,$srcdir/../," | \
                $EGREP '/libpcap-[0-9]*.[0-9]*(.[0-9]*)?([ab][0-9]*)?$'`
            places2=`ls .. | sed -e 's,/$,,' -e "s,^,../," | \
                $EGREP '/libpcap-[0-9]*.[0-9]*(.[0-9]*)?([ab][0-9]*)?$'`
            pcapH=FAIL
            if test -r $local_pcap_dir/pcap.h; then
                pcapH=$local_pcap_dir
            else
                for dir in $places $srcdir/../libpcap ../libpcap $srcdir/libpcap $places2 ; do
                    if test -r $dir/pcap.h ; then
                        pcapH=$dir
                    fi
                done
            fi

            if test $pcapH = FAIL ; then
                as_fn_error $? "cannot find pcap.h: see the INSTALL.md file" "$LINENO" 5
            fi

            #
            # Force the compiler to look for header files in the
            # directory containing pcap.h.
            #
            V_INCLS="-I$pcapH $V_INCLS"
        fi
    fi

    if test -z "$PKG_CONFIG" -a -z "$PCAP_CONFIG"; then
        #
        # We don't have pkg-config or pcap-config; find out any additional
        # link flags we need.  (If we have pkg-config or pcap-config, we
        # assume it tells us what we need.)
        #
        case "$host_os" in

        aix*)
            #
            # If libpcap is DLPI-based, we have to use /lib/pse.exp if
            # present, as we use the STREAMS routines.
            #
            # (XXX - true only if we're linking with a static libpcap?)
            #
            pseexe="/lib/pse.exp"
            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $pseexe" >&5
printf %s "checking for $pseexe... " >&6; }
            if test -f $pseexe ; then
                { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
                LIBS="$LIBS -I:$pseexe"
            fi

            #
            # If libpcap is BPF-based, we need "-lodm" and "-lcfg", as
            # we use them to load the BPF module.
            #
            # (XXX - true only if we're linking with a static libpcap?)
            #
            LIBS="$LIBS -lodm -lcfg"
            ;;

	solaris*)
            # libdlpi is needed for Solaris 11 and later.
            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for dlpi_walk in -ldlpi" >&5
printf %s "checking for dlpi_walk in -ldlpi... " >&6; }
if test ${ac_cv_lib_dlpi_dlpi_walk+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-ldlpi -L/lib $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char dlpi_walk ();
int
main (void)
{
return dlpi_walk ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_dlpi_dlpi_walk=yes
else $as_nop
  ac_cv_lib_dlpi_dlpi_walk=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_dlpi_dlpi_walk" >&5
printf "%s\n" "$ac_cv_lib_dlpi_dlpi_walk" >&6; }
if test "x$ac_cv_lib_dlpi_dlpi_walk" = xyes
then :
  LIBS="$LIBS -ldlpi" LDFLAGS="-L/lib $LDFLAGS"
fi

            ;;
        esac
    fi

    LIBS="$libpcap $LIBS"

                                    ac_fn_c_check_func "$LINENO" "pcap_loop" "ac_cv_func_pcap_loop"
if test "x$ac_cv_func_pcap_loop" = xyes
then :
  
else $as_nop
  
        as_fn_error $? "
For some reason, linking with libpcap failed.

This may be a result of the way you have configured the build.  For
example, you may have specified a static build with a version of libpcap
that cannot be linked statically, or you may have specified something
that would cause a 32-bit tcpdump to be linked with a 64-bit libpcap or
vice versa.

Please check the config.log file in the build directory.  It should have
a line that says \"checking for pcap_loop\"; following that will be a
command that links a small test program with libpcap, and following that
should be error messages indicating why the attempt to link failed.

Then check the doc/README.{platform}.md file for the platform for which
you're building, to see if it gives any advice." "$LINENO" 5
    
fi



#
# Check for these after AC_LBL_LIBPCAP, so we link with the appropriate
# libraries (e.g., "-lsocket -lnsl" on Solaris).
#
# You are in a twisty little maze of UN*Xes, all different.
# Some might not have ether_ntohost().
# Some might have it and declare it in <net/ethernet.h>.
# Some might have it and declare it in <netinet/ether.h>
# Some might have it and declare it in <sys/ethernet.h>.
# Some might have it and declare it in <arpa/inet.h>.
# Some might have it and declare it in <netinet/if_ether.h>.
# Some might have it and not declare it in any header file.
#
# Before you is a C compiler.
#
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $CC options needed to detect all undeclared functions" >&5
printf %s "checking for $CC options needed to detect all undeclared functions... " >&6; }
if test ${ac_cv_c_undeclared_builtin_options+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_save_CFLAGS=$CFLAGS
   ac_cv_c_undeclared_builtin_options='cannot detect'
   for ac_arg in '' -fno-builtin; do
     CFLAGS="$ac_save_CFLAGS $ac_arg"
     # This test program should *not* compile successfully.
     cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{
(void) strchr;
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
else $as_nop
  # This test program should compile successfully.
        # No library function is consistently available on
        # freestanding implementations, so test against a dummy
        # declaration.  Include always-available headers on the
        # off chance that they somehow elicit warnings.
        cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <float.h>
#include <limits.h>
#include <stdarg.h>
#include <stddef.h>
extern void ac_decl (int, char *);

int
main (void)
{
(void) ac_decl (0, (char *) 0);
  (void) ac_decl;

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  if test x"$ac_arg" = x
then :
  ac_cv_c_undeclared_builtin_options='none needed'
else $as_nop
  ac_cv_c_undeclared_builtin_options=$ac_arg
fi
          break
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
    done
    CFLAGS=$ac_save_CFLAGS
  
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_c_undeclared_builtin_options" >&5
printf "%s\n" "$ac_cv_c_undeclared_builtin_options" >&6; }
  case $ac_cv_c_undeclared_builtin_options in @%:@(
  'cannot detect') :
    { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error $? "cannot make $CC report undeclared builtins
See \`config.log' for more details" "$LINENO" 5; } ;; @%:@(
  'none needed') :
    ac_c_undeclared_builtin_options='' ;; @%:@(
  *) :
    ac_c_undeclared_builtin_options=$ac_cv_c_undeclared_builtin_options ;;
esac


  for ac_func in ether_ntohost
do :
  ac_fn_c_check_func "$LINENO" "ether_ntohost" "ac_cv_func_ether_ntohost"
if test "x$ac_cv_func_ether_ntohost" = xyes
then :
  printf "%s\n" "@%:@define HAVE_ETHER_NTOHOST 1" >>confdefs.h
 
	#
	# OK, we have ether_ntohost().  Is it declared in <net/ethernet.h>?
	#
	# This test fails if we don't have <net/ethernet.h> or if we do
	# but it doesn't declare ether_ntohost().
	#
	ac_fn_check_decl "$LINENO" "ether_ntohost" "ac_cv_have_decl_ether_ntohost" "
#include <net/ethernet.h>
	    
" "$ac_c_undeclared_builtin_options" "CFLAGS"
if test "x$ac_cv_have_decl_ether_ntohost" = xyes
then :
  
		
printf "%s\n" "@%:@define NET_ETHERNET_H_DECLARES_ETHER_NTOHOST 1" >>confdefs.h

	    
fi
	#
	# Did that succeed?
	#
	if test "$ac_cv_have_decl_ether_ntohost" != yes; then
		#
		# No, how about <netinet/ether.h>, as on Linux?
		#
		# This test fails if we don't have <netinet/ether.h>
		# or if we do but it doesn't declare ether_ntohost().
		#
		# Unset ac_cv_have_decl_ether_ntohost so we don't
		# treat the previous failure as a cached value and
		# suppress the next test.
		#
		unset ac_cv_have_decl_ether_ntohost
		ac_fn_check_decl "$LINENO" "ether_ntohost" "ac_cv_have_decl_ether_ntohost" "
#include <netinet/ether.h>
		    
" "$ac_c_undeclared_builtin_options" "CFLAGS"
if test "x$ac_cv_have_decl_ether_ntohost" = xyes
then :
  
			
printf "%s\n" "@%:@define NETINET_ETHER_H_DECLARES_ETHER_NTOHOST 1" >>confdefs.h

		    
fi
	fi
	#
	# Did that succeed?
	#
	if test "$ac_cv_have_decl_ether_ntohost" != yes; then
		#
		# No, how about <sys/ethernet.h>, as on Solaris 10
		# and later?
		#
		# This test fails if we don't have <sys/ethernet.h>
		# or if we do but it doesn't declare ether_ntohost().
		#
		# Unset ac_cv_have_decl_ether_ntohost so we don't
		# treat the previous failure as a cached value and
		# suppress the next test.
		#
		unset ac_cv_have_decl_ether_ntohost
		ac_fn_check_decl "$LINENO" "ether_ntohost" "ac_cv_have_decl_ether_ntohost" "
#include <sys/ethernet.h>
		    
" "$ac_c_undeclared_builtin_options" "CFLAGS"
if test "x$ac_cv_have_decl_ether_ntohost" = xyes
then :
  
			
printf "%s\n" "@%:@define SYS_ETHERNET_H_DECLARES_ETHER_NTOHOST 1" >>confdefs.h

		    
fi
	fi
	#
	# Did that succeed?
	#
	if test "$ac_cv_have_decl_ether_ntohost" != yes; then
		#
		# No, how about <arpa/inet.h>, as in AIX?
		#
		# This test fails if we don't have <arpa/inet.h>
		# (if we have ether_ntohost(), we should have
		# networking, and if we have networking, we should
		# have <arpa/inet.h>) or if we do but it doesn't
		# declare ether_ntohost().
		#
		# Unset ac_cv_have_decl_ether_ntohost so we don't
		# treat the previous failure as a cached value and
		# suppress the next test.
		#
		unset ac_cv_have_decl_ether_ntohost
		ac_fn_check_decl "$LINENO" "ether_ntohost" "ac_cv_have_decl_ether_ntohost" "
#include <arpa/inet.h>
		    
" "$ac_c_undeclared_builtin_options" "CFLAGS"
if test "x$ac_cv_have_decl_ether_ntohost" = xyes
then :
  
			
printf "%s\n" "@%:@define ARPA_INET_H_DECLARES_ETHER_NTOHOST 1" >>confdefs.h

		    
fi
	fi
	#
	# Did that succeed?
	#
	if test "$ac_cv_have_decl_ether_ntohost" != yes; then
		#
		# No, how about <netinet/if_ether.h>?
		# On some platforms, it requires <net/if.h> and
		# <netinet/in.h>, and we always include it with
		# both of them, so test it with both of them.
		#
		# This test fails if we don't have <netinet/if_ether.h>
		# and the headers we include before it, or if we do but
		# <netinet/if_ether.h> doesn't declare ether_hostton().
		#
		# Unset ac_cv_have_decl_ether_ntohost so we don't
		# treat the previous failure as a cached value and
		# suppress the next test.
		#
		unset ac_cv_have_decl_ether_ntohost
		ac_fn_check_decl "$LINENO" "ether_ntohost" "ac_cv_have_decl_ether_ntohost" "
#include <sys/types.h>
#include <sys/socket.h>
#include <net/if.h>
#include <netinet/in.h>
#include <netinet/if_ether.h>
		    
" "$ac_c_undeclared_builtin_options" "CFLAGS"
if test "x$ac_cv_have_decl_ether_ntohost" = xyes
then :
  
			
printf "%s\n" "@%:@define NETINET_IF_ETHER_H_DECLARES_ETHER_NTOHOST 1" >>confdefs.h

		    
fi
	fi
	#
	# After all that, is ether_ntohost() declared?
	#
	if test "$ac_cv_have_decl_ether_ntohost" = yes; then
		#
		# Yes.
		#
		
printf "%s\n" "@%:@define HAVE_DECL_ETHER_NTOHOST 1" >>confdefs.h

	else
		#
		# No, we'll have to declare it ourselves.
		# Do we have "struct ether_addr" if we include
		# <netinet/if_ether.h>?
		#
		ac_fn_c_check_type "$LINENO" "struct ether_addr" "ac_cv_type_struct_ether_addr" "
			#include <sys/types.h>
			#include <sys/socket.h>
			#include <net/if.h>
			#include <netinet/in.h>
			#include <netinet/if_ether.h>
		    
"
if test "x$ac_cv_type_struct_ether_addr" = xyes
then :
  
printf "%s\n" "@%:@define HAVE_STRUCT_ETHER_ADDR 1" >>confdefs.h


fi

	fi
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for buggy ether_ntohost" >&5
printf %s "checking for buggy ether_ntohost... " >&6; }
if test ${ac_cv_buggy_ether_ntohost+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  
	if test "$cross_compiling" = yes
then :
  ac_cv_buggy_ether_ntohost="not while cross-compiling"
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

  #include <netdb.h>
  #if defined(NET_ETHERNET_H_DECLARES_ETHER_NTOHOST)
    /*
     * OK, just include <net/ethernet.h>.
     */
    #include <net/ethernet.h>
  #elif defined(NETINET_ETHER_H_DECLARES_ETHER_NTOHOST)
    /*
     * OK, just include <netinet/ether.h>
     */
    #include <netinet/ether.h>
  #elif defined(SYS_ETHERNET_H_DECLARES_ETHER_NTOHOST)
    /*
     * OK, just include <sys/ethernet.h>
     */
    #include <sys/ethernet.h>
  #elif defined(ARPA_INET_H_DECLARES_ETHER_NTOHOST)
    /*
     * OK, just include <arpa/inet.h>
     */
    #include <arpa/inet.h>
  #elif defined(NETINET_IF_ETHER_H_DECLARES_ETHER_NTOHOST)
    /*
     * OK, include <netinet/if_ether.h>, after all the other stuff we
     * need to include or define for its benefit.
     */
    #define NEED_NETINET_IF_ETHER_H
  #else
    /*
     * We'll have to declare it ourselves.
     * If <netinet/if_ether.h> defines struct ether_addr, include
     * it.  Otherwise, define it ourselves.
     */
    #ifdef HAVE_STRUCT_ETHER_ADDR
      #define NEED_NETINET_IF_ETHER_H
    #else /* HAVE_STRUCT_ETHER_ADDR */
	struct ether_addr {
		/* Beware FreeBSD calls this "octet". */
		unsigned char ether_addr_octet[MAC_ADDR_LEN];
	};
    #endif /* HAVE_STRUCT_ETHER_ADDR */
  #endif /* what declares ether_ntohost() */

  #ifdef NEED_NETINET_IF_ETHER_H
    /*
     * Include diag-control.h before <net/if.h>, which too defines a macro
     * named ND_UNREACHABLE.
     */
    #include "diag-control.h"
    #include <net/if.h>		/* Needed on some platforms */
    #include <netinet/in.h>	/* Needed on some platforms */
    #include <netinet/if_ether.h>
  #endif /* NEED_NETINET_IF_ETHER_H */

  #ifndef HAVE_DECL_ETHER_NTOHOST
    /*
     * No header declares it, so declare it ourselves.
     */
    extern int ether_ntohost(char *, const struct ether_addr *);
  #endif /* !defined(HAVE_DECL_ETHER_NTOHOST) */
  #include <stdlib.h>
  #include <sys/types.h>
  #include <sys/param.h>
  #include <sys/socket.h>

  int
  main(int argc, char **argv)
  {
    u_char ea[6] = { 0xff, 0xff, 0xff, 0xff, 0xff };
    char name[MAXHOSTNAMELEN];

    ether_ntohost(name, (struct ether_addr *)ea);
    exit(0);
  }
	
	
_ACEOF
if ac_fn_c_try_run "$LINENO"
then :
  ac_cv_buggy_ether_ntohost=no
else $as_nop
  ac_cv_buggy_ether_ntohost=yes
fi
rm -f core *.core core.conftest.* gmon.out bb.out conftest$ac_exeext \
  conftest.$ac_objext conftest.beam conftest.$ac_ext
fi

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_buggy_ether_ntohost" >&5
printf "%s\n" "$ac_cv_buggy_ether_ntohost" >&6; }
    if test "$ac_cv_buggy_ether_ntohost" = "no"; then
	
printf "%s\n" "@%:@define USE_ETHER_NTOHOST 1" >>confdefs.h

    fi

fi

done

#
# Do we have the new open API?  Check for pcap_create, and assume that,
# if we do, we also have pcap_activate() and the other new routines
# introduced in libpcap 1.0.
#
# We require those routines, so fail if we don't find it.
#
ac_fn_c_check_func "$LINENO" "pcap_create" "ac_cv_func_pcap_create"
if test "x$ac_cv_func_pcap_create" = xyes
then :
  
else $as_nop
  as_fn_error $? "libpcap is too old; 1.0 or later is required" "$LINENO" 5
fi


#
# OK, do we have pcap_set_tstamp_type?  If so, assume we have
# pcap_list_tstamp_types and pcap_free_tstamp_types as well.
#
ac_fn_c_check_func "$LINENO" "pcap_set_tstamp_type" "ac_cv_func_pcap_set_tstamp_type"
if test "x$ac_cv_func_pcap_set_tstamp_type" = xyes
then :
  printf "%s\n" "@%:@define HAVE_PCAP_SET_TSTAMP_TYPE 1" >>confdefs.h

fi

#
# And do we have pcap_set_tstamp_precision?  If so, we assume
# we also have pcap_open_offline_with_tstamp_precision.
#
ac_fn_c_check_func "$LINENO" "pcap_set_tstamp_precision" "ac_cv_func_pcap_set_tstamp_precision"
if test "x$ac_cv_func_pcap_set_tstamp_precision" = xyes
then :
  printf "%s\n" "@%:@define HAVE_PCAP_SET_TSTAMP_PRECISION 1" >>confdefs.h

fi


#
# Check for a miscellaneous collection of functions which we use
# if we have them.
#
ac_fn_c_check_func "$LINENO" "pcap_set_immediate_mode" "ac_cv_func_pcap_set_immediate_mode"
if test "x$ac_cv_func_pcap_set_immediate_mode" = xyes
then :
  printf "%s\n" "@%:@define HAVE_PCAP_SET_IMMEDIATE_MODE 1" >>confdefs.h

fi
ac_fn_c_check_func "$LINENO" "pcap_dump_ftell64" "ac_cv_func_pcap_dump_ftell64"
if test "x$ac_cv_func_pcap_dump_ftell64" = xyes
then :
  printf "%s\n" "@%:@define HAVE_PCAP_DUMP_FTELL64 1" >>confdefs.h

fi

#
# See the comment in AC_LBL_LIBPCAP in aclocal.m4 for the reason
# why we don't check for remote-capture APIs if we're building
# with the system libpcap on macOS.
#
if test "$_dont_check_for_remote_apis" != "yes"; then
	ac_fn_c_check_func "$LINENO" "pcap_open" "ac_cv_func_pcap_open"
if test "x$ac_cv_func_pcap_open" = xyes
then :
  printf "%s\n" "@%:@define HAVE_PCAP_OPEN 1" >>confdefs.h

fi
ac_fn_c_check_func "$LINENO" "pcap_findalldevs_ex" "ac_cv_func_pcap_findalldevs_ex"
if test "x$ac_cv_func_pcap_findalldevs_ex" = xyes
then :
  printf "%s\n" "@%:@define HAVE_PCAP_FINDALLDEVS_EX 1" >>confdefs.h

fi

fi

#
# Check for special debugging functions
#
ac_fn_c_check_func "$LINENO" "pcap_set_parser_debug" "ac_cv_func_pcap_set_parser_debug"
if test "x$ac_cv_func_pcap_set_parser_debug" = xyes
then :
  printf "%s\n" "@%:@define HAVE_PCAP_SET_PARSER_DEBUG 1" >>confdefs.h

fi

if test "$ac_cv_func_pcap_set_parser_debug" = "no" ; then
	#
	# OK, we don't have pcap_set_parser_debug() to set the libpcap
	# filter expression parser debug flag; can we directly set the
	# flag?
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether pcap_debug is defined by libpcap" >&5
printf %s "checking whether pcap_debug is defined by libpcap... " >&6; }
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

		extern int pcap_debug;

		return pcap_debug;
	   
  ;
  return 0;
}
	   
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_lbl_cv_pcap_debug_defined=yes
else $as_nop
  ac_lbl_cv_pcap_debug_defined=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
	if test "$ac_lbl_cv_pcap_debug_defined" = yes ; then
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		
printf "%s\n" "@%:@define HAVE_PCAP_DEBUG 1" >>confdefs.h

	else
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		#
		# OK, what about "yydebug"?
		#
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether yydebug is defined by libpcap" >&5
printf %s "checking whether yydebug is defined by libpcap... " >&6; }
		cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

			extern int yydebug;

			return yydebug;
		   
  ;
  return 0;
}
		   
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_lbl_cv_yydebug_defined=yes
else $as_nop
  ac_lbl_cv_yydebug_defined=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
		if test "$ac_lbl_cv_yydebug_defined" = yes ; then
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			
printf "%s\n" "@%:@define HAVE_YYDEBUG 1" >>confdefs.h

		else
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		fi
	fi
fi
ac_fn_c_check_func "$LINENO" "pcap_set_optimizer_debug" "ac_cv_func_pcap_set_optimizer_debug"
if test "x$ac_cv_func_pcap_set_optimizer_debug" = xyes
then :
  printf "%s\n" "@%:@define HAVE_PCAP_SET_OPTIMIZER_DEBUG 1" >>confdefs.h

fi


#
# bpf_dump() moved from tcpdump to libpcap in libpcap 0.6, but some
# versions of libpcap didn't pick that change up; the OpenBSD libpcap
# picked up most of the new APIs from versions up to 1.0, but didn't
# pick up bpf_dump(), so we need to provide it if it's absent.
#
ac_fn_c_check_func "$LINENO" "bpf_dump" "ac_cv_func_bpf_dump"
if test "x$ac_cv_func_bpf_dump" = xyes
then :
  printf "%s\n" "@%:@define HAVE_BPF_DUMP 1" >>confdefs.h

else $as_nop
  case " $LIB@&t@OBJS " in
  *" bpf_dump.$ac_objext "* ) ;;
  *) LIB@&t@OBJS="$LIB@&t@OBJS bpf_dump.$ac_objext"
 ;;
esac

fi
	
#
# Assume V7/BSD convention for man pages (file formats in section 5,
# miscellaneous info in section 7).
#
MAN_FILE_FORMATS=5
MAN_MISC_INFO=7
case "$host_os" in

hpux*)
	#
	# Use System V conventions for man pages.
	#
	MAN_FILE_FORMATS=4
	MAN_MISC_INFO=5
	;;

solaris*)
	#
	# Use System V conventions for man pages.
	#
	MAN_FILE_FORMATS=4
	MAN_MISC_INFO=5
	;;
esac

savedcppflags="$CPPFLAGS"
CPPFLAGS="$CPPFLAGS $V_INCLS"

#
# Check whether we have pcap/pcap-inttypes.h.
# If we do, we use that to get the C99 types defined.
#
ac_fn_c_check_header_compile "$LINENO" "pcap/pcap-inttypes.h" "ac_cv_header_pcap_pcap_inttypes_h" "$ac_includes_default"
if test "x$ac_cv_header_pcap_pcap_inttypes_h" = xyes
then :
  printf "%s\n" "@%:@define HAVE_PCAP_PCAP_INTTYPES_H 1" >>confdefs.h

fi


CPPFLAGS="$savedcppflags"

#
# Define the old BSD specified-width types in terms of the C99 types;
# we may need them with libpcap include files.
#
ac_fn_c_check_type "$LINENO" "u_int8_t" "ac_cv_type_u_int8_t" "$ac_includes_default
#include <sys/types.h>

"
if test "x$ac_cv_type_u_int8_t" = xyes
then :
  
else $as_nop
  
printf "%s\n" "@%:@define u_int8_t uint8_t" >>confdefs.h

fi

ac_fn_c_check_type "$LINENO" "u_int16_t" "ac_cv_type_u_int16_t" "$ac_includes_default
#include <sys/types.h>

"
if test "x$ac_cv_type_u_int16_t" = xyes
then :
  
else $as_nop
  
printf "%s\n" "@%:@define u_int16_t uint16_t" >>confdefs.h

fi

ac_fn_c_check_type "$LINENO" "u_int32_t" "ac_cv_type_u_int32_t" "$ac_includes_default
#include <sys/types.h>

"
if test "x$ac_cv_type_u_int32_t" = xyes
then :
  
else $as_nop
  
printf "%s\n" "@%:@define u_int32_t uint32_t" >>confdefs.h

fi

ac_fn_c_check_type "$LINENO" "u_int64_t" "ac_cv_type_u_int64_t" "$ac_includes_default
#include <sys/types.h>

"
if test "x$ac_cv_type_u_int64_t" = xyes
then :
  
else $as_nop
  
printf "%s\n" "@%:@define u_int64_t uint64_t" >>confdefs.h

fi


if test -n "$ac_tool_prefix"; then
  # Extract the first word of "${ac_tool_prefix}ranlib", so it can be a program name with args.
set dummy ${ac_tool_prefix}ranlib; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_RANLIB+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$RANLIB"; then
  ac_cv_prog_RANLIB="$RANLIB" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_RANLIB="${ac_tool_prefix}ranlib"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
RANLIB=$ac_cv_prog_RANLIB
if test -n "$RANLIB"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $RANLIB" >&5
printf "%s\n" "$RANLIB" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$ac_cv_prog_RANLIB"; then
  ac_ct_RANLIB=$RANLIB
  # Extract the first word of "ranlib", so it can be a program name with args.
set dummy ranlib; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_ac_ct_RANLIB+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$ac_ct_RANLIB"; then
  ac_cv_prog_ac_ct_RANLIB="$ac_ct_RANLIB" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_ac_ct_RANLIB="ranlib"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
ac_ct_RANLIB=$ac_cv_prog_ac_ct_RANLIB
if test -n "$ac_ct_RANLIB"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_ct_RANLIB" >&5
printf "%s\n" "$ac_ct_RANLIB" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

  if test "x$ac_ct_RANLIB" = x; then
    RANLIB=":"
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    RANLIB=$ac_ct_RANLIB
  fi
else
  RANLIB="$ac_cv_prog_RANLIB"
fi

if test -n "$ac_tool_prefix"; then
  # Extract the first word of "${ac_tool_prefix}ar", so it can be a program name with args.
set dummy ${ac_tool_prefix}ar; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_AR+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$AR"; then
  ac_cv_prog_AR="$AR" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_AR="${ac_tool_prefix}ar"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
AR=$ac_cv_prog_AR
if test -n "$AR"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $AR" >&5
printf "%s\n" "$AR" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$ac_cv_prog_AR"; then
  ac_ct_AR=$AR
  # Extract the first word of "ar", so it can be a program name with args.
set dummy ar; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_ac_ct_AR+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$ac_ct_AR"; then
  ac_cv_prog_ac_ct_AR="$ac_ct_AR" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_ac_ct_AR="ar"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
ac_ct_AR=$ac_cv_prog_ac_ct_AR
if test -n "$ac_ct_AR"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_ct_AR" >&5
printf "%s\n" "$ac_ct_AR" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

  if test "x$ac_ct_AR" = x; then
    AR=""
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    AR=$ac_ct_AR
  fi
else
  AR="$ac_cv_prog_AR"
fi


rm -f os-proto.h
    if test "${LBL_CFLAGS+set}" = set; then
	    V_CCOPT="$V_CCOPT ${LBL_CFLAGS}"
    fi
    if test -f .devel ; then
	    #
	    # Skip all the warning option stuff on some compilers.
	    #
	    if test "$ac_lbl_cc_dont_try_gcc_dashW" != yes; then
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -W option" >&5
printf %s "checking whether the compiler supports the -W option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -W"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -W"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wall option" >&5
printf %s "checking whether the compiler supports the -Wall option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wall"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wall"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wassign-enum option" >&5
printf %s "checking whether the compiler supports the -Wassign-enum option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wassign-enum"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wassign-enum"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wcast-qual option" >&5
printf %s "checking whether the compiler supports the -Wcast-qual option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wcast-qual"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wcast-qual"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wmissing-prototypes option" >&5
printf %s "checking whether the compiler supports the -Wmissing-prototypes option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wmissing-prototypes"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wmissing-prototypes"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wmissing-variable-declarations option" >&5
printf %s "checking whether the compiler supports the -Wmissing-variable-declarations option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wmissing-variable-declarations"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wmissing-variable-declarations"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wnull-pointer-subtraction option" >&5
printf %s "checking whether the compiler supports the -Wnull-pointer-subtraction option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wnull-pointer-subtraction"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wnull-pointer-subtraction"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wold-style-definition option" >&5
printf %s "checking whether the compiler supports the -Wold-style-definition option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wold-style-definition"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wold-style-definition"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wpedantic option" >&5
printf %s "checking whether the compiler supports the -Wpedantic option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wpedantic"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wpedantic"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wpointer-arith option" >&5
printf %s "checking whether the compiler supports the -Wpointer-arith option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wpointer-arith"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wpointer-arith"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wpointer-sign option" >&5
printf %s "checking whether the compiler supports the -Wpointer-sign option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wpointer-sign"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wpointer-sign"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wshadow option" >&5
printf %s "checking whether the compiler supports the -Wshadow option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wshadow"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wshadow"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wsign-compare option" >&5
printf %s "checking whether the compiler supports the -Wsign-compare option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wsign-compare"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wsign-compare"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wstrict-prototypes option" >&5
printf %s "checking whether the compiler supports the -Wstrict-prototypes option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wstrict-prototypes"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wstrict-prototypes"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wundef option" >&5
printf %s "checking whether the compiler supports the -Wundef option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wundef"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wundef"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wunreachable-code-return option" >&5
printf %s "checking whether the compiler supports the -Wunreachable-code-return option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wunreachable-code-return"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wunreachable-code-return"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wunused-but-set-parameter option" >&5
printf %s "checking whether the compiler supports the -Wunused-but-set-parameter option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wunused-but-set-parameter"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wunused-but-set-parameter"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wunused-but-set-variable option" >&5
printf %s "checking whether the compiler supports the -Wunused-but-set-variable option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wunused-but-set-variable"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wunused-but-set-variable"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wused-but-marked-unused option" >&5
printf %s "checking whether the compiler supports the -Wused-but-marked-unused option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wused-but-marked-unused"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wused-but-marked-unused"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wwrite-strings option" >&5
printf %s "checking whether the compiler supports the -Wwrite-strings option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wwrite-strings"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		CFLAGS="$save_CFLAGS"
		V_CCOPT="$V_CCOPT -Wwrite-strings"
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
	    fi
	    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports generating dependencies" >&5
printf %s "checking whether the compiler supports generating dependencies... " >&6; }
	if test "$GCC" = yes ; then
		#
		# GCC, or a compiler deemed to be GCC by AC_PROG_CC (even
		# though it's not); we assume that, in this case, the flag
		# would be -M.
		#
		ac_lbl_dependency_flag="-M"
	else
		#
		# Not GCC or a compiler deemed to be GCC; what platform is
		# this?  (We're assuming that if the compiler isn't GCC
		# it's the compiler from the vendor of the OS; that won't
		# necessarily be true for x86 platforms, where it might be
		# the Intel C compiler.)
		#
		case "$host_os" in

		darwin*)
			#
			# Clang uses -M.
			#
			ac_lbl_dependency_flag="-M"
			;;

		solaris*)
			#
			# Sun C uses -xM.
			#
			ac_lbl_dependency_flag="-xM"
			;;

		hpux*)
			#
			# HP's older C compilers don't support this.
			# HP's newer C compilers support this with
			# either +M or +Make; the older compilers
			# interpret +M as something completely
			# different, so we use +Make so we don't
			# think it works with the older compilers.
			#
			ac_lbl_dependency_flag="+Make"
			;;

		*)
			#
			# Not one of the above; assume no support for
			# generating dependencies.
			#
			ac_lbl_dependency_flag=""
			;;
		esac
	fi

	#
	# Is ac_lbl_dependency_flag defined and, if so, does the compiler
	# complain about it?
	#
	# Note: clang doesn't seem to exit with an error status when handed
	# an unknown non-warning error, even if you pass it
	# -Werror=unknown-warning-option.  However, it always supports
	# -M, so the fact that this test always succeeds with clang
	# isn't an issue.
	#
	if test ! -z "$ac_lbl_dependency_flag"; then
		cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
		if { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: eval \"\$CC \$ac_lbl_dependency_flag conftest.c >/dev/null 2>&1\""; } >&5
  (eval "$CC $ac_lbl_dependency_flag conftest.c >/dev/null 2>&1") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }; then
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes, with $ac_lbl_dependency_flag" >&5
printf "%s\n" "yes, with $ac_lbl_dependency_flag" >&6; }
			DEPENDENCY_CFLAG="$ac_lbl_dependency_flag"
			MKDEP='${top_srcdir}/mkdep'
		else
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
			#
			# We can't run mkdep, so have "make depend" do
			# nothing.
			#
			MKDEP=:
		fi
		rm -rf conftest*
	else
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		#
		# We can't run mkdep, so have "make depend" do
		# nothing.
		#
		MKDEP=:
	fi
	
	
    
	    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether to use an os-proto.h header" >&5
printf %s "checking whether to use an os-proto.h header... " >&6; }
	    os=`echo $host_os | sed -e 's/\([0-9][0-9]*\)[^0-9].*$/\1/'`
	    name="lbl/os-$os.h"
	    if test -f $name ; then
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes, at \"$name\"" >&5
printf "%s\n" "yes, at \"$name\"" >&6; }
		    ln -s $name os-proto.h
		    
printf "%s\n" "@%:@define HAVE_OS_PROTO_H 1" >>confdefs.h

	    else
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
	    fi
    fi

# Check for OpenSSL/libressl libcrypto
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether to use OpenSSL/libressl libcrypto" >&5
printf %s "checking whether to use OpenSSL/libressl libcrypto... " >&6; }
# Specify location for both includes and libraries.
want_libcrypto=ifavailable

@%:@ Check whether --with-crypto was given.
if test ${with_crypto+y}
then :
  withval=$with_crypto; 
	if test $withval = no
	then
		# User doesn't want to link with libcrypto.
		want_libcrypto=no
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
	elif test $withval = yes
	then
		# User wants to link with libcrypto but hasn't specified
		# a directory.
		want_libcrypto=yes
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
	else
		# User wants to link with libcrypto and has specified
		# a directory, so use the provided value.
		want_libcrypto=yes
		libcrypto_root=$withval
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes, using the version installed in $withval" >&5
printf "%s\n" "yes, using the version installed in $withval" >&6; }
	fi

else $as_nop
  
	#
	# Use libcrypto if it's present, otherwise don't; no directory
	# was specified.
	#
	want_libcrypto=ifavailable
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes, if available" >&5
printf "%s\n" "yes, if available" >&6; }

fi

if test "$want_libcrypto" != "no"; then
	#
	# Were we told where to look for libcrypto?
	#
	if test -z "$libcrypto_root"; then
		#
		# No.
		#
		# First, try looking for it with pkg-config, if we have it.
		#
		# Homebrew's pkg-config does not, by default, look for
		# pkg-config files for packages it has installed.
		# Furthermore, at least for OpenSSL, they appear to be
		# dumped in package-specific directories whose paths are
		# not only package-specific but package-version-specific.
		#
		# So the only way to find openssl is to get the value of
		# PKG_CONFIG_PATH from "brew --env openssl" and add that
		# to PKG_CONFIG_PATH.  (No, we can't just assume it's under
		# /usr/local; Homebrew have conveniently chosen to put it
		# under /opt/homebrew on ARM.)
		#
		# That's the nice thing about Homebrew - it makes things easier!
		# Thanks!
		#
		save_PKG_CONFIG_PATH="$PKG_CONFIG_PATH"
		if test -n "$BREW"; then
			openssl_pkgconfig_dir=`$BREW --env --plain openssl | sed -n 's/PKG_CONFIG_PATH: //p'`
			PKG_CONFIG_PATH="$openssl_pkgconfig_dir:$PKG_CONFIG_PATH"
		fi
		
if test -n "$PKG_CONFIG"; then
            
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for libcrypto with pkg-config" >&5
printf %s "checking for libcrypto with pkg-config... " >&6; }
    if { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$PKG_CONFIG --exists --print-errors \"libcrypto\""; } >&5
  ($PKG_CONFIG --exists --print-errors "libcrypto") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }; then
	#
	# The package was found, so try to get its C flags and
	# libraries.
	#
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: found" >&5
printf "%s\n" "found" >&6; }
	if test ! -n "$LIBCRYPTO_CFLAGS"; then
    LIBCRYPTO_CFLAGS=`$PKG_CONFIG --cflags "libcrypto" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --cflags "libcrypto" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --cflags "libcrypto" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --cflags \"libcrypto\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
	if test ! -n "$LIBCRYPTO_LIBS"; then
    LIBCRYPTO_LIBS=`$PKG_CONFIG --libs "libcrypto" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --libs "libcrypto" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --libs "libcrypto" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --libs \"libcrypto\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
	if test ! -n "$LIBCRYPTO_LIBS_STATIC"; then
    LIBCRYPTO_LIBS_STATIC=`$PKG_CONFIG --libs --static "libcrypto" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --libs --static "libcrypto" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --libs --static "libcrypto" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --libs --static \"libcrypto\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
        
			#
			# We found OpenSSL/libressl libcrypto.
			#
			HAVE_LIBCRYPTO=yes
		    
    else
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: not found" >&5
printf "%s\n" "not found" >&6; }
        :
    fi
else
    # No pkg-config, so obviously not found with pkg-config.
    :
fi

		PKG_CONFIG_PATH="$save_PKG_CONFIG_PATH"

		#
		# If it wasn't found, and we have Homebrew installed, see
		# if it's in Homebrew.
		#
		if test "x$HAVE_LIBCRYPTO" != "xyes" -a -n "$BREW"; then
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for openssl in Homebrew" >&5
printf %s "checking for openssl in Homebrew... " >&6; }
			#
			# The brew man page lies when it speaks of
			# $BREW --prefix --installed <formula>
			# outputting nothing.  In Homebrew 3.3.16,
			# it produces output regardless of whether
			# the formula is installed or not, so we
			# send the standard output and error to
			# the bit bucket.
			#
			# libcrypto isn't a formula, openssl is a formula.
			#
			if $BREW --prefix --installed openssl >/dev/null 2>&1; then
				#
				# Yes.  Get the include directory and library
				# directory.  (No, we can't just assume it's
				# under /usr/local; Homebrew have conveniently
				# chosen to put it under /opt/homebrew on ARM.)
				#
				{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
				HAVE_LIBCRYPTO=yes
				openssl_path=`$BREW --prefix openssl`
				LIBCRYPTO_CFLAGS="-I$openssl_path/include"
				LIBCRYPTO_LIBS="-L$openssl_path/lib -lcrypto"
			else
				{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
			fi
		fi

		#
		# If it wasn't found, and /usr/local/include and /usr/local/lib
		# exist, check if it's in /usr/local.  (We check whether they
		# exist because, if they don't exist, the compiler will warn
		# about that and then ignore the argument, so they test
		# using just the system header files and libraries.)
		#
		# We include the standard include file to 1) make sure that
		# it's installed (if it's just a shared library for the
		# benefit of existing programs, that's not useful) and 2)
		# because SSL_library_init() is a library routine in some
		# versions and a #defined wrapper around OPENSSL_init_ssl()
		# in others.
		#
		if test "x$HAVE_LIBCRYPTO" != "xyes" -a -d "/usr/local/include" -a -d "/usr/local/lib"; then
			
	save_CFLAGS="$CFLAGS"
	save_LIBS="$LIBS"
	save_LDFLAGS="$LDFLAGS"

			CFLAGS="$CFLAGS -I/usr/local/include"
			LIBS="$LIBS -L/usr/local/lib -lcrypto"
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether we have an OpenSSL/libressl libcrypto in /usr/local that we can use" >&5
printf %s "checking whether we have an OpenSSL/libressl libcrypto in /usr/local that we can use... " >&6; }
			cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

#include <openssl/evp.h>
			    
int
main (void)
{

EVP_CIPHER_CTX_block_size((EVP_CIPHER_CTX *)0);
return 0;
			    
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  
				{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
				HAVE_LIBCRYPTO=yes
				LIBCRYPTO_CFLAGS="-I/usr/local/include"
				LIBCRYPTO_LIBS="-L/usr/local/lib -lcrypto"
			    
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
			
	CFLAGS="$save_CFLAGS"
	LIBS="$save_LIBS"
	LDFLAGS="$save_LDFLAGS"

		fi

		#
		# If it wasn't found, check if it's a system library.
		#
		# We include the standard include file to 1) make sure that
		# it's installed (if it's just a shared library for the
		# benefit of existing programs, that's not useful) and 2)
		# make sure this isn't a newer macOS that provides libcrypto
		# as a shared library but doesn't provide headers - Apple,
		# bless their pointy little heads, apparently ship libcrypto
		# as a library, but not the header files, in El Capitan and
		# later, probably because they don't want you writing nasty
		# portable code that could run on other UN*Xes, they want you
		# writing code that uses their Shiny New Crypto Library and
		# that thus only runs on macOS.
		#
		if test "x$HAVE_LIBCRYPTO" != "xyes"; then
			
	save_CFLAGS="$CFLAGS"
	save_LIBS="$LIBS"
	save_LDFLAGS="$LDFLAGS"

			LIBS="$LIBS -lcrypto"
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether we have a system OpenSSL/libressl that we can use" >&5
printf %s "checking whether we have a system OpenSSL/libressl that we can use... " >&6; }
			cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

#include <openssl/evp.h>
			    
int
main (void)
{

EVP_CIPHER_CTX_block_size((EVP_CIPHER_CTX *)0);
return 0;
			    
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  
				{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
				HAVE_LIBCRYPTO=yes
				LIBCRYPTO_LIBS="-lcrypto"
			    
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
			
	CFLAGS="$save_CFLAGS"
	LIBS="$save_LIBS"
	LDFLAGS="$save_LDFLAGS"

		fi
	else
		#
		# Yes.
		#
		# Look for it there.
		#
		
	save_CFLAGS="$CFLAGS"
	save_LIBS="$LIBS"
	save_LDFLAGS="$LDFLAGS"

		CFLAGS="$CFLAGS -I$libcrypto_root/include"
		LIBS="$LIBS -L$libcrypto_root/lib -lcrypto"
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether we have a system OpenSSL/libressl that we can use" >&5
printf %s "checking whether we have a system OpenSSL/libressl that we can use... " >&6; }
		cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

#include <openssl/evp.h>
		    
int
main (void)
{

EVP_CIPHER_CTX_block_size((EVP_CIPHER_CTX *)0);
return 0;
		    
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			HAVE_LIBCRYPTO=yes
			LIBCRYPTO_CFLAGS="-I$libcrypto_root/include"
			LIBCRYPTO_LIBS="-L$libcrypto_root/lib -lcrypto"
		    
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
		
	CFLAGS="$save_CFLAGS"
	LIBS="$save_LIBS"
	LDFLAGS="$save_LDFLAGS"

	fi

	#
	# OK, did we find it?
	#
	if test "x$HAVE_LIBCRYPTO" = "xyes"; then
		
printf "%s\n" "@%:@define HAVE_LIBCRYPTO 1" >>confdefs.h


		#
		# Put the subdirectories of the libcrypto root directory
		# at the end of the header and library search path, to
		# make sure they come after any -I or -L flags for
		# a local libpcap - those must take precedence of any
		# directory that might contain an installed version of
		# libpcap.
		#
		V_INCLS="$V_INCLS $LIBCRYPTO_CFLAGS"
		LIBS="$LIBS $LIBCRYPTO_LIBS"

		#
		# OK, then:
		#
		# 1) do we have EVP_CIPHER_CTX_new?
		# If so, we use it to allocate an EVP_CIPHER_CTX, as
		# EVP_CIPHER_CTX may be opaque; otherwise, we allocate it
		# ourselves.
		#
		# 2) do we have EVP_DecryptInit_ex()?
		# If so, we use it, because we need to be able to make
		# two "initialize the cipher" calls, one with the cipher
		# and key, and one with the IV, and, as of OpenSSL 1.1,
		# You Can't Do That with EVP_DecryptInit(), because a
		# call to EVP_DecryptInit() will unconditionally clear
		# the context, and if you don't supply a cipher, it'll
		# clear the cipher, rendering the context unusable and
		# causing a crash.
		#
		ac_fn_c_check_func "$LINENO" "EVP_CIPHER_CTX_new" "ac_cv_func_EVP_CIPHER_CTX_new"
if test "x$ac_cv_func_EVP_CIPHER_CTX_new" = xyes
then :
  printf "%s\n" "@%:@define HAVE_EVP_CIPHER_CTX_NEW 1" >>confdefs.h

fi
ac_fn_c_check_func "$LINENO" "EVP_DecryptInit_ex" "ac_cv_func_EVP_DecryptInit_ex"
if test "x$ac_cv_func_EVP_DecryptInit_ex" = xyes
then :
  printf "%s\n" "@%:@define HAVE_EVP_DECRYPTINIT_EX 1" >>confdefs.h

fi

	else
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: OpenSSL/libressl libcrypto not found" >&5
printf "%s\n" "$as_me: OpenSSL/libressl libcrypto not found" >&6;}
	fi
fi

# Check for libcap-ng
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether to use libcap-ng" >&5
printf %s "checking whether to use libcap-ng... " >&6; }
# Specify location for both includes and libraries.

@%:@ Check whether --with-cap_ng was given.
if test ${with_cap_ng+y}
then :
  withval=$with_cap_ng; 
	if test $withval = no
	then
		want_libcap_ng=no
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
	elif test $withval = yes
	then
		want_libcap_ng=yes
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
	fi

else $as_nop
  
	#
	# Use libcap-ng if it's present, otherwise don't.
	#
	want_libcap_ng=ifavailable
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes, if available" >&5
printf "%s\n" "yes, if available" >&6; }

fi


# libcap-ng is available on Linux only.
expr "$host_os" : linux >/dev/null || want_libcap_ng=no

if test "$want_libcap_ng" != "no"; then
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for capng_change_id in -lcap-ng" >&5
printf %s "checking for capng_change_id in -lcap-ng... " >&6; }
if test ${ac_cv_lib_cap_ng_capng_change_id+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lcap-ng  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char capng_change_id ();
int
main (void)
{
return capng_change_id ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_cap_ng_capng_change_id=yes
else $as_nop
  ac_cv_lib_cap_ng_capng_change_id=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_cap_ng_capng_change_id" >&5
printf "%s\n" "$ac_cv_lib_cap_ng_capng_change_id" >&6; }
if test "x$ac_cv_lib_cap_ng_capng_change_id" = xyes
then :
  printf "%s\n" "@%:@define HAVE_LIBCAP_NG 1" >>confdefs.h

  LIBS="-lcap-ng $LIBS"

fi

	ac_fn_c_check_header_compile "$LINENO" "cap-ng.h" "ac_cv_header_cap_ng_h" "$ac_includes_default"
if test "x$ac_cv_header_cap_ng_h" = xyes
then :
  printf "%s\n" "@%:@define HAVE_CAP_NG_H 1" >>confdefs.h

fi

fi

if test "$missing_includes" = "yes"; then
	CPPFLAGS="$CPPFLAGS -I$srcdir/missing"
	V_INCLS="$V_INCLS -I$srcdir/missing"
fi










  # Find a good install program.  We prefer a C program (faster),
# so one script is as good as another.  But avoid the broken or
# incompatible versions:
# SysV /etc/install, /usr/sbin/install
# SunOS /usr/etc/install
# IRIX /sbin/install
# AIX /bin/install
# AmigaOS /C/install, which installs bootblocks on floppy discs
# AIX 4 /usr/bin/installbsd, which doesn't work without a -g flag
# AFS /usr/afsws/bin/install, which mishandles nonexistent args
# SVR4 /usr/ucb/install, which tries to use the nonexistent group "staff"
# OS/2's system install, which has a completely different semantic
# ./install, which can be erroneously created by make from ./install.sh.
# Reject install programs that cannot install multiple files.
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for a BSD-compatible install" >&5
printf %s "checking for a BSD-compatible install... " >&6; }
if test -z "$INSTALL"; then
if test ${ac_cv_path_install+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    # Account for fact that we put trailing slashes in our PATH walk.
case $as_dir in @%:@((
  ./ | /[cC]/* | \
  /etc/* | /usr/sbin/* | /usr/etc/* | /sbin/* | /usr/afsws/bin/* | \
  ?:[\\/]os2[\\/]install[\\/]* | ?:[\\/]OS2[\\/]INSTALL[\\/]* | \
  /usr/ucb/* ) ;;
  *)
    # OSF1 and SCO ODT 3.0 have their own names for install.
    # Don't use installbsd from OSF since it installs stuff as root
    # by default.
    for ac_prog in ginstall scoinst install; do
      for ac_exec_ext in '' $ac_executable_extensions; do
	if as_fn_executable_p "$as_dir$ac_prog$ac_exec_ext"; then
	  if test $ac_prog = install &&
	    grep dspmsg "$as_dir$ac_prog$ac_exec_ext" >/dev/null 2>&1; then
	    # AIX install.  It has an incompatible calling convention.
	    :
	  elif test $ac_prog = install &&
	    grep pwplus "$as_dir$ac_prog$ac_exec_ext" >/dev/null 2>&1; then
	    # program-specific install script used by HP pwplus--don't use.
	    :
	  else
	    rm -rf conftest.one conftest.two conftest.dir
	    echo one > conftest.one
	    echo two > conftest.two
	    mkdir conftest.dir
	    if "$as_dir$ac_prog$ac_exec_ext" -c conftest.one conftest.two "`pwd`/conftest.dir/" &&
	      test -s conftest.one && test -s conftest.two &&
	      test -s conftest.dir/conftest.one &&
	      test -s conftest.dir/conftest.two
	    then
	      ac_cv_path_install="$as_dir$ac_prog$ac_exec_ext -c"
	      break 3
	    fi
	  fi
	fi
      done
    done
    ;;
esac

  done
IFS=$as_save_IFS

rm -rf conftest.one conftest.two conftest.dir

fi
  if test ${ac_cv_path_install+y}; then
    INSTALL=$ac_cv_path_install
  else
    # As a last resort, use the slow shell script.  Don't cache a
    # value for INSTALL within a source directory, because that will
    # break other packages using the cache if that directory is
    # removed, or if the value is a relative name.
    INSTALL=$ac_install_sh
  fi
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $INSTALL" >&5
printf "%s\n" "$INSTALL" >&6; }

# Use test -z because SunOS4 sh mishandles braces in ${var-val}.
# It thinks the first close brace ends the variable substitution.
test -z "$INSTALL_PROGRAM" && INSTALL_PROGRAM='${INSTALL}'

test -z "$INSTALL_SCRIPT" && INSTALL_SCRIPT='${INSTALL}'

test -z "$INSTALL_DATA" && INSTALL_DATA='${INSTALL} -m 644'


ac_config_headers="$ac_config_headers config.h"




ac_config_commands="$ac_config_commands .devel"

ac_config_files="$ac_config_files Makefile tcpdump.1"

cat >confcache <<\_ACEOF
# This file is a shell script that caches the results of configure
# tests run on this system so they can be shared between configure
# scripts and configure runs, see configure's option --config-cache.
# It is not useful on other systems.  If it contains results you don't
# want to keep, you may remove or edit it.
#
# config.status only pays attention to the cache file if you give it
# the --recheck option to rerun configure.
#
# `ac_cv_env_foo' variables (set or unset) will be overridden when
# loading this file, other *unset* `ac_cv_foo' will be assigned the
# following values.

_ACEOF

# The following way of writing the cache mishandles newlines in values,
# but we know of no workaround that is simple, portable, and efficient.
# So, we kill variables containing newlines.
# Ultrix sh set writes to stderr and can't be redirected directly,
# and sets the high bit in the cache file unless we assign to the vars.
(
  for ac_var in `(set) 2>&1 | sed -n 's/^\([a-zA-Z_][a-zA-Z0-9_]*\)=.*/\1/p'`; do
    eval ac_val=\$$ac_var
    case $ac_val in #(
    *${as_nl}*)
      case $ac_var in #(
      *_cv_*) { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: cache variable $ac_var contains a newline" >&5
printf "%s\n" "$as_me: WARNING: cache variable $ac_var contains a newline" >&2;} ;;
      esac
      case $ac_var in #(
      _ | IFS | as_nl) ;; #(
      BASH_ARGV | BASH_SOURCE) eval $ac_var= ;; #(
      *) { eval $ac_var=; unset $ac_var;} ;;
      esac ;;
    esac
  done

  (set) 2>&1 |
    case $as_nl`(ac_space=' '; set) 2>&1` in #(
    *${as_nl}ac_space=\ *)
      # `set' does not quote correctly, so add quotes: double-quote
      # substitution turns \\\\ into \\, and sed turns \\ into \.
      sed -n \
	"s/'/'\\\\''/g;
	  s/^\\([_$as_cr_alnum]*_cv_[_$as_cr_alnum]*\\)=\\(.*\\)/\\1='\\2'/p"
      ;; #(
    *)
      # `set' quotes correctly as required by POSIX, so do not add quotes.
      sed -n "/^[_$as_cr_alnum]*_cv_[_$as_cr_alnum]*=/p"
      ;;
    esac |
    sort
) |
  sed '
     /^ac_cv_env_/b end
     t clear
     :clear
     s/^\([^=]*\)=\(.*[{}].*\)$/test ${\1+y} || &/
     t end
     s/^\([^=]*\)=\(.*\)$/\1=${\1=\2}/
     :end' >>confcache
if diff "$cache_file" confcache >/dev/null 2>&1; then :; else
  if test -w "$cache_file"; then
    if test "x$cache_file" != "x/dev/null"; then
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: updating cache $cache_file" >&5
printf "%s\n" "$as_me: updating cache $cache_file" >&6;}
      if test ! -f "$cache_file" || test -h "$cache_file"; then
	cat confcache >"$cache_file"
      else
        case $cache_file in #(
        */* | ?:*)
	  mv -f confcache "$cache_file"$$ &&
	  mv -f "$cache_file"$$ "$cache_file" ;; #(
        *)
	  mv -f confcache "$cache_file" ;;
	esac
      fi
    fi
  else
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: not updating unwritable cache $cache_file" >&5
printf "%s\n" "$as_me: not updating unwritable cache $cache_file" >&6;}
  fi
fi
rm -f confcache

test "x$prefix" = xNONE && prefix=$ac_default_prefix
# Let make expand exec_prefix.
test "x$exec_prefix" = xNONE && exec_prefix='${prefix}'

DEFS=-DHAVE_CONFIG_H

ac_libobjs=
ac_ltlibobjs=
U=
for ac_i in : $LIB@&t@OBJS; do test "x$ac_i" = x: && continue
  # 1. Remove the extension, and $U if already installed.
  ac_script='s/\$U\././;s/\.o$//;s/\.obj$//'
  ac_i=`printf "%s\n" "$ac_i" | sed "$ac_script"`
  # 2. Prepend LIBOBJDIR.  When used with automake>=1.10 LIBOBJDIR
  #    will be set to the directory where LIBOBJS objects are built.
  as_fn_append ac_libobjs " \${LIBOBJDIR}$ac_i\$U.$ac_objext"
  as_fn_append ac_ltlibobjs " \${LIBOBJDIR}$ac_i"'$U.lo'
done
LIB@&t@OBJS=$ac_libobjs

LTLIBOBJS=$ac_ltlibobjs



: "${CONFIG_STATUS=./config.status}"
ac_write_fail=0
ac_clean_files_save=$ac_clean_files
ac_clean_files="$ac_clean_files $CONFIG_STATUS"
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: creating $CONFIG_STATUS" >&5
printf "%s\n" "$as_me: creating $CONFIG_STATUS" >&6;}
as_write_fail=0
cat >$CONFIG_STATUS <<_ASEOF || as_write_fail=1
#! $SHELL
# Generated by $as_me.
# Run this file to recreate the current configuration.
# Compiler output produced by configure, useful for debugging
# configure, is in config.log if it exists.

debug=false
ac_cs_recheck=false
ac_cs_silent=false

SHELL=\${CONFIG_SHELL-$SHELL}
export SHELL
_ASEOF
cat >>$CONFIG_STATUS <<\_ASEOF || as_write_fail=1
## -------------------- ##
## M4sh Initialization. ##
## -------------------- ##

# Be more Bourne compatible
DUALCASE=1; export DUALCASE # for MKS sh
as_nop=:
if test ${ZSH_VERSION+y} && (emulate sh) >/dev/null 2>&1
then :
  emulate sh
  NULLCMD=:
  # Pre-4.2 versions of Zsh do word splitting on ${1+"$@"}, which
  # is contrary to our usage.  Disable this feature.
  alias -g '${1+"$@"}'='"$@"'
  setopt NO_GLOB_SUBST
else $as_nop
  case `(set -o) 2>/dev/null` in @%:@(
  *posix*) :
    set -o posix ;; @%:@(
  *) :
     ;;
esac
fi



# Reset variables that may have inherited troublesome values from
# the environment.

# IFS needs to be set, to space, tab, and newline, in precisely that order.
# (If _AS_PATH_WALK were called with IFS unset, it would have the
# side effect of setting IFS to empty, thus disabling word splitting.)
# Quoting is to prevent editors from complaining about space-tab.
as_nl='
'
export as_nl
IFS=" ""	$as_nl"

PS1='$ '
PS2='> '
PS4='+ '

# Ensure predictable behavior from utilities with locale-dependent output.
LC_ALL=C
export LC_ALL
LANGUAGE=C
export LANGUAGE

# We cannot yet rely on "unset" to work, but we need these variables
# to be unset--not just set to an empty or harmless value--now, to
# avoid bugs in old shells (e.g. pre-3.0 UWIN ksh).  This construct
# also avoids known problems related to "unset" and subshell syntax
# in other old shells (e.g. bash 2.01 and pdksh 5.2.14).
for as_var in BASH_ENV ENV MAIL MAILPATH CDPATH
do eval test \${$as_var+y} \
  && ( (unset $as_var) || exit 1) >/dev/null 2>&1 && unset $as_var || :
done

# Ensure that fds 0, 1, and 2 are open.
if (exec 3>&0) 2>/dev/null; then :; else exec 0</dev/null; fi
if (exec 3>&1) 2>/dev/null; then :; else exec 1>/dev/null; fi
if (exec 3>&2)            ; then :; else exec 2>/dev/null; fi

# The user is always right.
if ${PATH_SEPARATOR+false} :; then
  PATH_SEPARATOR=:
  (PATH='/bin;/bin'; FPATH=$PATH; sh -c :) >/dev/null 2>&1 && {
    (PATH='/bin:/bin'; FPATH=$PATH; sh -c :) >/dev/null 2>&1 ||
      PATH_SEPARATOR=';'
  }
fi


# Find who we are.  Look in the path if we contain no directory separator.
as_myself=
case $0 in @%:@((
  *[\\/]* ) as_myself=$0 ;;
  *) as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    test -r "$as_dir$0" && as_myself=$as_dir$0 && break
  done
IFS=$as_save_IFS

     ;;
esac
# We did not find ourselves, most probably we were run as `sh COMMAND'
# in which case we are not to be found in the path.
if test "x$as_myself" = x; then
  as_myself=$0
fi
if test ! -f "$as_myself"; then
  printf "%s\n" "$as_myself: error: cannot find myself; rerun with an absolute file name" >&2
  exit 1
fi



@%:@ as_fn_error STATUS ERROR [LINENO LOG_FD]
@%:@ ----------------------------------------
@%:@ Output "`basename @S|@0`: error: ERROR" to stderr. If LINENO and LOG_FD are
@%:@ provided, also output the error to LOG_FD, referencing LINENO. Then exit the
@%:@ script with STATUS, using 1 if that was 0.
as_fn_error ()
{
  as_status=$1; test $as_status -eq 0 && as_status=1
  if test "$4"; then
    as_lineno=${as_lineno-"$3"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: $2" >&$4
  fi
  printf "%s\n" "$as_me: error: $2" >&2
  as_fn_exit $as_status
} @%:@ as_fn_error



@%:@ as_fn_set_status STATUS
@%:@ -----------------------
@%:@ Set @S|@? to STATUS, without forking.
as_fn_set_status ()
{
  return $1
} @%:@ as_fn_set_status

@%:@ as_fn_exit STATUS
@%:@ -----------------
@%:@ Exit the shell with STATUS, even in a "trap 0" or "set -e" context.
as_fn_exit ()
{
  set +e
  as_fn_set_status $1
  exit $1
} @%:@ as_fn_exit

@%:@ as_fn_unset VAR
@%:@ ---------------
@%:@ Portably unset VAR.
as_fn_unset ()
{
  { eval $1=; unset $1;}
}
as_unset=as_fn_unset

@%:@ as_fn_append VAR VALUE
@%:@ ----------------------
@%:@ Append the text in VALUE to the end of the definition contained in VAR. Take
@%:@ advantage of any shell optimizations that allow amortized linear growth over
@%:@ repeated appends, instead of the typical quadratic growth present in naive
@%:@ implementations.
if (eval "as_var=1; as_var+=2; test x\$as_var = x12") 2>/dev/null
then :
  eval 'as_fn_append ()
  {
    eval $1+=\$2
  }'
else $as_nop
  as_fn_append ()
  {
    eval $1=\$$1\$2
  }
fi # as_fn_append

@%:@ as_fn_arith ARG...
@%:@ ------------------
@%:@ Perform arithmetic evaluation on the ARGs, and store the result in the
@%:@ global @S|@as_val. Take advantage of shells that can avoid forks. The arguments
@%:@ must be portable across @S|@(()) and expr.
if (eval "test \$(( 1 + 1 )) = 2") 2>/dev/null
then :
  eval 'as_fn_arith ()
  {
    as_val=$(( $* ))
  }'
else $as_nop
  as_fn_arith ()
  {
    as_val=`expr "$@" || test $? -eq 1`
  }
fi # as_fn_arith


if expr a : '\(a\)' >/dev/null 2>&1 &&
   test "X`expr 00001 : '.*\(...\)'`" = X001; then
  as_expr=expr
else
  as_expr=false
fi

if (basename -- /) >/dev/null 2>&1 && test "X`basename -- / 2>&1`" = "X/"; then
  as_basename=basename
else
  as_basename=false
fi

if (as_dir=`dirname -- /` && test "X$as_dir" = X/) >/dev/null 2>&1; then
  as_dirname=dirname
else
  as_dirname=false
fi

as_me=`$as_basename -- "$0" ||
$as_expr X/"$0" : '.*/\([^/][^/]*\)/*$' \| \
	 X"$0" : 'X\(//\)$' \| \
	 X"$0" : 'X\(/\)' \| . 2>/dev/null ||
printf "%s\n" X/"$0" |
    sed '/^.*\/\([^/][^/]*\)\/*$/{
	    s//\1/
	    q
	  }
	  /^X\/\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\/\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`

# Avoid depending upon Character Ranges.
as_cr_letters='abcdefghijklmnopqrstuvwxyz'
as_cr_LETTERS='ABCDEFGHIJKLMNOPQRSTUVWXYZ'
as_cr_Letters=$as_cr_letters$as_cr_LETTERS
as_cr_digits='0123456789'
as_cr_alnum=$as_cr_Letters$as_cr_digits


# Determine whether it's possible to make 'echo' print without a newline.
# These variables are no longer used directly by Autoconf, but are AC_SUBSTed
# for compatibility with existing Makefiles.
ECHO_C= ECHO_N= ECHO_T=
case `echo -n x` in @%:@(((((
-n*)
  case `echo 'xy\c'` in
  *c*) ECHO_T='	';;	# ECHO_T is single tab character.
  xy)  ECHO_C='\c';;
  *)   echo `echo ksh88 bug on AIX 6.1` > /dev/null
       ECHO_T='	';;
  esac;;
*)
  ECHO_N='-n';;
esac

# For backward compatibility with old third-party macros, we provide
# the shell variables $as_echo and $as_echo_n.  New code should use
# AS_ECHO(["message"]) and AS_ECHO_N(["message"]), respectively.
as_@&t@echo='printf %s\n'
as_@&t@echo_n='printf %s'

rm -f conf$$ conf$$.exe conf$$.file
if test -d conf$$.dir; then
  rm -f conf$$.dir/conf$$.file
else
  rm -f conf$$.dir
  mkdir conf$$.dir 2>/dev/null
fi
if (echo >conf$$.file) 2>/dev/null; then
  if ln -s conf$$.file conf$$ 2>/dev/null; then
    as_ln_s='ln -s'
    # ... but there are two gotchas:
    # 1) On MSYS, both `ln -s file dir' and `ln file dir' fail.
    # 2) DJGPP < 2.04 has no symlinks; `ln -s' creates a wrapper executable.
    # In both cases, we have to default to `cp -pR'.
    ln -s conf$$.file conf$$.dir 2>/dev/null && test ! -f conf$$.exe ||
      as_ln_s='cp -pR'
  elif ln conf$$.file conf$$ 2>/dev/null; then
    as_ln_s=ln
  else
    as_ln_s='cp -pR'
  fi
else
  as_ln_s='cp -pR'
fi
rm -f conf$$ conf$$.exe conf$$.dir/conf$$.file conf$$.file
rmdir conf$$.dir 2>/dev/null


@%:@ as_fn_mkdir_p
@%:@ -------------
@%:@ Create "@S|@as_dir" as a directory, including parents if necessary.
as_fn_mkdir_p ()
{

  case $as_dir in #(
  -*) as_dir=./$as_dir;;
  esac
  test -d "$as_dir" || eval $as_mkdir_p || {
    as_dirs=
    while :; do
      case $as_dir in #(
      *\'*) as_qdir=`printf "%s\n" "$as_dir" | sed "s/'/'\\\\\\\\''/g"`;; #'(
      *) as_qdir=$as_dir;;
      esac
      as_dirs="'$as_qdir' $as_dirs"
      as_dir=`$as_dirname -- "$as_dir" ||
$as_expr X"$as_dir" : 'X\(.*[^/]\)//*[^/][^/]*/*$' \| \
	 X"$as_dir" : 'X\(//\)[^/]' \| \
	 X"$as_dir" : 'X\(//\)$' \| \
	 X"$as_dir" : 'X\(/\)' \| . 2>/dev/null ||
printf "%s\n" X"$as_dir" |
    sed '/^X\(.*[^/]\)\/\/*[^/][^/]*\/*$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)[^/].*/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`
      test -d "$as_dir" && break
    done
    test -z "$as_dirs" || eval "mkdir $as_dirs"
  } || test -d "$as_dir" || as_fn_error $? "cannot create directory $as_dir"


} @%:@ as_fn_mkdir_p
if mkdir -p . 2>/dev/null; then
  as_mkdir_p='mkdir -p "$as_dir"'
else
  test -d ./-p && rmdir ./-p
  as_mkdir_p=false
fi


@%:@ as_fn_executable_p FILE
@%:@ -----------------------
@%:@ Test if FILE is an executable regular file.
as_fn_executable_p ()
{
  test -f "$1" && test -x "$1"
} @%:@ as_fn_executable_p
as_test_x='test -x'
as_executable_p=as_fn_executable_p

# Sed expression to map a string onto a valid CPP name.
as_tr_cpp="eval sed 'y%*$as_cr_letters%P$as_cr_LETTERS%;s%[^_$as_cr_alnum]%_%g'"

# Sed expression to map a string onto a valid variable name.
as_tr_sh="eval sed 'y%*+%pp%;s%[^_$as_cr_alnum]%_%g'"


exec 6>&1
## ----------------------------------- ##
## Main body of $CONFIG_STATUS script. ##
## ----------------------------------- ##
_ASEOF
test $as_write_fail = 0 && chmod +x $CONFIG_STATUS || ac_write_fail=1

cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
# Save the log message, to keep $0 and so on meaningful, and to
# report actual input values of CONFIG_FILES etc. instead of their
# values after options handling.
ac_log="
This file was extended by tcpdump $as_me 5.0.0-PRE-GIT, which was
generated by GNU Autoconf 2.71.  Invocation command line was

  CONFIG_FILES    = $CONFIG_FILES
  CONFIG_HEADERS  = $CONFIG_HEADERS
  CONFIG_LINKS    = $CONFIG_LINKS
  CONFIG_COMMANDS = $CONFIG_COMMANDS
  $ $0 $@

on `(hostname || uname -n) 2>/dev/null | sed 1q`
"

_ACEOF

case $ac_config_files in *"
"*) set x $ac_config_files; shift; ac_config_files=$*;;
esac

case $ac_config_headers in *"
"*) set x $ac_config_headers; shift; ac_config_headers=$*;;
esac


cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
# Files that config.status was made for.
config_files="$ac_config_files"
config_headers="$ac_config_headers"
config_commands="$ac_config_commands"

_ACEOF

cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
ac_cs_usage="\
\`$as_me' instantiates files and other configuration actions
from templates according to the current configuration.  Unless the files
and actions are specified as TAGs, all are instantiated by default.

Usage: $0 [OPTION]... [TAG]...

  -h, --help       print this help, then exit
  -V, --version    print version number and configuration settings, then exit
      --config     print configuration, then exit
  -q, --quiet, --silent
                   do not print progress messages
  -d, --debug      don't remove temporary files
      --recheck    update $as_me by reconfiguring in the same conditions
      --file=FILE[:TEMPLATE] 
                   instantiate the configuration file FILE
      --header=FILE[:TEMPLATE] 
                   instantiate the configuration header FILE

Configuration files:
$config_files

Configuration headers:
$config_headers

Configuration commands:
$config_commands

Report bugs to <https://github.com/the-tcpdump-group/tcpdump/issues>.
tcpdump home page: <https://www.tcpdump.org/>."

_ACEOF
ac_cs_config=`printf "%s\n" "$ac_configure_args" | sed "$ac_safe_unquote"`
ac_cs_config_escaped=`printf "%s\n" "$ac_cs_config" | sed "s/^ //; s/'/'\\\\\\\\''/g"`
cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
ac_cs_config='$ac_cs_config_escaped'
ac_cs_version="\\
tcpdump config.status 5.0.0-PRE-GIT
configured by $0, generated by GNU Autoconf 2.71,
  with options \\"\$ac_cs_config\\"

Copyright (C) 2021 Free Software Foundation, Inc.
This config.status script is free software; the Free Software Foundation
gives unlimited permission to copy, distribute and modify it."

ac_pwd='$ac_pwd'
srcdir='$srcdir'
INSTALL='$INSTALL'
test -n "\$AWK" || AWK=awk
_ACEOF

cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
# The default lists apply if the user does not specify any file.
ac_need_defaults=:
while test $# != 0
do
  case $1 in
  --*=?*)
    ac_option=`expr "X$1" : 'X\([^=]*\)='`
    ac_optarg=`expr "X$1" : 'X[^=]*=\(.*\)'`
    ac_shift=:
    ;;
  --*=)
    ac_option=`expr "X$1" : 'X\([^=]*\)='`
    ac_optarg=
    ac_shift=:
    ;;
  *)
    ac_option=$1
    ac_optarg=$2
    ac_shift=shift
    ;;
  esac

  case $ac_option in
  # Handling of the options.
  -recheck | --recheck | --rechec | --reche | --rech | --rec | --re | --r)
    ac_cs_recheck=: ;;
  --version | --versio | --versi | --vers | --ver | --ve | --v | -V )
    printf "%s\n" "$ac_cs_version"; exit ;;
  --config | --confi | --conf | --con | --co | --c )
    printf "%s\n" "$ac_cs_config"; exit ;;
  --debug | --debu | --deb | --de | --d | -d )
    debug=: ;;
  --file | --fil | --fi | --f )
    $ac_shift
    case $ac_optarg in
    *\'*) ac_optarg=`printf "%s\n" "$ac_optarg" | sed "s/'/'\\\\\\\\''/g"` ;;
    '') as_fn_error $? "missing file argument" ;;
    esac
    as_fn_append CONFIG_FILES " '$ac_optarg'"
    ac_need_defaults=false;;
  --header | --heade | --head | --hea )
    $ac_shift
    case $ac_optarg in
    *\'*) ac_optarg=`printf "%s\n" "$ac_optarg" | sed "s/'/'\\\\\\\\''/g"` ;;
    esac
    as_fn_append CONFIG_HEADERS " '$ac_optarg'"
    ac_need_defaults=false;;
  --he | --h)
    # Conflict between --help and --header
    as_fn_error $? "ambiguous option: \`$1'
Try \`$0 --help' for more information.";;
  --help | --hel | -h )
    printf "%s\n" "$ac_cs_usage"; exit ;;
  -q | -quiet | --quiet | --quie | --qui | --qu | --q \
  | -silent | --silent | --silen | --sile | --sil | --si | --s)
    ac_cs_silent=: ;;

  # This is an error.
  -*) as_fn_error $? "unrecognized option: \`$1'
Try \`$0 --help' for more information." ;;

  *) as_fn_append ac_config_targets " $1"
     ac_need_defaults=false ;;

  esac
  shift
done

ac_configure_extra_args=

if $ac_cs_silent; then
  exec 6>/dev/null
  ac_configure_extra_args="$ac_configure_extra_args --silent"
fi

_ACEOF
cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
if \$ac_cs_recheck; then
  set X $SHELL '$0' $ac_configure_args \$ac_configure_extra_args --no-create --no-recursion
  shift
  \printf "%s\n" "running CONFIG_SHELL=$SHELL \$*" >&6
  CONFIG_SHELL='$SHELL'
  export CONFIG_SHELL
  exec "\$@"
fi

_ACEOF
cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
exec 5>>config.log
{
  echo
  sed 'h;s/./-/g;s/^.../@%:@@%:@ /;s/...$/ @%:@@%:@/;p;x;p;x' <<_ASBOX
@%:@@%:@ Running $as_me. @%:@@%:@
_ASBOX
  printf "%s\n" "$ac_log"
} >&5

_ACEOF
cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
_ACEOF

cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1

# Handling of arguments.
for ac_config_target in $ac_config_targets
do
  case $ac_config_target in
    "config.h") CONFIG_HEADERS="$CONFIG_HEADERS config.h" ;;
    ".devel") CONFIG_COMMANDS="$CONFIG_COMMANDS .devel" ;;
    "Makefile") CONFIG_FILES="$CONFIG_FILES Makefile" ;;
    "tcpdump.1") CONFIG_FILES="$CONFIG_FILES tcpdump.1" ;;

  *) as_fn_error $? "invalid argument: \`$ac_config_target'" "$LINENO" 5;;
  esac
done


# If the user did not use the arguments to specify the items to instantiate,
# then the envvar interface is used.  Set only those that are not.
# We use the long form for the default assignment because of an extremely
# bizarre bug on SunOS 4.1.3.
if $ac_need_defaults; then
  test ${CONFIG_FILES+y} || CONFIG_FILES=$config_files
  test ${CONFIG_HEADERS+y} || CONFIG_HEADERS=$config_headers
  test ${CONFIG_COMMANDS+y} || CONFIG_COMMANDS=$config_commands
fi

# Have a temporary directory for convenience.  Make it in the build tree
# simply because there is no reason against having it here, and in addition,
# creating and moving files from /tmp can sometimes cause problems.
# Hook for its removal unless debugging.
# Note that there is a small window in which the directory will not be cleaned:
# after its creation but before its name has been assigned to `$tmp'.
$debug ||
{
  tmp= ac_tmp=
  trap 'exit_status=$?
  : "${ac_tmp:=$tmp}"
  { test ! -d "$ac_tmp" || rm -fr "$ac_tmp"; } && exit $exit_status
' 0
  trap 'as_fn_exit 1' 1 2 13 15
}
# Create a (secure) tmp directory for tmp files.

{
  tmp=`(umask 077 && mktemp -d "./confXXXXXX") 2>/dev/null` &&
  test -d "$tmp"
}  ||
{
  tmp=./conf$$-$RANDOM
  (umask 077 && mkdir "$tmp")
} || as_fn_error $? "cannot create a temporary directory in ." "$LINENO" 5
ac_tmp=$tmp

# Set up the scripts for CONFIG_FILES section.
# No need to generate them if there are no CONFIG_FILES.
# This happens for instance with `./config.status config.h'.
if test -n "$CONFIG_FILES"; then


ac_cr=`echo X | tr X '\015'`
# On cygwin, bash can eat \r inside `` if the user requested igncr.
# But we know of no other shell where ac_cr would be empty at this
# point, so we can use a bashism as a fallback.
if test "x$ac_cr" = x; then
  eval ac_cr=\$\'\\r\'
fi
ac_cs_awk_cr=`$AWK 'BEGIN { print "a\rb" }' </dev/null 2>/dev/null`
if test "$ac_cs_awk_cr" = "a${ac_cr}b"; then
  ac_cs_awk_cr='\\r'
else
  ac_cs_awk_cr=$ac_cr
fi

echo 'BEGIN {' >"$ac_tmp/subs1.awk" &&
_ACEOF


{
  echo "cat >conf$$subs.awk <<_ACEOF" &&
  echo "$ac_subst_vars" | sed 's/.*/&!$&$ac_delim/' &&
  echo "_ACEOF"
} >conf$$subs.sh ||
  as_fn_error $? "could not make $CONFIG_STATUS" "$LINENO" 5
ac_delim_num=`echo "$ac_subst_vars" | grep -c '^'`
ac_delim='%!_!# '
for ac_last_try in false false false false false :; do
  . ./conf$$subs.sh ||
    as_fn_error $? "could not make $CONFIG_STATUS" "$LINENO" 5

  ac_delim_n=`sed -n "s/.*$ac_delim\$/X/p" conf$$subs.awk | grep -c X`
  if test $ac_delim_n = $ac_delim_num; then
    break
  elif $ac_last_try; then
    as_fn_error $? "could not make $CONFIG_STATUS" "$LINENO" 5
  else
    ac_delim="$ac_delim!$ac_delim _$ac_delim!! "
  fi
done
rm -f conf$$subs.sh

cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
cat >>"\$ac_tmp/subs1.awk" <<\\_ACAWK &&
_ACEOF
sed -n '
h
s/^/S["/; s/!.*/"]=/
p
g
s/^[^!]*!//
:repl
t repl
s/'"$ac_delim"'$//
t delim
:nl
h
s/\(.\{148\}\)..*/\1/
t more1
s/["\\]/\\&/g; s/^/"/; s/$/\\n"\\/
p
n
b repl
:more1
s/["\\]/\\&/g; s/^/"/; s/$/"\\/
p
g
s/.\{148\}//
t nl
:delim
h
s/\(.\{148\}\)..*/\1/
t more2
s/["\\]/\\&/g; s/^/"/; s/$/"/
p
b
:more2
s/["\\]/\\&/g; s/^/"/; s/$/"\\/
p
g
s/.\{148\}//
t delim
' <conf$$subs.awk | sed '
/^[^""]/{
  N
  s/\n//
}
' >>$CONFIG_STATUS || ac_write_fail=1
rm -f conf$$subs.awk
cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
_ACAWK
cat >>"\$ac_tmp/subs1.awk" <<_ACAWK &&
  for (key in S) S_is_set[key] = 1
  FS = ""

}
{
  line = $ 0
  nfields = split(line, field, "@")
  substed = 0
  len = length(field[1])
  for (i = 2; i < nfields; i++) {
    key = field[i]
    keylen = length(key)
    if (S_is_set[key]) {
      value = S[key]
      line = substr(line, 1, len) "" value "" substr(line, len + keylen + 3)
      len += length(value) + length(field[++i])
      substed = 1
    } else
      len += 1 + keylen
  }

  print line
}

_ACAWK
_ACEOF
cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
if sed "s/$ac_cr//" < /dev/null > /dev/null 2>&1; then
  sed "s/$ac_cr\$//; s/$ac_cr/$ac_cs_awk_cr/g"
else
  cat
fi < "$ac_tmp/subs1.awk" > "$ac_tmp/subs.awk" \
  || as_fn_error $? "could not setup config files machinery" "$LINENO" 5
_ACEOF

# VPATH may cause trouble with some makes, so we remove sole $(srcdir),
# ${srcdir} and @srcdir@ entries from VPATH if srcdir is ".", strip leading and
# trailing colons and then remove the whole line if VPATH becomes empty
# (actually we leave an empty line to preserve line numbers).
if test "x$srcdir" = x.; then
  ac_vpsub='/^[	 ]*VPATH[	 ]*=[	 ]*/{
h
s///
s/^/:/
s/[	 ]*$/:/
s/:\$(srcdir):/:/g
s/:\${srcdir}:/:/g
s/:@srcdir@:/:/g
s/^:*//
s/:*$//
x
s/\(=[	 ]*\).*/\1/
G
s/\n//
s/^[^=]*=[	 ]*$//
}'
fi

cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
fi # test -n "$CONFIG_FILES"

# Set up the scripts for CONFIG_HEADERS section.
# No need to generate them if there are no CONFIG_HEADERS.
# This happens for instance with `./config.status Makefile'.
if test -n "$CONFIG_HEADERS"; then
cat >"$ac_tmp/defines.awk" <<\_ACAWK ||
BEGIN {
_ACEOF

# Transform confdefs.h into an awk script `defines.awk', embedded as
# here-document in config.status, that substitutes the proper values into
# config.h.in to produce config.h.

# Create a delimiter string that does not exist in confdefs.h, to ease
# handling of long lines.
ac_delim='%!_!# '
for ac_last_try in false false :; do
  ac_tt=`sed -n "/$ac_delim/p" confdefs.h`
  if test -z "$ac_tt"; then
    break
  elif $ac_last_try; then
    as_fn_error $? "could not make $CONFIG_HEADERS" "$LINENO" 5
  else
    ac_delim="$ac_delim!$ac_delim _$ac_delim!! "
  fi
done

# For the awk script, D is an array of macro values keyed by name,
# likewise P contains macro parameters if any.  Preserve backslash
# newline sequences.

ac_word_re=[_$as_cr_Letters][_$as_cr_alnum]*
sed -n '
s/.\{148\}/&'"$ac_delim"'/g
t rset
:rset
s/^[	 ]*#[	 ]*define[	 ][	 ]*/ /
t def
d
:def
s/\\$//
t bsnl
s/["\\]/\\&/g
s/^ \('"$ac_word_re"'\)\(([^()]*)\)[	 ]*\(.*\)/P["\1"]="\2"\
D["\1"]=" \3"/p
s/^ \('"$ac_word_re"'\)[	 ]*\(.*\)/D["\1"]=" \2"/p
d
:bsnl
s/["\\]/\\&/g
s/^ \('"$ac_word_re"'\)\(([^()]*)\)[	 ]*\(.*\)/P["\1"]="\2"\
D["\1"]=" \3\\\\\\n"\\/p
t cont
s/^ \('"$ac_word_re"'\)[	 ]*\(.*\)/D["\1"]=" \2\\\\\\n"\\/p
t cont
d
:cont
n
s/.\{148\}/&'"$ac_delim"'/g
t clear
:clear
s/\\$//
t bsnlc
s/["\\]/\\&/g; s/^/"/; s/$/"/p
d
:bsnlc
s/["\\]/\\&/g; s/^/"/; s/$/\\\\\\n"\\/p
b cont
' <confdefs.h | sed '
s/'"$ac_delim"'/"\\\
"/g' >>$CONFIG_STATUS || ac_write_fail=1

cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
  for (key in D) D_is_set[key] = 1
  FS = ""
}
/^[\t ]*#[\t ]*(define|undef)[\t ]+$ac_word_re([\t (]|\$)/ {
  line = \$ 0
  split(line, arg, " ")
  if (arg[1] == "#") {
    defundef = arg[2]
    mac1 = arg[3]
  } else {
    defundef = substr(arg[1], 2)
    mac1 = arg[2]
  }
  split(mac1, mac2, "(") #)
  macro = mac2[1]
  prefix = substr(line, 1, index(line, defundef) - 1)
  if (D_is_set[macro]) {
    # Preserve the white space surrounding the "#".
    print prefix "define", macro P[macro] D[macro]
    next
  } else {
    # Replace #undef with comments.  This is necessary, for example,
    # in the case of _POSIX_SOURCE, which is predefined and required
    # on some systems where configure will not decide to define it.
    if (defundef == "undef") {
      print "/*", prefix defundef, macro, "*/"
      next
    }
  }
}
{ print }
_ACAWK
_ACEOF
cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
  as_fn_error $? "could not setup config headers machinery" "$LINENO" 5
fi # test -n "$CONFIG_HEADERS"


eval set X "  :F $CONFIG_FILES  :H $CONFIG_HEADERS    :C $CONFIG_COMMANDS"
shift
for ac_tag
do
  case $ac_tag in
  :[FHLC]) ac_mode=$ac_tag; continue;;
  esac
  case $ac_mode$ac_tag in
  :[FHL]*:*);;
  :L* | :C*:*) as_fn_error $? "invalid tag \`$ac_tag'" "$LINENO" 5;;
  :[FH]-) ac_tag=-:-;;
  :[FH]*) ac_tag=$ac_tag:$ac_tag.in;;
  esac
  ac_save_IFS=$IFS
  IFS=:
  set x $ac_tag
  IFS=$ac_save_IFS
  shift
  ac_file=$1
  shift

  case $ac_mode in
  :L) ac_source=$1;;
  :[FH])
    ac_file_inputs=
    for ac_f
    do
      case $ac_f in
      -) ac_f="$ac_tmp/stdin";;
      *) # Look for the file first in the build tree, then in the source tree
	 # (if the path is not absolute).  The absolute path cannot be DOS-style,
	 # because $ac_f cannot contain `:'.
	 test -f "$ac_f" ||
	   case $ac_f in
	   [\\/$]*) false;;
	   *) test -f "$srcdir/$ac_f" && ac_f="$srcdir/$ac_f";;
	   esac ||
	   as_fn_error 1 "cannot find input file: \`$ac_f'" "$LINENO" 5;;
      esac
      case $ac_f in *\'*) ac_f=`printf "%s\n" "$ac_f" | sed "s/'/'\\\\\\\\''/g"`;; esac
      as_fn_append ac_file_inputs " '$ac_f'"
    done

    # Let's still pretend it is `configure' which instantiates (i.e., don't
    # use $as_me), people would be surprised to read:
    #    /* config.h.  Generated by config.status.  */
    configure_input='Generated from '`
	  printf "%s\n" "$*" | sed 's|^[^:]*/||;s|:[^:]*/|, |g'
	`' by configure.'
    if test x"$ac_file" != x-; then
      configure_input="$ac_file.  $configure_input"
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: creating $ac_file" >&5
printf "%s\n" "$as_me: creating $ac_file" >&6;}
    fi
    # Neutralize special characters interpreted by sed in replacement strings.
    case $configure_input in #(
    *\&* | *\|* | *\\* )
       ac_sed_conf_input=`printf "%s\n" "$configure_input" |
       sed 's/[\\\\&|]/\\\\&/g'`;; #(
    *) ac_sed_conf_input=$configure_input;;
    esac

    case $ac_tag in
    *:-:* | *:-) cat >"$ac_tmp/stdin" \
      || as_fn_error $? "could not create $ac_file" "$LINENO" 5 ;;
    esac
    ;;
  esac

  ac_dir=`$as_dirname -- "$ac_file" ||
$as_expr X"$ac_file" : 'X\(.*[^/]\)//*[^/][^/]*/*$' \| \
	 X"$ac_file" : 'X\(//\)[^/]' \| \
	 X"$ac_file" : 'X\(//\)$' \| \
	 X"$ac_file" : 'X\(/\)' \| . 2>/dev/null ||
printf "%s\n" X"$ac_file" |
    sed '/^X\(.*[^/]\)\/\/*[^/][^/]*\/*$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)[^/].*/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`
  as_dir="$ac_dir"; as_fn_mkdir_p
  ac_builddir=.

case "$ac_dir" in
.) ac_dir_suffix= ac_top_builddir_sub=. ac_top_build_prefix= ;;
*)
  ac_dir_suffix=/`printf "%s\n" "$ac_dir" | sed 's|^\.[\\/]||'`
  # A ".." for each directory in $ac_dir_suffix.
  ac_top_builddir_sub=`printf "%s\n" "$ac_dir_suffix" | sed 's|/[^\\/]*|/..|g;s|/||'`
  case $ac_top_builddir_sub in
  "") ac_top_builddir_sub=. ac_top_build_prefix= ;;
  *)  ac_top_build_prefix=$ac_top_builddir_sub/ ;;
  esac ;;
esac
ac_abs_top_builddir=$ac_pwd
ac_abs_builddir=$ac_pwd$ac_dir_suffix
# for backward compatibility:
ac_top_builddir=$ac_top_build_prefix

case $srcdir in
  .)  # We are building in place.
    ac_srcdir=.
    ac_top_srcdir=$ac_top_builddir_sub
    ac_abs_top_srcdir=$ac_pwd ;;
  [\\/]* | ?:[\\/]* )  # Absolute name.
    ac_srcdir=$srcdir$ac_dir_suffix;
    ac_top_srcdir=$srcdir
    ac_abs_top_srcdir=$srcdir ;;
  *) # Relative name.
    ac_srcdir=$ac_top_build_prefix$srcdir$ac_dir_suffix
    ac_top_srcdir=$ac_top_build_prefix$srcdir
    ac_abs_top_srcdir=$ac_pwd/$srcdir ;;
esac
ac_abs_srcdir=$ac_abs_top_srcdir$ac_dir_suffix


  case $ac_mode in
  :F)
  #
  # CONFIG_FILE
  #

  case $INSTALL in
  [\\/$]* | ?:[\\/]* ) ac_INSTALL=$INSTALL ;;
  *) ac_INSTALL=$ac_top_build_prefix$INSTALL ;;
  esac
_ACEOF

cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
# If the template does not know about datarootdir, expand it.
# FIXME: This hack should be removed a few years after 2.60.
ac_datarootdir_hack=; ac_datarootdir_seen=
ac_sed_dataroot='
/datarootdir/ {
  p
  q
}
/@datadir@/p
/@docdir@/p
/@infodir@/p
/@localedir@/p
/@mandir@/p'
case `eval "sed -n \"\$ac_sed_dataroot\" $ac_file_inputs"` in
*datarootdir*) ac_datarootdir_seen=yes;;
*@datadir@*|*@docdir@*|*@infodir@*|*@localedir@*|*@mandir@*)
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: $ac_file_inputs seems to ignore the --datarootdir setting" >&5
printf "%s\n" "$as_me: WARNING: $ac_file_inputs seems to ignore the --datarootdir setting" >&2;}
_ACEOF
cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
  ac_datarootdir_hack='
  s&@datadir@&$datadir&g
  s&@docdir@&$docdir&g
  s&@infodir@&$infodir&g
  s&@localedir@&$localedir&g
  s&@mandir@&$mandir&g
  s&\\\${datarootdir}&$datarootdir&g' ;;
esac
_ACEOF

# Neutralize VPATH when `$srcdir' = `.'.
# Shell code in configure.ac might set extrasub.
# FIXME: do we really want to maintain this feature?
cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
ac_sed_extra="$ac_vpsub
$extrasub
_ACEOF
cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
:t
/@[a-zA-Z_][a-zA-Z_0-9]*@/!b
s|@configure_input@|$ac_sed_conf_input|;t t
s&@top_builddir@&$ac_top_builddir_sub&;t t
s&@top_build_prefix@&$ac_top_build_prefix&;t t
s&@srcdir@&$ac_srcdir&;t t
s&@abs_srcdir@&$ac_abs_srcdir&;t t
s&@top_srcdir@&$ac_top_srcdir&;t t
s&@abs_top_srcdir@&$ac_abs_top_srcdir&;t t
s&@builddir@&$ac_builddir&;t t
s&@abs_builddir@&$ac_abs_builddir&;t t
s&@abs_top_builddir@&$ac_abs_top_builddir&;t t
s&@INSTALL@&$ac_INSTALL&;t t
$ac_datarootdir_hack
"
eval sed \"\$ac_sed_extra\" "$ac_file_inputs" | $AWK -f "$ac_tmp/subs.awk" \
  >$ac_tmp/out || as_fn_error $? "could not create $ac_file" "$LINENO" 5

test -z "$ac_datarootdir_hack$ac_datarootdir_seen" &&
  { ac_out=`sed -n '/\${datarootdir}/p' "$ac_tmp/out"`; test -n "$ac_out"; } &&
  { ac_out=`sed -n '/^[	 ]*datarootdir[	 ]*:*=/p' \
      "$ac_tmp/out"`; test -z "$ac_out"; } &&
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: $ac_file contains a reference to the variable \`datarootdir'
which seems to be undefined.  Please make sure it is defined" >&5
printf "%s\n" "$as_me: WARNING: $ac_file contains a reference to the variable \`datarootdir'
which seems to be undefined.  Please make sure it is defined" >&2;}

  rm -f "$ac_tmp/stdin"
  case $ac_file in
  -) cat "$ac_tmp/out" && rm -f "$ac_tmp/out";;
  *) rm -f "$ac_file" && mv "$ac_tmp/out" "$ac_file";;
  esac \
  || as_fn_error $? "could not create $ac_file" "$LINENO" 5
 ;;
  :H)
  #
  # CONFIG_HEADER
  #
  if test x"$ac_file" != x-; then
    {
      printf "%s\n" "/* $configure_input  */" >&1 \
      && eval '$AWK -f "$ac_tmp/defines.awk"' "$ac_file_inputs"
    } >"$ac_tmp/config.h" \
      || as_fn_error $? "could not create $ac_file" "$LINENO" 5
    if diff "$ac_file" "$ac_tmp/config.h" >/dev/null 2>&1; then
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: $ac_file is unchanged" >&5
printf "%s\n" "$as_me: $ac_file is unchanged" >&6;}
    else
      rm -f "$ac_file"
      mv "$ac_tmp/config.h" "$ac_file" \
	|| as_fn_error $? "could not create $ac_file" "$LINENO" 5
    fi
  else
    printf "%s\n" "/* $configure_input  */" >&1 \
      && eval '$AWK -f "$ac_tmp/defines.awk"' "$ac_file_inputs" \
      || as_fn_error $? "could not create -" "$LINENO" 5
  fi
 ;;
  
  :C)  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: executing $ac_file commands" >&5
printf "%s\n" "$as_me: executing $ac_file commands" >&6;}
 ;;
  esac


  case $ac_file$ac_mode in
    ".devel":C) if test -f .devel; then
	echo timestamp > stamp-h
	cat $srcdir/Makefile-devel-adds >> Makefile
	make depend || exit 1
fi ;;

  esac
done # for ac_tag


as_fn_exit 0
_ACEOF
ac_clean_files=$ac_clean_files_save

test $ac_write_fail = 0 ||
  as_fn_error $? "write failure creating $CONFIG_STATUS" "$LINENO" 5


# configure is writing to config.log, and then calls config.status.
# config.status does its own redirection, appending to config.log.
# Unfortunately, on DOS this fails, as config.log is still kept open
# by configure, so config.status won't be able to write to it; its
# output is simply discarded.  So we exec the FD to /dev/null,
# effectively closing config.log, so it can be properly (re)opened and
# appended to by config.status.  When coming back to configure, we
# need to make the FD available again.
if test "$no_create" != yes; then
  ac_cs_success=:
  ac_config_status_args=
  test "$silent" = yes &&
    ac_config_status_args="$ac_config_status_args --quiet"
  exec 5>/dev/null
  $SHELL $CONFIG_STATUS $ac_config_status_args || ac_cs_success=false
  exec 5>>config.log
  # Use ||, not &&, to avoid exiting from the if with $? = 1, which
  # would make configure fail if this is the last instruction.
  $ac_cs_success || as_fn_exit 1
fi
if test -n "$ac_unrecognized_opts" && test "$enable_option_checking" != no; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: unrecognized options: $ac_unrecognized_opts" >&5
printf "%s\n" "$as_me: WARNING: unrecognized options: $ac_unrecognized_opts" >&2;}
fi

exit 0
   