#!/bin/bash

echo "=== MySQL发行版检测测试 ==="
echo

echo "1. 测试systemd服务检测："
echo "检查MariaDB服务状态："
systemctl is-active mariadb 2>/dev/null && echo "✅ MariaDB服务运行中" || echo "❌ MariaDB服务未运行"

echo "检查MySQL服务状态："
systemctl is-active mysql 2>/dev/null && echo "✅ MySQL服务运行中" || echo "❌ MySQL服务未运行"

echo "检查mysqld服务状态："
systemctl is-active mysqld 2>/dev/null && echo "✅ mysqld服务运行中" || echo "❌ mysqld服务未运行"

echo

echo "2. 测试进程检测："
ps aux | grep -E '(mysqld|mariadbd)' | grep -v grep | while read line; do
    echo "✅ 发现MySQL进程: $line"
done

echo

echo "3. 测试端口监听检测："
ss -tlnp | grep ':3306 ' | while read line; do
    echo "✅ 发现3306端口监听: $line"
done

echo

echo "4. 测试版本命令检测："
if command -v mysql >/dev/null 2>&1; then
    echo "✅ mysql命令可用："
    mysql --version 2>/dev/null || echo "❌ mysql命令执行失败"
else
    echo "❌ mysql命令不可用"
fi

echo

echo "5. 测试自定义服务名检测："
echo "搜索所有可能的MySQL相关服务："
systemctl list-units --type=service | grep -E '(mysql|mariadb|percona)' | while read line; do
    echo "✅ 发现相关服务: $line"
done

echo

echo "6. 测试Docker容器检测："
if command -v docker >/dev/null 2>&1; then
    echo "检查Docker中的MySQL容器："
    docker ps | grep -E '(mysql|mariadb|percona)' 2>/dev/null | while read line; do
        echo "✅ 发现MySQL容器: $line"
    done
else
    echo "❌ Docker不可用"
fi

echo

echo "7. 测试端口检测："
echo "检查MySQL监听的端口："
ss -tlnp | grep -E '(mysqld|mariadbd|percona)' | while read line; do
    port=$(echo "$line" | awk '{print $4}' | cut -d: -f2)
    echo "✅ 发现MySQL监听端口: $port"
done

echo

echo "8. 测试非标准端口检测："
echo "检查所有可能的MySQL端口："
for port in 3306 3307 3308 3309 33060 33061; do
    if ss -tln | grep ":$port " >/dev/null 2>&1; then
        echo "✅ 端口 $port 有服务监听"
    else
        echo "❌ 端口 $port 无服务监听"
    fi
done

echo

echo "9. 测试配置文件端口检测："
echo "检查MySQL配置文件中的端口设置："
for config in /etc/mysql/my.cnf /etc/my.cnf /etc/mysql/mysql.conf.d/mysqld.cnf /etc/mysql/mariadb.conf.d/50-server.cnf; do
    if [ -f "$config" ]; then
        echo "✅ 发现配置文件: $config"
        grep -i "port" "$config" 2>/dev/null | grep -v "^#" | while read line; do
            echo "  配置: $line"
        done
    fi
done

echo

echo "=== 检测完成 ==="
