    1  15:17:28.958610 IP *************.138 > *************.138: NBT UDP PACKET(138)
    2  15:17:28.958708 IP *************.138 > *************.138: NBT UDP PACKET(138)
    3  15:17:28.959360 IP *************.138 > *************.138: NBT UDP PACKET(138)
    4  15:17:28.961018 IP *************.137 > *************.137: NBT UDP PACKET(137): QUERY; REQUEST; BROADCAST
    5  15:17:29.710899 IP *************.137 > *************.137: NBT UDP PACKET(137): QUERY; REQUEST; BROADCAST
    6  15:17:30.461235 IP *************.137 > *************.137: NBT UDP PACKET(137): QUERY; REQUEST; BROADCAST
    7  15:17:30.798408 IP *************.138 > *************.138: NBT UDP PACKET(138)
    8  15:17:33.464213 IP *************.137 > *************.137: NBT UDP PACKET(137): QUERY; REQUEST; BROADCAST
    9  15:17:34.214302 IP *************.137 > *************.137: NBT UDP PACKET(137): QUERY; REQUEST; BROADCAST
   10  15:17:34.964688 IP *************.137 > *************.137: NBT UDP PACKET(137): QUERY; REQUEST; BROADCAST
   11  15:17:35.473290 ARP, Request who-has *********** tell *************, length 28
   12  15:17:35.481559 ARP, Reply *********** is-at 00:0d:88:4f:25:91, length 46
   13  15:17:35.481577 IP *************.68 > ***********.67: BOOTP/DHCP, Request from 00:04:23:57:a5:7a, length 300
   14  15:17:35.622870 EAP packet (0) v1, len 5
   15  15:17:35.666378 IP 0.0.0.0.68 > ***************.67: BOOTP/DHCP, Request from 00:04:23:57:a5:7a, length 300
   16  15:17:35.851486 IP 0.0.0.0.68 > ***************.67: BOOTP/DHCP, Request from 00:04:23:57:a5:7a, length 300
   17  15:17:36.156548 EAPOL start (1) v1, len 0
   18  15:17:36.158698 EAP packet (0) v1, len 5
   19  15:17:37.766046 EAP packet (0) v1, len 45
   20  15:17:37.790625 EAP packet (0) v1, len 20
   21  15:17:37.830669 EAP packet (0) v1, len 76
   22  15:17:37.848577 EAP packet (0) v1, len 80
   23  15:17:38.661939 EAP packet (0) v1, len 28
   24  15:17:38.685352 EAP packet (0) v1, len 4
   25  15:17:38.686358 EAPOL key (3) v1, len 57
   26  15:17:38.687182 EAPOL key (3) v1, len 44
   27  15:17:39.852392 IP 0.0.0.0.68 > ***************.67: BOOTP/DHCP, Request from 00:04:23:57:a5:7a, length 300
   28  15:17:46.852719 IP 0.0.0.0.68 > ***************.67: BOOTP/DHCP, Request from 00:04:23:57:a5:7a, length 300
   29  15:18:02.852731 IP 0.0.0.0.68 > ***************.67: BOOTP/DHCP, Request from 00:04:23:57:a5:7a, length 300
   30  15:18:08.689384 EAPOL start (1) v1, len 0
   31  15:18:08.696826 EAP packet (0) v1, len 5
   32  15:18:08.713116 EAP packet (0) v1, len 45
   33  15:18:08.787664 EAP packet (0) v1, len 20
   34  15:18:10.344628 EAP packet (0) v1, len 76
   35  15:18:10.473292 EAP packet (0) v1, len 80
   36  15:18:11.152435 EAP packet (0) v1, len 28
   37  15:18:11.251425 EAP packet (0) v1, len 4
   38  15:18:11.252509 EAPOL key (3) v1, len 57
   39  15:18:11.253336 EAPOL key (3) v1, len 44
   40  15:18:35.856823 ARP, Request who-has ************** tell **************, length 28
   41  15:18:35.885105 ARP, Request who-has ************** tell **************, length 28
   42  15:18:36.885304 ARP, Request who-has ************** tell **************, length 28
   43  15:18:37.907817 IP **************.4299 > 239.255.************: UDP, length 133
   44  15:18:37.910524 IP ************** > **********: igmp v3 report, 1 group record(s)
   45  15:18:37.964030 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   46  15:18:38.691974 IP ************** > **********: igmp v3 report, 1 group record(s)
   47  15:18:38.714004 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   48  15:18:39.464435 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   49  15:18:39.898479 IP 0.0.0.0.68 > ***************.67: BOOTP/DHCP, Request from 00:04:23:57:a5:7a, length 300
   50  15:18:40.214836 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   51  15:18:40.909196 IP **************.4299 > 239.255.************: UDP, length 133
   52  15:18:40.965632 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   53  15:18:41.254259 EAPOL start (1) v1, len 0
   54  15:18:41.256353 EAP packet (0) v1, len 5
   55  15:18:41.275901 EAP packet (0) v1, len 45
   56  15:18:41.388857 EAP packet (0) v1, len 20
   57  15:18:41.715620 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   58  15:18:42.466013 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   59  15:18:42.963175 EAP packet (0) v1, len 76
   60  15:18:42.987906 EAP packet (0) v1, len 80
   61  15:18:43.216408 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   62  15:18:43.675053 EAP packet (0) v1, len 28
   63  15:18:43.695554 EAP packet (0) v1, len 4
   64  15:18:43.696547 EAPOL key (3) v1, len 57
   65  15:18:43.697368 EAPOL key (3) v1, len 44
   66  15:18:43.899684 IP 0.0.0.0.68 > ***************.67: BOOTP/DHCP, Request from 00:04:23:57:a5:7a, length 300
   67  15:18:43.909719 IP **************.4299 > 239.255.************: UDP, length 133
   68  15:18:43.967353 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   69  15:18:43.967896 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   70  15:18:44.717196 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   71  15:18:44.718161 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   72  15:18:45.467593 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   73  15:18:45.468557 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   74  15:18:46.217980 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   75  15:18:46.218950 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   76  15:18:46.969929 IP **************.138 > ***************.138: NBT UDP PACKET(138)
   77  15:18:46.970205 IP **************.138 > ***************.138: NBT UDP PACKET(138)
   78  15:18:48.470207 IP **************.138 > ***************.138: NBT UDP PACKET(138)
   79  15:18:49.970986 IP **************.138 > ***************.138: NBT UDP PACKET(138)
   80  15:18:51.471768 IP **************.138 > ***************.138: NBT UDP PACKET(138)
   81  15:18:52.900388 IP 0.0.0.0.68 > ***************.67: BOOTP/DHCP, Request from 00:04:23:57:a5:7a, length 300
   82  15:18:52.972547 IP **************.138 > ***************.138: NBT UDP PACKET(138)
   83  15:18:53.972751 IP **************.138 > ***************.138: NBT UDP PACKET(138)
   84  15:18:54.972939 IP **************.138 > ***************.138: NBT UDP PACKET(138)
   85  15:18:55.973129 IP **************.138 > ***************.138: NBT UDP PACKET(138)
   86  15:18:56.973475 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   87  15:18:57.723686 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   88  15:18:58.474079 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   89  15:18:59.224473 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   90  15:18:59.974983 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   91  15:19:00.725263 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   92  15:19:01.475654 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   93  15:19:02.226046 IP **************.137 > ***************.137: NBT UDP PACKET(137): REGISTRATION; REQUEST; BROADCAST
   94  15:19:02.976511 IP **************.138 > ***************.138: NBT UDP PACKET(138)
   95  15:19:02.976737 IP **************.138 > ***************.138: NBT UDP PACKET(138)
   96  15:19:02.977520 IP **************.138 > ***************.138: NBT UDP PACKET(138)
   97  15:19:02.979092 IP **************.137 > ***************.137: NBT UDP PACKET(137): QUERY; REQUEST; BROADCAST
   98  15:19:03.728840 IP **************.137 > ***************.137: NBT UDP PACKET(137): QUERY; REQUEST; BROADCAST
   99  15:19:04.479238 IP **************.137 > ***************.137: NBT UDP PACKET(137): QUERY; REQUEST; BROADCAST
  100  15:19:07.482218 IP **************.137 > ***************.137: NBT UDP PACKET(137): QUERY; REQUEST; BROADCAST
  101  15:19:08.232205 IP **************.137 > ***************.137: NBT UDP PACKET(137): QUERY; REQUEST; BROADCAST
  102  15:19:08.982597 IP **************.137 > ***************.137: NBT UDP PACKET(137): QUERY; REQUEST; BROADCAST
  103  15:19:09.900631 IP 0.0.0.0.68 > ***************.67: BOOTP/DHCP, Request from 00:04:23:57:a5:7a, length 300
  104  15:19:13.696821 EAPOL start (1) v1, len 0
  105  15:19:13.704581 EAP packet (0) v1, len 5
  106  15:19:13.718221 EAP packet (0) v1, len 45
  107  15:19:13.734974 EAP packet (0) v1, len 20
  108  15:19:14.801245 IP **************.138 > ***************.138: NBT UDP PACKET(138)
  109  15:19:15.293800 EAP packet (0) v1, len 76
  110  15:19:15.312531 EAP packet (0) v1, len 80
  111  15:19:15.997763 EAP packet (0) v1, len 28
  112  15:19:16.022323 EAP packet (0) v1, len 4
  113  15:19:16.023335 EAPOL key (3) v1, len 57
  114  15:19:16.024149 EAPOL key (3) v1, len 44
