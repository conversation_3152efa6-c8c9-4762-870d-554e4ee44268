@(#) $Header: /tcpdump/master/libpcap/CHANGES,v ******** 2005/09/05 09:17:47 guy Exp $ (LBL)

Mon. 	September 5, 2005.  <EMAIL>. Summary for 0.9.4 libpcap release

	Support for radiotap on Linux (<PERSON>)
	Fixes for HP-UX
	Support for additional Juniper link-layer types
	Fixes for filters on MPLS-encapsulated packets
	"vlan" filter fixed
	"pppoed" and "pppoes" filters added; the latter modifies later
	parts of the filter expression to look at the PPP headers and
	headers in the PPP payload

Tue. 	July 5, 2005.  <EMAIL>. Summary for 0.9.3 libpcap release

	Fixes for compiling on nearly every platform,
		including improved 64bit support
	MSDOS Support
	Add support for sending packets
	OpenBSD pf format support
	IrDA capture (Linux only)

Tue.   March 30, 2004. <EMAIL>. Summary for 3.8.3 release

	Fixed minor problem in gencode.c that would appear on 64-bit
	platforms.
	Version number is now sane.

Mon.   March 29, 2004. <EMAIL>. Summary for 3.8.2 release

	updates for autoconf 2.5
	fixes for ppp interfaces for freebsd 4.1
	pcap gencode can generate code for 802.11, IEEE1394, and pflog.

Wed.   November 12, 2003. <EMAIL>. Summary for 0.8 release

	added pcap_findalldevs()
	Win32 patches from NetGroup, Politecnico di Torino (Italy)
	OpenBSD pf, DLT_PFLOG added
	Many changes to ATM support.
	lookup pcap_lookupnet()
	Added DLT_ARCNET_LINUX, DLT_ENC, DLT_IEEE802_11_RADIO, DLT_SUNATM,
		DLT_IP_OVER_FC, DLT_FRELAY, others.
	Sigh.  More AIX wonderfulness.
	Document updates.
	Changes to API: pcap_next_ex(), pcap_breakloop(), pcap_dump_flush(),
			pcap_list_datalinks(), pcap_set_datalink(),
			pcap_lib_version(), pcap_datalink_val_to_name(),
			pcap_datalink_name_to_val(), new error returns.

Tuesday, February 25, 2003. <EMAIL>.  0.7.2 release

 	Support link types that use 802.2 always, never, and sometimes.
 	Don't decrease the size of the BPF buffer from the default.
 	Support frame relay.
 	Handle 32-bit timestamps in DLPI, and pass the right buffer size.
 	Handle Linux systems with modern kernel but without
 	 SOL_PACKET in the userland headers.
 	Linux support for ARPHRD_RAWHDLC.
 	Handle 32-bit timestamps in snoop.
 	Support eg (Octane/O2xxx/O3xxx Gigabit) devices.
 	Add new reserved DLT types.

Monday October 23, 2001. <EMAIL>. Summary for 0.7 release

	Added pcap_findalldevs() call to get list of interfaces in a MI way.

	pcap_stats() has been documented as to what its counters mean on
	each platform.

Tuesday January 9, 2001. <EMAIL>. Summary for 0.6 release

	New Linux libpcap implementation, which, in 2.2 and later
	kernels, uses PF_PACKET sockets and supports kernel packet
	filtering (if compiled into the kernel), and supports the "any"
	device for capturing on all interfaces.  Cleans up promiscuous
	mode better on pre-2.2 kernels, and has various other fixes
	(handles 2.4 ARPHRD_IEEE802_TR, handles ISDN devices better,
	doesn't show duplicate packets on loopback interface, etc.).

	Fixed HP-UX libpcap implementation to correctly get the PPA for
	an interface, to allow interfaces to be opened by interface name.

	libpcap savefiles have system-independent link-layer type values
	in the header, rather than sometimes platform-dependent DLT_
	values, to make it easier to exchange capture files between
	different OSes.

	Non-standard capture files produced by some Linux tcpdumps, e.g.
	the one from Red Hat Linux 6.2 and later, can now be read.

	Updated autoconf stock files.

	Filter expressions can filter on VLAN IDs and various OSI
	protocols, and work on Token Ring (with non-source-routed
	packets).

	"pcap_open_dead()" added to allow compiling filter expressions
	to pcap code without opening a capture device or capture file.

	Header files fixed to allow use in C++ programs.

	Removed dependancy on native headers for packet layout.
	Removed Linux specific headers that were shipped.

	Security fixes: Strcpy replaced with strlcpy, sprintf replaced
	with snprintf.

	Fixed bug that could cause subsequent "pcap_compile()"s to fail
	erroneously after one compile failed.

	Assorted other bug fixes.

	README.aix and README.linux files added to describe
	platform-specific issues.

	"getifaddrs()" rather than SIOCGIFCONF used, if available.

v0.5 Sat Jun 10 11:09:15 PDT 2000

<EMAIL>
- Brought in KAME IPv6/IPsec bpf compiler.
- Fixes for NetBSD.
- Support added for OpenBSD DLT_LOOP and BSD/OS DLT_C_HDLC (Cisco HDLC),
  and changes to work around different BSDs having different DLT_ types
  with the same numeric value.

Assar Westerlund  <<EMAIL>>
- Building outside the source code tree fixed.
- Changed to write out time stamps with 32-bit seconds and microseconds
  fields, regardless of whether those fields are 32 bits or 64 bits in
  the OS's native "struct timeval".
- Changed "pcap_lookupdev()" to dynamically grow the buffer into which
  the list of interfaces is read as necessary in order to hold the
  entire list.

Greg Troxel <<EMAIL>>
- Added a new "pcap_compile_nopcap()", which lets you compile a filter
  expression into a BPF program without having an open live capture or
  capture file.

v0.4 Sat Jul 25 12:40:09 PDT 1998

- Fix endian problem with DLT_NULL devices. From FreeBSD via Bill
  Fenner (<EMAIL>)

- Fix alignment problem with FDDI under DLPI. This was causing core
  dumps under Solaris.

- Added configure options to disable flex and bison. Resulted from a
  bug <NAME_EMAIL> (Bruce Barnett). Also added
  options to disable gcc and to force a particular packet capture type.

- Added support for Fore ATM interfaces (qaa and fa) under IRIX. Thanks
  to John Hawkinson (<EMAIL>)

- Change Linux PPP and SLIP to use DLT_RAW since the kernel does not
  supply any "link layer" data.

- Change Linux to use SIOCGIFHWADDR ioctl to determine link layer type.
  Thanks to Thomas Sailer (<EMAIL>)

- Change IRIX PPP to use DLT_RAW since the kernel does not supply any
  "link layer" data.

- Modified to support the new BSD/OS 2.1 PPP and SLIP link layer header
  formats.

- Added some new SGI snoop interface types. Thanks to Steve Alexander
  (<EMAIL>)

- Fixes for HP-UX 10.20 (which is similar to HP-UX 9). Thanks to
  Richard Allen (<EMAIL>) and Steinar Haug (<EMAIL>)

- Fddi supports broadcast as reported by Jeff Macdonald
  (<EMAIL>). Also correct ieee802 and arcnet.

- Determine Linux pcap buffer size at run time or else it might not be
  big enough for some interface types (e.g. FDDI). Thanks to Jes
  Sorensen (<EMAIL>)

- Fix some linux alignment problems.

- Document promisc argument to pcap_open_live(). Reported by Ian Marsh
  (<EMAIL>)

- Support Metricom radio packets under Linux. Thanks to Kevin Lai
  (<EMAIL>)

- Bind to interface name under Linux to avoid packets from multiple
  interfaces on multi-homed hosts. Thanks to Kevin Lai
  (<EMAIL>)

- Change L_SET to SEEK_SET for HP-UX. Thanks to Roland Roberts
  (<EMAIL>)

- Fixed an uninitialized memory reference found by Kent Vander Velden
  (<EMAIL>)

- Fixed lex pattern for IDs to allow leading digits. As reported by
  Theo de Raadt (<EMAIL>)

- Fixed Linux include file problems when using GNU libc.

- Ifdef ARPHRD_FDDI since not all versions of the Linux kernel have it.
  Reported reported by Eric Jacksch (<EMAIL>)

- Fixed bug in pcap_dispatch() that kept it from returning on packet
  timeouts.

- Changed ISLOOPBACK() macro when IFF_LOOPBACK isn't available to check
  for "lo" followed by an eos or digit (newer versions of Linux
  apparently call the loopback "lo" instead of "lo0").

- Fixed Linux networking include files to use ints instead of longs to
  avoid problems with 64 bit longs on the alpha. Thanks to Cristian
  Gafton (<EMAIL>)

v0.3 Sat Nov 30 20:56:27 PST 1996

- Added Linux support.

- Fixed savefile bugs.

- Solaris x86 fix from Tim Rylance (<EMAIL>)

- Add support for bpf kernel port filters.

- Remove duplicate atalk protocol table entry. Thanks to Christian
  Hopps (<EMAIL>)

- Fixed pcap_lookupdev() to ignore nonexistent devices. This was
  reported to happen under BSD/OS by David Vincenzetti
  (<EMAIL>)

- Avoid solaris compiler warnings. Thanks to Bruce Barnett
  (<EMAIL>)

v0.2.1 Sun Jul 14 03:02:26 PDT 1996

- Fixes for HP-UX 10. Thanks in part to to Thomas Wolfram
  (<EMAIL>) and Rick Jones (<EMAIL>)

- Added support for SINIX. Thanks to Andrej Borsenkow
  (<EMAIL>)

- Fixes for AIX (although this system is not yet supported). Thanks to
  John Hawkinson (<EMAIL>)

- Use autoconf's idea of the top level directory in install targets.
  Thanks to John Hawkinson.

- Add missing autoconf packet capture result message. Thanks to Bill
  Fenner (<EMAIL>)

- Fixed padding problems in the pf module.

- Fixed some more alignment problems on the alpha.

- Added explicit netmask support. Thanks to Steve Nuchia
  (<EMAIL>)

- Fixed to handle raw ip addresses such as ******* without "left
  justifing"

- Add "sca" keyword (for DEC cluster services) as suggested by Terry
  Kennedy (<EMAIL>)

- Add "atalk" keyword as suggested by John Hawkinson.

- Add "igrp" keyword.

- Fixed HID definition in grammar.y to be a string, not a value.

- Use $CC when checking gcc version. Thanks to Carl Lindberg
  (<EMAIL>)

- Removed obsolete reference to pcap_immediate() from the man page.
  Michael Stolarchuk (<EMAIL>)

- DLT_NULL has a 4 byte family header. Thanks to Jeffrey Honig
  (<EMAIL>)

v0.2 Sun Jun 23 02:28:42 PDT 1996

- Add support for HP-UX. Resulted from code contributed by Tom Murray
  (<EMAIL>) and Philippe-Andri Prindeville
  (<EMAIL>)

- Update INSTALL with a reminder to install include files. Thanks to
  Mark Andrews (<EMAIL>)

- Fix bpf compiler alignment bug on the alpha.

- Use autoconf to detect architectures that can't handle misaligned
  accesses.

- Added loopback support for snoop. Resulted from report Steve
  Alexander (<EMAIL>)

v0.1 Fri Apr 28 18:11:03 PDT 1995

- Fixed compiler and optimizer bugs.  The BPF filter engine uses unsigned
  comparison operators, while the code generator and optimizer assumed
  signed semantics in several places.  Thanks to Charlie Slater
  (<EMAIL>) for pointing this out.

- Removed FDDI ifdef's, they aren't really needed. Resulted from report
  by Gary Veum (<EMAIL>).

- Add pcap-null.c which allows offline use of libpcap on systems that
  don't support live package capture. This feature resulting from a
  request from Jan van Oorschot (<EMAIL>).

- Make bpf_compile() reentrant. Fix thanks to Pascal Hennequin
  (<EMAIL>).

- Port to GNU autoconf.

- Fix pcap-dlpi.c to work with isdn. Resulted from report by Flemming
  Johansen (<EMAIL>).

- Handle multi-digit interface unit numbers (aka ppa's) under dlpi.
  Resulted from report by Daniel Ehrlich (<EMAIL>).

- Fix pcap-dlpi.c to work in non-promiscuous mode. Resulted from report
  by Jeff Murphy (<EMAIL>).

- Add support for "long jumps". Thanks to Jeffrey Mogul
  (<EMAIL>).

- Fix minor problems when compiling with BDEBUG as noticed by Scott
  Bertilson (<EMAIL>).

- Declare sys_errlist "const char *const" to avoid problems under
  FreeBSD. Resulted from <NAME_EMAIL>.

v0.0.6 Fri Apr 28 04:07:13 PDT 1995

- Add missing variable declaration missing from 0.0.6

v0.0.5 Fri Apr 28 00:22:21 PDT 1995

- Workaround for problems when pcap_read() returns 0 due to the timeout
  expiring.

v0.0.4 Thu Apr 20 20:41:48 PDT 1995

- Change configuration to not use gcc v2 flags with gcc v1.

- Fixed a bug in pcap_next(); if pcap_dispatch() returns 0, pcap_next()
  should also return 0. Thanks to Richard Stevens (<EMAIL>).

- Fixed configure to test for snoop before dlpi to avoid problems under
  IRIX 5. Thanks to J. Eric Townsend (<EMAIL>).

- Hack around deficiency in Ultrix's make.

- Fix two bugs related to the Solaris pre-5.3.2 bufmod bug; handle
  savefiles that have more than snapshot bytes of data in them (so we
  can read old savefiles) and avoid writing such files.

- Added checkioctl which is used with gcc to check that the
  "fixincludes" script has been run.

v0.0.3 Tue Oct 18 18:13:46 PDT 1994

- Fixed configure to test for snoop before dlpi to avoid problems under
  IRIX 5. Thanks to J. Eric Townsend (<EMAIL>).

v0.0.2 Wed Oct 12 20:56:37 PDT 1994

- Implement timeout in the dlpi pcap_open_live(). Thanks to Richard
  Stevens.

- Determine pcap link type from dlpi media type. Resulted from report
  by Mahesh Jethanandani (<EMAIL>).

v0.0.1 Fri Jun 24 14:50:57 PDT 1994

- Fixed bug in nit_setflags() in pcap-snit.c. The streams ioctl timeout
  wasn't being initialized sometimes resulting in an "NIOCSFLAGS:
  Invalid argument" error under OSF/1. Reported by Matt Day
  (<EMAIL>) and Danny Mitzel (<EMAIL>).

- Turn on FDDI support by default.

v0.0 Mon Jun 20 19:20:16 PDT 1994

- Initial release.

- Fixed bug with greater/less keywords, reported by Mark Andrews
  (<EMAIL>).

- Fix bug where '|' was defined as BPF_AND instead of BPF_OR, reported
  by Elan Amir (<EMAIL>).

- Machines with little-endian byte ordering are supported thanks to
  Jeff Mogul.

- Add hack for version 2.3 savefiles which don't have caplen and len
  swapped thanks to Vern Paxson.

- Added "&&" and "||" aliases for "and" and "or" thanks to Vern Paxson.

- Added length, inbound and outbound keywords.
