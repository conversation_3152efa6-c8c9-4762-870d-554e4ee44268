#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

// 从配置文件检测MySQL日志路径
static char* get_mysql_log_from_config() {
    FILE* fp;
    char line[512];
    static char log_path[256] = {0};
    
    // 常见的MySQL配置文件位置
    const char* config_files[] = {
        "/etc/mysql/my.cnf",
        "/etc/my.cnf", 
        "/usr/local/mysql/my.cnf",
        "/var/lib/mysql/my.cnf",
        "/etc/mysql/mysql.conf.d/mysqld.cnf",
        "/etc/mysql/mariadb.conf.d/50-server.cnf",
        "/etc/mysql/mariadb.conf.d/99-custom-logging.cnf",
        NULL
    };
    
    // 扫描配置文件
    for (int i = 0; config_files[i] != NULL; i++) {
        printf("检查配置文件: %s\n", config_files[i]);
        fp = fopen(config_files[i], "r");
        if (fp != NULL) {
            printf("  文件可读\n");
            while (fgets(line, sizeof(line), fp)) {
                // 跳过注释和空行
                char* trimmed = line;
                while (*trimmed == ' ' || *trimmed == '\t') trimmed++;
                if (*trimmed == '#' || *trimmed == '\n' || *trimmed == '\0') continue;
                
                printf("  检查行: %s", line);
                
                // 查找log_error配置项
                if (strstr(trimmed, "log_error") && strstr(trimmed, "=")) {
                    printf("  找到log_error配置!\n");
                    char* eq = strchr(trimmed, '=');
                    if (eq != NULL) {
                        eq++; // 跳过等号
                        while (*eq == ' ' || *eq == '\t') eq++; // 跳过空格
                        
                        // 提取路径
                        char* end = eq;
                        while (*end != '\0' && *end != '\n' && *end != '\r' && *end != ' ' && *end != '\t') end++;
                        *end = '\0';
                        
                        if (strlen(eq) > 0) {
                            strncpy(log_path, eq, sizeof(log_path) - 1);
                            log_path[sizeof(log_path) - 1] = '\0';
                            printf("  提取到路径: %s\n", log_path);
                            fclose(fp);
                            return log_path;
                        }
                    }
                }
            }
            fclose(fp);
        } else {
            printf("  文件不可读\n");
        }
    }
    
    return NULL;
}

int main() {
    printf("测试MySQL日志路径检测...\n");
    
    char* log_path = get_mysql_log_from_config();
    if (log_path != NULL) {
        printf("找到日志路径: %s\n", log_path);
        if (access(log_path, R_OK) == 0) {
            printf("日志文件可读: 是\n");
        } else {
            printf("日志文件可读: 否\n");
        }
    } else {
        printf("未找到日志路径配置\n");
    }
    
    return 0;
}
