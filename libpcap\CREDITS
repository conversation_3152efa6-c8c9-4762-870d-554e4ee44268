This file lists people who have contributed to libpcap.

The current maintainers (in alphabetical order):
    <PERSON>                <denis at ovsienko dot info>
    <PERSON><PERSON><PERSON><PERSON>       <devel dot fx dot lebail at orange dot fr>
    <PERSON>                    <gharris at sonic dot net>
    <PERSON>            <mcr at sandelman dot ottawa dot on dot ca>

Additional people who have contributed patches (in alphabetical order):
    <PERSON>                  <adbudau at bitdefender dot com>
    <PERSON><PERSON>                   <axos88 at gmail dot com>
    <PERSON>                   <Alan at LCS dot MIT dot EDU>
    <PERSON>                   <china at thewrittenword dot com>
    <PERSON>             <al at galanin dot nnov dot ru>
    <PERSON> '<PERSON>      <Leo dot Bergolth at wu-wien dot ac dot at>
    <PERSON><PERSON>              <kuznet at ms2 dot inr dot ac dot ru>
    <PERSON><PERSON>             <vishnya at ispras dot ru>
    <PERSON>                    <44322503+MadAlexUK at users dot noreply dot github dot com>
    <PERSON>     <al<PERSON><PERSON><PERSON><PERSON><PERSON>nandez at gmail dot com>
    <PERSON>                <autostart dot ini at gmail dot com>
    <PERSON><PERSON> Klink                   <alois at aloisklink dot com>
    Al<PERSON> Bar-Lev                  <alonbl at sourceforge dot net>
    <PERSON>                 <anders dot broman at ericsson dot com>
    <PERSON><PERSON>                 <andres dot p at zoho dot com>
    <PERSON>                  <atatat at atatdot dot net>
                                  <andy-1 at sourceforge dot net>
    Ani Sinha                     <ani at aristanetworks dot com>
    Anthony Kirby                 <Anthony dot Kirby at nominet dot uk>
    Antonio Vázquez Blanco        <antonio dot vazquez at tarlogic dot com>
    Antti Kantee                  <pooka at netbsd dot org>
    Archit Shah                   <archit at cave32 dot com>
    Arien Vijn                    <arienvijn at sourceforge dot net>
    Arkadiusz Miskiewicz          <misiek at pld dot org dot pl>
    Armando L. Caro Jr.           <acaro at mail dot eecis dot udel dot edu>
    Assar Westerlund              <assar at sics dot se>
    Atsushi Yasumoto              <30277794+atusy at users dot noreply dot github dot com>
    Atzm Watanabe                 <atzm at atzm dot org>
    Baptiste Peugnez              <baptiste dot peugnez at cea dot fr>
    Baruch Siach                  <baruch at tkos dot co dot il>
    Bill Parker                   <wp02855 at gmail dot com>
    Biswapriyo Nath               <nathbappai at gmail dot com>
    blazeable                     <blazeable at blazeable dot eu>
    bleader                       <bleader at ratonland dot org>
    Brent Cook                    <brent at boundary dot com>
    Brian Ginsbach                <ginsbach at cray dot com>
    B. Scott Michel               <scooter dot phd at gmail dot com>
    Cedric Cellier                <rixed at happyleptic dot org>
    Charles M. Hannum             <mycroft at netbsd dot org>
    Chris G. Demetriou            <cgd at netbsd dot org>
    Chris Lightfoot               <cwrl at users dot sourceforge dot net>
    Chris Maynard                 <Chris dot Maynard at gtech dot com>
    Chris Pepper                  <pepper at mail dot reppep dot com>
    Christian Bell                <csbell at myri dot com>
    Christian Peron               <csjp at freebsd dot org>
    Christian Svensson            <blue at cmd dot nu>
    Christopher K Lee             <christopher dot lee at cspi dot com>
    Clément Péron                 <peron dot clem at gmail dot com>
    Daniel Borkmann               <dborkman at redhat dot com>
    Daniele Orlandi               <daniele at orlandi dot com>
    Daniel Lublin                 <daniel at lublin dot se>
    Daniel Miller                 <dmiller at nmap dot org>
    Dario Lombardo                <lomato at gmail dot com>
    Darren Lim                    <darren dot lim at endace dot com>
    Darren Reed                   <darrenr at sun dot com>
    Dave Barach                   <dave at barachs dot net>
    David Clark                   <david dot clark at datasoft dot com>
    David Kaelbling               <drk at sgi dot com>
    David Karoly                  <david dot karoly at outlook dot com>
    David Ward                    <david dot ward at ll dot mit dot edu>
    David Young                   <dyoung at ojctech dot com>
    Dean Gaudet                   <dean at arctic dot org>
    dhruv                         <rsrivat at sourceforge dot net>
    Dmytro Ovdiienko              <dmitriy dot ovdienko at gmail dot com>
    Don Ebright                   <Don dot Ebright at compuware dot com>
    Dug Song                      <dugsong at monkey dot org>
    Dustin Spicuzza               <dustin at virtualroadside dot com>
    dzejarczech                   <dzejarczech at sourceforge dot net>
    Ed Maste                      <emaste at FreeBSD dot org>
    Edward Sheldrake              <ejs1920 at sourceforge dot net>
    Ege Çetin                     <egecetin at hotmail dot com dot tr>
    Eli Schwartz                  <eschwartz93 at gmail dot com>
    Eric Anderson                 <anderse at hpl dot hp dot com>
    Erik de Castro Lopo           <erik dot de dot castro dot lopo at sensorynetworks dot com>
    Eugene Exarevsky              <eugene dot exarevsky at dsr-corporation dot com>
    Fedor Sakharov                <fedor dot sakharov at gmail dot com>
    Felix Janda                   <felix dot janda at posteo dot de>
    Felix Obenhuber               <felix at obenhuber dot de>
    fghzxm                        <fghzxm at outlook dot com>
    Florent Drouin                <Florent dot Drouin at alcatel-lucent dot fr>
    Florian Fainelli              <f dot fainelli at gmail dot com>
    François Revol                <revol at free dot fr>
    Frank Gorgas-Waller           <frank dot gorgas-waller at auerswald dot de>
    Franz Schaefer                <schaefer at mond dot at>
    frederich                     <frederich at sourceforge dot net>
    Fulko Hew                     <fulko dot hew at gmail dot com>
    Fumiyuki Shimizu              <fumifumi at abacustech dot jp>
    Gabor Tatarka                 <gabor dot tatarka at ericsson dot com>
    Gabriel Ganne                 <gabriel dot ganne at gmail dot com>
    Garrett Cooper                <yaberauneya at sourceforge dot net>
    George Neville-Neil           <gnn at freebsd dot org>
    Gerald Combs                  <gerald at zing dot org>
    Gerard Garcia                 <nouboh at gmail dot com>
    Gianluca Varenni              <gianluca dot varenni at gmail dot com>
    Gilbert Hoyek                 <gil_hoyek at hotmail dot com>
    Gisle Vanem                   <gvanem at yahoo dot no>
    Gokul Sivakumar               <gokulkumar792 at gmail dot com>
    Graeme Hewson                 <ghewson at cix dot compulink dot co dot uk>
    Gregor Maier                  <gregor at net dot in dot tum dot de>
    Greg Stark                    <gsstark at mit dot edu>
    Greg Troxel                   <gdt at ir dot bbn dot com>
    Guillaume Pelat               <endymion_ at users dot sourceforge dot net>
    Gustavo Zacarias              <gustavo at zacarias dot com dot ar>
    Hagen Paul Pfeifer            <hagen at jauu dot net>
    Hans Leidekker                <hans at meelstraat dot net>
    headshog                      <craaaaaachind at gmail dot com>
    Henri Chataing                <henrichataing at google dot com>
    Henri Doreau                  <hdoreau at sourceforge dot net>
    Hiroaki KAWAI                 <kawai at stratosphere dot co dot jp>
    hopper-vul                    <hopper dot vul at gmail dot com>
    Hyung Sik Yoon                <hsyn at kr dot ibm dot com>
    Igor Khristophorov            <igor at atdot dot org>
    Jakub Sitnicki                <jsitnicki at gmail dot com>
    Jakub Zawadzki                <darkjames at darkjames dot pl>
    James Ko                      <jck at exegin dot com>
    Jan Adam                      <jadam at hilscher dot com>
    Jan-Philip Velders            <jpv at veldersjes dot net>
    Jason R. Thorpe               <thorpej at netbsd dot org>
    Javier Achirica               <achirica at ttd dot net>
    Jean-Louis Charton            <Jean-Louis dot CHARTON at oikialog dot com>
    Jean Tourrilhes               <jt at hpl dot hp dot com>
    Jefferson Ogata               <jogata at nodc dot noaa dot gov>
    Jerome Duval                  <jerome dot duval at gmail dot com>
    Jesper Dangaard Brouer        <hawk at comx dot dk>
    Jesper Peterson               <jesper at endace dot com>
    Jesse Gross                   <jesse at nicira dot com>
    Jessica Clarke                <jrtc27 at jrtc27 dot com>
    JHA                           <jon dot anderson at oracle dot com>
    jingyu yang                   <jingleyang at users dot noreply dot github dot com>
    Jiri Slaby                    <jirislaby at gmail dot com>
    João Valverde                 <joao dot valverde at tecnico dot ulisboa dot pt>
    Joel                          <********+joelg989 at users dot noreply dot github dot com>
    Joerg Mayer                   <jmayer at loplof dot de>
    John Bankier                  <jbankier at rainfinity dot com>
    Jon Lindgren                  <jonl at yubyub dot net>
    Jon Smirl                     <jonsmirl at gmail dot com>
    Jorge Boncompte [DTI2]        <jorge at dti2 dot net>
    Josh Soref                    <2119212+jsoref at users dot noreply dot github dot com>
    jromanr                       <jromanr at hotmail dot com>
    Juergen Schoenwaelder         <schoenw at ibr dot cs dot tu-bs dot de>
    Julien Moutinho               <julm at savines dot alpes dot fr dot eu dot org>
    Jung-uk Kim                   <jkim at FreeBSD dot org>
    Kazushi Sugyo                 <sugyo at pb dot jp dot nec dot com>
    Kenny Luong                   <kluong at cloudflare dot com>
    Kevin Boulain                 <kevin dot boulain at securactive dot net>
    Klaus Klein                   <kleink at netbsd dot org>
    Koryn Grant                   <koryn at endace dot com>
    Kris Katterjohn               <katterjohn at gmail dot com>
    Krzysztof Halasa              <khc at pm dot waw dot pl>
    Lennert Buytenhek             <buytenh at wantstofly dot org>
    Li kunyu                      <kunyu at nfschina dot com>
    lixiaoyan                     <lixiaoyan at google dot com>
    Lorenzo Cavallaro             <sullivan at sikurezza dot org>
    Loris Degioanni               <loris at netgroup-serv dot polito dot it>
    Love Hörnquist-Åstrand        <lha at stacken dot kth dot se>
    Lubomir Varga                 <lubomir dot varga at qpp dot sk>
    Luis MartinGarcia             <luis dot mgarc at gmail dot com>
    Luiz Angelo Daros de Luca     <luizluca at gmail dot com>
    lxy                           <391861737 at qq dot com>
    Maciej W. Rozycki             <macro at ds2 dot pg dot gda dot pl>
    Mansour Behabadi              <mansour at oxplot dot com>
    Marcus Felipe Pereira         <marcus at task dot com dot br>
    Mario J. Rugiero              <mrugiero at gmail dot com>
    Mark C. Brown                 <mbrown at hp dot com>
    Mark Johnston                 <markjdb at gmail dot com>
    Mark Marshall                 <mark dot marshall at omicronenergy dot com>
    Mark Pizzolato                <List-tcpdump-workers at subscriptions dot pizzolato dot net>
    Markus Mayer                  <markus_mayer at sourceforge dot net>
    Martin Husemann               <martin at netbsd dot org>
    Martin Kaiser                 <martin at kaiser dot cx>
    Márton Németh                 <nm127 at freemail dot hu>
    Matias Karhumaa               <matias dot karhumaa at gmail dot com>
    Matt Eaton                    <agnosticdev at gmail dot com>
    Matthew Luckie                <mjl at luckie dot org dot nz>
    Matthias Hannig               <matthias at hannig dot cc>
    Matwey V. Kornilov            <matwey dot kornilov at gmail dot com>
    maxice8                       <thinkabit dot ukim at gmail dot com>
    Max Laier                     <max at love2party dot net>
    Michal Kubecek                <mkubecek at suse dot cz>
    Michal Labedzki               <michal dot labedzki at tieto dot com>
    Michal Ruprich                <michalruprich at gmail dot com>
    Michal Sekletar               <msekleta at redhat dot com>
    Mike Frysinger                <vapier at gmail dot com>
    Mike Kershaw                  <dragorn at kismetwireless dot net>
    Mike Wiacek                   <mike at iroot dot net>
    Milosz Kaniewski              <milosz dot kaniewski at gmail dot com>
    Miroslav Lichvar              <mlichvar at redhat dot com>
    Monroe Williams               <monroe at pobox dot com>
    Myricom Help                  <myri at users dot noreply dot github dot com>
    Nan Xiao                      <nan at chinadtrace dot org>
    nic-kaczinsky                 <68271784+nic-kaczinsky at users dot noreply dot github dot com>
    Nick Kelsey                   <nickk at silicondust dot com>
    Nicolas Dade                  <ndade at nsd dot dyndns dot org>
    Niko Delarich                 <niko dot delarich at gmail dot com>
    Nikolay Edigaryev             <edigaryev at gmail dot com>
    N. Leiten                     <nleiten at sourceforge dot net>
    nnposter                      <nnposter at users dot noreply dot github dot com>
                                  <nvercamm at sourceforge dot net>
    Octavian Cerna                <tavy at ylabs dot com>
    Olaf Kirch                    <okir at caldera dot de>
    Ollie Wild                    <aaw at users dot sourceforge dot net>
    Ondřej Hošek                  <ondra dot hosek at gmail dot com>
    Onno van der Linden           <onno at simplex dot nl>
    Orgad Shaneh                  <orgad dot shaneh at audiocodes dot com>
    Ørjan Malde                   <red at foxi dot me>
    Paolo Abeni                   <pabeni at redhat dot com>
    Patrick Marie                 <mycroft at virgaria dot org>
    Patrick McHardy               <kaber at trash dot net>
    Paul Mundt                    <lethal at linux-sh dot org>
    Pavel Kankovsky               <kan at dcit dot cz>
    Pawel Brzezinski              <pawel dot brzezinski at harman dot com>
    Pawel Pokrywka                <publicpp at gmail dot com>
    Peter Fales                   <peter at fales-lorenz dot net>
    Peter Jeremy                  <peter dot jeremy at alcatel dot com dot au>
    Peter Volkov                  <pva at gentoo dot org>
    Petr Vorel                    <pvorel at suse dot cz>
    Philippe Antoine              <contact at catenacyber dot fr>
    Phil Wood                     <cpw at lanl dot gov>
    Rafal Maszkowski              <rzm at icm dot edu dot pl>
    ramin                         <lordrasmus at gmail dot com>
                                  <rcb-isis at users dot sourceforge dot net>
    Richard Braun                 <rbraun at sceen dot net>
    Richard Stearn                <richard at rns-stearn dot demon dot co dot uk>
    Rick Jones                    <raj at cup dot hp dot com>
    Robert Edmonds                <edmonds at debian dot org>
    Roberto Mariani               <jelot-tcpdump at jelot dot it>
    Roland Dreier                 <roland at purestorage dot com>
    Romain Francoise              <rfrancoise at debian dot org>
    Rongxi Li                     <rongxi dot li at chaitin dot com>
    Rose                          <83477269+AtariDreams at users dot noreply dot github dot com>
    Ryan Castellucci              <github-1b66+210325 at ryanc dot org>
    Sagun Shakya                  <sagun dot shakya at sun dot com>
    Samuel Thibault               <samuel dot thibault at ens-lyon dot org>
    Scott Barron                  <sb125499 at ohiou dot edu>
    Scott Gifford                 <sgifford at tir dot com>
    Scott Mcmillan                <scott dot a dot mcmillan at intel dot com>
    Sebastian Krahmer             <krahmer at cs dot uni-potsdam dot de>
    Sebastien Roy                 <Sebastien dot Roy at Sun dot COM>
    Sepherosa Ziehau              <sepherosa at gmail dot com>
    Shane Kerr                    <shane at time-travellers dot org>
    Shaun Clowes                  <delius at progsoc dot uts dot edu dot au>
    solofox                       <wensg100 at sina dot com>
    Solomon Peachy                <pizza at shaftnet dot org>
    Stefan Hudson                 <hudson at mbay dot net>
    Stephen Donnelly              <stephen at endace dot com>
    Steve Karg                    <skarg at users dot sourceforge dot net>
    Stig Bjørlykke                <stig at bjorlykke dot org>
    stubbfel                      <stubbfel at gmail dot com>
    Takashi Yamamoto              <yamt at mwd dot biglobe dot ne dot jp>
    Tanaka Shin-ya                <zstanaka at archer dot livedoor dot com>
    Thomas Habets                 <habets at google dot com>
    Thomas Petazzoni              <thomas dot petazzoni at free-electrons dot com>
    Tobias Poschwatta             <posch at sourceforge dot net>
    Tomasz Moń                    <desowin at gmail dot com>
    Tommy Beadle                  <tbeadle at arbor dot net>
    Tony Li                       <tli at procket dot com>
    Torsten Landschoff            <torsten at debian dot org>
    Tymoteusz Blazejczyk          <tymoteusz dot blazejczyk at intel dot com>
    Uns Lider                     <unslider at miranda dot org>
    Uwe Girlich                   <Uwe dot Girlich at philosys dot de>
    Vitaly Lavrov                 <vel21ripn at gmail dot com>
    Vivien Didelot                <vivien dot didelot at gmail dot com>
    Vladimir Gladkov              <vovkos at gmail dot com>
    Vladimir Marek                <vlmarek at volny dot cz>
    Walter Schell                 <walterschell at users dot noreply dot github dot com>
    Wesley Shields                <wxs at FreeBSD dot org>
    Xianjie Zhang                 <xzhang at cup dot hp dot com>
    Xin Li                        <delphij at FreeBSD dot org>
    Xue Jiang Qing                <xuejianqing at star-net dot cn>
    Yang Luo                      <hsluoyz at qq dot com>
    Yen Yen Lim
    Yoann Vandoorselaere          <yoann at prelude-ids dot org>
    Yogesh Prasad                 <yogesh dot prasad at rockwellcollins dot com>
    Yvan Vanhullebus              <vanhu at sourceforge dot net>

The original LBL crew:
    Steve McCanne
    Craig Leres
    Van Jacobson

Past maintainers (in alphabetical order):
    Bill Fenner                   <fenner at research dot att dot com>
    Fulvio Risso                  <risso at polito dot it>
    Hannes Gredler                <hannes at gredler dot at>
    Jun-ichiro itojun Hagino      <itojun at iijlab dot net>		Also see: http://www.wide.ad.jp/itojun-award/
