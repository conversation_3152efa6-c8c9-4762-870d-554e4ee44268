<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="en"><head><title>PCAP New Generation Dump File Format</title>
<meta name="description" content="PCAP New Generation Dump File Format">
<meta name="keywords" content="Internet-Draft, Libpcap, dump file format">
<meta name="generator" content="xml2rfc v1.22 (http://xml.resource.org/)">
<style type='text/css'>
<!--
    body {
        font-family: verdana, charcoal, helvetica, arial, sans-serif;
        font-size: small ; color: #000000 ; background-color: #ffffff ; }
    .title { color: #990000; font-size: x-large ;
        font-weight: bold; text-align: right;
        font-family: helvetica, monaco, "MS Sans Serif", arial, sans-serif;
        background-color: transparent; }
    .filename { color: #666666; font-size: 18px; line-height: 28px;
        font-weight: bold; text-align: right;
        font-family: helvetica, arial, sans-serif;
        background-color: transparent; }
    td.rfcbug { background-color: #000000 ; width: 30px ; height: 30px ; 
        text-align: justify; vertical-align: middle ; padding-top: 2px ; }
    td.rfcbug span.RFC { color: #666666; font-weight: bold; text-decoration: none;
        background-color: #000000 ;
        font-family: monaco, charcoal, geneva, "MS Sans Serif", helvetica, verdana, sans-serif;
        font-size: x-small ; }
    td.rfcbug span.hotText { color: #ffffff; font-weight: normal; text-decoration: none;
        text-align: center ;
        font-family: charcoal, monaco, geneva, "MS Sans Serif", helvetica, verdana, sans-serif;
        font-size: x-small ; background-color: #000000; }

     A { font-weight: bold; }
     A:link { color: #990000; background-color: transparent ; }
     A:visited { color: #333333; background-color: transparent ; }
     A:active { color: #333333; background-color: transparent ; }

    p { margin-left: 2em; margin-right: 2em; }
    p.copyright { font-size: x-small ; }
    p.toc { font-size: small ; font-weight: bold ; margin-left: 3em ;}

    span.emph { font-style: italic; }
    span.strong { font-weight: bold; }
    span.verb { font-family: "Courier New", Courier, monospace ; }

    ol.text { margin-left: 2em; margin-right: 2em; }
    ul.text { margin-left: 2em; margin-right: 2em; }
    li { margin-left: 3em;  }

    pre { margin-left: 3em; color: #333333;  background-color: transparent;
        font-family: "Courier New", Courier, monospace ; font-size: small ;
        }

    h3 { color: #333333; font-size: medium ;
        font-family: helvetica, arial, sans-serif ;
        background-color: transparent; }
    h4 { font-size: small; font-family: helvetica, arial, sans-serif ; }

    table.bug { width: 30px ; height: 15px ; }
    td.bug { color: #ffffff ; background-color: #990000 ;
        text-align: center ; width: 30px ; height: 15px ;
         }
    td.bug A.link2 { color: #ffffff ; font-weight: bold;
        text-decoration: none;
        font-family: monaco, charcoal, geneva, "MS Sans Serif", helvetica, sans-serif;
        font-size: x-small ; background-color: transparent }

    td.header { color: #ffffff; font-size: x-small ;
        font-family: arial, helvetica, sans-serif; vertical-align: top;
        background-color: #666666 ; width: 33% ; }
    td.author { font-weight: bold; margin-left: 4em; font-size: x-small ; }
    td.author-text { font-size: x-small; }
    table.data { vertical-align: top ; border-collapse: collapse ;
        border-style: solid solid solid solid ;
        border-color: black black black black ;
        font-size: small ; text-align: center ; }
    table.data th { font-weight: bold ;
        border-style: solid solid solid solid ;
        border-color: black black black black ; }
    table.data td {
        border-style: solid solid solid solid ;
        border-color: #333333 #333333 #333333 #333333 ; }

    hr { height: 1px }
-->
</style>
</head>
<body>
<table summary="layout" cellpadding="0" cellspacing="2" class="bug" align="right"><tr><td class="bug"><a href="#toc" class="link2">&nbsp;TOC&nbsp;</a></td></tr></table>
<table summary="layout" width="66%" border="0" cellpadding="0" cellspacing="0"><tr><td><table summary="layout" width="100%" border="0" cellpadding="2" cellspacing="1">
<tr><td class="header">Network Working Group</td><td class="header">L. Degioanni</td></tr>
<tr><td class="header">Internet-Draft</td><td class="header">F. Risso</td></tr>
<tr><td class="header">Expires: August 30, 2004</td><td class="header">Politecnico di Torino</td></tr>
<tr><td class="header">&nbsp;</td><td class="header">March 2004</td></tr>
</table></td></tr></table>
<div align="right"><span class="title"><br />PCAP New Generation Dump File Format</span></div>
<div align="right"><span class="title"><br />pcap</span></div>

<h3>Status of this Memo</h3>
<p>
This document is an Internet-Draft and is
in full conformance with all provisions of Section 10 of RFC2026.</p>
<p>
Internet-Drafts are working documents of the Internet Engineering
Task Force (IETF), its areas, and its working groups.
Note that other groups may also distribute working documents as
Internet-Drafts.</p>
<p>
Internet-Drafts are draft documents valid for a maximum of six months
and may be updated, replaced, or obsoleted by other documents at any time.
It is inappropriate to use Internet-Drafts as reference material or to cite
them other than as "work in progress."</p>
<p>
The list of current Internet-Drafts can be accessed at
<a href='http://www.ietf.org/ietf/1id-abstracts.txt'>http://www.ietf.org/ietf/1id-abstracts.txt</a>.</p>
<p>
The list of Internet-Draft Shadow Directories can be accessed at
<a href='http://www.ietf.org/shadow.html'>http://www.ietf.org/shadow.html</a>.</p>
<p>
This Internet-Draft will expire on August 30, 2004.</p>

<h3>Copyright Notice</h3>
<p>
Copyright (C) The Internet Society (2004). All Rights Reserved.</p>

<h3>Abstract</h3>

<p>This document describes a format to dump captured packets on a file. This format is extensible and it is currently proposed for implementation in the libpcap/WinPcap packet capture library.
</p><a name="toc"></a><br /><hr />
<h3>Table of Contents</h3>
<p class="toc">
<a href="#anchor1">1.</a>&nbsp;
Objectives<br />
<a href="#anchor2">2.</a>&nbsp;
General File Structure<br />
<a href="#sectionblock">2.1</a>&nbsp;
General Block Structure<br />
<a href="#anchor3">2.2</a>&nbsp;
Block Types<br />
<a href="#anchor4">2.3</a>&nbsp;
Block Hierarchy and Precedence<br />
<a href="#anchor5">2.4</a>&nbsp;
Data format<br />
<a href="#anchor6">3.</a>&nbsp;
Block Definition<br />
<a href="#sectionshb">3.1</a>&nbsp;
Section Header Block (mandatory)<br />
<a href="#sectionidb">3.2</a>&nbsp;
Interface Description Block (mandatory)<br />
<a href="#sectionpb">3.3</a>&nbsp;
Packet Block (optional)<br />
<a href="#anchor7">3.4</a>&nbsp;
Simple Packet Block (optional)<br />
<a href="#anchor8">3.5</a>&nbsp;
Name Resolution Block (optional)<br />
<a href="#anchor9">3.6</a>&nbsp;
Interface Statistics Block (optional)<br />
<a href="#sectionopt">4.</a>&nbsp;
Options<br />
<a href="#anchor10">5.</a>&nbsp;
Experimental Blocks (deserved to a further investigation)<br />
<a href="#anchor11">5.1</a>&nbsp;
Other Packet Blocks (experimental)<br />
<a href="#anchor12">5.2</a>&nbsp;
Compression Block (experimental)<br />
<a href="#anchor13">5.3</a>&nbsp;
Encryption Block (experimental)<br />
<a href="#anchor14">5.4</a>&nbsp;
Fixed Length Block (experimental)<br />
<a href="#anchor15">5.5</a>&nbsp;
Directory Block (experimental)<br />
<a href="#anchor16">5.6</a>&nbsp;
Traffic Statistics and Monitoring Blocks (experimental)<br />
<a href="#anchor17">5.7</a>&nbsp;
Event/Security Block (experimental)<br />
<a href="#anchor18">6.</a>&nbsp;
Conclusions<br />
<a href="#anchor19">7.</a>&nbsp;
Most important open issues<br />
<a href="#rfc.copyright">&#167;</a>&nbsp;
Intellectual Property and Copyright Statements<br />
</p>
<br clear="all" />

<a name="anchor1"></a><br /><hr />
<table summary="layout" cellpadding="0" cellspacing="2" class="bug" align="right"><tr><td class="bug"><a href="#toc" class="link2">&nbsp;TOC&nbsp;</a></td></tr></table>
<a name="rfc.section.1"></a><h3>1.&nbsp;Objectives</h3>

<p>The problem of exchanging packet traces becomes more and more critical every day; unfortunately, no standard solutions exist for this task right now. One of the most accepted packet interchange formats is the one defined by libpcap, which is rather old and does not fit for some of the nowadays applications especially in terms of extensibility.
</p>
<p>This document proposes a new format for dumping packet traces. The following goals are being pursued:
</p>
<ul class="text">
<li>Extensibility: aside of some common functionalities, third parties should be able to enrich the information embedded in the file with proprietary extensions, which will be ignored by tools that are not able to understand them.
</li>
<li>Portability: a capture trace must contain all the information needed to read data independently from network, hardware and operating system of the machine that made the capture.
</li>
<li>Merge/Append data: it should be possible to add data at the end of a given file, and the resulting file must still be readable.
</li>
</ul>
<a name="anchor2"></a><br /><hr />
<table summary="layout" cellpadding="0" cellspacing="2" class="bug" align="right"><tr><td class="bug"><a href="#toc" class="link2">&nbsp;TOC&nbsp;</a></td></tr></table>
<a name="rfc.section.2"></a><h3>2.&nbsp;General File Structure</h3>

<a name="rfc.section.2.1"></a><h4><a name="sectionblock">2.1</a>&nbsp;General Block Structure</h4>

<p>A capture file is organized in blocks, that are appended one to another to form the file. All the blocks share a common format, which is shown in <a href="#formatblock">Figure 1</a>.
</p><br /><hr />
<a name="formatblock"></a>
<pre>
    0                   1                   2                   3   
    0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                          Block Type                           |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                      Block Total Length                       |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   /                          Block Body                           /
   /          /* variable length, aligned to 32 bits */            /
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                      Block Total Length                       |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
</pre>
<table border="0" cellpadding="0" cellspacing="2" align="center"><tr><td align="center"><font face="monaco, MS Sans Serif" size="1"><b>&nbsp;Basic block structure.&nbsp;</b></font><br /></td></tr></table><hr size="1" shade="0">

<p>The fields have the following meaning:
</p>
<ul class="text">
<li>Block Type (32 bits): unique value that identifies the block. Values whose Most Significant Bit (MSB) is equal to 1 are reserved for local use. They allow to save private data to the file and to extend the file format.
</li>
<li>Block Total Length: total size of this block, in bytes. For instance, a block that does not have a body has a length of 12 bytes.
</li>
<li>Block Body: content of the block.
</li>
<li>Block Total Length: total size of this block, in bytes. This field is duplicated for permitting backward file navigation.
</li>
</ul>
<p>This structure, shared among all blocks, makes easy to process a file and to skip unneeded or unknown blocks. Blocks can be nested one inside the others (NOTE: needed?). Some of the blocks are mandatory, i.e. a dump file is not valid if they are not present, other are optional.
</p>
<p>The structure of the blocks allows to define other blocks if needed. A parser that does non understand them can simply ignore their content.
</p>
<a name="rfc.section.2.2"></a><h4><a name="anchor3">2.2</a>&nbsp;Block Types</h4>

<p>The currently defined blocks are the following:
</p>
<ol class="text">
<li>Section Header Block: it defines the most important characteristics of the capture file.
</li>
<li>Interface Description Block: it defines the most important characteristics of the interface(s) used for capturing traffic.
</li>
<li>Packet Block: it contains a single captured packet, or a portion of it.
</li>
<li>Simple Packet Block: it contains a single captured packet, or a portion of it, with only a minimal set of information about it.
</li>
<li>Name Resolution Block: it defines the mapping from numeric addresses present in the packet dump and the canonical name counterpart.
</li>
<li>Capture Statistics Block: it defines how to store some statistical data (e.g. packet dropped, etc) which can be useful to undestand the conditions in which the capture has been made.
</li>
<li>Compression Marker Block: TODO
</li>
<li>Encryption Marker Block: TODO
</li>
<li>Fixed Length Marker Block: TODO
</li>
</ol>
<p>The following blocks instead are considered interesting but the authors believe that they deserve more in-depth discussion before being defined:
</p>
<ol class="text">
<li>Further Packet Blocks
</li>
<li>Directory Block
</li>
<li>Traffic Statistics and Monitoring Blocks
</li>
<li>Alert and Security Blocks
</li>
</ol>
<p>TODO Currently standardized Block Type codes are specified in Appendix 1.
</p>
<a name="rfc.section.2.3"></a><h4><a name="anchor4">2.3</a>&nbsp;Block Hierarchy and Precedence</h4>

<p>The file must begin with a Section Header Block. However, more than one Section Header Block can be present on the dump, each one covering the data following it till the next one (or the end of file). A Section includes the data delimited by two Section Header Blocks (or by a Section Header Block and the end of the file), including the first Section Header Block.
</p>
<p>In case an application cannot read a Section because of different version number, it must skip everything until the next Section Header Block. Note that, in order to properly skip the blocks until the next section, all blocks must have the fields Type and Length at the beginning. This is a mandatory requirement that must be maintained in future versions of the block format.
</p>
<p><a href="#fssample-SHB">Figure 2</a> shows two valid files: the first has a typical configuration, with a single Section Header that covers the whole file. The second one contains three headers, and is normally the result of file concatenation. An application that understands only version 1.0 of the file format skips the intermediate section and restart processing the packets after the third Section Header.
</p><br /><hr />
<a name="fssample-SHB"></a>
<pre>
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   | SHB v1.0  |                      Data                         |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   Typical configuration with a single Section Header Block 


   |--   1st Section   --|--   2nd Section   --|--  3rd Section  --|
   |                                                               |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   | SHB v1.0  |  Data   | SHB V1.1  |  Data   | SHB V1.0  |  Data |  
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   Configuration with three different Section Header Blocks
</pre>
<table border="0" cellpadding="0" cellspacing="2" align="center"><tr><td align="center"><font face="monaco, MS Sans Serif" size="1"><b>&nbsp;File structure example: the Section Header Block.&nbsp;</b></font><br /></td></tr></table><hr size="1" shade="0">

<p>NOTE: TO BE COMPLETED with some examples of other blocks
</p>
<a name="rfc.section.2.4"></a><h4><a name="anchor5">2.4</a>&nbsp;Data format</h4>

<p>Data contained in each section will always be saved according to the characteristics (little endian / big endian) of the dumping machine. This refers to all fields that are saved as numbers and that span over two or more bytes.
</p>
<p>The approach of having each section saved in the native format of the generating host is more efficient because it avoids translation of data when reading / writing on the host itself, which is the most common case when generating/processing capture dumps.
</p>
<p>TODO Probably we have to specify something more here. Is what we're saying enough to avoid any kind of ambiguity?.
</p>
<a name="anchor6"></a><br /><hr />
<table summary="layout" cellpadding="0" cellspacing="2" class="bug" align="right"><tr><td class="bug"><a href="#toc" class="link2">&nbsp;TOC&nbsp;</a></td></tr></table>
<a name="rfc.section.3"></a><h3>3.&nbsp;Block Definition</h3>

<p>This section details the format of the body of the blocks currently defined.
</p>
<a name="rfc.section.3.1"></a><h4><a name="sectionshb">3.1</a>&nbsp;Section Header Block (mandatory)</h4>

<p>The Section Header Block is mandatory. It identifies the beginning of a section of the capture dump file. Its format is shown in <a href="#formatSHB">Figure 3</a>.
</p><br /><hr />
<a name="formatSHB"></a>
<pre>
    0                   1                   2                   3   
    0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                            Magic                              |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |              Major            |             Minor             |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   /                                                               / 
   /                      Options (variable)                       / 
   /                                                               / 
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
</pre>
<table border="0" cellpadding="0" cellspacing="2" align="center"><tr><td align="center"><font face="monaco, MS Sans Serif" size="1"><b>&nbsp;Section Header Block format.&nbsp;</b></font><br /></td></tr></table><hr size="1" shade="0">

<p>The meaning of the fields is:
</p>
<ul class="text">
<li>Magic: magic number, whose value is the hexadecimal number 0x1A2B3C4D. This number can be used to distinguish section that have been saved on little-endian machines from the one saved on big-endian machines.
</li>
<li>Major: number of the current mayor version of the format. Current value is 1.
</li>
<li>Minor: number of the current minor version of the format. Current value is 0.
</li>
<li>Options: optionally, a list of options (formatted according to the rules defined in <a href="#sectionopt">Section 4</a>) can be present.
</li>
</ul>
<p>Aside form the options defined in <a href="#sectionopt">Section 4</a>, the following options are valid within this block:
</p><a name="InterfaceOptions1"></a>
<table class="data" align="center" border="1" cellpadding="2" cellspacing="2">
<tr>
<th align="left" width="25%">Name</th>
<th align="left" width="25%">Code</th>
<th align="left" width="25%">Length</th>
<th align="left" width="25%">Description</th>
</tr>
<tr>
<td align="left">Hardware</td>
<td align="left">2</td>
<td align="left">variable</td>
<td align="left">An ascii string containing the description of the hardware used to create this section.</td>
</tr>
<tr>
<td align="left">Operating System</td>
<td align="left">3</td>
<td align="left">variable</td>
<td align="left">An ascii string containing the name of the operating system used to create this section.</td>
</tr>
<tr>
<td align="left">User Application</td>
<td align="left">3</td>
<td align="left">variable</td>
<td align="left">An ascii string containing the name of the application used to create this section.</td>
</tr>
</table>

<p>The Section Header Block does not contain data but it rather identifies a list of blocks (interfaces, packets) that are logically correlated. This block does not contain any reference to the size of the section it is currently delimiting, therefore the reader cannot skip a whole section at once. In case a section must be skipped, the user has to repeatedly skip all the blocks contained within it; this makes the parsing of the file slower but it permits to append several capture dumps at the same file.
</p>
<a name="rfc.section.3.2"></a><h4><a name="sectionidb">3.2</a>&nbsp;Interface Description Block (mandatory)</h4>

<p>The Interface Description Block is mandatory. This block is needed to specify the characteristics of the network interface on which the capture has been made. In order to properly associate the captured data to the corresponding interface, the Interface Description Block must be defined before any other block that uses it; therefore, this block is usually placed immediately after the Section Header Block.
</p>
<p>An Interface Description Block is valid only inside the section which it belongs to. The structure of a Interface Description Block is shown in <a href="#formatidb">Figure 4</a>.
</p><br /><hr />
<a name="formatidb"></a>
<pre>
    0                   1                   2                   3   
    0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |          Interface ID         |           LinkType            |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                            SnapLen                            |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   /                                                               / 
   /                      Options (variable)                       / 
   /                                                               / 
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 </pre>
<table border="0" cellpadding="0" cellspacing="2" align="center"><tr><td align="center"><font face="monaco, MS Sans Serif" size="1"><b>&nbsp;Interface Description Block format.&nbsp;</b></font><br /></td></tr></table><hr size="1" shade="0">

<p>The meaning of the fields is:
</p>
<ul class="text">
<li>Interface ID: a progressive number that identifies uniquely any interface inside current section. Two Interface Description Blocks can have the same Interface ID only if they are in different sections of the file. The Interface ID is referenced by the packet blocks.
</li>
<li>LinkType: a value that defines the link layer type of this interface.
</li>
<li>SnapLen: maximum number of bytes dumped from each packet. The portion of each packet that exceeds this value will not be stored in the file.
</li>
<li>Options: optionally, a list of options (formatted according to the rules defined in <a href="#sectionopt">Section 4</a>) can be present.
</li>
</ul>
<p>In addition to the options defined in <a href="#sectionopt">Section 4</a>, the following options are valid within this block:
</p><a name="InterfaceOptions2"></a>
<table class="data" align="center" border="1" cellpadding="2" cellspacing="2">
<tr>
<th align="left" width="25%">Name</th>
<th align="left" width="25%">Code</th>
<th align="left" width="25%">Length</th>
<th align="left" width="25%">Description</th>
</tr>
<tr>
<td align="left">if_name</td>
<td align="left">2</td>
<td align="left">Variable</td>
<td align="left">Name of the device used to capture data.</td>
</tr>
<tr>
<td align="left">if_IPv4addr</td>
<td align="left">3</td>
<td align="left">8</td>
<td align="left">Interface network address and netmask.</td>
</tr>
<tr>
<td align="left">if_IPv6addr</td>
<td align="left">4</td>
<td align="left">17</td>
<td align="left">Interface network address and prefix length (stored in the last byte).</td>
</tr>
<tr>
<td align="left">if_MACaddr</td>
<td align="left">5</td>
<td align="left">6</td>
<td align="left">Interface Hardware MAC address (48 bits).</td>
</tr>
<tr>
<td align="left">if_EUIaddr</td>
<td align="left">6</td>
<td align="left">8</td>
<td align="left">Interface Hardware EUI address (64 bits), if available.</td>
</tr>
<tr>
<td align="left">if_speed</td>
<td align="left">7</td>
<td align="left">8</td>
<td align="left">Interface speed (in bps).</td>
</tr>
<tr>
<td align="left">if_tsaccur</td>
<td align="left">8</td>
<td align="left">1</td>
<td align="left">Precision of timestamps. If the Most Significant Bit is equal to zero, the remaining bits indicates the accuracy as as a negative power of 10 (e.g. 6 means microsecond accuracy). If the Most Significant Bit is equal to zero, the remaining bits indicates the accuracy as as negative power of 2 (e.g. 10 means 1/1024 of second). If this option is not present, a precision of 10^-6 is assumed.</td>
</tr>
<tr>
<td align="left">if_tzone</td>
<td align="left">9</td>
<td align="left">4</td>
<td align="left">Time zone for GMT support (TODO: specify better).</td>
</tr>
<tr>
<td align="left">if_flags</td>
<td align="left">10</td>
<td align="left">4</td>
<td align="left">Interface flags. (TODO: specify better. Possible flags: promiscuous, inbound/outbound, traffic filtered during capture).</td>
</tr>
<tr>
<td align="left">if_filter</td>
<td align="left">11</td>
<td align="left">variable</td>
<td align="left">The filter (e.g. "capture only TCP traffic") used to capture traffic. The first byte of the Option Data keeps a code of the filter used (e.g. if this is a libpcap string, or BPF bytecode, and more). More details about this format will be presented in Appendix XXX (TODO).</td>
</tr>
<tr>
<td align="left">if_opersystem</td>
<td align="left">12</td>
<td align="left">variable</td>
<td align="left">An ascii string containing the name of the operating system of the machine that hosts this interface. This can be different from the same information that can be contained by the Section Header Block (<a href="#sectionshb">Section 3.1</a>) because the capture can have been done on a remote machine.</td>
</tr>
</table>

<a name="rfc.section.3.3"></a><h4><a name="sectionpb">3.3</a>&nbsp;Packet Block (optional)</h4>

<p>A Packet Block is the standard container for storing the packets coming from the network. The Packet Block is optional because packets can be stored either by means of this block or the Simple Packet Block, which can be used to speed up dump generation. The format of a packet block is shown in <a href="#formatpb">Figure 5</a>.
</p><br /><hr />
<a name="formatpb"></a>
<pre>
    0                   1                   2                   3   
    0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |         Interface ID          |          Drops Count          |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                        Timestamp (High)                       |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                        Timestamp (Low)                        |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                         Captured Len                          |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                          Packet Len                           |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                                                               |
   |                          Packet Data                          |
   |                                                               |
   |              /* variable length, byte-aligned */              |
   |                                                               |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   /                                                               / 
   /                      Options (variable)                       / 
   /                                                               / 
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
</pre>
<table border="0" cellpadding="0" cellspacing="2" align="center"><tr><td align="center"><font face="monaco, MS Sans Serif" size="1"><b>&nbsp;Packet Block format.&nbsp;</b></font><br /></td></tr></table><hr size="1" shade="0">

<p>The Packet Block has the following fields:
</p>
<ul class="text">
<li>Interface ID: Specifies the interface this packet comes from, and corresponds to the ID of one of the Interface Description Blocks present in this section of the file (see <a href="#formatidb">Figure 4</a>).
</li>
<li>Drops Count: a local drop counter. It specified the number of packets lost (by the interface and the operating system) between this packet and the preceding one. The value xFFFF (in hexadecimal) is reserved for those systems in which this information is not available.
</li>
<li>Timestamp (High): the most significative part of the timestamp. in standard Unix format, i.e. from 1/1/1970.
</li>
<li>Timestamp (Low): the less significative part of the timestamp. The way to interpret this field is specified by the 'ts_accur' option (see <a href="#formatidb">Figure 4</a>) of the Interface Description block referenced by this packet. If the Interface Description block does not contain a 'ts_accur' option, then this field is expressed in microseconds.
</li>
<li>Captured Len: number of bytes captured from the packet (i.e. the length of the Packet Data field). It will be the minimum value among the actual Packet Length and the snapshot length (defined in <a href="#formatidb">Figure 4</a>).
</li>
<li>Packet Len: actual length of the packet when it was transmitted on the network. Can be different from Captured Len if the user wants only a snapshot of the packet.
</li>
<li>Packet Data: the data coming from the network, including link-layer headers. The length of this field is Captured Len. The format of the link-layer headers depends on the LinkType field specified in the Interface Description Block (see <a href="#sectionidb">Section 3.2</a>) and it is specified in Appendix XXX (TODO).
</li>
<li>Options: optionally, a list of options (formatted according to the rules defined in <a href="#sectionopt">Section 4</a>) can be present.
</li>
</ul>
<p>
</p>
<a name="rfc.section.3.4"></a><h4><a name="anchor7">3.4</a>&nbsp;Simple Packet Block (optional)</h4>

<p>The Simple Packet Block is a lightweight container for storing the packets coming from the network. Its presence is optional.
</p>
<p>A Simple Packet Block is similar to a Packet Block (see <a href="#sectionpb">Section 3.3</a>), but it is smaller, simpler to process and contains only a minimal set of information. This block is preferred to the standard Packet Block when performance or space occupation are critical factors, such as in sustained traffic dump applications. A capture file can contain both Packet Blocks and Simple Packet Blocks: for example, a capture tool could switch from Packet Blocks to Simple Packet Blocks when the hardware resources become critical.
</p>
<p>The Simple Packet Block does not contain the Interface ID field. Therefore, it must be assumed that all the Simple Packet Blocks have been captured on the interface previously specified in the Interface Description Block.
</p>
<p><a href="#formatpbs">Figure 6</a> shows the format of the Simple Packet Block.
</p><br /><hr />
<a name="formatpbs"></a>
<pre>
    0                   1                   2                   3   
    0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                          Packet Len                           |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                                                               |
   |                          Packet Data                          |
   |                                                               |
   |              /* variable length, byte-aligned */              |
   |                                                               |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 </pre>
<table border="0" cellpadding="0" cellspacing="2" align="center"><tr><td align="center"><font face="monaco, MS Sans Serif" size="1"><b>&nbsp;Simple Packet Block format.&nbsp;</b></font><br /></td></tr></table><hr size="1" shade="0">

<p>The Packet Block has the following fields:
</p>
<ul class="text">
<li>Packet Len: actual length of the packet when it was transmitted on the network. Can be different from captured len if the packet has been truncated.
</li>
<li>Packet data: the data coming from the network, including link-layers headers. The length of this field can be derived from the field Block Total Length, present in the Block Header.
</li>
</ul>
<p>The Simple Packet Block does not contain the timestamp because this is one of the most costly operations on PCs. Additionally, there are applications that do not require it; e.g. an Intrusion Detection System is interested in packets, not in their timestamp.
</p>
<p>The Simple Packet Block is very efficient in term of disk space: a snapshot of length 100 bytes requires only 16 bytes of overhead, which corresponds to an efficiency of more than 86%.
</p>
<a name="rfc.section.3.5"></a><h4><a name="anchor8">3.5</a>&nbsp;Name Resolution Block (optional)</h4>

<p>The Name Resolution Block is used to support the correlation of numeric addresses (present in the captured packets) and their corresponding canonical names and it is optional. Having the literal names saved in the file, this prevents the need of a name resolution in a delayed time, when the association between names and addresses can be different from the one in use at capture time. Moreover, The Name Resolution Block avoids the need of issuing a lot of DNS requests every time the trace capture is opened, and allows to have name resolution also when reading the capture with a machine not connected to the network.
</p>
<p>The format of the Name Resolution Block is shown in <a href="#formatnrb">Figure 7</a>.
</p><br /><hr />
<a name="formatnrb"></a>
<pre>
    0                   1                   2                   3   
    0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |      Record Type              |         Record Length         | 
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                       Record Value                            |
   |              /* variable length, byte-aligned */              |
   |               + + + + + + + + + + + + + + + + + + + + + + + + +
   |               |               |               |               |
   +-+-+-+-+-+-+-+-+ + + + + + + + + + + + + + + + + + + + + + + + +
             . . . other records . . .
   |  Record Type == end_of_recs   |  Record Length == 00          |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   /                                                               / 
   /                      Options (variable)                       / 
   /                                                               / 
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 </pre>
<table border="0" cellpadding="0" cellspacing="2" align="center"><tr><td align="center"><font face="monaco, MS Sans Serif" size="1"><b>&nbsp;Name Resolution Block format.&nbsp;</b></font><br /></td></tr></table><hr size="1" shade="0">

<p>A Name Resolution Block is a zero-terminated list of records (in the TLV format), each of which contains an association between a network address and a name. There are three possible types of records:
</p><a name="nrrecords"></a>
<table class="data" align="center" border="1" cellpadding="2" cellspacing="2">
<tr>
<th align="left" width="25%">Name</th>
<th align="left" width="25%">Code</th>
<th align="left" width="25%">Length</th>
<th align="left" width="25%">Description</th>
</tr>
<tr>
<td align="left">end_of_recs</td>
<td align="left">0</td>
<td align="left">0</td>
<td align="left">End of records</td>
</tr>
<tr>
<td align="left">ip4_rec</td>
<td align="left">1</td>
<td align="left">Variable</td>
<td align="left">Specifies an IPv4 address (contained in the first 4 bytes), followed by one or more zero-terminated strings containing the DNS entries for that address.</td>
</tr>
<tr>
<td align="left">ip6_rec</td>
<td align="left">1</td>
<td align="left">Variable</td>
<td align="left">Specifies an IPv6 address (contained in the first 16 bytes), followed by one or more zero-terminated strings containing the DNS entries for that address.</td>
</tr>
</table>

<p>After the list or Name Resolution Records, optionally, a list of options (formatted according to the rules defined in <a href="#sectionopt">Section 4</a>) can be present.
</p>
<p>A Name Resolution Block is normally placed at the beginning of the file, but no assumptions can be taken about its position. Name Resolution Blocks can be added in a second time by tools that process the file, like network analyzers.
</p>
<p>In addiction to the options defined in <a href="#sectionopt">Section 4</a>, the following options are valid within this block:
</p><table class="data" align="center" border="1" cellpadding="2" cellspacing="2">
<tr>
<th align="left" width="25%">Name</th>
<th align="left" width="25%">Code</th>
<th align="left" width="25%">Length</th>
<th align="left" width="25%">Description</th>
</tr>
<tr>
<td align="left">ns_dnsname</td>
<td align="left">2</td>
<td align="left">Variable</td>
<td align="left">An ascii string containing the name of the machine (DNS server) used to perform the name resolution.</td>
</tr>
</table>

<a name="rfc.section.3.6"></a><h4><a name="anchor9">3.6</a>&nbsp;Interface Statistics Block (optional)</h4>

<p>The Interface Statistics Block contains the capture statistics for a given interface and it is optional. The statistics are referred to the interface defined in the current Section identified by the Interface ID field.
</p>
<p>The format of the Interface Statistics Block is shown in <a href="#formatisb">Figure 8</a>.
</p><br /><hr />
<a name="formatisb"></a>
<pre>
    0                   1                   2                   3   
    0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                         IfRecv                                |
   |                          (high + low)                         |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                         IfDrop                                |
   |                          (high + low)                         |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                         FilterAccept                          |
   |                          (high + low)                         |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                         OSDrop                                |
   |                          (high + low)                         |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                         UsrDelivered                          |
   |                          (high + low)                         |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |          Interface ID         |           Reserved            |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   /                                                               / 
   /                      Options (variable)                       / 
   /                                                               / 
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 </pre>
<table border="0" cellpadding="0" cellspacing="2" align="center"><tr><td align="center"><font face="monaco, MS Sans Serif" size="1"><b>&nbsp;Interface Statistics Block format.&nbsp;</b></font><br /></td></tr></table><hr size="1" shade="0">

<p>The fields have the following meaning:
</p>
<ul class="text">
<li>IfRecv: number of packets received from the interface during the capture. This number is reported as a 64 bits value, in which the most significat bits are located in the first four bytes of the field.
</li>
<li>IfDrop: number of packets dropped by the interface during the capture due to lack of resources.
</li>
<li>FilterAccept: number of packets accepeted by filter during current capture.
</li>
<li>OSDrop: number of packets dropped by the operating system during the capture.
</li>
<li>UsrDelivered: number of packets delivered to the user. UsrDelivered can be different from the value 'FilterAccept - OSDropped' because some packets could still lay in the OS buffers when the capture ended.
</li>
<li>Interface ID: reference to an Interface Description Block.
</li>
<li>Reserved: Reserved to future use.
</li>
<li>Options: optionally, a list of options (formatted according to the rules defined in <a href="#sectionopt">Section 4</a>) can be present.
</li>
</ul>
<p>In addiction to the options defined in <a href="#sectionopt">Section 4</a>, the following options are valid within this block:
</p><table class="data" align="center" border="1" cellpadding="2" cellspacing="2">
<tr>
<th align="left" width="25%">Name</th>
<th align="left" width="25%">Code</th>
<th align="left" width="25%">Length</th>
<th align="left" width="25%">Description</th>
</tr>
<tr>
<td align="left">isb_starttime</td>
<td align="left">2</td>
<td align="left">8</td>
<td align="left">Time in which the capture started; time will be stored in two blocks of four bytes each, containing the timestamp in seconds and nanoseconds.</td>
</tr>
<tr>
<td align="left">isb_endtime</td>
<td align="left">3</td>
<td align="left">8</td>
<td align="left">Time in which the capture started; time will be stored in two blocks of four bytes each, containing the timestamp in seconds and nanoseconds.</td>
</tr>
</table>

<a name="sectionopt"></a><br /><hr />
<table summary="layout" cellpadding="0" cellspacing="2" class="bug" align="right"><tr><td class="bug"><a href="#toc" class="link2">&nbsp;TOC&nbsp;</a></td></tr></table>
<a name="rfc.section.4"></a><h3>4.&nbsp;Options</h3>

<p>Almost all blocks have the possibility to embed optional fields. Optional fields can be used to insert some information that may be useful when reading data, but that it is not really needed for packet processing. Therefore, each tool can be either read the content of the optional fields (if any), or skip them at once.
</p>
<p>Skipping all the optional fields at once is straightforward because most of the blocks have a fixed length, therefore the field Block Length (present in the General Block Structure, see  <a href="#sectionblock">Section 2.1</a>) can be used to skip everything till the next block.
</p>
<p>Options are a list of Type - Length - Value fields, each one containing a single value:
</p>
<ul class="text">
<li>Option Type (2 bytes): it contains the code that specifies the type of the current TLV record. Option types whose Most Significant Bit is equal to one are reserved for local use; therefore, there is no guarantee that the code used is unique among all capture files (generated by other applications). In case of vendor-specific extensions that have to be identified uniquely, vendors must request an Option Code whose MSB is equal to zero.
</li>
<li>Option Length (2 bytes): it contains the length of the following 'Option Value' field.
</li>
<li>Option Value (variable length): it contains the value of the given option. The length of this field as been specified by the Option Length field.
</li>
</ul>
<p>Options may be repeated several times (e.g. an interface that has several IP addresses associated to it). The option list is terminated by a special code which is the 'End of Option'.
</p>
<p>The format of the optional fields is shown in <a href="#formatopt">Figure 9</a>.
</p><br /><hr />
<a name="formatopt"></a>
<pre>
    0                   1                   2                   3   
    0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |      Option Code              |         Option Length         | 
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                       Option Value                            |
   |              /* variable length, byte-aligned */              |
   |               + + + + + + + + + + + + + + + + + + + + + + + + +
   |               /               /               /               |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   /                                                               /
   /                 . . . other options . . .                     /
   /                                                               /
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |   Option Code == opt_endofopt  |  Option Length == 0          |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 </pre>
<table border="0" cellpadding="0" cellspacing="2" align="center"><tr><td align="center"><font face="monaco, MS Sans Serif" size="1"><b>&nbsp;Options format.&nbsp;</b></font><br /></td></tr></table><hr size="1" shade="0">

<p>The following codes can always be present in any optional field:
</p><table class="data" align="center" border="1" cellpadding="2" cellspacing="2">
<tr>
<th align="left" width="25%">Name</th>
<th align="left" width="25%">Code</th>
<th align="left" width="25%">Length</th>
<th align="left" width="25%">Description</th>
</tr>
<tr>
<td align="left">opt_endofopt</td>
<td align="left">0</td>
<td align="left">0</td>
<td align="left">End of options: it is used to delimit the end of the optional fields. This block cannot be repeated within a given list of options.</td>
</tr>
<tr>
<td align="left">opt_comment</td>
<td align="left">1</td>
<td align="left">variable</td>
<td align="left">Comment: it is an ascii string containing a comment that is associated to the current block.</td>
</tr>
</table>

<a name="anchor10"></a><br /><hr />
<table summary="layout" cellpadding="0" cellspacing="2" class="bug" align="right"><tr><td class="bug"><a href="#toc" class="link2">&nbsp;TOC&nbsp;</a></td></tr></table>
<a name="rfc.section.5"></a><h3>5.&nbsp;Experimental Blocks (deserved to a further investigation)</h3>

<a name="rfc.section.5.1"></a><h4><a name="anchor11">5.1</a>&nbsp;Other Packet Blocks (experimental)</h4>

<p>Can some other packet blocks (besides the two described in the previous paragraphs) be useful?
</p>
<a name="rfc.section.5.2"></a><h4><a name="anchor12">5.2</a>&nbsp;Compression Block (experimental)</h4>

<p>The Compression Block is optional. A file can contain an arbitrary number of these blocks. A Compression Block, as the name says, is used to store compressed data. Its format is shown in <a href="#formatcb">Figure 10</a>.
</p><br /><hr />
<a name="formatcb"></a>
<pre>
    0                   1                   2                   3   
    0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |  Compr. Type  |                                               |
   +-+-+-+-+-+-+-+-+                                               |
   |                                                               |
   |                       Compressed Data                         |
   |                                                               |
   |              /* variable length, byte-aligned */              |
   |                                                               |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 </pre>
<table border="0" cellpadding="0" cellspacing="2" align="center"><tr><td align="center"><font face="monaco, MS Sans Serif" size="1"><b>&nbsp;Compression Block format.&nbsp;</b></font><br /></td></tr></table><hr size="1" shade="0">

<p>The fields have the following meaning:
</p>
<ul class="text">
<li>Compression Type: specifies the compression algorithm. Possible values for this field are 0 (uncompressed), 1 (Lempel Ziv), 2 (Gzip), other?? Probably some kind of dumb and fast compression algorithm could be effective with some types of traffic (for example web), but which?
</li>
<li>Compressed Data: data of this block. Once decompressed, it is made of other blocks.
</li>
</ul>
<a name="rfc.section.5.3"></a><h4><a name="anchor13">5.3</a>&nbsp;Encryption Block (experimental)</h4>

<p>The Encryption Block is optional. A file can contain an arbitrary number of these blocks. An Encryption Block is used to sotre encrypted data. Its format is shown in <a href="#formateb">Figure 11</a>.
</p><br /><hr />
<a name="formateb"></a>
<pre>
    0                   1                   2                   3   
    0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |   Encr. Type  |                                               |
   +-+-+-+-+-+-+-+-+                                               |
   |                                                               |
   |                       Compressed Data                         |
   |                                                               |
   |              /* variable length, byte-aligned */              |
   |                                                               |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 </pre>
<table border="0" cellpadding="0" cellspacing="2" align="center"><tr><td align="center"><font face="monaco, MS Sans Serif" size="1"><b>&nbsp;Encryption Block format.&nbsp;</b></font><br /></td></tr></table><hr size="1" shade="0">

<p>The fields have the following meaning:
</p>
<ul class="text">
<li>Compression Type: specifies the encryption algorithm. Possible values for this field are ??? NOTE: this block should probably contain other fields, depending on the encryption algorithm. To be define precisely.
</li>
<li>Encrypted Data: data of this block. Once decripted, it consists of other blocks.
</li>
</ul>
<a name="rfc.section.5.4"></a><h4><a name="anchor14">5.4</a>&nbsp;Fixed Length Block (experimental)</h4>

<p>The Fixed Length Block is optional. A file can contain an arbitrary number of these blocks. A Fixed Length Block can be used to optimize the access to the file. Its format is shown in <a href="#formatflm">Figure 12</a>.
A Fixed Length Block stores records with constant size. It contains a set of Blocks (normally Packet Blocks or Simple Packet Blocks), of wihich it specifies the size. Knowing this size a priori helps to scan the file and to load some portions of it without truncating a block, and is particularly useful with cell-based networks like ATM.
</p><br /><hr />
<a name="formatflm"></a>
<pre>
    0                   1                   2                   3   
    0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |          Cell Size            |                               |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+                               |
   |                                                               |
   |                        Fixed Size Data                        |
   |                                                               |
   |              /* variable length, byte-aligned */              |
   |                                                               |
   |                                                               |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 </pre>
<table border="0" cellpadding="0" cellspacing="2" align="center"><tr><td align="center"><font face="monaco, MS Sans Serif" size="1"><b>&nbsp;Fixed Length Block format.&nbsp;</b></font><br /></td></tr></table><hr size="1" shade="0">

<p>The fields have the following meaning:
</p>
<ul class="text">
<li>Cell size: the size of the blocks contained in the data field.
</li>
<li>Fixed Size Data: data of this block.
</li>
</ul>
<a name="rfc.section.5.5"></a><h4><a name="anchor15">5.5</a>&nbsp;Directory Block (experimental)</h4>

<p>If present, this block contains the following information:
</p>
<ul class="text">
<li>number of indexed packets (N)
</li>
<li>table with position and length of any indexed packet (N entries)
</li>
</ul>
<p>A directory block must be followed by at least N packets, otherwise it must be considered invalid. It can be used to efficiently load portions of the file to memory and to support operations on memory mapped files. This block can be added by tools like network analyzers as a consequence of file processing.
</p>
<a name="rfc.section.5.6"></a><h4><a name="anchor16">5.6</a>&nbsp;Traffic Statistics and Monitoring Blocks (experimental)</h4>

<p>One or more blocks could be defined to contain network statistics or traffic monitoring information. They could be use to store data collected from RMON or Netflow probes, or from other network monitoring tools.
</p>
<a name="rfc.section.5.7"></a><h4><a name="anchor17">5.7</a>&nbsp;Event/Security Block (experimental)</h4>

<p>This block could be used to store events. Events could contain generic information (for example network load over 50%, server down...) or security alerts. An event could be:
</p>
<ul class="text">
<li>skipped, if the application doesn't know how to do with it
</li>
<li>processed independently by the packets. In other words, the applications skips the packets and processes only the alerts
</li>
<li>processed in relation to packets: for example, a security tool could load only the packets of the file that are near a security alert; a monitorg tool could skip the packets captured while the server was down.
</li>
</ul>
<a name="anchor18"></a><br /><hr />
<table summary="layout" cellpadding="0" cellspacing="2" class="bug" align="right"><tr><td class="bug"><a href="#toc" class="link2">&nbsp;TOC&nbsp;</a></td></tr></table>
<a name="rfc.section.6"></a><h3>6.&nbsp;Conclusions</h3>

<p>The file format proposed in this document should be very versatile and satisfy a wide range of applications.
In the simplest case, it can contain a raw dump of the network data, made of a series of Simple Packet Blocks.
In the most complex case, it can be used as a repository for heterogeneous information.
In every case, the file remains easy to parse and an application can always skip the data it is not interested in; at the same time, different applications can share the file, and each of them can benfit of the information produced by the others.
Two or more files can be concatenated obtaining another valid file.
</p>
<a name="anchor19"></a><br /><hr />
<table summary="layout" cellpadding="0" cellspacing="2" class="bug" align="right"><tr><td class="bug"><a href="#toc" class="link2">&nbsp;TOC&nbsp;</a></td></tr></table>
<a name="rfc.section.7"></a><h3>7.&nbsp;Most important open issues</h3>

<ul class="text">
<li>Data, in the file, must be byte or word aligned? Currently, the structure of this document is not consistent with respect to this point.
</li>
</ul><a name="rfc.copyright"></a><br /><hr />
<table summary="layout" cellpadding="0" cellspacing="2" class="bug" align="right"><tr><td class="bug"><a href="#toc" class="link2">&nbsp;TOC&nbsp;</a></td></tr></table>
<h3>Intellectual Property Statement</h3>
<p class='copyright'>
The IETF takes no position regarding the validity or scope of
any intellectual property or other rights that might be claimed
to  pertain to the implementation or use of the technology
described in this document or the extent to which any license
under such rights might or might not be available; neither does
it represent that it has made any effort to identify any such
rights. Information on the IETF's procedures with respect to
rights in standards-track and standards-related documentation
can be found in BCP-11. Copies of claims of rights made
available for publication and any assurances of licenses to
be made available, or the result of an attempt made
to obtain a general license or permission for the use of such
proprietary rights by implementors or users of this
specification can be obtained from the IETF Secretariat.</p>
<p class='copyright'>
The IETF invites any interested party to bring to its
attention any copyrights, patents or patent applications, or
other proprietary rights which may cover technology that may be
required to practice this standard. Please address the
information to the IETF Executive Director.</p>
<h3>Full Copyright Statement</h3>
<p class='copyright'>
Copyright (C) The Internet Society (2004). All Rights Reserved.</p>
<p class='copyright'>
This document and translations of it may be copied and furnished to
others, and derivative works that comment on or otherwise explain it
or assist in its implementation may be prepared, copied, published and
distributed, in whole or in part, without restriction of any kind,
provided that the above copyright notice and this paragraph are
included on all such copies and derivative works. However, this
document itself may not be modified in any way, such as by removing
the copyright notice or references to the Internet Society or other
Internet organizations, except as needed for the purpose of
developing Internet standards in which case the procedures for
copyrights defined in the Internet Standards process must be
followed, or as required to translate it into languages other than
English.</p>
<p class='copyright'>
The limited permissions granted above are perpetual and will not be
revoked by the Internet Society or its successors or assignees.</p>
<p class='copyright'>
This document and the information contained herein is provided on an
&quot;AS IS&quot; basis and THE INTERNET SOCIETY AND THE INTERNET ENGINEERING
TASK FORCE DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING
BUT NOT LIMITED TO ANY WARRANTY THAT THE USE OF THE INFORMATION
HEREIN WILL NOT INFRINGE ANY RIGHTS OR ANY IMPLIED WARRANTIES OF
MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.</p>
<h3>Acknowledgment</h3>
<p class='copyright'>
Funding for the RFC Editor function is currently provided by the
Internet Society.</p>
</body></html>
