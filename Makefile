#伪目标 .PHONY:clean
#声明目标为伪目标之后，makefile将不会判断目标是否存在或该目标是否需要更新
.PHONY:clean show rebuild


#模式匹配 %目标:%依赖
#目标和依赖相同部份，可用%来通配
#%.o:%.cpp


#wildcard				$(wildcard  ./*.cpp) 获取当前目录下所有的.cpp文件
#patsubst 				$(patsubst  %.cpp，%.o，./*.cpp) 将对应的cpp 文件名替换成 .o 文件名



#include -C ./sar/Makefile
#make -C ./sar/Makefile

# 设置依赖文件目录
DEP_DIR = .deps
# 创建依赖目录
$(shell mkdir -p $(DEP_DIR)/agent)

# 源文件和对象文件
OBJ_agent=$(patsubst %.c,%.o,$(wildcard ./agent/*.c))
TARGET=send_data

# 编译标志
CFLAGS = -g --std=gnu99 -D_GNU_SOURCE -Wall -Wextra -I./libpcap

# 依赖标志
DEP_FLAGS = -MT $@ -MMD -MP -MF $(DEP_DIR)/$*.d

$(shell rm -rf $(TARGET))
$(shell rm -rf sadc)
$(TARGET):$(OBJ_agent)
#	$(CXX) $^ -o $@
	make -C ./sar
	make -C ./libpcap
	make -C ./tcpdump-5.0.0 tcpdump.o fptype.o libnetdissect.a
#	$(shell cp ./sar/sadc .)
	$(CC) -g $(OBJ_agent)  ./sar/act_sadc.o ./sar/sadc.o ./sar/pr_stats.o  ./sar/sa_wrap.o ./sar/sa_common.o ./sar/network_security.o ./sar/librdstats.a ./sar/librdsensors.a ./sar/libsyscom.a  ./tcpdump-5.0.0/fptype.o ./tcpdump-5.0.0/tcpdump.o ./tcpdump-5.0.0/libnetdissect.a ./libpcap/libpcap.a -o $@ -lm -lpthread -ldl -lrt -lcrypto
%.o:%.c
	$(CC) $(CFLAGS) $(DEP_FLAGS) -c $< -o $@

# 包含所有生成的依赖文件
-include $(patsubst %.o,$(DEP_DIR)/%.d,$(OBJ_agent))

clean:
	$(RM) $(OBJ_agent) 
	$(RM) ./sar/*.o
	$(RM) ./libpcap/*.o
	$(RM) ./libpcap/*.a
	$(RM) ./tcpdump-5.0.0/*.o ./tcpdump-5.0.0/*.a
	$(RM) -r $(DEP_DIR)
	$(RM) $(TARGET)

# 完全重新构建
rebuild: clean
	$(MAKE) all

show:
	#echo $(AS)
	#echo $(CC)
	#echo $(CPP)
	#echo $(CXX)
	#echo $(RM)

	#echo $(wildcard  ./*.cpp)
	#echo $(patsubst %.cpp,%.o,$(wildcard ./*.cpp))
	#echo $(OBJ)
	#echo $(CFLAGS)
