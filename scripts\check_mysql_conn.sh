#!/bin/bash

# 使用方法: ./script.sh mysql_log_file
# 统计整个日志文件中每秒的连接数量，显示连接数最多的前10秒

if [ $# -lt 1 ]; then
    echo "Usage: $0 log_file"
    echo "Example: $0 mysql.log"
    exit 1
fi

LOG_FILE=$1

# 处理日志并按秒统计连接
awk '
BEGIN {
    current_timestamp = "";
}

# 当找到时间戳行时，保存当前时间戳
/^[0-9]+ [0-9]+:[0-9]+:[0-9]+/ {
    current_timestamp = $1 " " $2;
}

# 当找到Connect行时进行统计
/Connect/ && current_timestamp != "" {
    connections[current_timestamp]++;
    total_connections++;
    
    # 提取连接来源(可选)
    match($0, /Connect[[:space:]]+([^@]+)@([^[:space:]]+)/, arr);
    if (arr[2] != "") {
        hosts[current_timestamp, arr[2]]++;
    }
}

END {
    print "总连接数: " total_connections;
    
    # 创建临时数组用于排序
    for (timestamp in connections) {
        sorted_timestamps[timestamp] = connections[timestamp];
    }
    
    # 按连接数排序输出前10个时间戳
    print "\n=== 连接数最多的前10秒 ===";
    print "时间戳                连接数";
    print "----------------------------";
    
    PROCINFO["sorted_in"] = "@val_num_desc";
    count = 0;
    for (timestamp in sorted_timestamps) {
        printf "%-20s %d 连接\n", timestamp, sorted_timestamps[timestamp];
        
        # 显示该秒内的主机分布(可选)
        for (i in hosts) {
            split(i, parts, SUBSEP);
            if (parts[1] == timestamp) {
                printf "  └─ %-30s %d 连接\n", parts[2], hosts[i];
            }
        }
        
        count++;
        if (count >= 10) break;
    }
}' "$LOG_FILE"
