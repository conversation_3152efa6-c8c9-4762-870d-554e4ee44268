.TH SADF 1 "APRIL 2017" Linux "Linux User's Manual" -*- nroff -*-
.SH NAME
sadf \- Display data collected by sar in multiple formats.
.SH SYNOPSIS
.B sadf [ -C ] [ -c | -d | -g | -j | -p | -r | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ] [ -O
.I opts
.B [,...] ] [ -P {
.I cpu_list
.B | ALL } ] [ -s [
.I hh:mm[:ss]
.B ] ] [ -e [
.I hh:mm[:ss]
.B ] ] [ --
.I sar_options
.B ] [
.I interval
.B [
.I count
.B ] ] [
.I datafile
|
.I -[0-9]+
.B ]
.SH DESCRIPTION
The
.B sadf
command is used for displaying the contents of data files created by the
.BR sar (1)
command. But unlike
.BR sar ,
.B sadf
can write its data in many different formats (CSV, XML, etc.)
The default format is one that can
easily be handled by pattern processing commands like awk (see option -p).
The
.B sadf
command can also be used to draw graphs for the various activities collected
by
.B sar
and display them as SVG (Scalable Vector Graphics) graphics in your web browser
(see option -g).

The
.B sadf
command extracts and writes to standard output records saved in the
.I datafile
file. This file must have been created by a version of
.B sar
which is compatible with that of
.B sadf.
If
.I datafile
is omitted,
.B sadf
uses the standard system activity daily data file.
It is also possible to enter -1, -2 etc. as an argument to
.B sadf
to display data of that days ago.
For example, -1 will point at the standard system
activity file of yesterday.

The standard system activity daily data file is named
.I saDD
or
.IR saYYYYMMDD ,
where YYYY stands for the current year, MM for the current month and
DD for the current day.
.B sadf
will look for the most recent of
.I saDD
and
.IR saYYYYMMDD ,
and use it. By default it is located in the
.I @SA_DIR@
directory. Yet it is possible to specify an alternate location for it:
If
.I datafile
is a directory (instead of a plain file) then it will be considered as
the directory where the standard system activity daily data file is
located.

The
.I interval
and
.I count
parameters are used to tell
.B sadf
to select
.I count
records at
.I interval
seconds apart. If the
.I count
parameter is not set, then all the records saved in the data file will be
displayed.

All the activity flags of
.B sar
may be entered on the command line to indicate which
activities are to be reported. Before specifying them, put a pair of
dashes (--) on the command line in order not to confuse the flags
with those of
.B sadf.
Not specifying any flags selects only CPU activity.

.SH OPTIONS
.IP -C
Tell
.B sadf
to display comments present in file.
.IP -c
Convert an old system activity binary datafile (version 9.1.6 and later)
to current up-to-date format. Use the following syntax:

.B sadf -c old_datafile > new_datafile

.IP -d
Print the contents of the data file in a format that can easily
be ingested by a relational database system. The output consists
of fields separated by a semicolon. Each record contains
the hostname of the host where the file was created, the interval value
(or -1 if not applicable), the timestamp in a form easily acceptable by
most databases, and additional semicolon separated data fields as specified
by
.I sar_options
command line options.
Note that timestamp output can be controlled by options -T, -t and -U.
.IP "-e [ hh:mm[:ss] ]"
Set the ending time of the report, given in local time. The default ending
time is 18:00:00. Hours must be given in 24-hour format.
.IP -g
Print the contents of the data file in SVG (Scalable Vector Graphics) format.
This option enables you to display some fancy graphs in your web browser.
Use the following syntax:

.B sadf -g your_datafile [ --
.I sar_options
.B ] > output.svg

and open the resulting SVG file in your favorite web browser.
.IP -H
Display only the header of the report (when applicable). If no format has
been specified, then the header data (metadata) of the data file are displayed.
.IP -h
When used in conjunction with option -d, all activities
will be displayed horizontally on a single line.
.IP -j
Print the contents of the data file in JSON (JavaScript Object Notation)
format. Timestamps can be controlled by options -T and -t.
.IP "-O opts [,...]"
Use the specified options to control the output of
.BR sadf .
The following options are used to control SVG output displayed by
.BR "sadf -g":

.B autoscale
.RS
.RS
Draw all the graphs of a given view as large as possible based on current
view's scale. To do this, a factor (10, 100, 1000...) is used to
enlarge the graph drawing.
This option may be interesting when several graphs are drawn on the same
view, some with only very small values, and others with high ones,
the latter making the former hardly visible.
.RE

.BR height= value
.RS
Set SVG canvas height to
.IR value .
.RE

.B oneday
.RS
Display graphs data over a period of 24 hours. Note that hours are still
printed in UTC by default: You should use option -T to print them in local
time and get a time window starting from midnight.
.RE

.B packed
.RS
Group all views from the same activity (and for the same device) on the same row.
.RE

.B showidle
.RS
Also display %idle state in graphs for CPU statistics.
.RE

.B showinfo
.RS
Display additional information (such as the date and the host name) on each view.
.RE

.B skipempty
.RS
Do not display views where all graphs have only zero values.
.RE

The following option is used to control raw output displayed by
.BR "sadf -r":

.B showhints
.RS
Display additional noteworthy information, such as e.g., a monotonic
counter value which has decreased.
.RE
.RE
.IP "-P { cpu_list | ALL }"
Tell
.B sadf
that processor dependent statistics are to be reported only for the
specified processor or processors.
.I cpu_list
is a list of comma-separated values or range of values (e.g.,
.BR 0,2,4-7,12- ).
Note that processor 0 is the first processor, and processor
.B all
is the global average among all processors.
Specifying the
.B ALL
keyword reports statistics for each individual processor, and globally for
all processors.
.IP -p
Print the contents of the data file in a format that can
easily be handled by pattern processing commands like awk.
The output consists of fields separated by a tab. Each record contains the
hostname of the host where the file was created, the interval value
(or -1 if not applicable), the timestamp,
the device name (or - if not applicable),
the field name and its value.
Note that timestamp output can be controlled by options -T, -t and -U.
.IP -r
Print the raw contents of the data file. With this format, the values for
all the counters are displayed as read from the kernel, which means e.g., that
no average values are calculated over the elapsed time interval.
.IP "-s [ hh:mm[:ss] ]"
Set the starting time of the data (given in local time), causing the
.B sadf
command to extract records time-tagged at, or following, the time
specified. The default starting time is 08:00:00.
Hours must be given in 24-hour format.
.IP -T
Display timestamp in local time instead of UTC (Coordinated Universal Time).
.IP -t
Display timestamp in the original local time of the data file creator
instead of UTC (Coordinated Universal Time).
.IP -U
Display timestamp (UTC - Coordinated Universal Time) in seconds from
the epoch.
.IP -V
Print version number then exit.
.IP -x
Print the contents of the data file in XML format.
Timestamps can be controlled by options -T and -t.
The corresponding
DTD (Document Type Definition) and XML Schema are included in the sysstat
source package. They are also available at
.I http://pagesperso-orange.fr/sebastien.godard/download.html

.SH ENVIRONMENT
The
.B sadf
command takes into account the following environment variable:

.IP S_TIME_DEF_TIME
If this variable exists and its value is
.BR UTC
then
.B sadf
will use UTC time instead of local time to determine the current daily data
file located in the
.IR @SA_DIR@
directory.
.SH EXAMPLES
.B sadf -d @SA_DIR@/sa21 -- -r -n DEV
.RS
Extract memory and network statistics from system activity
file 'sa21', and display them in a format that can be ingested by a
database.
.RE

.B sadf -p -P 1
.RS
Extract CPU statistics for processor 1 (the second processor) from current
daily data file, and display them in a format that can easily be handled
by a pattern processing command.
.RE

.SH BUGS
SVG output (as created by option -g) is fully compliant with SVG 1.1 standard.
Graphics have been successfully displayed in various web browsers, including
Firefox, Chrome and Opera. Yet SVG rendering is broken on Microsoft browsers
(tested on Internet Explorer 11 and Edge 13.1): So please don't use them.

.SH FILES
.I @SA_DIR@/saDD
.br
.I @SA_DIR@/saYYYYMMDD
.RS
The standard system activity daily data files and their default location.
YYYY stands for the current year, MM for the current month and DD for the
current day.

.RE
.SH AUTHOR
Sebastien Godard (sysstat <at> orange.fr)
.SH SEE ALSO
.BR sar (1),
.BR sadc (8),
.BR sa1 (8),
.BR sa2 (8),
.BR sysstat (5)

.I http://pagesperso-orange.fr/sebastien.godard/
