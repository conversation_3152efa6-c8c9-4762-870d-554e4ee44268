# -*- perl -*-

# The "verbose" Link Management Protocol test involves a float calculation that
# may produce a slightly different result depending on the compiler and the
# version of the instruction set for which it's generating code (see GitHub
# issue #333). The test is done with an output file that depends on the
# floating-point type, as reported by "./tcpdump --fp-type".

$testlist = [
    {
        config_set => 'HAVE_FPTYPE1',
        name => 'lmp-v-fptype1',
        input => 'lmp.pcap',
        output => 'lmp-v-fptype1.out',
        args   => '-T lmp -v'
    },
    {
        config_set => 'HAVE_FPTYPE2',
        name => 'lmp-v-fptype2',
        input => 'lmp.pcap',
        output => 'lmp-v-fptype2.out',
        args   => '-T lmp -v'
    },
    ];

1;
