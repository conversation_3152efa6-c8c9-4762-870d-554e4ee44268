# Danish translation of the sysstat package.
# Copyright (C) 2013 Free Software Foundation, Inc.
# This file is distributed under the same license as the sysstat package.
#
# <PERSON> <<EMAIL>>, 2007.
# <PERSON><PERSON> <<EMAIL>>, 2009.
# <PERSON> <<EMAIL>>, 2008, 2009, 2010, 2011, 2012, 2013.
#
# power management -> strømstyring
#
msgid ""
msgstr ""
"Project-Id-Version: sysstat-10.1.6\n"
"Report-Msgid-Bugs-To: sysstat <at> orange.fr\n"
"POT-Creation-Date: 2013-06-08 09:01+0200\n"
"PO-Revision-Date: 2013-06-15 15:19+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Danish <<EMAIL>>\n"
"Language: da\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms:  nplurals=2; plural=(n != 1);\n"

#: iostat.c:86 cifsiostat.c:71 mpstat.c:90 sar.c:94 pidstat.c:83
#: nfsiostat.c:70
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ]\n"
msgstr "Brug: %s [ tilvalg ] [ <interval> [ <count> ] ]\n"

#: iostat.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ] [ --debuginfo ]\n"
msgstr ""
"Tilvalg er:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | ETIKET | STI | UUID | ... } ]\n"
"[ [ -T ] -g <gruppenavn> ] [ -P ] <enhed> [,...] | ALL ] ]\n"
"[ <enhed> [...] | ALL ] ] [ --debuginfo ]\n"

#: iostat.c:95
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ]\n"
msgstr ""
"Tilvalg er:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | ETIKET | STI | UUID | ... } ]\n"
"[ [ -T ] -g <gruppenavn> ] [ -p [ <enhed> [,...] | ALL ] ]\n"
"[ <enhed> [...] | ALL ] ]\n"

#: iostat.c:330
#, c-format
msgid "Cannot find disk data\n"
msgstr "Kan ikke lokalisere diskdata\n"

#: iostat.c:1394 sa_common.c:1303
#, c-format
msgid "Invalid type of persistent device name\n"
msgstr "Ugyldig type vedvarende enhedsnavn\n"

#: sadf_misc.c:596
#, c-format
msgid "System activity data file: %s (%#x)\n"
msgstr "Systemaktivitetsdatafil: %s (%#x)\n"

#: sadf_misc.c:605
#, c-format
msgid "Host: "
msgstr "Vært: "

#: sadf_misc.c:611
#, c-format
msgid "Size of a long int: %d\n"
msgstr "Størrelse på en lang int: %d\n"

#: sadf_misc.c:613
#, c-format
msgid "List of activities:\n"
msgstr "Oversigt over aktiviteter:\n"

# evt. "Ukendt format for aktivitet"
#: sadf_misc.c:626
#, c-format
msgid "\t[Unknown activity format]"
msgstr "\t[Ukendt aktivitetsformat]"

#: sadc.c:84
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
msgstr "Brug: %s [ tilvalg ] [ <interval> [ <count> ] ] [ <uddatafil> ]\n"

#: sadc.c:87
#, c-format
msgid ""
"Options are:\n"
"[ -C <comment> ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"
msgstr ""
"Tilvalg er:\n"
"[ -C <kommentar> ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"

#: sadc.c:250
#, c-format
msgid "Cannot write data to system activity file: %s\n"
msgstr "Kan ikke skrive data til systemaktivitetsfil: %s\n"

#: sadc.c:537
#, c-format
msgid "Cannot write system activity file header: %s\n"
msgstr "Kan ikke skrive systemaktivitetsfilhoved: %s\n"

#: sadc.c:650 sadc.c:659 sadc.c:720 ioconf.c:491 rd_stats.c:105
#: sa_common.c:1109 count.c:275
#, c-format
msgid "Cannot open %s: %s\n"
msgstr "Kan ikke åbne %s: %s\n"

#: sadc.c:842
#, c-format
msgid "Cannot append data to that file (%s)\n"
msgstr "Kan ikke tilføje data til den fil (%s)\n"

#: common.c:62
#, c-format
msgid "sysstat version %s\n"
msgstr "sysstat version %s\n"

#: cifsiostat.c:75 nfsiostat.c:74
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"
msgstr ""
"Tilvalg er:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"

#: cifsiostat.c:78 nfsiostat.c:77
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
msgstr ""
"Tilvalg er:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#: mpstat.c:93
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -P { <cpu> [,...] | ON | ALL } ]\n"
msgstr ""
"Tilvalg er:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -P { <cpu> [,...] | ON | ALL } ]\n"

# sar.c:
#: mpstat.c:609 sar.c:402 pidstat.c:1857
msgid "Average:"
msgstr "Middel:"

#: mpstat.c:976
#, c-format
msgid "Not that many processors!\n"
msgstr "Der er ikke så mange cpuer!\n"

#: sadf.c:86
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <datafile> ]\n"
msgstr "Brug: %s [ tilvalg ] [ <interval> [ <count> ] ] [ <datafil> ]\n"

#: sadf.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -C ] [ -d | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -P { <cpu> [,...] | ALL } ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"
"[ -- <sar_options> ]\n"
msgstr ""
"Tilvalg er:\n"
"[ -C ] [ -d | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -P { <cpu> [,...] | ALL } ] [ -s [ <tt:mm:ss> ] ] [ -e [ <tt:mm:ss> ] ]\n"
"[ -- <sar_tilvalg> ]\n"

#: sar.c:109
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -d ] [ -F ] [ -H ] [ -h ] [ -p ] [ -q ] [ -R ]\n"
"[ -r ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ] [ -v ] [ -W ] [ -w ] [ -y ]\n"
"[ -I { <int> [,...] | SUM | ALL | XALL } ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -m { <keyword> [,...] | ALL } ] [ -n { <keyword> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filename> ] | -o [ <filename> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"
msgstr ""
"Tilvalg:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -d ] [ -F ] [ -H ] [ -h ] [ -p ] [ -q ] [ -R ]\n"
"[ -r ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ] [ -v ] [ -W ] [ -w ] [ -y ]\n"
"[ -I { <int> [,...] | SUM | ALL | XALL } ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -m { <nøgleord> [,...] | ALL } ] [ -n { <nøgleord> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filnavn> ] | -o [ <filnavn> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <tt:mm:ss> ] ] [ -e [ <tt:mm:ss> ] ]\n"

#: sar.c:131
#, c-format
msgid "Main options and reports:\n"
msgstr "Vigtigste tilvalg og rapporter:\n"

#: sar.c:132
#, c-format
msgid "\t-b\tI/O and transfer rate statistics\n"
msgstr "\t-b\tI/O og statistik for overførelsesrate\n"

#: sar.c:133
#, c-format
msgid "\t-B\tPaging statistics\n"
msgstr "\t-B\tPagingstatistik\n"

#: sar.c:134
#, c-format
msgid "\t-d\tBlock devices statistics\n"
msgstr "\t-d\tStatistik for blokenheder\n"

#: sar.c:135
#, c-format
msgid "\t-F\tFilesystems statistics\n"
msgstr "\t-F\tStatistik for filsystemer\n"

#: sar.c:136
#, c-format
msgid "\t-H\tHugepages utilization statistics\n"
msgstr "\t-H\tForbrugsstatistik for store sider\n"

#: sar.c:137
#, c-format
msgid ""
"\t-I { <int> | SUM | ALL | XALL }\n"
"\t\tInterrupts statistics\n"
msgstr ""
"\t-I { <int> | SUM | ALL | XALL }\n"
"\t\tStatistik for afbrydelser\n"

# clock ? tog fra KDE ksysguard (klok)
# øjeblikkelig klokfrekvens / "CPU-klokfrevens lige nu:"/"CPUens 
# klokfrekvens lige nu
# spændingsinddata eller spændingsindgange.
#: sar.c:139
#, c-format
msgid ""
"\t-m { <keyword> [,...] | ALL }\n"
"\t\tPower management statistics\n"
"\t\tKeywords are:\n"
"\t\tCPU\tCPU instantaneous clock frequency\n"
"\t\tFAN\tFans speed\n"
"\t\tFREQ\tCPU average clock frequency\n"
"\t\tIN\tVoltage inputs\n"
"\t\tTEMP\tDevices temperature\n"
"\t\tUSB\tUSB devices plugged into the system\n"
msgstr ""
"\t-m { <nøgleord> [,...] | ALL }\n"
"\t\tStatistik for strømstyring\n"
"\t\tNøgleord:\n"
"\t\tCPU\tCPU-klokfrekvens lige nu\n"
"\t\tFAN\tBlæserhastighed\n"
"\t\tFREQ\tGennemsnitlig CPU-klokfrekvens\n"
"\t\tIN\tSpændingsindgange\n"
"\t\tTEMP\tEnhedens temperatur\n"
"\t\tUSB\tUSB-enheder tilsluttet systemet\n"

#: sar.c:148
#, c-format
msgid ""
"\t-n { <keyword> [,...] | ALL }\n"
"\t\tNetwork statistics\n"
"\t\tKeywords are:\n"
"\t\tDEV\tNetwork interfaces\n"
"\t\tEDEV\tNetwork interfaces (errors)\n"
"\t\tNFS\tNFS client\n"
"\t\tNFSD\tNFS server\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP traffic\t(v4)\n"
"\t\tEIP\tIP traffic\t(v4) (errors)\n"
"\t\tICMP\tICMP traffic\t(v4)\n"
"\t\tEICMP\tICMP traffic\t(v4) (errors)\n"
"\t\tTCP\tTCP traffic\t(v4)\n"
"\t\tETCP\tTCP traffic\t(v4) (errors)\n"
"\t\tUDP\tUDP traffic\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP traffic\t(v6)\n"
"\t\tEIP6\tIP traffic\t(v6) (errors)\n"
"\t\tICMP6\tICMP traffic\t(v6)\n"
"\t\tEICMP6\tICMP traffic\t(v6) (errors)\n"
"\t\tUDP6\tUDP traffic\t(v6)\n"
msgstr ""
"\t-n { <nøgleord> [,...] | ALL }\n"
"\t\tNetværksstatistik\n"
"\t\tNøgleord er:\n"
"\t\tDEV\tNetværksgrænseflader\n"
"\t\tEDEV\tNetværksgrænseflader (fejl)\n"
"\t\tNFS\tNFS-klient\n"
"\t\tNFSD\tNFS-server\n"
"\t\tSOCK\tSokler\t(v4)\n"
"\t\tIP\tIP-trafik\t(v4)\n"
"\t\tEIP\tIP-trafik\t(v4) (fejl)\n"
"\t\tICMP\tICMP-trafik\t(v4)\n"
"\t\tEICMP\tICMP-trafik\t(v4) (fejl)\n"
"\t\tTCP\tTCP-trafik\t(v4)\n"
"\t\tETCP\tTCP-trafik\t(v4) (fejl)\n"
"\t\tUDP\tUDP-trafik\t(v4)\n"
"\t\tSOCK6\tSokler\t(v6)\n"
"\t\tIP6\tIP-trafik\t(v6)\n"
"\t\tEIP6\tIP-trafik\t(v6) (fejl)\n"
"\t\tICMP6\tICMP-trafik\t(v6)\n"
"\t\tEICMP6\tICMP-trafik\t(v6) (fejl)\n"
"\t\tUDP6\tUDP-trafik\t(v6)\n"

#: sar.c:169
#, c-format
msgid "\t-q\tQueue length and load average statistics\n"
msgstr "\t-q\tStatistik for kølængde og gennemsnitlig belastning\n"

#: sar.c:170
#, c-format
msgid "\t-r\tMemory utilization statistics\n"
msgstr "\t-r\tStatistik for hukommelsesforbrug\n"

#: sar.c:171
#, c-format
msgid "\t-R\tMemory statistics\n"
msgstr "\t-R\tHukommelsesstatistik\n"

#: sar.c:172
#, c-format
msgid "\t-S\tSwap space utilization statistics\n"
msgstr "\t-S\tStatistik for swappladsforbrug\n"

#: sar.c:173
#, c-format
msgid ""
"\t-u [ ALL ]\n"
"\t\tCPU utilization statistics\n"
msgstr ""
"\t-u [ ALL ]\n"
"\t\tStatistik for CPU-forbrug\n"

#: sar.c:175
#, c-format
msgid "\t-v\tKernel tables statistics\n"
msgstr "\t-v\tStatistik for kernetabeller\n"

#: sar.c:176
#, c-format
msgid "\t-w\tTask creation and system switching statistics\n"
msgstr "\t-w\tOpgaveoprettelse og statistik for systemskift\n"

#: sar.c:177
#, c-format
msgid "\t-W\tSwapping statistics\n"
msgstr "\t-W\tStatistik for swapping\n"

#: sar.c:178
#, c-format
msgid "\t-y\tTTY devices statistics\n"
msgstr "\t-y\tStatistik for TTY-enheder\n"

#: sar.c:236
#, c-format
msgid "End of data collecting unexpected\n"
msgstr "Uventet afslutning på dataindsamling\n"

#: sar.c:823
#, c-format
msgid "Invalid data format\n"
msgstr "Ugyldig dataformat\n"

#: sar.c:827
#, c-format
msgid "Using a wrong data collector from a different sysstat version\n"
msgstr "Bruger en forkert dataindsamler fra en anden sysstat version\n"

#: sar.c:851
#, c-format
msgid "Inconsistent input data\n"
msgstr "Inkonsistent inddata\n"

#: sar.c:1034 pidstat.c:216
#, c-format
msgid "Requested activities not available\n"
msgstr "Angivne aktiviteter er ikke tilgængelig\n"

#: sar.c:1304
#, c-format
msgid "-f and -o options are mutually exclusive\n"
msgstr "tilvalgene -f og -o udelukker hinanden\n"

#: sar.c:1310
#, c-format
msgid "Not reading from a system activity file (use -f option)\n"
msgstr "Læsning sker ikke fra en systemaktivitetsfil (brug tilvalget -f)\n"

#: sar.c:1442
#, c-format
msgid "Cannot find the data collector (%s)\n"
msgstr "Kan ikke lokalisere dataindsamleren (%s)\n"

#: sa_common.c:917
#, c-format
msgid "Error while reading system activity file: %s\n"
msgstr "Læsefejl ved indlæsning af aktivitetsfil: %s\n"

#: sa_common.c:927
#, c-format
msgid "End of system activity file unexpected\n"
msgstr "Uventet afslutning på systemaktivitetsfil\n"

#: sa_common.c:946
#, c-format
msgid "File created by sar/sadc from sysstat version %d.%d.%d"
msgstr "Fil oprettet af sar/sadc fra sysstat version %d.%d.%d"

#: sa_common.c:977
#, c-format
msgid "Invalid system activity file: %s\n"
msgstr "Ugyldig systemaktivitetsfil: %s\n"

#: sa_common.c:984
#, c-format
msgid "Current sysstat version can no longer read the format of this file (%#x)\n"
msgstr "Nuværende sysstat version kan ikke længere læse formatet på denne fil (%#x)\n"

#: sa_common.c:1216
#, c-format
msgid "Requested activities not available in file %s\n"
msgstr "Angivne aktiviteter findes ikke i filen %s\n"

#: pidstat.c:86
#, c-format
msgid ""
"Options are:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -r ] [ -s ] [ -t ] [ -U [ <username> ] ] [ -u ]\n"
"[ -V ] [ -w ] [ -C <command> ] [ -p { <pid> [,...] | SELF | ALL } ]\n"
"[ -T { TASK | CHILD | ALL } ]\n"
msgstr ""
"Tilvalg er:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -r ] [ -s ] [ -t ] [ -U [ <brugernavn> ] ] [ -u ]\n"
"[ -V ] [ -W ] [ -C <kommando> ] [ -p { <pid> [,...] | SELF | ALL } ]\n"
"[ -T { TASK | CHILD | ALL } ]\n"

#: count.c:321
#, c-format
msgid "Cannot handle so many processors!\n"
msgstr "Kan ikke håndtere så mange cpuer!\n"

#: pr_stats.c:2348 pr_stats.c:2361 pr_stats.c:2461 pr_stats.c:2473
msgid "Summary"
msgstr "Resume"

#: pr_stats.c:2399
msgid "Other devices not listed here"
msgstr "Andre enheder ikke vist her"
