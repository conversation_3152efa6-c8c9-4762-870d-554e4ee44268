addrtoname.o: addrtoname.c config.h netdissect-stdinc.h ftmacros.h \
 compiler-tests.h varattrs.h funcattrs.h my.h ../libpcap/pcap.h \
 ../libpcap/pcap/pcap.h ../libpcap/pcap/funcattrs.h \
 ../libpcap/pcap/compiler-tests.h ../libpcap/pcap/pcap-inttypes.h \
 ../libpcap/pcap/socket.h ../libpcap/pcap/bpf.h ../libpcap/pcap/dlt.h \
 ../libpcap/pcap-namedb.h ../libpcap/pcap/namedb.h netdissect.h \
 status-exit-codes.h diag-control.h ip.h ip6.h addrtoname.h extract.h \
 addrtostr.h ethertype.h llc.h oui.h
config.h:
netdissect-stdinc.h:
ftmacros.h:
compiler-tests.h:
varattrs.h:
funcattrs.h:
my.h:
../libpcap/pcap.h:
../libpcap/pcap/pcap.h:
../libpcap/pcap/funcattrs.h:
../libpcap/pcap/compiler-tests.h:
../libpcap/pcap/pcap-inttypes.h:
../libpcap/pcap/socket.h:
../libpcap/pcap/bpf.h:
../libpcap/pcap/dlt.h:
../libpcap/pcap-namedb.h:
../libpcap/pcap/namedb.h:
netdissect.h:
status-exit-codes.h:
diag-control.h:
ip.h:
ip6.h:
addrtoname.h:
extract.h:
addrtostr.h:
ethertype.h:
llc.h:
oui.h:
