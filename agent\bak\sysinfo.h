#define ORIG_TOPDEFS 1
#define sum_cores  sysconf(_SC_NPROCESSORS_ONLN)
typedef struct CPU_P {
        long long u_frme;
        long long u_f;
        char u_frme_p[6];//u_frme_p
        char s_frme_p[6];//s_frme_p
        char n_frme_p[6];//n_frme_p
        char i_frme_p[6];//i_frme_p
        char w_frme_p[6];//w_frme_p
        char x_frme_p[6];//x_frme_p
        char y_frme_p[6];//y_frme_p
        char z_frme_p[6];//z_frme_p
	char z1_frme_p[6];
	char z2_frme_p[6];
	char sum_user_p[6];
	char sum_sys_p[6];
	char sum_total_p[6];
} CPU_P;

typedef struct MEMINFO{
	unsigned long	KB_active;
	unsigned long	KB_main_buffers;
	unsigned long	KB_page_cached;
	unsigned long   KB_inactive;
	unsigned long	KB_main_available;
	unsigned long	KB_main_free;
	unsigned long	KB_main_total;
	unsigned long	KB_swap_free;
	unsigned long	KB_swap_total;
	//count to used
	unsigned long	KB_main_cached;//page cached + SReclaimable
	unsigned long	KB_swap_used;
	unsigned long	KB_main_used;
	         char   pcnt_tot[6];//my_used+my_misc %
		 char   swp_USE_p[6];//swap_used%	






}MEMINFO;

typedef struct _os_data 
{
  char avg_load11[5];
  int active1;
  char time1[20];
  char hostname[256];
  int  cores;
  CPU_P cpu_p; 
  MEMINFO meminfo;

}os_data;
int sys();
int get_load();
int output_load();
int avg_load(os_data *os);
int get_time1(os_data *os);
char* get_time();
os_data *os;
char *time1;

//process
typedef          long long SIC_t;
 #define TRIMz(x)  ((tz = (SIC_t)(x)) < 0 ? 0 : tz)
   SIC_t z1_frme,z2_frme,u_frme,u_f, s_frme, n_frme, i_frme, w_frme, x_frme, y_frme, z_frme, tot_frme,sum_user,sum_sys, tz;
   float scale;
/*
#ifndef OFF_NUMASKIP
static int Numa_node_of_cpu(int num) { return (1 == (num % 4)) ? 0 : (num % 4); }
#else
static int Numa_node_of_cpu(int num) { return (num % 4); }
#endif
int smp_num_cpus;
#define CAPBUFSIZ    32
static int         Cpu_faux_tot;
//typedef unsigned char FLG_t;
typedef int FLG_t;
#define CAPTABMAX  9
#define WINNAMSIZ  4
#define CLRBUFSIZ    64
#define PFLAGSSIZ    80
#define GROUPSMAX  4
#define SCREENMAX   512
//static int Numa_node_tot;
typedef unsigned long long TIC_t;
//typedef          long long SIC_t;
#define TICS_EDGE  20
#define CHKw(q,f)    (int)((q)->rc.winflags & (f))
#define GRPNAMSIZ  WINNAMSIZ+2
#define SIGNAL_STRING
#define QUICK_THREADS
*/

//new

#define CAPTABMAX  9             /* max entries in each win's caps table   */
#define GROUPSMAX  4             /* the max number of simultaneous windows */
#define WINNAMSIZ  4             /* size of RCW_t winname buf (incl '\0')  */
#define GRPNAMSIZ  WINNAMSIZ+2   /* window's name + number as in: '#:...'  */

        /* This typedef just ensures consistent 'process flags' handling */
typedef int FLG_t;
#define TNYBUFSIZ    16
#define CAPBUFSIZ    32
#define CLRBUFSIZ    64
#define PFLAGSSIZ   128
#define SMLBUFSIZ   128
#define MEDBUFSIZ   256
#define LRGBUFSIZ   512
#define OURPATHSZ  1024
#define BIGBUFSIZ  2048
#define BOTBUFSIZ 16384


        /* The scaling 'target' used with memory fields */
enum scale_enum {
   SK_Kb, SK_Mb, SK_Gb, SK_Tb, SK_Pb, SK_Eb
};

#define MONPIDMAX  20


        /* This type helps support both a window AND the rcfile */
typedef struct RCW_t {  // the 'window' portion of an rcfile
   int    sortindx,               // sort field (represented as procflag)
          winflags,               // 'view', 'show' and 'sort' mode flags
          maxtasks,               // user requested maximum, 0 equals all
          graph_cpus,             // 't' - View_STATES supplementary vals
          graph_mems,             // 'm' - View_MEMORY supplememtary vals
          double_up,              // '4' - show multiple cpus on one line
          combine_cpus,           // '!' - keep combining additional cpus
          core_types,             // '5' - show/filter P-core/E-core cpus
          summclr,                // a colors 'number' used for summ info
          msgsclr,                //             "           in msgs/pmts
          headclr,                //             "           in cols head
          taskclr;                //             "           in task rows
   char   winname [WINNAMSIZ];    // name for the window, user changeable
   FLG_t  fieldscur [PFLAGSSIZ];  // the fields for display & their order
} RCW_t;




        /* This represents the complete rcfile */
typedef struct RCF_t {
   char   id;                   // rcfile version id
   int    mode_altscr;          // 'A' - Alt display mode (multi task windows)
   int    mode_irixps;          // 'I' - Irix vs. Solaris mode (SMP-only)
   float  delay_time;           // 'd'/'s' - How long to sleep twixt updates
   int    win_index;            // Curwin, as index
   RCW_t  win [GROUPSMAX];      // a 'WIN_t.rc' for each window
   int    fixed_widest;         // 'X' - wider non-scalable col addition
   int    summ_mscale;          // 'E' - scaling of summary memory values
   int    task_mscale;          // 'e' - scaling of process memory values
   int    zero_suppress;        // '0' - suppress scaled zeros toggle
   int    tics_scaled;          // ^E  - scale TIME and/or TIME+ columns
} RCF_t;

        // this next guy must never, ever change
        // ( transitioned from 'char' to 'int' )
#define RCF_XFORMED_ID  'k'
        // this next guy is incremented when columns change
        // ( to prevent older top versions from accessing )
#define RCF_VERSION_ID  'k'
        /* The default values for the local config file */

        /* The default delay twix updates */
#ifdef ORIG_TOPDEFS
#define DEF_DELAY  3.0
#else
#define DEF_DELAY  1.5
#endif


        /* Flags for each possible field (and then some) --
           these MUST be kept in sync with the Fieldstab[] array !! */
enum pflag {
   EU_PID = 0, EU_PPD,
   EU_UED, EU_UEN, EU_URD, EU_URN, EU_USD, EU_USN,
   EU_GID, EU_GRP, EU_PGD, EU_TTY, EU_TPG, EU_SID,
   EU_PRI, EU_NCE, EU_THD,
   EU_CPN, EU_CPU, EU_TME, EU_TM2,
   EU_MEM, EU_VRT, EU_SWP, EU_RES, EU_COD, EU_DAT, EU_SHR,
   EU_FL1, EU_FL2, EU_DRT,
   EU_STA, EU_CMD, EU_WCH, EU_FLG, EU_CGR,
   EU_SGD, EU_SGN, EU_TGD,
   EU_OOA, EU_OOM,
   EU_ENV,
   EU_FV1, EU_FV2,
   EU_USE,
   EU_NS1, EU_NS2, EU_NS3, EU_NS4, EU_NS5, EU_NS6,
   EU_LXC,
   EU_RZA, EU_RZF, EU_RZL, EU_RZS,
   EU_CGN,
   EU_NMA,
   EU_LID,
   EU_EXE,
   EU_RSS, EU_PSS, EU_PZA, EU_PZF, EU_PZS, EU_USS,
   EU_IRB, EU_IRO, EU_IWB, EU_IWO,
   EU_AGI, EU_AGN,
   EU_TM3, EU_TM4, EU_CUU, EU_CUC,
   EU_NS7, EU_NS8,
#ifdef USE_X_COLHDR
   // not really pflags, used with tbl indexing
   EU_MAXPFLGS
#else
   // not really pflags, used with tbl indexing & col highlighting
   EU_MAXPFLGS, EU_XON, EU_XOF
#endif
};
        // Default flags if there's no rcfile to provide user customizations
#ifdef ORIG_TOPDEFS
#define DEF_WINFLGS ( View_LOADAV | View_STATES | View_CPUSUM | View_MEMORY \
   | Show_HIBOLD | Show_HIROWS | Show_IDLEPS | Show_TASKON | Show_JRNUMS \
   | Qsrt_NORMAL )
#define DEF_GRAPHS2  0, 0
#define DEF_SCALES2  SK_Mb, SK_Kb
#define ALT_WINFLGS  DEF_WINFLGS
#define ALT_GRAPHS2  0, 0
#else
#define DEF_WINFLGS ( View_LOADAV | View_STATES | View_MEMORY | Show_CMDLIN \
   | Show_COLORS | Show_FOREST | Show_HIROWS | Show_IDLEPS | Show_JRNUMS | Show_TASKON \
   | Qsrt_NORMAL )
#define DEF_GRAPHS2  1, 2
#define DEF_SCALES2  SK_Gb, SK_Mb
#define ALT_WINFLGS (DEF_WINFLGS | Show_HIBOLD) & ~Show_FOREST
#define ALT_GRAPHS2  2, 0
#endif

        /* The Persistent 'Mode' flags!
           These are preserved in the rc file, as a single integer and the
           letter shown is the corresponding 'command' toggle */
        // 'View_' flags affect the summary (minimum), taken from 'Curwin'
#define View_CPUSUM  0x008000     // '1' - show combined cpu stats (vs. each)
#define View_CPUNOD  0x400000     // '2' - show numa node cpu stats ('3' also)
#define View_LOADAV  0x004000     // 'l' - display load avg and uptime summary
#define View_STATES  0x002000     // 't' - display task/cpu(s) states summary
#define View_MEMORY  0x001000     // 'm' - display memory summary
#define View_NOBOLD  0x000008     // 'B' - disable 'bold' attribute globally
#define View_SCROLL  0x080000     // 'C' - enable coordinates msg w/ scrolling
        // 'Show_' & 'Qsrt_' flags are for task display in a visible window
#define Show_COLORS  0x000800     // 'z' - show in color (vs. mono)
#define Show_HIBOLD  0x000400     // 'b' - rows and/or cols bold (vs. reverse)
#define Show_HICOLS  0x000200     // 'x' - show sort column emphasized
#define Show_HIROWS  0x000100     // 'y' - show running tasks highlighted
#define Show_CMDLIN  0x000080     // 'c' - show cmdline vs. name
#define Show_CTIMES  0x000040     // 'S' - show times as cumulative
#define Show_IDLEPS  0x000020     // 'i' - show idle processes (all tasks)
#define Show_TASKON  0x000010     // '-' - tasks showable when Mode_altscr
#define Show_FOREST  0x000002     // 'V' - show cmd/cmdlines with ascii art
#define Qsrt_NORMAL  0x000004     // 'R' - reversed column sort (high to low)
#define Show_JRSTRS  0x040000     // 'j' - right justify "string" data cols
#define Show_JRNUMS  0x020000     // 'J' - right justify "numeric" data cols
        // these flag(s) have no command as such - they're for internal use
#define NOPRINT_xxx  0x010000     // build task rows only (not for display)
#define EQUWINS_xxx  0x000001     // rebalance all wins & tasks (off i,n,u/U)

#ifdef ORIG_TOPDEFS
#define DEF_FIELDS { \
     75,  81, 103, 105, 119, 123, 129, 137, 111, 117, 115, 139,  76,  78,  82,  84,  86,  88,  90,  92, \
     94,  96,  98, 100, 106, 108, 112, 120, 124, 126, 130, 132, 134, 140, 142, 144, 146, 148, 150, 152, \
    154, 156, 158, 160, 162, 164, 166, 168, 170, 172, 174, 176, 178, 180, 182, 184, 186, 188, 190, 192, \
    194, 196, 198, 200, 202, 204, 206, 208, 210, 212, 214, 216, 218, 220, 222, 224, 226, 228, 230, 232, \
    234, 236, 238, 240, 242, 244, 246, 248, 250, 252, 254, 256, 258, 260, 262, 264, 266, 268, 270, 272  }
#else
#define DEF_FIELDS { \
     75,  76, 150,  81, 103, 105, 119, 123, 128, 111, 117, 115, 106, 108, 137, 140, 139,  78,  82,  84, \
     86,  88,  90,  92,  94,  96,  98, 100, 112, 120, 124, 126, 130, 132, 134, 142, 144, 146, 148, 152, \
    154, 156, 158, 160, 162, 164, 166, 168, 170, 172, 174, 176, 178, 180, 182, 184, 186, 188, 190, 192, \
    194, 196, 198, 200, 202, 204, 206, 208, 210, 212, 214, 216, 218, 220, 222, 224, 226, 228, 230, 232, \
    234, 236, 238, 240, 242, 244, 246, 248, 250, 252, 254, 256, 258, 260, 262, 264, 266, 268, 270, 272  }
#endif
#define JOB_FIELDS { \
     75,  77, 115, 111, 117,  80, 103, 105, 137, 119, 123, 128, 120,  79, 139,  82,  84,  86,  88,  90, \
     92,  94,  96,  98, 100, 106, 108, 112, 124, 126, 130, 132, 134, 140, 142, 144, 146, 148, 150, 152, \
    154, 156, 158, 160, 162, 164, 166, 168, 170, 172, 174, 176, 178, 180, 182, 184, 186, 188, 190, 192, \
    194, 196, 198, 200, 202, 204, 206, 208, 210, 212, 214, 216, 218, 220, 222, 224, 226, 228, 230, 232, \
    234, 236, 238, 240, 242, 244, 246, 248, 250, 252, 254, 256, 258, 260, 262, 264, 266, 268, 270, 272  }
#define MEM_FIELDS { \
     75, 117, 119, 120, 123, 125, 127, 129, 131, 154, 132, 156, 135, 136, 102, 104, 111, 139,  76,  78, \
     80,  82,  84,  86,  88,  90,  92,  94,  96,  98, 100, 106, 108, 112, 114, 140, 142, 144, 146, 148, \
    150, 152, 158, 160, 162, 164, 166, 168, 170, 172, 174, 176, 178, 180, 182, 184, 186, 188, 190, 192, \
    194, 196, 198, 200, 202, 204, 206, 208, 210, 212, 214, 216, 218, 220, 222, 224, 226, 228, 230, 232, \
    234, 236, 238, 240, 242, 244, 246, 248, 250, 252, 254, 256, 258, 260, 262, 264, 266, 268, 270, 272  }
#define USR_FIELDS { \
     75,  77,  79,  81,  85,  97, 115, 111, 117, 137, 139,  82,  86,  88,  90,  92,  94,  98, 100, 102, \
    104, 106, 108, 112, 118, 120, 122, 124, 126, 128, 130, 132, 134, 140, 142, 144, 146, 148, 150, 152, \
    154, 156, 158, 160, 162, 164, 166, 168, 170, 172, 174, 176, 178, 180, 182, 184, 186, 188, 190, 192, \
    194, 196, 198, 200, 202, 204, 206, 208, 210, 212, 214, 216, 218, 220, 222, 224, 226, 228, 230, 232, \
    234, 236, 238, 240, 242, 244, 246, 248, 250, 252, 254, 256, 258, 260, 262, 264, 266, 268, 270, 272  }



        /* The default values for the local config file */
#define DEF_RCFILE { \
   RCF_VERSION_ID, 0, 1, DEF_DELAY, 0, { \
   { EU_CPU, DEF_WINFLGS, 0, DEF_GRAPHS2, 1, 0, 0, \
      COLOR_RED, COLOR_RED, COLOR_YELLOW, COLOR_RED, \
      "Def", DEF_FIELDS }, \
   { EU_PID, ALT_WINFLGS, 0, ALT_GRAPHS2, 0, 0, 0, \
      COLOR_CYAN, COLOR_CYAN, COLOR_WHITE, COLOR_CYAN, \
      "Job", JOB_FIELDS }, \
   { EU_MEM, ALT_WINFLGS, 0, ALT_GRAPHS2, 0, 0, 0, \
      COLOR_MAGENTA, COLOR_MAGENTA, COLOR_BLUE, COLOR_MAGENTA, \
      "Mem", MEM_FIELDS }, \
   { EU_UEN, ALT_WINFLGS, 0, ALT_GRAPHS2, 0, 0, 0, \
      COLOR_YELLOW, COLOR_YELLOW, COLOR_GREEN, COLOR_YELLOW, \
      "Usr", USR_FIELDS } \
   }, 0, DEF_SCALES2, 0, 0 }



#define SCREENMAX   512

        /* This structure stores configurable information for each window.
           By expending a little effort in its creation and user requested
           maintenance, the only real additional per frame cost of having
           windows is an extra sort -- but that's just on pointers! */
typedef struct WIN_t {
   FLG_t  pflgsall [PFLAGSSIZ],        // all 'active/on' fieldscur, as enum
          procflgs [PFLAGSSIZ];        // fieldscur subset, as enum
   RCW_t  rc;                          // stuff that gets saved in the rcfile
   int    winnum,          // a window's number (array pos + 1)
          winlines,        // current task window's rows (volatile)
          maxpflgs,        // number of displayed procflgs ("on" in fieldscur)
          totpflgs,        // total of displayable procflgs in pflgsall array
          begpflg,         // scrolled beginning pos into pflgsall array
          endpflg,         // scrolled ending pos into pflgsall array
          begtask,         // scrolled beginning pos into total tasks
          begnext,         // new scrolled delta for next frame's begtask
#ifndef SCROLLVAR_NO
          varcolbeg,       // scrolled position within variable width col
#endif
          varcolsz,        // max length of variable width column(s)
          usrseluid,       // validated uid for 'u/U' user selection
          usrseltyp,       // the basis for matching above uid
          usrselflg,       // flag denoting include/exclude matches
          hdrcaplen;       // column header xtra caps len, if any
   char   capclr_sum [CLRBUFSIZ],      // terminfo strings built from
          capclr_msg [CLRBUFSIZ],      //   RCW_t colors (& rebuilt too),
          capclr_pmt [CLRBUFSIZ],      //   but NO recurring costs !
          capclr_hdr [CLRBUFSIZ],      //   note: sum, msg and pmt strs
          capclr_rowhigh [SMLBUFSIZ],  //         are only used when this
          capclr_rownorm [CLRBUFSIZ],  //         window is the 'Curwin'!
          cap_bold [CAPBUFSIZ],        // support for View_NOBOLD toggle
          grpname [GRPNAMSIZ],         // window number:name, printable
#ifdef USE_X_COLHDR
          columnhdr [ROWMINSIZ],       // column headings for procflgs
#else
          columnhdr [SCREENMAX],       // column headings for procflgs
#endif
         *captab [CAPTABMAX];          // captab needed by show_special()
   struct osel_s *osel_1st;            // other selection criteria anchor
   int    osel_tot;                    // total of other selection criteria
   char  *findstr;                     // window's current/active search string
   int    findlen;                     // above's strlen, without call overhead
   int    focus_pid;                   // target pid when 'F' toggle is active
   int    focus_beg;                   // ppt index where 'F' toggle has begun
   int    focus_end;                   // ppt index where 'F' toggle has ended
#ifdef FOCUS_TREE_X
   int    focus_lvl;                   // the indentation level of parent task
#endif
   struct pids_stack **ppt;            // this window's stacks ptr array
   struct WIN_t *next,                 // next window in window stack
                *prev;                 // prior window in window stack
} WIN_t;
#define LIB_USLEEP  100000
#define STRLCPY(dst,src) { memccpy(dst, src, '\0', sizeof(dst)); dst[sizeof(dst) - 1] = '\0'; }
#define CHKw(q,f)    (int)((q)->rc.winflags & (f))
#define MEDBUFSIZ   256
#define FNDBUFSIZ  MEDBUFSIZ
#define OFFw(q,f)    (q)->rc.winflags &= ~(f)
#define W_MIN_COL  3
#define W_MIN_ROW  3
#define ROWMAXSIZ  ( SCREENMAX + 16 * (CAPBUFSIZ + CLRBUFSIZ) )
#define PSU_CLREOS(y) memset(&Pseudo_screen[ROWMAXSIZ*y], '\0', Pseudo_size-(ROWMAXSIZ*y))
        /* Used to manipulate (and document) the Frames_signal states */
enum resize_states {
   BREAK_off = 0, BREAK_kbd, BREAK_sig, BREAK_autox, BREAK_screen
};

   // space between task fields/columns
#define COLPADSTR   " "
#define COLPADSIZ   ( sizeof(COLPADSTR) - 1 )
   // continuation ch when field/column truncated
#define COLPLUSCH   '+'
#define mkVIZrow1    { Curwin->begnext = +1; Curwin->begtask -= 1; }
#define VIZISw(q)    (!Rc.mode_altscr || CHKw(q,Show_TASKON))
#define FLDviz(q,i)  ( (q)->rc.fieldscur[i] &  FLDon )
#define FLDget(q,i)  ( (((q)->rc.fieldscur[i]) >> 1) - FLD_OFFSET  )

#define VARcol(E)    (-1 == Fieldstab[E].width)
#define FLDon        0x01
#define FLD_OFFSET  ( (int)'%' )
