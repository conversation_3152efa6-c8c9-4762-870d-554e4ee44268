#!/bin/bash

# 短连接测试程序快速部署脚本
# 用于将测试程序部署到远程服务器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用方法
show_usage() {
    echo "短连接测试程序部署脚本"
    echo ""
    echo "用法: $0 <remote_host> [remote_user] [remote_path]"
    echo ""
    echo "参数:"
    echo "  remote_host  - 远程服务器IP或主机名"
    echo "  remote_user  - 远程用户名 (默认: root)"
    echo "  remote_path  - 远程部署路径 (默认: /tmp)"
    echo ""
    echo "示例:"
    echo "  $0 *************"
    echo "  $0 ************* ubuntu"
    echo "  $0 ************* ubuntu /home/<USER>"
    echo ""
    echo "部署完成后，在远程服务器上运行:"
    echo "  ./remote_short_conn_test <target_host> <port> <rate> <duration>"
}

# 检查参数
if [ $# -lt 1 ] || [ $# -gt 3 ]; then
    show_usage
    exit 1
fi

REMOTE_HOST="$1"
REMOTE_USER="${2:-root}"
REMOTE_PATH="${3:-/tmp}"

print_info "开始部署短连接测试程序到远程服务器"
print_info "目标服务器: ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}"

# 检查本地文件是否存在
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
REMOTE_TEST_FILE="${SCRIPT_DIR}/remote_short_conn_test.c"
SIMPLE_TEST_FILE="${SCRIPT_DIR}/simple_short_conn_test.c"
README_FILE="${SCRIPT_DIR}/README_short_conn_test.md"

if [ ! -f "$REMOTE_TEST_FILE" ]; then
    print_error "找不到文件: $REMOTE_TEST_FILE"
    exit 1
fi

# 测试SSH连接
print_info "测试SSH连接..."
if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "${REMOTE_USER}@${REMOTE_HOST}" "echo 'SSH连接成功'" 2>/dev/null; then
    print_error "无法连接到 ${REMOTE_USER}@${REMOTE_HOST}"
    print_warning "请确保:"
    print_warning "1. SSH服务正在运行"
    print_warning "2. 用户名和密码正确"
    print_warning "3. 已配置SSH密钥认证"
    exit 1
fi

print_success "SSH连接测试成功"

# 创建远程目录
print_info "创建远程目录..."
ssh "${REMOTE_USER}@${REMOTE_HOST}" "mkdir -p ${REMOTE_PATH}/short_conn_test"

# 复制文件到远程服务器
print_info "复制测试程序源代码..."
scp "$REMOTE_TEST_FILE" "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/short_conn_test/"
scp "$SIMPLE_TEST_FILE" "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/short_conn_test/"
scp "$README_FILE" "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/short_conn_test/"

print_success "文件复制完成"

# 在远程服务器上编译程序
print_info "在远程服务器上编译程序..."
ssh "${REMOTE_USER}@${REMOTE_HOST}" << EOF
cd ${REMOTE_PATH}/short_conn_test

echo "检查编译环境..."
if ! command -v gcc &> /dev/null; then
    echo "错误: gcc未安装"
    echo "请安装gcc: "
    echo "  Ubuntu/Debian: sudo apt-get install gcc"
    echo "  CentOS/RHEL: sudo yum install gcc"
    exit 1
fi

echo "编译远程短连接测试程序..."
gcc -o remote_short_conn_test remote_short_conn_test.c
if [ \$? -eq 0 ]; then
    echo "✓ remote_short_conn_test 编译成功"
else
    echo "✗ remote_short_conn_test 编译失败"
    exit 1
fi

echo "编译简单短连接测试程序..."
gcc -o simple_short_conn_test simple_short_conn_test.c
if [ \$? -eq 0 ]; then
    echo "✓ simple_short_conn_test 编译成功"
else
    echo "✗ simple_short_conn_test 编译失败"
    exit 1
fi

chmod +x remote_short_conn_test simple_short_conn_test

echo "编译完成！"
echo ""
echo "可用的测试程序:"
ls -la remote_short_conn_test simple_short_conn_test
EOF

if [ $? -eq 0 ]; then
    print_success "程序编译成功"
else
    print_error "程序编译失败"
    exit 1
fi

# 显示部署完成信息
print_success "部署完成！"
echo ""
print_info "部署位置: ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/short_conn_test/"
print_info "可用程序:"
print_info "  - remote_short_conn_test  (推荐，功能最全)"
print_info "  - simple_short_conn_test  (简单版本)"
echo ""
print_info "使用示例:"
echo "  # 连接到远程服务器"
echo "  ssh ${REMOTE_USER}@${REMOTE_HOST}"
echo ""
echo "  # 进入测试目录"
echo "  cd ${REMOTE_PATH}/short_conn_test"
echo ""
echo "  # 运行测试 (向目标服务器发起短连接)"
echo "  ./remote_short_conn_test <目标服务器IP> <端口> <连接数/秒> <持续时间>"
echo ""
echo "  # 示例: 向*************的MySQL发起30连接/秒，持续60秒"
echo "  ./remote_short_conn_test ************* 3306 30 60"
echo ""
print_warning "注意: 请将<目标服务器IP>替换为运行网络安全监控系统的服务器IP"
echo ""
print_info "查看详细使用说明:"
echo "  cat ${REMOTE_PATH}/short_conn_test/README_short_conn_test.md"
