# NLS support for the sysstat package.
# This file is distributed under the same license as the sysstat package.
# Copyright (C) 1999-2013 Free Software Foundation, Inc.
# <PERSON><PERSON><PERSON> <sysstat [at] orange.fr>, 1999-2012.
# Japanese translation by <PERSON><PERSON><PERSON> <<EMAIL>/or.jp>, 2008-2013.
#
msgid ""
msgstr ""
"Project-Id-Version: sysstat 10.1.4\n"
"Report-Msgid-Bugs-To: sysstat <at> orange.fr\n"
"POT-Creation-Date: 2013-03-03 15:47+0100\n"
"PO-Revision-Date: 2013-03-10 21:45+0900\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Japanese <<EMAIL>>\n"
"Language: ja\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: sadf_misc.c:596
#, c-format
msgid "System activity data file: %s (%#x)\n"
msgstr "システム動作情報ファイル: %s (%#x)\n"

#: sadf_misc.c:605
#, c-format
msgid "Host: "
msgstr "ホスト名: "

#: sadf_misc.c:611
#, c-format
msgid "Size of a long int: %d\n"
msgstr "long int のサイズ: %d\n"

#: sadf_misc.c:613
#, c-format
msgid "List of activities:\n"
msgstr "動作情報のリスト:\n"

#: sadf_misc.c:626
#, c-format
msgid "\t[Unknown activity format]"
msgstr "\t[不明な動作記録フォーマット]"

#: iostat.c:85 cifsiostat.c:70 mpstat.c:89 pidstat.c:82 sar.c:94
#: nfsiostat.c:69
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ]\n"
msgstr "使い方: %s [ オプション ] [ <間隔> [ <回数> ] ]\n"

#: iostat.c:88
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ] [ --debuginfo ]\n"
msgstr ""
"利用可能なオプション:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <グループ名> ] [ -p [ <デバイス> [,...] | ALL ] ]\n"
"[ <デバイス> [...] | ALL ] [ --debuginfo ]\n"

#: iostat.c:94
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ]\n"
msgstr ""
"利用可能なオプション:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -T ] -g <グループ名> ] [ -p [ <デバイス名> [,...] | ALL ] ]\n"
"[ <デバイス名> [...] | ALL ]\n"

#: iostat.c:329
#, c-format
msgid "Cannot find disk data\n"
msgstr "ディスクデータが見つかりません\n"

#: iostat.c:1392 sa_common.c:1300
#, c-format
msgid "Invalid type of persistent device name\n"
msgstr "永続的なデバイス名が不正です\n"

#: sadc.c:84
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
msgstr "使い方: %s [ オプション ] [ <間隔> [ <回数> ] ] [ <出力ファイル> ]\n"

#: sadc.c:87
#, c-format
msgid ""
"Options are:\n"
"[ -C <comment> ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"
msgstr ""
"利用可能なオプション:\n"
"[ -C <コメント> ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"

#: sadc.c:250
#, c-format
msgid "Cannot write data to system activity file: %s\n"
msgstr "システム動作情報ファイルに書き込みができません: %s\n"

#: sadc.c:537
#, c-format
msgid "Cannot write system activity file header: %s\n"
msgstr "システム動作情報ファイルのヘッダに書き込みができません: %s\n"

#: sadc.c:650 sadc.c:659 sadc.c:720 ioconf.c:491 rd_stats.c:68 rd_stats.c:2124
#: sa_common.c:1109
#, c-format
msgid "Cannot open %s: %s\n"
msgstr "%s を開けません: %s\n"

#: sadc.c:842
#, c-format
msgid "Cannot append data to that file (%s)\n"
msgstr "ファイル (%s) にデータを追加できません\n"

#: common.c:62
#, c-format
msgid "sysstat version %s\n"
msgstr "sysstat バージョン %s\n"

#: cifsiostat.c:74 nfsiostat.c:73
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"
msgstr ""
"利用可能なオプション:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"

#: cifsiostat.c:77 nfsiostat.c:76
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
msgstr ""
"利用可能なオプション:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#: sadf.c:86
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <datafile> ]\n"
msgstr "使い方: %s [ オプション ] [ <間隔> [ <回数> ] ] [ <データファイル名> ]\n"

#: sadf.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -C ] [ -d | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -P { <cpu> [,...] | ALL } ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"
"[ -- <sar_options> ]\n"
msgstr ""
"利用可能なオプション:\n"
"[ -C ] [ -d | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -P { <cpu> [,...] | ALL } ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"
"[ -- <sar のオプション> ]\n"

#: mpstat.c:92
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -P { <cpu> [,...] | ON | ALL } ]\n"
msgstr ""
"利用可能なオプション:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -P { <cpu> [,...] | ON | ALL } ]\n"

# sar.c:
#: mpstat.c:608 pidstat.c:1856 sar.c:401
msgid "Average:"
msgstr "平均値:"

#: mpstat.c:984
#, c-format
msgid "Not that many processors!\n"
msgstr "そんなに CPU はありません!\n"

#: rd_stats.c:2170
#, c-format
msgid "Cannot handle so many processors!\n"
msgstr "そんなに大量のプロセッサは扱えません!\n"

#: pr_stats.c:2348 pr_stats.c:2361
msgid "Summary"
msgstr "サマリ"

#: pr_stats.c:2399
msgid "Other devices not listed here"
msgstr "ここに挙げられていない他のデバイス"

#: sa_common.c:917
#, c-format
msgid "Error while reading system activity file: %s\n"
msgstr "システム動作情報ファイルの読み込み中にエラーが起きました: %s\n"

#: sa_common.c:927
#, c-format
msgid "End of system activity file unexpected\n"
msgstr "システム動作情報ファイルが途中で予期無く終了しました\n"

#: sa_common.c:946
#, c-format
msgid "File created by sar/sadc from sysstat version %d.%d.%d"
msgstr "sysstat (バージョン %d.%d.%d) の sar/sadc によって作られたファイル"

#: sa_common.c:977
#, c-format
msgid "Invalid system activity file: %s\n"
msgstr "システム動作情報ファイルの形式が正しくありません: %s\n"

#: sa_common.c:984
#, c-format
msgid "Current sysstat version can no longer read the format of this file (%#x)\n"
msgstr "sysstat の現在のバージョンではこのデータファイル (%#x) の形式を読み込めなくなっています\n"

#: sa_common.c:1216
#, c-format
msgid "Requested activities not available in file %s\n"
msgstr "要求された動作情報はファイル %s 内にはありません\n"

#: pidstat.c:85
#, c-format
msgid ""
"Options are:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -r ] [ -s ] [ -t ] [ -U [ username ] ] [ -u ]\n"
"[ -V ] [ -w ] [ -C <command> ] [ -p { <pid> [,...] | SELF | ALL } ]\n"
"[ -T { TASK | CHILD | ALL } ]\n"
msgstr ""
"利用可能なオプション:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -r ] [ -s ] [ -t ] [ -U [ ユーザー名 ] ] [ -u ]\n"
"[ -V ] [ -w ] [ -C <コマンド> ] [ -p { <pid> [,...] | SELF | ALL } ]\n"
"[ -T { TASK | CHILD | ALL } ]\n"

#: pidstat.c:215 sar.c:1033
#, c-format
msgid "Requested activities not available\n"
msgstr "要求された動作情報は利用できません\n"

#: sar.c:109
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -d ] [ -H ] [ -h ] [ -p ] [ -q ] [ -R ]\n"
"[ -r ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ] [ -v ] [ -W ] [ -w ] [ -y ]\n"
"[ -I { <int> [,...] | SUM | ALL | XALL } ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -m { <keyword> [,...] | ALL } ] [ -n { <keyword> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filename> ] | -o [ <filename> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"
msgstr ""
"利用可能なオプション:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -d ] [ -H ] [ -h ] [ -p ] [ -q ] [ -R ]\n"
"[ -r ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ] [ -v ] [ -W ] [ -w ] [ -y ]\n"
"[ -I { <int> [,...] | SUM | ALL | XALL } ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -m { <キーワード> [,...] | ALL } ] [ -n { <キーワード> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <ファイル名> ] | -o [ <ファイル名> ] | -[0-9]+ ]\n"
"[ -i <間隔> ] [ -s [ <hh:mm:ss> ] ] [ -e [ <hh:mm:ss> ] ]\n"

#: sar.c:131
#, c-format
msgid "Main options and reports:\n"
msgstr "主要なオプションとその結果:\n"

#: sar.c:132
#, c-format
msgid "\t-b\tI/O and transfer rate statistics\n"
msgstr "\t-b\tI/O と転送率の状況\n"

#: sar.c:133
#, c-format
msgid "\t-B\tPaging statistics\n"
msgstr "\t-B\tページングの状態\n"

#: sar.c:134
#, c-format
msgid "\t-d\tBlock device statistics\n"
msgstr "\t-d\tブロックデバイスの状態\n"

#: sar.c:135
#, c-format
msgid "\t-H\tHugepages utilization statistics\n"
msgstr "\t-H\tHugepage の利用状況\n"

#: sar.c:136
#, c-format
msgid ""
"\t-I { <int> | SUM | ALL | XALL }\n"
"\t\tInterrupts statistics\n"
msgstr ""
"\t-I { <int> | SUM | ALL | XALL }\n"
"\t\t割り込み状況\n"

#: sar.c:138
#, c-format
msgid ""
"\t-m { <keyword> [,...] | ALL }\n"
"\t\tPower management statistics\n"
"\t\tKeywords are:\n"
"\t\tCPU\tCPU instantaneous clock frequency\n"
"\t\tFAN\tFans speed\n"
"\t\tFREQ\tCPU average clock frequency\n"
"\t\tIN\tVoltage inputs\n"
"\t\tTEMP\tDevices temperature\n"
"\t\tUSB\tUSB devices plugged into the system\n"
msgstr ""
"\t-m { <キーワード> [,...] | ALL }\n"
"\t\t電源管理状態の\n"
"\t\tキーワード:\n"
"\t\tCPU\tCPU 周波数\n"
"\t\tFAN\tファン回転数\n"
"\t\tFREQ\tCPU の平均周波数\n"
"\t\tIN\t入力電圧\n"
"\t\tTEMP\tデバイス温度\n"
"\t\tUSB\tシステムに挿入されている USB デバイス\n"

#: sar.c:147
#, c-format
msgid ""
"\t-n { <keyword> [,...] | ALL }\n"
"\t\tNetwork statistics\n"
"\t\tKeywords are:\n"
"\t\tDEV\tNetwork interfaces\n"
"\t\tEDEV\tNetwork interfaces (errors)\n"
"\t\tNFS\tNFS client\n"
"\t\tNFSD\tNFS server\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP traffic\t(v4)\n"
"\t\tEIP\tIP traffic\t(v4) (errors)\n"
"\t\tICMP\tICMP traffic\t(v4)\n"
"\t\tEICMP\tICMP traffic\t(v4) (errors)\n"
"\t\tTCP\tTCP traffic\t(v4)\n"
"\t\tETCP\tTCP traffic\t(v4) (errors)\n"
"\t\tUDP\tUDP traffic\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP traffic\t(v6)\n"
"\t\tEIP6\tIP traffic\t(v6) (errors)\n"
"\t\tICMP6\tICMP traffic\t(v6)\n"
"\t\tEICMP6\tICMP traffic\t(v6) (errors)\n"
"\t\tUDP6\tUDP traffic\t(v6)\n"
msgstr ""
"\t-n { <キーワード> [,...] | ALL }\n"
"\t\tネットワークの状態\n"
"\t\tキーワード一覧:\n"
"\t\tDEV\tネットワークインターフェイス\n"
"\t\tEDEV\tネットワークインターフェイス (エラー)\n"
"\t\tNFS\tNFS クライアント\n"
"\t\tNFSD\tNFS サーバ\n"
"\t\tSOCK\tソケット\t(v4)\n"
"\t\tIP\tIP トラフィック\t(v4)\n"
"\t\tEIP\tIP トラフィック\t(v4) (エラー)\n"
"\t\tICMP\tICMP トラフィック\t(v4)\n"
"\t\tEICMP\tICMP トラフィック\t(v4) (エラー)\n"
"\t\tTCP\tTCP トラフィック\t(v4)\n"
"\t\tETCP\tTCP トラフィック\t(v4) (エラー)\n"
"\t\tUDP\tUDP トラフィック\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP トラフィック\t(v6)\n"
"\t\tEIP6\tIP トラフィック\t(v6) (エラー)\n"
"\t\tICMP6\tICMP トラフィック\t(v6)\n"
"\t\tEICMP6\tICMP トラフィック\t(v6) (エラー)\n"
"\t\tUDP6\tUDP トラフィック\t(v6)\n"

#: sar.c:168
#, c-format
msgid "\t-q\tQueue length and load average statistics\n"
msgstr "\t-q\tQキューの長さとロードアベレージの状態\n"

#: sar.c:169
#, c-format
msgid "\t-r\tMemory utilization statistics\n"
msgstr "\t-r\tメモリ利用率の状態\n"

#: sar.c:170
#, c-format
msgid "\t-R\tMemory statistics\n"
msgstr "\t-R\tメモリの状況\n"

#: sar.c:171
#, c-format
msgid "\t-S\tSwap space utilization statistics\n"
msgstr "\t-S\tスワップ領域の利用状況\n"

#: sar.c:172
#, c-format
msgid ""
"\t-u [ ALL ]\n"
"\t\tCPU utilization statistics\n"
msgstr ""
"\t-u [ ALL ]\n"
"\t\tCPU 利用の利用状況\n"

#: sar.c:174
#, c-format
msgid "\t-v\tKernel table statistics\n"
msgstr "\t-v\tカーネルのテーブル状態\n"

#: sar.c:175
#, c-format
msgid "\t-w\tTask creation and system switching statistics\n"
msgstr "\t-w\tタスクの作成とシステムスイッチの状態\n"

# , c-format
#: sar.c:176
#, c-format
msgid "\t-W\tSwapping statistics\n"
msgstr "\t-W\tスワップの状態\n"

# , c-format
#: sar.c:177
#, c-format
msgid "\t-y\tTTY device statistics\n"
msgstr "\t-y\tTTY デバイスの状態\n"

#: sar.c:235
#, c-format
msgid "End of data collecting unexpected\n"
msgstr "データの収集が予期無く終了しました\n"

#: sar.c:822
#, c-format
msgid "Invalid data format\n"
msgstr "データ形式が正しくありません\n"

#: sar.c:826
#, c-format
msgid "Using a wrong data collector from a different sysstat version\n"
msgstr "異なるバージョンの sysstat のデータ収集プログラムによる不正なデータを使っています\n"

#: sar.c:850
#, c-format
msgid "Inconsistent input data\n"
msgstr "矛盾した入力データです\n"

#: sar.c:1303
#, c-format
msgid "-f and -o options are mutually exclusive\n"
msgstr "-f と -o オプションは相互に排他的です\n"

#: sar.c:1309
#, c-format
msgid "Not reading from a system activity file (use -f option)\n"
msgstr "システム動作情報ファイルの読み込みがありません (-f オプションを使ってください)\n"

#: sar.c:1441
#, c-format
msgid "Cannot find the data collector (%s)\n"
msgstr "データ収集プログラム (%s) が見つかりません\n"
