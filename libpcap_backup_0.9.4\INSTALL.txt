@(#) $Header: /tcpdump/master/libpcap/INSTALL.txt,v ******** 2005/06/20 21:30:14 guy Exp $ (LBL)

To build libpcap, run "./configure" (a shell script). The configure
script will determine your system attributes and generate an
appropriate Makefile from Makefile.in. Next run "make". If everything
goes well you can su to root and run "make install". However, you need
not install libpcap if you just want to build tcpdump; just make sure
the tcpdump and libpcap directory trees have the same parent
directory.

If configure says:

    configure: warning: cannot determine packet capture interface
    configure: warning: (see INSTALL for more info)

then your system either does not support packet capture or your system
does support packet capture but libpcap does not support that
particular type. (If you have HP-UX, see below.) If your system uses a
packet capture not supported by libpcap, please send us patches; don't
forget to include an autoconf fragment suitable for use in
configure.in.

It is possible to override the default packet capture type, although
the circumstance where this works are limited. For example if you have
installed bpf under SunOS 4 and wish to build a snit libpcap:

    ./configure --with-pcap=snit

Another example is to force a supported packet capture type in the case
where the configure scripts fails to detect it.

You will need an ANSI C compiler to build libpcap. The configure script
will abort if your compiler is not ANSI compliant. If this happens, use
the GNU C compiler, available via anonymous ftp:

	ftp://ftp.gnu.org/pub/gnu/gcc/

If you use flex, you must use version 2.4.6 or higher. The configure
script automatically detects the version of flex and will not use it
unless it is new enough. You can use "flex -V" to see what version you
have (unless it's really old). The current version of flex is available
via anonymous ftp:

	ftp://ftp.ee.lbl.gov/flex-*.tar.Z

As of this writing, the current version is 2.5.4.

If you use bison, you must use flex (and visa versa). The configure
script automatically falls back to lex and yacc if both flex and bison
are not found.

Sometimes the stock C compiler does not interact well with flex and
bison. The list of problems includes undefined references for alloca.
You can get around this by installing gcc or manually disabling flex
and bison with:

    ./configure --without-flex --without-bison

If your system only has AT&T lex, this is okay unless your libpcap
program uses other lex/yacc generated code. (Although it's possible to
map the yy* identifiers with a script, we use flex and bison so we
don't feel this is necessary.)

Some systems support the Berkeley Packet Filter natively; for example
out of the box OSF and BSD/OS have bpf. If your system does not support
bpf, you will need to pick up:

	ftp://ftp.ee.lbl.gov/bpf-*.tar.Z

Note well: you MUST have kernel source for your operating system in
order to install bpf. An exception is SunOS 4; the bpf distribution
includes replacement kernel objects for some of the standard SunOS 4
network device drivers. See the bpf INSTALL document for more
information.

If you use Solaris, there is a bug with bufmod(7) that is fixed in
Solaris 2.3.2 (aka SunOS 5.3.2). Setting a snapshot length with the
broken bufmod(7) results in data be truncated from the FRONT of the
packet instead of the end.  The work around is to not set a snapshot
length but this results in performance problems since the entire packet
is copied to user space. If you must run an older version of Solaris,
there is a patch available from Sun; ask for bugid 1149065. After
installing the patch, use "setenv BUFMOD_FIXED" to enable use of
bufmod(7). However, we recommend you run a more current release of
Solaris.

If you use the SPARCompiler, you must be careful to not use the
/usr/ucb/cc interface. If you do, you will get bogus warnings and
perhaps errors. Either make sure your path has /opt/SUNWspro/bin
before /usr/ucb or else:

    setenv CC /opt/SUNWspro/bin/cc

before running configure. (You might have to do a "make distclean"
if you already ran configure once).

Also note that "make depend" won't work; while all of the known
universe uses -M, the SPARCompiler uses -xM to generate makefile
dependencies.

If you are trying to do packet capture with a FORE ATM card, you may or
may not be able to. They usually only release their driver in object
code so unless their driver supports packet capture, there's not much
libpcap can do.

If you get an error like:

    tcpdump: recv_ack: bind error 0x???

when using DLPI, look for the DL_ERROR_ACK error return values, usually
in /usr/include/sys/dlpi.h, and find the corresponding value.

Under {DEC OSF/1, Digital UNIX, Tru64 UNIX}, packet capture must be
enabled before it can be used.  For instructions on how to enable packet
filter support, see:

	ftp://ftp.digital.com/pub/Digital/dec-faq/Digital-UNIX

Look for the "How do I configure the Berkeley Packet Filter and capture
tcpdump traces?" item.

Once you enable packet filter support, your OSF system will support bpf
natively.

Under Ultrix, packet capture must be enabled before it can be used. For
instructions on how to enable packet filter support, see:

	ftp://ftp.digital.com/pub/Digital/dec-faq/ultrix

If you use HP-UX, you must have at least version 9 and either the
version of cc that supports ANSI C (cc -Aa) or else use the GNU C
compiler. You must also buy the optional streams package. If you don't
have:

    /usr/include/sys/dlpi.h
    /usr/include/sys/dlpi_ext.h

then you don't have the streams package. In addition, we believe you
need to install the "9.X LAN and DLPI drivers cumulative" patch
(PHNE_6855) to make the version 9 DLPI work with libpcap.

The DLPI streams package is standard starting with HP-UX 10.

The HP implementation of DLPI is a little bit eccentric. Unlike
Solaris, you must attach /dev/dlpi instead of the specific /dev/*
network pseudo device entry in order to capture packets. The PPA is
based on the ifnet "index" number. Under HP-UX 9, it is necessary to
read /dev/kmem and the kernel symbol file (/hp-ux). Under HP-UX 10,
DLPI can provide information for determining the PPA. It does not seem
to be possible to trace the loopback interface. Unlike other DLPI
implementations, PHYS implies MULTI and SAP and you get an error if you
try to enable more than one promiscuous mode at a time.

It is impossible to capture outbound packets on HP-UX 9.  To do so on
HP-UX 10, you will, apparently, need a late "LAN products cumulative
patch" (at one point, it was claimed that this would be PHNE_18173 for
s700/10.20; at another point, it was claimed that the required patches
were PHNE_20892, PHNE_20725 and PHCO_10947, or newer patches), and to do
so on HP-UX 11 you will, apparently, need the latest lancommon/DLPI
patches and the latest driver patch for the interface(s) in use on HP-UX
11 (at one point, it was claimed that patches PHNE_19766, PHNE_19826,
PHNE_20008, and PHNE_20735 did the trick).

Furthermore, on HP-UX 10, you will need to turn on a kernel switch by
doing

	echo 'lanc_outbound_promisc_flag/W 1' | adb -w /stand/vmunix /dev/mem

You would have to arrange that this happen on reboots; the right way to
do that would probably be to put it into an executable script file
"/sbin/init.d/outbound_promisc" and making
"/sbin/rc2.d/S350outbound_promisc" a symbolic link to that script.

Finally, testing shows that there can't be more than one simultaneous
DLPI user per network interface.

If you use Linux, this version of libpcap is known to compile and run
under Red Hat 4.0 with the 2.0.25 kernel.  It may work with earlier 2.X
versions but is guaranteed not to work with 1.X kernels.  Running more
than one libpcap program at a time, on a system with a 2.0.X kernel, can
cause problems since promiscuous mode is implemented by twiddling the
interface flags from the libpcap application; the packet capture
mechanism in the 2.2 and later kernels doesn't have this problem.  Also,
packet timestamps aren't very good.  This appears to be due to haphazard
handling of the timestamp in the kernel.

Note well: there is rumoured to be a version of tcpdump floating around
called 3.0.3 that includes libpcap and is supposed to support Linux. 
You should be advised that neither the Network Research Group at LBNL
nor the Tcpdump Group ever generated a release with this version number. 
The LBNL Network Research Group notes with interest that a standard
cracker trick to get people to install trojans is to distribute bogus
packages that have a version number higher than the current release. 
They also noted with annoyance that 90% of the Linux related bug reports
they got are due to changes made to unofficial versions of their page. 
If you are having trouble but aren't using a version that came from
tcpdump.org, please try that before submitting a bug report!

On Linux, libpcap will not work if the kernel does not have the packet
socket option enabled; see the README.linux file for information about
this.

If you use AIX, you may not be able to build libpcap from this release.
We do not have an AIX system in house so it's impossible for us to test
AIX patches submitted to us.  We are told that you must link against
/lib/pse.exp, that you must use AIX cc or a GNU C compiler newer than
2.7.2, and that you may need to run strload before running a libpcap
application.

Read the README.aix file for information on installing libpcap and
configuring your system to be able to support libpcap.

If you use NeXTSTEP, you will not be able to build libpcap from this
release. We hope to support this operating system in some future
release of libpcap.

If you use SINIX, you should be able to build libpcap from this
release. It is known to compile and run on SINIX-Y/N 5.42 with the C-DS
V1.0 or V1.1 compiler. But note that in some releases of SINIX, yacc
emits incorrect code; if grammar.y fails to compile, change every
occurence of:

	#ifdef YYDEBUG

to:
	#if YYDEBUG

Another workaround is to use flex and bison.

If you use SCO, you might have trouble building libpcap from this
release. We do not have a machine running SCO and have not had reports
of anyone successfully building on it. Since SCO apparently supports
DLPI, it's possible the current version works. Meanwhile, SCO provides
a tcpdump binary as part of their "Network/Security Tools" package:

    http://www.sco.com/technology/internet/goodies/#SECURITY

There is also a README that explains how to enable packet capture.

If you use UnixWare, you will not be able to build libpcap from this
release. We hope to support this operating system in some future
release of libpcap. Meanwhile, there appears to be an UnixWare port of
libpcap 0.0 (and tcpdump 3.0) in:

    ftp://ftp1.freebird.org/pub/mirror/freebird/internet/systools/

UnixWare appears to use a hacked version of DLPI.

If linking tcpdump fails with "Undefined: _alloca" when using bison on
a Sun4, your version of bison is broken. In any case version 1.16 or
higher is recommended (1.14 is known to cause problems 1.16 is known to
work). Either pick up a current version from:

	ftp://ftp.gnu.org/pub/gnu/bison

or hack around it by inserting the lines:

	#ifdef __GNUC__
	#define alloca __builtin_alloca
	#else
	#ifdef sparc
	#include <alloca.h>
	#else
	char *alloca ();
	#endif
	#endif

right after the (100 line!) GNU license comment in bison.simple, remove
grammar.[co] and fire up make again.

If you use SunOS 4, your kernel must support streams NIT. If you run a
libpcap program and it dies with:

    /dev/nit: No such device

You must add streams NIT support to your kernel configuration, run
config and boot the new kernel.

If you are running a version of SunOS earlier than 4.1, you will need
to replace the Sun supplied /sys/sun{3,4,4c}/OBJ/nit_if.o with the
appropriate version from this distribution's SUNOS4 subdirectory and
build a new kernel:

	nit_if.o.sun3-sunos4		(any flavor of sun3)
	nit_if.o.sun4c-sunos4.0.3c	(SS1, SS1+, IPC, SLC, etc.)
	nit_if.o.sun4-sunos4		(Sun4's not covered by
					    nit_if.o.sun4c-sunos4.0.3c)

These nit replacements fix a bug that makes nit essentially unusable in
pre-SunOS 4.1.  In addition, our sun4c-sunos4.0.3c nit gives you
timestamps to the resolution of the SS-1 clock (1 us) rather than the
lousy 20ms timestamps Sun gives you  (tcpdump will print out the full
timestamp resolution if it finds it's running on a SS-1).

FILES
-----
CHANGES		- description of differences between releases
ChmodBPF/*	- Mac OS X startup item to set ownership and permissions
		  on /dev/bpf*
CREDITS		- people that have helped libpcap along
FILES		- list of files exported as part of the distribution
INSTALL.txt	- this file
LICENSE		- the license under which tcpdump is distributed
Makefile.in	- compilation rules (input to the configure script)
README		- description of distribution
README.aix	- notes on using libpcap on AIX
README.dag	- notes on using libpcap to capture on Endace DAG devices
README.hpux	- notes on using libpcap on HP-UX
README.linux	- notes on using libpcap on Linux
README.macosx	- notes on using libpcap on Mac OS X
README.septel   - notes on using libpcap to capture on Intel/Septel devices
README.tru64	- notes on using libpcap on Digital/Tru64 UNIX
README.Win32	- notes on using libpcap on Win32 systems (with WinPcap)
SUNOS4		- pre-SunOS 4.1 replacement kernel nit modules
VERSION		- version of this release
acconfig.h	- support for post-2.13 autoconf
aclocal.m4	- autoconf macros
arcnet.h	- ARCNET definitions
atmuni31.h	- ATM Q.2931 definitions
bpf/net		- copy of bpf_filter.c
bpf_dump.c	- BPF program printing routines
bpf_filter.c	- symlink to bpf/net/bpf_filter.c
bpf_image.c	- BPF disassembly routine
config.guess	- autoconf support
config.h.in	- autoconf input
config.sub	- autoconf support
configure	- configure script (run this first)
configure.in	- configure script source
etherent.c	- /etc/ethers support routines
ethertype.h	- Ethernet protocol types and names definitions
fad-getad.c	- pcap_findalldevs() for systems with getifaddrs()
fad-gifc.c	- pcap_findalldevs() for systems with only SIOCGIFLIST
fad-glifc.c	- pcap_findalldevs() for systems with SIOCGLIFCONF
fad-null.c	- pcap_findalldevs() for systems without capture support
fad-win32.c	- pcap_findalldevs() for WinPcap
gencode.c	- BPF code generation routines
gencode.h	- BPF code generation definitions
grammar.y	- filter string grammar
inet.c		- network routines
install-sh	- BSD style install script
lbl/os-*.h	- OS-dependent defines and prototypes
llc.h		- 802.2 LLC SAP definitions
missing/*	- replacements for missing library functions
mkdep		- construct Makefile dependency list
msdos/*		- drivers for MS-DOS capture support
nametoaddr.c	- hostname to address routines
nlpid.h		- OSI network layer protocol identifier definitions
net		- symlink to bpf/net
optimize.c	- BPF optimization routines
packaging	- packaging information for building libpcap RPMs
pcap-bpf.c	- BSD Packet Filter support
pcap-bpf.h	- BPF definitions
pcap-dag.c	- Endace DAG device capture support
pcap-dag.h	- Endace DAG device capture support
pcap-dlpi.c	- Data Link Provider Interface support
pcap-dos.c	- MS-DOS capture support
pcap-dos.h	- headers for MS-DOS capture support
pcap-enet.c	- enet support
pcap-int.h	- internal libpcap definitions
pcap-linux.c	- Linux packet socket support
pcap-namedb.h	- public libpcap name database definitions
pcap-nit.c	- SunOS Network Interface Tap support
pcap-nit.h	- SunOS Network Interface Tap definitions
pcap-null.c	- dummy monitor support (allows offline use of libpcap)
pcap-pf.c	- Ultrix and Digital/Tru64 UNIX Packet Filter support
pcap-pf.h	- Ultrix and Digital/Tru64 UNIX Packet Filter definitions
pcap-septel.c   - INTEL/Septel device capture support
pcap-septel.h   - INTEL/Septel device capture support
pcap-stdinc.h	- includes and #defines for compiling on Win32 systems
pcap-snit.c	- SunOS 4.x STREAMS-based Network Interface Tap support
pcap-snoop.c	- IRIX Snoop network monitoring support
pcap-win32.c	- WinPcap capture support
pcap.3		- manual entry
pcap.c		- pcap utility routines
pcap.h		- public libpcap definitions
pf.h		- OpenBSD DLT_PFLOG definitions
ppp.h		- Point to Point Protocol definitions
rawss7.h	- information on DLT_ types for SS7
savefile.c	- offline support
scanner.l	- filter string scanner
sll.h		- definitions for Linux cooked mode fake link-layer header
sunatmpos.h	- definitions for SunATM capturing
Win32		- headers and routines for building on Win32 systems
