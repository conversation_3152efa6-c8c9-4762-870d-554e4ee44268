.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_DUMP 3PCAP "8 March 2015"
.SH NAME
pcap_dump \- write a packet to a capture file
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
void pcap_dump(u_char *user, struct pcap_pkthdr *h,
    u_char *sp);
.ft
.fi
.SH DESCRIPTION
.BR pcap_dump ()
outputs a packet to the ``savefile'' opened with
.BR pcap_dump_open (3PCAP).
Note that its calling arguments are suitable for use with
.BR pcap_dispatch (3PCAP)
or
.BR pcap_loop (3PCAP).
If called directly, the
.I user
parameter is of type
.B pcap_dumper_t
as returned by
.BR pcap_dump_open ().
.SH SEE ALSO
.BR pcap (3PCAP)
