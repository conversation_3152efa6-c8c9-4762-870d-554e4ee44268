#!/bin/bash

# MariaDB 日志连接数统计脚本
# 用途: 分析 MariaDB general log 文件，统计每秒的连接数
# 作者: Augment Agent
# 日期: 2025-07-19

# 默认日志文件路径
DEFAULT_LOG_FILE="/tmp/mariadb-general.log"

# 显示使用帮助
show_help() {
    echo "用法: $0 [选项] [日志文件路径]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -s, --summary       只显示统计摘要"
    echo "  -d, --detail        显示详细的每秒连接数"
    echo "  -t, --top N         显示连接数最高的前N秒 (默认10)"
    echo "  -f, --file FILE     指定日志文件路径"
    echo "  -o, --output FILE   将结果输出到文件"
    echo ""
    echo "示例:"
    echo "  $0                                    # 分析默认日志文件"
    echo "  $0 -f /var/log/mysql/general.log     # 分析指定日志文件"
    echo "  $0 -s                                 # 只显示摘要"
    echo "  $0 -t 10                              # 显示连接数最高的前10秒"
    echo "  $0 -d -o result.txt                  # 详细结果输出到文件"
    echo ""
}

# 分析日志文件的函数
analyze_connections() {
    local log_file="$1"
    local show_detail="$2"
    local output_file="$3"
    local top_n="$4"
    
    if [ ! -f "$log_file" ]; then
        echo "错误: 日志文件 '$log_file' 不存在!" >&2
        exit 1
    fi

    if [ ! -r "$log_file" ]; then
        echo "错误: 无法读取日志文件 '$log_file'!" >&2
        exit 1
    fi
    
    # 使用 awk 分析日志
    local awk_script='
    {
        # 检查是否有时间戳行 (YYMMDD HH:MM:SS)
        if (match($0, /^[0-9]{6} [0-9]{2}:[0-9]{2}:[0-9]{2}/)) {
            current_timestamp = substr($0, RSTART, RLENGTH)
        }

        # 检查是否是 Connect 行
        if (/Connect/) {
            if (current_timestamp != "") {
                connections[current_timestamp]++
                total++

                # 提取小时和分钟用于统计
                if (match(current_timestamp, /[0-9]{2}:[0-9]{2}/)) {
                    hour_min = substr(current_timestamp, RSTART, RLENGTH)
                    hourly[hour_min]++
                }
            }
        }
    }
    END {
        print "=== MariaDB 连接统计报告 ==="
        print "分析文件: " FILENAME
        print "分析时间: " strftime("%Y-%m-%d %H:%M:%S")
        print ""
        
        if (total == 0) {
            print "未发现任何连接记录!"
            exit 0
        }
        
        print "总连接数: " total
        print "统计时间段: " length(connections) " 秒"
        
        if (length(connections) > 0) {
            avg = total / length(connections)
            printf "平均每秒连接数: %.2f\n", avg
        }
        
        # 计算最大和最小连接数
        max_conn = 0
        min_conn = 999999
        for (time in connections) {
            if (connections[time] > max_conn) max_conn = connections[time]
            if (connections[time] < min_conn) min_conn = connections[time]
        }
        print "最大每秒连接数: " max_conn
        print "最小每秒连接数: " min_conn
        
        if (top_n > 0) {
            print ""
            print "=== 连接数最高的前" top_n "秒 ==="

            # 创建数组用于排序
            n = 0
            for (time in connections) {
                times[n] = time
                counts[n] = connections[time]
                n++
            }

            # 简单冒泡排序（按连接数降序）
            for (i = 0; i < n-1; i++) {
                for (j = 0; j < n-1-i; j++) {
                    if (counts[j] < counts[j+1]) {
                        # 交换连接数
                        temp_count = counts[j]
                        counts[j] = counts[j+1]
                        counts[j+1] = temp_count
                        # 交换时间
                        temp_time = times[j]
                        times[j] = times[j+1]
                        times[j+1] = temp_time
                    }
                }
            }

            # 输出前top_n个
            limit = (n < top_n) ? n : top_n
            for (i = 0; i < limit; i++) {
                printf "%d. %s: %d 连接\n", i+1, times[i], counts[i]
            }
        } else {
            print ""
            print "=== 每秒连接数统计 ==="
            PROCINFO["sorted_in"] = "@ind_str_asc"
            for (time in connections) {
                printf "%s: %d 连接\n", time, connections[time]
            }
        }

        if (show_detail == "1") {
            print ""
            print "=== 每分钟连接数汇总 ==="
            PROCINFO["sorted_in"] = "@ind_str_asc"
            for (hm in hourly) {
                printf "%s: %d 连接\n", hm, hourly[hm]
            }
        }
    }'
    
    # 执行分析
    if [ -n "$output_file" ]; then
        awk -v show_detail="$show_detail" -v top_n="$top_n" -v FILENAME="$log_file" "$awk_script" "$log_file" > "$output_file"
        echo "结果已保存到: $output_file"
    else
        awk -v show_detail="$show_detail" -v top_n="$top_n" -v FILENAME="$log_file" "$awk_script" "$log_file"
    fi
}

# 主程序
main() {
    local log_file="$DEFAULT_LOG_FILE"
    local show_detail="0"
    local summary_only="0"
    local output_file=""
    local top_n="0"
    
    # 解析命令行参数
    while [ $# -gt 0 ]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -s|--summary)
                summary_only="1"
                shift
                ;;
            -d|--detail)
                show_detail="1"
                shift
                ;;
            -t|--top)
                if [ -n "$2" ] && [ "$2" -gt 0 ] 2>/dev/null; then
                    top_n="$2"
                    shift 2
                else
                    top_n="10"
                    shift
                fi
                ;;
            -f|--file)
                if [ -n "$2" ]; then
                    log_file="$2"
                    shift 2
                else
                    echo "错误: -f 选项需要指定文件路径" >&2
                    exit 1
                fi
                ;;
            -o|--output)
                if [ -n "$2" ]; then
                    output_file="$2"
                    shift 2
                else
                    echo "错误: -o 选项需要指定输出文件路径" >&2
                    exit 1
                fi
                ;;
            -*)
                echo "错误: 未知选项 $1" >&2
                show_help
                exit 1
                ;;
            *)
                # 如果没有 -f 选项，将参数作为日志文件路径
                log_file="$1"
                shift
                ;;
        esac
    done

    # 如果指定了 summary_only，则不显示详情
    if [ "$summary_only" = "1" ]; then
        show_detail="0"
    fi
    
    # 执行分析
    analyze_connections "$log_file" "$show_detail" "$output_file" "$top_n"
}

# 运行主程序
main "$@"
