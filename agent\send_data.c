#include <stdio.h>
#include <stdlib.h>
#include "send.h"
#include <string.h>
#include "top.h"
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <signal.h>
#include "err.h"
#include "readproc.h"
#include <fcntl.h>
#include <pthread.h>
#include "misc.h"
#include "meminfo.h"
#include <limits.h>
#include <errno.h>

extern int o2;
#include <sys/time.h>
// #include "../libpcap/pcap.h"
#include "../libpcap/pcap-int.h"

extern int sadc1();
extern struct sigaction alrm_act;
os_data *os; // first init hear
// 用来验证线程是否创建
volatile int thread_created = 0;

// 高效同步机制类型定义
typedef struct {
    pthread_mutex_t mutex;
    pthread_cond_t cond;
    int count;
    int waiting;
    int generation;
} efficient_barrier_t;

// 双门禁同步机制函数声明
extern int init_barriers();
extern int cleanup_barriers();
extern int efficient_barrier_wait(efficient_barrier_t *barrier);
extern int efficient_barrier_wait_timeout(efficient_barrier_t *barrier, int timeout_seconds);

// 双门禁同步机制变量声明
extern efficient_barrier_t barrier_a_done;   // 门禁1：A线程完成工作
extern efficient_barrier_t barrier_bc_done;  // 门禁2：BC线程完成工作

#include "pwcache.h"
#include "pids.h"
// #include "readproc.h"

#define container_of(ptr, type, member) \
        (type *)((char *)(ptr) - (char *)&((type *)0)->member)

// add perf
// #include "../builtin.h"

// add sar
#include "../sar/sa1.h"
// 注意：ioconf.h在Windows环境下有链接问题，我们使用函数声明代替
// #include "../sar/ioconf.h"
// add tcp1_lock
extern int tcp1_lock;
// add sar_lk
extern int sar_lk;

// add tcpdump
#include "../tcpdump-5.0.0/my.h"

// add 2024
#include "meminfo.h"
#include "pids.h"

extern int test;
extern int test2;
extern int test3;
extern struct pcap *pd;

// add for hsearch_r
#include <search.h>

// ioconf清理函数声明（避免Windows链接问题）
extern void cleanup_ioconf_cache(void);

// add 2024-11-4 thread pool
#include "thpool.h"
// 定义全局日志文件指针和互斥锁
FILE *g_logfile = NULL;
pthread_mutex_t log_mutex = PTHREAD_MUTEX_INITIALIZER;

// 定义top.h中声明的全局变量
int o = 0;  // active procsses
int o1 = 0; // active threads
int o3 = 0; // max global2
int o4 = 0; // max
int perf_count = 0;
int lock_on = 0;
sem_t sadc_sem;      // 用于控制tcp1和sadc1开始执行的信号量
sem_t tcp_sem;      // 用于控制tcp1和sadc1开始执行的信号量
sem_t completion_sem; // 用于等待tcp1和sadc1完成的信号量

// 定义其他top.h中的全局变量
struct stat_info *free_info = NULL;
struct graph_parms *Graph_cpus = NULL, *Graph_mems = NULL;
struct epoll_event evs[1];
struct epoll_event ev;
int epfd = -1;
int epoll_check = 0;
struct timespec start, end;

// 定义sa1.h中声明的全局变量
int sar_lock1 = 0;
int wait1 = 0;
int wait2 = 0;
int release1 = 0;
int enable_tasks_refresh = 0;  // 默认禁用tasks_refresh，当argv[2]为"deep"时启用深度监控
int deep_monitoring_mode = 0;  // 深度监控模式标志
extern struct sigaction int_act;
int sigint_caught = 0;
int tcp1_lock = 0;
pthread_mutex_t db_mutex = PTHREAD_MUTEX_INITIALIZER;
pthread_cond_t dblost = PTHREAD_COND_INITIALIZER;
pthread_mutex_t rdb = PTHREAD_MUTEX_INITIALIZER;
pthread_cond_t rcond = PTHREAD_COND_INITIALIZER;
pthread_mutex_t xxx = PTHREAD_MUTEX_INITIALIZER;
pthread_cond_t sar_rcond = PTHREAD_COND_INITIALIZER;
pthread_cond_t tc11 = PTHREAD_COND_INITIALIZER;
pthread_cond_t cc11 = PTHREAD_COND_INITIALIZER;
pthread_mutex_t tcpdump = PTHREAD_MUTEX_INITIALIZER;
pthread_cond_t tcp_d = PTHREAD_COND_INITIALIZER;

// 定义my.h中声明的全局变量
int max_ip = 0;
char **ip_str = NULL;
int ip_count = 0;
struct bpf_program fcode;
char *cmdbuf1 = NULL;
int Oflag1 = 0;
int netmask1 = 0;
int timeout1 = 0;
int c_tcp1_lock1 = 0;

// 定义my.h中更多的全局变量
time_t t1 = 0, t2 = 0;
int group = 0, group1 = 0, group2 = 0, group3 = 0, group4 = 0;
int group5 = 0, group6 = 0, group7 = 0, group8 = 0, group9 = 0;
int group0 = 0, groupa = 0;
int tcp_lock = 0;

// 定义readproc.h中声明的全局变量
struct utlbuf_s *free_str1 = NULL;

// 定义top.h中更多的全局变量
char *time1 = NULL;
SIC_t z1_frme = 0, z2_frme = 0, u_frme = 0, u_f = 0, s_frme = 0, n_frme = 0;
SIC_t i_frme = 0, w_frme = 0, x_frme = 0, y_frme = 0, z_frme = 0, tot_frme = 0;
SIC_t sum_user = 0, sum_sys = 0, tz = 0;
float scale = 0.0;

// 定义更多top.h中的全局变量
int g_tcp = 0;
int max_tcp_count = 0;
int aaa = 0;
long mem_TOT_global = 0;

// 定义meminfo.h中声明的全局变量
struct meminfo_info *free_meminfo = NULL;

// 定义pids.h中声明的全局变量
enum pids_item *Pids_itms = NULL;
struct pids_fetch *Pids_reap = NULL;
struct pids_fetch *Pids_reap1 = NULL;
struct pids_info *free_pidsinfo = NULL;

// 定义pwcache.h中声明的全局变量
struct pwbuf *pw7hash[HASHSIZE] = {NULL};

// 定义my.h中更多的全局变量
pthread_mutex_t tcp_sm = PTHREAD_MUTEX_INITIALIZER;
pthread_cond_t tcp_sc = PTHREAD_COND_INITIALIZER;
my_tcp *my_tcp1 = NULL;
my_tcp2 *my_tcp21 = NULL;
my_tcp3 *my_tcp31 = NULL;
my_tcp4 *my_tcp41 = NULL;
my_tcp5 *my_tcp51 = NULL;
my_tcp6 *my_tcp61 = NULL;
my_tcp7 *my_tcp71 = NULL;
my_tcp8 *my_tcp81 = NULL;
my_tcp9 *my_tcp91 = NULL;
my_tcp0 *my_tcp01 = NULL;
my_tcpa *my_tcpa1 = NULL;
my_tcpb *my_tcpb1 = NULL;

// 定义rwlock变量
pthread_rwlock_t tcp1_rwlock = PTHREAD_RWLOCK_INITIALIZER;
pthread_rwlock_t tcp2_rwlock = PTHREAD_RWLOCK_INITIALIZER;
pthread_rwlock_t tcp3_rwlock = PTHREAD_RWLOCK_INITIALIZER;
pthread_rwlock_t tcp4_rwlock = PTHREAD_RWLOCK_INITIALIZER;
pthread_rwlock_t tcp5_rwlock = PTHREAD_RWLOCK_INITIALIZER;
pthread_rwlock_t tcp6_rwlock = PTHREAD_RWLOCK_INITIALIZER;
pthread_rwlock_t tcp7_rwlock = PTHREAD_RWLOCK_INITIALIZER;
pthread_rwlock_t tcp8_rwlock = PTHREAD_RWLOCK_INITIALIZER;
pthread_rwlock_t tcp9_rwlock = PTHREAD_RWLOCK_INITIALIZER;
pthread_rwlock_t tcp0_rwlock = PTHREAD_RWLOCK_INITIALIZER;
pthread_rwlock_t tcpa_rwlock = PTHREAD_RWLOCK_INITIALIZER;
pthread_rwlock_t tcpb_rwlock = PTHREAD_RWLOCK_INITIALIZER;

// 定义其他变量
struct hsearch_data *htab = NULL;
my_tcp *list = NULL;

typedef struct Task
{
        void (*function)(void *arg);
        void *arg;
} Task;

struct ThreadPool
{
        // 任务队列
        Task *taskQ;
        int queueCapacity; // 容量
        int queueSize;     // 当前任务个数
        int queueFront;    // 队头 -> 取数据
        int queueRear;     // 队尾 -> 放数据

        pthread_t managerID;       // 管理者线程ID
        pthread_t *threadIDs;      // 工作的线程ID
        int minNum;                // 最小线程数量
        int maxNum;                // 最大线程数量
        int busyNum;               // 忙的线程的个数
        int liveNum;               // 存活的线程的个数
        int exitNum;               // 要销毁的线程个数
        pthread_mutex_t mutexPool; // 锁整个的线程池
        pthread_mutex_t mutexBusy; // 锁busyNum变量
        pthread_cond_t notFull;    // 任务队列是不是满了
        pthread_cond_t notEmpty;   // 任务队列是不是空了

        int shutdown; // 是不是要销毁线程池, 销毁为1, 不销毁为0
};
extern ThreadPool *pool;

// end thread pool

static void *alloc_r(void *ptr, size_t num)
{
        void *pv;

        if (!num)
                ++num;
        if (!(pv = realloc(ptr, num)))
                perror("alloc_r failed");
        ;
        return pv;
} // end: alloc_r

extern int sock;

void sigint_handler(int sig)
{
        //    write(1, "Caught Ctrl+C\n", 14);  // 打印信息
        write_log("ctrl-c caught ....\n");
        wait1 = 1;
}

/*
void ignore_sigpipe() {
    struct sigaction act;
    act.sa_handler = SIG_IGN;
    sigemptyset(&act.sa_mask);
    act.sa_flags = 0;
    if (sigaction(SIGPIPE, &act, 0) == -1) {
        perror("sigaction");
        exit(EXIT_FAILURE);
    }
}
*/

/*
struct utlbuf_s {
    char *buf;     // dynamically grown buffer
    int   siz;     // current len of the above
} utlbuf_s;
*/

#define PROCPATHLEN 64
static int file2str(const char *directory, const char *what, struct utlbuf_s *ub)
{
#define buffGRW 1024
        char path[PROCPATHLEN];
        int fd, num, tot_read = 0, len;

        /* on first use we preallocate a buffer of minimum size to emulate
           former 'local static' behavior -- even if this read fails, that
           buffer will likely soon be used for another subdirectory anyway
           ( besides, with the calloc call we will never need use memcpy ) */
        if (ub->buf)
                ub->buf[0] = '\0';
        else
        {
                ub->buf = calloc(1, (ub->siz = buffGRW));
                if (!ub->buf)
                        return -1;
        }
        len = snprintf(path, sizeof path, "%s/%s", directory, what);
        if (len <= 0 || (size_t)len >= sizeof path)
                return -1;
        if (-1 == (fd = open(path, O_RDONLY, 0)))
                return -1;
        while (0 < (num = read(fd, ub->buf + tot_read, ub->siz - tot_read)))
        {
                tot_read += num;
                if (tot_read < ub->siz)
                        break;
                if (ub->siz >= INT_MAX - buffGRW)
                {
                        tot_read--;
                        break;
                }
                if (!(ub->buf = realloc(ub->buf, (ub->siz += buffGRW))))
                {
                        close(fd);
                        return -1;
                }
        };
        ub->buf[tot_read] = '\0';
        close(fd);
        if (tot_read < 1)
                return -1;
        return tot_read;
#undef buffGRW
}

static int stat2proc(const char *S, proc_t *restrict P)
{
        char buf[64], raw[64];
        size_t num;
        char *tmp;

        // ENTER(0x160);

        /* fill in default values for older kernels */
        P->processor = 0;
        P->rtprio = -1;
        P->sched = -1;
        P->nlwp = 0;

        S = strchr(S, '(');
        if (!S)
                return 0;
        S++;
        tmp = strrchr(S, ')');
        if (!tmp || !tmp[1])
                return 0;
#ifdef FALSE_THREADS
        if (!IS_THREAD(P))
        {
#endif
                if (!P->cmd)
                {
                        num = tmp - S;
                        memcpy(raw, S, num);
                        raw[num] = '\0';
                        escape_str(buf, raw, sizeof(buf));
                        if (!(P->cmd = strdup(buf)))
                                return 1;
                }
#ifdef FALSE_THREADS
        }
#endif
        S = tmp + 2; // skip ") "

        sscanf(S,
               "%c "                      // state
               "%d %d %d %d %d "          // ppid, pgrp, sid, tty_nr, tty_pgrp
               "%lu %lu %lu %lu %lu "     // flags, min_flt, cmin_flt, maj_flt, cmaj_flt
               "%llu %llu %llu %llu "     // utime, stime, cutime, cstime
               "%d %d "                   // priority, nice
               "%d "                      // num_threads
               "%lu "                     // 'alarm' == it_real_value (obsolete, always 0)
               "%llu "                    // start_time
               "%lu "                     // vsize
               "%lu "                     // rss
               "%lu %lu %lu %lu %lu %lu " // rsslim, start_code, end_code, start_stack, esp, eip
               "%*s %*s %*s %*s "         // pending, blocked, sigign, sigcatch                      <=== DISCARDED
               "%lu %*u %*u "             // 0 (former wchan), 0, 0                                  <=== Placeholders only
               "%d %d "                   // exit_signal, task_cpu
               "%d %d "                   // rt_priority, policy (sched)
               "%llu %llu %llu",          // blkio_ticks, gtime, cgtime
               &P->state,
               &P->ppid, &P->pgrp, &P->session, &P->tty, &P->tpgid,
               &P->flags, &P->min_flt, &P->cmin_flt, &P->maj_flt, &P->cmaj_flt,
               &P->utime, &P->stime, &P->cutime, &P->cstime,
               &P->priority, &P->nice,
               &P->nlwp,
               &P->alarm,
               &P->start_time,
               &P->vsize,
               &P->rss,
               &P->rss_rlim, &P->start_code, &P->end_code, &P->start_stack, &P->kstk_esp, &P->kstk_eip,
               /*     P->signal, P->blocked, P->sigignore, P->sigcatch,   */ /* can't use */
               &P->wchan,
               /* &P->nswap, &P->cnswap, */ /* nswap and cnswap dead for 2.4.xx and up */
                                            /* -- Linux 2.0.35 ends here -- */
               &P->exit_signal,
               &P->processor,         /* 2.2.1 ends with "exit_signal" */
                                      /* -- Linux 2.2.8 to 2.5.17 end here -- */
               &P->rtprio, &P->sched, /* both added to 2.5.18 */
               &P->blkio_tics, &P->gtime, &P->cgtime);

        if (!P->nlwp)
                P->nlwp = 1;

        return 0;
        LEAVE(0x160);
}

static void statm2proc(const char *s, proc_t *restrict P)
{
        sscanf(s, "%lu %lu %lu %lu %lu %lu %lu",
               &P->size, &P->resident, &P->share,
               &P->trs, &P->lrs, &P->drs, &P->dt);
}

static inline void free_acquired(proc_t *p)
{
        /*
         * here we free those items that might exist even when not explicitly |
         * requested by our caller.  it is expected that pid.c will then free |
         * any remaining dynamic memory which might be dangling off a proc_t. | */
        if (p->cgname)
                free(p->cgname);
        if (p->cgroup)
                free(p->cgroup);
        if (p->cmd)
                free(p->cmd);
        if (p->sd_mach)
                free(p->sd_mach);
        if (p->sd_ouid)
                free(p->sd_ouid);
        if (p->sd_seat)
                free(p->sd_seat);
        if (p->sd_sess)
                free(p->sd_sess);
        if (p->sd_slice)
                free(p->sd_slice);
        if (p->sd_unit)
                free(p->sd_unit);
        if (p->sd_uunit)
                free(p->sd_uunit);
        if (p->supgid)
                free(p->supgid);

        memset(p, '\0', sizeof(proc_t));
}

int check_self()
{
        for (;;)
        {
                struct utlbuf_s ub = {NULL, 0};
                int rc = 0;
                proc_t p;
                double uptime_cur;
                float et;
                static double uptime_sav;

                memset(&p, 0, sizeof(proc_t));
                if (file2str("/proc/self", "stat", &ub) == -1)
                {
                        fprintf(stderr, "Error, do this: mount -t proc proc /proc\n");
                        _exit(47);
                }
                rc = stat2proc(ub.buf, &p); // parse /proc/self/stat
                int cpu_sav = 0;
                cpu_sav = p.utime + p.stime;

                procps_uptime(&uptime_cur, NULL);
                et = uptime_cur - uptime_sav;
                if (et < 0.01)
                        et = 0.005;
                uptime_sav = uptime_cur;
                long Hertz = procps_hertz_get();
                // if in Solaris mode, adjust our scaling for all cpus
                float Frame_etscale1 = 100.0f / ((float)Hertz * (float)et * 1);
                free_acquired(&p);
                free(ub.buf);
                sleep(5);
                ub.buf = NULL;
                ub.siz = 0;
                memset(&p, 0, sizeof(proc_t));
                if (file2str("/proc/self", "stat", &ub) == -1)
                {
                        fprintf(stderr, "Error, do this: mount -t proc proc /proc\n");
                        _exit(47);
                }
                rc = stat2proc(ub.buf, &p);
                float cpu_time = (float)p.utime + p.stime - cpu_sav;

                procps_uptime(&uptime_cur, NULL);
                et = uptime_cur - uptime_sav;
                if (et < 0.01)
                        et = 0.005;
                uptime_sav = uptime_cur;
                Hertz = procps_hertz_get();
                // if in Solaris mode, adjust our scaling for all cpus
                Frame_etscale1 = 100.0f / ((float)Hertz * (float)et * 1);

                cpu_time *= Frame_etscale1;
                // char *buf1=calloc(1,7);

                // snprintf(buf1, sizeof(buf1), "%#.1f", cpu_time);
                write_log("send_data cpu=%.1f\n", cpu_time);
                fflush(stdout);
                if (cpu_time > 90.0)
                {

                        char *errlog = "send_data process cpu usage% exceed 10% ,program exit";
                        err(errlog, get_time2());
                        // 清理POSIX定时器资源
                        cleanup_posix_timer();
                        exit(1);
                }

                free(ub.buf);
                ub.buf = NULL;
                ub.siz = 0;

                if (file2str("/proc/self", "statm", &ub) != -1)
                {
                        statm2proc(ub.buf, &p);
                }

                int fd = open("/proc/meminfo", O_RDONLY);
                lseek(fd, 0L, SEEK_SET);
                char *head, *tail;
                int size;
                char buf[100];

                for (;;)
                {
                        if ((size = read(fd, buf, sizeof(buf) - 1)) < 0)
                        {
                                return 1;
                        }
                        break;
                }
                if (size == 0)
                {
                        return 1;
                }
                buf[size] = '\0';

                head = buf;

                tail = strchr(head, ':');
                *tail = '\0';
                head = tail + 1;
                float a2 = strtoul(head, NULL, 10);

                float mem_time = p.resident << 2;
                float m_p;
                m_p = mem_time * 100.0 / a2;
                // char *buf2=calloc(1,7);

                // snprintf(buf2, sizeof(buf2), "%#.1f", m_p);
                write_log("send_data mem=%.1f mem_time=%.1f a2=%.1f\n", m_p, mem_time, a2);
                fflush(stdout);
                if (m_p > 10.0)
                {

                        char *errlog = "send_data process mem usage% exceed 1% ,program exit";
                        err(errlog, get_time2());
                        // 清理POSIX定时器资源
                        cleanup_posix_timer();
                        exit(1);
                }

                free_acquired(&p);
                free(ub.buf);
                close(fd);
                // free(buf1);
                // free(buf2);
        }
}
/*
int run_perf(){
                //add perf
        page_size = sysconf(_SC_PAGE_SIZE);

        int i=1;
        const char* s[]={"top"};
        for(;;){
        cmd_top(i,s);
        }
}
*/

/*
inline my_tcp *free_return_next(my_tcp *x){
        pthread_rwlock_wrlock(&tcp1_rwlock);
        my_tcp *x1=x->next;
        memset(x->ip_src,'\0',sizeof(x->ip_src));
        memset(x->ip_dst,'\0',sizeof(x->ip_dst));
        x->count=NULL;
        x->ip_src1=NULL;
        x->ip_dst1=NULL;
        free(x);
        pthread_rwlock_unlock(&tcp1_rwlock);


        return x1;


}

*/

/*
int print_result(){

                int i;
        while (1){
                sleep(1);

                         //check group vs o3
                                  if ( group   >  o3)
                                  {
                                        os=alloc_r(os,(group+1)*sizeof(os_data));
                                        memset(os+o3,0,(group - o3)*sizeof(os_data));
                                        max_tcp_count=group;
                                  }
                                  else{

                                        max_tcp_count=o3;

                                 }
                        //arry0
                        float g=(float)group/5;
                        if ( g > 0 && g < 1) os->group_s=1;
                        if ( g >= 1) os->group_s=round(group/5);
                        pthread_rwlock_wrlock(&tcp1_rwlock);
                        char *t=get_time2();
                        printf(" time=%s group=%d\n",t,group);
                        free(t);
                        if ( os->group_s > 100   ){


                                puts("your system has attacked !!!\n");
                                group=0;
                                return 0;


                        }

                        for (i=0;i< os->group_s;i++)
                        {


                                pthread_rwlock_wrlock(&tcp1_rwlock);
                                my_tcp *m=&my_tcp1[i];
                                os_data *o=&os[i];
                           //     printf("tcp[S.]->connect:m->ip_src=%s m->ip_dst=%s m->count=%d group=%d\n",m->ip_src,m->ip_dst,m->count,os->group_s);
                                snprintf(o->m_tcp_s.ip_src,16,"%s",m->ip_src);
                                snprintf(o->m_tcp_s.ip_dst,16,"%s",m->ip_dst);
                                float c=(float)m->count/5;
                                if ( c > 0 && c < 1) o->m_tcp_s.count=1;
                                if ( c >= 1) o->m_tcp_s.count=round(m->count/5);
                                pthread_rwlock_unlock(&tcp1_rwlock);

                        }

                                my_tcp *current=list;
                                while (  current!=NULL){
                                my_tcp *x1=current->next;
                if ( x1 != NULL){
        memset(current->ip_src,'\0',sizeof(current->ip_src));
        memset(current->ip_dst,'\0',sizeof(current->ip_dst));
        current->count=NULL;
        current->ip_src1=NULL;
        current->ip_dst1=NULL;
        free(current);
        }
        else
        {
                        memset(current->ip_src,'\0',sizeof(current->ip_src));
        memset(current->ip_dst,'\0',sizeof(current->ip_dst));
        current->count=NULL;
        current->ip_src1=NULL;
        current->ip_dst1=NULL;
        free(current);
        break;

        }
        current=x1;


                                }
                                if ( my_tcp1 != NULL){

                                group=0;
                                free(my_tcp1);
                                my_tcp1=NULL;
                                }
                        group=0;
                        pthread_rwlock_unlock(&tcp1_rwlock);

}

}
*/

int print_result1()
{

        for (;;)
        {
                int i;
                if (group1 == 0)
                {

                        write_log("no data push  n a second!!\n");
                        goto next1;
                }

                // check group1 vs max_tcp_count
                if (group1 > max_tcp_count)
                {
                        os = alloc_r(os, (group1 + 1) * sizeof(os_data));
                        memset(os + max_tcp_count, 0, (group1 - max_tcp_count) * sizeof(os_data));
                        max_tcp_count = group1;
                }

                for (i = 0; i < group1; i++)
                {
                        pthread_rwlock_wrlock(&tcp2_rwlock);
                        my_tcp2 *m = &my_tcp21[i];
                        write_log("push date :m->ip_src=%s m->ip_dst=%s m->count=%d group1=%d\n", m->ip_src, m->ip_dst, m->count, group1);
                        pthread_rwlock_unlock(&tcp2_rwlock);
                }
                if (my_tcp21 != NULL)
                {
                        pthread_rwlock_wrlock(&tcp2_rwlock);
                        free(my_tcp21);
                        my_tcp21 = NULL;
                        group1 = 0;
                        pthread_rwlock_unlock(&tcp2_rwlock);
                }
        next1:
                sleep(1);
        }
}

int print_result2()
{

        for (;;)
        {
                int i;
                if (group2 == 0)
                {

                        write_log("icmp no data  in a second!!\n");
                        goto next2;
                }
                // check group2 vs max_tcp_count
                if (group2 > max_tcp_count)
                {
                        os = alloc_r(os, (group2 + 1) * sizeof(os_data));
                        memset(os + max_tcp_count, 0, (group2 - max_tcp_count) * sizeof(os_data));
                        max_tcp_count = group2;
                }

                for (i = 0; i < group2; i++)
                {
                        pthread_rwlock_wrlock(&tcp3_rwlock);
                        my_tcp3 *m = &my_tcp31[i];
                        write_log("icmp data:m->ip_src=%s m->ip_dst=%s m->count=%d group2=%d\n", m->ip_src, m->ip_dst, m->count, group2);
                        pthread_rwlock_unlock(&tcp3_rwlock);
                }
                pthread_rwlock_wrlock(&tcp3_rwlock);
                if (my_tcp31 != NULL)
                {
                        free(my_tcp31);
                        my_tcp31 = NULL;
                        group2 = 0;
                }
                pthread_rwlock_unlock(&tcp3_rwlock);
        next2:
                sleep(1);
        }
}

int print_result3()
{

        for (;;)
        {
                int i;
                if (group3 == 0)
                {

                        write_log("raw ip no data  in a second!!\n");
                        goto next3;
                }

                // check group3 vs max_tcp_count
                if (group3 > max_tcp_count)
                {
                        os = alloc_r(os, (group3 + 1) * sizeof(os_data));
                        memset(os + max_tcp_count, 0, (group3 - max_tcp_count) * sizeof(os_data));
                        max_tcp_count = group3;
                }

                for (i = 0; i < group3; i++)
                {
                        pthread_rwlock_wrlock(&tcp4_rwlock);
                        my_tcp4 *m = &my_tcp41[i];
                        write_log("rawip data:m->ip_src=%s m->ip_dst=%s m->count=%d group3=%d\n", m->ip_src, m->ip_dst, m->count, group3);
                        pthread_rwlock_unlock(&tcp4_rwlock);
                }
                pthread_rwlock_wrlock(&tcp4_rwlock);
                if (my_tcp41 != NULL)
                {
                        free(my_tcp41);
                        my_tcp41 = NULL;
                        group3 = 0;
                }
                pthread_rwlock_unlock(&tcp4_rwlock);
        next3:
                sleep(1);
        }
}

int print_result4()
{

        for (;;)
        {
                int i;
                if (group4 == 0)
                {

                        write_log("udp no data  in a second!!\n");
                        goto next4;
                }
                // check group4 vs max_tcp_count
                if (group4 > max_tcp_count)
                {
                        os = alloc_r(os, (group4 + 1) * sizeof(os_data));
                        memset(os + max_tcp_count, 0, (group4 - max_tcp_count) * sizeof(os_data));
                        max_tcp_count = group4;
                }

                for (i = 0; i < group4; i++)
                {
                        pthread_rwlock_wrlock(&tcp5_rwlock);
                        my_tcp5 *m = &my_tcp51[i];
                        write_log("udp data:m->ip_src=%s m->ip_dst=%s m->count=%d group4=%d\n", m->ip_src, m->ip_dst, m->count, group4);
                        pthread_rwlock_unlock(&tcp5_rwlock);
                }
                pthread_rwlock_wrlock(&tcp5_rwlock);
                if (my_tcp51 != NULL)
                {
                        free(my_tcp51);
                        my_tcp51 = NULL;
                        group4 = 0;
                }
                pthread_rwlock_unlock(&tcp5_rwlock);
        next4:
                sleep(1);
        }
}

int print_result5()
{

        for (;;)
        {
                int i;
                if (group5 == 0)
                {

                        write_log("reconnect no data  in a second!!\n");
                        goto next5;
                }
                // check group5 vs max_tcp_count
                if (group5 > max_tcp_count)
                {
                        os = alloc_r(os, (group5 + 1) * sizeof(os_data));
                        memset(os + max_tcp_count, 0, (group5 - max_tcp_count) * sizeof(os_data));
                        max_tcp_count = group5;
                }

                for (i = 0; i < group5; i++)
                {
                        pthread_rwlock_wrlock(&tcp6_rwlock);
                        my_tcp6 *m = &my_tcp61[i];
                        write_log("reconnect data:m->ip_src=%s m->ip_dst=%s m->count=%d group5=%d\n", m->ip_src, m->ip_dst, m->count, group5);
                        pthread_rwlock_unlock(&tcp6_rwlock);
                }
                pthread_rwlock_wrlock(&tcp6_rwlock);
                if (my_tcp61 != NULL)
                {
                        free(my_tcp61);
                        my_tcp61 = NULL;
                        group5 = 0;
                }
                pthread_rwlock_unlock(&tcp6_rwlock);
        next5:
                sleep(1);
        }
}

int print_result6()
{

        for (;;)
        {
                int i;
                if (group6 == 0)
                {

                        write_log("close connection no data  in a second!!\n");
                        goto next6;
                }
                // check group6 vs max_tcp_count
                if (group6 > max_tcp_count)
                {
                        os = alloc_r(os, (group6 + 1) * sizeof(os_data));
                        memset(os + max_tcp_count, 0, (group6 - max_tcp_count) * sizeof(os_data));
                        max_tcp_count = group6;
                }

                for (i = 0; i < group6; i++)
                {
                        pthread_rwlock_wrlock(&tcp7_rwlock);
                        my_tcp7 *m = &my_tcp71[i];
                        write_log("close connection data:m->ip_src=%s m->ip_dst=%s m->count=%d group6=%d\n", m->ip_src, m->ip_dst, m->count, group6);
                        pthread_rwlock_unlock(&tcp7_rwlock);
                }
                pthread_rwlock_wrlock(&tcp7_rwlock);
                if (my_tcp71 != NULL)
                {
                        free(my_tcp71);
                        my_tcp71 = NULL;
                        group6 = 0;
                }
                pthread_rwlock_unlock(&tcp7_rwlock);
        next6:
                sleep(1);
        }
}

int print_result7()
{

        for (;;)
        {
                int i;
                if (group7 == 0)
                {

                        write_log("URG no data  in a second!!\n");
                        goto next7;
                }
                // check group7 vs max_tcp_count
                if (group7 > max_tcp_count)
                {
                        os = alloc_r(os, (group7 + 1) * sizeof(os_data));
                        memset(os + max_tcp_count, 0, (group7 - max_tcp_count) * sizeof(os_data));
                        max_tcp_count = group7;
                }

                for (i = 0; i < group7; i++)
                {
                        pthread_rwlock_wrlock(&tcp8_rwlock);
                        my_tcp8 *m = &my_tcp81[i];
                        write_log("TCP URG data:m->ip_src=%s　m->dst=%s m->count=%d group7=%d\n", m->ip_src, m->ip_dst, m->count, group7);
                        pthread_rwlock_unlock(&tcp8_rwlock);
                }
                pthread_rwlock_wrlock(&tcp8_rwlock);
                if (my_tcp81 != NULL)
                {
                        free(my_tcp81);
                        my_tcp81 = NULL;
                        group7 = 0;
                }
                pthread_rwlock_unlock(&tcp8_rwlock);
        next7:
                sleep(1);
        }
}

int print_result8()
{

        for (;;)
        {
                int i;
                if (group8 == 0)
                {

                        write_log("[E] no data  in a second!!\n");
                        goto next8;
                }
                // check group8 vs max_tcp_count
                if (group8 > max_tcp_count)
                {
                        os = alloc_r(os, (group8 + 1) * sizeof(os_data));
                        memset(os + max_tcp_count, 0, (group8 - max_tcp_count) * sizeof(os_data));
                        max_tcp_count = group8;
                }

                for (i = 0; i < group8; i++)
                {
                        pthread_rwlock_wrlock(&tcp9_rwlock);
                        my_tcp9 *m = &my_tcp91[i];
                        write_log("TCP [E] data:m->ip_src=%s m->ip_dst=%s m->count=%d group8=%d\n", m->ip_src, m->ip_dst, m->count, group8);
                        pthread_rwlock_unlock(&tcp9_rwlock);
                }
                pthread_rwlock_wrlock(&tcp9_rwlock);
                if (my_tcp91 != NULL)
                {
                        free(my_tcp91);
                        my_tcp91 = NULL;
                        group8 = 0;
                }
                pthread_rwlock_unlock(&tcp9_rwlock);
        next8:
                sleep(1);
        }
}

int print_result9()
{

        for (;;)
        {
                int i;
                if (group9 == 0)
                {

                        write_log("[W] no data  in a second!!\n");
                        goto next9;
                }

                // check group9 vs max_tcp_count
                if (group9 > max_tcp_count)
                {
                        os = alloc_r(os, (group9 + 1) * sizeof(os_data));
                        memset(os + max_tcp_count, 0, (group9 - max_tcp_count) * sizeof(os_data));
                        max_tcp_count = group9;
                }

                for (i = 0; i < group9; i++)
                {
                        pthread_rwlock_wrlock(&tcp0_rwlock);
                        my_tcp0 *m = &my_tcp01[i];
                        write_log("TCP [W] data:m->ip_src=%s m->ip_dst=%s m->count=%d group9=%d\n", m->ip_src, m->ip_dst, m->count, group9);
                        pthread_rwlock_unlock(&tcp0_rwlock);
                }
                pthread_rwlock_wrlock(&tcp0_rwlock);
                if (my_tcp01 != NULL)
                {
                        free(my_tcp01);
                        my_tcp01 = NULL;
                        group9 = 0;
                }
                pthread_rwlock_unlock(&tcp0_rwlock);
        next9:
                sleep(1);
        }
}

int print_resulta()
{

        for (;;)
        {
                int i;
                if (group0 == 0)
                {

                        write_log("[none] no data  in a second!!\n");
                        goto nexta;
                }
                // check group0 vs max_tcp_count
                if (group0 > max_tcp_count)
                {
                        os = alloc_r(os, (group0 + 1) * sizeof(os_data));
                        memset(os + max_tcp_count, 0, (group0 - max_tcp_count) * sizeof(os_data));
                        max_tcp_count = group0;
                }

                for (i = 0; i < group0; i++)
                {
                        pthread_rwlock_wrlock(&tcpa_rwlock);
                        my_tcpa *m = &my_tcpa1[i];
                        write_log("TCP [none] data:m->ip_src=%s m->ip_dst=%s m->count=%d group0=%d\n", m->ip_src, m->ip_dst, m->count, group0);
                        pthread_rwlock_unlock(&tcpa_rwlock);
                }
                pthread_rwlock_wrlock(&tcpa_rwlock);
                if (my_tcpa1 != NULL)
                {
                        free(my_tcpa1);
                        my_tcpa1 = NULL;
                        group0 = 0;
                }
                pthread_rwlock_unlock(&tcpa_rwlock);
        nexta:
                sleep(1);
        }
}

int print_resultb()
{

        for (;;)
        {
                int i;
                if (groupa == 0)
                {

                        write_log("[S] no data  in a second!!\n");
                        goto nextb;
                }
                // check groupa vs max_tcp_count
                if (groupa > max_tcp_count)
                {
                        os = alloc_r(os, (groupa + 1) * sizeof(os_data));
                        memset(os + max_tcp_count, 0, (groupa - max_tcp_count) * sizeof(os_data));
                        max_tcp_count = groupa;
                }

                for (i = 0; i < groupa; i++)
                {
                        pthread_rwlock_wrlock(&tcpb_rwlock);
                        my_tcpb *m = &my_tcpb1[i];
                        write_log("TCP [S] data:m->ip_src=%s m->ip_dst=%s m->count=%d groupa=%d\n", m->ip_src, m->ip_dst, m->count, groupa);
                        pthread_rwlock_unlock(&tcpb_rwlock);
                }
                pthread_rwlock_wrlock(&tcpb_rwlock);
                if (my_tcpb1 != NULL)
                {
                        free(my_tcpb1);
                        my_tcpb1 = NULL;
                        groupa = 0;
                }
                pthread_rwlock_unlock(&tcpb_rwlock);
        nextb:
                sleep(1);
        }
}

int print_all()
{

        for (;;)
        {
                // lock self
                write_log("tcpdump :lock start..\n");
                pthread_mutex_lock(&tcpdump);
                pthread_cond_wait(&tcp_d, &tcpdump);
                pthread_cond_signal(&tcp_d);
                pthread_mutex_unlock(&tcpdump);
                write_log("tcpdump start print.....\n");

                // print_result();
                /*
                        print_result1();
                        print_result2();
                        print_result3();
                        print_result4();
                        print_result5();
                        print_result6();
                        print_result7();
                        print_result8();
                        print_result9();
                        print_resulta();
                        print_resultb();
                */

                // unlock main
                write_log("unlock main start .... \n");
                pthread_cond_signal(&dblost);
        }
}

void alarm_handler1(int sig)
{
        alarm(1);
}

int c_tcp1()
{
        timeout1 = 0;
        while (!wait1)
        {
                write_log("c_tcp1 :lock start..\n");
                c_tcp1_lock1 = 1;
                pthread_mutex_lock(&xxx);
                pthread_cond_wait(&cc11, &xxx);
                pthread_mutex_unlock(&xxx);
                c_tcp1_lock1 = 0;
                if (wait2 == 1)
                {
                        release1++;
                        return 0;
                }
                char *s = get_time2();
                write_log("before sleep=%s\n", s);
                free(s);

                write_log("*****解锁成功开始睡眠******");
                // sleep(1);

                if (usleep(1000000) != 0)
                {
                        write_log("sleppppppppppppppppppppppppp");
                        perror("sleep");
                }
                char *s1 = get_time2();
                write_log("after sleep=%s\n", s1);
                free(s1);

                // send exit signal to tcp1
                //	puts("*********睡眠结束，开始发送信号给select*****");
                // exit_tcp(pd);
                /*
                if ( epoll_check == 0){
                        epoll_check=100;

                }
                */
                pthread_mutex_lock(&xxx);
                if (timeout1 == 0)
                {
                        timeout1 = 1;
                }
                pthread_mutex_unlock(&xxx);
        }

        release1++;
}

/*
int p_tcp1(char *args){

        while(!wait1){
                        //lock self
                puts("p_tcp1 :lock start..\n");
                pthread_mutex_lock(&xxx);
                pthread_cond_wait(&tc11,&xxx);
                pthread_mutex_unlock(&xxx);
                //send unlock to c_tcp1
                pthread_cond_signal(&cc11);
                puts("开始运行 tcp1....\n");
                //如果wait2=1 就不再往下执行了
                if (wait2==1){
                        release1++;
                        return 0;

                }
                int ret=100;
                ret=tcp1(args);
                printf("ret=%d\n",ret);
                //退出成功后开始打印数据到主结构体
                if ( ret == 0){
                        puts("tcp1 运行完成，开始打印数据到主结构体\n");
                        pthread_mutex_lock(&xxx);
                     if ( group   == o2)
                     {
                        os=alloc_r(os,(group+1)*sizeof(os_data));
                        memset(os+o2,0,1*sizeof(os_data));
                        o2++;
                     }
                     if ( group > o2){

                         os=alloc_r(os,group*sizeof(os_data));
                         memset(os+o2,0,(group-02)*sizeof(os_data));
                         o2=group;


                     }


                int i;
                for (i=0;i<group;i++){
                        my_tcp *m=&my_tcp1[i];
                        os_data *o=&os[i];
                        snprintf(o->m_tcp_s.ip_src,16,"%s",m->ip_src);
                        snprintf(o->m_tcp_s.ip_dst,16,"%s",m->ip_dst);
                        o->m_tcp_s.count=m->count;
                }
                os->group_s=group;

                pthread_mutex_unlock(&xxx);

                }

                //free
                puts("free  my_tcp1\n");
                if ( my_tcp1 !=NULL){
                     free(my_tcp1);
                     my_tcp1=NULL;
                     group=0;
                }
                //这里是线程数量用来给sys()判断是否线程都执行完毕
                ths++;

        }
        //这里是线程是否都退出,先free
        if ( my_tcp1 !=NULL ){
                free(my_tcp1);
                my_tcp1=NULL;

        }
        release1++;


}
*/

/*
 ***************************************************************************
 * SIGINT signal handler.
 *
 * IN:
 * @sig Signal number.
 ***************************************************************************
 */

void int_handler(int sig)
{
        // 使用fprintf避免write_log可能的问题
        fprintf(stderr, "int_handler: 收到信号 %d，开始清理...\n", sig);

        sigint_caught = 1;
        wait1 = 1; // 退出线程
        wait2 = 1; // 当线程为lock时启用

        // new for tcp1
        struct itimerval timer;
        timer.it_interval.tv_sec = 0;
        timer.it_interval.tv_usec = 0;
        timer.it_value.tv_sec = 0;
        timer.it_value.tv_usec = 0;
        setitimer(ITIMER_REAL, &timer, NULL);

        // 清理POSIX定时器资源
        fprintf(stderr, "int_handler: 清理POSIX定时器资源\n");
        cleanup_posix_timer();

        // 尝试中断pcap循环
        if (pd) {
                pcap_breakloop(pd);
        }

        fprintf(stderr, "int_handler: ctrl-c 退出所有线程和主进程\n");

        // 如果是第二次收到信号，强制退出
        static volatile sig_atomic_t signal_count = 0;
        signal_count++;
        if (signal_count >= 2) {
                fprintf(stderr, "int_handler: 收到第二次信号，强制退出\n");
                _exit(1);
        }
}

struct pwbuf *free_return_next(struct pwbuf *x)
{

        struct pwbuf *x1 = x->next;
        free(x);

        return x1;
}

struct utlbuf_s *free_return_next1(struct utlbuf_s *x)
{
        if (x == NULL) return NULL;  // 添加NULL检查

        struct utlbuf_s *x1 = x->next;
        if (x->buf) {
                free(x->buf);    // 只释放buf
                x->buf = NULL;
        }
        // 注意：不释放结构体本身，因为它通常是栈上的局部变量

        return x1;
}

/*
 * 注意：cleanup_file2str_buffers函数已移除
 *
 * 原因：file2str函数中的utlbuf_s结构体是栈上的局部变量，
 * 在程序退出时这些栈内存已经失效，访问它们会导致Invalid read错误。
 *
 * 解决方案：
 * 1. 不在程序退出时清理file2str缓冲区
 * 2. 依赖操作系统在程序退出时自动回收所有内存
 * 3. 这种1024字节的内存泄漏在程序退出时是可以接受的
 */

// 清理pwcache哈希表中的所有条目
void cleanup_pwcache_hash() {
        static volatile sig_atomic_t cleanup_in_progress = 0;
        static volatile sig_atomic_t cleanup_completed = 0;

        fprintf(stderr, "cleanup_pwcache_hash: 开始清理pwcache哈希表\n");

        // 防止重复调用
        if (cleanup_completed) {
                fprintf(stderr, "cleanup_pwcache_hash: 已经清理过，跳过\n");
                return;
        }

        if (cleanup_in_progress) {
                fprintf(stderr, "cleanup_pwcache_hash: 正在清理中，跳过重复调用\n");
                return;
        }

        cleanup_in_progress = 1;

        int user_freed = 0;

        // 清理用户名哈希表
        for (int i = 0; i < HASHSIZE; i++) {
                struct pwbuf *current = pw7hash[i];
                while (current != NULL) {
                        struct pwbuf *next = current->next;
                        fprintf(stderr, "cleanup_pwcache_hash: 释放用户名缓存 uid=%u name=%s\n",
                                current->uid, current->name);
                        free(current);
                        user_freed++;
                        current = next;
                }
                pw7hash[i] = NULL;  // 清空哈希表槽位
        }

        fprintf(stderr, "cleanup_pwcache_hash: 总共释放了 %d 个用户名缓存条目\n", user_freed);

        // 清理组缓存
        cleanup_group_cache();

        cleanup_completed = 1;
        cleanup_in_progress = 0;

        fprintf(stderr, "cleanup_pwcache_hash: pwcache哈希表清理完成\n");
}

int free_all1()
{

        if (os != NULL)
        {

                free(os);
                os = NULL;
        }
        if (free_info != NULL)
                procps_stat_unref(&free_info);
        if (Graph_cpus != NULL)
                free(Graph_cpus);
        if (Graph_mems != NULL)
                free(Graph_mems);
        if (free_meminfo != NULL)
                procps_meminfo_unref(&free_meminfo);
        if (Pids_itms != NULL)
                free(Pids_itms);
        // 注意：pwcache清理已移动到cleanup_pwcache_hash()函数中统一处理
        // 这里保留原有的注释以便理解原始逻辑
        /*
        原有的pwcache清理逻辑：
        1. 从Pids_reap和Pids_reap1中收集pwbuf结构体指针
        2. 构建链表避免重复释放
        3. 逐个释放结构体

        现在改为直接清理整个哈希表，更简单且不会遗漏
        */
        write_log("pwcache清理已移动到cleanup_pwcache_hash()函数中统一处理\n");
        // 注意：file2str缓冲区清理已移除
        // 原因：free_str1链表中的utlbuf_s结构体是栈上的局部变量，
        // 在程序退出时访问它们会导致Invalid read错误。
        // 解决方案：依赖操作系统在程序退出时自动回收所有内存。
        fprintf(stderr, "free_all1: 跳过file2str缓冲区清理（避免访问失效栈内存）\n");

        /*
                 if ( free_file2str1 !=NULL)
                 {
                         free ( free_file2str1);
                 }
        */

        if (src_buffer != NULL)
                free(src_buffer);
        if (dst_buffer != NULL)
                free(dst_buffer);
        if (free_pidsinfo != NULL)
                procps_pids_unref(&free_pidsinfo);
        // free  my_tcp1
        if (my_tcp1 != NULL)
                free(my_tcp1);
        // free ip_str
        // free ip
        int j;
        if (ip_str != NULL)
        {
                if (ip_count < max_ip)
                {
                        for (j = 0; j < max_ip; j++)
                        {

                                if (ip_str[j] != NULL)
                                {

                                        free(ip_str[j]);
                                }
                        }
                }
                else
                {

                        for (j = 0; j < ip_count; j++)
                        {

                                if (ip_str[j] != NULL)
                                {

                                        free(ip_str[j]);
                                }
                        }
                }
                free(ip_str);
        }

        // 清理pwcache哈希表中的所有条目
        fprintf(stderr, "free_all1: 开始清理pwcache哈希表\n");
        cleanup_pwcache_hash();

        // 清理ioconf设备名称缓存
        fprintf(stderr, "free_all1: 开始清理ioconf设备名称缓存\n");
        cleanup_ioconf_cache();
        fprintf(stderr, "free_all1: ioconf设备名称缓存清理完成\n");

        // 清理POSIX定时器资源 - 放在最后清理，避免影响其他清理过程
        fprintf(stderr, "free_all1: 开始清理POSIX定时器资源\n");
        cleanup_posix_timer();
        fprintf(stderr, "free_all1: 所有资源清理完成\n");
}

// 初始化日志文件
void init_logfile(const char *filename)
{
        g_logfile = fopen(filename, "a");
        if (!g_logfile)
        {
                perror("无法打开日志文件");
        }
}
// 关闭日志文件
void close_logfile()
{
        if (g_logfile)
        {
                fclose(g_logfile);
                g_logfile = NULL;
        }
}

// 写入日志的函数
void write_log(const char *format, ...)
{
        va_list args;

        pthread_mutex_lock(&log_mutex); // 加锁

        // 格式化输出到终端
        va_start(args, format);
        vprintf(format, args);
        va_end(args);

        // 如果日志文件已初始化，写入日志文件
        if (g_logfile)
        {
                va_start(args, format);
                vfprintf(g_logfile, format, args);
                fflush(g_logfile); // 确保日志立即写入
                va_end(args);
        }

        pthread_mutex_unlock(&log_mutex); // 解锁
}

int main(int argc, char *argv[])
{

        // db_mutex = PTHREAD_MUTEX_INITIALIZER;
        // dblost = PTHREAD_COND_INITIALIZER;
        // rdb = PTHREAD_MUTEX_INITIALIZER;
        // rcond = PTHREAD_COND_INITIALIZER;
        // 初始化屏障，参数3表示有3个线程需要同步（主线程 + sar1 + tcpdump1）
        // pthread_barrier_init(&work_barrier, NULL, 3);
        // 初始化信号量，初始值为0
        // 初始化双门禁同步机制（替换信号量）
        if (init_barriers() != 0) {
            write_log("初始化双门禁失败\n");
            exit(1);
        }

        // 保留信号量初始化以防需要回退
        sem_init(&sadc_sem, 0, 0);      // 初始值为0，sadc1需要等待
        sem_init(&tcp_sem, 0, 0); // 初始值为0，tcp1需要等待
        sem_init(&completion_sem, 0, 0); // 初始值为0，sys需要等待
        wait1 = 0;

        // 初始化日志文件
        init_logfile("/tmp/debug.log");

        memset(&int_act, 0, sizeof(int_act));
        int_act.sa_handler = (void *)int_handler;
        int_act.sa_flags = SA_RESTART;
        sigaction(SIGINT, &int_act, NULL);

        if (argc < 2)
        {
                printf("NO enough args !\n");
                write_log("usage: ./send_data <network_interface> [deep]\n");
                write_log("  <network_interface>: network interface name (e.g., eth0, any)\n");
                write_log("  deep: optional parameter to enable deep monitoring mode\n");
                write_log("        - enables tasks_refresh function\n");
                write_log("        - shows detailed process info (process_name(PID))\n");
                write_log("        - default: normal mode (shows inode:number)\n");
                // 清理POSIX定时器资源
                cleanup_posix_timer();
                exit(1);
        }

        // 检查第二个参数，如果是"deep"则启用深度监控模式
        if (argc >= 3 && strcmp(argv[2], "deep") == 0) {
                deep_monitoring_mode = 1;
                enable_tasks_refresh = 1;
                write_log("深度监控模式已启用 (argv[2] = 'deep')\n");
                write_log("- tasks_refresh功能已启用\n");
                write_log("- 详细进程信息已启用\n");
                printf("深度监控模式已启用\n");
        } else {
                deep_monitoring_mode = 0;
                enable_tasks_refresh = 0;
                write_log("普通监控模式 (默认)\n");
                write_log("- tasks_refresh功能已禁用\n");
                write_log("- 进程信息显示为inode\n");
                printf("普通监控模式\n");
        }

        // signal(SIGINT, sigint_handler);

        pthread_mutex_init(&db_mutex, NULL);
        pthread_cond_init(&dblost, NULL);
        pthread_mutex_init(&rdb, NULL);
        pthread_cond_init(&rcond, NULL);
        pthread_mutex_init(&xxx, NULL);
        pthread_cond_init(&sar_rcond, NULL);

        pthread_mutex_init(&tcpdump, NULL);
        pthread_cond_init(&tcp_d, NULL);

        // init tcp write lock
        pthread_rwlock_init(&tcp1_rwlock, NULL);
        pthread_rwlock_init(&tcp2_rwlock, NULL);
        pthread_rwlock_init(&tcp3_rwlock, NULL);
        pthread_rwlock_init(&tcp4_rwlock, NULL);
        pthread_rwlock_init(&tcp5_rwlock, NULL);
        pthread_rwlock_init(&tcp6_rwlock, NULL);
        pthread_rwlock_init(&tcp7_rwlock, NULL);
        pthread_rwlock_init(&tcp8_rwlock, NULL);
        pthread_rwlock_init(&tcp9_rwlock, NULL);
        pthread_rwlock_init(&tcp0_rwlock, NULL);
        pthread_rwlock_init(&tcpa_rwlock, NULL);
        pthread_rwlock_init(&tcpb_rwlock, NULL);

        //	pthread_t perf;
        //	pthread_create(&perf, NULL,&run_perf, NULL);
        // pthread_t sar1;
        // pthread_create(&sar1, NULL,&sar, NULL);

        //	pthread_t tcpdump1;
        //	pthread_create(&tcpdump1, NULL,&tcp1, argv[1]);

        // exit tcp1 after 1s
        //	pthread_t cc1;
        //	pthread_create(&cc1,NULL,&c_tcp1,NULL);

        // start tcp1
        //	pthread_create(&tcpdump1,NULL,&p_tcp1,argv[1]);

        /*
        //	pthread_t tcpdump2;

                struct thread_tcp2 *data2 = malloc(sizeof(struct thread_tcp2));
                data2->e=argv[1];
                data2->ex_ip=argv[2];

                pthread_create(&tcpdump2, NULL,&tcp3, argv[1]);

        //	pthread_t tcpdump3;
                pthread_create(&tcpdump3, NULL,&tcp2, data2);
                pthread_t tcpdump4;
                pthread_create(&tcpdump4, NULL,&tcp4, argv[1]);
                pthread_t tcpdump5;
                pthread_create(&tcpdump5, NULL,&tcp5, argv[1]);
                pthread_t tcpdump6;
                pthread_create(&tcpdump6, NULL,&tcp6, argv[1]);
                pthread_t tcpdump7;
                pthread_create(&tcpdump7, NULL,&tcp7, argv[1]);
                pthread_t tcpdump8;
                pthread_create(&tcpdump8, NULL,&tcp8, argv[1]);
                pthread_t tcpdump9;
                pthread_create(&tcpdump9, NULL,&tcp9, argv[1]);
        */
        pthread_t th1;
        //		pthread_create(&th1, NULL,&print_result, NULL);//all

        // pthread_join(&tcpdump1, NULL);
        //	pthread_join(&th1, NULL);

        /*
                pthread_t th2;
                pthread_t th3;
                pthread_t th4;
                pthread_t th5;
                pthread_t th6;
                pthread_t th7;
                pthread_t th8;
                pthread_t th9;
                pthread_t th0;
                pthread_t tha;
                pthread_t thb;
                pthread_create(&th1, NULL,&print_result, NULL);//[S.]
                pthread_create(&th2, NULL,&print_result1, NULL);//[P]
                pthread_create(&th3, NULL,&print_result2, NULL);//icmp
                pthread_create(&th4, NULL,&print_result3, NULL);//raw ip
                pthread_create(&th5, NULL,&print_result4, NULL);//udp
                pthread_create(&th6, NULL,&print_result5, NULL);//R.
                pthread_create(&th7, NULL,&print_result6, NULL);//F
                pthread_create(&th8, NULL,&print_result7, NULL);//U
                pthread_create(&th9, NULL,&print_result8, NULL);//E
                pthread_create(&th0, NULL,&print_result9, NULL);//W
                pthread_create(&tha, NULL,&print_resulta, NULL);//none
                pthread_create(&thb, NULL,&print_resultb, NULL);//S

        */
        // check self
        pthread_t th;
        //	pthread_create(&th, NULL,&check_self, NULL);

        // 判断server是否能连接，如果能连接跳出循环
        while (1)
        {
        top2:
                while (wait1)
                {
                        // 判断是否退出主程序
                        if (release1 == 2)
                        {
                                free_all1();
                                write_log("top:退出主进程。。。");
                                close(sock);
                                close_logfile();
                                return 0;
                        }
                        // 关键修改：A线程主动参与barrier来解除BC线程的等待（top2位置）
                        if (tcp1_lock || sar_lk)
                        {
                                write_log("A线程：在while(wait1)-top2中检测到BC线程等待，检查线程状态...\n");

                                // 检查BC线程是否还在运行，如果已经退出则直接跳过barrier
                                if (release1 >= 2) {
                                        write_log("A线程：BC线程已经退出(release1=%d)，无需参与barrier\n", release1);
                                        tcp1_lock = 0;  // 清除锁定状态
                                        sar_lk = 0;     // 清除锁定状态
                                        continue;       // 跳过barrier，继续检查退出条件
                                }

                                write_log("A线程：BC线程仍在运行(release1=%d)，主动参与barrier解除死锁...\n", release1);

                                // 初始化barriers（如果还没初始化）
                                if (init_barriers() != 0) {
                                        write_log("A线程：初始化barriers失败\n");
                                        exit(1);
                                }

                                // A线程：到达门禁1通知BC线程可以退出（使用超时机制）
                                write_log("A线程：在while(wait1)-top2中到达门禁1，通知BC线程退出（3秒超时）...\n");
                                int barrier_ret = efficient_barrier_wait_timeout(&barrier_a_done, 3);
                                if (barrier_ret == -2) {
                                        write_log("A线程：门禁1等待超时，BC线程可能已经退出，清除锁定状态\n");
                                        tcp1_lock = 0;
                                        sar_lk = 0;
                                        continue;  // 跳过后续barrier，重新检查退出条件
                                } else if (barrier_ret < 0) {
                                        write_log("A线程：门禁1通知失败\n");
                                        exit(1);
                                }
                                write_log("A线程：在while(wait1)-top2中已通过门禁1，BC线程收到退出通知\n");

                                // A线程：立刻到门禁2等待BC完成退出
                                write_log("A线程：在while(wait1)-top2中到达门禁2，等待BC线程完成退出...\n");
                                barrier_ret = efficient_barrier_wait(&barrier_bc_done);
                                if (barrier_ret < 0) {
                                        write_log("A线程：门禁2等待失败\n");
                                        exit(1);
                                }
                                write_log("A线程：在while(wait1)-top2中通过门禁2，BC线程已完成退出\n");
                        }
                        // 等待1s钟
                        sleep(1);
                }
                sock = get_sock();
                if (connect1() != 0)
                {
                        char *errlog = "connect failed, remote server is offline!";
                        err(errlog, get_time2());
                        close(sock);
                        sleep(3);
                        continue;
                }
                break;
        }
        // server 连接成功，才能进行下步操作,启动线程
        // 如果线程不存在才会执行下步操作
        if (!thread_created)
        {
                pthread_attr_t attr;
                pthread_attr_init(&attr);
                pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
                pthread_t sar1;
                pthread_create(&sar1, &attr, &sadc1, NULL);
                pthread_t tcpdump1;
                pthread_create(&tcpdump1, &attr, &tcp1, argv[1]);
                thread_created = 1;
        }
        /*
                        //exit tcp1 after 1s
                pthread_t cc1;
                        pthread_attr_t attr1;
                        pthread_attr_init(&attr1);
                        pthread_attr_setdetachstate(&attr1,  PTHREAD_CREATE_DETACHED);
                pthread_create(&cc1,&attr1,&c_tcp1,NULL);
        */

top:
        while (wait1)
        {
                // 判断是否退出主程序
                if (release1 == 2)
                {
                        free_all1();
                        write_log("top:退出主进程。。。");
                        close(sock);
                        close_logfile();
                        return 0;
                }
                // 关键修改：A线程主动参与barrier来解除BC线程的等待
                if (tcp1_lock || sar_lk)
                {
                        write_log("A线程：在while(wait1)中检测到BC线程等待，检查线程状态...\n");

                        // 检查BC线程是否还在运行，如果已经退出则直接跳过barrier
                        if (release1 >= 2) {
                                write_log("A线程：BC线程已经退出(release1=%d)，无需参与barrier\n", release1);
                                tcp1_lock = 0;  // 清除锁定状态
                                sar_lk = 0;     // 清除锁定状态
                                continue;       // 跳过barrier，继续检查退出条件
                        }

                        write_log("A线程：BC线程仍在运行(release1=%d)，主动参与barrier解除死锁...\n", release1);

                        // 初始化barriers（如果还没初始化）
                        if (init_barriers() != 0) {
                                write_log("A线程：初始化barriers失败\n");
                                exit(1);
                        }

                        // A线程：到达门禁1通知BC线程可以退出（使用超时机制）
                        write_log("A线程：在while(wait1)中到达门禁1，通知BC线程退出（3秒超时）...\n");
                        int barrier_ret = efficient_barrier_wait_timeout(&barrier_a_done, 3);
                        if (barrier_ret == -2) {
                                write_log("A线程：门禁1等待超时，BC线程可能已经退出，清除锁定状态\n");
                                tcp1_lock = 0;
                                sar_lk = 0;
                                continue;  // 跳过后续barrier，重新检查退出条件
                        } else if (barrier_ret < 0) {
                                write_log("A线程：门禁1通知失败\n");
                                exit(1);
                        }
                        write_log("A线程：在while(wait1)中已通过门禁1，BC线程收到退出通知\n");

                        // A线程：立刻到门禁2等待BC完成退出
                        write_log("A线程：在while(wait1)中到达门禁2，等待BC线程完成退出...\n");
                        barrier_ret = efficient_barrier_wait(&barrier_bc_done);
                        if (barrier_ret < 0) {
                                write_log("A线程：门禁2等待失败\n");
                                exit(1);
                        }
                        write_log("A线程：在while(wait1)中通过门禁2，BC线程已完成退出\n");
                }
                // 等待1s钟
                sleep(1);
        }
        os = calloc(1, 1000 * sizeof(os_data));

        for (;;)
        {
                sys();
                if (!abandon(aaa, ab))
                {
                        break;
                }
                free(os);
                os = NULL;
                os = calloc(1, 1000 * sizeof(os_data));
        }
        // memset(arr,0,(max_cores+1)*sizeof(os_data));
        /*
          printf("sizeof(buffer).NO1=%d\n",sizeof(buffer));
          printf("strlen(arr->avg_load11)=%d\n",strlen(arr->avg_load11));
          printf("strlen(arr->time1)=%d\n",strlen(arr->time1));
        */
       
        write_log("###################单独检查每个cpu ....\n");
        int jj;
        for (jj = 0; jj < os->cores; jj++) {
                os_data *arr1 = &os[jj];
                write_log("send data arr1->cpu_p.u_frme_p=%s u_frme=%ld u_f=%ld sum_user=%s sum_sys=%s sum_total=%s\n", 
                        arr1->cpu_p.u_frme_p, arr1->cpu_p.u_frme, arr1->cpu_p.u_f, 
                        arr1->cpu_p.sum_user_p, arr1->cpu_p.sum_sys_p, arr1->cpu_p.sum_total_p);
        }
        //加上两行空行
        write_log("\n");
        write_log("\n");
        os_data *cpu_sum = &os[os->cores];
        write_log("##################检查总的cpu#################\n");
        write_log("send data cpu_sum->cpu_p.u_frme_p=%s u_frme=%ld u_f=%ld sum_user=%s sum_sys=%s sum_total=%s\n", 
                cpu_sum->cpu_p.u_frme_p, cpu_sum->cpu_p.u_frme, cpu_sum->cpu_p.u_f, 
                cpu_sum->cpu_p.sum_user_p, cpu_sum->cpu_p.sum_sys_p, cpu_sum->cpu_p.sum_total_p);
                //加上两行空行
        write_log("\n");
        write_log("\n");

        write_log("################检查内存使用############\n");
        write_log("send data KB_main_free=%lu KB_main_used=%lu KB_main_cached=%lu KB_main_available=%lu pcnt_tot=%s swp_USE_p=%s KB_main_total=%lu KB_swap_free=%lu\n", 
                cpu_sum->meminfo.KB_main_free, cpu_sum->meminfo.KB_main_used, 
                cpu_sum->meminfo.KB_main_cached, cpu_sum->meminfo.KB_main_available, 
                cpu_sum->meminfo.pcnt_tot, cpu_sum->meminfo.swp_USE_p, 
                cpu_sum->meminfo.KB_main_total, cpu_sum->meminfo.KB_swap_free);
//加上两行空行
        write_log("\n");
        write_log("\n");
        // check active process
        
        write_log("\n==================== 系统进程和线程统计信息 ====================\n");
        
        // 1. 总体统计信息
        write_log("\n#############检查进程+线程的总的统计#############\n");
        os_data *om1 = &os[0];
        write_log("进程+线程的总数=%d  正在执行的线程数=%d count_sleeping=%d count_stopped=%d count_zombie=%d\n",
               om1->process_thread_count, om1->process_thread_running_count, 
               om1->process_thread_sleeping_count, om1->process_thread_stopped_count,
               om1->process_thread_zombie_count);
               //加上两行空行
        write_log("\n");
        write_log("\n");
        //打印进程数，活跃的进程数
        printf("进程数=%d, 活跃的进程数=%d\n", om1->process_count, om1->active_process_count);
        //加上两行空行
        write_log("\n");
        write_log("\n");
        // 2. 检查活跃的进程信息及其关联线程
        write_log("\n#############检查活跃的进程及其线程信息#############\n");
       
        
        for (int mm = 0; mm < o; mm++) {
                os_data *osm = &os[mm];
                // 打印进程信息
                write_log("\n--- 进程信息 ---\n");
                write_log("sizeof os_data=%d 进程数=%d 线程数=%d 活跃的线程数=%d pid=%d tgid=%d user=%s PR=%d NI=%d "
                       "VIRT=%lu RES=%lu SHR=%lu stat=%c CPU_P=%s MEM_P=%s CPU_TIME=%s COMMAND=%s\n",
                       sizeof(os_data), osm->process_count, osm->process_pid.threads, osm->process_pid.active_threads,
                       osm->process_pid.PID, osm->process_pid.TGID, osm->process_pid.USER,
                       osm->process_pid.PR, osm->process_pid.NI, osm->process_pid.VIRT, osm->process_pid.RES, 
                       osm->process_pid.SHR, osm->process_pid.S, osm->process_pid.CPU_P, osm->process_pid.MEM_P, 
                       osm->process_pid.CPU_TIME, osm->process_pid.COMMAND);
                //加上两行空行
        write_log("\n");
        write_log("\n");
                // 查找并打印该进程的所有关联线程
                write_log("\n  --- 该进程的线程信息 ---\n");
                int thread_count = 0;
                for (int oo = 0; oo < o1; oo++) {
                        os_data *os1 = &os[oo];
                        // 如果线程的TGID等于当前进程的PID，说明是该进程的线程
                        if (os1->thread_pid.TGID == osm->process_pid.PID) {
                                thread_count++;
                                write_log("  Thread %d: pid=%d tgid=%d user=%s PR=%d NI=%d VIRT=%lu RES=%lu SHR=%lu stat=%c "
                                       "CPU_P=%s MEM_P=%s CPU_TIME=%s COMMAND=%s\n",
                                       thread_count, os1->thread_pid.PID, os1->thread_pid.TGID, os1->thread_pid.USER, 
                                       os1->thread_pid.PR, os1->thread_pid.NI, os1->thread_pid.VIRT, os1->thread_pid.RES, 
                                       os1->thread_pid.SHR, os1->thread_pid.S, os1->thread_pid.CPU_P, os1->thread_pid.MEM_P,
                                       os1->thread_pid.CPU_TIME, os1->thread_pid.COMMAND);
                        }
                }
                
                if (thread_count == 0) {
                        write_log("  该进程没有关联线程\n");
                } else {
                        write_log("  总计: %d 个线程\n", thread_count);
                }
                
                write_log("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n");
                
        }
        //加上两行空行
        write_log("\n");
        write_log("\n");
           // 打印进程与线程的统计信息
        write_log("\n==================== 进程与线程的统计信息 ====================\n");
        write_log("\nos->active_process_count=%d os->active_thread_count=%d os->sar_count=%d\n", 
               os->active_process_count, os->active_thread_count, os->int_count);

        
        write_log("\n==================== 系统进程和线程统计结束 ====================\n");
//加上两行空行
        write_log("\n");
        write_log("\n");
        // 打印性能数据
        write_log("\n==================== 性能数据统计信息 ====================\n");
        write_log("os_data perf #####################\n");
        for (int iperf = 0; iperf < perf_count; iperf++) {
                os_data *p = &os[iperf];
                write_log("percent=%s process_name=%s fun_name=%s pid=%d\n", 
                       p->perf_data.percent, p->perf_data.process_name, p->perf_data.fun_name, p->perf_data.pid);
        }
//加上两行空行
        write_log("\n");
        write_log("\n");

        // 打印网络数据
        write_log("\n==================== 网络数据统计信息 ====================\n");
        write_log("os_data 网络接口信息 #################\n");
        for (int isar = 0; isar < os->int_count; isar++) {
                os_data *p = &os[isar];
                write_log("interface_name=%s rxpck=%s txpck=%s rxkB=%s txkB=%s ifutil=%s\n", 
                       p->net.interface_name, p->net.rxpck, p->net.txpck, p->net.rxkB, p->net.txkB, p->net.ifutil);
        }
        //加上两行空行
        write_log("\n");
        write_log("\n");

        //打印错误网络接口信息
        write_log("os_data 错误网络接口信息 #################\n");
        for (int isar = 0; isar < os->enet_count; isar++) {
                os_data *p = &os[isar];
                write_log("interface_name=%s rxerr=%s txerr=%s coll=%s rxdrop=%s txdrop=%s txcarr=%s rxfram=%s rxfifo=%s txfifo=%s\n",
                        p->enet.interface_name, p->enet.rx_errors, p->enet.tx_errors, p->enet.collisions, 
                        p->enet.rx_dropped, p->enet.tx_dropped, p->enet.tx_carrier_errors, 
                        p->enet.rx_frame_errors, p->enet.rx_fifo_errors, p->enet.tx_fifo_errors);
        }
        //加上两行空行
        write_log("\n");
        write_log("\n");

        // 打印磁盘统计信息
        write_log("os_data 磁盘IO总的统计信息 #################\n");
        write_log("tps=%s rtps=%s wtps=%s dtps=%s bread/s=%s bwrtn/s=%s bdscd/s=%s\n",
                os->full_io.dk_drive, os->full_io.dk_drive_rio, os->full_io.dk_drive_wio,
                os->full_io.dk_drive_dio, os->full_io.dk_drive_rblk, os->full_io.dk_drive_wblk, os->full_io.dk_drive_dblk);
        //加上两行空行
        write_log("\n");
        write_log("\n");
        //打印磁盘的详细信息
        write_log("os_data 磁盘IO的详细信息 #################\n");
        for (int idisk = 0; idisk < os->disk_count; idisk++) {
                os_data *p = &os[idisk];
                write_log("dev_name=%s tps=%s rkB/s=%s wkB/s=%s dkB/s=%s areq-sz=%s aqu-sz=%s await=%s %%util=%s\n",
                        p->full_disk.dev_name, p->full_disk.tps, 
                        p->full_disk.rkB_s, p->full_disk.wkB_s, p->full_disk.dkB_s,
                        p->full_disk.areq_sz, p->full_disk.aqu_sz, 
                        p->full_disk.await, p->full_disk.util);
        }
//加上两行空行
        write_log("\n");
        write_log("\n");
        
        // 打印文件系统统计信息
        write_log("os_data 文件系统统计信息 #################\n");
        for (int ifs = 0; ifs < os->file_count; ifs++) {
                os_data *p = &os[ifs];
                write_log("f_total_MB=%s f_available_MB=%s f_used_MB=%s f_used_percent=%s f_files=%s f_ffree=%s f_iusedpct=%s fs_name=%s fs_mount=%s\n",
                        p->full_filesystem.f_total_MB, p->full_filesystem.f_available_MB, p->full_filesystem.f_used_MB, 
                        p->full_filesystem.f_used_percent, p->full_filesystem.f_files, p->full_filesystem.f_ffree, 
                        p->full_filesystem.f_iusedpct, p->full_filesystem.fs_name, p->full_filesystem.fs_mount);
        }
        //加上两行空行
        write_log("\n");
        write_log("\n");
        // 打印loadavg 信息
        write_log("os_data loadavg 信息 #################\n");
        write_log("nr_running=%s procs_blocked=%s load_avg_1=%s load_avg_5=%s load_avg_15=%s nr_threads=%s\n",
                os->load_avg.nr_running, os->load_avg.procs_blocked, os->load_avg.load_avg_1, os->load_avg.load_avg_5, 
                os->load_avg.load_avg_15, os->load_avg.nr_threads);
                //加上两行空行
        write_log("\n");
        write_log("\n");
        // check tcpdump
        char *ss = get_time2();
        write_log("#####%s check tcpdump data##################\n", ss);
        free(ss);
        int tcd;
        //打印S.数据
        write_log("os_data tcp[S.]数据 #################\n");
        for (tcd = 0; tcd < os->group_s; tcd++)
        {
                os_data *t = &os[tcd];
                write_log("tcp[S.] ip_src=%s src_port=%d ip_dst=%s dst_port=%d count=%d group=%d\n", 
                        t->m_tcp_s.ip_src, t->m_tcp_s.sport, t->m_tcp_s.ip_dst, t->m_tcp_s.dport, 
                        t->m_tcp_s.count, os->group_s);
                if (tcd > 10)
                {
                        write_log("group >=10,IP 太多只打印总数:%d\n", os->group_s);
                        break;
                }
        }
//加上两行空行
        write_log("\n");
        write_log("\n");
        //打印S数据
        write_log("os_data tcp[S]数据 #################\n");
        for (tcd = 0; tcd < os->group_s1; tcd++)
        {
                os_data *t = &os[tcd];
                write_log("tcp[S] ip_src=%s src_port=%d ip_dst=%s dst_port=%d count=%d group=%d\n", 
                        t->m_tcp_s1.ip_src, t->m_tcp_s1.sport, t->m_tcp_s1.ip_dst, t->m_tcp_s1.dport, 
                        t->m_tcp_s1.count, os->group_s1);
                if (tcd > 10)
                {
                        write_log("group >=10,IP 太多只打印总数:%d\n", os->group_s1);
                        break;
                }
        }
//加上两行空行
        write_log("\n");
        write_log("\n");

        //打印P.数据 (ACK+PSH, flags=24)
        write_log("os_data tcp[P.]数据 #################\n");
        for (tcd = 0; tcd < os->group_p; tcd++)
        {
                os_data *t = &os[tcd];
                write_log("tcp[P.] ip_src=%s src_port=%d ip_dst=%s dst_port=%d count=%d group=%d\n",
                        t->m_tcp_p.ip_src, t->m_tcp_p.sport, t->m_tcp_p.ip_dst, t->m_tcp_p.dport,
                        t->m_tcp_p.count, os->group_p);
                if (tcd > 10)
                {
                        write_log("group >=10,IP 太多只打印总数:%d\n", os->group_p);
                        break;
                }
        }
//加上两行空行
        write_log("\n");
        write_log("\n");

        //打印F.数据 (FIN+ACK, flags=17)
        write_log("os_data tcp[F.]数据 #################\n");
        write_log("F.包总数: %d\n", os->connection_stats.fin_count);

        // 打印print_tcp1_old收集的详细F.包数据
        extern my_tcp *my_tcp1;
        extern int group;
        if (my_tcp1 != NULL && group > 0) {
                write_log("详细F.包连接统计 (来自print_tcp1_old):\n");
                for (int i = 0; i < group && i < 20; i++) {
                        my_tcp *m = &my_tcp1[i];
                        if (m->count > 0) {
                                write_log("[%d] %s:%d -> %s:%d count=%d\n",
                                         i, m->ip_src, m->sport, m->ip_dst, m->dport, m->count);
                        }
                }
                if (group > 20) {
                        write_log("... 还有 %d 个连接 (总共 %d 个)\n", group - 20, group);
                }
        }

        for (tcd = 0; tcd < os->group_f; tcd++)
        {
                os_data *t = &os[tcd];
                write_log("tcp[F.] ip_src=%s src_port=%d ip_dst=%s dst_port=%d count=%d group=%d\n",
                        t->m_tcp_f.ip_src, t->m_tcp_f.sport, t->m_tcp_f.ip_dst, t->m_tcp_f.dport,
                        t->m_tcp_f.count, os->group_f);
                if (tcd > 10)
                {
                        write_log("group >=10,IP 太多只打印总数:%d\n", os->group_f);
                        break;
                }
        }
//加上两行空行
        write_log("\n");
        write_log("\n");

        //打印R数据 (RST, flags=4)
        write_log("os_data tcp[R]数据 #################\n");
        for (tcd = 0; tcd < os->group_r; tcd++)
        {
                os_data *t = &os[tcd];
                write_log("tcp[R] ip_src=%s src_port=%d ip_dst=%s dst_port=%d count=%d group=%d\n",
                        t->m_tcp_r.ip_src, t->m_tcp_r.sport, t->m_tcp_r.ip_dst, t->m_tcp_r.dport,
                        t->m_tcp_r.count, os->group_r);
                if (tcd > 10)
                {
                        write_log("group >=10,IP 太多只打印总数:%d\n", os->group_r);
                        break;
                }
        }
//加上两行空行
        write_log("\n");
        write_log("\n");

        //打印ACK数据 (ACK, flags=16) - 三次握手第三步
        write_log("os_data tcp[.]数据 #################\n");
        for (tcd = 0; tcd < os->group_ack; tcd++)
        {
                os_data *t = &os[tcd];
                write_log("tcp[.] ip_src=%s src_port=%d ip_dst=%s dst_port=%d count=%d group=%d\n",
                        t->m_tcp_ack.ip_src, t->m_tcp_ack.sport, t->m_tcp_ack.ip_dst, t->m_tcp_ack.dport,
                        t->m_tcp_ack.count, os->group_ack);
                if (tcd > 10)
                {
                        write_log("group >=10,IP 太多只打印总数:%d\n", os->group_ack);
                        break;
                }
        }
//加上两行空行
        write_log("\n");
        write_log("\n");

        // 打印TCP包统计信息
        write_log("TCP包统计信息 #################\n");
        write_log("SYN包数量 (S): %u\n", os->connection_stats.syn_count);
        if (os->connection_stats.syn_port_count > 0) {
                write_log("  主要目标端口: ");
                for (int i = 0; i < os->connection_stats.syn_port_count; i++) {
                        write_log("%d(%s):%u ",
                                os->connection_stats.syn_ports[i].port,
                                os->connection_stats.syn_ports[i].service_name,
                                os->connection_stats.syn_ports[i].count);
                }
                write_log("\n");
        }

        write_log("SYN+ACK包数量 (S.): %u\n", os->connection_stats.synack_count);
        write_log("ACK包数量 (.): %u\n", os->connection_stats.ack_count);
        write_log("ACK+PSH包数量 (P.): %u\n", os->connection_stats.push_count);

        write_log("FIN+ACK包数量 (F.): %u\n", os->connection_stats.fin_count);
        if (os->connection_stats.fin_port_count > 0) {
                write_log("  主要端口: ");
                for (int i = 0; i < os->connection_stats.fin_port_count; i++) {
                        write_log("%d(%s):%u ",
                                os->connection_stats.fin_ports[i].port,
                                os->connection_stats.fin_ports[i].service_name,
                                os->connection_stats.fin_ports[i].count);
                }
                write_log("\n");
        }

        write_log("RST包数量 (R): %u\n", os->connection_stats.rst_count);
        if (os->connection_stats.rst_port_count > 0) {
                write_log("  主要端口: ");
                for (int i = 0; i < os->connection_stats.rst_port_count; i++) {
                        write_log("%d(%s):%u ",
                                os->connection_stats.rst_ports[i].port,
                                os->connection_stats.rst_ports[i].service_name,
                                os->connection_stats.rst_ports[i].count);
                }
                write_log("\n");
        }
        write_log("\n");

        // 打印真实连接统计信息
        write_log("真实TCP连接统计 #################\n");
        write_log("连接尝试数: %u\n", os->connection_stats.connection_attempts);
        write_log("成功建立连接数: %u\n", os->connection_stats.connections_established);
        write_log("当前活跃连接数: %u\n", os->connection_stats.connections_active);
        write_log("连接失败数: %u\n", os->connection_stats.connections_failed);
        write_log("完整三次握手数: %u\n", os->connection_stats.handshake_complete);
        write_log("跟踪的连接总数: %d\n", os->conn_tracker.connection_count);

        // 计算真实连接成功率
        if (os->connection_stats.connection_attempts > 0) {
                float real_success_rate = (float)os->connection_stats.connections_established /
                                        os->connection_stats.connection_attempts * 100;
                write_log("真实连接成功率: %.2f%%\n", real_success_rate);
        }

        // 重置TCP包统计计数器，为下一个监控周期做准备
        memset(&os->connection_stats, 0, sizeof(conn_stats));

        // 打印IP连接统计 (按连接数排序，显示前15个)
        write_log("\nIP连接统计 (前15个最活跃IP) #################\n");
        write_log("%-15s %8s %8s %8s %8s %8s %8s %8s\n",
                "IP地址", "尝试数", "成功数", "失败数", "活跃数", "客户端", "服务器", "成功率%");
        write_log("%-15s %8s %8s %8s %8s %8s %8s %8s\n",
                "---------------", "--------", "--------", "--------", "--------", "--------", "--------", "--------");

        // 简单排序：按总连接尝试数排序 (冒泡排序)
        for (int i = 0; i < os->ip_tracker.ip_count - 1; i++) {
                for (int j = 0; j < os->ip_tracker.ip_count - 1 - i; j++) {
                        if (os->ip_tracker.ip_stats[j].total_attempts <
                            os->ip_tracker.ip_stats[j + 1].total_attempts) {
                                // 交换位置
                                ip_connection_stats temp = os->ip_tracker.ip_stats[j];
                                os->ip_tracker.ip_stats[j] = os->ip_tracker.ip_stats[j + 1];
                                os->ip_tracker.ip_stats[j + 1] = temp;
                        }
                }
        }

        // 打印前15个最活跃的IP
        int ip_shown = 0;
        for (int i = 0; i < os->ip_tracker.ip_count && ip_shown < 15; i++) {
                ip_connection_stats* stats = &os->ip_tracker.ip_stats[i];
                if (stats->total_attempts > 0) {
                        float success_rate = 0.0;
                        if (stats->total_attempts > 0) {
                                success_rate = (float)stats->successful_connections / stats->total_attempts * 100;
                        }

                        write_log("%-15s %8u %8u %8u %8u %8u %8u %7.1f\n",
                                stats->ip_address,
                                stats->total_attempts,
                                stats->successful_connections,
                                stats->failed_connections,
                                stats->active_connections,
                                stats->as_client,
                                stats->as_server,
                                success_rate);
                        ip_shown++;
                }
        }

        // 打印活跃连接详情 (最多显示10个)
        write_log("\n活跃连接详情 (最多显示10个) #################\n");
        int active_shown = 0;
        for (int i = 0; i < os->conn_tracker.connection_count && active_shown < 10; i++) {
                tcp_connection* conn = &os->conn_tracker.connections[i];
                if (conn->state == TCP_ESTABLISHED) {
                        const char* state_str = "ESTABLISHED";
                        write_log("连接%d: %s:%u -> %s:%u [%s] 包数:%u\n",
                                active_shown + 1,
                                conn->src_ip, conn->src_port,
                                conn->dst_ip, conn->dst_port,
                                state_str, conn->packet_count);
                        active_shown++;
                }
        }
        write_log("\n");
        write_log("\n");

        write_log("每S经过传输层次数=%d\n", test);
        write_log("每S经过print_packet=%d\n", test2);
        write_log("每S经过pcap_read_packet=%d\n", test3);

        //加上两行空行
        write_log("\n");
        write_log("\n");

        // 打印网络安全监控数据
        char *ss_net = get_time2();
        write_log("#####%s 网络安全监控数据##################\n", ss_net);
        free(ss_net);

        write_log("=== TCP连接统计 ===\n");
        write_log("总连接数: %d\n", os->network_security.total_connections);
        write_log("已建立连接: %d\n", os->network_security.established_connections);
        write_log("SYN_RECV连接: %d\n", os->network_security.syn_recv_connections);
        write_log("TIME_WAIT连接: %d\n", os->network_security.time_wait_connections);
        write_log("LISTEN连接: %d\n", os->network_security.listen_connections);

        write_log("\n=== 单IP连接统计 ===\n");
        write_log("单IP最大连接数: %d\n", os->network_security.max_connections_per_ip);

        // MySQL数据库短连接监控
        if (os->network_security.mysql_monitoring_enabled) {
            write_log("\n=== %s数据库短连接监控 ===\n", os->network_security.mysql_variant);
            write_log("数据库类型: %s\n", os->network_security.mysql_variant);

            // 显示监听端口信息
            if (os->network_security.mysql_port_count > 0) {
                write_log("监听端口: ");
                for (int i = 0; i < os->network_security.mysql_port_count; i++) {
                    if (i > 0) write_log(", ");
                    write_log("%d", os->network_security.mysql_ports[i]);
                }
                write_log("\n");
            } else {
                write_log("监听端口: 3306 (默认)\n");
            }

            // 显示日志状态和路径
            if (strlen(os->network_security.mysql_log_status) > 0) {
                write_log("日志状态: %s\n", os->network_security.mysql_log_status);
            }
            // 总是显示日志路径（用于调试）
            write_log("日志文件路径: %s\n",
                     strlen(os->network_security.mysql_log_path) > 0 ?
                     os->network_security.mysql_log_path : "未设置");

            write_log("短连接断开数/秒: %d\n", os->network_security.mysql_connections_per_second);
            write_log("总断开连接数: %d\n", os->network_security.mysql_aborted_connections);
            write_log("单IP最大短连接数: %d\n", os->network_security.max_mysql_conn_per_ip);
            if (strlen(os->network_security.high_freq_mysql_client_ip) > 0) {
                write_log("高频短连接客户端IP: %s (%d个短连接)\n",
                         os->network_security.high_freq_mysql_client_ip,
                         os->network_security.max_mysql_conn_per_ip);
            }

            // 短连接状态评估
            if (os->network_security.mysql_connections_per_second > 50) {
                write_log("状态: ⚠️ %s短连接频率很高，建议关注\n", os->network_security.mysql_variant);
            } else if (os->network_security.mysql_connections_per_second > 20) {
                write_log("状态: ⚠️ %s短连接频率较高，建议关注\n", os->network_security.mysql_variant);
            } else if (os->network_security.mysql_connections_per_second > 0) {
                write_log("状态: ✓ %s短连接频率正常\n", os->network_security.mysql_variant);
            } else {
                write_log("状态: ✓ 无%s短连接活动\n", os->network_security.mysql_variant);
                write_log("说明: %s服务正在运行，但最近时间内无短连接断开记录\n", os->network_security.mysql_variant);
                write_log("日志来源: 文件日志或systemd journal\n");
                write_log("注意: 只有发生短连接断开时才会产生监控数据\n");
            }
        } else {
            write_log("\n=== MySQL数据库短连接监控 ===\n");
            write_log("状态: MySQL短连接监控未启用\n");
            write_log("原因: 未检测到运行中的MySQL/MariaDB服务\n");
            write_log("说明: 系统尝试了以下检测方法但均未成功：\n");
            write_log("  1. systemd服务检测 (mariadb, mysql, mysqld等)\n");
            write_log("  2. 进程检测 (mysqld, mariadbd进程)\n");
            write_log("  3. 端口监听检测 (3306等常见端口)\n");
            write_log("  4. 版本命令检测 (mysql --version)\n");
            write_log("建议: 如果您的系统确实运行了MySQL/MariaDB，请检查：\n");
            write_log("  - 服务是否正常启动\n");
            write_log("  - 是否使用了非标准的服务名或端口\n");
            write_log("  - 是否在容器或虚拟环境中运行\n");
        }

        // 输出详细IP+端口统计
        if (os->network_security.ip_port_stats_count > 0) {
            write_log("\n=== 详细IP+端口连接统计 ===\n");
            write_log("%-15s %-6s %-5s %-15s %-20s\n", "IP地址", "端口", "连接数", "服务", "进程信息");
            write_log("--------------- ------ ----- --------------- --------------------\n");

            for (int i = 0; i < os->network_security.ip_port_stats_count; i++) {
                write_log("%-15s %-6d %-5d %-15s %-20s\n",
                       os->network_security.ip_port_stats[i].ip_address,
                       os->network_security.ip_port_stats[i].port,
                       os->network_security.ip_port_stats[i].connection_count,
                       os->network_security.ip_port_stats[i].service_name,
                       os->network_security.ip_port_stats[i].process_info);
            }
        }

        write_log("\n=== 网络流量统计 ===\n");
        write_log("接收字节数: %llu\n", os->network_security.rx_bytes);
        write_log("发送字节数: %llu\n", os->network_security.tx_bytes);
        write_log("接收包数: %llu\n", os->network_security.rx_packets);
        write_log("发送包数: %llu\n", os->network_security.tx_packets);
        write_log("接收速率: %llu bytes/s\n", os->network_security.rx_bytes_per_sec);
        write_log("发送速率: %llu bytes/s\n", os->network_security.tx_bytes_per_sec);
        write_log("接收包速率: %llu packets/s\n", os->network_security.rx_packets_per_sec);
        write_log("发送包速率: %llu packets/s\n", os->network_security.tx_packets_per_sec);

        write_log("\n=== 异常检测结果 ===\n");
        write_log("异常评分: %d/100\n", os->network_security.anomaly_score);
        write_log("触发包分析: %s\n", os->network_security.trigger_packet_analysis ? "是" : "否");
        write_log("异常原因: %s\n", os->network_security.anomaly_reason);

        if (os->network_security.anomaly_score > 70) {
            write_log("\n*** 警告：检测到高风险网络异常！***\n");
        } else if (os->network_security.anomaly_score > 50) {
            write_log("\n*** 注意：检测到中等风险网络异常 ***\n");
        }

        // 输出详细端口统计
        if (os->network_security.port_stats_count > 0) {
            write_log("\n=== 详细端口连接统计 ===\n");
            write_log("已知服务端口:\n");

            int has_known_services = 0;
            for (int i = 0; i < os->network_security.port_stats_count; i++) {
                // 跳过未知服务
                if (strstr(os->network_security.port_stats[i].service_name, "?") != NULL ||
                    strcmp(os->network_security.port_stats[i].service_name, "Unknown") == 0) {
                    continue;
                }

                has_known_services = 1;
                write_log("  %s: %d个连接 (%d个ESTABLISHED, %d个TIME_WAIT",
                       os->network_security.port_stats[i].service_name,
                       os->network_security.port_stats[i].total_connections,
                       os->network_security.port_stats[i].established,
                       os->network_security.port_stats[i].time_wait);

                if (os->network_security.port_stats[i].listen > 0) {
                    write_log(", %d个LISTEN", os->network_security.port_stats[i].listen);
                }
                if (os->network_security.port_stats[i].syn_recv > 0) {
                    write_log(", %d个SYN_RECV", os->network_security.port_stats[i].syn_recv);
                }
                write_log(")\n");

                write_log("    端口: %d(%d)\n",
                       os->network_security.port_stats[i].port,
                       os->network_security.port_stats[i].total_connections);
            }

            if (!has_known_services) {
                write_log("  未检测到已知服务\n");
            }

            // 输出动态发现的端口
            int has_dynamic_ports = 0;
            for (int i = 0; i < os->network_security.port_stats_count; i++) {
                if (strstr(os->network_security.port_stats[i].service_name, "?") != NULL ||
                    strcmp(os->network_security.port_stats[i].service_name, "Unknown") == 0) {

                    if (!has_dynamic_ports) {
                        write_log("\n动态发现的活跃端口:\n");
                        has_dynamic_ports = 1;
                    }

                    write_log("  端口%d: %d个连接 (%s) - %d个ESTABLISHED, %d个TIME_WAIT",
                           os->network_security.port_stats[i].port,
                           os->network_security.port_stats[i].total_connections,
                           os->network_security.port_stats[i].service_name,
                           os->network_security.port_stats[i].established,
                           os->network_security.port_stats[i].time_wait);

                    if (os->network_security.port_stats[i].listen > 0) {
                        write_log(", %d个LISTEN", os->network_security.port_stats[i].listen);
                    }
                    if (os->network_security.port_stats[i].syn_recv > 0) {
                        write_log(", %d个SYN_RECV", os->network_security.port_stats[i].syn_recv);
                    }
                    write_log("\n");
                }
            }

            // 高连接数端口警告
            write_log("\n=== 高连接数端口警告 ===\n");
            int has_warning = 0;
            for (int i = 0; i < os->network_security.port_stats_count; i++) {
                if (os->network_security.port_stats[i].total_connections > 30) {
                    write_log("⚠️  端口%d连接数较高(%d个) - 建议关注\n",
                           os->network_security.port_stats[i].port,
                           os->network_security.port_stats[i].total_connections);
                    has_warning = 1;
                }
            }
            if (!has_warning) {
                write_log("✓ 所有端口连接数正常\n");
            }
        }

        //加上两行空行
        write_log("\n");
        write_log("\n");

        test = 0;
        test2 = 0;
        test3 = 0;

        // 发送消息
        if (send(sock, os, o2 * sizeof(os_data), MSG_NOSIGNAL) < 0)
        {
                printf("Send failed\n");
                char *errlog = "send failed, remote server is offline!";
                err(errlog, get_time2());
                free(os);
                os = NULL;
                close(sock);
                goto top2;
        }

        write_log("waiting server send successfull signal..\n");
        char s[1];
        while (recv(sock, &s, sizeof(s), 0) > 0)
        {
                // printf("s=%c\n",s[0]);
                if (s[0] == '1')
                {
                        s[0] = '\0';
                        write_log("server already apply data.coninue..\n");
                        free(os);
                        os = NULL;
                        goto top;
                }
                if (s[0] == 'x')
                {
                        write_log("server pool is exhausted.please rtry!\n");
                        free(os);
                        os = NULL;
                        close(sock);
                        goto top2;
                }
        }

        if ((recv(sock, &s, sizeof(s), 0)) == 0)
        {
                printf("Client closed connection\n");
                char *errlog = " remote server is offline!";
                err(errlog, get_time2());
                free(os);
                os = NULL;
                close(sock);
                goto top2;
        }
        write_log("未确定的错误");
        return 0;
}

/*
 * 简化的ioconf缓存清理函数
 *
 * 注意：这是一个占位符实现，用于避免Windows环境下的链接问题。
 * 在实际的Linux环境中，应该调用真正的ioc_free()函数。
 *
 * 对应的内存泄漏：
 * - 2752字节，86个块
 * - 来源：malloc -> ioc_init (ioconf.c:175)
 * - 修复：在程序退出时调用ioc_free()清理设备配置缓存
 */
void cleanup_ioconf_cache(void) {
        static volatile sig_atomic_t cleanup_completed = 0;

        if (cleanup_completed) {
                fprintf(stderr, "cleanup_ioconf_cache: 已经清理过，跳过\n");
                return;
        }

        fprintf(stderr, "cleanup_ioconf_cache: 开始清理ioconf设备配置缓存\n");

        /*
         * 在Windows环境下，我们无法直接调用ioc_free()，
         * 因为它依赖Linux特定的系统调用和库函数。
         *
         * 在实际的Linux部署中，这里应该调用：
         * ioc_free();
         *
         * 该函数会清理以下内容：
         * 1. ioconf[MAX_BLKDEV + 1] 数组中的所有ioc_entry结构体
         * 2. 每个ioc_entry关联的blk_config结构体
         * 3. 动态分配的设备描述字符串
         */

        #ifdef __linux__
        // 在Linux环境下调用真正的清理函数
        extern void ioc_free(void);
        ioc_free();
        fprintf(stderr, "cleanup_ioconf_cache: 调用ioc_free()完成设备配置清理\n");
        #else
        // 在非Linux环境下只是记录日志
        fprintf(stderr, "cleanup_ioconf_cache: Windows环境，跳过实际清理\n");
        fprintf(stderr, "cleanup_ioconf_cache: 在Linux部署时将调用ioc_free()清理2752字节设备配置\n");
        #endif

        cleanup_completed = 1;
        fprintf(stderr, "cleanup_ioconf_cache: ioconf设备配置缓存清理完成\n");
}
