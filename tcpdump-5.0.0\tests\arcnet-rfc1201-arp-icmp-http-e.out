    1  2025-05-10 13:20:23.012325 be 00 ea 26: Request who-has ************* tell ***********, length 18
    2  2025-05-10 13:20:23.012419 50 be 00 26: Reply ************* is-at 50, length 18
    3  2025-05-10 13:20:23.013721 be 50 a8 92: *********** > *************: ICMP echo request, id 59392, seq 0, length 64
    4  2025-05-10 13:20:23.013926 50 be ea 92: ************* > ***********: ICMP echo reply, id 59392, seq 0, length 64
    5  2025-05-10 13:20:24.009498 be 50 a8 92: *********** > *************: ICMP echo request, id 59392, seq 256, length 64
    6  2025-05-10 13:20:24.009584 50 be a8 92: ************* > ***********: ICMP echo reply, id 59392, seq 256, length 64
    7  2025-05-10 13:20:28.012714 50 be 83 26: Request who-has *********** tell *************, length 18
    8  2025-05-10 13:20:28.013554 be 50 ea 26: Reply *********** is-at be, length 18
    9  2025-05-10 13:20:28.179366 be 50 c3 65: ***********.1024 > ***********.53: 16943+ A? tcpdump.org. (29)
   10  2025-05-10 13:20:28.252775 50 be oldip 81: ***********.53 > ***********.1024: 16943 1/0/0 A *************** (45)
   11  2025-05-10 13:20:28.255861 be 50 b2 82: ***********.1024 > ***********.53: 16944+ PTR? ***************.in-addr.arpa. (46)
   12  2025-05-10 13:20:28.296735 50 be oldip 118: ***********.53 > ***********.1024: 16944 1/0/0 PTR flagpole-4.tcpdump.org. (82)
   13  2025-05-10 13:20:28.299292 be 50 c0 68: ***********.1027 > ***************.80: Flags [S], seq 2567257385, win 1872, options [mss 468,sackOK,TS val 305412 ecr 0,nop,wscale 0], length 0
   14  2025-05-10 13:20:28.346375 50 be oldip 68: ***************.80 > ***********.1027: Flags [S.], seq 305328889, ack 2567257386, win 14600, options [mss 1320,nop,wscale 12,sackOK,TS val 1281130006 ecr 305412], length 0
   15  2025-05-10 13:20:28.347811 be 50 c8 60: ***********.1027 > ***************.80: Flags [.], ack 1, win 1872, options [nop,nop,TS val 305417 ecr 1281130006], length 0
   16  2025-05-10 13:20:28.351197 be 50 06 254: ***********.1027 > ***************.80: Flags [P.], seq 1:195, ack 1, win 1872, options [nop,nop,TS val 305417 ecr 1281130006], length 194: HTTP: GET / HTTP/1.1
   17  2025-05-10 13:20:28.390574 50 be oldip 60: ***************.80 > ***********.1027: Flags [.], ack 1, win 1024, options [nop,nop,TS val 1281130053 ecr 305417], length 0
   18  2025-05-10 13:20:28.417685 50 be oldip 60: ***************.80 > ***********.1027: Flags [.], ack 195, win 1024, options [nop,nop,TS val 1281130074 ecr 305417], length 0
   19  2025-05-10 13:20:28.550695 50 be oldip 245: ***************.80 > ***********.1027: Flags [P.], seq 1:186, ack 195, win 1024, options [nop,nop,TS val 1281130211 ecr 305417], length 185: HTTP: HTTP/1.1 301 Moved Permanently
   20  2025-05-10 13:20:28.552307 50 be oldip 288: ***************.80 > ***********.1027: Flags [P.], seq 186:414, ack 195, win 1024, options [nop,nop,TS val 1281130211 ecr 305417], length 228: HTTP
   21  2025-05-10 13:20:28.554661 be 50 c8 60: ***********.1027 > ***************.80: Flags [.], ack 186, win 2736, options [nop,nop,TS val 305438 ecr 1281130211], length 0
   22  2025-05-10 13:20:28.555316 be 50 c8 60: ***********.1027 > ***************.80: Flags [.], ack 414, win 3648, options [nop,nop,TS val 305438 ecr 1281130211], length 0
   23  2025-05-10 13:20:28.557232 be 50 c8 60: ***********.1027 > ***************.80: Flags [F.], seq 195, ack 414, win 3648, options [nop,nop,TS val 305438 ecr 1281130211], length 0
   24  2025-05-10 13:20:28.607583 50 be oldip 60: ***************.80 > ***********.1027: Flags [.], ack 196, win 1024, options [nop,nop,TS val 1281130271 ecr 305438], length 0
   25  2025-05-10 13:20:28.608180 50 be oldip 60: ***************.80 > ***********.1027: Flags [F.], seq 414, ack 196, win 1024, options [nop,nop,TS val 1281130271 ecr 305438], length 0
   26  2025-05-10 13:20:28.609468 be 50 c8 60: ***********.1027 > ***************.80: Flags [.], ack 415, win 3648, options [nop,nop,TS val 305443 ecr 1281130271], length 0
