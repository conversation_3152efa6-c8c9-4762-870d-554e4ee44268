    1  2023-08-10 12:23:59.828062 IP (tos 0xc0, ttl 255, id 1498, offset 0, flags [none], proto TCP (6), length 72)
    ***********.58320 > ***********.646: Flags [P.], cksum 0x3986 (correct), seq 96201:96233, ack 83737, win 2990, length 32
	LDP, Label-Space-ID: ***********:0, pdu-length: 28
	  Notification Message (0x0001), length: 18, Message ID: 0xfffffff9, Flags: [ignore if unknown]
	    Status TLV (0x0300), length: 10, Flags: [ignore and don't forward if unknown]
	      Status Code: Shutdown, Flags: [Fatal error and don't forward]
    2  2023-08-10 12:23:59.828114 IP (tos 0xc0, ttl 255, id 1499, offset 0, flags [none], proto TCP (6), length 40)
    ***********.58320 > ***********.646: Flags [F.], cksum 0x7d76 (correct), seq 32, ack 1, win 2990, length 0
    3  2023-08-10 12:24:00.018513 IP (tos 0xc0, ttl 1, id 0, offset 0, flags [none], proto UDP (17), length 70)
    ********.646 > *********.646: 
	LDP, Label-Space-ID: ***********:0, pdu-length: 38
	  Hello Message (0x0100), length: 28, Message ID: 0x00000038, Flags: [ignore if unknown]
	    Common Hello Parameters TLV (0x0400), length: 4, Flags: [ignore and don't forward if unknown]
	      Hold Time: 15s, Flags: [Link Hello]
	    IPv4 Transport Address TLV (0x0401), length: 4, Flags: [ignore and don't forward if unknown]
	      IPv4 Transport Address: ***********
	    Dual-Stack Capability TLV (0x0701), length: 4, Flags: [continue processing and don't forward if unknown]
	      Transport Connection Preference: IPv4
    4  2023-08-10 12:24:05.058942 IP (tos 0xc0, ttl 1, id 0, offset 0, flags [none], proto UDP (17), length 70)
    ********.646 > *********.646: 
	LDP, Label-Space-ID: ***********:0, pdu-length: 38
	  Hello Message (0x0100), length: 28, Message ID: 0x00000038, Flags: [ignore if unknown]
	    Common Hello Parameters TLV (0x0400), length: 4, Flags: [ignore and don't forward if unknown]
	      Hold Time: 15s, Flags: [Link Hello]
	    IPv4 Transport Address TLV (0x0401), length: 4, Flags: [ignore and don't forward if unknown]
	      IPv4 Transport Address: ***********
	    Dual-Stack Capability TLV (0x0701), length: 4, Flags: [continue processing and don't forward if unknown]
	      Transport Connection Preference: IPv4
    5  2023-08-10 12:24:08.049677 IP (tos 0xc0, ttl 1, id 0, offset 0, flags [none], proto UDP (17), length 70)
    ********.646 > *********.646: 
	LDP, Label-Space-ID: ***********:0, pdu-length: 38
	  Hello Message (0x0100), length: 28, Message ID: 0x00000000, Flags: [ignore if unknown]
	    Common Hello Parameters TLV (0x0400), length: 4, Flags: [ignore and don't forward if unknown]
	      Hold Time: 15s, Flags: [Link Hello]
	    IPv4 Transport Address TLV (0x0401), length: 4, Flags: [ignore and don't forward if unknown]
	      IPv4 Transport Address: ***********
	    Dual-Stack Capability TLV (0x0701), length: 4, Flags: [continue processing and don't forward if unknown]
	      Transport Connection Preference: IPv4
    6  2023-08-10 12:24:10.017949 IP (tos 0xc0, ttl 1, id 0, offset 0, flags [none], proto UDP (17), length 70)
    ********.646 > *********.646: 
	LDP, Label-Space-ID: ***********:0, pdu-length: 38
	  Hello Message (0x0100), length: 28, Message ID: 0x00000038, Flags: [ignore if unknown]
	    Common Hello Parameters TLV (0x0400), length: 4, Flags: [ignore and don't forward if unknown]
	      Hold Time: 15s, Flags: [Link Hello]
	    IPv4 Transport Address TLV (0x0401), length: 4, Flags: [ignore and don't forward if unknown]
	      IPv4 Transport Address: ***********
	    Dual-Stack Capability TLV (0x0701), length: 4, Flags: [continue processing and don't forward if unknown]
	      Transport Connection Preference: IPv4
    7  2023-08-10 12:24:11.046122 IP (tos 0x0, ttl 255, id 1505, offset 0, flags [none], proto TCP (6), length 48)
    ***********.58321 > ***********.646: Flags [S], cksum 0x6df9 (correct), seq 110236, win 65535, options [mss 1420,nop,wscale 5], length 0
    8  2023-08-10 12:24:11.046149 IP (tos 0xc0, ttl 255, id 1506, offset 0, flags [none], proto TCP (6), length 81)
    ***********.58321 > ***********.646: Flags [P.], cksum 0x68c1 (correct), seq 110237:110278, ack 87765, win 3025, length 41
	LDP, Label-Space-ID: ***********:0, pdu-length: 37
	  Initialization Message (0x0200), length: 27, Message ID: 0x00000001, Flags: [ignore if unknown]
	    Common Session Parameters TLV (0x0500), length: 14, Flags: [ignore and don't forward if unknown]
	      Version: 1, Keepalive: 30s, Flags: [Downstream Unsolicited, Loop Detection Enabled]
	      Path Vector Limit 32, Max-PDU length: 0, Receiver Label-Space-ID ***********:0
	    Typed Wildcard FEC Capability TLV (0x050b), length: 1, Flags: [continue processing and don't forward if unknown]
	      Support
    9  2023-08-10 12:24:11.052281 IP (tos 0xc0, ttl 255, id 1507, offset 0, flags [none], proto TCP (6), length 58)
    ***********.58321 > ***********.646: Flags [P.], cksum 0x73a6 (correct), seq 41:59, ack 60, win 3023, length 18
   10  2023-08-10 12:24:11.103231 IP (tos 0xc0, ttl 255, id 1508, offset 0, flags [none], proto TCP (6), length 387)
    ***********.58321 > ***********.646: Flags [P.], cksum 0x46c6 (correct), seq 59:406, ack 60, win 3023, length 347
	LDP, Label-Space-ID: ***********:0, pdu-length: 56
	  Address Message (0x0300), length: 46, Message ID: 0x00000003, Flags: [ignore if unknown]
	    Address List TLV (0x0101), length: 38, Flags: [ignore and don't forward if unknown]
	      Address Family: IPv4, addresses ******** ******** ******** *********** *********** *********** *********** *********** ***********
	LDP, Label-Space-ID: ***********:0, pdu-length: 68
	  Address Message (0x0300), length: 58, Message ID: 0x00000004, Flags: [ignore if unknown]
	    Address List TLV (0x0101), length: 50, Flags: [ignore and don't forward if unknown]
	      Address Family: IPv6, addresses fe80::7850:c6ff:fec0:0 fe80::7850:c6ff:fec0:1 fe80::7850:c6ff:fec0:3
	LDP, Label-Space-ID: ***********:0, pdu-length: 211
	  Label Mapping Message (0x0400), length: 37, Message ID: 0x00000005, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 3
	    Hop Count TLV (0x0103), length: 1, Flags: [ignore and don't forward if unknown]
	      Hop Count: 1
	    Path Vector TLV (0x0104), length: 4, Flags: [ignore and don't forward if unknown]
	      Path Vector: ***********
	  Label Mapping Message (0x0400), length: 37, Message ID: 0x00000006, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 3
	    Hop Count TLV (0x0103), length: 1, Flags: [ignore and don't forward if unknown]
	      Hop Count: 1
	    Path Vector TLV (0x0104), length: 4, Flags: [ignore and don't forward if unknown]
	      Path Vector: ***********
	  Label Mapping Message (0x0400), length: 37, Message ID: 0x00000007, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 3
	    Hop Count TLV (0x0103), length: 1, Flags: [ignore and don't forward if unknown]
	      Hop Count: 1
	    Path Vector TLV (0x0104), length: 4, Flags: [ignore and don't forward if unknown]
	      Path Vector: ***********
	  Label Mapping Message (0x0400), length: 37, Message ID: 0x00000008, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 3
	    Hop Count TLV (0x0103), length: 1, Flags: [ignore and don't forward if unknown]
	      Hop Count: 1
	    Path Vector TLV (0x0104), length: 4, Flags: [ignore and don't forward if unknown]
	      Path Vector: ***********
	  Label Mapping Message (0x0400), length: 37, Message ID: 0x00000009, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 3
	    Hop Count TLV (0x0103), length: 1, Flags: [ignore and don't forward if unknown]
	      Hop Count: 1
	    Path Vector TLV (0x0104), length: 4, Flags: [ignore and don't forward if unknown]
	      Path Vector: ***********
   11  2023-08-10 12:24:11.929655 IP (tos 0xc0, ttl 255, id 1509, offset 0, flags [none], proto TCP (6), length 40)
    ***********.58321 > ***********.646: Flags [.], cksum 0x34ab (correct), ack 168, win 3020, length 0
   12  2023-08-10 12:24:11.929679 IP (tos 0xc0, ttl 255, id 1510, offset 0, flags [none], proto TCP (6), length 300)
    ***********.58321 > ***********.646: Flags [P.], cksum 0xbffe (correct), seq 406:666, ack 813, win 3000, length 260
	LDP, Label-Space-ID: ***********:0, pdu-length: 48
	  Label Release Message (0x0403), length: 38, Message ID: 0x0000000a, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20066
	    Status TLV (0x0300), length: 10, Flags: [ignore and don't forward if unknown]
	      Status Code: Loop Detected, Flags: [Advisory Notification and don't forward], causing Message ID: 0x0000000f, Message Type: Label Mapping
	LDP, Label-Space-ID: ***********:0, pdu-length: 48
	  Label Release Message (0x0403), length: 38, Message ID: 0x0000000b, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20066
	    Status TLV (0x0300), length: 10, Flags: [ignore and don't forward if unknown]
	      Status Code: Loop Detected, Flags: [Advisory Notification and don't forward], causing Message ID: 0x00000010, Message Type: Label Mapping
	LDP, Label-Space-ID: ***********:0, pdu-length: 48
	  Label Release Message (0x0403), length: 38, Message ID: 0x0000000c, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20066
	    Status TLV (0x0300), length: 10, Flags: [ignore and don't forward if unknown]
	      Status Code: Loop Detected, Flags: [Advisory Notification and don't forward], causing Message ID: 0x00000011, Message Type: Label Mapping
	LDP, Label-Space-ID: ***********:0, pdu-length: 48
	  Label Release Message (0x0403), length: 38, Message ID: 0x0000000d, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20066
	    Status TLV (0x0300), length: 10, Flags: [ignore and don't forward if unknown]
	      Status Code: Loop Detected, Flags: [Advisory Notification and don't forward], causing Message ID: 0x00000012, Message Type: Label Mapping
	LDP, Label-Space-ID: ***********:0, pdu-length: 48
	  Label Release Message (0x0403), length: 38, Message ID: 0x0000000e, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20066
	    Status TLV (0x0300), length: 10, Flags: [ignore and don't forward if unknown]
	      Status Code: Loop Detected, Flags: [Advisory Notification and don't forward], causing Message ID: 0x00000013, Message Type: Label Mapping
   13  2023-08-10 12:24:12.831754 IP (tos 0xc0, ttl 255, id 1520, offset 0, flags [none], proto TCP (6), length 415)
    ***********.58321 > ***********.646: Flags [P.], cksum 0xef2a (correct), seq 666:1041, ack 813, win 3000, length 375
	LDP, Label-Space-ID: ***********:0, pdu-length: 371
	  Label Mapping Message (0x0400), length: 41, Message ID: 0x0000000f, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20065
	    Hop Count TLV (0x0103), length: 1, Flags: [ignore and don't forward if unknown]
	      Hop Count: 2
	    Path Vector TLV (0x0104), length: 8, Flags: [ignore and don't forward if unknown]
	      Path Vector: ***********, ***********
	  Label Mapping Message (0x0400), length: 41, Message ID: 0x00000010, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20065
	    Hop Count TLV (0x0103), length: 1, Flags: [ignore and don't forward if unknown]
	      Hop Count: 2
	    Path Vector TLV (0x0104), length: 8, Flags: [ignore and don't forward if unknown]
	      Path Vector: ***********, ***********
	  Label Mapping Message (0x0400), length: 41, Message ID: 0x00000011, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20065
	    Hop Count TLV (0x0103), length: 1, Flags: [ignore and don't forward if unknown]
	      Hop Count: 2
	    Path Vector TLV (0x0104), length: 8, Flags: [ignore and don't forward if unknown]
	      Path Vector: ***********, ***********
	  Label Mapping Message (0x0400), length: 41, Message ID: 0x00000012, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20065
	    Hop Count TLV (0x0103), length: 1, Flags: [ignore and don't forward if unknown]
	      Hop Count: 2
	    Path Vector TLV (0x0104), length: 8, Flags: [ignore and don't forward if unknown]
	      Path Vector: ***********, ***********
	  Label Mapping Message (0x0400), length: 41, Message ID: 0x00000013, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20065
	    Hop Count TLV (0x0103), length: 1, Flags: [ignore and don't forward if unknown]
	      Hop Count: 2
	    Path Vector TLV (0x0104), length: 8, Flags: [ignore and don't forward if unknown]
	      Path Vector: ***********, ***********
	  Label Withdraw Message (0x0402), length: 24, Message ID: 0x00000014, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20066
	  Label Withdraw Message (0x0402), length: 24, Message ID: 0x00000015, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20066
	  Label Withdraw Message (0x0402), length: 24, Message ID: 0x00000016, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20066
	  Label Withdraw Message (0x0402), length: 24, Message ID: 0x00000017, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20066
	  Label Withdraw Message (0x0402), length: 24, Message ID: 0x00000018, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20066
   14  2023-08-10 12:24:13.028465 IP (tos 0xc0, ttl 1, id 0, offset 0, flags [none], proto UDP (17), length 70)
    ********.646 > *********.646: 
	LDP, Label-Space-ID: ***********:0, pdu-length: 38
	  Hello Message (0x0100), length: 28, Message ID: 0x00000000, Flags: [ignore if unknown]
	    Common Hello Parameters TLV (0x0400), length: 4, Flags: [ignore and don't forward if unknown]
	      Hold Time: 15s, Flags: [Link Hello]
	    IPv4 Transport Address TLV (0x0401), length: 4, Flags: [ignore and don't forward if unknown]
	      IPv4 Transport Address: ***********
	    Dual-Stack Capability TLV (0x0701), length: 4, Flags: [continue processing and don't forward if unknown]
	      Transport Connection Preference: IPv4
   15  2023-08-10 12:24:13.058561 IP (tos 0xc0, ttl 255, id 1524, offset 0, flags [none], proto TCP (6), length 40)
    ***********.58321 > ***********.646: Flags [.], cksum 0x2e0b (correct), ack 1263, win 2986, length 0
   16  2023-08-10 12:24:13.833287 IP (tos 0xc0, ttl 255, id 1530, offset 0, flags [none], proto TCP (6), length 255)
    ***********.58321 > ***********.646: Flags [P.], cksum 0x7e4f (correct), seq 1041:1256, ack 1263, win 2986, length 215
	LDP, Label-Space-ID: ***********:0, pdu-length: 211
	  Label Mapping Message (0x0400), length: 37, Message ID: 0x00000019, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20066
	    Hop Count TLV (0x0103), length: 1, Flags: [ignore and don't forward if unknown]
	      Hop Count: 0
	    Path Vector TLV (0x0104), length: 4, Flags: [ignore and don't forward if unknown]
	      Path Vector: ***********
	  Label Mapping Message (0x0400), length: 37, Message ID: 0x0000001a, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20066
	    Hop Count TLV (0x0103), length: 1, Flags: [ignore and don't forward if unknown]
	      Hop Count: 0
	    Path Vector TLV (0x0104), length: 4, Flags: [ignore and don't forward if unknown]
	      Path Vector: ***********
	  Label Mapping Message (0x0400), length: 37, Message ID: 0x0000001b, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20066
	    Hop Count TLV (0x0103), length: 1, Flags: [ignore and don't forward if unknown]
	      Hop Count: 0
	    Path Vector TLV (0x0104), length: 4, Flags: [ignore and don't forward if unknown]
	      Path Vector: ***********
	  Label Mapping Message (0x0400), length: 37, Message ID: 0x0000001c, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20066
	    Hop Count TLV (0x0103), length: 1, Flags: [ignore and don't forward if unknown]
	      Hop Count: 0
	    Path Vector TLV (0x0104), length: 4, Flags: [ignore and don't forward if unknown]
	      Path Vector: ***********
	  Label Mapping Message (0x0400), length: 37, Message ID: 0x0000001d, Flags: [ignore if unknown]
	    FEC TLV (0x0100), length: 8, Flags: [ignore and don't forward if unknown]
	      Prefix FEC (0x02): IPv4 prefix ***********/32
	    Generic Label TLV (0x0200), length: 4, Flags: [ignore and don't forward if unknown]
	      Label: 20066
	    Hop Count TLV (0x0103), length: 1, Flags: [ignore and don't forward if unknown]
	      Hop Count: 0
	    Path Vector TLV (0x0104), length: 4, Flags: [ignore and don't forward if unknown]
	      Path Vector: ***********
   17  2023-08-10 12:24:15.036359 IP (tos 0xc0, ttl 1, id 0, offset 0, flags [none], proto UDP (17), length 70)
    ********.646 > *********.646: 
	LDP, Label-Space-ID: ***********:0, pdu-length: 38
	  Hello Message (0x0100), length: 28, Message ID: 0x00000038, Flags: [ignore if unknown]
	    Common Hello Parameters TLV (0x0400), length: 4, Flags: [ignore and don't forward if unknown]
	      Hold Time: 15s, Flags: [Link Hello]
	    IPv4 Transport Address TLV (0x0401), length: 4, Flags: [ignore and don't forward if unknown]
	      IPv4 Transport Address: ***********
	    Dual-Stack Capability TLV (0x0701), length: 4, Flags: [continue processing and don't forward if unknown]
	      Transport Connection Preference: IPv4
   18  2023-08-10 12:24:18.042950 IP (tos 0xc0, ttl 1, id 0, offset 0, flags [none], proto UDP (17), length 70)
    ********.646 > *********.646: 
	LDP, Label-Space-ID: ***********:0, pdu-length: 38
	  Hello Message (0x0100), length: 28, Message ID: 0x00000000, Flags: [ignore if unknown]
	    Common Hello Parameters TLV (0x0400), length: 4, Flags: [ignore and don't forward if unknown]
	      Hold Time: 15s, Flags: [Link Hello]
	    IPv4 Transport Address TLV (0x0401), length: 4, Flags: [ignore and don't forward if unknown]
	      IPv4 Transport Address: ***********
	    Dual-Stack Capability TLV (0x0701), length: 4, Flags: [continue processing and don't forward if unknown]
	      Transport Connection Preference: IPv4
   19  2023-08-10 12:24:20.052540 IP (tos 0xc0, ttl 1, id 0, offset 0, flags [none], proto UDP (17), length 70)
    ********.646 > *********.646: 
	LDP, Label-Space-ID: ***********:0, pdu-length: 38
	  Hello Message (0x0100), length: 28, Message ID: 0x00000038, Flags: [ignore if unknown]
	    Common Hello Parameters TLV (0x0400), length: 4, Flags: [ignore and don't forward if unknown]
	      Hold Time: 15s, Flags: [Link Hello]
	    IPv4 Transport Address TLV (0x0401), length: 4, Flags: [ignore and don't forward if unknown]
	      IPv4 Transport Address: ***********
	    Dual-Stack Capability TLV (0x0701), length: 4, Flags: [continue processing and don't forward if unknown]
	      Transport Connection Preference: IPv4
   20  2023-08-10 12:24:21.030795 IP (tos 0xc0, ttl 255, id 1534, offset 0, flags [none], proto TCP (6), length 58)
    ***********.58321 > ***********.646: Flags [P.], cksum 0x6a3d (correct), seq 1256:1274, ack 1263, win 2986, length 18
   21  2023-08-10 12:24:21.087477 IP (tos 0xc0, ttl 255, id 1535, offset 0, flags [none], proto TCP (6), length 40)
    ***********.58321 > ***********.646: Flags [.], cksum 0x2d11 (correct), ack 1281, win 2985, length 0
   22  2023-08-10 12:24:23.011577 IP (tos 0xc0, ttl 1, id 0, offset 0, flags [none], proto UDP (17), length 70)
    ********.646 > *********.646: 
	LDP, Label-Space-ID: ***********:0, pdu-length: 38
	  Hello Message (0x0100), length: 28, Message ID: 0x00000000, Flags: [ignore if unknown]
	    Common Hello Parameters TLV (0x0400), length: 4, Flags: [ignore and don't forward if unknown]
	      Hold Time: 15s, Flags: [Link Hello]
	    IPv4 Transport Address TLV (0x0401), length: 4, Flags: [ignore and don't forward if unknown]
	      IPv4 Transport Address: ***********
	    Dual-Stack Capability TLV (0x0701), length: 4, Flags: [continue processing and don't forward if unknown]
	      Transport Connection Preference: IPv4
