    1  18:10:03.368228 IP (tos 0x0, ttl 128, id 0, offset 0, flags [DF], proto RSVP (46), length 40)
    ************* > ***********: 
	RSVPv1 Hello Message (20), Flags: [none], length: 20, ttl: 64, checksum: 0x98ce
	  ERO Object (20) Flags: [reject if unknown], Class-Type: IPv4 (1), length: 8
	    Subobject Type: Label, length 0
	    ERROR: zero length ERO subtype
	  ERROR: object header too short 0 < 4
    2  18:10:03.425201 IP (tos 0x0, ttl 64, id 0, offset 0, flags [DF], proto RSVP (46), length 40)
    ************** > ***********: 
	RSVPv1 Hello Message (20), Flags: [none], length: 20, ttl: 64, checksum: 0x98ce
	  ERO Object (20) Flags: [reject if unknown], Class-Type: IPv4 (1), length: 8
	    Subobject Type: Label, length 0
	    ERROR: zero length ERO subtype
	  ERROR: object header too short 0 < 4
    3  18:10:03.485172 IP (tos 0x0, ttl 64, id 0, offset 0, flags [DF], proto RSVP (46), length 40)
    *********** > ***********: 
	RSVPv1 Hello Message (20), Flags: [none], length: 20, ttl: 128, checksum: 0x58ce
	  ERO Object (20) Flags: [reject if unknown], Class-Type: IPv4 (1), length: 8
	    Subobject Type: Label, length 0
	    ERROR: zero length ERO subtype
	  ERROR: object header too short 0 < 4
    4  18:10:03.545141 IP (tos 0x0, ttl 64, id 0, offset 0, flags [DF], proto RSVP (46), length 40)
    ************* > ***********: 
	RSVPv1 Hello Message (20), Flags: [none], length: 20, ttl: 128, checksum: 0x58ce
	  ERO Object (20) Flags: [reject if unknown], Class-Type: IPv4 (1), length: 8
	    Subobject Type: Label, length 0
	    ERROR: zero length ERO subtype
	  ERROR: object header too short 0 < 4
    5  18:10:03.605110 IP (tos 0x0, ttl 64, id 0, offset 0, flags [DF], proto RSVP (46), length 40)
    ************* > ***********: 
	RSVPv1 Hello Message (20), Flags: [none], length: 20, ttl: 128, checksum: 0x58ce
	  ERO Object (20) Flags: [reject if unknown], Class-Type: IPv4 (1), length: 8
	    Subobject Type: Label, length 0
	    ERROR: zero length ERO subtype
	  ERROR: object header too short 0 < 4
