#!/usr/bin/env perl

# Copyright (c) 2024 The Tcpdump Group
# All rights reserved.
# SPDX-License-Identifier: BSD-2-Clause
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1. Redistributions of source code must retain the above copyright
#    notice, this list of conditions and the following disclaimer.
# 2. Redistributions in binary form must reproduce the above copyright
#    notice, this list of conditions and the following disclaimer in the
#    documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
# "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
# LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
# FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
# COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
# INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING,
# BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
# CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
# LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
# ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
# POSSIBILITY OF SUCH DAMAGE.

require 5.8.4; # Solaris 10
use sigtrap qw(die normal-signals);
use strict;
use warnings FATAL => qw(uninitialized);
use Getopt::Long;
use Time::HiRes;
use Config;
use FindBin;
require $FindBin::RealBin . '/TEST' . ($Config{useithreads} ? 'mt' : 'st') . '.pm';
require $FindBin::RealBin . '/TESTlib.pm';
# TESTlib.pm
use subs qw(
	file_get_contents
	file_put_contents
	get_diff_flags
	init_tmpdir
	mytmpfile
	read_config_h
	result_failed
	result_passed
	result_skipped
	result_timed_out
	run_skip_test
	skip_config_def1
	skip_config_have_decl
	skip_config_undef
	skip_os
	skip_os_not
	string_in_file
	test_and_report
);

use constant SAVEFILE_DIR => $FindBin::RealBin . '/../tests/filter/';
my $filename_expected = 'expected.txt';
my $filename_stdout = 'stdout.txt';
my $filename_filter = 'filter.txt';
my $filename_diags = 'diags.txt';

# See RFC 6761 Section 6.4.
my $nonexistent = 'host.invalid';

use constant {
	EX_OK => 0,
	# On all platforms where timeout(1) is available it exits with status 124
	# if the command timed out.
	TIMED_OUT => 124,
	EX_USAGE => 64,
	# In this libpcap version filtertest exits with EX_DATAERR if the user input
	# is invalid and with other status codes for other (memory/file/etc) error
	# conditions.
	EX_DATAERR => 65,
};

# Set these later only if running any tests.
my $diff_flags;
my $timeout_bin;
my $test_timeout;
my $filtertest;

sub usage_text {
	my $detailed = shift;
	my $myname = $FindBin::Script;

	my $ret = "Usage: ${myname} [--passed] [--skipped]
       (run all tests)
  or:  ${myname} --list
       (print all test labels)
  or:  ${myname} --one <test_label>
       (run one test only and print the details)
  or:  ${myname} --config
       (print the parsed contents of config.h)
  or:  ${myname} --help
       (print the detailed help screen)

Options:
  --passed        print the passed tests details
  --skipped       print the skipped tests details
";
	return $ret unless $detailed;
	$ret .= "
Set FILTERTEST_TIMEOUT to 0 to disable the timeout completely or to any other
value to override the default timeout.  The timeout applies to every test.
If TIMEOUT_BIN is not set, the timeout applies iff the default \"timeout\"
binary works.  If it is set (on macOS there is no default \"timeout\" and the
binary from GNU coreutils package may be available as \"gtimeout\" only), the
custom binary must work.

FILTERTEST_BIN and CONFIG_H allow to specify custom paths to respective files
if the current working directory is not the directory where the build output
files go to.  Otherwise by default this script finds the files for both
Autoconf and CMake, both in-tree and out-of-tree builds.

TIMEOUT_BIN allows to specify the path to a timeout(1) binary.

TESTRUN_JOBS allows to specify the number of tester threads (1 by default).

TESTRUN_SHORT=1 generates a shorter list of tests (not recommended unless the
host is very slow and time is a factor).
";
	return $ret;
}

my $config_h = defined $ENV{CONFIG_H} ? $ENV{CONFIG_H} : './config.h';
my $only_one = undef;
my $only_list = 0;
my $print_passed = 0;
my $print_skipped = 0;
my $only_short = defined $ENV{TESTRUN_SHORT} ? $ENV{TESTRUN_SHORT} : 0;
if (! GetOptions (
	'one=s' => \$only_one,
	'list' => \$only_list,
	'passed' => \$print_passed,
	'skipped' => \$print_skipped,
	'config' => sub {
		my %config = read_config_h $config_h;
		printf "%-50s %s\n", $_, $config{$_} foreach sort keys %config;
		exit EX_OK;
	},
	'help' => sub {print STDOUT usage_text 1; exit EX_OK;},
)) {
	print STDERR usage_text 0;
	exit EX_USAGE;
};
$print_passed = $print_skipped = 1 if $only_one;

# Initialize now so that the skip functions in TESTlib.pm (and therefore the
# test declarations below) work as intended.
read_config_h ($config_h);

# All accept tests require that BDEBUG is not set.
die 'ERROR: This script is not compatible with optimizer debugging.' if skip_config_def1 'BDEBUG';

# Skip unless the file exists and is readable and contains every given line
# (less any duplicates) at least once in any order.
sub skip_unless_file_contains_lines {
	my $filename = shift;
	my %notfound = map {$_ => 1} @_;
	# In Perl 5.8.4 "s///" does not have the "r" option.
	my $basename = $filename;
	$basename =~ s|^.*[/\\]||o;
	my $skip = "configure the $basename file";

	open FH, '<', $filename or return $skip;
	while (<FH>) {
		s/[\r\n]*$//o; # chomp() removes LF, but not CR, even on Windows.
		next unless exists $notfound{$_};
		delete $notfound{$_};
		last unless scalar keys %notfound;
	}
	close FH or die "failed closing '$filename'";
	return scalar (keys %notfound) ? $skip : '';
}

# Skip if running on Linux with musl libc.
sub skip_musl_libc {
	return skip_os ('linux') &&
		skip_config_undef ('HAVE_GLIBC') &&
		skip_config_undef ('HAVE_UCLIBC') &&
		'musl libc';
}

# libpcap.test is a domain subject to RFC 6761 Section 6.2.
#
# Each of the seven hostnames under host123.libpcap.test has 1..3 addresses
# (at most one of {Ethernet, IPv4, IPv6} each, at least one in total):
# * eth-ipv4-ipv6.host123.libpcap.test
# * eth-ipv4-noipv6.host123.libpcap.test
# * eth-noipv4-ipv6.host123.libpcap.test
# * eth-noipv4-noipv6.host123.libpcap.test
# * noeth-ipv4-ipv6.host123.libpcap.test
# * noeth-ipv4-noipv6.host123.libpcap.test
# * noeth-noipv4-ipv6.host123.libpcap.test
#
# To add tests that require hostnames with a different arrangement of
# addresses, consider adding similar mnemonic domains.

# The MAC address is in the locally-administered DECnet OUI space.
# These 4 lines can be copied into ethers(5) directly:
my @ethers_lines = split /\n/, <<EOF;
aa:00:04:00:14:0e eth-noipv4-noipv6.host123.libpcap.test
aa:00:04:00:14:0e eth-noipv4-ipv6.host123.libpcap.test
aa:00:04:00:14:0e eth-ipv4-noipv6.host123.libpcap.test
aa:00:04:00:14:0e eth-ipv4-ipv6.host123.libpcap.test
EOF

# The IPv6 address is subject to RFC4193.
# These 8 lines can be copied into hosts(5) directly:
my @hosts_lines = split /\n/, <<EOF;
*********** noeth-ipv4-noipv6.host123.libpcap.test
*********** noeth-ipv4-ipv6.host123.libpcap.test
*********** eth-ipv4-noipv6.host123.libpcap.test
*********** eth-ipv4-ipv6.host123.libpcap.test
fd00:a1b2:c3d4::1020:3040:5060:7080 noeth-noipv4-ipv6.host123.libpcap.test
fd00:a1b2:c3d4::1020:3040:5060:7080 noeth-ipv4-ipv6.host123.libpcap.test
fd00:a1b2:c3d4::1020:3040:5060:7080 eth-noipv4-ipv6.host123.libpcap.test
fd00:a1b2:c3d4::1020:3040:5060:7080 eth-ipv4-ipv6.host123.libpcap.test
EOF

# Dotted-quad is the only portable syntax common to all implementations of
# getnetbyname().
# These 3 lines can be copied into networks(5) directly (except on OpenBSD):
my @networks_lines = split /\n/, <<EOF;
net-10-0-0-0.libpcap.test 10.0.0.0
net-10-20-0-0.libpcap.test *********
net-10-20-30-0.libpcap.test **********
EOF
# These 3 lines can be copied into hosts(5) directly (on OpenBSD only):
my @networks_lines_swapped = split /\n/, <<EOF;
10.0.0.0 net-10-0-0-0.libpcap.test
********* net-10-20-0-0.libpcap.test
********** net-10-20-30-0.libpcap.test
EOF

# Skip if /etc/ethers (or the equivalent) has not been configured for tests.
#
# Do not use getent(1): it works, but experiences some portability issues and
# does not always test the same code paths as what libpcap uses.
my $cached_skip_no_ethers;
sub skip_no_ethers {
	$cached_skip_no_ethers =
		# pcap_ether_hostton() currently cannot guess a suitable
		# location for the libpcap-only ethers(5).
		skip_os ('msys') ||
		skip_unless_file_contains_lines (
			$^O eq 'haiku' ? '/boot/system/settings/network/ethers' :
			'/etc/ethers',
			@ethers_lines
		) unless defined $cached_skip_no_ethers;
	return $cached_skip_no_ethers;
}

# Same as the above, plus require the platform to treat hostnames in
# ethers(5) correctly (case-insensitive).
sub skip_no_ethers_casecmp {
	return skip_no_ethers ||
		# pcap_ether_hostton() uses strcmp().
		skip_musl_libc ||
		skip_os ('haiku') ||
		# On illumos and possibly Solaris 11.4 ether_hostton() initially
		# fails to find any upper-case hostnames, then after a few minutes
		# it finds some or all of them, or sometimes it does not.
		skip_os ('solaris') ||
		# ether_hostton() uses strcmp().
		skip_os ('freebsd') ||
		skip_os ('netbsd') ||
		skip_os ('openbsd');
}

# Skip if /etc/hosts (or the equivalent) has not been configured for tests.
#
# Do not use getent(1): it experiences too many portability issues (especially
# with dual-stack hostnames) to be practicable.
my $cached_skip_no_hosts;
sub skip_no_hosts {
	return '%SYSTEMROOT% not set' if $^O eq 'msys' && ! defined $ENV{SYSTEMROOT};
	$cached_skip_no_hosts = skip_unless_file_contains_lines (
		$^O eq 'haiku' ? '/boot/system/settings/network/hosts' :
		$^O eq 'solaris' ? '/etc/inet/hosts' :
		$^O eq 'msys' ? "${ENV{SYSTEMROOT}}\\system32\\drivers\\etc\\hosts" :
		'/etc/hosts',
		@hosts_lines
	) unless defined $cached_skip_no_hosts;
	return $cached_skip_no_hosts;
}

# Same as the above, plus require the platform to treat hostnames in
# hosts(5) correctly (case-insensitive).
sub skip_no_hosts_casecmp {
	return skip_no_hosts ||
		# getaddrinfo() uses strcmp() on /etc/hosts.
		skip_musl_libc();
}

# Skip if /etc/networks (or the equivalent) has not been configured for tests.
my $cached_skip_no_networks;
sub skip_no_networks {
	$cached_skip_no_networks =
		# Does not implement getnetbyname().
		skip_os ('msys') ||
		# getnetbyname() is a no-op.
		skip_musl_libc() ||
		skip_unless_file_contains_lines (
			$^O eq 'solaris' ? '/etc/inet/networks' :
			$^O eq 'haiku' ? '/etc/networks' : # May change, see haiku bug report 19432.
			$^O eq 'openbsd' ? '/etc/hosts' : # (sic)
			'/etc/networks',
			$^O eq 'openbsd' ? @networks_lines_swapped : @networks_lines
		) unless defined $cached_skip_no_networks;
	return $cached_skip_no_networks;
}

sub skip_no_networks_casecmp {
	return skip_no_networks ||
		skip_os ('solaris');
}

sub skip_big_endian {
	return pack ('S', 0x4245) eq 'BE' ? 'big-endian' : '';
}

sub skip_little_endian {
	return pack ('S', 0x454c) eq 'LE' ? 'little-endian' : '';
}

# In accept_blocks the top-level keys are test block names.  Each test block
# defines one or more tests.  When possible, a test block name should be easy
# to relate with the main filter expression, for example, ip_multicast for
# "ip multicast" etc.  However, because in Perl hashes the keys are not ordered,
# sometimes the easiest way to group the tests in the alphabetically-sorted
# output is to use an artificial prefix, for example, mtp2_ for "fisu", "lssu",
# "msu" etc.  The top-level values are in turn hashes, where the keys have the
# following meaning:
#
# * DLT (mandatory, string): the name of the DLT to use for the test
# * aliases (mandatory, array of strings): must contain at least one string,
#   each string is a filter expression and must produce exactly the same
#   bytecode (applies to "opt" and "unopt" separately)
# * snaplen (optional, int): the snapshot length to use for the test
# * opt (optional, [multi-line] string): the expected optimised bytecode
# * unopt (optional, [multi-line] string): the expected unoptimised bytecode
# * skip (optional, string): if defined and is not equal to an empty string,
#   causes the test to skip using the string as the reason
# * linuxext (optional, int): if defined and is equal to 1, use Linux BPF
#   extensions.
# * netmask (optional, string, dotted-quad): the netmask for pcap_compile()
#
# At least one of "opt" and "unopt" must be defined in each accept test block.

my @accept_blocks = (
	{
		name => 'empty',
		DLT => 'EN10MB',
		aliases => [''],
		unopt => '
			(000) ret      #262144
			',
	},

	{
		name => 'greater',
		DLT => 'RAW',
		snaplen => 200,
		aliases => [
			'greater 100',
			'len >= 100',
			'length >= 100',
		],
		# Only the optimized bytecode is equivalent!
		opt => '
			(000) ld       #pktlen
			(001) jge      #0x64            jt 2	jf 3
			(002) ret      #200
			(003) ret      #0
			',
	}, # greater
	{
		name => 'less',
		DLT => 'RAW',
		snaplen => 200,
		aliases => [
			'less 200',
			'len <= 200',
			'length <= 200',
		],
		# Only the optimized bytecode is equivalent!
		opt => '
			(000) ld       #pktlen
			(001) jgt      #0xc8            jt 2	jf 3
			(002) ret      #0
			(003) ret      #200
			',
	}, # less

	{
		name => 'link_index_byte',
		DLT => 'EN10MB',
		aliases => [
			'link[5] == 0x12',
			'ether[5] == 0x12',
			'fddi[5] == 0x12',
			'ppp[5] == 0x12',
			'slip[5] == 0x12',
			'tr[5] == 0x12',
			'wlan[5] == 0x12',
			'ether[5:1] == 0x12',
			'fddi[5:1] == 0x12',
			'ppp[5:1] == 0x12',
			'slip[5:1] == 0x12',
			'tr[5:1] == 0x12',
			'wlan[5:1] == 0x12',
		],
		opt => '
			(000) ldb      [5]
			(001) jeq      #0x12            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_index_byte
	{
		name => 'link_index_halfword',
		DLT => 'EN10MB',
		aliases => [
			'link[7:2] == 0x1234',
			'ether[7:2] == 0x1234',
			'fddi[7:2] == 0x1234',
			'ppp[7:2] == 0x1234',
			'slip[7:2] == 0x1234',
			'tr[7:2] == 0x1234',
			'wlan[7:2] == 0x1234',
		],
		opt => '
			(000) ldh      [7]
			(001) jeq      #0x1234          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_index_halfword
	{
		name => 'link_index_word',
		DLT => 'EN10MB',
		aliases => [
			'link[10:4] == 0x12345678',
			'ether[10:4] == 0x12345678',
			'fddi[10:4] == 0x12345678',
			'ppp[10:4] == 0x12345678',
			'slip[10:4] == 0x12345678',
			'tr[10:4] == 0x12345678',
			'wlan[10:4] == 0x12345678',
		],
		opt => '
			(000) ld       [10]
			(001) jeq      #0x12345678      jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_index_word

	{
		name => 'icmp_index_icmptype',
		DLT => 'EN10MB',
		snaplen => 16000,
		# Verify that the named offset resolves to the same constant and that the
		# protocol supports index operation.
		aliases => [
			'icmp[icmptype] != 0xff',
			'icmp[0] != 0xff',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x800           jt 2	jf 10
			(002) ldb      [23]
			(003) jeq      #0x1             jt 4	jf 10
			(004) ldh      [20]
			(005) jset     #0x1fff          jt 10	jf 6
			(006) ldxb     4*([14]&0xf)
			(007) ldb      [x + 14]
			(008) jeq      #0xff            jt 10	jf 9
			(009) ret      #16000
			(010) ret      #0
			',
	}, # icmp_index_icmptype
	{
		name => 'icmp_index_icmpcode',
		DLT => 'EN10MB',
		snaplen => 16000,
		# Same as for icmp[icmptype] above.
		aliases => [
			'icmp[icmpcode] != 0xff',
			'icmp[1] != 0xff',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x800           jt 2	jf 10
			(002) ldb      [23]
			(003) jeq      #0x1             jt 4	jf 10
			(004) ldh      [20]
			(005) jset     #0x1fff          jt 10	jf 6
			(006) ldxb     4*([14]&0xf)
			(007) ldb      [x + 15]
			(008) jeq      #0xff            jt 10	jf 9
			(009) ret      #16000
			(010) ret      #0
			',
	}, # icmp_index_icmpcode
	{
		name => 'icmp6_index_icmp6type',
		DLT => 'IPV6',
		snaplen => 10000,
		# Same as for icmp[icmptype] above.
		aliases => [
			'icmp6[icmp6type] != 0xff',
			'icmp6[0] != 0xff',
		],
		opt => '
			(000) ld       #0x0
			(001) ldb      [6]
			(002) jeq      #0x3a            jt 3	jf 6
			(003) ldb      [40]
			(004) jeq      #0xff            jt 6	jf 5
			(005) ret      #10000
			(006) ret      #0
			',
	}, # icmp6_index_icmp6type
	{
		name => 'icmp6_index_icmp6code',
		DLT => 'IPV6',
		snaplen => 10000,
		# Same as for icmp[icmptype] above.
		aliases => [
			'icmp6[icmp6code] != 0xff',
			'icmp6[1] != 0xff',
		],
		opt => '
			(000) ld       #0x0
			(001) ldb      [6]
			(002) jeq      #0x3a            jt 3	jf 6
			(003) ldb      [41]
			(004) jeq      #0xff            jt 6	jf 5
			(005) ret      #10000
			(006) ret      #0
			',
	}, # icmp6_index_icmp6code
	{
		name => 'udp_index_halfword',
		DLT => 'RAW',
		snaplen => 65535,
		# The implementation is IPv4-only.
		aliases => ['udp[8:2] == 0xabcd'],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 11
			(003) ldb      [9]
			(004) jeq      #0x11            jt 5	jf 11
			(005) ldh      [6]
			(006) jset     #0x1fff          jt 11	jf 7
			(007) ldxb     4*([0]&0xf)
			(008) ldh      [x + 8]
			(009) jeq      #0xabcd          jt 10	jf 11
			(010) ret      #65535
			(011) ret      #0
			',
	}, # udp_index_halfword
	{
		name => 'tcp_index_byte',
		DLT => 'RAW',
		snaplen => 262144,
		# Same as for icmp[icmptype] above.
		# The implementation is IPv4-only.
		aliases => [
			'tcp[tcpflags] == 0xFF',
			'tcp[13] == 0xFF',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 11
			(003) ldb      [9]
			(004) jeq      #0x6             jt 5	jf 11
			(005) ldh      [6]
			(006) jset     #0x1fff          jt 11	jf 7
			(007) ldxb     4*([0]&0xf)
			(008) ldb      [x + 13]
			(009) jeq      #0xff            jt 10	jf 11
			(010) ret      #262144
			(011) ret      #0
			',
	}, # tcp_index_byte
	{
		name => 'linux_sll_inbound',
		DLT => 'LINUX_SLL',
		snaplen => 65535,
		aliases => ['inbound'],
		unopt => '
			(000) ldh      [0]
			(001) jeq      #0x4             jt 2	jf 3
			(002) ret      #0
			(003) ret      #65535
			',
	}, # linux_sll_inbound
	{
		name => 'linux_sll_outbound',
		DLT => 'LINUX_SLL',
		snaplen => 65535,
		aliases => ['outbound'],
		unopt => '
			(000) ldh      [0]
			(001) jeq      #0x4             jt 2	jf 3
			(002) ret      #65535
			(003) ret      #0
			',
	}, # linux_sll_outbound
	{
		name => 'linux_sll2_inbound',
		DLT => 'LINUX_SLL2',
		snaplen => 65535,
		aliases => ['inbound'],
		unopt => '
			(000) ldb      [10]
			(001) jeq      #0x4             jt 2	jf 3
			(002) ret      #0
			(003) ret      #65535
			',
	}, # linux_sll2_inbound
	{
		name => 'linux_sll2_outbound',
		DLT => 'LINUX_SLL2',
		snaplen => 65535,
		aliases => ['outbound'],
		unopt => '
			(000) ldb      [10]
			(001) jeq      #0x4             jt 2	jf 3
			(002) ret      #65535
			(003) ret      #0
			',
	}, # linux_sll2_outbound
	{
		name => 'linux_sll2_ifindex',
		DLT => 'LINUX_SLL2',
		snaplen => 65535,
		aliases => ['ifindex 7'],
		unopt => '
			(000) ld       [4]
			(001) jeq      #0x7             jt 2	jf 3
			(002) ret      #65535
			(003) ret      #0
			',
	}, # linux_sll2_ifindex
	{
		name => 'slip_inbound',
		DLT => 'SLIP',
		snaplen => 100,
		aliases => ['inbound'],
		unopt => '
			(000) ldb      [0]
			(001) jeq      #0x0             jt 2	jf 3
			(002) ret      #100
			(003) ret      #0
			',
	}, # slip_inbound
	{
		name => 'slip_outbound',
		DLT => 'SLIP',
		snaplen => 100,
		aliases => ['outbound'],
		unopt => '
			(000) ldb      [0]
			(001) jeq      #0x1             jt 2	jf 3
			(002) ret      #100
			(003) ret      #0
			',
	}, # slip_outbound
	{
		name => 'ipnet_inbound',
		DLT => 'IPNET',
		snaplen => 1000,
		aliases => ['inbound'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x2             jt 2	jf 3
			(002) ret      #1000
			(003) ret      #0
			',
	}, # ipnet_inbound
	{
		name => 'ipnet_outbound',
		DLT => 'IPNET',
		snaplen => 1000,
		aliases => ['outbound'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x1             jt 2	jf 3
			(002) ret      #1000
			(003) ret      #0
			',
	}, # ipnet_outbound
	{
		name => 'pflog_inbound',
		DLT => 'PFLOG',
		aliases => ['inbound'],
		unopt => '
			(000) ldb      [60]
			(001) jeq      #0x1             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_inbound
	{
		name => 'pflog_outbound',
		DLT => 'PFLOG',
		aliases => ['outbound'],
		unopt => '
			(000) ldb      [60]
			(001) jeq      #0x2             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_outbound
	{
		name => 'ppp_pppd_inbound',
		DLT => 'PPP_PPPD',
		aliases => ['inbound'],
		unopt => '
			(000) ldb      [0]
			(001) jeq      #0x0             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # ppp_pppd_inbound
	{
		name => 'ppp_pppd_oubound',
		DLT => 'PPP_PPPD',
		aliases => ['outbound'],
		unopt => '
			(000) ldb      [0]
			(001) jeq      #0x1             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # ppp_pppd_oubound
	{
		name => 'juniper_mfr_inbound',
		DLT => 'JUNIPER_MFR',
		aliases => ['inbound'],
		unopt => '
			(000) ldb      [3]
			(001) and      #0x1
			(002) jeq      #0x1             jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # juniper_mfr_inbound
	{
		name => 'juniper_mfr_outbound',
		DLT => 'JUNIPER_MFR',
		aliases => ['outbound'],
		unopt => '
			(000) ldb      [3]
			(001) and      #0x1
			(002) jeq      #0x0             jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # juniper_mfr_outbound
	{
		name => 'inbound_linuxext',
		skip => skip_os_not ('linux'),
		linuxext => 1,
		DLT => 'EN10MB',
		aliases => ['inbound'],
		unopt => '
			(000) ldh      [type]
			(001) jeq      #0x4             jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # inbound_linuxext
	{
		name => 'outbound_linuxext',
		skip => skip_os_not ('linux'),
		linuxext => 1,
		DLT => 'EN10MB',
		aliases => ['outbound'],
		unopt => '
			(000) ldh      [type]
			(001) jeq      #0x4             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # outbound_linuxext
	{
		name => 'ifindex_linuxext',
		skip => skip_os_not ('linux'),
		linuxext => 1,
		DLT => 'EN10MB',
		aliases => ['ifindex 10'],
		unopt => '
			(000) ld       [ifidx]
			(001) jeq      #0xa             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # ifindex_linuxext

	{
		name => 'pflog_ifname',
		DLT => 'PFLOG',
		aliases => [
			'ifname testname1',
			'on testname1',
		],
		unopt => '
			(000) ld       [9]
			(001) jeq      #0x616d6531      jt 2	jf 7
			(002) ld       [5]
			(003) jeq      #0x6573746e      jt 4	jf 7
			(004) ldb      [4]
			(005) jeq      #0x74            jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # pflog_ifname
	{
		name => 'pflog_rnr',
		DLT => 'PFLOG',
		aliases => [
			'rnr 0x456789',
			'rulenum 0x456789',
		],
		unopt => '
			(000) ld       [36]
			(001) jeq      #0x456789        jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_rnr
	{
		name => 'pflog_reason_match',
		DLT => 'PFLOG',
		aliases => ['reason match'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0x0             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_match
	{
		name => 'pflog_reason_bad_offset',
		DLT => 'PFLOG',
		aliases => ['reason bad-offset'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0x1             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_bad_offset
	{
		name => 'pflog_reason_fragment',
		DLT => 'PFLOG',
		aliases => ['reason fragment'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0x2             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_fragment
	{
		name => 'pflog_reason_short',
		DLT => 'PFLOG',
		aliases => ['reason short'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0x3             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_short
	{
		name => 'pflog_reason_normalize',
		DLT => 'PFLOG',
		aliases => ['reason normalize'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0x4             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_normalize
	{
		name => 'pflog_reason_memory',
		DLT => 'PFLOG',
		aliases => ['reason memory'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0x5             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_memory
	{
		name => 'pflog_reason_bad_timestamp',
		DLT => 'PFLOG',
		aliases => ['reason bad-timestamp'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0x6             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_bad_timestamp
	{
		name => 'pflog_reason_congestion',
		DLT => 'PFLOG',
		aliases => ['reason congestion'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0x7             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_congestion
	{
		name => 'pflog_reason_ip_option',
		DLT => 'PFLOG',
		aliases => ['reason ip-option'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0x8             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_ip_option
	{
		name => 'pflog_reason_proto_cksum',
		DLT => 'PFLOG',
		aliases => ['reason proto-cksum'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0x9             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_proto_cksum
	{
		name => 'pflog_reason_state_mismatch',
		DLT => 'PFLOG',
		aliases => ['reason state-mismatch'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0xa             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_state_mismatch
	{
		name => 'pflog_reason_state_insert',
		DLT => 'PFLOG',
		aliases => ['reason state-insert'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0xb             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_state_insert
	{
		name => 'pflog_reason_state_limit',
		DLT => 'PFLOG',
		aliases => ['reason state-limit'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0xc             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_state_limit
	{
		name => 'pflog_reason_src_limit',
		DLT => 'PFLOG',
		aliases => ['reason src-limit'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0xd             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_src_limit
	{
		name => 'pflog_reason_synproxy',
		DLT => 'PFLOG',
		aliases => ['reason synproxy'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0xe             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_synproxy
	{
		name => 'pflog_reason_map_failed',
		skip => skip_os_not ('freebsd'),
		DLT => 'PFLOG',
		aliases => ['reason map-failed'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0xf             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_map_failed
	{
		name => 'pflog_reason_state_locked',
		skip => skip_os_not ('netbsd'),
		DLT => 'PFLOG',
		aliases => ['reason state-locked'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0xf             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_state_locked
	{
		name => 'pflog_reason_translate',
		skip => skip_os_not ('openbsd'),
		DLT => 'PFLOG',
		aliases => ['reason translate'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0xf             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_translate
	{
		name => 'pflog_reason_no_route',
		skip => skip_os_not ('openbsd'),
		DLT => 'PFLOG',
		aliases => ['reason no-route'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0x10            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_no_route
	{
		name => 'pflog_reason_dummynet',
		skip => skip_os_not ('darwin'),
		DLT => 'PFLOG',
		aliases => ['reason dummynet'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0xf             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_reason_dummynet
	{
		name => 'pflog_rset',
		DLT => 'PFLOG',
		aliases => [
			'rset testruleset2',
			'ruleset testruleset2',
		],
		unopt => '
			(000) ld       [28]
			(001) jeq      #0x73657432      jt 2	jf 7
			(002) ld       [24]
			(003) jeq      #0x72756c65      jt 4	jf 7
			(004) ld       [20]
			(005) jeq      #0x74657374      jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # pflog_rset
	{
		name => 'pflog_srnr',
		DLT => 'PFLOG',
		aliases => [
			'srnr 123',
			'subrulenum 123',
		],
		unopt => '
			(000) ld       [40]
			(001) jeq      #0x7b            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_srnr
	{
		name => 'pflog_action_pass',
		DLT => 'PFLOG',
		aliases => [
			'action pass',
			'action accept',
		],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0x0             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_pass
	{
		name => 'pflog_action_block',
		DLT => 'PFLOG',
		aliases => [
			'action block',
			'action drop',
		],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0x1             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_block
	{
		name => 'pflog_action_scrub',
		DLT => 'PFLOG',
		aliases => ['action scrub'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0x2             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_scrub
	{
		name => 'pflog_action_noscrub',
		DLT => 'PFLOG',
		aliases => ['action noscrub'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0x3             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_noscrub
	{
		name => 'pflog_action_nat',
		DLT => 'PFLOG',
		aliases => ['action nat'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0x4             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_nat
	{
		name => 'pflog_action_nonat',
		DLT => 'PFLOG',
		aliases => ['action nonat'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0x5             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_nonat
	{
		name => 'pflog_action_binat',
		DLT => 'PFLOG',
		aliases => ['action binat'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0x6             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_binat
	{
		name => 'pflog_action_nobinat',
		DLT => 'PFLOG',
		aliases => ['action nobinat'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0x7             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_nobinat
	{
		name => 'pflog_action_rdr',
		DLT => 'PFLOG',
		aliases => ['action rdr'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0x8             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_rdr
	{
		name => 'pflog_action_nordr',
		DLT => 'PFLOG',
		aliases => ['action nordr'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0x9             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_nordr
	{
		name => 'pflog_action_synproxy_drop',
		DLT => 'PFLOG',
		aliases => ['action synproxy-drop'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0xa             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_synproxy_drop
	{
		name => 'pflog_action_defer',
		skip => skip_os_not ('freebsd') && skip_os_not ('openbsd'),
		DLT => 'PFLOG',
		aliases => ['action defer'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0xb             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_defer
	{
		name => 'pflog_action_match',
		skip => skip_os_not ('openbsd'),
		DLT => 'PFLOG',
		aliases => ['action match'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0xc             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_match
	{
		name => 'pflog_action_divert',
		skip => skip_os_not ('openbsd'),
		DLT => 'PFLOG',
		aliases => ['action divert'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0xd             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_divert
	{
		name => 'pflog_action_rt',
		skip => skip_os_not ('openbsd'),
		DLT => 'PFLOG',
		aliases => ['action rt'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0xe             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_rt
	{
		name => 'pflog_action_afrt',
		skip => skip_os_not ('openbsd'),
		DLT => 'PFLOG',
		aliases => ['action afrt'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0xf             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_afrt
	{
		name => 'pflog_action_dummynet',
		skip => skip_os_not ('darwin'),
		DLT => 'PFLOG',
		aliases => ['action dummynet'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0xb             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_dummynet
	{
		name => 'pflog_action_nodummynet',
		skip => skip_os_not ('darwin'),
		DLT => 'PFLOG',
		aliases => ['action nodummynet'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0xc             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_nodummynet
	{
		name => 'pflog_action_nat64',
		skip => skip_os_not ('darwin'),
		DLT => 'PFLOG',
		aliases => ['action nat64'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0xd             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_nat64
	{
		name => 'pflog_action_nonat64',
		skip => skip_os_not ('darwin'),
		DLT => 'PFLOG',
		aliases => ['action nonat64'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0xe             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # pflog_action_nonat64

	{
		name => 'mtp2_fisu',
		DLT => 'MTP2',
		aliases => ['fisu'],
		opt => '
			(000) ldb      [2]
			(001) jset     #0x3f            jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # mtp2_fisu
	{
		name => 'mtp2_lssu',
		DLT => 'MTP2',
		aliases => [
			'lssu',
			'lsu', # Not documented (and probably should not be).
		],
		opt => '
			(000) ldb      [2]
			(001) and      #0x3f
			(002) jgt      #0x0             jt 3	jf 7
			(003) ldb      [2]
			(004) and      #0x3f
			(005) jgt      #0x2             jt 7	jf 6
			(006) ret      #262144
			(007) ret      #0
			',
	}, # mtp2_lssu
	{
		name => 'mtp2_msu',
		DLT => 'MTP2',
		aliases => ['msu'],
		opt => '
			(000) ldb      [2]
			(001) and      #0x3f
			(002) jgt      #0x2             jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp2_msu
	{
		name => 'mtp2_sio',
		DLT => 'MTP2',
		aliases => [
			'sio 0xd2',
			'sio = 0xd2',
			'sio == 0xd2',
			'sio (0xd2)',
			'sio (0xd2 or 0xd2)',
		],
		opt => '
			(000) ldb      [3]
			(001) jeq      #0xd2            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # mtp2_sio
	{
		name => 'mtp2_sio_gt',
		DLT => 'MTP2',
		aliases => ['sio > 0xa1'],
		unopt => '
			(000) ldb      [3]
			(001) jgt      #0xa1            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # mtp2_sio_gt
	{
		name => 'mtp2_sio_ge',
		DLT => 'MTP2',
		aliases => ['sio >= 0xa2'],
		unopt => '
			(000) ldb      [3]
			(001) jge      #0xa2            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # mtp2_sio_ge
	{
		name => 'mtp2_sio_le',
		DLT => 'MTP2',
		aliases => ['sio <= 0xa3'],
		unopt => '
			(000) ldb      [3]
			(001) jgt      #0xa3            jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # mtp2_sio_le
	{
		name => 'mtp2_sio_lt',
		DLT => 'MTP2',
		aliases => ['sio < 0xa4'],
		unopt => '
			(000) ldb      [3]
			(001) jge      #0xa4            jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # mtp2_sio_lt
	{
		name => 'mtp2_sio_ne',
		DLT => 'MTP2',
		aliases => ['sio != 0xa5'],
		unopt => '
			(000) ldb      [3]
			(001) jeq      #0xa5            jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # mtp2_sio_ne
	{
		name => 'mtp2_sio_nary',
		DLT => 'MTP2',
		aliases => ['sio (73 or 74 or 75)'],
		opt => '
			(000) ldb      [3]
			(001) jeq      #0x49            jt 4	jf 2
			(002) jeq      #0x4a            jt 4	jf 3
			(003) jeq      #0x4b            jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # mtp2_sio_nary
	{
		name => 'mtp3_dpc',
		DLT => 'MTP2',
		aliases => [
			'dpc 0x31d6',
			'dpc = 0x31d6',
			'dpc == 0x31d6',
			'dpc (0x31d6)',
			'dpc (0x31d6 or 0x31d6)',
		],
		opt => '
			(000) ldh      [4]
			(001) and      #0xff3f
			(002) jeq      #0xd631          jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp3_dpc
	{
		name => 'mtp3_dpc_gt',
		DLT => 'MTP2',
		aliases => ['dpc > 0x1273'],
		unopt => '
			(000) ldh      [4]
			(001) and      #0xff3f
			(002) jgt      #0x7312          jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp3_dpc_gt
	{
		name => 'mtp3_dpc_ge',
		DLT => 'MTP2',
		aliases => ['dpc >= 0x1273'],
		unopt => '
			(000) ldh      [4]
			(001) and      #0xff3f
			(002) jge      #0x7312          jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp3_dpc_ge
	{
		name => 'mtp3_dpc_le',
		DLT => 'MTP2',
		aliases => ['dpc <= 0x1273'],
		unopt => '
			(000) ldh      [4]
			(001) and      #0xff3f
			(002) jgt      #0x7312          jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # mtp3_dpc_le
	{
		name => 'mtp3_dpc_lt',
		DLT => 'MTP2',
		aliases => ['dpc < 0x1273'],
		unopt => '
			(000) ldh      [4]
			(001) and      #0xff3f
			(002) jge      #0x7312          jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # mtp3_dpc_lt
	{
		name => 'mtp3_dpc_ne',
		DLT => 'MTP2',
		aliases => ['dpc != 0x1273'],
		unopt => '
			(000) ldh      [4]
			(001) and      #0xff3f
			(002) jeq      #0x7312          jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # mtp3_dpc_ne
	{
		name => 'mtp3_dpc_nary',
		DLT => 'MTP2',
		aliases => ['dpc (0x1274 or 0x1275 or 0x1276)'],
		unopt => '
			(000) ldh      [4]
			(001) and      #0xff3f
			(002) jeq      #0x7412          jt 9	jf 3
			(003) ldh      [4]
			(004) and      #0xff3f
			(005) jeq      #0x7512          jt 9	jf 6
			(006) ldh      [4]
			(007) and      #0xff3f
			(008) jeq      #0x7612          jt 9	jf 10
			(009) ret      #262144
			(010) ret      #0
			',
	}, # mtp3_dpc_nary
	{
		name => 'mtp3_opc',
		DLT => 'MTP2',
		aliases => [
			'opc 0x3b35',
			'opc = 0x3b35',
			'opc == 0x3b35',
			'opc (0x3b35)',
			'opc (0x3b35 or 0x3b35)',
		],
		opt => '
			(000) ld       [4]
			(001) and      #0xc0ff0f
			(002) jeq      #0x40cd0e        jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp3_opc
	{
		name => 'mtp3_opc_gt',
		DLT => 'MTP2',
		aliases => ['opc > 0x607'],
		unopt => '
			(000) ld       [4]
			(001) and      #0xc0ff0f
			(002) jgt      #0xc08101        jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp3_opc_gt
	{
		name => 'mtp3_opc_ge',
		DLT => 'MTP2',
		aliases => ['opc >= 0x607'],
		unopt => '
			(000) ld       [4]
			(001) and      #0xc0ff0f
			(002) jge      #0xc08101        jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp3_opc_ge
	{
		name => 'mtp3_opc_le',
		DLT => 'MTP2',
		aliases => ['opc <= 0x607'],
		unopt => '
			(000) ld       [4]
			(001) and      #0xc0ff0f
			(002) jgt      #0xc08101        jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # mtp3_opc_le
	{
		name => 'mtp3_opc_lt',
		DLT => 'MTP2',
		aliases => ['opc < 0x607'],
		unopt => '
			(000) ld       [4]
			(001) and      #0xc0ff0f
			(002) jge      #0xc08101        jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # mtp3_opc_lt
	{
		name => 'mtp3_opc_ne',
		DLT => 'MTP2',
		aliases => ['opc != 0x607'],
		unopt => '
			(000) ld       [4]
			(001) and      #0xc0ff0f
			(002) jeq      #0xc08101        jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # mtp3_opc_ne
	{
		name => 'mtp2_opc_nary',
		DLT => 'MTP2',
		aliases => ['opc (0x608 or 0x609 or 0x60a)'],
		unopt => '
			(000) ld       [4]
			(001) and      #0xc0ff0f
			(002) jeq      #0x8201          jt 9	jf 3
			(003) ld       [4]
			(004) and      #0xc0ff0f
			(005) jeq      #0x408201        jt 9	jf 6
			(006) ld       [4]
			(007) and      #0xc0ff0f
			(008) jeq      #0x808201        jt 9	jf 10
			(009) ret      #262144
			(010) ret      #0
			',
	}, # mtp2_opc_nary
	{
		name => 'mtp3_sls',
		DLT => 'MTP2',
		aliases => [
			'sls 3',
			'sls = 3',
			'sls == 3',
			'sls (3)',
			'sls (3 or 3)',
		],
		opt => '
			(000) ldb      [7]
			(001) and      #0xf0
			(002) jeq      #0x30            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp3_sls
	{
		name => 'mtp3_sls_gt',
		DLT => 'MTP2',
		aliases => ['sls > 1'],
		unopt => '
			(000) ldb      [7]
			(001) and      #0xf0
			(002) jgt      #0x10            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp3_sls_gt
	{
		name => 'mtp3_sls_ge',
		DLT => 'MTP2',
		aliases => ['sls >= 2'],
		unopt => '
			(000) ldb      [7]
			(001) and      #0xf0
			(002) jge      #0x20            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp3_sls_ge
	{
		name => 'mtp3_sls_le',
		DLT => 'MTP2',
		aliases => ['sls <= 4'],
		unopt => '
			(000) ldb      [7]
			(001) and      #0xf0
			(002) jgt      #0x40            jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # mtp3_sls_le
	{
		name => 'mtp3_sls_lt',
		DLT => 'MTP2',
		aliases => ['sls < 5'],
		unopt => '
			(000) ldb      [7]
			(001) and      #0xf0
			(002) jge      #0x50            jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # mtp3_sls_lt
	{
		name => 'mtp3_sls_ne',
		DLT => 'MTP2',
		aliases => ['sls != 8'],
		unopt => '
			(000) ldb      [7]
			(001) and      #0xf0
			(002) jeq      #0x80            jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # mtp3_sls_ne
	{
		name => 'mtp3_sls_nary',
		DLT => 'MTP2',
		aliases => ['sls (3 or 4 or 5)'],
		unopt => '
			(000) ldb      [7]
			(001) and      #0xf0
			(002) jeq      #0x30            jt 9	jf 3
			(003) ldb      [7]
			(004) and      #0xf0
			(005) jeq      #0x40            jt 9	jf 6
			(006) ldb      [7]
			(007) and      #0xf0
			(008) jeq      #0x50            jt 9	jf 10
			(009) ret      #262144
			(010) ret      #0
			',
	}, # mtp3_sls_nary
	{
		name => 'mtp2_hfisu',
		DLT => 'MTP2',
		aliases => ['hfisu'],
		opt => '
			(000) ldh      [4]
			(001) jset     #0xff80          jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # mtp2_hfisu
	{
		name => 'mtp2_hlssu',
		DLT => 'MTP2',
		aliases => ['hlssu'],
		opt => '
			(000) ldh      [4]
			(001) and      #0xff80
			(002) jgt      #0x0             jt 3	jf 7
			(003) ldh      [4]
			(004) and      #0xff80
			(005) jgt      #0x100           jt 7	jf 6
			(006) ret      #262144
			(007) ret      #0
			',
	}, # mtp2_hlssu
	{
		name => 'mtp2_hmsu',
		DLT => 'MTP2',
		aliases => ['hmsu'],
		opt => '
			(000) ldh      [4]
			(001) and      #0xff80
			(002) jgt      #0x100           jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp2_hmsu
	{
		name => 'mtp2_hsio',
		DLT => 'MTP2',
		aliases => [
			'hsio 0x41',
			'hsio = 0x41',
			'hsio == 0x41',
			'hsio (0x41)',
			'hsio (0x41 or 0x41)',
		],
		opt => '
			(000) ldb      [6]
			(001) jeq      #0x41            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # mtp2_hsio
	{
		name => 'mtp2_hsio_gt',
		DLT => 'MTP2',
		aliases => ['hsio > 0x41'],
		unopt => '
			(000) ldb      [6]
			(001) jgt      #0x41            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # mtp2_hsio_gt
	{
		name => 'mtp2_hsio_ge',
		DLT => 'MTP2',
		aliases => ['hsio >= 0x41'],
		unopt => '
			(000) ldb      [6]
			(001) jge      #0x41            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # mtp2_hsio_ge
	{
		name => 'mtp2_hsio_le',
		DLT => 'MTP2',
		aliases => ['hsio <= 0x41'],
		unopt => '
			(000) ldb      [6]
			(001) jgt      #0x41            jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # mtp2_hsio_le
	{
		name => 'mtp2_hsio_lt',
		DLT => 'MTP2',
		aliases => ['hsio < 0x41'],
		unopt => '
			(000) ldb      [6]
			(001) jge      #0x41            jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # mtp2_hsio_lt
	{
		name => 'mtp2_hsio_ne',
		DLT => 'MTP2',
		aliases => ['hsio != 0x41'],
		unopt => '
			(000) ldb      [6]
			(001) jeq      #0x41            jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # mtp2_hsio_ne
	{
		name => 'mtp2_hsio_nary',
		DLT => 'MTP2',
		aliases => ['hsio (0x42 or 0x43 or 0x44)'],
		opt => '
			(000) ldb      [6]
			(001) jeq      #0x42            jt 4	jf 2
			(002) jeq      #0x43            jt 4	jf 3
			(003) jeq      #0x44            jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # mtp2_hsio_nary
	{
		name => 'mtp3_hdpc',
		DLT => 'MTP2',
		aliases => [
			'hdpc 0x0ab5',
			'hdpc = 0x0ab5',
			'hdpc == 0x0ab5',
			'hdpc (0x0ab5)',
			'hdpc (0x0ab5 or 0x0ab5)',
		],
		opt => '
			(000) ldh      [7]
			(001) and      #0xff3f
			(002) jeq      #0xb50a          jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp3_hdpc
	{
		name => 'mtp3_hdpc_gt',
		DLT => 'MTP2',
		aliases => ['hdpc > 0x0ab5'],
		unopt => '
			(000) ldh      [7]
			(001) and      #0xff3f
			(002) jgt      #0xb50a          jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp3_hdpc_gt
	{
		name => 'mtp3_hdpc_ge',
		DLT => 'MTP2',
		aliases => ['hdpc >= 0x0ab5'],
		unopt => '
			(000) ldh      [7]
			(001) and      #0xff3f
			(002) jge      #0xb50a          jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp3_hdpc_ge
	{
		name => 'mtp3_hdpc_le',
		DLT => 'MTP2',
		aliases => ['hdpc <= 0x0ab5'],
		unopt => '
			(000) ldh      [7]
			(001) and      #0xff3f
			(002) jgt      #0xb50a          jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # mtp3_hdpc_le
	{
		name => 'mtp3_hdpc_lt',
		DLT => 'MTP2',
		aliases => ['hdpc < 0x0ab5'],
		unopt => '
			(000) ldh      [7]
			(001) and      #0xff3f
			(002) jge      #0xb50a          jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # mtp3_hdpc_lt
	{
		name => 'mtp3_hdpc_ne',
		DLT => 'MTP2',
		aliases => ['hdpc != 0x0ab5'],
		unopt => '
			(000) ldh      [7]
			(001) and      #0xff3f
			(002) jeq      #0xb50a          jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # mtp3_hdpc_ne
	{
		name => 'mtp3_hdpc_nary',
		DLT => 'MTP2',
		aliases => ['hdpc (0x0ab6 or 0x0ab7 or 0x0ab8)'],
		unopt => '
			(000) ldh      [7]
			(001) and      #0xff3f
			(002) jeq      #0xb60a          jt 9	jf 3
			(003) ldh      [7]
			(004) and      #0xff3f
			(005) jeq      #0xb70a          jt 9	jf 6
			(006) ldh      [7]
			(007) and      #0xff3f
			(008) jeq      #0xb80a          jt 9	jf 10
			(009) ret      #262144
			(010) ret      #0
			',
	}, # mtp3_hdpc_nary
	{
		name => 'mtp3_hopc',
		DLT => 'MTP2',
		aliases => [
			'hopc 0x3aba',
			'hopc = 0x3aba',
			'hopc == 0x3aba',
			'hopc (0x3aba)',
			'hopc (0x3aba or 0x3aba)',
		],
		opt => '
			(000) ld       [7]
			(001) and      #0xc0ff0f
			(002) jeq      #0x80ae0e        jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp3_hopc
	{
		name => 'mtp3_hopc_gt',
		DLT => 'MTP2',
		aliases => ['hopc > 0x3aba'],
		unopt => '
			(000) ld       [7]
			(001) and      #0xc0ff0f
			(002) jgt      #0x80ae0e        jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp3_hopc_gt
	{
		name => 'mtp3_hopc_ge',
		DLT => 'MTP2',
		aliases => ['hopc >= 0x3aba'],
		unopt => '
			(000) ld       [7]
			(001) and      #0xc0ff0f
			(002) jge      #0x80ae0e        jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp3_hopc_ge
	{
		name => 'mtp3_hopc_le',
		DLT => 'MTP2',
		aliases => ['hopc <= 0x3aba'],
		unopt => '
			(000) ld       [7]
			(001) and      #0xc0ff0f
			(002) jgt      #0x80ae0e        jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # mtp3_hopc_le
	{
		name => 'mtp3_hopc_lt',
		DLT => 'MTP2',
		aliases => ['hopc < 0x3aba'],
		unopt => '
			(000) ld       [7]
			(001) and      #0xc0ff0f
			(002) jge      #0x80ae0e        jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # mtp3_hopc_lt
	{
		name => 'mtp3_hopc_ne',
		DLT => 'MTP2',
		aliases => ['hopc != 0x3aba'],
		unopt => '
			(000) ld       [7]
			(001) and      #0xc0ff0f
			(002) jeq      #0x80ae0e        jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # mtp3_hopc_ne
	{
		name => 'mtp3_hopc_nary',
		DLT => 'MTP2',
		aliases => ['hopc (9000 or 10000 or 9001)'],
		unopt => '
			(000) ld       [7]
			(001) and      #0xc0ff0f
			(002) jeq      #0xca08          jt 9	jf 3
			(003) ld       [7]
			(004) and      #0xc0ff0f
			(005) jeq      #0xc409          jt 9	jf 6
			(006) ld       [7]
			(007) and      #0xc0ff0f
			(008) jeq      #0x40ca08        jt 9	jf 10
			(009) ret      #262144
			(010) ret      #0
			',
	}, # mtp3_hopc_nary
	{
		name => 'mtp3_hsls',
		DLT => 'MTP2',
		aliases => [
			'hsls 5',
			'hsls = 5',
			'hsls == 5',
			'hsls (5)',
			'hsls (5 or 5)',
		],
		opt => '
			(000) ldb      [10]
			(001) and      #0xf0
			(002) jeq      #0x50            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp3_hsls
	{
		name => 'mtp3_hsls_gt',
		DLT => 'MTP2',
		aliases => ['hsls > 6'],
		unopt => '
			(000) ldb      [10]
			(001) and      #0xf0
			(002) jgt      #0x60            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp3_hsls_gt
	{
		name => 'mtp3_hsls_ge',
		DLT => 'MTP2',
		aliases => ['hsls >= 7'],
		unopt => '
			(000) ldb      [10]
			(001) and      #0xf0
			(002) jge      #0x70            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # mtp3_hsls_ge
	{
		name => 'mtp3_hsls_le',
		DLT => 'MTP2',
		aliases => ['hsls <= 8'],
		unopt => '
			(000) ldb      [10]
			(001) and      #0xf0
			(002) jgt      #0x80            jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # mtp3_hsls_le
	{
		name => 'mtp3_hsls_lt',
		DLT => 'MTP2',
		aliases => ['hsls < 9'],
		unopt => '
			(000) ldb      [10]
			(001) and      #0xf0
			(002) jge      #0x90            jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # mtp3_hsls_lt
	{
		name => 'mtp3_hsls_ne',
		DLT => 'MTP2',
		aliases => ['hsls != 10'],
		unopt => '
			(000) ldb      [10]
			(001) and      #0xf0
			(002) jeq      #0xa0            jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # mtp3_hsls_ne
	{
		name => 'mtp3_hsls_nary',
		DLT => 'MTP2',
		aliases => ['hsls (13 or 12 or 11)'],
		unopt => '
			(000) ldb      [10]
			(001) and      #0xf0
			(002) jeq      #0xd0            jt 9	jf 3
			(003) ldb      [10]
			(004) and      #0xf0
			(005) jeq      #0xc0            jt 9	jf 6
			(006) ldb      [10]
			(007) and      #0xf0
			(008) jeq      #0xb0            jt 9	jf 10
			(009) ret      #262144
			(010) ret      #0
			',
	}, # mtp3_hsls_nary

	{
		name => 'atm_vpi',
		DLT => 'SUNATM',
		aliases => [
			'vpi 10',
			'vpi = 10',
			'vpi == 10',
			'vpi (10)',
			'vpi (10 or 10)',
		],
		opt => '
			(000) ldb      [1]
			(001) jeq      #0xa             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # atm_vpi
	{
		name => 'atm_vpi_gt',
		DLT => 'SUNATM',
		aliases => ['vpi > 100'],
		unopt => '
			(000) ldb      [1]
			(001) jgt      #0x64            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # atm_vpi_gt
	{
		name => 'atm_vpi_ge',
		DLT => 'SUNATM',
		aliases => ['vpi >= 101'],
		unopt => '
			(000) ldb      [1]
			(001) jge      #0x65            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # atm_vpi_ge
	{
		name => 'atm_vpi_le',
		DLT => 'SUNATM',
		aliases => ['vpi <= 102'],
		unopt => '
			(000) ldb      [1]
			(001) jgt      #0x66            jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # atm_vpi_le
	{
		name => 'atm_vpi_lt',
		DLT => 'SUNATM',
		aliases => ['vpi < 103'],
		unopt => '
			(000) ldb      [1]
			(001) jge      #0x67            jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # atm_vpi_lt
	{
		name => 'atm_vpi_ne',
		DLT => 'SUNATM',
		aliases => ['vpi != 104'],
		unopt => '
			(000) ldb      [1]
			(001) jeq      #0x68            jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # atm_vpi_ne
	{
		name => 'atm_vpi_nary',
		DLT => 'SUNATM',
		aliases => ['vpi (105 or 0x7a or 0311)'],
		# The bytecode preserves the order of values, hence no aliases for the
		# permutations.
		opt => '
			(000) ldb      [1]
			(001) jeq      #0x69            jt 4	jf 2
			(002) jeq      #0x7a            jt 4	jf 3
			(003) jeq      #0xc9            jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # atm_vpi_nary
	{
		name => 'atm_vci',
		DLT => 'SUNATM',
		aliases => [
			'vci 20',
			'vci = 20',
			'vci == 20',
			'vci (20)',
			'vci (20 or 20)',
		],
		opt => '
			(000) ldh      [2]
			(001) jeq      #0x14            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # atm_vci
	{
		name => 'atm_vci_gt',
		DLT => 'SUNATM',
		aliases => ['vci > 20001'],
		unopt => '
			(000) ldh      [2]
			(001) jgt      #0x4e21          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # atm_vci_gt
	{
		name => 'atm_vci_ge',
		DLT => 'SUNATM',
		aliases => ['vci >= 20002'],
		unopt => '
			(000) ldh      [2]
			(001) jge      #0x4e22          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # atm_vci_ge
	{
		name => 'atm_vci_le',
		DLT => 'SUNATM',
		aliases => ['vci <= 20003'],
		unopt => '
			(000) ldh      [2]
			(001) jgt      #0x4e23          jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # atm_vci_le
	{
		name => 'atm_vci_lt',
		DLT => 'SUNATM',
		aliases => ['vci < 20004'],
		unopt => '
			(000) ldh      [2]
			(001) jge      #0x4e24          jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # atm_vci_lt
	{
		name => 'atm_vci_ne',
		DLT => 'SUNATM',
		aliases => ['vci != 20005'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x4e25          jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # atm_vci_ne
	{
		name => 'atm_vci_nary',
		DLT => 'SUNATM',
		aliases => ['vci (10 or 0xdb or 0700)'],
		opt => '
			(000) ldh      [2]
			(001) jeq      #0xa             jt 4	jf 2
			(002) jeq      #0xdb            jt 4	jf 3
			(003) jeq      #0x1c0           jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # atm_vci_nary
	{
		name => 'atm_lane',
		DLT => 'SUNATM',
		aliases => ['lane'],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf
			(002) jeq      #0x1             jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # atm_lane
	{
		name => 'atm_oamf4sc',
		DLT => 'SUNATM',
		aliases => ['oamf4sc'],
		opt => '
			(000) ldb      [1]
			(001) jeq      #0x0             jt 2	jf 5
			(002) ldh      [2]
			(003) jeq      #0x3             jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # atm_oamf4sc
	{
		name => 'atm_oamf4ec',
		DLT => 'SUNATM',
		aliases => ['oamf4ec'],
		opt => '
			(000) ldb      [1]
			(001) jeq      #0x0             jt 2	jf 5
			(002) ldh      [2]
			(003) jeq      #0x4             jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # atm_oamf4ec
	{
		name => 'atm_oamf4',
		DLT => 'SUNATM',
		aliases => [
			'oamf4',
			'oam',
		],
		opt => '
			(000) ldb      [1]
			(001) jeq      #0x0             jt 2	jf 6
			(002) ldh      [2]
			(003) jeq      #0x3             jt 5	jf 4
			(004) jeq      #0x4             jt 5	jf 6
			(005) ret      #262144
			(006) ret      #0
			',
	}, # atm_oamf4
	{
		name => 'atm_metac',
		DLT => 'SUNATM',
		aliases => ['metac'],
		opt => '
			(000) ldb      [1]
			(001) jeq      #0x0             jt 2	jf 5
			(002) ldh      [2]
			(003) jeq      #0x1             jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # atm_metac
	{
		name => 'atm_bcc',
		DLT => 'SUNATM',
		aliases => ['bcc'],
		opt => '
			(000) ldb      [1]
			(001) jeq      #0x0             jt 2	jf 5
			(002) ldh      [2]
			(003) jeq      #0x2             jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # atm_bcc
	{
		name => 'atm_sc',
		DLT => 'SUNATM',
		aliases => ['sc'],
		opt => '
			(000) ldb      [1]
			(001) jeq      #0x0             jt 2	jf 5
			(002) ldh      [2]
			(003) jeq      #0x5             jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # atm_sc
	{
		name => 'atm_ilmic',
		DLT => 'SUNATM',
		aliases => ['ilmic'],
		opt => '
			(000) ldb      [1]
			(001) jeq      #0x0             jt 2	jf 5
			(002) ldh      [2]
			(003) jeq      #0x10            jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # atm_ilmic
	{
		name => 'atm_connectmsg',
		DLT => 'SUNATM',
		aliases => ['connectmsg'],
		opt => '
			(000) ldb      [1]
			(001) jeq      #0x0             jt 2	jf 12
			(002) ldh      [2]
			(003) jeq      #0x5             jt 4	jf 12
			(004) ldb      [9]
			(005) jeq      #0x5a            jt 11	jf 6
			(006) jeq      #0x4d            jt 11	jf 7
			(007) jeq      #0xf             jt 11	jf 8
			(008) jeq      #0x7             jt 11	jf 9
			(009) jeq      #0x5             jt 11	jf 10
			(010) jeq      #0x2             jt 11	jf 12
			(011) ret      #262144
			(012) ret      #0
			',
	}, # atm_connectmsg
	{
		name => 'atm_metaconnect',
		DLT => 'SUNATM',
		aliases => ['metaconnect'],
		opt => '
			(000) ldb      [1]
			(001) jeq      #0x0             jt 2	jf 11
			(002) ldh      [2]
			(003) jeq      #0x1             jt 4	jf 11
			(004) ldb      [9]
			(005) jeq      #0x5a            jt 10	jf 6
			(006) jeq      #0x4d            jt 10	jf 7
			(007) jeq      #0x7             jt 10	jf 8
			(008) jeq      #0x5             jt 10	jf 9
			(009) jeq      #0x2             jt 10	jf 11
			(010) ret      #262144
			(011) ret      #0
			',
	}, # atm_metaconnect

	# Do not permutate all possible aliases for "link", just the ones that
	# obviously make sense for the DLT.
	{
		name => 'arcnet_broadcast_multicast',
		DLT => 'ARCNET',
		aliases => [
			'broadcast',
			'multicast',
			'link broadcast',
			'link multicast',
			'link dst $00',
		],
		unopt => '
			(000) ldb      [1]
			(001) jeq      #0x0             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # arcnet_broadcast_multicast
	{
		name => 'arcnet_host',
		DLT => 'ARCNET',
		aliases => [
			'link host $0e',
			'link src or dst host $0e',
			'link src or dst $0e',
			'link host $e',
			'link src or dst host $e',
			'link src or dst $e',
		],
		unopt => '
			(000) ldb      [0]
			(001) jeq      #0xe             jt 4	jf 2
			(002) ldb      [1]
			(003) jeq      #0xe             jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # arcnet_host
	{
		name => 'arcnet_src_host',
		DLT => 'ARCNET',
		aliases => [
			'link src host $8c',
			'link src $8c',
		],
		unopt => '
			(000) ldb      [0]
			(001) jeq      #0x8c            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # arcnet_src_host
	{
		name => 'arcnet_dst_host',
		DLT => 'ARCNET',
		aliases => [
			'link dst host $a4',
			'link dst $a4',
		],
		unopt => '
			(000) ldb      [1]
			(001) jeq      #0xa4            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # arcnet_dst_host
	{
		name => 'fddi_broadcast',
		DLT => 'FDDI',
		aliases => [
			'broadcast',
			'fddi broadcast',
			'link broadcast',
		],
		unopt => '
			(000) ld       [3]
			(001) jeq      #0xffffffff      jt 2	jf 5
			(002) ldh      [1]
			(003) jeq      #0xffff          jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # fddi_broadcast
	{
		name => 'fddi_multicast',
		DLT => 'FDDI',
		aliases => [
			'multicast',
			'fddi multicast',
			'link multicast',
		],
		unopt => '
			(000) ldb      [1]
			(001) jset     #0x1             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # fddi_multicast
	{
		name => 'ieee802_broadcast',
		DLT => 'IEEE802',
		aliases => [
			'broadcast',
			'tr broadcast',
			'link broadcast',
		],
		unopt => '
			(000) ld       [4]
			(001) jeq      #0xffffffff      jt 2	jf 5
			(002) ldh      [2]
			(003) jeq      #0xffff          jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # ieee802_broadcast
	{
		name => 'ieee802_multicast',
		DLT => 'IEEE802',
		aliases => [
			'multicast',
			'tr multicast',
			'link multicast',
		],
		unopt => '
			(000) ldb      [2]
			(001) jset     #0x1             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # ieee802_multicast
	{
		name => 'ieee802_11_broadcast',
		DLT => 'IEEE802_11',
		aliases => [
			'broadcast',
			'wlan broadcast',
			'ether broadcast',
			'link broadcast',
		],
		opt => '
			(000) ldb      [0]
			(001) jset     #0x4             jt 14	jf 2
			(002) jset     #0x8             jt 3	jf 9
			(003) ldb      [1]
			(004) jset     #0x1             jt 5	jf 9
			(005) ld       [18]
			(006) jeq      #0xffffffff      jt 7	jf 14
			(007) ldh      [16]
			(008) jeq      #0xffff          jt 13	jf 14
			(009) ld       [6]
			(010) jeq      #0xffffffff      jt 11	jf 14
			(011) ldh      [4]
			(012) jeq      #0xffff          jt 13	jf 14
			(013) ret      #262144
			(014) ret      #0
			',
	}, # ieee802_11_broadcast
	{
		name => 'ieee802_11_multicast',
		DLT => 'IEEE802_11',
		aliases => [
			'multicast',
			'wlan multicast',
			'ether multicast',
			'link multicast',
		],
		opt => '
			(000) ldb      [0]
			(001) jset     #0x4             jt 10	jf 2
			(002) jset     #0x8             jt 3	jf 7
			(003) ldb      [1]
			(004) jset     #0x1             jt 5	jf 7
			(005) ldb      [16]
			(006) jset     #0x1             jt 9	jf 10
			(007) ldb      [4]
			(008) jset     #0x1             jt 9	jf 10
			(009) ret      #262144
			(010) ret      #0
			',
	}, # ieee802_11_multicast
	{
		name => 'ip_over_fc_broadcast',
		DLT => 'IP_OVER_FC',
		aliases => [
			'broadcast',
			'link broadcast',
		],
		unopt => '
			(000) ld       [4]
			(001) jeq      #0xffffffff      jt 2	jf 5
			(002) ldh      [2]
			(003) jeq      #0xffff          jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # ip_over_fc_broadcast
	{
		name => 'ip_over_fc_multicast',
		DLT => 'IP_OVER_FC',
		aliases => [
			'multicast',
			'link multicast',
		],
		opt => '
			(000) ldb      [2]
			(001) jset     #0x1             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # ip_over_fc_multicast
	{
		name => 'atm_multicast',
		DLT => 'SUNATM',
		aliases => ['lane and multicast'],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf
			(002) jeq      #0x1             jt 3	jf 8
			(003) ldh      [4]
			(004) jeq      #0xff00          jt 8	jf 5
			(005) ldb      [6]
			(006) jset     #0x1             jt 7	jf 8
			(007) ret      #262144
			(008) ret      #0
			',
	}, # atm_multicast

	{
		name => 'ether_broadcast',
		DLT => 'EN10MB',
		snaplen => 16000,
		aliases => [
			'broadcast',
			'ether broadcast',
			'link broadcast',
		],
		opt => '
			(000) ld       [2]
			(001) jeq      #0xffffffff      jt 2	jf 5
			(002) ldh      [0]
			(003) jeq      #0xffff          jt 4	jf 5
			(004) ret      #16000
			(005) ret      #0
			',
	}, # ether_broadcast
	{
		name => 'ether_multicast',
		DLT => 'EN10MB',
		snaplen => 16000,
		aliases => [
			'multicast',
			'ether multicast',
			'link multicast',
		],
		opt => '
			(000) ldb      [0]
			(001) jset     #0x1             jt 2	jf 3
			(002) ret      #16000
			(003) ret      #0
			',
	}, # ether_multicast
	{
		name => 'ether_host_addr',
		DLT => 'EN10MB',
		aliases => [
			'ether host ab:cd:ef:0:0:1',
			'ether src or dst host ab.CD.ef.0.0.1',
			'ether src or dst Ab.cD.ef.00.0.01',
		],
		opt => '
			(000) ld       [8]
			(001) jeq      #0xef000001      jt 2	jf 4
			(002) ldh      [6]
			(003) jeq      #0xabcd          jt 8	jf 4
			(004) ld       [2]
			(005) jeq      #0xef000001      jt 6	jf 9
			(006) ldh      [0]
			(007) jeq      #0xabcd          jt 8	jf 9
			(008) ret      #262144
			(009) ret      #0
			',
	}, # ether_host_addr
	{
		name => 'ether_host_name',
		skip => skip_no_ethers(),
		DLT => 'EN10MB',
		aliases => [
			'ether host eth-noipv4-noipv6.host123.libpcap.test',
			'ether src or dst eth-noipv4-noipv6.host123.libpcap.test',
			'ether src or dst host eth-noipv4-noipv6.host123.libpcap.test',
		],
		opt => '
			(000) ld       [8]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [6]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [2]
			(005) jeq      #0x400140e       jt 6	jf 9
			(006) ldh      [0]
			(007) jeq      #0xaa00          jt 8	jf 9
			(008) ret      #262144
			(009) ret      #0
			',
	}, # ether_host_name
	{
		name => 'ether_host_NAME',
		skip => skip_no_ethers_casecmp(),
		DLT => 'EN10MB',
		aliases => [
			'ether host ETH-NOIPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'ether src or dst ETH-NOIPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'ether src or dst host ETH-NOIPV4-NOIPV6.HOST123.LIBPCAP.TEST',
		],
		opt => '
			(000) ld       [8]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [6]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [2]
			(005) jeq      #0x400140e       jt 6	jf 9
			(006) ldh      [0]
			(007) jeq      #0xaa00          jt 8	jf 9
			(008) ret      #262144
			(009) ret      #0
			',
	}, # ether_host_NAME
	{
		name => 'ether_src_host_addr',
		DLT => 'EN10MB',
		aliases => [
			'ether src host ab-cd-ef-00-00-02',
			'ether src ab.cd.ef.00.00.02',
		],
		opt => '
			(000) ld       [8]
			(001) jeq      #0xef000002      jt 2	jf 5
			(002) ldh      [6]
			(003) jeq      #0xabcd          jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # ether_src_host_addr
	{
		name => 'ether_src_host_name',
		skip => skip_no_ethers(),
		DLT => 'EN10MB',
		aliases => [
			'ether src host eth-noipv4-noipv6.host123.libpcap.test',
			'ether src eth-noipv4-noipv6.host123.libpcap.test',
		],
		opt => '
			(000) ld       [8]
			(001) jeq      #0x400140e       jt 2	jf 5
			(002) ldh      [6]
			(003) jeq      #0xaa00          jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # ether_src_host_name
	{
		name => 'ether_src_host_NAME',
		skip => skip_no_ethers_casecmp(),
		DLT => 'EN10MB',
		aliases => [
			'ether src host ETH-NOIPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'ether src ETH-NOIPV4-NOIPV6.HOST123.LIBPCAP.TEST',
		],
		opt => '
			(000) ld       [8]
			(001) jeq      #0x400140e       jt 2	jf 5
			(002) ldh      [6]
			(003) jeq      #0xaa00          jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # ether_src_host_NAME
	{
		name => 'ether_dst_host_addr',
		DLT => 'EN10MB',
		aliases => [
			'ether dst host abcd.ef00.0003',
			'ether dst abcdef000003',
		],
		opt => '
			(000) ld       [2]
			(001) jeq      #0xef000003      jt 2	jf 5
			(002) ldh      [0]
			(003) jeq      #0xabcd          jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # ether_dst_host_addr
	{
		name => 'ether_dst_host_name',
		skip => skip_no_ethers(),
		DLT => 'EN10MB',
		aliases => [
			'ether dst host eth-noipv4-noipv6.host123.libpcap.test',
			'ether dst eth-noipv4-noipv6.host123.libpcap.test',
		],
		opt => '
			(000) ld       [2]
			(001) jeq      #0x400140e       jt 2	jf 5
			(002) ldh      [0]
			(003) jeq      #0xaa00          jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # ether_dst_host_name
	{
		name => 'ether_dst_host_NAME',
		skip => skip_no_ethers_casecmp(),
		DLT => 'EN10MB',
		aliases => [
			'ether dst host ETH-NOIPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'ether dst ETH-NOIPV4-NOIPV6.HOST123.LIBPCAP.TEST',
		],
		opt => '
			(000) ld       [2]
			(001) jeq      #0x400140e       jt 2	jf 5
			(002) ldh      [0]
			(003) jeq      #0xaa00          jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # ether_dst_host_NAME

	# 3 DLTs lead to gen_ether_linktype(), which implements 7 code paths,
	# let's test these for DLT_EN10MB only.
	{
		name => 'ether_proto_aarp',
		DLT => 'EN10MB',
		aliases => [
			'ether proto \aarp',
			'aarp',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x80f3          jt 7	jf 2
			(002) jgt      #0x5dc           jt 8	jf 3
			(003) ld       [18]
			(004) jeq      #0x80f3          jt 5	jf 8
			(005) ld       [14]
			(006) jeq      #0xaaaa0300      jt 7	jf 8
			(007) ret      #262144
			(008) ret      #0
			',
	}, # ether_proto_aarp
	{
		name => 'ether_proto_arp',
		DLT => 'EN10MB',
		aliases => [
			'ether proto \arp',
			'arp',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x806           jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # ether_proto_arp
	{
		name => 'ether_proto_atalk',
		DLT => 'EN10MB',
		aliases => [
			'ether proto \atalk',
			'atalk',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x809b          jt 7	jf 2
			(002) jgt      #0x5dc           jt 8	jf 3
			(003) ld       [18]
			(004) jeq      #0x7809b         jt 5	jf 8
			(005) ld       [14]
			(006) jeq      #0xaaaa0308      jt 7	jf 8
			(007) ret      #262144
			(008) ret      #0
			',
	}, # ether_proto_atalk
	{
		name => 'ether_proto_decnet',
		DLT => 'EN10MB',
		aliases => [
			'ether proto \decnet',
			'decnet',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x6003          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # ether_proto_decnet
	{
		name => 'ether_proto_ip',
		DLT => 'EN10MB',
		# gen_ether_linktype() default case
		aliases => [
			'ether proto \ip',
			'ip',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x800           jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # ether_proto_ip
	{
		name => 'ether_proto_6',
		DLT => 'EN10MB',
		aliases => ['ether proto 6'], # gen_ether_linktype() LLCSAP_IP
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 5	jf 2
			(002) ldh      [14]
			(003) jeq      #0x606           jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # ether_proto_6
	{
		name => 'ether_proto_ip6',
		DLT => 'EN10MB',
		aliases => ['ether proto \ip6'],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # ether_proto_ip6
	{
		name => 'ip6',
		DLT => 'EN10MB',
		aliases => ['ip6'],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # ip6
	{
		name => 'ether_proto_ipx',
		DLT => 'EN10MB',
		aliases => [
			'ether proto \ipx',
			'ipx',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x8137          jt 11	jf 2
			(002) jgt      #0x5dc           jt 12	jf 3
			(003) ld       [18]
			(004) jeq      #0x8137          jt 5	jf 7
			(005) ld       [14]
			(006) jeq      #0xaaaa0300      jt 11	jf 7
			(007) ldb      [14]
			(008) jeq      #0xe0            jt 11	jf 9
			(009) ldh      [14]
			(010) jeq      #0xffff          jt 11	jf 12
			(011) ret      #262144
			(012) ret      #0
			',
	}, # ether_proto_ipx
	{
		name => 'ether_proto_iso',
		DLT => 'EN10MB',
		aliases => [
			'ether proto \iso',
			'iso',
		],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 5	jf 2
			(002) ldh      [14]
			(003) jeq      #0xfefe          jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # ether_proto_iso
	{
		name => 'ether_proto_lat',
		DLT => 'EN10MB',
		aliases => [
			'ether proto \lat',
			'lat',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x6004          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # ether_proto_lat
	{
		name => 'ether_proto_loopback',
		DLT => 'EN10MB',
		# No backslash escaping and no alias (the identifier is not a keyword).
		aliases => ['ether proto loopback'],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x9000          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # ether_proto_loopback
	{
		name => 'ether_proto_mopdl',
		DLT => 'EN10MB',
		aliases => [
			'ether proto \mopdl',
			'mopdl',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x6001          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # ether_proto_mopdl
	{
		name => 'ether_proto_moprc',
		DLT => 'EN10MB',
		aliases => [
			'ether proto \moprc',
			'moprc',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x6002          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # ether_proto_moprc
	{
		name => 'ether_proto_netbeui',
		DLT => 'EN10MB',
		aliases => [
			'ether proto \netbeui',
			'netbeui',
		],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 5	jf 2
			(002) ldh      [14]
			(003) jeq      #0xf0f0          jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # ether_proto_netbeui
	{
		name => 'ether_proto_rarp',
		DLT => 'EN10MB',
		aliases => [
			'ether proto \rarp',
			'rarp',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x8035          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # ether_proto_rarp
	{
		name => 'ether_proto_sca',
		DLT => 'EN10MB',
		aliases => [
			'ether proto \sca',
			'sca',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x6007          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # ether_proto_sca
	{
		name => 'ether_proto_stp',
		DLT => 'EN10MB',
		aliases => [
			'ether proto \stp',
			'stp',
		],
		opt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 5	jf 2
			(002) ldb      [14]
			(003) jeq      #0x42            jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # ether_proto_stp

	# The complete cartesian product of all DLTs and all link-layer protocol
	# numbers in gen_linktype() is not a practicable test space.  Try testing
	# all unique code paths and eliminate/coalesce code points as necessary.
	{
		name => 'link_proto_ip_NETANALYZER',
		DLT => 'NETANALYZER',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldh      [16]
			(001) jeq      #0x800           jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_NETANALYZER
	{
		name => 'link_proto_ip_NETANALYZER_TRANSPARENT',
		DLT => 'NETANALYZER_TRANSPARENT',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldh      [24]
			(001) jeq      #0x800           jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_NETANALYZER_TRANSPARENT
	{
		name => 'link_proto_ip_C_HDLC',
		DLT => 'C_HDLC',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x800           jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_C_HDLC
	{
		name => 'link_proto_iso_C_HDLC',
		DLT => 'C_HDLC',
		aliases => ['link proto \iso'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0xfefe          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_iso_C_HDLC
	{
		name => 'link_proto_ip_IEEE802_11',
		DLT => 'IEEE802_11',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldx      #0x0
			(001) txa
			(002) add      #24
			(003) st       M[0]
			(004) ldb      [x + 0]
			(005) jset     #0x8             jt 6	jf 11
			(006) jset     #0x4             jt 11	jf 7
			(007) jset     #0x80            jt 8	jf 11
			(008) ld       M[0]
			(009) add      #2
			(010) st       M[0]
			(011) ldb      [0]
			(012) and      #0xc
			(013) jeq      #0x8             jt 14	jf 18
			(014) ldx      M[0]
			(015) ldh      [x + 6]
			(016) jeq      #0x800           jt 17	jf 18
			(017) ret      #262144
			(018) ret      #0
			',
	}, # link_proto_ip_IEEE802_11
	{
		name => 'link_proto_ip_PRISM_HEADER',
		DLT => 'PRISM_HEADER',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ld       [0]
			(001) and      #0xfffff000
			(002) jeq      #0x80211000      jt 3	jf 5
			(003) ld       [4]
			(004) ja       6
			(005) ld       #0x90
			(006) st       M[0]
			(007) tax
			(008) txa
			(009) add      #24
			(010) st       M[1]
			(011) ldb      [x + 0]
			(012) jset     #0x8             jt 13	jf 18
			(013) jset     #0x4             jt 18	jf 14
			(014) jset     #0x80            jt 15	jf 18
			(015) ld       M[1]
			(016) add      #2
			(017) st       M[1]
			(018) ldx      M[0]
			(019) ldb      [x + 0]
			(020) and      #0xc
			(021) jeq      #0x8             jt 22	jf 26
			(022) ldx      M[1]
			(023) ldh      [x + 6]
			(024) jeq      #0x800           jt 25	jf 26
			(025) ret      #262144
			(026) ret      #0
			',
	}, # link_proto_ip_PRISM_HEADER
	{
		name => 'link_proto_ip_IEEE802_11_RADIO',
		DLT => 'IEEE802_11_RADIO',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldb      [3]
			(001) lsh      #8
			(002) tax
			(003) ldb      [2]
			(004) or       x
			(005) st       M[0]
			(006) tax
			(007) txa
			(008) add      #24
			(009) st       M[1]
			(010) ldb      [x + 0]
			(011) jset     #0x8             jt 12	jf 29
			(012) jset     #0x4             jt 29	jf 13
			(013) jset     #0x80            jt 14	jf 17
			(014) ld       M[1]
			(015) add      #2
			(016) st       M[1]
			(017) ld       [4]
			(018) jset     #0x2000000       jt 19	jf 29
			(019) jset     #0x80            jt 29	jf 20
			(020) jset     #0x1000000       jt 21	jf 23
			(021) ldb      [16]
			(022) jset     #0x20            jt 25	jf 29
			(023) ldb      [8]
			(024) jset     #0x20            jt 25	jf 29
			(025) ld       M[1]
			(026) add      #3
			(027) and      #0xfffffffc
			(028) st       M[1]
			(029) ldx      M[0]
			(030) ldb      [x + 0]
			(031) and      #0xc
			(032) jeq      #0x8             jt 33	jf 37
			(033) ldx      M[1]
			(034) ldh      [x + 6]
			(035) jeq      #0x800           jt 36	jf 37
			(036) ret      #262144
			(037) ret      #0
			',
	}, # link_proto_ip_IEEE802_11_RADIO
	{
		name => 'link_proto_ip_IEEE802_11_RADIO_AVS',
		DLT => 'IEEE802_11_RADIO_AVS',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ld       [4]
			(001) st       M[0]
			(002) tax
			(003) txa
			(004) add      #24
			(005) st       M[1]
			(006) ldb      [x + 0]
			(007) jset     #0x8             jt 8	jf 13
			(008) jset     #0x4             jt 13	jf 9
			(009) jset     #0x80            jt 10	jf 13
			(010) ld       M[1]
			(011) add      #2
			(012) st       M[1]
			(013) ldx      M[0]
			(014) ldb      [x + 0]
			(015) and      #0xc
			(016) jeq      #0x8             jt 17	jf 21
			(017) ldx      M[1]
			(018) ldh      [x + 6]
			(019) jeq      #0x800           jt 20	jf 21
			(020) ret      #262144
			(021) ret      #0
			',
	}, # link_proto_ip_IEEE802_11_RADIO_AVS
	{
		name => 'link_proto_ip_PPI',
		DLT => 'PPI',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ld       [4]
			(001) jeq      #0x69000000      jt 2	jf 27
			(002) ldb      [3]
			(003) lsh      #8
			(004) tax
			(005) ldb      [2]
			(006) or       x
			(007) st       M[0]
			(008) tax
			(009) txa
			(010) add      #24
			(011) st       M[1]
			(012) ldb      [x + 0]
			(013) jset     #0x8             jt 14	jf 19
			(014) jset     #0x4             jt 19	jf 15
			(015) jset     #0x80            jt 16	jf 19
			(016) ld       M[1]
			(017) add      #2
			(018) st       M[1]
			(019) ldx      M[0]
			(020) ldb      [x + 0]
			(021) and      #0xc
			(022) jeq      #0x8             jt 23	jf 27
			(023) ldx      M[1]
			(024) ldh      [x + 6]
			(025) jeq      #0x800           jt 26	jf 27
			(026) ret      #262144
			(027) ret      #0
			',
	}, # link_proto_ip_PPI
	{
		name => 'link_proto_ip_FDDI',
		DLT => 'FDDI',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldh      [19]
			(001) jeq      #0x800           jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_FDDI
	# 11 DLTs lead to gen_llc_linktype(), which implements 6 code paths,
	# let's test these for DLT_IEEE802 only.
	{
		name => 'link_proto_ip_IEEE802',
		DLT => 'IEEE802',
		aliases => ['link proto \ip'], # gen_llc_linktype() default case
		unopt => '
			(000) ldh      [20]
			(001) jeq      #0x800           jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_IEEE802
	{
		name => 'link_proto_6_IEEE802',
		DLT => 'IEEE802',
		aliases => ['link proto 6'], # gen_llc_linktype() LLCSAP_IP
		unopt => '
			(000) ldh      [14]
			(001) jeq      #0x606           jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_6_IEEE802
	{
		name => 'link_proto_iso_IEEE802',
		DLT => 'IEEE802',
		aliases => ['link proto \iso'],
		unopt => '
			(000) ldh      [14]
			(001) jeq      #0xfefe          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_iso_IEEE802
	{
		name => 'link_proto_netbeui_IEEE802',
		DLT => 'IEEE802',
		aliases => ['link proto \netbeui'],
		unopt => '
			(000) ldh      [14]
			(001) jeq      #0xf0f0          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_netbeui_IEEE802
	{
		name => 'link_proto_ipx_IEEE802',
		DLT => 'IEEE802',
		aliases => ['link proto \ipx'],
		unopt => '
			(000) ldb      [14]
			(001) jeq      #0xe0            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ipx_IEEE802
	{
		name => 'link_proto_atalk_IEEE802',
		DLT => 'IEEE802',
		aliases => ['link proto \atalk'],
		unopt => '
			(000) ld       [18]
			(001) jeq      #0x7809b         jt 2	jf 5
			(002) ld       [14]
			(003) jeq      #0xaaaa0308      jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # link_proto_atalk_IEEE802
	{
		name => 'link_proto_aarp_IEEE802',
		DLT => 'IEEE802',
		aliases => ['link proto \aarp'],
		unopt => '
			(000) ldh      [20]
			(001) jeq      #0x80f3          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_aarp_IEEE802
	{
		name => 'link_proto_stp_IEEE802',
		DLT => 'IEEE802',
		aliases => ['link proto \stp'],
		unopt => '
			(000) ldb      [14]
			(001) jeq      #0x42            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_stp_IEEE802
	{
		name => 'link_proto_ip_ATM_RFC1483',
		DLT => 'ATM_RFC1483',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldh      [6]
			(001) jeq      #0x800           jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_ATM_RFC1483
	{
		name => 'link_proto_ip_ATM_CLIP',
		DLT => 'ATM_CLIP',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldh      [6]
			(001) jeq      #0x800           jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_ATM_CLIP
	{
		name => 'link_proto_ip_IP_OVER_FC',
		DLT => 'IP_OVER_FC',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldh      [22]
			(001) jeq      #0x800           jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_IP_OVER_FC
	{
		name => 'link_proto_ip_SUNATM',
		DLT => 'SUNATM',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf
			(002) jeq      #0x2             jt 3	jf 6
			(003) ldh      [10]
			(004) jeq      #0x800           jt 5	jf 6
			(005) ret      #262144
			(006) ret      #0
			',
	}, # link_proto_ip_SUNATM
	{
		name => 'lane_and_link_proto_ip_SUNATM',
		DLT => 'SUNATM',
		aliases => ['lane and link proto \ip'],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf
			(002) jeq      #0x1             jt 3	jf 8
			(003) ldh      [4]
			(004) jeq      #0xff00          jt 8	jf 5
			(005) ldh      [18]
			(006) jeq      #0x800           jt 7	jf 8
			(007) ret      #262144
			(008) ret      #0
			',
	}, # lane_and_link_proto_ip_SUNATM
	{
		name => 'link_proto_ip_LINUX_SLL',
		DLT => 'LINUX_SLL',
		aliases => ['link proto \ip'], # gen_linux_sll_linktype() default case
		unopt => '
			(000) ldh      [14]
			(001) jeq      #0x800           jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_LINUX_SLL
	{
		name => 'link_proto_ip_LINUX_SLL2',
		DLT => 'LINUX_SLL2', # gen_linktype() default case
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldh      [0]
			(001) jeq      #0x800           jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_LINUX_SLL
	{
		name => 'link_proto_ip6_LINUX_SLL',
		DLT => 'LINUX_SLL',
		aliases => ['link proto \ip6'], # gen_linux_sll_linktype() default case
		unopt => '
			(000) ldh      [14]
			(001) jeq      #0x86dd          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_LINUX_SLL
	{
		name => 'link_proto_6_LINUX_SLL',
		DLT => 'LINUX_SLL',
		aliases => ['link proto 6'], # gen_linux_sll_linktype() LLCSAP_IP
		unopt => '
			(000) ldh      [14]
			(001) jeq      #0x4             jt 2	jf 5
			(002) ldh      [16]
			(003) jeq      #0x606           jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # link_proto_6_LINUX_SLL
	{
		name => 'link_proto_iso_LINUX_SLL',
		DLT => 'LINUX_SLL',
		aliases => ['link proto \iso'],
		unopt => '
			(000) ldh      [14]
			(001) jeq      #0x4             jt 2	jf 5
			(002) ldh      [16]
			(003) jeq      #0xfefe          jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # link_proto_iso_LINUX_SLL
	{
		name => 'link_proto_netbeui_LINUX_SLL',
		DLT => 'LINUX_SLL',
		aliases => ['link proto \netbeui'],
		unopt => '
			(000) ldh      [14]
			(001) jeq      #0x4             jt 2	jf 5
			(002) ldh      [16]
			(003) jeq      #0xf0f0          jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # link_proto_netbeui_LINUX_SLL
	{
		name => 'link_proto_ipx_LINUX_SLL',
		DLT => 'LINUX_SLL',
		aliases => ['link proto \ipx'],
		opt => '
			(000) ldh      [14]
			(001) jeq      #0x8137          jt 10	jf 2
			(002) jeq      #0x1             jt 10	jf 3
			(003) jeq      #0x4             jt 4	jf 11
			(004) ldb      [16]
			(005) jeq      #0xe0            jt 10	jf 6
			(006) ld       [20]
			(007) jeq      #0x8137          jt 8	jf 11
			(008) ld       [16]
			(009) jeq      #0xaaaa0300      jt 10	jf 11
			(010) ret      #262144
			(011) ret      #0
			',
	}, # link_proto_ipx_LINUX_SLL
	{
		name => 'link_proto_atalk_LINUX_SLL',
		DLT => 'LINUX_SLL',
		aliases => ['link proto \atalk'],
		opt => '
			(000) ldh      [14]
			(001) jeq      #0x809b          jt 7	jf 2
			(002) jeq      #0x4             jt 3	jf 8
			(003) ld       [20]
			(004) jeq      #0x7809b         jt 5	jf 8
			(005) ld       [16]
			(006) jeq      #0xaaaa0308      jt 7	jf 8
			(007) ret      #262144
			(008) ret      #0
			',
	}, # link_proto_atalk_LINUX_SLL
	{
		name => 'link_proto_aarp_LINUX_SLL',
		DLT => 'LINUX_SLL',
		aliases => ['link proto \aarp'],
		opt => '
			(000) ldh      [14]
			(001) jeq      #0x80f3          jt 7	jf 2
			(002) jeq      #0x4             jt 3	jf 8
			(003) ld       [20]
			(004) jeq      #0x80f3          jt 5	jf 8
			(005) ld       [16]
			(006) jeq      #0xaaaa0300      jt 7	jf 8
			(007) ret      #262144
			(008) ret      #0
			',
	}, # link_proto_aarp_LINUX_SLL
	{
		name => 'link_proto_ip_SLIP',
		DLT => 'SLIP',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # link_proto_ip_SLIP
	{
		name => 'link_proto_ip_SLIP_BSDOS',
		DLT => 'SLIP_BSDOS',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # link_proto_ip_SLIP_BSDOS
	{
		name => 'link_proto_ip_RAW',
		DLT => 'RAW',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # link_proto_ip_RAW
	{
		name => 'link_proto_ip6_RAW',
		DLT => 'RAW',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x60            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # link_proto_ip6_RAW
	{
		name => 'link_proto_stp_RAW',
		DLT => 'RAW',
		aliases => ['link proto \stp'],
		unopt => '
			(000) ld       #0x1
			(001) jeq      #0x0             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_stp_RAW
	{
		name => 'link_proto_ip_IPV4',
		DLT => 'IPV4',
		aliases => ['link proto \ip'],
		opt => '
			(000) ret      #262144
			',
	}, # link_proto_ip_IPV4
	{
		name => 'link_proto_ip6_IPV4',
		DLT => 'IPV4',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       #0x1
			(001) jeq      #0x0             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_IPV4
	{
		name => 'link_proto_ip6_IPV6',
		DLT => 'IPV6',
		aliases => ['link proto \ip6'],
		opt => '
			(000) ret      #262144
			',
	}, # link_proto_ip6_IPV6
	{
		name => 'link_proto_ip_IPV6',
		DLT => 'IPV6',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ld       #0x1
			(001) jeq      #0x0             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_IPV6
	# 4 DLTs lead to ethertype_to_ppptype(), which implements 9 code paths,
	# let's test these for DLT_PPP only.
	{
		name => 'link_proto_ip_PPP',
		DLT => 'PPP',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x21            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_PPP
	{
		name => 'link_proto_ip6_PPP',
		DLT => 'PPP',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x57            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_PPP
	{
		name => 'link_proto_decnet_PPP',
		DLT => 'PPP',
		aliases => ['link proto \decnet'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x27            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_decnet_PPP
	{
		name => 'link_proto_atalk_PPP',
		DLT => 'PPP',
		aliases => ['link proto \atalk'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x29            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_atalk_PPP
	{
		name => 'link_proto_xnsidp_PPP',
		DLT => 'PPP',
		aliases => ['link proto 0x0600'], # ethertype_to_ppptype() ETHERTYPE_NS
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x25            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_xnsidp_PPP
	{
		name => 'link_proto_iso_PPP',
		DLT => 'PPP',
		aliases => ['link proto \iso'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x23            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_iso_PPP
	{
		name => 'link_proto_stp_PPP',
		DLT => 'PPP',
		aliases => ['link proto \stp'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x31            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_stp_PPP
	{
		name => 'link_proto_lat_PPP',
		DLT => 'PPP',
		aliases => ['link proto \lat'], # ethertype_to_ppptype() default case
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x6004          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_lat_PPP
	{
		name => 'link_proto_ipx_PPP',
		DLT => 'PPP',
		aliases => ['link proto \ipx'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x2b            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ipx_PPP
	{
		name => 'link_proto_ip_PPP_PPPD',
		DLT => 'PPP_PPPD',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x21            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_PPP_PPPD
	{
		name => 'link_proto_ip_PPP_SERIAL',
		DLT => 'PPP_SERIAL',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x21            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_PPP_SERIAL
	{
		name => 'link_proto_ip_PPP_ETHER',
		DLT => 'PPP_ETHER',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldh      [6]
			(001) jeq      #0x21            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_PPP_ETHER
	{
		name => 'link_proto_ip_PPP_BSDOS',
		DLT => 'PPP_BSDOS',
		aliases => ['link proto \ip'],
		opt => '
			(000) ldh      [5]
			(001) jeq      #0x21            jt 4	jf 2
			(002) jeq      #0x2d            jt 4	jf 3
			(003) jeq      #0x2f            jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # link_proto_ip_PPP_BSDOS
	{
		name => 'link_proto_ip6_PPP_BSDOS',
		DLT => 'PPP_BSDOS',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ldh      [5]
			(001) jeq      #0x57            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_PPP_BSDOS

	# DLT_NULL and DLT_ENC depend on the values of AF_INET and AF_INET6,
	# which are OS-specific, and on the host byte order.  Exercise these
	# dimensions completely for DLT_NULL only.
	{
		name => 'link_proto_ip_1LE_NULL',
		skip => skip_big_endian() ||
			skip_os_not ('haiku'),
		DLT => 'NULL',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x1000000       jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_1LE_NULL
	{
		name => 'link_proto_ip_2LE_NULL',
		skip => skip_big_endian() ||
			skip_os ('haiku'),
		DLT => 'NULL',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x2000000       jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_2LE_NULL
	{
		name => 'link_proto_ip_2BE_NULL',
		skip => skip_little_endian(),
		DLT => 'NULL',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x2             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_2BE_NULL
	{
		name => 'link_proto_ip6_5LE_NULL',
		skip => skip_big_endian() ||
			skip_os_not ('haiku'),
		DLT => 'NULL',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x5000000       jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_5LE_NULL
	{
		name => 'link_proto_ip6_10LE_NULL',
		skip => skip_big_endian() ||
			skip_os_not ('linux'),
		DLT => 'NULL',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0xa000000       jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_10LE_NULL
	{
		name => 'link_proto_ip6_10BE_NULL',
		skip => skip_little_endian() ||
			skip_os_not ('linux'),
		DLT => 'NULL',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0xa             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_10BE_NULL
	{
		name => 'link_proto_ip6_22BE_NULL',
		skip => skip_little_endian() ||
			skip_os_not ('hpux'),
		DLT => 'NULL',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x16            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_22BE_NULL
	{
		name => 'link_proto_ip6_24LE_NULL',
		skip => skip_big_endian() ||
			(skip_os_not ('aix') && skip_os_not ('netbsd') && skip_os_not ('openbsd')),
		DLT => 'NULL',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x18000000      jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_24LE_NULL
	{
		name => 'link_proto_ip6_24BE_NULL',
		skip => skip_little_endian() ||
			(skip_os_not ('aix') && skip_os_not ('netbsd') && skip_os_not ('openbsd')),
		DLT => 'NULL',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x18            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_24BE_NULL
	{
		name => 'link_proto_ip6_26LE_NULL',
		skip => skip_big_endian() ||
			skip_os_not ('solaris'),
		DLT => 'NULL',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x1a000000      jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_26LE_NULL
	{
		name => 'link_proto_ip6_26BE_NULL',
		skip => skip_little_endian() ||
			skip_os_not ('solaris'),
		DLT => 'NULL',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x1a            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_26BE_NULL
	{
		name => 'link_proto_ip6_28LE_NULL',
		skip => skip_big_endian() ||
			(skip_os_not ('dragonfly') && skip_os_not ('freebsd')),
		DLT => 'NULL',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x1c000000      jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_28LE_NULL
	{
		name => 'link_proto_ip6_28BE_NULL',
		skip => skip_little_endian() ||
			(skip_os_not ('dragonfly') && skip_os_not ('freebsd')),
		DLT => 'NULL',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x1c            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_28BE_NULL
	{
		name => 'link_proto_ip6_30LE_NULL',
		skip => skip_big_endian() ||
			skip_os_not ('darwin'),
		DLT => 'NULL',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x1e000000      jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_30LE_NULL
	{
		name => 'link_proto_ip6_30BE_NULL',
		skip => skip_little_endian() ||
			skip_os_not ('darwin'),
		DLT => 'NULL',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x1e            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_30BE_NULL
	{
		name => 'link_proto_stp_NULL',
		# The same code path for DLT_ENC and DLT_LOOP.
		DLT => 'NULL',
		aliases => ['link proto \stp'],
		unopt => '
			(000) ld       #0x1
			(001) jeq      #0x0             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_stp_NULL
	{
		name => 'link_proto_ip_2LE_ENC',
		skip => skip_big_endian() ||
			skip_os_not ('linux'),
		DLT => 'ENC',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x2000000       jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_2LE_ENC
	{
		name => 'link_proto_ip6_28LE_ENC',
		skip => skip_big_endian() ||
			(skip_os_not ('dragonfly') && skip_os_not ('freebsd')),
		DLT => 'ENC',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x1c000000      jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_28LE_ENC
	# DLT_LOOP and DLT_PFLOG depend on the values of AF_INET and AF_INET6,
	# which are OS-specific.  Exercise this dimension completely for
	# DLT_LOOP only.
	{
		name => 'link_proto_ip_1_LOOP',
		skip => skip_os_not ('haiku'),
		DLT => 'LOOP',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x1             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_1_LOOP
	{
		name => 'link_proto_ip_2_LOOP',
		skip => skip_os ('haiku'),
		DLT => 'LOOP',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x2             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_2_LOOP
	{
		name => 'link_proto_ip6_5_LOOP',
		skip => skip_os_not ('haiku'),
		DLT => 'LOOP',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x5             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_5_LOOP
	{
		name => 'link_proto_ip6_10_LOOP',
		skip => skip_os_not ('linux'),
		DLT => 'LOOP',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0xa             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_10_LOOP
	{
		name => 'link_proto_ip6_22_LOOP',
		skip => skip_os_not ('hpux'),
		DLT => 'LOOP',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x16            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_22_LOOP
	{
		name => 'link_proto_ip6_24_LOOP',
		skip => (skip_os_not ('aix') && skip_os_not ('netbsd') && skip_os_not ('openbsd')),
		DLT => 'LOOP',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x18            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_24_LOOP
	{
		name => 'link_proto_ip6_26_LOOP',
		skip => skip_os_not ('solaris'),
		DLT => 'LOOP',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x1a            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_26_LOOP
	{
		name => 'link_proto_ip6_28_LOOP',
		skip => (skip_os_not ('dragonfly') && skip_os_not ('freebsd')),
		DLT => 'LOOP',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x1c            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_28_LOOP
	{
		name => 'link_proto_ip6_30_LOOP',
		skip => skip_os_not ('darwin'),
		DLT => 'LOOP',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ld       [0]
			(001) jeq      #0x1e            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_30_LOOP
	{
		name => 'link_proto_ip_2_PFLOG',
		skip => skip_os ('haiku'),
		DLT => 'PFLOG',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldb      [1]
			(001) jeq      #0x2             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_2_PFLOG
	{
		name => 'link_proto_ip6_24_PFLOG',
		skip => (skip_os_not ('aix') && skip_os_not ('netbsd') && skip_os_not ('openbsd')),
		DLT => 'PFLOG',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ldb      [1]
			(001) jeq      #0x18            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_PFLOG
	{
		name => 'link_proto_stp_PFLOG',
		DLT => 'PFLOG',
		aliases => ['link proto \stp'],
		unopt => '
			(000) ld       #0x1
			(001) jeq      #0x0             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_stp_PFLOG
	{
		name => 'link_proto_stp_ARCNET',
		DLT => 'ARCNET',
		aliases => ['link proto \stp'],
		unopt => '
			(000) ld       #0x1
			(001) jeq      #0x0             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_stp_ARCNET
	{
		name => 'link_proto_ip6_ARCNET',
		DLT => 'ARCNET',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0xc4            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_ARCNET
	{
		name => 'link_proto_ip_ARCNET',
		DLT => 'ARCNET',
		aliases => ['link proto \ip'],
		opt => '
			(000) ldb      [2]
			(001) jeq      #0xd4            jt 3	jf 2
			(002) jeq      #0xf0            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # link_proto_ip_ARCNET
	{
		name => 'link_proto_arp_ARCNET',
		DLT => 'ARCNET',
		aliases => ['link proto \arp'],
		opt => '
			(000) ldb      [2]
			(001) jeq      #0xd5            jt 3	jf 2
			(002) jeq      #0xf1            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # link_proto_arp_ARCNET
	{
		name => 'link_proto_rarp_ARCNET',
		DLT => 'ARCNET',
		aliases => ['link proto \rarp'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0xd6            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_rarp_ARCNET
	{
		name => 'link_proto_atalk_ARCNET',
		DLT => 'ARCNET',
		aliases => ['link proto \atalk'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0xdd            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_atalk_ARCNET
	{
		name => 'link_proto_ip6_ARCNET_LINUX',
		DLT => 'ARCNET_LINUX',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ldb      [4]
			(001) jeq      #0xc4            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_ARCNET_LINUX
	{
		name => 'link_proto_ip_ARCNET_LINUX',
		DLT => 'ARCNET_LINUX',
		aliases => ['link proto \ip'],
		opt => '
			(000) ldb      [4]
			(001) jeq      #0xd4            jt 3	jf 2
			(002) jeq      #0xf0            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # link_proto_ip_ARCNET_LINUX
	{
		name => 'link_proto_arp_ARCNET_LINUX',
		DLT => 'ARCNET_LINUX',
		aliases => ['link proto \arp'],
		opt => '
			(000) ldb      [4]
			(001) jeq      #0xd5            jt 3	jf 2
			(002) jeq      #0xf1            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # link_proto_arp_ARCNET_LINUX
	{
		name => 'link_proto_rarp_ARCNET_LINUX',
		DLT => 'ARCNET_LINUX',
		aliases => ['link proto \rarp'],
		unopt => '
			(000) ldb      [4]
			(001) jeq      #0xd6            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_rarp_ARCNET_LINUX
	{
		name => 'link_proto_atalk_ARCNET_LINUX',
		DLT => 'ARCNET_LINUX',
		aliases => ['link proto \atalk'],
		unopt => '
			(000) ldb      [4]
			(001) jeq      #0xdd            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_atalk_ARCNET_LINUX
	{
		name => 'link_proto_atalk_LTALK',
		DLT => 'LTALK',
		aliases => ['link proto \atalk'],
		opt => '
			(000) ret      #262144
			',
	}, # link_proto_atalk_LTALK
	{
		name => 'link_proto_ip_LTALK',
		DLT => 'LTALK',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ld       #0x1
			(001) jeq      #0x0             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_LTALK
	{
		name => 'link_proto_ip_FRELAY',
		DLT => 'FRELAY',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x3cc           jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_FRELAY
	{
		name => 'link_proto_ip6_FRELAY',
		DLT => 'FRELAY',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x38e           jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_FRELAY
	{
		name => 'link_proto_iso_FRELAY',
		DLT => 'FRELAY',
		aliases => ['link proto \iso'],
		opt => '
			(000) ldh      [2]
			(001) jeq      #0x381           jt 4	jf 2
			(002) jeq      #0x382           jt 4	jf 3
			(003) jeq      #0x383           jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # link_proto_iso_FRELAY
	{
		name => 'link_proto_stp_FRELAY',
		DLT => 'FRELAY',
		aliases => ['link proto \stp'],
		unopt => '
			(000) ld       #0x1
			(001) jeq      #0x0             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_arp_FRELAY
	{
		name => 'link_proto_ip_JUNIPER_MFR',
		DLT => 'JUNIPER_MFR',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ld       [0]
			(001) and      #0xffffff00
			(002) jeq      #0x4d474300      jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # link_proto_ip_JUNIPER_MFR
	{
		name => 'link_proto_ip_BACNET_MS_TP',
		DLT => 'BACNET_MS_TP',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ld       [0]
			(001) and      #0xffff0000
			(002) jeq      #0x55ff0000      jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # link_proto_ip_BACNET_MS_TP
	{
		name => 'link_proto_ip_IPNET',
		DLT => 'IPNET',
		aliases => ['link proto \ip'],
		unopt => '
			(000) ldb      [1]
			(001) jeq      #0x2             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip_IPNET
	{
		name => 'link_proto_ip6_IPNET',
		DLT => 'IPNET',
		aliases => ['link proto \ip6'],
		unopt => '
			(000) ldb      [1]
			(001) jeq      #0x1a            jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_ip6_IPNET
	{
		name => 'link_proto_stp_IPNET',
		DLT => 'IPNET',
		aliases => ['link proto \stp'],
		unopt => '
			(000) ld       #0x1
			(001) jeq      #0x0             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_stp_IPNET

	# Edge cases for LLC/EtherType.
	{
		name => 'link_proto_0_EN10MB',
		DLT => 'EN10MB',
		aliases => ['link proto 0'],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 5	jf 2
			(002) ldb      [14]
			(003) jeq      #0x0             jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # link_proto_0_EN10MB
	{
		name => 'link_proto_255_EN10MB',
		DLT => 'EN10MB',
		aliases => ['link proto 255'],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 5	jf 2
			(002) ldb      [14]
			(003) jeq      #0xff            jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # link_proto_255_EN10MB
	{
		name => 'link_proto_1501_EN10MB',
		DLT => 'EN10MB',
		aliases => ['link proto 1501'],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x5dd           jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_1501_EN10MB
	{
		name => 'link_proto_65535_EN10MB',
		DLT => 'EN10MB',
		aliases => ['link proto 65535'],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0xffff          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # link_proto_65535_EN10MB

	# ARP and RARP tests are interleaved for ease of cross-reference
	# because ARP filter programs and RARP filter programs differ in the
	# link-layer protocol code point only (0x8035 instead of 0x0806 for
	# most DLTs, 0xD6 instead of {0xD5, 0xF1} for ARCnet).
	{
		name => 'arp_host_addr_en10mb',
		DLT => 'EN10MB',
		aliases => [
			'arp host *******',
			'arp src or dst *******',
			'arp src or dst host *******',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x806           jt 2	jf 7
			(002) ld       [28]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [38]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # arp_host_addr_en10mb
	{
		name => 'rarp_host_addr_en10mb',
		DLT => 'EN10MB',
		aliases => [
			'rarp host *******',
			'rarp src or dst *******',
			'rarp src or dst host *******',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x8035          jt 2	jf 7
			(002) ld       [28]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [38]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # rarp_host_addr_en10mb
	{
		name => 'arp_host_name_en10mb',
		skip => skip_no_hosts(),
		DLT => 'EN10MB',
		aliases => [
			'arp host noeth-ipv4-noipv6.host123.libpcap.test',
			'arp src or dst noeth-ipv4-noipv6.host123.libpcap.test',
			'arp src or dst host noeth-ipv4-noipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x806           jt 2	jf 7
			(002) ld       [28]
			(003) jeq      #0xa141e28       jt 6	jf 4
			(004) ld       [38]
			(005) jeq      #0xa141e28       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # arp_host_name_en10mb
	{
		name => 'rarp_host_name_en10mb',
		skip => skip_no_hosts(),
		DLT => 'EN10MB',
		aliases => [
			'rarp host noeth-ipv4-noipv6.host123.libpcap.test',
			'rarp src or dst noeth-ipv4-noipv6.host123.libpcap.test',
			'rarp src or dst host noeth-ipv4-noipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x8035          jt 2	jf 7
			(002) ld       [28]
			(003) jeq      #0xa141e28       jt 6	jf 4
			(004) ld       [38]
			(005) jeq      #0xa141e28       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # rarp_host_name_en10mb
	{
		name => 'arp_host_NAME_en10mb',
		skip => skip_no_hosts_casecmp(),
		DLT => 'EN10MB',
		aliases => [
			'arp host NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'arp src or dst NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'arp src or dst host NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x806           jt 2	jf 7
			(002) ld       [28]
			(003) jeq      #0xa141e28       jt 6	jf 4
			(004) ld       [38]
			(005) jeq      #0xa141e28       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # arp_host_NAME_en10mb
	{
		name => 'rarp_host_NAME_en10mb',
		skip => skip_no_hosts_casecmp(),
		DLT => 'EN10MB',
		aliases => [
			'rarp host NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'rarp src or dst NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'rarp src or dst host NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x8035          jt 2	jf 7
			(002) ld       [28]
			(003) jeq      #0xa141e28       jt 6	jf 4
			(004) ld       [38]
			(005) jeq      #0xa141e28       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # rarp_host_NAME_en10mb
	{
		name => 'arp_src_addr_en10mb',
		DLT => 'EN10MB',
		aliases => [
			'arp src *******',
			'arp src host *******',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x806           jt 2	jf 5
			(002) ld       [28]
			(003) jeq      #0x1020304       jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # arp_src_addr_en10mb
	{
		name => 'rarp_src_addr_en10mb',
		DLT => 'EN10MB',
		aliases => [
			'rarp src *******',
			'rarp src host *******',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x8035          jt 2	jf 5
			(002) ld       [28]
			(003) jeq      #0x1020304       jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # rarp_src_addr_en10mb
	{
		name => 'arp_src_name_en10mb',
		skip => skip_no_hosts(),
		DLT => 'EN10MB',
		aliases => [
			'arp src noeth-ipv4-noipv6.host123.libpcap.test',
			'arp src host noeth-ipv4-noipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x806           jt 2	jf 5
			(002) ld       [28]
			(003) jeq      #0xa141e28       jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # arp_src_name_en10mb
	{
		name => 'rarp_src_name_en10mb',
		skip => skip_no_hosts(),
		DLT => 'EN10MB',
		aliases => [
			'rarp src noeth-ipv4-noipv6.host123.libpcap.test',
			'rarp src host noeth-ipv4-noipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x8035          jt 2	jf 5
			(002) ld       [28]
			(003) jeq      #0xa141e28       jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # rarp_src_name_en10mb
	{
		name => 'arp_src_NAME_en10mb',
		skip => skip_no_hosts_casecmp(),
		DLT => 'EN10MB',
		aliases => [
			'arp src NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'arp src host NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x806           jt 2	jf 5
			(002) ld       [28]
			(003) jeq      #0xa141e28       jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # arp_src_NAME_en10mb
	{
		name => 'rarp_src_NAME_en10mb',
		skip => skip_no_hosts_casecmp(),
		DLT => 'EN10MB',
		aliases => [
			'rarp src NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'rarp src host NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x8035          jt 2	jf 5
			(002) ld       [28]
			(003) jeq      #0xa141e28       jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # rarp_src_NAME_en10mb
	{
		name => 'arp_dst_addr_en10mb',
		DLT => 'EN10MB',
		aliases => [
			'arp dst *******',
			'arp dst host *******',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x806           jt 2	jf 5
			(002) ld       [38]
			(003) jeq      #0x1020304       jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
		',
	}, # arp_dst_addr_en10mb
	{
		name => 'rarp_dst_addr_en10mb',
		DLT => 'EN10MB',
		aliases => [
			'rarp dst *******',
			'rarp dst host *******',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x8035          jt 2	jf 5
			(002) ld       [38]
			(003) jeq      #0x1020304       jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
		',
	}, # rarp_dst_addr_en10mb
	{
		name => 'arp_dst_name_en10mb',
		skip => skip_no_hosts(),
		DLT => 'EN10MB',
		aliases => [
			'arp dst noeth-ipv4-noipv6.host123.libpcap.test',
			'arp dst host noeth-ipv4-noipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x806           jt 2	jf 5
			(002) ld       [38]
			(003) jeq      #0xa141e28       jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # arp_dst_name_en10mb
	{
		name => 'rarp_dst_name_en10mb',
		skip => skip_no_hosts(),
		DLT => 'EN10MB',
		aliases => [
			'rarp dst noeth-ipv4-noipv6.host123.libpcap.test',
			'rarp dst host noeth-ipv4-noipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x8035          jt 2	jf 5
			(002) ld       [38]
			(003) jeq      #0xa141e28       jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # rarp_dst_name_en10mb
	{
		name => 'arp_dst_NAME_en10mb',
		skip => skip_no_hosts_casecmp(),
		DLT => 'EN10MB',
		aliases => [
			'arp dst NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'arp dst host NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x806           jt 2	jf 5
			(002) ld       [38]
			(003) jeq      #0xa141e28       jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # arp_dst_NAME_en10mb
	{
		name => 'rarp_dst_NAME_en10mb',
		skip => skip_no_hosts_casecmp(),
		DLT => 'EN10MB',
		aliases => [
			'rarp dst NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'rarp dst host NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x8035          jt 2	jf 5
			(002) ld       [38]
			(003) jeq      #0xa141e28       jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # rarp_dst_NAME_en10mb
	# Exercise other DLTs briefly only to touch some of the various code paths
	# in gen_linktype() with different L2 headers and offsets for the SPA
	# and TPA fields.
	{
		name => 'arp_host_addr_c_hdlc',
		DLT => 'C_HDLC',
		aliases => ['arp host *******'],
		opt => '
			(000) ldh      [2]
			(001) jeq      #0x806           jt 2	jf 7
			(002) ld       [18]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [28]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # arp_host_addr_c_hdlc
	{
		name => 'arp_host_addr_c_hdlc',
		DLT => 'C_HDLC',
		aliases => ['rarp host *******'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x8035          jt 2	jf 7
			(002) ld       [18]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [28]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # rarp_host_addr_c_hdlc
	{
		name => 'arp_host_addr_ieee802_11',
		DLT => 'IEEE802_11',
		aliases => ['arp host *******'],
		opt => '
			(000) ldx      #0x0
			(001) txa
			(002) add      #24
			(003) st       M[0]
			(004) ldb      [x + 0]
			(005) jset     #0x8             jt 6	jf 11
			(006) jset     #0x4             jt 11	jf 7
			(007) jset     #0x80            jt 8	jf 11
			(008) ld       M[0]
			(009) add      #2
			(010) st       M[0]
			(011) ldb      [0]
			(012) and      #0xc
			(013) jeq      #0x8             jt 14	jf 24
			(014) ldx      M[0]
			(015) ldh      [x + 6]
			(016) jeq      #0x806           jt 17	jf 24
			(017) ldx      M[0]
			(018) ld       [x + 22]
			(019) jeq      #0x1020304       jt 23	jf 20
			(020) ldx      M[0]
			(021) ld       [x + 32]
			(022) jeq      #0x1020304       jt 23	jf 24
			(023) ret      #262144
			(024) ret      #0
			',
	}, # arp_host_addr_ieee802_11
	{
		name => 'rarp_host_addr_ieee802_11',
		DLT => 'IEEE802_11',
		aliases => ['rarp host *******'],
		opt => '
			(000) ldx      #0x0
			(001) txa
			(002) add      #24
			(003) st       M[0]
			(004) ldb      [x + 0]
			(005) jset     #0x8             jt 6	jf 11
			(006) jset     #0x4             jt 11	jf 7
			(007) jset     #0x80            jt 8	jf 11
			(008) ld       M[0]
			(009) add      #2
			(010) st       M[0]
			(011) ldb      [0]
			(012) and      #0xc
			(013) jeq      #0x8             jt 14	jf 24
			(014) ldx      M[0]
			(015) ldh      [x + 6]
			(016) jeq      #0x8035          jt 17	jf 24
			(017) ldx      M[0]
			(018) ld       [x + 22]
			(019) jeq      #0x1020304       jt 23	jf 20
			(020) ldx      M[0]
			(021) ld       [x + 32]
			(022) jeq      #0x1020304       jt 23	jf 24
			(023) ret      #262144
			(024) ret      #0
			',
	}, # rarp_host_addr_ieee802_11
	{
		name => 'arp_host_addr_fddi',
		DLT => 'FDDI',
		aliases => ['arp host *******'],
		unopt => '
			(000) ldh      [19]
			(001) jeq      #0x806           jt 2	jf 7
			(002) ld       [35]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [45]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # arp_host_addr_fddi
	{
		name => 'rarp_host_addr_fddi',
		DLT => 'FDDI',
		aliases => ['rarp host *******'],
		unopt => '
			(000) ldh      [19]
			(001) jeq      #0x8035          jt 2	jf 7
			(002) ld       [35]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [45]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # rarp_host_addr_fddi
	{
		name => 'arp_host_addr_ieee802',
		DLT => 'IEEE802',
		aliases => ['arp host *******'],
		unopt => '
			(000) ldh      [20]
			(001) jeq      #0x806           jt 2	jf 7
			(002) ld       [36]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [46]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # arp_host_addr_ieee802
	{
		name => 'rarp_host_addr_ieee802',
		DLT => 'IEEE802',
		aliases => ['rarp host *******'],
		unopt => '
			(000) ldh      [20]
			(001) jeq      #0x8035          jt 2	jf 7
			(002) ld       [36]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [46]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # rarp_host_addr_ieee802
	{
		name => 'arp_host_addr_ip_over_fc',
		DLT => 'IP_OVER_FC',
		aliases => ['arp host *******'],
		unopt => '
			(000) ldh      [22]
			(001) jeq      #0x806           jt 2	jf 7
			(002) ld       [38]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [48]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # arp_host_addr_ip_over_fc
	{
		name => 'rarp_host_addr_ip_over_fc',
		DLT => 'IP_OVER_FC',
		aliases => ['rarp host *******'],
		unopt => '
			(000) ldh      [22]
			(001) jeq      #0x8035          jt 2	jf 7
			(002) ld       [38]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [48]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # rarp_host_addr_ip_over_fc
	{
		name => 'arp_host_addr_sunatm',
		DLT => 'SUNATM',
		aliases => ['arp host *******'],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf
			(002) jeq      #0x2             jt 3	jf 10
			(003) ldh      [10]
			(004) jeq      #0x806           jt 5	jf 10
			(005) ld       [26]
			(006) jeq      #0x1020304       jt 9	jf 7
			(007) ld       [36]
			(008) jeq      #0x1020304       jt 9	jf 10
			(009) ret      #262144
			(010) ret      #0
			',
	}, # arp_host_addr_sunatm
	{
		name => 'rarp_host_addr_sunatm',
		DLT => 'SUNATM',
		aliases => ['rarp host *******'],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf
			(002) jeq      #0x2             jt 3	jf 10
			(003) ldh      [10]
			(004) jeq      #0x8035          jt 5	jf 10
			(005) ld       [26]
			(006) jeq      #0x1020304       jt 9	jf 7
			(007) ld       [36]
			(008) jeq      #0x1020304       jt 9	jf 10
			(009) ret      #262144
			(010) ret      #0
			',
	}, # rarp_host_addr_sunatm
	{
		name => 'arp_host_addr_linux_sll',
		DLT => 'LINUX_SLL',
		aliases => ['arp host *******'],
		unopt => '
			(000) ldh      [14]
			(001) jeq      #0x806           jt 2	jf 7
			(002) ld       [30]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [40]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # arp_host_addr_linux_sll
	{
		name => 'rarp_host_addr_linux_sll',
		DLT => 'LINUX_SLL',
		aliases => ['rarp host *******'],
		unopt => '
			(000) ldh      [14]
			(001) jeq      #0x8035          jt 2	jf 7
			(002) ld       [30]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [40]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # rarp_host_addr_linux_sll
	{
		name => 'arp_host_addr_raw',
		DLT => 'RAW',
		aliases => ['arp host *******'],
		# This boils down to "ret #0", which the optimizer expectedly rejects.
		unopt => '
			(000) ld       #0x1
			(001) jeq      #0x0             jt 2	jf 7
			(002) ld       [14]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [24]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # arp_host_addr_raw
	{
		name => 'rarp_host_addr_raw',
		DLT => 'RAW',
		aliases => ['rarp host *******'],
		unopt => '
			(000) ld       #0x1
			(001) jeq      #0x0             jt 2	jf 7
			(002) ld       [14]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [24]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # rarp_host_addr_raw
	{
		name => 'arp_host_addr_ppp',
		DLT => 'PPP',
		aliases => ['arp host *******'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x806           jt 2	jf 7
			(002) ld       [18]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [28]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # arp_host_addr_ppp
	{
		name => 'rarp_host_addr_ppp',
		DLT => 'PPP',
		aliases => ['rarp host *******'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x8035          jt 2	jf 7
			(002) ld       [18]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [28]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # rarp_host_addr_ppp
	{
		name => 'arp_host_addr_ppp_bsdos',
		DLT => 'PPP_BSDOS',
		aliases => ['arp host *******'],
		unopt => '
			(000) ldh      [5]
			(001) jeq      #0x806           jt 2	jf 7
			(002) ld       [38]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [48]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # arp_host_addr_ppp_bsdos
	{
		name => 'rarp_host_addr_ppp_bsdos',
		DLT => 'PPP_BSDOS',
		aliases => ['rarp host *******'],
		unopt => '
			(000) ldh      [5]
			(001) jeq      #0x8035          jt 2	jf 7
			(002) ld       [38]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [48]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # rarp_host_addr_ppp_bsdos
	{
		name => 'arp_host_addr_arcnet',
		DLT => 'ARCNET',
		aliases => ['arp host *******'],
		opt => '
			(000) ldb      [2]
			(001) jeq      #0xd5            jt 3	jf 2
			(002) jeq      #0xf1            jt 3	jf 8
			(003) ld       [20]
			(004) jeq      #0x1020304       jt 7	jf 5
			(005) ld       [30]
			(006) jeq      #0x1020304       jt 7	jf 8
			(007) ret      #262144
			(008) ret      #0
			',
	}, # arp_host_addr_arcnet
	{
		name => 'rarp_host_addr_arcnet',
		DLT => 'ARCNET',
		aliases => ['rarp host *******'],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0xd6            jt 2	jf 7
			(002) ld       [20]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [30]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # rarp_host_addr_arcnet
	# At the time of this writing the DLTs below stand for the default case
	# in gen_linktype().
	{
		name => 'arp_host_addr_linux_sll2',
		DLT => 'LINUX_SLL2',
		aliases => ['arp host *******'],
		unopt => '
			(000) ldh      [0]
			(001) jeq      #0x806           jt 2	jf 7
			(002) ld       [34]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [44]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # arp_host_addr_linux_sll2
	{
		name => 'rarp_host_addr_linux_sll2',
		DLT => 'LINUX_SLL2',
		aliases => ['rarp host *******'],
		unopt => '
			(000) ldh      [0]
			(001) jeq      #0x8035          jt 2	jf 7
			(002) ld       [34]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [44]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # rarp_host_addr_linux_sll2
	{
		name => 'arp_host_addr_symfw',
		DLT => 'SYMANTEC_FIREWALL',
		aliases => ['arp host *******'],
		unopt => '
			(000) ldh      [6]
			(001) jeq      #0x806           jt 2	jf 7
			(002) ld       [58]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [68]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # arp_host_addr_symfw
	{
		name => 'rarp_host_addr_symfw',
		DLT => 'SYMANTEC_FIREWALL',
		aliases => ['rarp host *******'],
		unopt => '
			(000) ldh      [6]
			(001) jeq      #0x8035          jt 2	jf 7
			(002) ld       [58]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [68]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # rarp_host_addr_symfw
	{
		name => 'arp_host_addr_ipoieee1394',
		DLT => 'APPLE_IP_OVER_IEEE1394',
		aliases => ['arp host *******'],
		unopt => '
			(000) ldh      [16]
			(001) jeq      #0x806           jt 2	jf 7
			(002) ld       [32]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [42]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # arp_host_addr_ipoieee1394
	{
		name => 'rarp_host_addr_ipoieee1394',
		DLT => 'APPLE_IP_OVER_IEEE1394',
		aliases => ['rarp host *******'],
		unopt => '
			(000) ldh      [16]
			(001) jeq      #0x8035          jt 2	jf 7
			(002) ld       [32]
			(003) jeq      #0x1020304       jt 6	jf 4
			(004) ld       [42]
			(005) jeq      #0x1020304       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # rarp_host_addr_ipoieee1394

	{
		name => 'arp_net_addr',
		DLT => 'EN10MB',
		snaplen => 2000,
		aliases => [
			'arp net ***********/16',
			'arp src or dst net ***********/16',
			'arp net 192.168/16',
			'arp src or dst net 192.168/16',
			'arp net *********** mask ***********',
			'arp src or dst net *********** mask ***********',
			'arp net *********** mask 255.255',
			'arp src or dst net *********** mask 255.255',
			'arp net 192.168 mask ***********',
			'arp src or dst net 192.168 mask ***********',
			'arp net 192.168 mask 255.255',
			'arp src or dst net 192.168 mask 255.255',
			'arp net 192.168',
			'arp src or dst net 192.168',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x806           jt 2	jf 9
			(002) ld       [28]
			(003) and      #0xffff0000
			(004) jeq      #0xc0a80000      jt 8	jf 5
			(005) ld       [38]
			(006) and      #0xffff0000
			(007) jeq      #0xc0a80000      jt 8	jf 9
			(008) ret      #2000
			(009) ret      #0
			',
	}, # arp_net_addr
	{
		name => 'rarp_net_addr',
		DLT => 'LINUX_SLL2',
		snaplen => 2000,
		aliases => [
			'rarp net ***********/16',
			'rarp src or dst net ***********/16',
			'rarp net 192.168/16',
			'rarp src or dst net 192.168/16',
			'rarp net *********** mask ***********',
			'rarp src or dst net *********** mask ***********',
			'rarp net *********** mask 255.255',
			'rarp src or dst net *********** mask 255.255',
			'rarp net 192.168 mask ***********',
			'rarp src or dst net 192.168 mask ***********',
			'rarp net 192.168 mask 255.255',
			'rarp src or dst net 192.168 mask 255.255',
			'rarp net 192.168',
			'rarp src or dst net 192.168',
		],
		unopt => '
			(000) ldh      [0]
			(001) jeq      #0x8035          jt 2	jf 9
			(002) ld       [34]
			(003) and      #0xffff0000
			(004) jeq      #0xc0a80000      jt 8	jf 5
			(005) ld       [44]
			(006) and      #0xffff0000
			(007) jeq      #0xc0a80000      jt 8	jf 9
			(008) ret      #2000
			(009) ret      #0
			',
	}, # rarp_net_addr
	{
		name => 'arp_net_name',
		skip => skip_no_networks(),
		DLT => 'LINUX_SLL',
		aliases => [
			'arp net net-10-0-0-0.libpcap.test',
			'arp src or dst net net-10-0-0-0.libpcap.test',
		],
		unopt => '
			(000) ldh      [14]
			(001) jeq      #0x806           jt 2	jf 7
			(002) ld       [30]
			(003) jeq      #0xa000000       jt 6	jf 4
			(004) ld       [40]
			(005) jeq      #0xa000000       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # arp_net_name
	{
		name => 'rarp_net_name',
		skip => skip_no_networks(),
		DLT => 'IP_OVER_FC',
		aliases => [
			'rarp net net-10-0-0-0.libpcap.test',
			'rarp src or dst net net-10-0-0-0.libpcap.test',
		],
		unopt => '
			(000) ldh      [22]
			(001) jeq      #0x8035          jt 2	jf 7
			(002) ld       [38]
			(003) jeq      #0xa000000       jt 6	jf 4
			(004) ld       [48]
			(005) jeq      #0xa000000       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # rarp_net_name
	{
		name => 'arp_net_NAME',
		skip => skip_no_networks_casecmp(),
		DLT => 'SUNATM',
		aliases => [
			'arp net NET-10-0-0-0.LIBPCAP.TEST',
			'arp src or dst net NET-10-0-0-0.LIBPCAP.TEST',
		],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf
			(002) jeq      #0x2             jt 3	jf 10
			(003) ldh      [10]
			(004) jeq      #0x806           jt 5	jf 10
			(005) ld       [26]
			(006) jeq      #0xa000000       jt 9	jf 7
			(007) ld       [36]
			(008) jeq      #0xa000000       jt 9	jf 10
			(009) ret      #262144
			(010) ret      #0
			',
	}, # arp_net_NAME
	{
		name => 'rarp_net_NAME',
		skip => skip_no_networks_casecmp(),
		DLT => 'ARCNET',
		aliases => [
			'rarp net NET-10-0-0-0.LIBPCAP.TEST',
			'rarp src or dst net NET-10-0-0-0.LIBPCAP.TEST',
		],
		unopt => '
			(000) ldb      [2]
			(001) jeq      #0xd6            jt 2	jf 7
			(002) ld       [20]
			(003) jeq      #0xa000000       jt 6	jf 4
			(004) ld       [30]
			(005) jeq      #0xa000000       jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # rarp_net_NAME

	{
		name => 'vlan_eth_nullary',
		DLT => 'EN10MB',
		aliases => ['vlan'],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x8100          jt 4	jf 2
			(002) jeq      #0x88a8          jt 4	jf 3
			(003) jeq      #0x9100          jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # vlan_eth_nullary
	{
		name => 'vlan_eth_unary',
		DLT => 'EN10MB',
		aliases => ['vlan 4095'],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x8100          jt 4	jf 2
			(002) jeq      #0x88a8          jt 4	jf 3
			(003) jeq      #0x9100          jt 4	jf 8
			(004) ldh      [14]
			(005) and      #0xfff
			(006) jeq      #0xfff           jt 7	jf 8
			(007) ret      #262144
			(008) ret      #0
			',
	}, # vlan_eth_unary
	{
		name => 'vlan_and_vlan_eth',
		DLT => 'EN10MB',
		aliases => ['vlan and vlan'],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x8100          jt 4	jf 2
			(002) jeq      #0x88a8          jt 4	jf 3
			(003) jeq      #0x9100          jt 4	jf 9
			(004) ldh      [16]
			(005) jeq      #0x8100          jt 8	jf 6
			(006) jeq      #0x88a8          jt 8	jf 7
			(007) jeq      #0x9100          jt 8	jf 9
			(008) ret      #262144
			(009) ret      #0
			',
	}, # vlan_and_vlan_eth
	{
		name => 'vlan_netanalyzer_nullary',
		DLT => 'NETANALYZER',
		aliases => ['vlan'],
		opt => '
			(000) ldh      [16]
			(001) jeq      #0x8100          jt 4	jf 2
			(002) jeq      #0x88a8          jt 4	jf 3
			(003) jeq      #0x9100          jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # vlan_netanalyzer_nullary
	{
		name => 'vlan_netanalyzer_unary',
		DLT => 'NETANALYZER',
		aliases => ['vlan 10'],
		opt => '
			(000) ldh      [16]
			(001) jeq      #0x8100          jt 4	jf 2
			(002) jeq      #0x88a8          jt 4	jf 3
			(003) jeq      #0x9100          jt 4	jf 8
			(004) ldh      [18]
			(005) and      #0xfff
			(006) jeq      #0xa             jt 7	jf 8
			(007) ret      #262144
			(008) ret      #0
			',
	}, # vlan_netanalyzer_unary
	{
		name => 'vlan_eth_linuxext_nullary',
		skip => skip_os_not ('linux') || skip_config_have_decl ('SKF_AD_VLAN_TAG_PRESENT', 0),
		DLT => 'EN10MB',
		linuxext => 1,
		aliases => ['vlan'],
		opt => '
			(000) ldb      [vlanp]
			(001) jeq      #0x1             jt 6	jf 2
			(002) ldh      [12]
			(003) jeq      #0x8100          jt 6	jf 4
			(004) jeq      #0x88a8          jt 6	jf 5
			(005) jeq      #0x9100          jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # vlan_eth_linuxext_nullary
	{
		name => 'vlan_eth_linuxext_unary',
		skip => skip_os_not ('linux') || skip_config_have_decl ('SKF_AD_VLAN_TAG_PRESENT', 0),
		DLT => 'EN10MB',
		linuxext => 1,
		aliases => ['vlan 10'],
		opt => '
			(000) ldb      [vlanp]
			(001) jeq      #0x1             jt 6	jf 2
			(002) ldh      [12]
			(003) jeq      #0x8100          jt 6	jf 4
			(004) jeq      #0x88a8          jt 6	jf 5
			(005) jeq      #0x9100          jt 6	jf 14
			(006) ldb      [vlanp]
			(007) jeq      #0x1             jt 8	jf 10
			(008) ldh      [vlan_tci]
			(009) ja       11
			(010) ldh      [14]
			(011) and      #0xfff
			(012) jeq      #0xa             jt 13	jf 14
			(013) ret      #262144
			(014) ret      #0
			',
	}, # vlan_eth_linuxext_unary
	{
		name => 'vlan_and_vlan_eth_linuxext',
		skip => skip_os_not ('linux') || skip_config_have_decl ('SKF_AD_VLAN_TAG_PRESENT', 0),
		DLT => 'EN10MB',
		linuxext => 1,
		aliases => ['vlan and vlan'],
		opt => '
			(000) ld       #0x0
			(001) st       M[1]
			(002) ldb      [vlanp]
			(003) jeq      #0x1             jt 10	jf 4
			(004) ld       #0x4
			(005) st       M[1]
			(006) ldh      [12]
			(007) jeq      #0x8100          jt 10	jf 8
			(008) jeq      #0x88a8          jt 10	jf 9
			(009) jeq      #0x9100          jt 10	jf 16
			(010) ldx      M[1]
			(011) ldh      [x + 12]
			(012) jeq      #0x8100          jt 15	jf 13
			(013) jeq      #0x88a8          jt 15	jf 14
			(014) jeq      #0x9100          jt 15	jf 16
			(015) ret      #262144
			(016) ret      #0
			',
	}, # vlan_and_vlan_eth_linuxext

	{
		name => 'mpls_eth_nullary',
		DLT => 'EN10MB',
		aliases => ['mpls'],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x8847          jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # mpls_eth_nullary
	{
		name => 'mpls_eth_unary',
		DLT => 'EN10MB',
		aliases => ['mpls 100'],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x8847          jt 2	jf 6
			(002) ld       [14]
			(003) and      #0xfffff000
			(004) jeq      #0x64000         jt 5	jf 6
			(005) ret      #262144
			(006) ret      #0
			',
	}, # mpls_eth_unary
	{
		name => 'mpls_and_mpls_eth',
		DLT => 'EN10MB',
		aliases => ['mpls and mpls'],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x8847          jt 2	jf 5
			(002) ldb      [16]
			(003) jset     #0x1             jt 5	jf 4
			(004) ret      #262144
			(005) ret      #0
			',
	}, # mpls_and_mpls_eth
	{
		name => 'mpls_ppp_unary',
		DLT => 'PPP',
		aliases => ['mpls 100'],
		opt => '
			(000) ldh      [2]
			(001) jeq      #0x281           jt 2	jf 6
			(002) ld       [4]
			(003) and      #0xfffff000
			(004) jeq      #0x64000         jt 5	jf 6
			(005) ret      #262144
			(006) ret      #0
			',
	}, # mpls_ppp_unary
	{
		name => 'pppoes_mpls_unary',
		DLT => 'EN10MB',
		aliases => ['pppoes and mpls 123'],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x8864          jt 2	jf 8
			(002) ldh      [20]
			(003) jeq      #0x281           jt 4	jf 8
			(004) ld       [22]
			(005) and      #0xfffff000
			(006) jeq      #0x7b000         jt 7	jf 8
			(007) ret      #262144
			(008) ret      #0
			',
	}, # pppoes_mpls_unary

	{
		name => 'vxlan_nullary',
		DLT => 'EN10MB',
		aliases => ['vxlan'],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x800           jt 2	jf 13
			(002) ldb      [23]
			(003) jeq      #0x11            jt 4	jf 21
			(004) ldh      [20]
			(005) jset     #0x1fff          jt 21	jf 6
			(006) ldxb     4*([14]&0xf)
			(007) ldh      [x + 16]
			(008) jeq      #0x12b5          jt 9	jf 21
			(009) ldb      [x + 22]
			(010) jeq      #0x8             jt 11	jf 21
			(011) txa
			(012) jeq      x                jt 20	jf 21
			(013) jeq      #0x86dd          jt 14	jf 21
			(014) ldb      [20]
			(015) jeq      #0x11            jt 16	jf 21
			(016) ldh      [56]
			(017) jeq      #0x12b5          jt 18	jf 21
			(018) ldb      [62]
			(019) jeq      #0x8             jt 20	jf 21
			(020) ret      #262144
			(021) ret      #0
			',
	}, # vxlan_nullary
	{
		name => 'vxlan_unary',
		DLT => 'EN10MB',
		aliases => ['vxlan 12345'],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x800           jt 2	jf 16
			(002) ldb      [23]
			(003) jeq      #0x11            jt 4	jf 27
			(004) ldh      [20]
			(005) jset     #0x1fff          jt 27	jf 6
			(006) ldxb     4*([14]&0xf)
			(007) ldh      [x + 16]
			(008) jeq      #0x12b5          jt 9	jf 27
			(009) ldb      [x + 22]
			(010) jeq      #0x8             jt 11	jf 27
			(011) ld       [x + 26]
			(012) and      #0xffffff00
			(013) jeq      #0x303900        jt 14	jf 27
			(014) txa
			(015) jeq      x                jt 26	jf 27
			(016) jeq      #0x86dd          jt 17	jf 27
			(017) ldb      [20]
			(018) jeq      #0x11            jt 19	jf 27
			(019) ldh      [56]
			(020) jeq      #0x12b5          jt 21	jf 27
			(021) ldb      [62]
			(022) jeq      #0x8             jt 23	jf 27
			(023) ld       [66]
			(024) and      #0xffffff00
			(025) jeq      #0x303900        jt 26	jf 27
			(026) ret      #262144
			(027) ret      #0
			',
	}, # vxlan_unary
	{
		name => 'vxlan_and_vxlan',
		DLT => 'EN10MB',
		aliases => ['vxlan and vxlan'],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x800           jt 2	jf 13
			(002) ldb      [23]
			(003) jeq      #0x11            jt 4	jf 75
			(004) ldh      [20]
			(005) jset     #0x1fff          jt 75	jf 6
			(006) ldxb     4*([14]&0xf)
			(007) ldh      [x + 16]
			(008) jeq      #0x12b5          jt 9	jf 75
			(009) ldb      [x + 22]
			(010) jeq      #0x8             jt 11	jf 75
			(011) txa
			(012) jeq      x                jt 23	jf 75
			(013) jeq      #0x86dd          jt 14	jf 75
			(014) ldb      [20]
			(015) jeq      #0x11            jt 16	jf 75
			(016) ldh      [56]
			(017) jeq      #0x12b5          jt 18	jf 75
			(018) ldb      [62]
			(019) jeq      #0x8             jt 20	jf 75
			(020) ldx      #0x28
			(021) txa
			(022) jeq      #0x28            jt 23	jf 23
			(023) add      #22
			(024) add      #8
			(025) add      #12
			(026) st       M[1]
			(027) add      #2
			(028) tax
			(029) stx      M[2]
			(030) ld       #0x0
			(031) jeq      #0x0             jt 32	jf 32
			(032) ldx      M[1]
			(033) ldh      [x + 0]
			(034) jeq      #0x800           jt 35	jf 62
			(035) ldx      M[2]
			(036) ldb      [x + 9]
			(037) jeq      #0x11            jt 38	jf 75
			(038) ldh      [x + 6]
			(039) jset     #0x1fff          jt 75	jf 40
			(040) ldb      [x + 0]
			(041) and      #0xf
			(042) lsh      #2
			(043) add      x
			(044) tax
			(045) ldh      [x + 2]
			(046) jeq      #0x12b5          jt 47	jf 75
			(047) ldx      M[2]
			(048) ldb      [x + 0]
			(049) and      #0xf
			(050) lsh      #2
			(051) add      x
			(052) tax
			(053) ldb      [x + 8]
			(054) jeq      #0x8             jt 55	jf 75
			(055) ldx      M[2]
			(056) ldb      [x + 0]
			(057) and      #0xf
			(058) lsh      #2
			(059) add      x
			(060) tax
			(061) jeq      x                jt 74	jf 75
			(062) jeq      #0x86dd          jt 63	jf 75
			(063) ldx      M[2]
			(064) ldb      [x + 6]
			(065) jeq      #0x11            jt 66	jf 75
			(066) ldh      [x + 42]
			(067) jeq      #0x12b5          jt 68	jf 75
			(068) ldb      [x + 48]
			(069) jeq      #0x8             jt 70	jf 75
			(070) ld       #0x28
			(071) add      x
			(072) tax
			(073) jeq      x                jt 74	jf 75
			(074) ret      #262144
			(075) ret      #0
			',
	}, # vxlan_and_vxlan
	{
		name => 'geneve_nullary',
		DLT => 'EN10MB',
		aliases => ['geneve'],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x800           jt 2	jf 16
			(002) ldb      [23]
			(003) jeq      #0x11            jt 4	jf 16
			(004) ldh      [20]
			(005) jset     #0x1fff          jt 16	jf 6
			(006) ldxb     4*([14]&0xf)
			(007) ldh      [x + 16]
			(008) jeq      #0x17c1          jt 9	jf 16
			(009) ldxb     4*([14]&0xf)
			(010) ldb      [x + 22]
			(011) and      #0xc0
			(012) jeq      #0x0             jt 13	jf 16
			(013) ldxb     4*([14]&0xf)
			(014) txa
			(015) jeq      x                jt 28	jf 16
			(016) ldh      [12]
			(017) jeq      #0x86dd          jt 18	jf 50
			(018) ldb      [20]
			(019) jeq      #0x11            jt 20	jf 50
			(020) ldh      [56]
			(021) jeq      #0x17c1          jt 22	jf 50
			(022) ldb      [62]
			(023) and      #0xc0
			(024) jeq      #0x0             jt 25	jf 50
			(025) ld       #0x28
			(026) tax
			(027) jeq      x                jt 28	jf 50
			(028) add      #22
			(029) tax
			(030) add      #2
			(031) st       M[0]
			(032) ldb      [x + 0]
			(033) and      #0x3f
			(034) mul      #4
			(035) add      #8
			(036) add      x
			(037) st       M[1]
			(038) ldh      [x + 2]
			(039) ldx      M[1]
			(040) jeq      #0x6558          jt 41	jf 46
			(041) txa
			(042) add      #12
			(043) st       M[0]
			(044) add      #2
			(045) tax
			(046) stx      M[2]
			(047) ld       #0x0
			(048) jeq      #0x0             jt 49	jf 50
			(049) ret      #262144
			(050) ret      #0
			',
	}, # geneve_nullary
	{
		name => 'geneve_unary',
		DLT => 'EN10MB',
		aliases => ['geneve 12345'],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x800           jt 2	jf 20
			(002) ldb      [23]
			(003) jeq      #0x11            jt 4	jf 20
			(004) ldh      [20]
			(005) jset     #0x1fff          jt 20	jf 6
			(006) ldxb     4*([14]&0xf)
			(007) ldh      [x + 16]
			(008) jeq      #0x17c1          jt 9	jf 20
			(009) ldxb     4*([14]&0xf)
			(010) ldb      [x + 22]
			(011) and      #0xc0
			(012) jeq      #0x0             jt 13	jf 20
			(013) ldxb     4*([14]&0xf)
			(014) ld       [x + 26]
			(015) and      #0xffffff00
			(016) jeq      #0x303900        jt 17	jf 20
			(017) ldxb     4*([14]&0xf)
			(018) txa
			(019) jeq      x                jt 35	jf 20
			(020) ldh      [12]
			(021) jeq      #0x86dd          jt 22	jf 57
			(022) ldb      [20]
			(023) jeq      #0x11            jt 24	jf 57
			(024) ldh      [56]
			(025) jeq      #0x17c1          jt 26	jf 57
			(026) ldb      [62]
			(027) and      #0xc0
			(028) jeq      #0x0             jt 29	jf 57
			(029) ld       [66]
			(030) and      #0xffffff00
			(031) jeq      #0x303900        jt 32	jf 57
			(032) ld       #0x28
			(033) tax
			(034) jeq      x                jt 35	jf 57
			(035) add      #22
			(036) tax
			(037) add      #2
			(038) st       M[0]
			(039) ldb      [x + 0]
			(040) and      #0x3f
			(041) mul      #4
			(042) add      #8
			(043) add      x
			(044) st       M[1]
			(045) ldh      [x + 2]
			(046) ldx      M[1]
			(047) jeq      #0x6558          jt 48	jf 53
			(048) txa
			(049) add      #12
			(050) st       M[0]
			(051) add      #2
			(052) tax
			(053) stx      M[2]
			(054) ld       #0x0
			(055) jeq      #0x0             jt 56	jf 57
			(056) ret      #262144
			(057) ret      #0
			',
	}, # geneve_unary
	{
		name => 'geneve_and_geneve',
		DLT => 'EN10MB',
		aliases => ['geneve and geneve'],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x800           jt 2	jf 16
			(002) ldb      [23]
			(003) jeq      #0x11            jt 4	jf 16
			(004) ldh      [20]
			(005) jset     #0x1fff          jt 16	jf 6
			(006) ldxb     4*([14]&0xf)
			(007) ldh      [x + 16]
			(008) jeq      #0x17c1          jt 9	jf 16
			(009) ldxb     4*([14]&0xf)
			(010) ldb      [x + 22]
			(011) and      #0xc0
			(012) jeq      #0x0             jt 13	jf 16
			(013) ldxb     4*([14]&0xf)
			(014) txa
			(015) jeq      x                jt 28	jf 16
			(016) ldh      [12]
			(017) jeq      #0x86dd          jt 18	jf 123
			(018) ldb      [20]
			(019) jeq      #0x11            jt 20	jf 123
			(020) ldh      [56]
			(021) jeq      #0x17c1          jt 22	jf 123
			(022) ldb      [62]
			(023) and      #0xc0
			(024) jeq      #0x0             jt 25	jf 123
			(025) ld       #0x28
			(026) tax
			(027) jeq      x                jt 28	jf 123
			(028) add      #22
			(029) tax
			(030) add      #2
			(031) st       M[0]
			(032) ldb      [x + 0]
			(033) and      #0x3f
			(034) mul      #4
			(035) add      #8
			(036) add      x
			(037) st       M[1]
			(038) ldh      [x + 2]
			(039) ldx      M[1]
			(040) jeq      #0x6558          jt 41	jf 46
			(041) txa
			(042) add      #12
			(043) st       M[0]
			(044) add      #2
			(045) tax
			(046) stx      M[2]
			(047) ld       #0x0
			(048) jeq      #0x0             jt 49	jf 123
			(049) ldx      M[0]
			(050) ldh      [x + 0]
			(051) jeq      #0x800           jt 52	jf 83
			(052) ldx      M[2]
			(053) ldb      [x + 9]
			(054) jeq      #0x11            jt 55	jf 83
			(055) ldx      M[2]
			(056) ldh      [x + 6]
			(057) jset     #0x1fff          jt 83	jf 58
			(058) ldx      M[2]
			(059) ldb      [x + 0]
			(060) and      #0xf
			(061) lsh      #2
			(062) add      x
			(063) tax
			(064) ldh      [x + 2]
			(065) jeq      #0x17c1          jt 66	jf 83
			(066) ldx      M[2]
			(067) ldb      [x + 0]
			(068) and      #0xf
			(069) lsh      #2
			(070) add      x
			(071) tax
			(072) ldb      [x + 8]
			(073) and      #0xc0
			(074) jeq      #0x0             jt 75	jf 83
			(075) ldx      M[2]
			(076) ldb      [x + 0]
			(077) and      #0xf
			(078) lsh      #2
			(079) add      x
			(080) tax
			(081) txa
			(082) jeq      x                jt 101	jf 83
			(083) ldx      M[0]
			(084) ldh      [x + 0]
			(085) jeq      #0x86dd          jt 86	jf 123
			(086) ldx      M[2]
			(087) ldb      [x + 6]
			(088) jeq      #0x11            jt 89	jf 123
			(089) ldx      M[2]
			(090) ldh      [x + 42]
			(091) jeq      #0x17c1          jt 92	jf 123
			(092) ldx      M[2]
			(093) ldb      [x + 48]
			(094) and      #0xc0
			(095) jeq      #0x0             jt 96	jf 123
			(096) ldx      M[2]
			(097) ld       #0x28
			(098) add      x
			(099) tax
			(100) jeq      x                jt 101	jf 123
			(101) add      #8
			(102) tax
			(103) add      #2
			(104) st       M[3]
			(105) ldb      [x + 0]
			(106) and      #0x3f
			(107) mul      #4
			(108) add      #8
			(109) add      x
			(110) st       M[4]
			(111) ldh      [x + 2]
			(112) ldx      M[4]
			(113) jeq      #0x6558          jt 114	jf 119
			(114) txa
			(115) add      #12
			(116) st       M[3]
			(117) add      #2
			(118) tax
			(119) stx      M[5]
			(120) ld       #0x0
			(121) jeq      #0x0             jt 122	jf 123
			(122) ret      #262144
			(123) ret      #0
			',
	}, # geneve_and_geneve

	{
		name => 'wlan_host',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan host 12:34:56:78:9a:bc',
			'wlan src or dst host 12:34:56:78:9a:bc',
			'wlan src or dst 12:34:56:78:9a:bc',
		],
		opt => '
			(000) ldb      [0]
			(001) jset     #0x4             jt 33	jf 2
			(002) jset     #0x8             jt 3	jf 24
			(003) ldb      [1]
			(004) jset     #0x2             jt 5	jf 14
			(005) jset     #0x1             jt 6	jf 10
			(006) ld       [26]
			(007) jeq      #0x56789abc      jt 8	jf 20
			(008) ldh      [24]
			(009) jeq      #0x1234          jt 32	jf 20
			(010) ld       [18]
			(011) jeq      #0x56789abc      jt 12	jf 28
			(012) ldh      [16]
			(013) jeq      #0x1234          jt 32	jf 28
			(014) ld       [12]
			(015) jeq      #0x56789abc      jt 16	jf 18
			(016) ldh      [10]
			(017) jeq      #0x1234          jt 32	jf 18
			(018) ldb      [1]
			(019) jset     #0x1             jt 20	jf 28
			(020) ld       [18]
			(021) jeq      #0x56789abc      jt 22	jf 33
			(022) ldh      [16]
			(023) jeq      #0x1234          jt 32	jf 33
			(024) ld       [12]
			(025) jeq      #0x56789abc      jt 26	jf 28
			(026) ldh      [10]
			(027) jeq      #0x1234          jt 32	jf 28
			(028) ld       [6]
			(029) jeq      #0x56789abc      jt 30	jf 33
			(030) ldh      [4]
			(031) jeq      #0x1234          jt 32	jf 33
			(032) ret      #262144
			(033) ret      #0
			',
	}, # wlan_host
	{
		name => 'wlan_src_host',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan src host 12:34:56:78:9a:bc',
			'wlan src 12:34:56:78:9a:bc',
		],
		opt => '
			(000) ldb      [0]
			(001) jset     #0x4             jt 19	jf 2
			(002) jset     #0x8             jt 3	jf 14
			(003) ldb      [1]
			(004) jset     #0x2             jt 5	jf 14
			(005) jset     #0x1             jt 6	jf 10
			(006) ld       [26]
			(007) jeq      #0x56789abc      jt 8	jf 19
			(008) ldh      [24]
			(009) jeq      #0x1234          jt 18	jf 19
			(010) ld       [18]
			(011) jeq      #0x56789abc      jt 12	jf 19
			(012) ldh      [16]
			(013) jeq      #0x1234          jt 18	jf 19
			(014) ld       [12]
			(015) jeq      #0x56789abc      jt 16	jf 19
			(016) ldh      [10]
			(017) jeq      #0x1234          jt 18	jf 19
			(018) ret      #262144
			(019) ret      #0
			',
	}, # wlan_src_host
	{
		name => 'wlan_dst_host',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan dst host 12:34:56:78:9a:bc',
			'wlan dst 12:34:56:78:9a:bc',
		],
		opt => '
			(000) ldb      [0]
			(001) jset     #0x4             jt 14	jf 2
			(002) jset     #0x8             jt 3	jf 9
			(003) ldb      [1]
			(004) jset     #0x1             jt 5	jf 9
			(005) ld       [18]
			(006) jeq      #0x56789abc      jt 7	jf 14
			(007) ldh      [16]
			(008) jeq      #0x1234          jt 13	jf 14
			(009) ld       [6]
			(010) jeq      #0x56789abc      jt 11	jf 14
			(011) ldh      [4]
			(012) jeq      #0x1234          jt 13	jf 14
			(013) ret      #262144
			(014) ret      #0
			',
	}, # wlan_dst_host
	{
		name => 'wlan_ra',
		DLT => 'IEEE802_11',
		aliases => ['wlan ra 12:34:56:78:9a:bc'],
		opt => '
			(000) ldb      [0]
			(001) jset     #0x8             jt 2	jf 7
			(002) ld       [6]
			(003) jeq      #0x56789abc      jt 4	jf 7
			(004) ldh      [4]
			(005) jeq      #0x1234          jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # wlan_ra
	{
		name => 'wlan_ta',
		DLT => 'IEEE802_11',
		aliases => ['wlan ta 12:34:56:78:9a:bc'],
		opt => '
			(000) ldb      [0]
			(001) jset     #0x8             jt 2	jf 15
			(002) and      #0xc
			(003) jeq      #0x4             jt 4	jf 10
			(004) ldb      [0]
			(005) and      #0xf0
			(006) jeq      #0xc0            jt 15	jf 7
			(007) ldb      [0]
			(008) and      #0xf0
			(009) jeq      #0xd0            jt 15	jf 10
			(010) ld       [12]
			(011) jeq      #0x56789abc      jt 12	jf 15
			(012) ldh      [10]
			(013) jeq      #0x1234          jt 14	jf 15
			(014) ret      #262144
			(015) ret      #0
			',
	}, # wlan_ta
	{
		name => 'wlan_addr1',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan addr1 12:34:56:78:9a:bc',
			'wlan address1 12:34:56:78:9a:bc',
		],
		opt => '
			(000) ld       [6]
			(001) jeq      #0x56789abc      jt 2	jf 5
			(002) ldh      [4]
			(003) jeq      #0x1234          jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # wlan_addr1
	{
		name => 'wlan_addr2',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan addr2 12:34:56:78:9a:bc',
			'wlan address2 12:34:56:78:9a:bc',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xc
			(002) jeq      #0x4             jt 3	jf 9
			(003) ldb      [0]
			(004) and      #0xf0
			(005) jeq      #0xc0            jt 14	jf 6
			(006) ldb      [0]
			(007) and      #0xf0
			(008) jeq      #0xd0            jt 14	jf 9
			(009) ld       [12]
			(010) jeq      #0x56789abc      jt 11	jf 14
			(011) ldh      [10]
			(012) jeq      #0x1234          jt 13	jf 14
			(013) ret      #262144
			(014) ret      #0
			',
	}, # wlan_addr2
	{
		name => 'wlan_addr3',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan addr3 12:34:56:78:9a:bc',
			'wlan address3 12:34:56:78:9a:bc',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xc
			(002) jeq      #0x4             jt 8	jf 3
			(003) ld       [18]
			(004) jeq      #0x56789abc      jt 5	jf 8
			(005) ldh      [16]
			(006) jeq      #0x1234          jt 7	jf 8
			(007) ret      #262144
			(008) ret      #0
			',
	}, # wlan_addr3
	{
		name => 'wlan_addr4',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan addr4 12:34:56:78:9a:bc',
			'wlan address4 12:34:56:78:9a:bc',
		],
		opt => '
			(000) ldb      [1]
			(001) and      #0x3
			(002) jeq      #0x3             jt 3	jf 8
			(003) ld       [26]
			(004) jeq      #0x56789abc      jt 5	jf 8
			(005) ldh      [24]
			(006) jeq      #0x1234          jt 7	jf 8
			(007) ret      #262144
			(008) ret      #0
			',
	}, # wlan_addr4
	{
		name => 'wlan_type_mgt',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan type mgt',
			'type mgt',
			'wlan type 0',
			'type 0',
			'wlan type management',
			'type management',
		],
		opt => '
			(000) ldb      [0]
			(001) jset     #0xc             jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # type_mgt
	{
		name => 'wlan_subtype_assoc_req',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype assoc-req',
			'subtype assoc-req',
			'wlan type mgt subtype assoc-req',
			'type mgt subtype assoc-req',
			'wlan type 0 subtype assoc-req',
			'type 0 subtype assoc-req',
			'wlan type mgt subtype 0x00',
			'type mgt subtype 0x00',
			'wlan type 0 subtype 0x00',
			'type 0 subtype 0x00',
			'wlan subtype assocreq',
			'subtype assocreq',
			'wlan type mgt subtype assocreq',
			'type mgt subtype assocreq',
			'wlan type 0 subtype assocreq',
			'type 0 subtype assocreq',
			'wlan type management subtype assoc-req',
			'type management subtype assoc-req',
			'wlan type management subtype 0x00',
			'type management subtype 0x00',
			'wlan type management subtype assocreq',
			'type management subtype assocreq',
		],
		opt => '
			(000) ldb      [0]
			(001) jset     #0xfc            jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # wlan_type_mgt_subtype_assoc_req
	{
		name => 'wlan_subtype_assoc_resp',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype assoc-resp',
			'subtype assoc-resp',
			'wlan type mgt subtype assoc-resp',
			'type mgt subtype assoc-resp',
			'wlan type 0 subtype assoc-resp',
			'type 0 subtype assoc-resp',
			'wlan type mgt subtype 0x10',
			'type mgt subtype 0x10',
			'wlan type 0 subtype 0x10',
			'type 0 subtype 0x10',
			'wlan subtype assocresp',
			'subtype assocresp',
			'wlan type mgt subtype assocresp',
			'type mgt subtype assocresp',
			'wlan type 0 subtype assocresp',
			'type 0 subtype assocresp',
			'wlan type management subtype assoc-resp',
			'type management subtype assoc-resp',
			'wlan type management subtype 0x10',
			'type management subtype 0x10',
			'wlan type management subtype assocresp',
			'type management subtype assocresp',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x10            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_assoc_resp
	{
		name => 'wlan_subtype_reassoc_req',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype reassoc-req',
			'subtype reassoc-req',
			'wlan type mgt subtype reassoc-req',
			'type mgt subtype reassoc-req',
			'wlan type 0 subtype reassoc-req',
			'type 0 subtype reassoc-req',
			'wlan type mgt subtype 0x20',
			'type mgt subtype 0x20',
			'wlan type 0 subtype 0x20',
			'type 0 subtype 0x20',
			'wlan subtype reassocreq',
			'subtype reassocreq',
			'wlan type mgt subtype reassocreq',
			'type mgt subtype reassocreq',
			'wlan type 0 subtype reassocreq',
			'type 0 subtype reassocreq',
			'wlan type management subtype reassoc-req',
			'type management subtype reassoc-req',
			'wlan type management subtype 0x20',
			'type management subtype 0x20',
			'wlan type management subtype reassocreq',
			'type management subtype reassocreq',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x20            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_reassoc_req
	{
		name => 'wlan_subtype_reassoc_resp',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype reassoc-resp',
			'subtype reassoc-resp',
			'wlan type mgt subtype reassoc-resp',
			'type mgt subtype reassoc-resp',
			'wlan type 0 subtype reassoc-resp',
			'type 0 subtype reassoc-resp',
			'wlan type mgt subtype 0x30',
			'type mgt subtype 0x30',
			'wlan type 0 subtype 0x30',
			'type 0 subtype 0x30',
			'wlan subtype reassocresp',
			'subtype reassocresp',
			'wlan type mgt subtype reassocresp',
			'type mgt subtype reassocresp',
			'wlan type 0 subtype reassocresp',
			'type 0 subtype reassocresp',
			'wlan type management subtype reassoc-resp',
			'type management subtype reassoc-resp',
			'wlan type management subtype 0x30',
			'type management subtype 0x30',
			'wlan type management subtype reassocresp',
			'type management subtype reassocresp',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x30            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_mgt_subtype_reassoc_resp
	{
		name => 'wlan_subtype_probe_req',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype probe-req',
			'subtype probe-req',
			'wlan type mgt subtype probe-req',
			'type mgt subtype probe-req',
			'wlan type 0 subtype probe-req',
			'type 0 subtype probe-req',
			'wlan type mgt subtype 0x40',
			'type mgt subtype 0x40',
			'wlan type 0 subtype 0x40',
			'type 0 subtype 0x40',
			'wlan subtype probereq',
			'subtype probereq',
			'wlan type mgt subtype probereq',
			'type mgt subtype probereq',
			'wlan type 0 subtype probereq',
			'type 0 subtype probereq',
			'wlan type management subtype probe-req',
			'type management subtype probe-req',
			'wlan type management subtype 0x40',
			'type management subtype 0x40',
			'wlan type management subtype probereq',
			'type management subtype probereq',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x40            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_probe_req
	{
		name => 'wlan_subtype_probe_resp',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype probe-resp',
			'subtype probe-resp',
			'wlan type mgt subtype probe-resp',
			'type mgt subtype probe-resp',
			'wlan type 0 subtype probe-resp',
			'type 0 subtype probe-resp',
			'wlan type mgt subtype 0x50',
			'type mgt subtype 0x50',
			'wlan type 0 subtype 0x50',
			'type 0 subtype 0x50',
			'wlan subtype proberesp',
			'subtype proberesp',
			'wlan type mgt subtype proberesp',
			'type mgt subtype proberesp',
			'wlan type 0 subtype proberesp',
			'type 0 subtype proberesp',
			'wlan type management subtype probe-resp',
			'type management subtype probe-resp',
			'wlan type management subtype 0x50',
			'type management subtype 0x50',
			'wlan type management subtype proberesp',
			'type management subtype proberesp',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x50            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_probe_resp
	{
		name => 'wlan_subtype_beacon',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype beacon',
			'subtype beacon',
			'wlan type mgt subtype beacon',
			'type mgt subtype beacon',
			'wlan type 0 subtype beacon',
			'type 0 subtype beacon',
			'wlan type mgt subtype 0x80',
			'type mgt subtype 0x80',
			'wlan type 0 subtype 0x80',
			'type 0 subtype 0x80',
			'wlan type management subtype beacon',
			'type management subtype beacon',
			'wlan type management subtype 0x80',
			'type management subtype 0x80',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x80            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_beacon
	{
		name => 'wlan_subtype_atim',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype atim',
			'subtype atim',
			'wlan type mgt subtype atim',
			'type mgt subtype atim',
			'wlan type 0 subtype atim',
			'type 0 subtype atim',
			'wlan type mgt subtype 0x90',
			'type mgt subtype 0x90',
			'wlan type 0 subtype 0x90',
			'type 0 subtype 0x90',
			'wlan type management subtype atim',
			'type management subtype atim',
			'wlan type management subtype 0x90',
			'type management subtype 0x90',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x90            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_atim
	{
		name => 'wlan_subtype_disassoc',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype disassoc',
			'subtype disassoc',
			'wlan type mgt subtype disassoc',
			'type mgt subtype disassoc',
			'wlan type 0 subtype disassoc',
			'type 0 subtype disassoc',
			'wlan type mgt subtype 0xa0',
			'type mgt subtype 0xa0',
			'wlan type 0 subtype 0xa0',
			'type 0 subtype 0xa0',
			'wlan subtype disassociation',
			'subtype disassociation',
			'wlan type mgt subtype disassociation',
			'type mgt subtype disassociation',
			'wlan type 0 subtype disassociation',
			'type 0 subtype disassociation',
			'wlan type management subtype disassoc',
			'type management subtype disassoc',
			'wlan type management subtype 0xa0',
			'type management subtype 0xa0',
			'wlan type management subtype disassociation',
			'type management subtype disassociation',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0xa0            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_disassoc
	{
		name => 'wlan_subtype_auth',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype auth',
			'subtype auth',
			'wlan type mgt subtype auth',
			'type mgt subtype auth',
			'wlan type 0 subtype auth',
			'type 0 subtype auth',
			'wlan type mgt subtype 0xb0',
			'type mgt subtype 0xb0',
			'wlan type 0 subtype 0xb0',
			'type 0 subtype 0xb0',
			'wlan subtype authentication',
			'subtype authentication',
			'wlan type mgt subtype authentication',
			'type mgt subtype authentication',
			'wlan type 0 subtype authentication',
			'type 0 subtype authentication',
			'wlan type management subtype auth',
			'type management subtype auth',
			'wlan type management subtype 0xb0',
			'type management subtype 0xb0',
			'wlan type management subtype authentication',
			'type management subtype authentication',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0xb0            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_auth
	{
		name => 'wlan_subtype_deauth',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype deauth',
			'subtype deauth',
			'wlan type mgt subtype deauth',
			'type mgt subtype deauth',
			'wlan type 0 subtype deauth',
			'type 0 subtype deauth',
			'wlan type mgt subtype 0xc0',
			'type mgt subtype 0xc0',
			'wlan type 0 subtype 0xc0',
			'type 0 subtype 0xc0',
			'wlan subtype deauthentication',
			'subtype deauthentication',
			'wlan type mgt subtype deauthentication',
			'type mgt subtype deauthentication',
			'wlan type 0 subtype deauthentication',
			'type 0 subtype deauthentication',
			'wlan type management subtype deauth',
			'type management subtype deauth',
			'wlan type management subtype 0xc0',
			'type management subtype 0xc0',
			'wlan type management subtype deauthentication',
			'type management subtype deauthentication',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0xc0            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_deauth
	{
		name => 'wlan_type_ctl',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan type ctl',
			'type ctl',
			'wlan type 4',
			'type 4',
			'wlan type control',
			'type control',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xc
			(002) jeq      #0x4             jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # type_ctl
	{
		name => 'wlan_subtype_bar',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype bar',
			'subtype bar',
			'wlan type ctl subtype bar',
			'type ctl subtype bar',
			'wlan type 4 subtype bar',
			'type 4 subtype bar',
			'wlan type ctl subtype 0x80',
			'type ctl subtype 0x80',
			'wlan type 4 subtype 0x80',
			'type 4 subtype 0x80',
			'wlan type control subtype bar',
			'type control subtype bar',
			'wlan type control subtype 0x80',
			'type control subtype 0x80',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x84            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_bar
	{
		name => 'wlan_subtype_ba',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype ba',
			'subtype ba',
			'wlan type ctl subtype ba',
			'type ctl subtype ba',
			'wlan type 4 subtype ba',
			'type 4 subtype ba',
			'wlan type ctl subtype 0x90',
			'type ctl subtype 0x90',
			'wlan type 4 subtype 0x90',
			'type 4 subtype 0x90',
			'wlan type control subtype ba',
			'type control subtype ba',
			'wlan type control subtype 0x90',
			'type control subtype 0x90',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x94            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_ba
	{
		name => 'wlan_subtype_ps_poll',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype ps-poll',
			'subtype ps-poll',
			'wlan type ctl subtype ps-poll',
			'type ctl subtype ps-poll',
			'wlan type 4 subtype ps-poll',
			'type 4 subtype ps-poll',
			'wlan type ctl subtype 0xa0',
			'type ctl subtype 0xa0',
			'wlan type 4 subtype 0xa0',
			'type 4 subtype 0xa0',
			'wlan type control subtype ps-poll',
			'type control subtype ps-poll',
			'wlan type control subtype 0xa0',
			'type control subtype 0xa0',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0xa4            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_ps_poll
	{
		name => 'wlan_subtype_rts',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype rts',
			'subtype rts',
			'wlan type ctl subtype rts',
			'type ctl subtype rts',
			'wlan type 4 subtype rts',
			'type 4 subtype rts',
			'wlan type ctl subtype 0xb0',
			'type ctl subtype 0xb0',
			'wlan type 4 subtype 0xb0',
			'type 4 subtype 0xb0',
			'wlan type control subtype rts',
			'type control subtype rts',
			'wlan type control subtype 0xb0',
			'type control subtype 0xb0',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0xb4            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_rts
	{
		name => 'wlan_subtype_cts',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype cts',
			'subtype cts',
			'wlan type ctl subtype cts',
			'type ctl subtype cts',
			'wlan type 4 subtype cts',
			'type 4 subtype cts',
			'wlan type ctl subtype 0xc0',
			'type ctl subtype 0xc0',
			'wlan type 4 subtype 0xc0',
			'type 4 subtype 0xc0',
			'wlan type control subtype cts',
			'type control subtype cts',
			'wlan type control subtype 0xc0',
			'type control subtype 0xc0',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0xc4            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_cts
	{
		name => 'wlan_subtype_ack',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype ack',
			'subtype ack',
			'wlan type ctl subtype ack',
			'type ctl subtype ack',
			'wlan type 4 subtype ack',
			'type 4 subtype ack',
			'wlan type ctl subtype 0xd0',
			'type ctl subtype 0xd0',
			'wlan type 4 subtype 0xd0',
			'type 4 subtype 0xd0',
			'wlan type control subtype ack',
			'type control subtype ack',
			'wlan type control subtype 0xd0',
			'type control subtype 0xd0',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0xd4            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_ack
	{
		name => 'wlan_subtype_cf_end',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype cf-end',
			'subtype cf-end',
			'wlan type ctl subtype cf-end',
			'type ctl subtype cf-end',
			'wlan type 4 subtype cf-end',
			'type 4 subtype cf-end',
			'wlan type ctl subtype 0xe0',
			'type ctl subtype 0xe0',
			'wlan type 4 subtype 0xe0',
			'type 4 subtype 0xe0',
			'wlan type control subtype cf-end',
			'type control subtype cf-end',
			'wlan type control subtype 0xe0',
			'type control subtype 0xe0',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0xe4            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_cf_end
	{
		name => 'wlan_subtype_cf_end_ack',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype cf-end-ack',
			'subtype cf-end-ack',
			'wlan type ctl subtype cf-end-ack',
			'type ctl subtype cf-end-ack',
			'wlan type 4 subtype cf-end-ack',
			'type 4 subtype cf-end-ack',
			'wlan type ctl subtype 0xf0',
			'type ctl subtype 0xf0',
			'wlan type 4 subtype 0xf0',
			'type 4 subtype 0xf0',
			'wlan type control subtype cf-end-ack',
			'type control subtype cf-end-ack',
			'wlan type control subtype 0xf0',
			'type control subtype 0xf0',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0xf4            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_cf_end_ack
	{
		name => 'wlan_type_data',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan type data',
			'type data',
			'wlan type 8',
			'type 8',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xc
			(002) jeq      #0x8             jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_type_data
	{
		name => 'wlan_subtype_data',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype data',
			'subtype data',
			'wlan type data subtype data',
			'type data subtype data',
			'wlan type 8 subtype data',
			'type 8 subtype data',
			'wlan type data subtype 0x00',
			'type data subtype 0x00',
			'wlan type 8 subtype 0x00',
			'type 8 subtype 0x00',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x8             jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_data
	{
		name => 'wlan_subtype_data_cf_ack',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype data-cf-ack',
			'subtype data-cf-ack',
			'wlan type data subtype data-cf-ack',
			'type data subtype data-cf-ack',
			'wlan type 8 subtype data-cf-ack',
			'type 8 subtype data-cf-ack',
			'wlan type data subtype 0x10',
			'type data subtype 0x10',
			'wlan type 8 subtype 0x10',
			'type 8 subtype 0x10',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x18            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_data_cf_ack
	{
		name => 'wlan_subtype_data_cf_poll',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype data-cf-poll',
			'subtype data-cf-poll',
			'wlan type data subtype data-cf-poll',
			'type data subtype data-cf-poll',
			'wlan type 8 subtype data-cf-poll',
			'type 8 subtype data-cf-poll',
			'wlan type data subtype 0x20',
			'type data subtype 0x20',
			'wlan type 8 subtype 0x20',
			'type 8 subtype 0x20',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x28            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_data_cf_poll
	{
		name => 'wlan_subtype_data_cf_ack_poll',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype data-cf-ack-poll',
			'subtype data-cf-ack-poll',
			'wlan type data subtype data-cf-ack-poll',
			'type data subtype data-cf-ack-poll',
			'wlan type 8 subtype data-cf-ack-poll',
			'type 8 subtype data-cf-ack-poll',
			'wlan type data subtype 0x30',
			'type data subtype 0x30',
			'wlan type 8 subtype 0x30',
			'type 8 subtype 0x30',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x38            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_data_cf_ack_poll
	{
		name => 'wlan_subtype_null',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype null',
			'subtype null',
			'wlan type data subtype null',
			'type data subtype null',
			'wlan type 8 subtype null',
			'type 8 subtype null',
			'wlan type data subtype 0x40',
			'type data subtype 0x40',
			'wlan type 8 subtype 0x40',
			'type 8 subtype 0x40',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x48            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_null
	{
		name => 'wlan_subtype_cf_ack',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype cf-ack',
			'subtype cf-ack',
			'wlan type data subtype cf-ack',
			'type data subtype cf-ack',
			'wlan type 8 subtype cf-ack',
			'type 8 subtype cf-ack',
			'wlan type data subtype 0x50',
			'type data subtype 0x50',
			'wlan type 8 subtype 0x50',
			'type 8 subtype 0x50',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x58            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_cf_ack
	{
		name => 'wlan_subtype_cf_poll',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype cf-poll',
			'subtype cf-poll',
			'wlan type data subtype cf-poll',
			'type data subtype cf-poll',
			'wlan type 8 subtype cf-poll',
			'type 8 subtype cf-poll',
			'wlan type data subtype 0x60',
			'type data subtype 0x60',
			'wlan type 8 subtype 0x60',
			'type 8 subtype 0x60',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x68            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_cf_poll
	{
		name => 'wlan_subtype_cf_ack_poll',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype cf-ack-poll',
			'subtype cf-ack-poll',
			'wlan type data subtype cf-ack-poll',
			'type data subtype cf-ack-poll',
			'wlan type 8 subtype cf-ack-poll',
			'type 8 subtype cf-ack-poll',
			'wlan type data subtype 0x70',
			'type data subtype 0x70',
			'wlan type 8 subtype 0x70',
			'type 8 subtype 0x70',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x78            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_cf_ack_poll
	{
		name => 'wlan_subtype_qos_data',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype qos-data',
			'subtype qos-data',
			'wlan type data subtype qos-data',
			'type data subtype qos-data',
			'wlan type 8 subtype qos-data',
			'type 8 subtype qos-data',
			'wlan type data subtype 0x80',
			'type data subtype 0x80',
			'wlan type 8 subtype 0x80',
			'type 8 subtype 0x80',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x88            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_qos_data
	{
		name => 'wlan_subtype_qos_data_cf_ack',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype qos-data-cf-ack',
			'subtype qos-data-cf-ack',
			'wlan type data subtype qos-data-cf-ack',
			'type data subtype qos-data-cf-ack',
			'wlan type 8 subtype qos-data-cf-ack',
			'type 8 subtype qos-data-cf-ack',
			'wlan type data subtype 0x90',
			'type data subtype 0x90',
			'wlan type 8 subtype 0x90',
			'type 8 subtype 0x90',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0x98            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_qos_data_cf_ack
	{
		name => 'wlan_subtype_qos_data_cf_poll',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype qos-data-cf-poll',
			'subtype qos-data-cf-poll',
			'wlan type data subtype qos-data-cf-poll',
			'type data subtype qos-data-cf-poll',
			'wlan type 8 subtype qos-data-cf-poll',
			'type 8 subtype qos-data-cf-poll',
			'wlan type data subtype 0xa0',
			'type data subtype 0xa0',
			'wlan type 8 subtype 0xa0',
			'type 8 subtype 0xa0',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0xa8            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_qos_data_cf_poll
	{
		name => 'wlan_subtype_qos_data_cf_ack_poll',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype qos-data-cf-ack-poll',
			'subtype qos-data-cf-ack-poll',
			'wlan type data subtype qos-data-cf-ack-poll',
			'type data subtype qos-data-cf-ack-poll',
			'wlan type 8 subtype qos-data-cf-ack-poll',
			'type 8 subtype qos-data-cf-ack-poll',
			'wlan type data subtype 0xb0',
			'type data subtype 0xb0',
			'wlan type 8 subtype 0xb0',
			'type 8 subtype 0xb0',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0xb8            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_qos_data_cf_ack_poll
	{
		name => 'wlan_subtype_qos',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype qos',
			'subtype qos',
			'wlan type data subtype qos',
			'type data subtype qos',
			'wlan type 8 subtype qos',
			'type 8 subtype qos',
			'wlan type data subtype 0xc0',
			'type data subtype 0xc0',
			'wlan type 8 subtype 0xc0',
			'type 8 subtype 0xc0',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0xc8            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_qos
	{
		name => 'wlan_subtype_qos_cf_poll',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype qos-cf-poll',
			'subtype qos-cf-poll',
			'wlan type data subtype qos-cf-poll',
			'type data subtype qos-cf-poll',
			'wlan type 8 subtype qos-cf-poll',
			'type 8 subtype qos-cf-poll',
			'wlan type data subtype 0xe0',
			'type data subtype 0xe0',
			'wlan type 8 subtype 0xe0',
			'type 8 subtype 0xe0',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0xe8            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_qos_cf_poll
	{
		name => 'wlan_subtype_qos_cf_ack_poll',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan subtype qos-cf-ack-poll',
			'subtype qos-cf-ack-poll',
			'wlan type data subtype qos-cf-ack-poll',
			'type data subtype qos-cf-ack-poll',
			'wlan type 8 subtype qos-cf-ack-poll',
			'type 8 subtype qos-cf-ack-poll',
			'wlan type data subtype 0xf0',
			'type data subtype 0xf0',
			'wlan type 8 subtype 0xf0',
			'type 8 subtype 0xf0',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xfc
			(002) jeq      #0xf8            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_subtype_qos_cf_ack_poll
	{
		name => 'wlan_type_reserved',
		DLT => 'IEEE802_11',
		# Reserved frame type, no name.
		aliases => [
			'wlan type 12',
			'type 12',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xc
			(002) jeq      #0xc             jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_type_reserved
	{
		name => 'wlan_dir_nods',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan dir nods',
			'wlan dir 0',
			'dir nods',
			'dir 0',
			'wlan direction nods',
			'wlan direction 0',
			'direction nods',
			'direction 0',
		],
		opt => '
			(000) ldb      [1]
			(001) jset     #0x3             jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # wlan_dir_nods
	{
		name => 'wlan_dir_tods',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan dir tods',
			'wlan dir 1',
			'dir tods',
			'dir 1',
			'wlan direction tods',
			'wlan direction 1',
			'direction tods',
			'direction 1',
		],
		opt => '
			(000) ldb      [1]
			(001) and      #0x3
			(002) jeq      #0x1             jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_dir_tods
	{
		name => 'wlan_dir_fromds',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan dir fromds',
			'wlan dir 2',
			'dir fromds',
			'dir 2',
			'wlan direction fromds',
			'wlan direction 2',
			'direction fromds',
			'direction 2',
		],
		opt => '
			(000) ldb      [1]
			(001) and      #0x3
			(002) jeq      #0x2             jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_dir_fromds
	{
		name => 'wlan_dir_dstods',
		DLT => 'IEEE802_11',
		aliases => [
			'wlan dir dstods',
			'wlan dir 3',
			'dir dstods',
			'dir 3',
			'wlan direction dstods',
			'wlan direction 3',
			'direction dstods',
			'direction 3',
		],
		opt => '
			(000) ldb      [1]
			(001) and      #0x3
			(002) jeq      #0x3             jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # wlan_dir_dstods
	# For the other DLTs that "wlan dir" supports as well test only one
	# alias and only one direction -- the only difference is the
	# DLT-specific preamble, and the many equivalent ways to spell what
	# follows have already been tested above.
	{
		name => 'wlan_dir_fromds_IEEE802_11_RADIO',
		DLT => 'IEEE802_11_RADIO',
		aliases => ['wlan dir fromds'],
		opt => '
			(000) ldb      [3]
			(001) lsh      #8
			(002) tax
			(003) ldb      [2]
			(004) or       x
			(005) tax
			(006) ldb      [x + 1]
			(007) and      #0x3
			(008) jeq      #0x2             jt 9	jf 10
			(009) ret      #262144
			(010) ret      #0
			',
	}, # wlan_dir_fromds_IEEE802_11_RADIO
	{
		name => 'wlan_dir_fromds_IEEE802_11_RADIO_AVS',
		DLT => 'IEEE802_11_RADIO_AVS',
		aliases => ['wlan dir fromds'],
		opt => '
			(000) ld       [4]
			(001) tax
			(002) ldb      [x + 1]
			(003) and      #0x3
			(004) jeq      #0x2             jt 5	jf 6
			(005) ret      #262144
			(006) ret      #0
			',
	}, # wlan_dir_fromds_IEEE802_11_RADIO_AVS
	{
		name => 'wlan_dir_fromds_PRISM_HEADER',
		DLT => 'PRISM_HEADER',
		aliases => ['wlan dir fromds'],
		unopt => '
			(000) ld       [0]
			(001) and      #0xfffff000
			(002) jeq      #0x80211000      jt 3	jf 5
			(003) ld       [4]
			(004) ja       6
			(005) ld       #0x90
			(006) st       M[0]
			(007) tax
			(008) ldx      M[0]
			(009) ldb      [x + 1]
			(010) and      #0x3
			(011) jeq      #0x2             jt 12	jf 13
			(012) ret      #262144
			(013) ret      #0
			',
	}, # wlan_dir_fromds_PRISM_HEADER
	{
		name => 'wlan_dir_fromds_PPI',
		DLT => 'PPI',
		aliases => ['wlan dir fromds'],
		opt => '
			(000) ld       [4]
			(001) jeq      #0x69000000      jt 2	jf 12
			(002) ldb      [3]
			(003) lsh      #8
			(004) tax
			(005) ldb      [2]
			(006) or       x
			(007) tax
			(008) ldb      [x + 1]
			(009) and      #0x3
			(010) jeq      #0x2             jt 11	jf 12
			(011) ret      #262144
			(012) ret      #0
			',
	}, # wlan_dir_fromds_PPI

	{
		name => 'pppoed',
		snaplen => 200,
		DLT => 'EN10MB',
		aliases => ['pppoed'],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x8863          jt 2	jf 3
			(002) ret      #200
			(003) ret      #0
			',
	}, # pppoed
	{
		name => 'pppoes_nullary',
		snaplen => 200,
		DLT => 'EN10MB',
		aliases => ['pppoes'],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x8864          jt 2	jf 3
			(002) ret      #200
			(003) ret      #0
			',
	}, # pppoes_nullary
	{
		name => 'pppoes_unary',
		snaplen => 200,
		DLT => 'EN10MB',
		aliases => ['pppoes 1234'],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x8864          jt 2	jf 5
			(002) ldh      [16]
			(003) jeq      #0x4d2           jt 4	jf 5
			(004) ret      #200
			(005) ret      #0
			',
	}, # pppoes_unary

	{
		name => 'llc_nullary',
		snaplen => 200,
		DLT => 'EN10MB',
		aliases => ['llc'],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 4	jf 2
			(002) ldh      [14]
			(003) jeq      #0xffff          jt 4	jf 5
			(004) ret      #0
			(005) ret      #200
			',
	}, # llc_nullary
	{
		name => 'llc_i',
		snaplen => 100,
		DLT => 'EN10MB',
		aliases => ['llc i'],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 6	jf 2
			(002) ldh      [14]
			(003) jeq      #0xffff          jt 6	jf 4
			(004) ldb      [16]
			(005) jset     #0x1             jt 6	jf 7
			(006) ret      #0
			(007) ret      #100
			',
	}, # llc_i
	{
		name => 'llc_s',
		snaplen => 100,
		DLT => 'EN10MB',
		aliases => ['llc s'],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 8	jf 2
			(002) ldh      [14]
			(003) jeq      #0xffff          jt 8	jf 4
			(004) ldb      [16]
			(005) and      #0x3
			(006) jeq      #0x1             jt 7	jf 8
			(007) ret      #100
			(008) ret      #0
			',
	}, # llc_s
	{
		name => 'llc_u',
		snaplen => 100,
		DLT => 'EN10MB',
		aliases => ['llc u'],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 8	jf 2
			(002) ldh      [14]
			(003) jeq      #0xffff          jt 8	jf 4
			(004) ldb      [16]
			(005) and      #0x3
			(006) jeq      #0x3             jt 7	jf 8
			(007) ret      #100
			(008) ret      #0
			',
	}, # llc_u
	{
		name => 'llc_rr',
		snaplen => 100,
		DLT => 'EN10MB',
		aliases => ['llc rr'],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 8	jf 2
			(002) ldh      [14]
			(003) jeq      #0xffff          jt 8	jf 4
			(004) ldb      [16]
			(005) and      #0xf
			(006) jeq      #0x1             jt 7	jf 8
			(007) ret      #100
			(008) ret      #0
			',
	}, # llc_rr
	{
		name => 'llc_rnr',
		snaplen => 100,
		DLT => 'EN10MB',
		aliases => ['llc rnr'],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 8	jf 2
			(002) ldh      [14]
			(003) jeq      #0xffff          jt 8	jf 4
			(004) ldb      [16]
			(005) and      #0xf
			(006) jeq      #0x5             jt 7	jf 8
			(007) ret      #100
			(008) ret      #0
			',
	}, # llc_rnr
	{
		name => 'llc_rej',
		snaplen => 100,
		DLT => 'EN10MB',
		aliases => ['llc rej'],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 8	jf 2
			(002) ldh      [14]
			(003) jeq      #0xffff          jt 8	jf 4
			(004) ldb      [16]
			(005) and      #0xf
			(006) jeq      #0x9             jt 7	jf 8
			(007) ret      #100
			(008) ret      #0
			',
	}, # llc_rej
	{
		name => 'llc_ui',
		snaplen => 100,
		DLT => 'EN10MB',
		aliases => ['llc ui'],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 8	jf 2
			(002) ldh      [14]
			(003) jeq      #0xffff          jt 8	jf 4
			(004) ldb      [16]
			(005) and      #0xef
			(006) jeq      #0x3             jt 7	jf 8
			(007) ret      #100
			(008) ret      #0
			',
	}, # llc_ui
	{
		name => 'llc_ua',
		snaplen => 100,
		DLT => 'EN10MB',
		aliases => ['llc ua'],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 8	jf 2
			(002) ldh      [14]
			(003) jeq      #0xffff          jt 8	jf 4
			(004) ldb      [16]
			(005) and      #0xef
			(006) jeq      #0x63            jt 7	jf 8
			(007) ret      #100
			(008) ret      #0
			',
	}, # llc_ua
	{
		name => 'llc_disc',
		snaplen => 100,
		DLT => 'EN10MB',
		aliases => ['llc disc'],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 8	jf 2
			(002) ldh      [14]
			(003) jeq      #0xffff          jt 8	jf 4
			(004) ldb      [16]
			(005) and      #0xef
			(006) jeq      #0x43            jt 7	jf 8
			(007) ret      #100
			(008) ret      #0
			',
	}, # llc_disc
	{
		name => 'llc_dm',
		snaplen => 100,
		DLT => 'EN10MB',
		aliases => ['llc dm'],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 8	jf 2
			(002) ldh      [14]
			(003) jeq      #0xffff          jt 8	jf 4
			(004) ldb      [16]
			(005) and      #0xef
			(006) jeq      #0xf             jt 7	jf 8
			(007) ret      #100
			(008) ret      #0
			',
	}, # llc_dm
	{
		name => 'llc_sabme',
		snaplen => 100,
		DLT => 'EN10MB',
		aliases => ['llc sabme'],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 8	jf 2
			(002) ldh      [14]
			(003) jeq      #0xffff          jt 8	jf 4
			(004) ldb      [16]
			(005) and      #0xef
			(006) jeq      #0x6f            jt 7	jf 8
			(007) ret      #100
			(008) ret      #0
			',
	}, # llc_sabme
	{
		name => 'llc_test',
		snaplen => 100,
		DLT => 'EN10MB',
		aliases => ['llc test'],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 8	jf 2
			(002) ldh      [14]
			(003) jeq      #0xffff          jt 8	jf 4
			(004) ldb      [16]
			(005) and      #0xef
			(006) jeq      #0xe3            jt 7	jf 8
			(007) ret      #100
			(008) ret      #0
			',
	}, # llc_test
	{
		name => 'llc_xid',
		snaplen => 100,
		DLT => 'EN10MB',
		aliases => ['llc xid'],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 8	jf 2
			(002) ldh      [14]
			(003) jeq      #0xffff          jt 8	jf 4
			(004) ldb      [16]
			(005) and      #0xef
			(006) jeq      #0xaf            jt 7	jf 8
			(007) ret      #100
			(008) ret      #0
			',
	}, # llc_xid
	{
		name => 'llc_frmr',
		snaplen => 100,
		DLT => 'EN10MB',
		aliases => ['llc frmr'],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 8	jf 2
			(002) ldh      [14]
			(003) jeq      #0xffff          jt 8	jf 4
			(004) ldb      [16]
			(005) and      #0xef
			(006) jeq      #0x87            jt 7	jf 8
			(007) ret      #100
			(008) ret      #0
			',
	}, # llc_frmr
	{
		name => 'llc_SUNATM',
		DLT => 'SUNATM',
		aliases => ['llc'],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf
			(002) jeq      #0x2             jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # llc_SUNATM
	{
		name => 'llc_IEEE802',
		DLT => 'IEEE802',
		aliases => ['llc'],
		opt => '
			(000) ret      #262144
			',
	}, # llc_IEEE802
	{
		name => 'llc_FDDI',
		DLT => 'FDDI',
		aliases => ['llc'],
		opt => '
			(000) ret      #262144
			',
	}, # llc_FDDI
	{
		name => 'llc_ATM_RFC1483',
		DLT => 'ATM_RFC1483',
		aliases => ['llc'],
		opt => '
			(000) ret      #262144
			',
	}, # llc_ATM_RFC1483
	{
		name => 'llc_IEEE802_11',
		DLT => 'IEEE802_11',
		aliases => [
			'llc',
			'wlan type data',
		],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xc
			(002) jeq      #0x8             jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # llc_IEEE802_11
	{
		name => 'llc_IEEE802_11_RADIO',
		DLT => 'IEEE802_11_RADIO',
		aliases => [
			'llc',
			'wlan type data',
		],
		opt => '
			(000) ldb      [3]
			(001) lsh      #8
			(002) tax
			(003) ldb      [2]
			(004) or       x
			(005) tax
			(006) ldb      [x + 0]
			(007) and      #0xc
			(008) jeq      #0x8             jt 9	jf 10
			(009) ret      #262144
			(010) ret      #0
			',
	}, # llc_IEEE802_11_RADIO
	{
		name => 'llc_IEEE802_11_RADIO_AVS',
		DLT => 'IEEE802_11_RADIO_AVS',
		aliases => [
			'llc',
			'wlan type data',
		],
		opt => '
			(000) ld       [4]
			(001) tax
			(002) ldb      [x + 0]
			(003) and      #0xc
			(004) jeq      #0x8             jt 5	jf 6
			(005) ret      #262144
			(006) ret      #0
			',
	}, # llc_IEEE802_11_RADIO_AVS
	{
		name => 'llc_PRISM_HEADER',
		DLT => 'PRISM_HEADER',
		aliases => [
			'llc',
			'wlan type data',
		],
		unopt => '
			(000) ld       [0]
			(001) and      #0xfffff000
			(002) jeq      #0x80211000      jt 3	jf 5
			(003) ld       [4]
			(004) ja       6
			(005) ld       #0x90
			(006) st       M[0]
			(007) tax
			(008) ldx      M[0]
			(009) ldb      [x + 0]
			(010) and      #0xc
			(011) jeq      #0x8             jt 12	jf 13
			(012) ret      #262144
			(013) ret      #0
			',
	}, # llc_PRISM_HEADER
	{
		name => 'llc_PPI',
		DLT => 'PPI',
		aliases => [
			'llc',
			'wlan type data',
		],
		opt => '
			(000) ld       [4]
			(001) jeq      #0x69000000      jt 2	jf 12
			(002) ldb      [3]
			(003) lsh      #8
			(004) tax
			(005) ldb      [2]
			(006) or       x
			(007) tax
			(008) ldb      [x + 0]
			(009) and      #0xc
			(010) jeq      #0x8             jt 11	jf 12
			(011) ret      #262144
			(012) ret      #0
			',
	}, # llc_PPI

	{
		name => 'decnet_host',
		DLT => 'EN10MB',
		aliases => ['decnet host 50.764'],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x6003          jt 2	jf 39
			(002) ldb      [16]
			(003) and      #0x7
			(004) jeq      #0x2             jt 5	jf 7
			(005) ldh      [19]
			(006) jeq      #0xfcca          jt 38	jf 7
			(007) ldh      [16]
			(008) and      #0xff07
			(009) jeq      #0x8102          jt 10	jf 12
			(010) ldh      [20]
			(011) jeq      #0xfcca          jt 38	jf 12
			(012) ldb      [16]
			(013) and      #0x7
			(014) jeq      #0x6             jt 15	jf 17
			(015) ldh      [31]
			(016) jeq      #0xfcca          jt 38	jf 17
			(017) ldh      [16]
			(018) and      #0xff07
			(019) jeq      #0x8106          jt 20	jf 22
			(020) ldh      [32]
			(021) jeq      #0xfcca          jt 38	jf 22
			(022) ld       [16]
			(023) and      #0x7ffff00
			(024) jeq      #0x2fcca00       jt 38	jf 25
			(025) ld       [16]
			(026) and      #0xff07ffff
			(027) jeq      #0x8102fcca      jt 38	jf 28
			(028) ldb      [16]
			(029) and      #0x7
			(030) jeq      #0x6             jt 31	jf 33
			(031) ldh      [23]
			(032) jeq      #0xfcca          jt 38	jf 33
			(033) ldh      [16]
			(034) and      #0xff07
			(035) jeq      #0x8106          jt 36	jf 39
			(036) ldh      [24]
			(037) jeq      #0xfcca          jt 38	jf 39
			(038) ret      #262144
			(039) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x6003          jt 2	jf 39
			(002) ldb      [16]
			(003) and      #0x7
			(004) jeq      #0x2             jt 5	jf 7
			(005) ldh      [19]
			(006) jeq      #0xfcca          jt 38	jf 7
			(007) ldh      [16]
			(008) and      #0xff07
			(009) jeq      #0x8102          jt 10	jf 12
			(010) ldh      [20]
			(011) jeq      #0xfcca          jt 38	jf 12
			(012) ldb      [16]
			(013) and      #0x7
			(014) jeq      #0x6             jt 15	jf 17
			(015) ldh      [31]
			(016) jeq      #0xfcca          jt 38	jf 17
			(017) ldh      [16]
			(018) and      #0xff07
			(019) jeq      #0x8106          jt 20	jf 22
			(020) ldh      [32]
			(021) jeq      #0xfcca          jt 38	jf 22
			(022) ld       [16]
			(023) and      #0x7ffff00
			(024) jeq      #0x2fcca00       jt 38	jf 25
			(025) ld       [16]
			(026) and      #0xff07ffff
			(027) jeq      #0x8102fcca      jt 38	jf 28
			(028) ldb      [16]
			(029) and      #0x7
			(030) jeq      #0x6             jt 31	jf 33
			(031) ldh      [23]
			(032) jeq      #0xfcca          jt 38	jf 33
			(033) ldh      [16]
			(034) and      #0xff07
			(035) jeq      #0x8106          jt 36	jf 39
			(036) ldh      [24]
			(037) jeq      #0xfcca          jt 38	jf 39
			(038) ret      #262144
			(039) ret      #0
			',
	}, # decnet_host
	{
		name => 'decnet_src_host',
		DLT => 'EN10MB',
		aliases => [
			'decnet src host 50.764',
			'decnet src 50.764',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x6003          jt 2	jf 23
			(002) ldb      [16]
			(003) and      #0x7
			(004) jeq      #0x2             jt 5	jf 7
			(005) ldh      [19]
			(006) jeq      #0xfcca          jt 22	jf 7
			(007) ldh      [16]
			(008) and      #0xff07
			(009) jeq      #0x8102          jt 10	jf 12
			(010) ldh      [20]
			(011) jeq      #0xfcca          jt 22	jf 12
			(012) ldb      [16]
			(013) and      #0x7
			(014) jeq      #0x6             jt 15	jf 17
			(015) ldh      [31]
			(016) jeq      #0xfcca          jt 22	jf 17
			(017) ldh      [16]
			(018) and      #0xff07
			(019) jeq      #0x8106          jt 20	jf 23
			(020) ldh      [32]
			(021) jeq      #0xfcca          jt 22	jf 23
			(022) ret      #262144
			(023) ret      #0
			',
	}, # decnet_src_host
	{
		name => 'decnet_dst_host',
		DLT => 'EN10MB',
		aliases => [
			'decnet dst host 50.764',
			'decnet dst 50.764',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x6003          jt 2	jf 19
			(002) ld       [16]
			(003) and      #0x7ffff00
			(004) jeq      #0x2fcca00       jt 18	jf 5
			(005) ld       [16]
			(006) and      #0xff07ffff
			(007) jeq      #0x8102fcca      jt 18	jf 8
			(008) ldb      [16]
			(009) and      #0x7
			(010) jeq      #0x6             jt 11	jf 13
			(011) ldh      [23]
			(012) jeq      #0xfcca          jt 18	jf 13
			(013) ldh      [16]
			(014) and      #0xff07
			(015) jeq      #0x8106          jt 16	jf 19
			(016) ldh      [24]
			(017) jeq      #0xfcca          jt 18	jf 19
			(018) ret      #262144
			(019) ret      #0
			',
	}, # decnet_dst_host

	{
		name => 'iso_proto_clnp',
		DLT => 'EN10MB',
		aliases => [
			'iso proto \clnp',
			'clnp',
			'iso proto 0x81',
		],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 7	jf 2
			(002) ldh      [14]
			(003) jeq      #0xfefe          jt 4	jf 7
			(004) ldb      [17]
			(005) jeq      #0x81            jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # iso_proto_clnp
	{
		name => 'iso_proto_esis',
		DLT => 'EN10MB',
		aliases => [
			'iso proto \esis',
			'esis',
			'es-is',
			'iso proto 0x82',
		],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 7	jf 2
			(002) ldh      [14]
			(003) jeq      #0xfefe          jt 4	jf 7
			(004) ldb      [17]
			(005) jeq      #0x82            jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # iso_proto_esis
	{
		name => 'iso_proto_isis',
		DLT => 'EN10MB',
		aliases => [
			'iso proto \isis',
			'isis',
			'is-is',
			'iso proto 0x83',
		],
		unopt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 7	jf 2
			(002) ldh      [14]
			(003) jeq      #0xfefe          jt 4	jf 7
			(004) ldb      [17]
			(005) jeq      #0x83            jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # iso_proto_isis
	# Test the DLT dimension of "iso proto" for one alias only.
	{
		name => 'iso_proto_clnp_FRELAY',
		DLT => 'FRELAY',
		aliases => ['iso proto \clnp'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0x381           jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # clnp_FRELAY
	{
		name => 'iso_proto_clnp_C_HDLC',
		DLT => 'C_HDLC',
		aliases => ['iso proto \clnp'],
		unopt => '
			(000) ldh      [2]
			(001) jeq      #0xfefe          jt 2	jf 5
			(002) ldb      [5]
			(003) jeq      #0x81            jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # clnp_C_HDLC
	{
		name => 'isis_l1',
		DLT => 'EN10MB',
		aliases => [
			'l1',
			'isis proto 0x1a or 0x18 or 0x12 or 0x0f or 0x11',
		],
		opt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 22	jf 2
			(002) ldh      [14]
			(003) jeq      #0xfefe          jt 4	jf 22
			(004) ldb      [17]
			(005) jeq      #0x83            jt 6	jf 22
			(006) ldb      [21]
			(007) and      #0x1f
			(008) jeq      #0x1a            jt 21	jf 9
			(009) ldb      [21]
			(010) and      #0x1f
			(011) jeq      #0x18            jt 21	jf 12
			(012) ldb      [21]
			(013) and      #0x1f
			(014) jeq      #0x12            jt 21	jf 15
			(015) ldb      [21]
			(016) and      #0x1f
			(017) jeq      #0xf             jt 21	jf 18
			(018) ldb      [21]
			(019) and      #0x1f
			(020) jeq      #0x11            jt 21	jf 22
			(021) ret      #262144
			(022) ret      #0
			',
	}, # isis_l1
	{
		name => 'isis_l2',
		DLT => 'EN10MB',
		aliases => [
			'l2',
			'isis proto 0x1b or 0x19 or 0x14 or 0x10 or 0x11',
		],
		opt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 22	jf 2
			(002) ldh      [14]
			(003) jeq      #0xfefe          jt 4	jf 22
			(004) ldb      [17]
			(005) jeq      #0x83            jt 6	jf 22
			(006) ldb      [21]
			(007) and      #0x1f
			(008) jeq      #0x1b            jt 21	jf 9
			(009) ldb      [21]
			(010) and      #0x1f
			(011) jeq      #0x19            jt 21	jf 12
			(012) ldb      [21]
			(013) and      #0x1f
			(014) jeq      #0x14            jt 21	jf 15
			(015) ldb      [21]
			(016) and      #0x1f
			(017) jeq      #0x10            jt 21	jf 18
			(018) ldb      [21]
			(019) and      #0x1f
			(020) jeq      #0x11            jt 21	jf 22
			(021) ret      #262144
			(022) ret      #0
			',
	}, # isis_l2
	{
		name => 'isis_iih',
		DLT => 'EN10MB',
		aliases => [
			'iih',
			'isis proto 0x11 or 0x0f or 0x10',
		],
		opt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 16	jf 2
			(002) ldh      [14]
			(003) jeq      #0xfefe          jt 4	jf 16
			(004) ldb      [17]
			(005) jeq      #0x83            jt 6	jf 16
			(006) ldb      [21]
			(007) and      #0x1f
			(008) jeq      #0x11            jt 15	jf 9
			(009) ldb      [21]
			(010) and      #0x1f
			(011) jeq      #0xf             jt 15	jf 12
			(012) ldb      [21]
			(013) and      #0x1f
			(014) jeq      #0x10            jt 15	jf 16
			(015) ret      #262144
			(016) ret      #0
			',
	}, # isis_iih
	{
		name => 'isis_lsp',
		DLT => 'EN10MB',
		aliases => [
			'lsp',
			'isis proto 0x12 or 0x14',
		],
		opt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 13	jf 2
			(002) ldh      [14]
			(003) jeq      #0xfefe          jt 4	jf 13
			(004) ldb      [17]
			(005) jeq      #0x83            jt 6	jf 13
			(006) ldb      [21]
			(007) and      #0x1f
			(008) jeq      #0x12            jt 12	jf 9
			(009) ldb      [21]
			(010) and      #0x1f
			(011) jeq      #0x14            jt 12	jf 13
			(012) ret      #262144
			(013) ret      #0
			',
	}, # isis_lsp
	{
		name => 'isis_lsp_C_HDLC',
		DLT => 'C_HDLC',
		aliases => ['lsp'],
		opt => '
			(000) ldh      [2]
			(001) jeq      #0xfefe          jt 2	jf 11
			(002) ldb      [5]
			(003) jeq      #0x83            jt 4	jf 11
			(004) ldb      [9]
			(005) and      #0x1f
			(006) jeq      #0x12            jt 10	jf 7
			(007) ldb      [9]
			(008) and      #0x1f
			(009) jeq      #0x14            jt 10	jf 11
			(010) ret      #262144
			(011) ret      #0
			',
	}, # isis_lsp_C_HDLC
	{
		name => 'isis_snp',
		DLT => 'EN10MB',
		aliases => [
			'snp',
			'isis proto 0x1b or 0x1a or 0x18 or 0x19',
		],
		opt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 19	jf 2
			(002) ldh      [14]
			(003) jeq      #0xfefe          jt 4	jf 19
			(004) ldb      [17]
			(005) jeq      #0x83            jt 6	jf 19
			(006) ldb      [21]
			(007) and      #0x1f
			(008) jeq      #0x1b            jt 18	jf 9
			(009) ldb      [21]
			(010) and      #0x1f
			(011) jeq      #0x1a            jt 18	jf 12
			(012) ldb      [21]
			(013) and      #0x1f
			(014) jeq      #0x18            jt 18	jf 15
			(015) ldb      [21]
			(016) and      #0x1f
			(017) jeq      #0x19            jt 18	jf 19
			(018) ret      #262144
			(019) ret      #0
			',
	}, # isis_snp
	{
		name => 'isis_csnp',
		DLT => 'EN10MB',
		aliases => [
			'csnp',
			'isis proto 0x18 or 0x19',
		],
		opt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 13	jf 2
			(002) ldh      [14]
			(003) jeq      #0xfefe          jt 4	jf 13
			(004) ldb      [17]
			(005) jeq      #0x83            jt 6	jf 13
			(006) ldb      [21]
			(007) and      #0x1f
			(008) jeq      #0x18            jt 12	jf 9
			(009) ldb      [21]
			(010) and      #0x1f
			(011) jeq      #0x19            jt 12	jf 13
			(012) ret      #262144
			(013) ret      #0
			',
	}, # isis_csnp
	{
		name => 'isis_psnp',
		DLT => 'EN10MB',
		aliases => [
			'psnp',
			'isis proto 0x1a or 0x1b',
		],
		opt => '
			(000) ldh      [12]
			(001) jgt      #0x5dc           jt 13	jf 2
			(002) ldh      [14]
			(003) jeq      #0xfefe          jt 4	jf 13
			(004) ldb      [17]
			(005) jeq      #0x83            jt 6	jf 13
			(006) ldb      [21]
			(007) and      #0x1f
			(008) jeq      #0x1a            jt 12	jf 9
			(009) ldb      [21]
			(010) and      #0x1f
			(011) jeq      #0x1b            jt 12	jf 13
			(012) ret      #262144
			(013) ret      #0
			',
	}, # isis_psnp

	{
		name => 'ip_multicast',
		snaplen => 1000,
		DLT => 'IPV4',
		aliases => ['ip multicast'],
		opt => '
			(000) ld       #0x0
			(001) ldb      [16]
			(002) and      #0xf0
			(003) jeq      #0xe0            jt 4	jf 5
			(004) ret      #1000
			(005) ret      #0
			',
		unopt => '
			(000) ld       #0x0
			(001) jeq      #0x0             jt 2	jf 6
			(002) ldb      [16]
			(003) and      #0xf0
			(004) jeq      #0xe0            jt 5	jf 6
			(005) ret      #1000
			(006) ret      #0
			',
	}, # ip_multicast
	{
		name => 'ip_broadcast_30',
		DLT => 'IPV4',
		netmask => '***************',
		aliases => ['ip broadcast'],
		unopt => '
			(000) ld       #0x0
			(001) jeq      #0x0             jt 2	jf 9
			(002) ld       [16]
			(003) and      #0x3
			(004) jeq      #0x0             jt 8	jf 5
			(005) ld       [16]
			(006) and      #0x3
			(007) jeq      #0x3             jt 8	jf 9
			(008) ret      #262144
			(009) ret      #0
			',
	}, # ip_broadcast_30
	{
		name => 'ip_broadcast_24',
		DLT => 'IPV4',
		netmask => '*************',
		aliases => ['ip broadcast'],
		opt => '
			(000) ld       #0x0
			(001) ld       [16]
			(002) jset     #0xff            jt 3	jf 6
			(003) and      #0xff
			(004) jeq      #0xff            jt 6	jf 5
			(005) ret      #0
			(006) ret      #262144
			',
	}, # ip_broadcast_24
	{
		name => 'ip_broadcast_0',
		# tcpdump does this by default (matches 0.0.0.0 and ***************).
		DLT => 'RAW',
		netmask => '0.0.0.0',
		aliases => ['ip broadcast'],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 7
			(003) ld       [16]
			(004) jeq      #0x0             jt 6	jf 5
			(005) jeq      #0xffffffff      jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # ip_broadcast_0

	{
		name => 'ip_proto',
		DLT => 'EN10MB',
		# In this expression the protocol name is subject to external resolution
		# (typically via /etc/protocols), so pick something that is most likely
		# to resolve on all supported OSes.
		aliases => [
			'ip proto \tcp',
			'ip proto 6',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x800           jt 2	jf 5
			(002) ldb      [23]
			(003) jeq      #0x6             jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # ip6_proto
	{
		name => 'ip6_proto',
		DLT => 'EN10MB',
		aliases => [
			'ip6 proto \tcp', # Same as above.
			'ip6 proto 6',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 8
			(002) ldb      [20]
			(003) jeq      #0x6             jt 7	jf 4
			(004) jeq      #0x2c            jt 5	jf 8
			(005) ldb      [54]
			(006) jeq      #0x6             jt 7	jf 8
			(007) ret      #262144
			(008) ret      #0
			',
	}, # ip6_proto
	{
		name => 'proto',
		DLT => 'EN10MB',
		aliases => [
			'proto \tcp', # Same as above.
			'proto 6',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x800           jt 2	jf 4
			(002) ldb      [23]
			(003) jeq      #0x6             jt 10	jf 11
			(004) jeq      #0x86dd          jt 5	jf 11
			(005) ldb      [20]
			(006) jeq      #0x6             jt 10	jf 7
			(007) jeq      #0x2c            jt 8	jf 11
			(008) ldb      [54]
			(009) jeq      #0x6             jt 10	jf 11
			(010) ret      #262144
			(011) ret      #0
			',
	}, # proto
	{
		name => 'ip_protochain',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		DLT => 'RAW',
		aliases => [
			'ip protochain \udp',
			'ip protochain 17',
		],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 23
			(003) ldb      [9]
			(004) ldxb     4*([0]&0xf)
			(005) jeq      #0x11            jt 20	jf 6
			(006) jeq      #0x3b            jt 20	jf 7
			(007) add      #0
			(008) jeq      #0x33            jt 9	jf 20
			(009) ldb      [x + 0]
			(010) st       M[0]
			(011) txa
			(012) add      #1
			(013) tax
			(014) ldb      [x + 0]
			(015) add      #2
			(016) mul      #4
			(017) tax
			(018) ld       M[0]
			(019) ja       5
			(020) add      #0
			(021) jeq      #0x11            jt 22	jf 23
			(022) ret      #262144
			(023) ret      #0
			',
	}, # ip_protochain
	{
		name => 'ip6_protochain',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		DLT => 'RAW',
		aliases => [
			'ip6 protochain \udp',
			'ip6 protochain 17',
		],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x60            jt 3	jf 35
			(003) ldb      [6]
			(004) ldx      #0x28
			(005) jeq      #0x11            jt 32	jf 6
			(006) jeq      #0x3b            jt 32	jf 7
			(007) jeq      #0x0             jt 11	jf 8
			(008) jeq      #0x3c            jt 11	jf 9
			(009) jeq      #0x2b            jt 11	jf 10
			(010) jeq      #0x2c            jt 11	jf 20
			(011) ldb      [x + 0]
			(012) st       M[0]
			(013) ldb      [x + 1]
			(014) add      #1
			(015) mul      #8
			(016) add      x
			(017) tax
			(018) ld       M[0]
			(019) ja       5
			(020) jeq      #0x33            jt 21	jf 32
			(021) ldb      [x + 0]
			(022) st       M[0]
			(023) txa
			(024) add      #1
			(025) tax
			(026) ldb      [x + 0]
			(027) add      #2
			(028) mul      #4
			(029) tax
			(030) ld       M[0]
			(031) ja       5
			(032) add      #0
			(033) jeq      #0x11            jt 34	jf 35
			(034) ret      #262144
			(035) ret      #0
			',
	}, # ip6_protochain
	{
		name => 'protochain',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		DLT => 'RAW',
		aliases => [
			'protochain \udp',
			'protochain 17',
		],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 22
			(003) ldb      [9]
			(004) ldxb     4*([0]&0xf)
			(005) jeq      #0x11            jt 20	jf 6
			(006) jeq      #0x3b            jt 20	jf 7
			(007) add      #0
			(008) jeq      #0x33            jt 9	jf 20
			(009) ldb      [x + 0]
			(010) st       M[1]
			(011) txa
			(012) add      #1
			(013) tax
			(014) ldb      [x + 0]
			(015) add      #2
			(016) mul      #4
			(017) tax
			(018) ld       M[1]
			(019) ja       5
			(020) add      #0
			(021) jeq      #0x11            jt 56	jf 22
			(022) ldb      [0]
			(023) and      #0xf0
			(024) jeq      #0x60            jt 25	jf 57
			(025) ldb      [6]
			(026) ldx      #0x28
			(027) jeq      #0x11            jt 54	jf 28
			(028) jeq      #0x3b            jt 54	jf 29
			(029) jeq      #0x0             jt 33	jf 30
			(030) jeq      #0x3c            jt 33	jf 31
			(031) jeq      #0x2b            jt 33	jf 32
			(032) jeq      #0x2c            jt 33	jf 42
			(033) ldb      [x + 0]
			(034) st       M[1]
			(035) ldb      [x + 1]
			(036) add      #1
			(037) mul      #8
			(038) add      x
			(039) tax
			(040) ld       M[1]
			(041) ja       27
			(042) jeq      #0x33            jt 43	jf 54
			(043) ldb      [x + 0]
			(044) st       M[1]
			(045) txa
			(046) add      #1
			(047) tax
			(048) ldb      [x + 0]
			(049) add      #2
			(050) mul      #4
			(051) tax
			(052) ld       M[1]
			(053) ja       27
			(054) add      #0
			(055) jeq      #0x11            jt 56	jf 57
			(056) ret      #262144
			(057) ret      #0
			',
	}, # protochain
	{
		name => 'ip_host_addr',
		# For this and some other single-stack qualifiers below use DLT_RAW to
		# verify that the bytecode does not try to match the other protocol too.
		DLT => 'RAW',
		snaplen => 2000,
		aliases => [
			'ip host ***************',
			'ip src or dst ***************',
			'ip src or dst host ***************',
			'host ***************',
			'src or dst ***************',
			'src or dst host ***************',
			'ip host 0xc0a8aad3',
			'ip src or dst 0xc0a8aad3',
			'ip src or dst host 0xc0a8aad3',
			'host 0xc0a8aad3',
			'src or dst 0xc0a8aad3',
			'src or dst host 0xc0a8aad3',
			'ip host 3232279251',
			'ip src or dst 3232279251',
			'ip src or dst host 3232279251',
			'host 3232279251',
			'src or dst 3232279251',
			'src or dst host 3232279251',
			# No aliases for 030052125323, which matches a MAC address.
			# "net" without an explicit netmask defaults to /32.
			'ip net ***************/32',
			'ip src or dst net ***************/ 32',
			'net *************** /32',
			'src or dst net *************** / 32',
			'ip net ***************',
			'ip src or dst net ***************',
			'net ***************',
			'src or dst net ***************',
			'ip net 0xc0a8aad3',
			'ip src or dst net 0xc0a8aad3',
			'net 0xc0a8aad3',
			'src or dst net 0xc0a8aad3',
			'ip net 3232279251',
			'ip src or dst net 3232279251',
			'net 3232279251',
			'src or dst net 3232279251',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 8
			(003) ld       [12]
			(004) jeq      #0xc0a8aad3      jt 7	jf 5
			(005) ld       [16]
			(006) jeq      #0xc0a8aad3      jt 7	jf 8
			(007) ret      #2000
			(008) ret      #0
			',
	}, # ip_host_addr
	# The DLT supports both IPv4 and IPv6, the expressions use:
	# * IPv4-only syntax and an IPv4-only hostname
	# * IPv4-only syntax and an IPv4+IPv6 hostname
	# * IPv4+IPv6 syntax and an IPv4-only hostname
	# In each case the filter program should be the same and IPv4-only.  Other
	# similar tests use a similar approach.
	{
		name => 'ip_host_name',
		skip => skip_no_hosts(),
		DLT => 'RAW',
		snaplen => 2000,
		aliases => [
			'ip host noeth-ipv4-noipv6.host123.libpcap.test',
			'ip host noeth-ipv4-ipv6.host123.libpcap.test',
			'ip src or dst noeth-ipv4-noipv6.host123.libpcap.test',
			'ip src or dst noeth-ipv4-ipv6.host123.libpcap.test',
			'ip src or dst host noeth-ipv4-noipv6.host123.libpcap.test',
			'ip src or dst host noeth-ipv4-ipv6.host123.libpcap.test',
			'host noeth-ipv4-noipv6.host123.libpcap.test',
			'src or dst noeth-ipv4-noipv6.host123.libpcap.test',
			'src or dst host noeth-ipv4-noipv6.host123.libpcap.test',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 8
			(003) ld       [12]
			(004) jeq      #0xa141e28       jt 7	jf 5
			(005) ld       [16]
			(006) jeq      #0xa141e28       jt 7	jf 8
			(007) ret      #2000
			(008) ret      #0
			',
	}, # ip_host_name
	{
		name => 'ip_host_NAME',
		skip => skip_no_hosts_casecmp(),
		DLT => 'RAW',
		snaplen => 2000,
		aliases => [
			'ip host NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'ip host NOETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
			'ip src or dst NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'ip src or dst NOETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
			'ip src or dst host NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'ip src or dst host NOETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
			'host NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'src or dst NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'src or dst host NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 8
			(003) ld       [12]
			(004) jeq      #0xa141e28       jt 7	jf 5
			(005) ld       [16]
			(006) jeq      #0xa141e28       jt 7	jf 8
			(007) ret      #2000
			(008) ret      #0
			',
	}, # ip_host_NAME
	{
		name => 'ip_src_host_addr',
		DLT => 'RAW',
		snaplen => 2000,
		aliases => [
			'ip src host ********',
			'ip src ********',
			'src host ********',
			'src ********',
			'ip src host 0x0a000002',
			'ip src 0x0A000002',
			'src host 0X0a000002',
			'src 0X0A000002',
			'ip src host 0xa000002',
			'ip src 0xA000002',
			'src host 0Xa000002',
			'src 0XA000002',
			'ip src host 167772162',
			'ip src 167772162',
			'src host 167772162',
			'src 167772162',
			'ip src host 01200000002',
			'ip src 01200000002',
			'src host 01200000002',
			'src 01200000002',
			# "net" /32
			'ip src net ********/  32',
			'src net ********  /32',
			'ip src net ********',
			'src net ********',
			'ip src net 0x0a000002',
			'src net 0X0a000002',
			'ip src net 0xa000002',
			'src net 0Xa000002',
			'ip src net 167772162',
			'src net 167772162',
			'ip src net 01200000002',
			'src net 01200000002',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 6
			(003) ld       [12]
			(004) jeq      #0xa000002       jt 5	jf 6
			(005) ret      #2000
			(006) ret      #0
			',
	}, # ip_src_host_addr
	{
		name => 'ip_src_host_name',
		skip => skip_no_hosts(),
		DLT => 'RAW',
		snaplen => 2000,
		aliases => [
			'ip src host noeth-ipv4-noipv6.host123.libpcap.test',
			'ip src host noeth-ipv4-ipv6.host123.libpcap.test',
			'ip src noeth-ipv4-noipv6.host123.libpcap.test',
			'ip src noeth-ipv4-ipv6.host123.libpcap.test',
			'src host noeth-ipv4-noipv6.host123.libpcap.test',
			'src noeth-ipv4-noipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 6
			(003) ld       [12]
			(004) jeq      #0xa141e28       jt 5	jf 6
			(005) ret      #2000
			(006) ret      #0
			',
	}, # ip_src_host_name
	{
		name => 'ip_src_host_NAME',
		skip => skip_no_hosts_casecmp(),
		DLT => 'RAW',
		snaplen => 2000,
		aliases => [
			'ip src host NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'ip src host NOETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
			'ip src NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'ip src NOETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
			'src host NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'src NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 6
			(003) ld       [12]
			(004) jeq      #0xa141e28       jt 5	jf 6
			(005) ret      #2000
			(006) ret      #0
			',
	}, # ip_src_host_NAME
	{
		name => 'ip_dst_host_addr',
		DLT => 'RAW',
		snaplen => 2000,
		aliases => [
			'ip dst host ************',
			'ip dst ************',
			'dst host ************',
			'dst ************',
			'ip dst host 0xac11591e',
			'ip dst 0xAC11591E',
			'dst host 0Xac11591e',
			'dst 0XAC11591E',
			'ip dst host 2886818078',
			'ip dst 2886818078',
			'dst host 2886818078',
			'dst 2886818078',
			# No aliases for 025404254436, which matches a MAC address.
			# "net" /32
			'ip dst net ************ /  32',
			'dst net ************  / 32',
			'ip dst net ************',
			'dst net ************',
			'ip dst net 0xac11591e',
			'dst net 0Xac11591e',
			'ip dst net 2886818078',
			'dst net 2886818078',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 6
			(003) ld       [16]
			(004) jeq      #0xac11591e      jt 5	jf 6
			(005) ret      #2000
			(006) ret      #0
			',
	}, # ip_dst_host_addr
	{
		name => 'ip_dst_host_name',
		skip => skip_no_hosts(),
		DLT => 'RAW',
		snaplen => 2000,
		aliases => [
			'ip dst host noeth-ipv4-noipv6.host123.libpcap.test',
			'ip dst host noeth-ipv4-ipv6.host123.libpcap.test',
			'ip dst noeth-ipv4-noipv6.host123.libpcap.test',
			'ip dst noeth-ipv4-ipv6.host123.libpcap.test',
			'dst host noeth-ipv4-noipv6.host123.libpcap.test',
			'dst noeth-ipv4-noipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 6
			(003) ld       [16]
			(004) jeq      #0xa141e28       jt 5	jf 6
			(005) ret      #2000
			(006) ret      #0
			',
	}, # ip_dst_host_name
	{
		name => 'ip_dst_host_NAME',
		skip => skip_no_hosts_casecmp(),
		DLT => 'RAW',
		snaplen => 2000,
		aliases => [
			'ip dst host NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'ip dst host NOETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
			'ip dst NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'ip dst NOETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
			'dst host NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'dst NOETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 6
			(003) ld       [16]
			(004) jeq      #0xa141e28       jt 5	jf 6
			(005) ret      #2000
			(006) ret      #0
			',
	}, # ip_dst_host_NAME
	{
		name => 'ip_net_addr',
		DLT => 'RAW',
		snaplen => 2000,
		aliases => [
			'ip net ***********/16',
			'ip src or dst net ***********/16',
			'net ***********/16',
			'src or dst net ***********/16',
			'ip net 192.168/16',
			'ip src or dst net 192.168/16',
			'net 192.168/16',
			'src or dst net 192.168/16',
			'ip net *********** mask ***********',
			'ip src or dst net *********** mask ***********',
			'net *********** mask ***********',
			'src or dst net *********** mask ***********',
			'ip net *********** mask 255.255',
			'ip src or dst net *********** mask 255.255',
			'net *********** mask 255.255',
			'src or dst net *********** mask 255.255',
			'ip net 192.168 mask ***********',
			'ip src or dst net 192.168 mask ***********',
			'net 192.168 mask ***********',
			'src or dst net 192.168 mask ***********',
			'ip net 192.168 mask 255.255',
			'ip src or dst net 192.168 mask 255.255',
			'net 192.168 mask 255.255',
			'src or dst net 192.168 mask 255.255',
			'ip net 192.168',
			'ip src or dst net 192.168',
			'net 192.168',
			'src or dst net 192.168',
		],
		# Only the optimized bytecode is equivalent!
		# (This is because "net 192.168/16" means "ip net 192.168/16 or
		# arp net 192.168/16 or rarp net 192.168/16" and on DLT_RAW each
		# of latter two parts generates an explicit false condition,
		# which eventually optimizes out.)
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 10
			(003) ld       [12]
			(004) and      #0xffff0000
			(005) jeq      #0xc0a80000      jt 9	jf 6
			(006) ld       [16]
			(007) and      #0xffff0000
			(008) jeq      #0xc0a80000      jt 9	jf 10
			(009) ret      #2000
			(010) ret      #0
			',
	}, # ip_net_addr
	{
		name => 'ip_net_name',
		skip => skip_no_networks(),
		DLT => 'RAW',
		aliases => [
			'ip net net-10-0-0-0.libpcap.test',
			'ip src or dst net net-10-0-0-0.libpcap.test',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 8
			(003) ld       [12]
			(004) jeq      #0xa000000       jt 7	jf 5
			(005) ld       [16]
			(006) jeq      #0xa000000       jt 7	jf 8
			(007) ret      #262144
			(008) ret      #0
			',
	}, # ip_net_name
	{
		name => 'ip_net_NAME',
		skip => skip_no_networks_casecmp(),
		DLT => 'RAW',
		aliases => [
			'ip net NET-10-0-0-0.LIBPCAP.TEST',
			'ip src or dst net NET-10-0-0-0.LIBPCAP.TEST',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 8
			(003) ld       [12]
			(004) jeq      #0xa000000       jt 7	jf 5
			(005) ld       [16]
			(006) jeq      #0xa000000       jt 7	jf 8
			(007) ret      #262144
			(008) ret      #0
			',
	}, # ip_net_NAME
	{
		name => 'ip_src_net_addr',
		DLT => 'RAW',
		snaplen => 2000,
		aliases => [
			'ip src net ********/24',
			'src net ********/24',
			'ip src net 10.0.1/24',
			'src net 10.0.1/24',
			'ip src net ******** mask *************',
			'src net ******** mask *************',
			'ip src net ******** mask 255.255.255',
			'src net ******** mask 255.255.255',
			'ip src net 10.0.1 mask *************',
			'src net 10.0.1 mask *************',
			'ip src net 10.0.1 mask 255.255.255',
			'src net 10.0.1 mask 255.255.255',
			'ip src net 10.0.1',
			'src net 10.0.1',
		],
		# Only the optimized bytecode is equivalent!
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 7
			(003) ld       [12]
			(004) and      #0xffffff00
			(005) jeq      #0xa000100       jt 6	jf 7
			(006) ret      #2000
			(007) ret      #0
			',
	}, # ip_src_net_addr
	{
		name => 'ip_src_net_name',
		skip => skip_no_networks(),
		DLT => 'RAW',
		aliases => [
			'ip src net net-10-20-0-0.libpcap.test',
			'src net net-10-20-0-0.libpcap.test',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 6
			(003) ld       [12]
			(004) jeq      #0xa140000       jt 5	jf 6
			(005) ret      #262144
			(006) ret      #0
			',
	}, # ip_src_net_name
	{
		name => 'ip_src_net_NAME',
		skip => skip_no_networks_casecmp(),
		DLT => 'RAW',
		aliases => [
			'ip src net NET-10-20-0-0.LIBPCAP.TEST',
			'src net NET-10-20-0-0.LIBPCAP.TEST',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 6
			(003) ld       [12]
			(004) jeq      #0xa140000       jt 5	jf 6
			(005) ret      #262144
			(006) ret      #0
			',
	}, # ip_src_net_NAME
	{
		name => 'ip_dst_net_addr_0',
		DLT => 'RAW',
		snaplen => 2000,
		aliases => ['ip dst net 0.0.0.0/0'],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 4
			(003) ret      #2000
			(004) ret      #0
			',
	}, # ip_dst_net_addr_0
	{
		name => 'ip_dst_net_addr_8',
		DLT => 'RAW',
		snaplen => 2000,
		aliases => [
			'ip dst net 10.0.0.0/8',
			'dst net 10.0.0.0/8',
			'ip dst net 10.0.0.0 mask *********',
			'dst net 10.0.0.0 mask *********',
			# "net 10/<any masklength>", "net 10 mask <any mask>" and
			# "net <any net> mask 255" are invalid syntax.
			'ip dst net 10',
			'dst net 10',
		],
		# Only the optimized bytecode is equivalent!
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 7
			(003) ld       [16]
			(004) and      #0xff000000
			(005) jeq      #0xa000000       jt 6	jf 7
			(006) ret      #2000
			(007) ret      #0
			',
	}, # ip_dst_net_addr_8
	{
		name => 'ip_dst_net_name',
		skip => skip_no_networks(),
		DLT => 'RAW',
		aliases => [
			'ip dst net net-10-20-30-0.libpcap.test',
			'dst net net-10-20-30-0.libpcap.test',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 6
			(003) ld       [16]
			(004) jeq      #0xa141e00       jt 5	jf 6
			(005) ret      #262144
			(006) ret      #0
			',
	}, # ip_dst_net_name
	{
		name => 'ip_dst_net_NAME',
		skip => skip_no_networks_casecmp(),
		DLT => 'RAW',
		aliases => [
			'ip dst net NET-10-20-30-0.LIBPCAP.TEST',
			'dst net NET-10-20-30-0.LIBPCAP.TEST',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 6
			(003) ld       [16]
			(004) jeq      #0xa141e00       jt 5	jf 6
			(005) ret      #262144
			(006) ret      #0
			',
	}, # ip_dst_net_NAME
	# TODO: Verify identity with DLT_NETANALYZER and
	# DLT_NETANALYZER_TRANSPARENT in all DLT_EN10MB gateway tests.
	{
		name => 'gateway_name_en10mb',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'EN10MB',
		aliases => [
			'gateway eth-ipv4-noipv6.host123.libpcap.test',
			# In the current implementation of this keyword the presence of an IPv6
			# address in the Internet address space should make no difference in
			# the resulting filter program.
			'gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		opt => '
			(000) ld       [8]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [6]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [2]
			(005) jeq      #0x400140e       jt 6	jf 21
			(006) ldh      [0]
			(007) jeq      #0xaa00          jt 8	jf 21
			(008) ldh      [12]
			(009) jeq      #0x800           jt 10	jf 14
			(010) ld       [26]
			(011) jeq      #0xa141e28       jt 21	jf 12
			(012) ld       [30]
			(013) jeq      #0xa141e28       jt 21	jf 20
			(014) jeq      #0x806           jt 16	jf 15
			(015) jeq      #0x8035          jt 16	jf 20
			(016) ld       [28]
			(017) jeq      #0xa141e28       jt 21	jf 18
			(018) ld       [38]
			(019) jeq      #0xa141e28       jt 21	jf 20
			(020) ret      #262144
			(021) ret      #0
			',
	}, # gateway_name_en10mb
	{
		name => 'gateway_NAME_en10mb',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'EN10MB',
		aliases => [
			'gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		opt => '
			(000) ld       [8]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [6]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [2]
			(005) jeq      #0x400140e       jt 6	jf 21
			(006) ldh      [0]
			(007) jeq      #0xaa00          jt 8	jf 21
			(008) ldh      [12]
			(009) jeq      #0x800           jt 10	jf 14
			(010) ld       [26]
			(011) jeq      #0xa141e28       jt 21	jf 12
			(012) ld       [30]
			(013) jeq      #0xa141e28       jt 21	jf 20
			(014) jeq      #0x806           jt 16	jf 15
			(015) jeq      #0x8035          jt 16	jf 20
			(016) ld       [28]
			(017) jeq      #0xa141e28       jt 21	jf 18
			(018) ld       [38]
			(019) jeq      #0xa141e28       jt 21	jf 20
			(020) ret      #262144
			(021) ret      #0
			',
	}, # gateway_NAME_en10mb
	{
		name => 'ip_gateway_name_en10mb',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'EN10MB',
		aliases => [
			'ip gateway eth-ipv4-noipv6.host123.libpcap.test',
			'ip gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ld       [8]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [6]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [2]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [0]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [12]
			(009) jeq      #0x800           jt 10	jf 14
			(010) ld       [26]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [30]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # ip_gateway_name_en10mb
	{
		name => 'ip_gateway_NAME_en10mb',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'EN10MB',
		aliases => [
			'ip gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'ip gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ld       [8]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [6]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [2]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [0]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [12]
			(009) jeq      #0x800           jt 10	jf 14
			(010) ld       [26]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [30]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # ip_gateway_NAME_en10mb
	{
		name => 'arp_gateway_name_en10mb',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'EN10MB',
		aliases => [
			'arp gateway eth-ipv4-noipv6.host123.libpcap.test',
			'arp gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ld       [8]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [6]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [2]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [0]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [12]
			(009) jeq      #0x806           jt 10	jf 14
			(010) ld       [28]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [38]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # arp_gateway_name_en10mb
	{
		name => 'arp_gateway_NAME_en10mb',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'EN10MB',
		aliases => [
			'arp gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'arp gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ld       [8]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [6]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [2]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [0]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [12]
			(009) jeq      #0x806           jt 10	jf 14
			(010) ld       [28]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [38]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # arp_gateway_NAME_en10mb
	{
		name => 'rarp_gateway_name_en10mb',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'EN10MB',
		aliases => [
			'rarp gateway eth-ipv4-noipv6.host123.libpcap.test',
			'rarp gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ld       [8]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [6]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [2]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [0]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [12]
			(009) jeq      #0x8035          jt 10	jf 14
			(010) ld       [28]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [38]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # rarp_gateway_name_en10mb
	{
		name => 'rarp_gateway_NAME_en10mb',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'EN10MB',
		aliases => [
			'rarp gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'rarp gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ld       [8]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [6]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [2]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [0]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [12]
			(009) jeq      #0x8035          jt 10	jf 14
			(010) ld       [28]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [38]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # rarp_gateway_NAME_en10mb
	{
		name => 'gateway_name_fddi',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'FDDI',
		aliases => [
			'gateway eth-ipv4-noipv6.host123.libpcap.test',
			'gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		opt => '
			(000) ld       [9]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [7]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [3]
			(005) jeq      #0x400140e       jt 6	jf 21
			(006) ldh      [1]
			(007) jeq      #0xaa00          jt 8	jf 21
			(008) ldh      [19]
			(009) jeq      #0x800           jt 10	jf 14
			(010) ld       [33]
			(011) jeq      #0xa141e28       jt 21	jf 12
			(012) ld       [37]
			(013) jeq      #0xa141e28       jt 21	jf 20
			(014) jeq      #0x806           jt 16	jf 15
			(015) jeq      #0x8035          jt 16	jf 20
			(016) ld       [35]
			(017) jeq      #0xa141e28       jt 21	jf 18
			(018) ld       [45]
			(019) jeq      #0xa141e28       jt 21	jf 20
			(020) ret      #262144
			(021) ret      #0
			',
	}, # gateway_name_fddi
	{
		name => 'gateway_NAME_fddi',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'FDDI',
		aliases => [
			'gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		opt => '
			(000) ld       [9]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [7]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [3]
			(005) jeq      #0x400140e       jt 6	jf 21
			(006) ldh      [1]
			(007) jeq      #0xaa00          jt 8	jf 21
			(008) ldh      [19]
			(009) jeq      #0x800           jt 10	jf 14
			(010) ld       [33]
			(011) jeq      #0xa141e28       jt 21	jf 12
			(012) ld       [37]
			(013) jeq      #0xa141e28       jt 21	jf 20
			(014) jeq      #0x806           jt 16	jf 15
			(015) jeq      #0x8035          jt 16	jf 20
			(016) ld       [35]
			(017) jeq      #0xa141e28       jt 21	jf 18
			(018) ld       [45]
			(019) jeq      #0xa141e28       jt 21	jf 20
			(020) ret      #262144
			(021) ret      #0
			',
	}, # gateway_NAME_fddi
	{
		name => 'ip_gateway_name_fddi',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'FDDI',
		aliases => [
			'ip gateway eth-ipv4-noipv6.host123.libpcap.test',
			'ip gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ld       [9]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [7]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [3]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [1]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [19]
			(009) jeq      #0x800           jt 10	jf 14
			(010) ld       [33]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [37]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # ip_gateway_name_fddi
	{
		name => 'ip_gateway_NAME_fddi',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'FDDI',
		aliases => [
			'ip gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'ip gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ld       [9]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [7]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [3]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [1]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [19]
			(009) jeq      #0x800           jt 10	jf 14
			(010) ld       [33]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [37]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # ip_gateway_NAME_fddi
	{
		name => 'arp_gateway_name_fddi',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'FDDI',
		aliases => [
			'arp gateway eth-ipv4-noipv6.host123.libpcap.test',
			'arp gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ld       [9]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [7]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [3]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [1]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [19]
			(009) jeq      #0x806           jt 10	jf 14
			(010) ld       [35]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [45]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # arp_gateway_name_fddi
	{
		name => 'arp_gateway_NAME_fddi',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'FDDI',
		aliases => [
			'arp gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'arp gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ld       [9]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [7]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [3]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [1]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [19]
			(009) jeq      #0x806           jt 10	jf 14
			(010) ld       [35]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [45]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # arp_gateway_NAME_fddi
	{
		name => 'rarp_gateway_name_fddi',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'FDDI',
		aliases => [
			'rarp gateway eth-ipv4-noipv6.host123.libpcap.test',
			'rarp gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ld       [9]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [7]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [3]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [1]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [19]
			(009) jeq      #0x8035          jt 10	jf 14
			(010) ld       [35]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [45]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # rarp_gateway_name_fddi
	{
		name => 'rarp_gateway_NAME_fddi',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'FDDI',
		aliases => [
			'rarp gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'rarp gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ld       [9]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [7]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [3]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [1]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [19]
			(009) jeq      #0x8035          jt 10	jf 14
			(010) ld       [35]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [45]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # rarp_gateway_NAME_fddi
	{
		name => 'gateway_name_ieee802',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'IEEE802',
		aliases => [
			'gateway eth-ipv4-noipv6.host123.libpcap.test',
			'gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		opt => '
			(000) ld       [10]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [8]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [4]
			(005) jeq      #0x400140e       jt 6	jf 21
			(006) ldh      [2]
			(007) jeq      #0xaa00          jt 8	jf 21
			(008) ldh      [20]
			(009) jeq      #0x800           jt 10	jf 14
			(010) ld       [34]
			(011) jeq      #0xa141e28       jt 21	jf 12
			(012) ld       [38]
			(013) jeq      #0xa141e28       jt 21	jf 20
			(014) jeq      #0x806           jt 16	jf 15
			(015) jeq      #0x8035          jt 16	jf 20
			(016) ld       [36]
			(017) jeq      #0xa141e28       jt 21	jf 18
			(018) ld       [46]
			(019) jeq      #0xa141e28       jt 21	jf 20
			(020) ret      #262144
			(021) ret      #0
			',
	}, # gateway_name_ieee802
	{
		name => 'gateway_NAME_ieee802',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'IEEE802',
		aliases => [
			'gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		opt => '
			(000) ld       [10]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [8]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [4]
			(005) jeq      #0x400140e       jt 6	jf 21
			(006) ldh      [2]
			(007) jeq      #0xaa00          jt 8	jf 21
			(008) ldh      [20]
			(009) jeq      #0x800           jt 10	jf 14
			(010) ld       [34]
			(011) jeq      #0xa141e28       jt 21	jf 12
			(012) ld       [38]
			(013) jeq      #0xa141e28       jt 21	jf 20
			(014) jeq      #0x806           jt 16	jf 15
			(015) jeq      #0x8035          jt 16	jf 20
			(016) ld       [36]
			(017) jeq      #0xa141e28       jt 21	jf 18
			(018) ld       [46]
			(019) jeq      #0xa141e28       jt 21	jf 20
			(020) ret      #262144
			(021) ret      #0
			',
	}, # gateway_NAME_ieee802
	{
		name => 'ip_gateway_name_ieee802',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'IEEE802',
		aliases => [
			'ip gateway eth-ipv4-noipv6.host123.libpcap.test',
			'ip gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ld       [10]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [8]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [4]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [2]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [20]
			(009) jeq      #0x800           jt 10	jf 14
			(010) ld       [34]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [38]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # ip_gateway_name_ieee802
	{
		name => 'ip_gateway_NAME_ieee802',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'IEEE802',
		aliases => [
			'ip gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'ip gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ld       [10]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [8]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [4]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [2]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [20]
			(009) jeq      #0x800           jt 10	jf 14
			(010) ld       [34]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [38]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # ip_gateway_NAME_ieee802
	{
		name => 'arp_gateway_name_ieee802',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'IEEE802',
		aliases => [
			'arp gateway eth-ipv4-noipv6.host123.libpcap.test',
			'arp gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ld       [10]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [8]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [4]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [2]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [20]
			(009) jeq      #0x806           jt 10	jf 14
			(010) ld       [36]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [46]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # arp_gateway_name_ieee802
	{
		name => 'arp_gateway_NAME_ieee802',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'IEEE802',
		aliases => [
			'arp gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'arp gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ld       [10]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [8]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [4]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [2]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [20]
			(009) jeq      #0x806           jt 10	jf 14
			(010) ld       [36]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [46]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # arp_gateway_NAME_ieee802
	{
		name => 'rarp_gateway_name_ieee802',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'IEEE802',
		aliases => [
			'rarp gateway eth-ipv4-noipv6.host123.libpcap.test',
			'rarp gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ld       [10]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [8]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [4]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [2]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [20]
			(009) jeq      #0x8035          jt 10	jf 14
			(010) ld       [36]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [46]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # rarp_gateway_name_ieee802
	{
		name => 'rarp_gateway_NAME_ieee802',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'IEEE802',
		aliases => [
			'rarp gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'rarp gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ld       [10]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [8]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [4]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [2]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [20]
			(009) jeq      #0x8035          jt 10	jf 14
			(010) ld       [36]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [46]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # rarp_gateway_NAME_ieee802
	# TODO: Verify identity with DLT_PRISM_HEADER, DLT_IEEE802_11_RADIO_AVS,
	# DLT_IEEE802_11_RADIO and DLT_PPI in all DLT_IEEE802_11 gateway tests.
	{
		name => 'gateway_name_ieee802_11',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'IEEE802_11',
		aliases => [
			'gateway eth-ipv4-noipv6.host123.libpcap.test',
			'gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		opt => '
			(000) ldx      #0x0
			(001) txa
			(002) add      #24
			(003) st       M[0]
			(004) ldb      [x + 0]
			(005) jset     #0x8             jt 6	jf 11
			(006) jset     #0x4             jt 11	jf 7
			(007) jset     #0x80            jt 8	jf 11
			(008) ld       M[0]
			(009) add      #2
			(010) st       M[0]
			(011) ldb      [0]
			(012) jset     #0x4             jt 41	jf 13
			(013) ldb      [0]
			(014) jset     #0x8             jt 19	jf 15
			(015) ld       [12]
			(016) jeq      #0x400140e       jt 17	jf 19
			(017) ldh      [10]
			(018) jeq      #0xaa00          jt 63	jf 19
			(019) ldb      [0]
			(020) jset     #0x8             jt 21	jf 41
			(021) ldb      [1]
			(022) jset     #0x2             jt 27	jf 23
			(023) ld       [12]
			(024) jeq      #0x400140e       jt 25	jf 27
			(025) ldh      [10]
			(026) jeq      #0xaa00          jt 63	jf 27
			(027) ldb      [1]
			(028) jset     #0x2             jt 29	jf 41
			(029) ldb      [1]
			(030) jset     #0x1             jt 35	jf 31
			(031) ld       [18]
			(032) jeq      #0x400140e       jt 33	jf 35
			(033) ldh      [16]
			(034) jeq      #0xaa00          jt 63	jf 35
			(035) ldb      [1]
			(036) jset     #0x1             jt 37	jf 41
			(037) ld       [26]
			(038) jeq      #0x400140e       jt 39	jf 41
			(039) ldh      [24]
			(040) jeq      #0xaa00          jt 63	jf 41
			(041) ldb      [0]
			(042) jset     #0x4             jt 100	jf 43
			(043) ldb      [0]
			(044) jset     #0x8             jt 49	jf 45
			(045) ld       [6]
			(046) jeq      #0x400140e       jt 47	jf 49
			(047) ldh      [4]
			(048) jeq      #0xaa00          jt 63	jf 49
			(049) ldb      [0]
			(050) jset     #0x8             jt 51	jf 100
			(051) ldb      [1]
			(052) jset     #0x1             jt 57	jf 53
			(053) ld       [6]
			(054) jeq      #0x400140e       jt 55	jf 57
			(055) ldh      [4]
			(056) jeq      #0xaa00          jt 63	jf 57
			(057) ldb      [1]
			(058) jset     #0x1             jt 59	jf 100
			(059) ld       [18]
			(060) jeq      #0x400140e       jt 61	jf 100
			(061) ldh      [16]
			(062) jeq      #0xaa00          jt 63	jf 100
			(063) ldb      [0]
			(064) and      #0xc
			(065) jeq      #0x8             jt 66	jf 75
			(066) ldx      M[0]
			(067) ldh      [x + 6]
			(068) jeq      #0x800           jt 69	jf 75
			(069) ldx      M[0]
			(070) ld       [x + 20]
			(071) jeq      #0xa141e28       jt 100	jf 72
			(072) ldx      M[0]
			(073) ld       [x + 24]
			(074) jeq      #0xa141e28       jt 100	jf 75
			(075) ldb      [0]
			(076) and      #0xc
			(077) jeq      #0x8             jt 78	jf 87
			(078) ldx      M[0]
			(079) ldh      [x + 6]
			(080) jeq      #0x806           jt 81	jf 87
			(081) ldx      M[0]
			(082) ld       [x + 22]
			(083) jeq      #0xa141e28       jt 100	jf 84
			(084) ldx      M[0]
			(085) ld       [x + 32]
			(086) jeq      #0xa141e28       jt 100	jf 87
			(087) ldb      [0]
			(088) and      #0xc
			(089) jeq      #0x8             jt 90	jf 99
			(090) ldx      M[0]
			(091) ldh      [x + 6]
			(092) jeq      #0x8035          jt 93	jf 99
			(093) ldx      M[0]
			(094) ld       [x + 22]
			(095) jeq      #0xa141e28       jt 100	jf 96
			(096) ldx      M[0]
			(097) ld       [x + 32]
			(098) jeq      #0xa141e28       jt 100	jf 99
			(099) ret      #262144
			(100) ret      #0
			',
	}, # gateway_name_ieee802_11
	{
		name => 'gateway_NAME_ieee802_11',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'IEEE802_11',
		aliases => [
			'gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		opt => '
			(000) ldx      #0x0
			(001) txa
			(002) add      #24
			(003) st       M[0]
			(004) ldb      [x + 0]
			(005) jset     #0x8             jt 6	jf 11
			(006) jset     #0x4             jt 11	jf 7
			(007) jset     #0x80            jt 8	jf 11
			(008) ld       M[0]
			(009) add      #2
			(010) st       M[0]
			(011) ldb      [0]
			(012) jset     #0x4             jt 41	jf 13
			(013) ldb      [0]
			(014) jset     #0x8             jt 19	jf 15
			(015) ld       [12]
			(016) jeq      #0x400140e       jt 17	jf 19
			(017) ldh      [10]
			(018) jeq      #0xaa00          jt 63	jf 19
			(019) ldb      [0]
			(020) jset     #0x8             jt 21	jf 41
			(021) ldb      [1]
			(022) jset     #0x2             jt 27	jf 23
			(023) ld       [12]
			(024) jeq      #0x400140e       jt 25	jf 27
			(025) ldh      [10]
			(026) jeq      #0xaa00          jt 63	jf 27
			(027) ldb      [1]
			(028) jset     #0x2             jt 29	jf 41
			(029) ldb      [1]
			(030) jset     #0x1             jt 35	jf 31
			(031) ld       [18]
			(032) jeq      #0x400140e       jt 33	jf 35
			(033) ldh      [16]
			(034) jeq      #0xaa00          jt 63	jf 35
			(035) ldb      [1]
			(036) jset     #0x1             jt 37	jf 41
			(037) ld       [26]
			(038) jeq      #0x400140e       jt 39	jf 41
			(039) ldh      [24]
			(040) jeq      #0xaa00          jt 63	jf 41
			(041) ldb      [0]
			(042) jset     #0x4             jt 100	jf 43
			(043) ldb      [0]
			(044) jset     #0x8             jt 49	jf 45
			(045) ld       [6]
			(046) jeq      #0x400140e       jt 47	jf 49
			(047) ldh      [4]
			(048) jeq      #0xaa00          jt 63	jf 49
			(049) ldb      [0]
			(050) jset     #0x8             jt 51	jf 100
			(051) ldb      [1]
			(052) jset     #0x1             jt 57	jf 53
			(053) ld       [6]
			(054) jeq      #0x400140e       jt 55	jf 57
			(055) ldh      [4]
			(056) jeq      #0xaa00          jt 63	jf 57
			(057) ldb      [1]
			(058) jset     #0x1             jt 59	jf 100
			(059) ld       [18]
			(060) jeq      #0x400140e       jt 61	jf 100
			(061) ldh      [16]
			(062) jeq      #0xaa00          jt 63	jf 100
			(063) ldb      [0]
			(064) and      #0xc
			(065) jeq      #0x8             jt 66	jf 75
			(066) ldx      M[0]
			(067) ldh      [x + 6]
			(068) jeq      #0x800           jt 69	jf 75
			(069) ldx      M[0]
			(070) ld       [x + 20]
			(071) jeq      #0xa141e28       jt 100	jf 72
			(072) ldx      M[0]
			(073) ld       [x + 24]
			(074) jeq      #0xa141e28       jt 100	jf 75
			(075) ldb      [0]
			(076) and      #0xc
			(077) jeq      #0x8             jt 78	jf 87
			(078) ldx      M[0]
			(079) ldh      [x + 6]
			(080) jeq      #0x806           jt 81	jf 87
			(081) ldx      M[0]
			(082) ld       [x + 22]
			(083) jeq      #0xa141e28       jt 100	jf 84
			(084) ldx      M[0]
			(085) ld       [x + 32]
			(086) jeq      #0xa141e28       jt 100	jf 87
			(087) ldb      [0]
			(088) and      #0xc
			(089) jeq      #0x8             jt 90	jf 99
			(090) ldx      M[0]
			(091) ldh      [x + 6]
			(092) jeq      #0x8035          jt 93	jf 99
			(093) ldx      M[0]
			(094) ld       [x + 22]
			(095) jeq      #0xa141e28       jt 100	jf 96
			(096) ldx      M[0]
			(097) ld       [x + 32]
			(098) jeq      #0xa141e28       jt 100	jf 99
			(099) ret      #262144
			(100) ret      #0
			',
	}, # gateway_NAME_ieee802_11
	{
		name => 'ip_gateway_name_ieee802_11',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'IEEE802_11',
		aliases => [
			'ip gateway eth-ipv4-noipv6.host123.libpcap.test',
			'ip gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		opt => '
			(000) ldx      #0x0
			(001) txa
			(002) add      #24
			(003) st       M[0]
			(004) ldb      [x + 0]
			(005) jset     #0x8             jt 6	jf 11
			(006) jset     #0x4             jt 11	jf 7
			(007) jset     #0x80            jt 8	jf 11
			(008) ld       M[0]
			(009) add      #2
			(010) st       M[0]
			(011) ldb      [0]
			(012) jset     #0x4             jt 41	jf 13
			(013) ldb      [0]
			(014) jset     #0x8             jt 19	jf 15
			(015) ld       [12]
			(016) jeq      #0x400140e       jt 17	jf 19
			(017) ldh      [10]
			(018) jeq      #0xaa00          jt 63	jf 19
			(019) ldb      [0]
			(020) jset     #0x8             jt 21	jf 41
			(021) ldb      [1]
			(022) jset     #0x2             jt 27	jf 23
			(023) ld       [12]
			(024) jeq      #0x400140e       jt 25	jf 27
			(025) ldh      [10]
			(026) jeq      #0xaa00          jt 63	jf 27
			(027) ldb      [1]
			(028) jset     #0x2             jt 29	jf 41
			(029) ldb      [1]
			(030) jset     #0x1             jt 35	jf 31
			(031) ld       [18]
			(032) jeq      #0x400140e       jt 33	jf 35
			(033) ldh      [16]
			(034) jeq      #0xaa00          jt 63	jf 35
			(035) ldb      [1]
			(036) jset     #0x1             jt 37	jf 41
			(037) ld       [26]
			(038) jeq      #0x400140e       jt 39	jf 41
			(039) ldh      [24]
			(040) jeq      #0xaa00          jt 63	jf 41
			(041) ldb      [0]
			(042) jset     #0x4             jt 76	jf 43
			(043) ldb      [0]
			(044) jset     #0x8             jt 49	jf 45
			(045) ld       [6]
			(046) jeq      #0x400140e       jt 47	jf 49
			(047) ldh      [4]
			(048) jeq      #0xaa00          jt 63	jf 49
			(049) ldb      [0]
			(050) jset     #0x8             jt 51	jf 76
			(051) ldb      [1]
			(052) jset     #0x1             jt 57	jf 53
			(053) ld       [6]
			(054) jeq      #0x400140e       jt 55	jf 57
			(055) ldh      [4]
			(056) jeq      #0xaa00          jt 63	jf 57
			(057) ldb      [1]
			(058) jset     #0x1             jt 59	jf 76
			(059) ld       [18]
			(060) jeq      #0x400140e       jt 61	jf 76
			(061) ldh      [16]
			(062) jeq      #0xaa00          jt 63	jf 76
			(063) ldb      [0]
			(064) and      #0xc
			(065) jeq      #0x8             jt 66	jf 75
			(066) ldx      M[0]
			(067) ldh      [x + 6]
			(068) jeq      #0x800           jt 69	jf 75
			(069) ldx      M[0]
			(070) ld       [x + 20]
			(071) jeq      #0xa141e28       jt 76	jf 72
			(072) ldx      M[0]
			(073) ld       [x + 24]
			(074) jeq      #0xa141e28       jt 76	jf 75
			(075) ret      #262144
			(076) ret      #0
			',
	}, # ip_gateway_name_ieee802_11
	{
		name => 'ip_gateway_NAME_ieee802_11',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'IEEE802_11',
		aliases => [
			'ip gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'ip gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		opt => '
			(000) ldx      #0x0
			(001) txa
			(002) add      #24
			(003) st       M[0]
			(004) ldb      [x + 0]
			(005) jset     #0x8             jt 6	jf 11
			(006) jset     #0x4             jt 11	jf 7
			(007) jset     #0x80            jt 8	jf 11
			(008) ld       M[0]
			(009) add      #2
			(010) st       M[0]
			(011) ldb      [0]
			(012) jset     #0x4             jt 41	jf 13
			(013) ldb      [0]
			(014) jset     #0x8             jt 19	jf 15
			(015) ld       [12]
			(016) jeq      #0x400140e       jt 17	jf 19
			(017) ldh      [10]
			(018) jeq      #0xaa00          jt 63	jf 19
			(019) ldb      [0]
			(020) jset     #0x8             jt 21	jf 41
			(021) ldb      [1]
			(022) jset     #0x2             jt 27	jf 23
			(023) ld       [12]
			(024) jeq      #0x400140e       jt 25	jf 27
			(025) ldh      [10]
			(026) jeq      #0xaa00          jt 63	jf 27
			(027) ldb      [1]
			(028) jset     #0x2             jt 29	jf 41
			(029) ldb      [1]
			(030) jset     #0x1             jt 35	jf 31
			(031) ld       [18]
			(032) jeq      #0x400140e       jt 33	jf 35
			(033) ldh      [16]
			(034) jeq      #0xaa00          jt 63	jf 35
			(035) ldb      [1]
			(036) jset     #0x1             jt 37	jf 41
			(037) ld       [26]
			(038) jeq      #0x400140e       jt 39	jf 41
			(039) ldh      [24]
			(040) jeq      #0xaa00          jt 63	jf 41
			(041) ldb      [0]
			(042) jset     #0x4             jt 76	jf 43
			(043) ldb      [0]
			(044) jset     #0x8             jt 49	jf 45
			(045) ld       [6]
			(046) jeq      #0x400140e       jt 47	jf 49
			(047) ldh      [4]
			(048) jeq      #0xaa00          jt 63	jf 49
			(049) ldb      [0]
			(050) jset     #0x8             jt 51	jf 76
			(051) ldb      [1]
			(052) jset     #0x1             jt 57	jf 53
			(053) ld       [6]
			(054) jeq      #0x400140e       jt 55	jf 57
			(055) ldh      [4]
			(056) jeq      #0xaa00          jt 63	jf 57
			(057) ldb      [1]
			(058) jset     #0x1             jt 59	jf 76
			(059) ld       [18]
			(060) jeq      #0x400140e       jt 61	jf 76
			(061) ldh      [16]
			(062) jeq      #0xaa00          jt 63	jf 76
			(063) ldb      [0]
			(064) and      #0xc
			(065) jeq      #0x8             jt 66	jf 75
			(066) ldx      M[0]
			(067) ldh      [x + 6]
			(068) jeq      #0x800           jt 69	jf 75
			(069) ldx      M[0]
			(070) ld       [x + 20]
			(071) jeq      #0xa141e28       jt 76	jf 72
			(072) ldx      M[0]
			(073) ld       [x + 24]
			(074) jeq      #0xa141e28       jt 76	jf 75
			(075) ret      #262144
			(076) ret      #0
			',
	}, # ip_gateway_NAME_ieee802_11
	{
		name => 'arp_gateway_name_ieee802_11',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'IEEE802_11',
		aliases => [
			'arp gateway eth-ipv4-noipv6.host123.libpcap.test',
			'arp gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		opt => '
			(000) ldx      #0x0
			(001) txa
			(002) add      #24
			(003) st       M[0]
			(004) ldb      [x + 0]
			(005) jset     #0x8             jt 6	jf 11
			(006) jset     #0x4             jt 11	jf 7
			(007) jset     #0x80            jt 8	jf 11
			(008) ld       M[0]
			(009) add      #2
			(010) st       M[0]
			(011) ldb      [0]
			(012) jset     #0x4             jt 41	jf 13
			(013) ldb      [0]
			(014) jset     #0x8             jt 19	jf 15
			(015) ld       [12]
			(016) jeq      #0x400140e       jt 17	jf 19
			(017) ldh      [10]
			(018) jeq      #0xaa00          jt 63	jf 19
			(019) ldb      [0]
			(020) jset     #0x8             jt 21	jf 41
			(021) ldb      [1]
			(022) jset     #0x2             jt 27	jf 23
			(023) ld       [12]
			(024) jeq      #0x400140e       jt 25	jf 27
			(025) ldh      [10]
			(026) jeq      #0xaa00          jt 63	jf 27
			(027) ldb      [1]
			(028) jset     #0x2             jt 29	jf 41
			(029) ldb      [1]
			(030) jset     #0x1             jt 35	jf 31
			(031) ld       [18]
			(032) jeq      #0x400140e       jt 33	jf 35
			(033) ldh      [16]
			(034) jeq      #0xaa00          jt 63	jf 35
			(035) ldb      [1]
			(036) jset     #0x1             jt 37	jf 41
			(037) ld       [26]
			(038) jeq      #0x400140e       jt 39	jf 41
			(039) ldh      [24]
			(040) jeq      #0xaa00          jt 63	jf 41
			(041) ldb      [0]
			(042) jset     #0x4             jt 76	jf 43
			(043) ldb      [0]
			(044) jset     #0x8             jt 49	jf 45
			(045) ld       [6]
			(046) jeq      #0x400140e       jt 47	jf 49
			(047) ldh      [4]
			(048) jeq      #0xaa00          jt 63	jf 49
			(049) ldb      [0]
			(050) jset     #0x8             jt 51	jf 76
			(051) ldb      [1]
			(052) jset     #0x1             jt 57	jf 53
			(053) ld       [6]
			(054) jeq      #0x400140e       jt 55	jf 57
			(055) ldh      [4]
			(056) jeq      #0xaa00          jt 63	jf 57
			(057) ldb      [1]
			(058) jset     #0x1             jt 59	jf 76
			(059) ld       [18]
			(060) jeq      #0x400140e       jt 61	jf 76
			(061) ldh      [16]
			(062) jeq      #0xaa00          jt 63	jf 76
			(063) ldb      [0]
			(064) and      #0xc
			(065) jeq      #0x8             jt 66	jf 75
			(066) ldx      M[0]
			(067) ldh      [x + 6]
			(068) jeq      #0x806           jt 69	jf 75
			(069) ldx      M[0]
			(070) ld       [x + 22]
			(071) jeq      #0xa141e28       jt 76	jf 72
			(072) ldx      M[0]
			(073) ld       [x + 32]
			(074) jeq      #0xa141e28       jt 76	jf 75
			(075) ret      #262144
			(076) ret      #0
			',
	}, # arp_gateway_name_ieee802_11
	{
		name => 'arp_gateway_NAME_ieee802_11',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'IEEE802_11',
		aliases => [
			'arp gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'arp gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		opt => '
			(000) ldx      #0x0
			(001) txa
			(002) add      #24
			(003) st       M[0]
			(004) ldb      [x + 0]
			(005) jset     #0x8             jt 6	jf 11
			(006) jset     #0x4             jt 11	jf 7
			(007) jset     #0x80            jt 8	jf 11
			(008) ld       M[0]
			(009) add      #2
			(010) st       M[0]
			(011) ldb      [0]
			(012) jset     #0x4             jt 41	jf 13
			(013) ldb      [0]
			(014) jset     #0x8             jt 19	jf 15
			(015) ld       [12]
			(016) jeq      #0x400140e       jt 17	jf 19
			(017) ldh      [10]
			(018) jeq      #0xaa00          jt 63	jf 19
			(019) ldb      [0]
			(020) jset     #0x8             jt 21	jf 41
			(021) ldb      [1]
			(022) jset     #0x2             jt 27	jf 23
			(023) ld       [12]
			(024) jeq      #0x400140e       jt 25	jf 27
			(025) ldh      [10]
			(026) jeq      #0xaa00          jt 63	jf 27
			(027) ldb      [1]
			(028) jset     #0x2             jt 29	jf 41
			(029) ldb      [1]
			(030) jset     #0x1             jt 35	jf 31
			(031) ld       [18]
			(032) jeq      #0x400140e       jt 33	jf 35
			(033) ldh      [16]
			(034) jeq      #0xaa00          jt 63	jf 35
			(035) ldb      [1]
			(036) jset     #0x1             jt 37	jf 41
			(037) ld       [26]
			(038) jeq      #0x400140e       jt 39	jf 41
			(039) ldh      [24]
			(040) jeq      #0xaa00          jt 63	jf 41
			(041) ldb      [0]
			(042) jset     #0x4             jt 76	jf 43
			(043) ldb      [0]
			(044) jset     #0x8             jt 49	jf 45
			(045) ld       [6]
			(046) jeq      #0x400140e       jt 47	jf 49
			(047) ldh      [4]
			(048) jeq      #0xaa00          jt 63	jf 49
			(049) ldb      [0]
			(050) jset     #0x8             jt 51	jf 76
			(051) ldb      [1]
			(052) jset     #0x1             jt 57	jf 53
			(053) ld       [6]
			(054) jeq      #0x400140e       jt 55	jf 57
			(055) ldh      [4]
			(056) jeq      #0xaa00          jt 63	jf 57
			(057) ldb      [1]
			(058) jset     #0x1             jt 59	jf 76
			(059) ld       [18]
			(060) jeq      #0x400140e       jt 61	jf 76
			(061) ldh      [16]
			(062) jeq      #0xaa00          jt 63	jf 76
			(063) ldb      [0]
			(064) and      #0xc
			(065) jeq      #0x8             jt 66	jf 75
			(066) ldx      M[0]
			(067) ldh      [x + 6]
			(068) jeq      #0x806           jt 69	jf 75
			(069) ldx      M[0]
			(070) ld       [x + 22]
			(071) jeq      #0xa141e28       jt 76	jf 72
			(072) ldx      M[0]
			(073) ld       [x + 32]
			(074) jeq      #0xa141e28       jt 76	jf 75
			(075) ret      #262144
			(076) ret      #0
			',
	}, # arp_gateway_NAME_ieee802_11
	{
		name => 'rarp_gateway_name_ieee802_11',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'IEEE802_11',
		aliases => [
			'rarp gateway eth-ipv4-noipv6.host123.libpcap.test',
			'rarp gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		opt => '
			(000) ldx      #0x0
			(001) txa
			(002) add      #24
			(003) st       M[0]
			(004) ldb      [x + 0]
			(005) jset     #0x8             jt 6	jf 11
			(006) jset     #0x4             jt 11	jf 7
			(007) jset     #0x80            jt 8	jf 11
			(008) ld       M[0]
			(009) add      #2
			(010) st       M[0]
			(011) ldb      [0]
			(012) jset     #0x4             jt 41	jf 13
			(013) ldb      [0]
			(014) jset     #0x8             jt 19	jf 15
			(015) ld       [12]
			(016) jeq      #0x400140e       jt 17	jf 19
			(017) ldh      [10]
			(018) jeq      #0xaa00          jt 63	jf 19
			(019) ldb      [0]
			(020) jset     #0x8             jt 21	jf 41
			(021) ldb      [1]
			(022) jset     #0x2             jt 27	jf 23
			(023) ld       [12]
			(024) jeq      #0x400140e       jt 25	jf 27
			(025) ldh      [10]
			(026) jeq      #0xaa00          jt 63	jf 27
			(027) ldb      [1]
			(028) jset     #0x2             jt 29	jf 41
			(029) ldb      [1]
			(030) jset     #0x1             jt 35	jf 31
			(031) ld       [18]
			(032) jeq      #0x400140e       jt 33	jf 35
			(033) ldh      [16]
			(034) jeq      #0xaa00          jt 63	jf 35
			(035) ldb      [1]
			(036) jset     #0x1             jt 37	jf 41
			(037) ld       [26]
			(038) jeq      #0x400140e       jt 39	jf 41
			(039) ldh      [24]
			(040) jeq      #0xaa00          jt 63	jf 41
			(041) ldb      [0]
			(042) jset     #0x4             jt 76	jf 43
			(043) ldb      [0]
			(044) jset     #0x8             jt 49	jf 45
			(045) ld       [6]
			(046) jeq      #0x400140e       jt 47	jf 49
			(047) ldh      [4]
			(048) jeq      #0xaa00          jt 63	jf 49
			(049) ldb      [0]
			(050) jset     #0x8             jt 51	jf 76
			(051) ldb      [1]
			(052) jset     #0x1             jt 57	jf 53
			(053) ld       [6]
			(054) jeq      #0x400140e       jt 55	jf 57
			(055) ldh      [4]
			(056) jeq      #0xaa00          jt 63	jf 57
			(057) ldb      [1]
			(058) jset     #0x1             jt 59	jf 76
			(059) ld       [18]
			(060) jeq      #0x400140e       jt 61	jf 76
			(061) ldh      [16]
			(062) jeq      #0xaa00          jt 63	jf 76
			(063) ldb      [0]
			(064) and      #0xc
			(065) jeq      #0x8             jt 66	jf 75
			(066) ldx      M[0]
			(067) ldh      [x + 6]
			(068) jeq      #0x8035          jt 69	jf 75
			(069) ldx      M[0]
			(070) ld       [x + 22]
			(071) jeq      #0xa141e28       jt 76	jf 72
			(072) ldx      M[0]
			(073) ld       [x + 32]
			(074) jeq      #0xa141e28       jt 76	jf 75
			(075) ret      #262144
			(076) ret      #0
			',
	}, # rarp_gateway_name_ieee802_11
	{
		name => 'rarp_gateway_NAME_ieee802_11',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'IEEE802_11',
		aliases => [
			'rarp gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'rarp gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		opt => '
			(000) ldx      #0x0
			(001) txa
			(002) add      #24
			(003) st       M[0]
			(004) ldb      [x + 0]
			(005) jset     #0x8             jt 6	jf 11
			(006) jset     #0x4             jt 11	jf 7
			(007) jset     #0x80            jt 8	jf 11
			(008) ld       M[0]
			(009) add      #2
			(010) st       M[0]
			(011) ldb      [0]
			(012) jset     #0x4             jt 41	jf 13
			(013) ldb      [0]
			(014) jset     #0x8             jt 19	jf 15
			(015) ld       [12]
			(016) jeq      #0x400140e       jt 17	jf 19
			(017) ldh      [10]
			(018) jeq      #0xaa00          jt 63	jf 19
			(019) ldb      [0]
			(020) jset     #0x8             jt 21	jf 41
			(021) ldb      [1]
			(022) jset     #0x2             jt 27	jf 23
			(023) ld       [12]
			(024) jeq      #0x400140e       jt 25	jf 27
			(025) ldh      [10]
			(026) jeq      #0xaa00          jt 63	jf 27
			(027) ldb      [1]
			(028) jset     #0x2             jt 29	jf 41
			(029) ldb      [1]
			(030) jset     #0x1             jt 35	jf 31
			(031) ld       [18]
			(032) jeq      #0x400140e       jt 33	jf 35
			(033) ldh      [16]
			(034) jeq      #0xaa00          jt 63	jf 35
			(035) ldb      [1]
			(036) jset     #0x1             jt 37	jf 41
			(037) ld       [26]
			(038) jeq      #0x400140e       jt 39	jf 41
			(039) ldh      [24]
			(040) jeq      #0xaa00          jt 63	jf 41
			(041) ldb      [0]
			(042) jset     #0x4             jt 76	jf 43
			(043) ldb      [0]
			(044) jset     #0x8             jt 49	jf 45
			(045) ld       [6]
			(046) jeq      #0x400140e       jt 47	jf 49
			(047) ldh      [4]
			(048) jeq      #0xaa00          jt 63	jf 49
			(049) ldb      [0]
			(050) jset     #0x8             jt 51	jf 76
			(051) ldb      [1]
			(052) jset     #0x1             jt 57	jf 53
			(053) ld       [6]
			(054) jeq      #0x400140e       jt 55	jf 57
			(055) ldh      [4]
			(056) jeq      #0xaa00          jt 63	jf 57
			(057) ldb      [1]
			(058) jset     #0x1             jt 59	jf 76
			(059) ld       [18]
			(060) jeq      #0x400140e       jt 61	jf 76
			(061) ldh      [16]
			(062) jeq      #0xaa00          jt 63	jf 76
			(063) ldb      [0]
			(064) and      #0xc
			(065) jeq      #0x8             jt 66	jf 75
			(066) ldx      M[0]
			(067) ldh      [x + 6]
			(068) jeq      #0x8035          jt 69	jf 75
			(069) ldx      M[0]
			(070) ld       [x + 22]
			(071) jeq      #0xa141e28       jt 76	jf 72
			(072) ldx      M[0]
			(073) ld       [x + 32]
			(074) jeq      #0xa141e28       jt 76	jf 75
			(075) ret      #262144
			(076) ret      #0
			',
	}, # rarp_gateway_NAME_ieee802_11
	{
		name => 'gateway_name_ip_over_fc',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'IP_OVER_FC',
		aliases => [
			'gateway eth-ipv4-noipv6.host123.libpcap.test',
			'gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		opt => '
			(000) ld       [12]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [10]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [4]
			(005) jeq      #0x400140e       jt 6	jf 21
			(006) ldh      [2]
			(007) jeq      #0xaa00          jt 8	jf 21
			(008) ldh      [22]
			(009) jeq      #0x800           jt 10	jf 14
			(010) ld       [36]
			(011) jeq      #0xa141e28       jt 21	jf 12
			(012) ld       [40]
			(013) jeq      #0xa141e28       jt 21	jf 20
			(014) jeq      #0x806           jt 16	jf 15
			(015) jeq      #0x8035          jt 16	jf 20
			(016) ld       [38]
			(017) jeq      #0xa141e28       jt 21	jf 18
			(018) ld       [48]
			(019) jeq      #0xa141e28       jt 21	jf 20
			(020) ret      #262144
			(021) ret      #0
			',
	}, # gateway_name_ip_over_fc
	{
		name => 'gateway_NAME_ip_over_fc',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'IP_OVER_FC',
		aliases => [
			'gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		opt => '
			(000) ld       [12]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [10]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [4]
			(005) jeq      #0x400140e       jt 6	jf 21
			(006) ldh      [2]
			(007) jeq      #0xaa00          jt 8	jf 21
			(008) ldh      [22]
			(009) jeq      #0x800           jt 10	jf 14
			(010) ld       [36]
			(011) jeq      #0xa141e28       jt 21	jf 12
			(012) ld       [40]
			(013) jeq      #0xa141e28       jt 21	jf 20
			(014) jeq      #0x806           jt 16	jf 15
			(015) jeq      #0x8035          jt 16	jf 20
			(016) ld       [38]
			(017) jeq      #0xa141e28       jt 21	jf 18
			(018) ld       [48]
			(019) jeq      #0xa141e28       jt 21	jf 20
			(020) ret      #262144
			(021) ret      #0
			',
	}, # gateway_NAME_ip_over_fc
	{
		name => 'ip_gateway_name_ip_over_fc',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'IP_OVER_FC',
		aliases => [
			'ip gateway eth-ipv4-noipv6.host123.libpcap.test',
			'ip gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ld       [12]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [10]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [4]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [2]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [22]
			(009) jeq      #0x800           jt 10	jf 14
			(010) ld       [36]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [40]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # ip_gateway_name_ip_over_fc
	{
		name => 'ip_gateway_NAME_ip_over_fc',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'IP_OVER_FC',
		aliases => [
			'ip gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'ip gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ld       [12]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [10]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [4]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [2]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [22]
			(009) jeq      #0x800           jt 10	jf 14
			(010) ld       [36]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [40]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # ip_gateway_NAME_ip_over_fc
	{
		name => 'arp_gateway_name_ip_over_fc',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'IP_OVER_FC',
		aliases => [
			'arp gateway eth-ipv4-noipv6.host123.libpcap.test',
			'arp gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ld       [12]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [10]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [4]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [2]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [22]
			(009) jeq      #0x806           jt 10	jf 14
			(010) ld       [38]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [48]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # arp_gateway_name_ip_over_fc
	{
		name => 'arp_gateway_NAME_ip_over_fc',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'IP_OVER_FC',
		aliases => [
			'arp gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'arp gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ld       [12]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [10]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [4]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [2]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [22]
			(009) jeq      #0x806           jt 10	jf 14
			(010) ld       [38]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [48]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # arp_gateway_NAME_ip_over_fc
	{
		name => 'rarp_gateway_name_ip_over_fc',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'IP_OVER_FC',
		aliases => [
			'rarp gateway eth-ipv4-noipv6.host123.libpcap.test',
			'rarp gateway eth-ipv4-ipv6.host123.libpcap.test',
		],
		unopt => '
			(000) ld       [12]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [10]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [4]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [2]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [22]
			(009) jeq      #0x8035          jt 10	jf 14
			(010) ld       [38]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [48]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # rarp_gateway_name_ip_over_fc
	{
		name => 'rarp_gateway_NAME_ip_over_fc',
		skip => skip_no_ethers_casecmp() ||
			skip_no_hosts_casecmp(),
		DLT => 'IP_OVER_FC',
		aliases => [
			'rarp gateway ETH-IPV4-NOIPV6.HOST123.LIBPCAP.TEST',
			'rarp gateway ETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		unopt => '
			(000) ld       [12]
			(001) jeq      #0x400140e       jt 2	jf 4
			(002) ldh      [10]
			(003) jeq      #0xaa00          jt 8	jf 4
			(004) ld       [4]
			(005) jeq      #0x400140e       jt 6	jf 15
			(006) ldh      [2]
			(007) jeq      #0xaa00          jt 8	jf 15
			(008) ldh      [22]
			(009) jeq      #0x8035          jt 10	jf 14
			(010) ld       [38]
			(011) jeq      #0xa141e28       jt 15	jf 12
			(012) ld       [48]
			(013) jeq      #0xa141e28       jt 15	jf 14
			(014) ret      #262144
			(015) ret      #0
			',
	}, # rarp_gateway_NAME_ip_over_fc
	{
		name => 'carp',
		DLT => 'EN10MB',
		aliases => [
			'carp',
			'vrrp',
			'ip proto 112',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x800           jt 2	jf 5
			(002) ldb      [23]
			(003) jeq      #0x70            jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # carp
	{
		name => 'icmp',
		DLT => 'EN10MB',
		aliases => [
			'icmp',
			'ip proto 1',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x800           jt 2	jf 5
			(002) ldb      [23]
			(003) jeq      #0x1             jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # icmp
	{
		name => 'igmp',
		DLT => 'EN10MB',
		aliases => [
			'igmp',
			'ip proto 2',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x800           jt 2	jf 5
			(002) ldb      [23]
			(003) jeq      #0x2             jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # igmp
	# "igrp" uses IPPROTO_IGRP, which FreeBSD and its derivatives define, but
	# other supported OSes don't (thus libpcap uses its own value, which is
	# different from FreeBSD).  Test each case separately.
	# this discrepancy.
	{
		name => 'igrp_9',
		skip => skip_os ('freebsd') || skip_os ('darwin') || skip_os ('dragonfly'),
		DLT => 'EN10MB',
		aliases => [
			'igrp',
			'ip proto 9',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x800           jt 2	jf 5
			(002) ldb      [23]
			(003) jeq      #0x9             jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # igrp_9
	{
		name => 'igrp_88',
		skip => skip_os_not ('freebsd') && skip_os_not ('darwin') && skip_os_not ('dragonfly'),
		DLT => 'EN10MB',
		aliases => [
			'igrp',
			'ip proto 88',
		],
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x800           jt 2	jf 5
			(002) ldb      [23]
			(003) jeq      #0x58            jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # igrp_88
	{
		name => 'icmp6',
		DLT => 'EN10MB',
		aliases => [
			'icmp6',
			'ip6 proto 58',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 8
			(002) ldb      [20]
			(003) jeq      #0x3a            jt 7	jf 4
			(004) jeq      #0x2c            jt 5	jf 8
			(005) ldb      [54]
			(006) jeq      #0x3a            jt 7	jf 8
			(007) ret      #262144
			(008) ret      #0
			',
	}, # icmp6
	{
		name => 'ah',
		DLT => 'RAW',
		aliases => [
			'ah',
			'proto 51', # not "proto \ah"
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 5
			(003) ldb      [9]
			(004) jeq      #0x33            jt 13	jf 14
			(005) ldb      [0]
			(006) and      #0xf0
			(007) jeq      #0x60            jt 8	jf 14
			(008) ldb      [6]
			(009) jeq      #0x33            jt 13	jf 10
			(010) jeq      #0x2c            jt 11	jf 14
			(011) ldb      [40]
			(012) jeq      #0x33            jt 13	jf 14
			(013) ret      #262144
			(014) ret      #0
			',
	}, # ah
	{
		name => 'esp',
		DLT => 'RAW',
		aliases => [
			'esp',
			'proto 50', # not "proto \esp"
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 5
			(003) ldb      [9]
			(004) jeq      #0x32            jt 13	jf 14
			(005) ldb      [0]
			(006) and      #0xf0
			(007) jeq      #0x60            jt 8	jf 14
			(008) ldb      [6]
			(009) jeq      #0x32            jt 13	jf 10
			(010) jeq      #0x2c            jt 11	jf 14
			(011) ldb      [40]
			(012) jeq      #0x32            jt 13	jf 14
			(013) ret      #262144
			(014) ret      #0
			',
	}, # esp
	{
		name => 'pim',
		DLT => 'RAW',
		aliases => [
			'pim',
			'proto 103', # not "proto \pim"
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 5
			(003) ldb      [9]
			(004) jeq      #0x67            jt 13	jf 14
			(005) ldb      [0]
			(006) and      #0xf0
			(007) jeq      #0x60            jt 8	jf 14
			(008) ldb      [6]
			(009) jeq      #0x67            jt 13	jf 10
			(010) jeq      #0x2c            jt 11	jf 14
			(011) ldb      [40]
			(012) jeq      #0x67            jt 13	jf 14
			(013) ret      #262144
			(014) ret      #0
			',
	}, # pim
	{
		name => 'sctp',
		DLT => 'RAW',
		aliases => [
			'sctp',
			'proto 132', # not "proto \sctp"
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 5
			(003) ldb      [9]
			(004) jeq      #0x84            jt 13	jf 14
			(005) ldb      [0]
			(006) and      #0xf0
			(007) jeq      #0x60            jt 8	jf 14
			(008) ldb      [6]
			(009) jeq      #0x84            jt 13	jf 10
			(010) jeq      #0x2c            jt 11	jf 14
			(011) ldb      [40]
			(012) jeq      #0x84            jt 13	jf 14
			(013) ret      #262144
			(014) ret      #0
			',
	}, # sctp
	{
		name => 'tcp',
		DLT => 'RAW',
		aliases => [
			'tcp',
			'proto 6', # not "proto \tcp"
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 5
			(003) ldb      [9]
			(004) jeq      #0x6             jt 13	jf 14
			(005) ldb      [0]
			(006) and      #0xf0
			(007) jeq      #0x60            jt 8	jf 14
			(008) ldb      [6]
			(009) jeq      #0x6             jt 13	jf 10
			(010) jeq      #0x2c            jt 11	jf 14
			(011) ldb      [40]
			(012) jeq      #0x6             jt 13	jf 14
			(013) ret      #262144
			(014) ret      #0
			',
	}, # tcp
	{
		name => 'udp',
		DLT => 'RAW',
		aliases => [
			'udp',
			'proto 17', # not "proto \udp"
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x40            jt 3	jf 5
			(003) ldb      [9]
			(004) jeq      #0x11            jt 13	jf 14
			(005) ldb      [0]
			(006) and      #0xf0
			(007) jeq      #0x60            jt 8	jf 14
			(008) ldb      [6]
			(009) jeq      #0x11            jt 13	jf 10
			(010) jeq      #0x2c            jt 11	jf 14
			(011) ldb      [40]
			(012) jeq      #0x11            jt 13	jf 14
			(013) ret      #262144
			(014) ret      #0
			',
	}, # udp

	{
		name => 'ip6_host_addr',
		DLT => 'RAW',
		aliases => [
			'ip6 host ::1',
			'ip6 src or dst host ::1',
			'ip6 src or dst ::1',
			'host ::1',
			'src or dst host ::1',
			'src or dst ::1',
			'ip6 net ::1/128',
			'ip6 src or dst net ::1/128',
			'net ::1/128',
			'src or dst net ::1/128',
			# "...so in this primitive IPv6 "network" matches are really always
			# host matches"
			'ip6 net ::1',
			'ip6 src or dst net ::1',
			'net ::1',
			'src or dst net ::1',
			# This syntax is not documented and seems to be an unintended edge case
			# in the invocation of gen_mcode6() from the grammar.  It may become
			# invalid syntax later, in which case the aliases below will need to be
			# converted to reject tests.
			'ip6 host ::1/128',
			'ip6 src or dst host ::1/128',
			'ip6 src or dst ::1/128',
			'host ::1/128',
			'src or dst host ::1/128',
			'src or dst ::1/128',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x60            jt 3	jf 20
			(003) ld       [8]
			(004) jeq      #0x0             jt 5	jf 11
			(005) ld       [12]
			(006) jeq      #0x0             jt 7	jf 11
			(007) ld       [16]
			(008) jeq      #0x0             jt 9	jf 11
			(009) ld       [20]
			(010) jeq      #0x1             jt 19	jf 11
			(011) ld       [24]
			(012) jeq      #0x0             jt 13	jf 20
			(013) ld       [28]
			(014) jeq      #0x0             jt 15	jf 20
			(015) ld       [32]
			(016) jeq      #0x0             jt 17	jf 20
			(017) ld       [36]
			(018) jeq      #0x1             jt 19	jf 20
			(019) ret      #262144
			(020) ret      #0
			',
	}, # ip6_host_addr
	{
		name => 'ip6_host_name',
		skip => skip_no_hosts(),
		DLT => 'RAW',
		aliases => [
			'ip6 host noeth-noipv4-ipv6.host123.libpcap.test',
			'ip6 host noeth-ipv4-ipv6.host123.libpcap.test',
			'ip6 src or dst noeth-noipv4-ipv6.host123.libpcap.test',
			'ip6 src or dst noeth-ipv4-ipv6.host123.libpcap.test',
			'ip6 src or dst host noeth-noipv4-ipv6.host123.libpcap.test',
			'ip6 src or dst host noeth-ipv4-ipv6.host123.libpcap.test',
			'host noeth-noipv4-ipv6.host123.libpcap.test',
			'src or dst host noeth-noipv4-ipv6.host123.libpcap.test',
			'src or dst noeth-noipv4-ipv6.host123.libpcap.test',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x60            jt 3	jf 20
			(003) ld       [8]
			(004) jeq      #0xfd00a1b2      jt 5	jf 11
			(005) ld       [12]
			(006) jeq      #0xc3d40000      jt 7	jf 11
			(007) ld       [16]
			(008) jeq      #0x10203040      jt 9	jf 11
			(009) ld       [20]
			(010) jeq      #0x50607080      jt 19	jf 11
			(011) ld       [24]
			(012) jeq      #0xfd00a1b2      jt 13	jf 20
			(013) ld       [28]
			(014) jeq      #0xc3d40000      jt 15	jf 20
			(015) ld       [32]
			(016) jeq      #0x10203040      jt 17	jf 20
			(017) ld       [36]
			(018) jeq      #0x50607080      jt 19	jf 20
			(019) ret      #262144
			(020) ret      #0
			',
	}, # ip6_host_name
	{
		name => 'ip6_host_NAME',
		skip => skip_no_hosts_casecmp(),
		DLT => 'RAW',
		aliases => [
			'ip6 host NOETH-NOIPV4-IPV6.HOST123.LIBPCAP.TEST',
			'ip6 host NOETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
			'ip6 src or dst NOETH-NOIPV4-IPV6.HOST123.LIBPCAP.TEST',
			'ip6 src or dst NOETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
			'ip6 src or dst host NOETH-NOIPV4-IPV6.HOST123.LIBPCAP.TEST',
			'ip6 src or dst host NOETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
			'host NOETH-NOIPV4-IPV6.HOST123.LIBPCAP.TEST',
			'src or dst host NOETH-NOIPV4-IPV6.HOST123.LIBPCAP.TEST',
			'src or dst NOETH-NOIPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x60            jt 3	jf 20
			(003) ld       [8]
			(004) jeq      #0xfd00a1b2      jt 5	jf 11
			(005) ld       [12]
			(006) jeq      #0xc3d40000      jt 7	jf 11
			(007) ld       [16]
			(008) jeq      #0x10203040      jt 9	jf 11
			(009) ld       [20]
			(010) jeq      #0x50607080      jt 19	jf 11
			(011) ld       [24]
			(012) jeq      #0xfd00a1b2      jt 13	jf 20
			(013) ld       [28]
			(014) jeq      #0xc3d40000      jt 15	jf 20
			(015) ld       [32]
			(016) jeq      #0x10203040      jt 17	jf 20
			(017) ld       [36]
			(018) jeq      #0x50607080      jt 19	jf 20
			(019) ret      #262144
			(020) ret      #0
			',
	}, # ip6_host_NAME
	{
		name => 'ip6_src_host_addr',
		DLT => 'RAW',
		aliases => [
			'ip6 src host fe80::1122:33ff:fe44:5566',
			'ip6 src fe80::1122:33ff:fe44:5566',
			'src host fe80::1122:33ff:fe44:5566',
			'src fe80::1122:33ff:fe44:5566',
			# same as above
			'ip6 src host fe80::1122:33ff:fe44:5566/128',
			'ip6 src fe80::1122:33ff:fe44:5566/128',
			'src host fe80::1122:33ff:fe44:5566/128',
			'src fe80::1122:33ff:fe44:5566/128',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x60            jt 3	jf 12
			(003) ld       [8]
			(004) jeq      #0xfe800000      jt 5	jf 12
			(005) ld       [12]
			(006) jeq      #0x0             jt 7	jf 12
			(007) ld       [16]
			(008) jeq      #0x112233ff      jt 9	jf 12
			(009) ld       [20]
			(010) jeq      #0xfe445566      jt 11	jf 12
			(011) ret      #262144
			(012) ret      #0
			',
	}, # ip6_src_host_addr
	{
		name => 'ip6_src_host_name',
		skip => skip_no_hosts(),
		DLT => 'RAW',
		aliases => [
			'ip6 src host noeth-noipv4-ipv6.host123.libpcap.test',
			'ip6 src host noeth-ipv4-ipv6.host123.libpcap.test',
			'ip6 src noeth-noipv4-ipv6.host123.libpcap.test',
			'ip6 src noeth-ipv4-ipv6.host123.libpcap.test',
			'src host noeth-noipv4-ipv6.host123.libpcap.test',
			'src noeth-noipv4-ipv6.host123.libpcap.test',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x60            jt 3	jf 12
			(003) ld       [8]
			(004) jeq      #0xfd00a1b2      jt 5	jf 12
			(005) ld       [12]
			(006) jeq      #0xc3d40000      jt 7	jf 12
			(007) ld       [16]
			(008) jeq      #0x10203040      jt 9	jf 12
			(009) ld       [20]
			(010) jeq      #0x50607080      jt 11	jf 12
			(011) ret      #262144
			(012) ret      #0
			',
	}, # ip6_src_host_name
	{
		name => 'ip6_src_host_NAME',
		skip => skip_no_hosts_casecmp(),
		DLT => 'RAW',
		aliases => [
			'ip6 src host NOETH-NOIPV4-IPV6.HOST123.LIBPCAP.TEST',
			'ip6 src host NOETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
			'ip6 src NOETH-NOIPV4-IPV6.HOST123.LIBPCAP.TEST',
			'ip6 src NOETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
			'src host NOETH-NOIPV4-IPV6.HOST123.LIBPCAP.TEST',
			'src NOETH-NOIPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x60            jt 3	jf 12
			(003) ld       [8]
			(004) jeq      #0xfd00a1b2      jt 5	jf 12
			(005) ld       [12]
			(006) jeq      #0xc3d40000      jt 7	jf 12
			(007) ld       [16]
			(008) jeq      #0x10203040      jt 9	jf 12
			(009) ld       [20]
			(010) jeq      #0x50607080      jt 11	jf 12
			(011) ret      #262144
			(012) ret      #0
			',
	}, # ip6_src_host_NAME
	{
		name => 'ip6_dst_host_addr',
		DLT => 'RAW',
		aliases => [
			'ip6 dst host fe80::7788:99ff:feaa:bbcc',
			'ip6 dst fe80::7788:99ff:feaa:bbcc',
			'dst host fe80::7788:99ff:feaa:bbcc',
			'dst fe80::7788:99ff:feaa:bbcc',
			# same as above
			'ip6 dst host fe80::7788:99ff:feaa:bbcc/128',
			'ip6 dst fe80::7788:99ff:feaa:bbcc/128',
			'dst host fe80::7788:99ff:feaa:bbcc/128',
			'dst fe80::7788:99ff:feaa:bbcc/128',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x60            jt 3	jf 12
			(003) ld       [24]
			(004) jeq      #0xfe800000      jt 5	jf 12
			(005) ld       [28]
			(006) jeq      #0x0             jt 7	jf 12
			(007) ld       [32]
			(008) jeq      #0x778899ff      jt 9	jf 12
			(009) ld       [36]
			(010) jeq      #0xfeaabbcc      jt 11	jf 12
			(011) ret      #262144
			(012) ret      #0
			',
	}, # ip6_dst_host_addr
	{
		name => 'ip6_dst_host_name',
		skip => skip_no_hosts(),
		DLT => 'RAW',
		aliases => [
			'ip6 dst host noeth-noipv4-ipv6.host123.libpcap.test',
			'ip6 dst host noeth-ipv4-ipv6.host123.libpcap.test',
			'ip6 dst noeth-noipv4-ipv6.host123.libpcap.test',
			'ip6 dst noeth-ipv4-ipv6.host123.libpcap.test',
			'dst host noeth-noipv4-ipv6.host123.libpcap.test',
			'dst noeth-noipv4-ipv6.host123.libpcap.test',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x60            jt 3	jf 12
			(003) ld       [24]
			(004) jeq      #0xfd00a1b2      jt 5	jf 12
			(005) ld       [28]
			(006) jeq      #0xc3d40000      jt 7	jf 12
			(007) ld       [32]
			(008) jeq      #0x10203040      jt 9	jf 12
			(009) ld       [36]
			(010) jeq      #0x50607080      jt 11	jf 12
			(011) ret      #262144
			(012) ret      #0
			',
	}, # ip6_dst_host_name
	{
		name => 'ip6_dst_host_NAME',
		skip => skip_no_hosts_casecmp(),
		DLT => 'RAW',
		aliases => [
			'ip6 dst host NOETH-NOIPV4-IPV6.HOST123.LIBPCAP.TEST',
			'ip6 dst host NOETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
			'ip6 dst NOETH-NOIPV4-IPV6.HOST123.LIBPCAP.TEST',
			'ip6 dst NOETH-IPV4-IPV6.HOST123.LIBPCAP.TEST',
			'dst host NOETH-NOIPV4-IPV6.HOST123.LIBPCAP.TEST',
			'dst NOETH-NOIPV4-IPV6.HOST123.LIBPCAP.TEST',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x60            jt 3	jf 12
			(003) ld       [24]
			(004) jeq      #0xfd00a1b2      jt 5	jf 12
			(005) ld       [28]
			(006) jeq      #0xc3d40000      jt 7	jf 12
			(007) ld       [32]
			(008) jeq      #0x10203040      jt 9	jf 12
			(009) ld       [36]
			(010) jeq      #0x50607080      jt 11	jf 12
			(011) ret      #262144
			(012) ret      #0
			',
	}, # ip6_dst_host_NAME
	{
		name => 'ip6_net',
		DLT => 'RAW',
		aliases => [
			'ip6 net fe80::/10',
			'net fe80::/10',
			'src or dst net fe80::/10',
			'ip6 src or dst net fe80::/10',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x60            jt 3	jf 10
			(003) ld       [8]
			(004) and      #0xffc00000
			(005) jeq      #0xfe800000      jt 9	jf 6
			(006) ld       [24]
			(007) and      #0xffc00000
			(008) jeq      #0xfe800000      jt 9	jf 10
			(009) ret      #262144
			(010) ret      #0
			',
	}, # ip6_net
	{
		name => 'ip6_src_net',
		DLT => 'RAW',
		aliases => [
			'ip6 src net 2000::/3',
			'src net 2000::/3',
		],
		opt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x60            jt 3	jf 7
			(003) ld       [8]
			(004) and      #0xe0000000
			(005) jeq      #0x20000000      jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # ip6_src_net
	{
		name => 'ip6_dst_net_0',
		DLT => 'RAW',
		aliases => ['ip6 dst net ::/0'],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x60            jt 3	jf 4
			(003) ret      #262144
			(004) ret      #0
			',
	}, # ip6_dst_net_0
	{
		name => 'ip6_dst_net_8',
		DLT => 'RAW',
		aliases => ['ip6 dst net ff00::/8'],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x60            jt 3	jf 7
			(003) ld       [24]
			(004) and      #0xff000000
			(005) jeq      #0xff000000      jt 6	jf 7
			(006) ret      #262144
			(007) ret      #0
			',
	}, # ip6_dst_net_8
	{
		name => 'ip6_dst_net_40',
		DLT => 'RAW',
		aliases => ['ip6 dst net ff11:2233:4400::/40'],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x60            jt 3	jf 9
			(003) ld       [24]
			(004) jeq      #0xff112233      jt 5	jf 9
			(005) ld       [28]
			(006) and      #0xff000000
			(007) jeq      #0x44000000      jt 8	jf 9
			(008) ret      #262144
			(009) ret      #0
			',
	}, # ip6_dst_net_40
	{
		name => 'ip6_dst_net_80',
		DLT => 'RAW',
		aliases => ['ip6 dst net ff11:2233:4455:6677:8899::/80'],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x60            jt 3	jf 11
			(003) ld       [24]
			(004) jeq      #0xff112233      jt 5	jf 11
			(005) ld       [28]
			(006) jeq      #0x44556677      jt 7	jf 11
			(007) ld       [32]
			(008) and      #0xffff0000
			(009) jeq      #0x88990000      jt 10	jf 11
			(010) ret      #262144
			(011) ret      #0
			',
	}, # ip6_dst_net_80
	{
		name => 'ip6_dst_net_120',
		DLT => 'RAW',
		aliases => ['ip6 dst net ff11:2233:4455:6677:8899:aabb:ccdd:ee00/120'],
		unopt => '
			(000) ldb      [0]
			(001) and      #0xf0
			(002) jeq      #0x60            jt 3	jf 13
			(003) ld       [24]
			(004) jeq      #0xff112233      jt 5	jf 13
			(005) ld       [28]
			(006) jeq      #0x44556677      jt 7	jf 13
			(007) ld       [32]
			(008) jeq      #0x8899aabb      jt 9	jf 13
			(009) ld       [36]
			(010) and      #0xffffff00
			(011) jeq      #0xccddee00      jt 12	jf 13
			(012) ret      #262144
			(013) ret      #0
			',
	}, # ip6_dst_net_120
	{
		name => 'ip6_multicast',
		DLT => 'IPV6',
		aliases => ['ip6 multicast'],
		unopt => '
			(000) ld       #0x0
			(001) jeq      #0x0             jt 2	jf 5
			(002) ldb      [24]
			(003) jeq      #0xff            jt 4	jf 5
			(004) ret      #262144
			(005) ret      #0
			',
	}, # ip6_multicast

	{
		name => 'icmp_types',
		DLT => 'EN10MB',
		aliases => ['
			0 == icmp-echoreply &&
			3 == icmp-unreach &&
			4 == icmp-sourcequench &&
			5 == icmp-redirect &&
			8 == icmp-echo &&
			9 == icmp-routeradvert &&
			10 == icmp-routersolicit &&
			11 == icmp-timxceed &&
			12 == icmp-paramprob &&
			13 == icmp-tstamp &&
			14 == icmp-tstampreply &&
			15 == icmp-ireq &&
			16 == icmp-ireqreply &&
			17 == icmp-maskreq &&
			18 == icmp-maskreply
		'],
		opt => '
			(000) ret      #262144
			',
	}, # icmp_types
	{
		name => 'icmp6_types',
		DLT => 'IPV6',
		aliases => ['
			1 == icmp6-destinationunreach &&
			2 == icmp6-packettoobig &&
			3 == icmp6-timeexceeded &&
			4 == icmp6-parameterproblem &&
			128 == icmp6-echo &&
			129 == icmp6-echoreply &&
			130 == icmp6-multicastlistenerquery &&
			131 == icmp6-multicastlistenerreportv1 &&
			132 == icmp6-multicastlistenerdone &&
			133 == icmp6-routersolicit &&
			134 == icmp6-routeradvert &&
			135 == icmp6-neighborsolicit &&
			136 == icmp6-neighboradvert &&
			137 == icmp6-redirect &&
			138 == icmp6-routerrenum &&
			139 == icmp6-nodeinformationquery &&
			140 == icmp6-nodeinformationresponse &&
			141 == icmp6-ineighbordiscoverysolicit &&
			142 == icmp6-ineighbordiscoveryadvert &&
			143 == icmp6-multicastlistenerreportv2 &&
			144 == icmp6-homeagentdiscoveryrequest &&
			145 == icmp6-homeagentdiscoveryreply &&
			146 == icmp6-mobileprefixsolicit &&
			147 == icmp6-mobileprefixadvert &&
			148 == icmp6-certpathsolicit &&
			149 == icmp6-certpathadvert &&
			151 == icmp6-multicastrouteradvert &&
			152 == icmp6-multicastroutersolicit &&
			153 == icmp6-multicastrouterterm
		'],
		opt => '
			(000) ret      #262144
			',
	}, # icmp6_types

	{
		name => 'tcp_flags',
		DLT => 'EN10MB',
		aliases => ['
			0x01 == tcp-fin &&
			0x02 == tcp-syn &&
			0x04 == tcp-rst &&
			0x08 == tcp-push &&
			0x10 == tcp-ack &&
			0x20 == tcp-urg &&
			0x40 == tcp-ece &&
			0x80 == tcp-cwr
		'],
		opt => '
			(000) ret      #262144
			',
	}, # tcp_flags

	{
		name => 'named_offsets',
		DLT => 'EN10MB',
		aliases => ['
			icmptype == 0 &&
			icmpcode == 1 &&
			icmp6type == 0 &&
			icmp6code == 1 &&
			tcpflags == 13
		'],
		opt => '
			(000) ret      #262144
			',
	}, # offsets

	# In the tests below "smtp" depends on getaddrinfo().
	{
		name => 'tcp_port',
		DLT => 'EN10MB',
		aliases => [
			'tcp port 25',
			'tcp port smtp',
			'tcp src or dst port 25',
			'tcp src or dst port smtp',
			# degenerate "portrange"
			'tcp portrange 25-25',
			'tcp portrange 25-smtp',
			'tcp portrange smtp-25',
			'tcp portrange smtp-smtp',
			'tcp portrange 25',
			# "25" is a valid port range, but "smtp" is not.
			'tcp src or dst portrange 25-25',
			'tcp src or dst portrange 25-smtp',
			'tcp src or dst portrange smtp-25',
			'tcp src or dst portrange smtp-smtp',
			'tcp src or dst portrange 25',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 8
			(002) ldb      [20]
			(003) jeq      #0x6             jt 4	jf 19
			(004) ldh      [54]
			(005) jeq      #0x19            jt 18	jf 6
			(006) ldh      [56]
			(007) jeq      #0x19            jt 18	jf 19
			(008) jeq      #0x800           jt 9	jf 19
			(009) ldb      [23]
			(010) jeq      #0x6             jt 11	jf 19
			(011) ldh      [20]
			(012) jset     #0x1fff          jt 19	jf 13
			(013) ldxb     4*([14]&0xf)
			(014) ldh      [x + 14]
			(015) jeq      #0x19            jt 18	jf 16
			(016) ldh      [x + 16]
			(017) jeq      #0x19            jt 18	jf 19
			(018) ret      #262144
			(019) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 8
			(002) ldb      [20]
			(003) jeq      #0x6             jt 4	jf 8
			(004) ldh      [54]
			(005) jeq      #0x19            jt 20	jf 6
			(006) ldh      [56]
			(007) jeq      #0x19            jt 20	jf 8
			(008) ldh      [12]
			(009) jeq      #0x800           jt 10	jf 21
			(010) ldb      [23]
			(011) jeq      #0x6             jt 12	jf 21
			(012) ldh      [20]
			(013) jset     #0x1fff          jt 21	jf 14
			(014) ldxb     4*([14]&0xf)
			(015) ldh      [x + 14]
			(016) jeq      #0x19            jt 20	jf 17
			(017) ldxb     4*([14]&0xf)
			(018) ldh      [x + 16]
			(019) jeq      #0x19            jt 20	jf 21
			(020) ret      #262144
			(021) ret      #0
			',
	}, # tcp_port
	{
		name => 'tcp_src_port',
		DLT => 'EN10MB',
		aliases => [
			'tcp src port 25',
			'tcp src port smtp',
			# degenerate "src portrange"
			'tcp src portrange 25-25',
			'tcp src portrange 25-smtp',
			'tcp src portrange smtp-25',
			'tcp src portrange smtp-smtp',
			'tcp src portrange 25',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 6
			(002) ldb      [20]
			(003) jeq      #0x6             jt 4	jf 15
			(004) ldh      [54]
			(005) jeq      #0x19            jt 14	jf 15
			(006) jeq      #0x800           jt 7	jf 15
			(007) ldb      [23]
			(008) jeq      #0x6             jt 9	jf 15
			(009) ldh      [20]
			(010) jset     #0x1fff          jt 15	jf 11
			(011) ldxb     4*([14]&0xf)
			(012) ldh      [x + 14]
			(013) jeq      #0x19            jt 14	jf 15
			(014) ret      #262144
			(015) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 6
			(002) ldb      [20]
			(003) jeq      #0x6             jt 4	jf 6
			(004) ldh      [54]
			(005) jeq      #0x19            jt 15	jf 6
			(006) ldh      [12]
			(007) jeq      #0x800           jt 8	jf 16
			(008) ldb      [23]
			(009) jeq      #0x6             jt 10	jf 16
			(010) ldh      [20]
			(011) jset     #0x1fff          jt 16	jf 12
			(012) ldxb     4*([14]&0xf)
			(013) ldh      [x + 14]
			(014) jeq      #0x19            jt 15	jf 16
			(015) ret      #262144
			(016) ret      #0
			',
	}, # tcp_src_port
	{
		name => 'tcp_dst_port',
		DLT => 'EN10MB',
		aliases => [
			'tcp dst port 25',
			'tcp dst port smtp',
			# degenerate "dst portrange"
			'tcp dst portrange 25-25',
			'tcp dst portrange 25-smtp',
			'tcp dst portrange smtp-25',
			'tcp dst portrange smtp-smtp',
			'tcp dst portrange 25',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 6
			(002) ldb      [20]
			(003) jeq      #0x6             jt 4	jf 15
			(004) ldh      [56]
			(005) jeq      #0x19            jt 14	jf 15
			(006) jeq      #0x800           jt 7	jf 15
			(007) ldb      [23]
			(008) jeq      #0x6             jt 9	jf 15
			(009) ldh      [20]
			(010) jset     #0x1fff          jt 15	jf 11
			(011) ldxb     4*([14]&0xf)
			(012) ldh      [x + 16]
			(013) jeq      #0x19            jt 14	jf 15
			(014) ret      #262144
			(015) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 6
			(002) ldb      [20]
			(003) jeq      #0x6             jt 4	jf 6
			(004) ldh      [56]
			(005) jeq      #0x19            jt 15	jf 6
			(006) ldh      [12]
			(007) jeq      #0x800           jt 8	jf 16
			(008) ldb      [23]
			(009) jeq      #0x6             jt 10	jf 16
			(010) ldh      [20]
			(011) jset     #0x1fff          jt 16	jf 12
			(012) ldxb     4*([14]&0xf)
			(013) ldh      [x + 16]
			(014) jeq      #0x19            jt 15	jf 16
			(015) ret      #262144
			(016) ret      #0
			',
	}, # tcp_dst_port
	{
		name => 'tcp_portrange',
		DLT => 'EN10MB',
		aliases => [
			'tcp portrange 25-53',
			'tcp portrange 25-domain',
			'tcp portrange smtp-53',
			'tcp portrange smtp-domain',
			'tcp portrange 53-25',
			'tcp portrange domain-25',
			'tcp portrange 53-smtp',
			'tcp portrange domain-smtp',
			'tcp src or dst portrange 25-53',
			'tcp src or dst portrange 25-domain',
			'tcp src or dst portrange smtp-53',
			'tcp src or dst portrange smtp-domain',
			'tcp src or dst portrange 53-25',
			'tcp src or dst portrange domain-25',
			'tcp src or dst portrange 53-smtp',
			'tcp src or dst portrange domain-smtp',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 9
			(002) ldb      [20]
			(003) jeq      #0x6             jt 4	jf 22
			(004) ldh      [54]
			(005) jge      #0x19            jt 6	jf 7
			(006) jgt      #0x35            jt 7	jf 21
			(007) ldh      [56]
			(008) jge      #0x19            jt 20	jf 22
			(009) jeq      #0x800           jt 10	jf 22
			(010) ldb      [23]
			(011) jeq      #0x6             jt 12	jf 22
			(012) ldh      [20]
			(013) jset     #0x1fff          jt 22	jf 14
			(014) ldxb     4*([14]&0xf)
			(015) ldh      [x + 14]
			(016) jge      #0x19            jt 17	jf 18
			(017) jgt      #0x35            jt 18	jf 21
			(018) ldh      [x + 16]
			(019) jge      #0x19            jt 20	jf 22
			(020) jgt      #0x35            jt 22	jf 21
			(021) ret      #262144
			(022) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 12
			(002) ldb      [20]
			(003) jeq      #0x6             jt 4	jf 12
			(004) ldh      [54]
			(005) jge      #0x19            jt 6	jf 8
			(006) ldh      [54]
			(007) jgt      #0x35            jt 8	jf 30
			(008) ldh      [56]
			(009) jge      #0x19            jt 10	jf 12
			(010) ldh      [56]
			(011) jgt      #0x35            jt 12	jf 30
			(012) ldh      [12]
			(013) jeq      #0x800           jt 14	jf 31
			(014) ldb      [23]
			(015) jeq      #0x6             jt 16	jf 31
			(016) ldh      [20]
			(017) jset     #0x1fff          jt 31	jf 18
			(018) ldxb     4*([14]&0xf)
			(019) ldh      [x + 14]
			(020) jge      #0x19            jt 21	jf 24
			(021) ldxb     4*([14]&0xf)
			(022) ldh      [x + 14]
			(023) jgt      #0x35            jt 24	jf 30
			(024) ldxb     4*([14]&0xf)
			(025) ldh      [x + 16]
			(026) jge      #0x19            jt 27	jf 31
			(027) ldxb     4*([14]&0xf)
			(028) ldh      [x + 16]
			(029) jgt      #0x35            jt 31	jf 30
			(030) ret      #262144
			(031) ret      #0
			',
	}, # tcp_portrange
	{
		name => 'tcp_src_portrange',
		DLT => 'EN10MB',
		aliases => [
			'tcp src portrange 25-53',
			'tcp src portrange 25-domain',
			'tcp src portrange smtp-53',
			'tcp src portrange smtp-domain',
			'tcp src portrange 53-25',
			'tcp src portrange domain-25',
			'tcp src portrange 53-smtp',
			'tcp src portrange domain-smtp',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 6
			(002) ldb      [20]
			(003) jeq      #0x6             jt 4	jf 16
			(004) ldh      [54]
			(005) jge      #0x19            jt 14	jf 16
			(006) jeq      #0x800           jt 7	jf 16
			(007) ldb      [23]
			(008) jeq      #0x6             jt 9	jf 16
			(009) ldh      [20]
			(010) jset     #0x1fff          jt 16	jf 11
			(011) ldxb     4*([14]&0xf)
			(012) ldh      [x + 14]
			(013) jge      #0x19            jt 14	jf 16
			(014) jgt      #0x35            jt 16	jf 15
			(015) ret      #262144
			(016) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 8
			(002) ldb      [20]
			(003) jeq      #0x6             jt 4	jf 8
			(004) ldh      [54]
			(005) jge      #0x19            jt 6	jf 8
			(006) ldh      [54]
			(007) jgt      #0x35            jt 8	jf 20
			(008) ldh      [12]
			(009) jeq      #0x800           jt 10	jf 21
			(010) ldb      [23]
			(011) jeq      #0x6             jt 12	jf 21
			(012) ldh      [20]
			(013) jset     #0x1fff          jt 21	jf 14
			(014) ldxb     4*([14]&0xf)
			(015) ldh      [x + 14]
			(016) jge      #0x19            jt 17	jf 21
			(017) ldxb     4*([14]&0xf)
			(018) ldh      [x + 14]
			(019) jgt      #0x35            jt 21	jf 20
			(020) ret      #262144
			(021) ret      #0
			',
	}, # tcp_src_portrange
	{
		name => 'tcp_dst_portrange',
		DLT => 'EN10MB',
		aliases => [
			'tcp dst portrange 25-53',
			'tcp dst portrange 25-domain',
			'tcp dst portrange smtp-53',
			'tcp dst portrange smtp-domain',
			'tcp dst portrange 53-25',
			'tcp dst portrange domain-25',
			'tcp dst portrange 53-smtp',
			'tcp dst portrange domain-smtp',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 6
			(002) ldb      [20]
			(003) jeq      #0x6             jt 4	jf 16
			(004) ldh      [56]
			(005) jge      #0x19            jt 14	jf 16
			(006) jeq      #0x800           jt 7	jf 16
			(007) ldb      [23]
			(008) jeq      #0x6             jt 9	jf 16
			(009) ldh      [20]
			(010) jset     #0x1fff          jt 16	jf 11
			(011) ldxb     4*([14]&0xf)
			(012) ldh      [x + 16]
			(013) jge      #0x19            jt 14	jf 16
			(014) jgt      #0x35            jt 16	jf 15
			(015) ret      #262144
			(016) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 8
			(002) ldb      [20]
			(003) jeq      #0x6             jt 4	jf 8
			(004) ldh      [56]
			(005) jge      #0x19            jt 6	jf 8
			(006) ldh      [56]
			(007) jgt      #0x35            jt 8	jf 20
			(008) ldh      [12]
			(009) jeq      #0x800           jt 10	jf 21
			(010) ldb      [23]
			(011) jeq      #0x6             jt 12	jf 21
			(012) ldh      [20]
			(013) jset     #0x1fff          jt 21	jf 14
			(014) ldxb     4*([14]&0xf)
			(015) ldh      [x + 16]
			(016) jge      #0x19            jt 17	jf 21
			(017) ldxb     4*([14]&0xf)
			(018) ldh      [x + 16]
			(019) jgt      #0x35            jt 21	jf 20
			(020) ret      #262144
			(021) ret      #0
			',
	}, # tcp_dst_portrange
	# In the tests below "domain" depends on getaddrinfo().
	{
		name => 'udp_port',
		DLT => 'EN10MB',
		aliases => [
			'udp port 53',
			'udp port domain',
			'udp src or dst port 53',
			'udp src or dst port domain',
			# degenerate "portrange"
			'udp portrange 53-53',
			'udp portrange 53-domain',
			'udp portrange domain-53',
			'udp portrange domain-domain',
			'udp portrange 53',
			# "53" is a valid port range, but "domain" is not.
			'udp src or dst portrange 53-53',
			'udp src or dst portrange 53-domain',
			'udp src or dst portrange domain-53',
			'udp src or dst portrange domain-domain',
			'udp src or dst portrange 53',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 8
			(002) ldb      [20]
			(003) jeq      #0x11            jt 4	jf 19
			(004) ldh      [54]
			(005) jeq      #0x35            jt 18	jf 6
			(006) ldh      [56]
			(007) jeq      #0x35            jt 18	jf 19
			(008) jeq      #0x800           jt 9	jf 19
			(009) ldb      [23]
			(010) jeq      #0x11            jt 11	jf 19
			(011) ldh      [20]
			(012) jset     #0x1fff          jt 19	jf 13
			(013) ldxb     4*([14]&0xf)
			(014) ldh      [x + 14]
			(015) jeq      #0x35            jt 18	jf 16
			(016) ldh      [x + 16]
			(017) jeq      #0x35            jt 18	jf 19
			(018) ret      #262144
			(019) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 8
			(002) ldb      [20]
			(003) jeq      #0x11            jt 4	jf 8
			(004) ldh      [54]
			(005) jeq      #0x35            jt 20	jf 6
			(006) ldh      [56]
			(007) jeq      #0x35            jt 20	jf 8
			(008) ldh      [12]
			(009) jeq      #0x800           jt 10	jf 21
			(010) ldb      [23]
			(011) jeq      #0x11            jt 12	jf 21
			(012) ldh      [20]
			(013) jset     #0x1fff          jt 21	jf 14
			(014) ldxb     4*([14]&0xf)
			(015) ldh      [x + 14]
			(016) jeq      #0x35            jt 20	jf 17
			(017) ldxb     4*([14]&0xf)
			(018) ldh      [x + 16]
			(019) jeq      #0x35            jt 20	jf 21
			(020) ret      #262144
			(021) ret      #0
			',
	}, # udp_port
	{
		name => 'udp_src_port',
		DLT => 'EN10MB',
		aliases => [
			'udp src port 53',
			'udp src port domain',
			# degenerate "src portrange"
			'udp src portrange 53-53',
			'udp src portrange 53-domain',
			'udp src portrange domain-53',
			'udp src portrange domain-domain',
			'udp src portrange 53',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 6
			(002) ldb      [20]
			(003) jeq      #0x11            jt 4	jf 15
			(004) ldh      [54]
			(005) jeq      #0x35            jt 14	jf 15
			(006) jeq      #0x800           jt 7	jf 15
			(007) ldb      [23]
			(008) jeq      #0x11            jt 9	jf 15
			(009) ldh      [20]
			(010) jset     #0x1fff          jt 15	jf 11
			(011) ldxb     4*([14]&0xf)
			(012) ldh      [x + 14]
			(013) jeq      #0x35            jt 14	jf 15
			(014) ret      #262144
			(015) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 6
			(002) ldb      [20]
			(003) jeq      #0x11            jt 4	jf 6
			(004) ldh      [54]
			(005) jeq      #0x35            jt 15	jf 6
			(006) ldh      [12]
			(007) jeq      #0x800           jt 8	jf 16
			(008) ldb      [23]
			(009) jeq      #0x11            jt 10	jf 16
			(010) ldh      [20]
			(011) jset     #0x1fff          jt 16	jf 12
			(012) ldxb     4*([14]&0xf)
			(013) ldh      [x + 14]
			(014) jeq      #0x35            jt 15	jf 16
			(015) ret      #262144
			(016) ret      #0
			',
	}, # udp_src_port
	{
		name => 'udp_dst_port',
		DLT => 'EN10MB',
		aliases => [
			'udp dst port 53',
			'udp dst port domain',
			# degenerate "dst portrange"
			'udp dst portrange 53-53',
			'udp dst portrange 53-domain',
			'udp dst portrange domain-53',
			'udp dst portrange domain-domain',
			'udp dst portrange 53',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 6
			(002) ldb      [20]
			(003) jeq      #0x11            jt 4	jf 15
			(004) ldh      [56]
			(005) jeq      #0x35            jt 14	jf 15
			(006) jeq      #0x800           jt 7	jf 15
			(007) ldb      [23]
			(008) jeq      #0x11            jt 9	jf 15
			(009) ldh      [20]
			(010) jset     #0x1fff          jt 15	jf 11
			(011) ldxb     4*([14]&0xf)
			(012) ldh      [x + 16]
			(013) jeq      #0x35            jt 14	jf 15
			(014) ret      #262144
			(015) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 6
			(002) ldb      [20]
			(003) jeq      #0x11            jt 4	jf 6
			(004) ldh      [56]
			(005) jeq      #0x35            jt 15	jf 6
			(006) ldh      [12]
			(007) jeq      #0x800           jt 8	jf 16
			(008) ldb      [23]
			(009) jeq      #0x11            jt 10	jf 16
			(010) ldh      [20]
			(011) jset     #0x1fff          jt 16	jf 12
			(012) ldxb     4*([14]&0xf)
			(013) ldh      [x + 16]
			(014) jeq      #0x35            jt 15	jf 16
			(015) ret      #262144
			(016) ret      #0
			',
	}, # udp_dst_port
	{
		name => 'udp_portrange',
		DLT => 'EN10MB',
		aliases => [
			'udp portrange 67-68',
			'udp portrange 67-bootpc',
			'udp portrange bootps-68',
			'udp portrange bootps-bootpc',
			'udp portrange 68-67',
			'udp portrange bootpc-67',
			'udp portrange 68-bootps',
			'udp portrange bootpc-bootps',
			'udp src or dst portrange 67-68',
			'udp src or dst portrange 67-bootpc',
			'udp src or dst portrange bootps-68',
			'udp src or dst portrange bootps-bootpc',
			'udp src or dst portrange 68-67',
			'udp src or dst portrange bootpc-67',
			'udp src or dst portrange 68-bootps',
			'udp src or dst portrange bootpc-bootps',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 9
			(002) ldb      [20]
			(003) jeq      #0x11            jt 4	jf 22
			(004) ldh      [54]
			(005) jge      #0x43            jt 6	jf 7
			(006) jgt      #0x44            jt 7	jf 21
			(007) ldh      [56]
			(008) jge      #0x43            jt 20	jf 22
			(009) jeq      #0x800           jt 10	jf 22
			(010) ldb      [23]
			(011) jeq      #0x11            jt 12	jf 22
			(012) ldh      [20]
			(013) jset     #0x1fff          jt 22	jf 14
			(014) ldxb     4*([14]&0xf)
			(015) ldh      [x + 14]
			(016) jge      #0x43            jt 17	jf 18
			(017) jgt      #0x44            jt 18	jf 21
			(018) ldh      [x + 16]
			(019) jge      #0x43            jt 20	jf 22
			(020) jgt      #0x44            jt 22	jf 21
			(021) ret      #262144
			(022) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 12
			(002) ldb      [20]
			(003) jeq      #0x11            jt 4	jf 12
			(004) ldh      [54]
			(005) jge      #0x43            jt 6	jf 8
			(006) ldh      [54]
			(007) jgt      #0x44            jt 8	jf 30
			(008) ldh      [56]
			(009) jge      #0x43            jt 10	jf 12
			(010) ldh      [56]
			(011) jgt      #0x44            jt 12	jf 30
			(012) ldh      [12]
			(013) jeq      #0x800           jt 14	jf 31
			(014) ldb      [23]
			(015) jeq      #0x11            jt 16	jf 31
			(016) ldh      [20]
			(017) jset     #0x1fff          jt 31	jf 18
			(018) ldxb     4*([14]&0xf)
			(019) ldh      [x + 14]
			(020) jge      #0x43            jt 21	jf 24
			(021) ldxb     4*([14]&0xf)
			(022) ldh      [x + 14]
			(023) jgt      #0x44            jt 24	jf 30
			(024) ldxb     4*([14]&0xf)
			(025) ldh      [x + 16]
			(026) jge      #0x43            jt 27	jf 31
			(027) ldxb     4*([14]&0xf)
			(028) ldh      [x + 16]
			(029) jgt      #0x44            jt 31	jf 30
			(030) ret      #262144
			(031) ret      #0
			',
	}, # udp_portrange
	{
		name => 'udp_src_portrange',
		DLT => 'EN10MB',
		aliases => [
			'udp src portrange 67-68',
			'udp src portrange 67-bootpc',
			'udp src portrange bootps-68',
			'udp src portrange bootps-bootpc',
			'udp src portrange 68-67',
			'udp src portrange bootpc-67',
			'udp src portrange 68-bootps',
			'udp src portrange bootpc-bootps',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 6
			(002) ldb      [20]
			(003) jeq      #0x11            jt 4	jf 16
			(004) ldh      [54]
			(005) jge      #0x43            jt 14	jf 16
			(006) jeq      #0x800           jt 7	jf 16
			(007) ldb      [23]
			(008) jeq      #0x11            jt 9	jf 16
			(009) ldh      [20]
			(010) jset     #0x1fff          jt 16	jf 11
			(011) ldxb     4*([14]&0xf)
			(012) ldh      [x + 14]
			(013) jge      #0x43            jt 14	jf 16
			(014) jgt      #0x44            jt 16	jf 15
			(015) ret      #262144
			(016) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 8
			(002) ldb      [20]
			(003) jeq      #0x11            jt 4	jf 8
			(004) ldh      [54]
			(005) jge      #0x43            jt 6	jf 8
			(006) ldh      [54]
			(007) jgt      #0x44            jt 8	jf 20
			(008) ldh      [12]
			(009) jeq      #0x800           jt 10	jf 21
			(010) ldb      [23]
			(011) jeq      #0x11            jt 12	jf 21
			(012) ldh      [20]
			(013) jset     #0x1fff          jt 21	jf 14
			(014) ldxb     4*([14]&0xf)
			(015) ldh      [x + 14]
			(016) jge      #0x43            jt 17	jf 21
			(017) ldxb     4*([14]&0xf)
			(018) ldh      [x + 14]
			(019) jgt      #0x44            jt 21	jf 20
			(020) ret      #262144
			(021) ret      #0
			',
	}, # udp_src_portrange
	{
		name => 'udp_dst_portrange',
		DLT => 'EN10MB',
		aliases => [
			'udp dst portrange 67-68',
			'udp dst portrange 67-bootpc',
			'udp dst portrange bootps-68',
			'udp dst portrange bootps-bootpc',
			'udp dst portrange 68-67',
			'udp dst portrange bootpc-67',
			'udp dst portrange 68-bootps',
			'udp dst portrange bootpc-bootps',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 6
			(002) ldb      [20]
			(003) jeq      #0x11            jt 4	jf 16
			(004) ldh      [56]
			(005) jge      #0x43            jt 14	jf 16
			(006) jeq      #0x800           jt 7	jf 16
			(007) ldb      [23]
			(008) jeq      #0x11            jt 9	jf 16
			(009) ldh      [20]
			(010) jset     #0x1fff          jt 16	jf 11
			(011) ldxb     4*([14]&0xf)
			(012) ldh      [x + 16]
			(013) jge      #0x43            jt 14	jf 16
			(014) jgt      #0x44            jt 16	jf 15
			(015) ret      #262144
			(016) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 8
			(002) ldb      [20]
			(003) jeq      #0x11            jt 4	jf 8
			(004) ldh      [56]
			(005) jge      #0x43            jt 6	jf 8
			(006) ldh      [56]
			(007) jgt      #0x44            jt 8	jf 20
			(008) ldh      [12]
			(009) jeq      #0x800           jt 10	jf 21
			(010) ldb      [23]
			(011) jeq      #0x11            jt 12	jf 21
			(012) ldh      [20]
			(013) jset     #0x1fff          jt 21	jf 14
			(014) ldxb     4*([14]&0xf)
			(015) ldh      [x + 16]
			(016) jge      #0x43            jt 17	jf 21
			(017) ldxb     4*([14]&0xf)
			(018) ldh      [x + 16]
			(019) jgt      #0x44            jt 21	jf 20
			(020) ret      #262144
			(021) ret      #0
			',
	}, # udp_dst_portrange
	# SCTP tests below do not use service names because the translation is
	# currently broken and may not have a suitable /etc/services contents
	# in all supported environments after the bug fix.
	{
		name => 'sctp_port',
		DLT => 'EN10MB',
		aliases => [
			'sctp port 5672',
			'sctp src or dst port 5672',
			# degenerate "portrange"
			'sctp portrange 5672-5672',
			'sctp portrange 5672',
			'sctp src or dst portrange 5672-5672',
			'sctp src or dst portrange 5672',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 8
			(002) ldb      [20]
			(003) jeq      #0x84            jt 4	jf 19
			(004) ldh      [54]
			(005) jeq      #0x1628          jt 18	jf 6
			(006) ldh      [56]
			(007) jeq      #0x1628          jt 18	jf 19
			(008) jeq      #0x800           jt 9	jf 19
			(009) ldb      [23]
			(010) jeq      #0x84            jt 11	jf 19
			(011) ldh      [20]
			(012) jset     #0x1fff          jt 19	jf 13
			(013) ldxb     4*([14]&0xf)
			(014) ldh      [x + 14]
			(015) jeq      #0x1628          jt 18	jf 16
			(016) ldh      [x + 16]
			(017) jeq      #0x1628          jt 18	jf 19
			(018) ret      #262144
			(019) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 8
			(002) ldb      [20]
			(003) jeq      #0x84            jt 4	jf 8
			(004) ldh      [54]
			(005) jeq      #0x1628          jt 20	jf 6
			(006) ldh      [56]
			(007) jeq      #0x1628          jt 20	jf 8
			(008) ldh      [12]
			(009) jeq      #0x800           jt 10	jf 21
			(010) ldb      [23]
			(011) jeq      #0x84            jt 12	jf 21
			(012) ldh      [20]
			(013) jset     #0x1fff          jt 21	jf 14
			(014) ldxb     4*([14]&0xf)
			(015) ldh      [x + 14]
			(016) jeq      #0x1628          jt 20	jf 17
			(017) ldxb     4*([14]&0xf)
			(018) ldh      [x + 16]
			(019) jeq      #0x1628          jt 20	jf 21
			(020) ret      #262144
			(021) ret      #0
			',
	}, # sctp_port
	{
		name => 'sctp_src_port',
		DLT => 'EN10MB',
		aliases => [
			'sctp src port 5672',
			# degenerate "src portrange"
			'sctp src portrange 5672-5672',
			'sctp src portrange 5672',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 6
			(002) ldb      [20]
			(003) jeq      #0x84            jt 4	jf 15
			(004) ldh      [54]
			(005) jeq      #0x1628          jt 14	jf 15
			(006) jeq      #0x800           jt 7	jf 15
			(007) ldb      [23]
			(008) jeq      #0x84            jt 9	jf 15
			(009) ldh      [20]
			(010) jset     #0x1fff          jt 15	jf 11
			(011) ldxb     4*([14]&0xf)
			(012) ldh      [x + 14]
			(013) jeq      #0x1628          jt 14	jf 15
			(014) ret      #262144
			(015) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 6
			(002) ldb      [20]
			(003) jeq      #0x84            jt 4	jf 6
			(004) ldh      [54]
			(005) jeq      #0x1628          jt 15	jf 6
			(006) ldh      [12]
			(007) jeq      #0x800           jt 8	jf 16
			(008) ldb      [23]
			(009) jeq      #0x84            jt 10	jf 16
			(010) ldh      [20]
			(011) jset     #0x1fff          jt 16	jf 12
			(012) ldxb     4*([14]&0xf)
			(013) ldh      [x + 14]
			(014) jeq      #0x1628          jt 15	jf 16
			(015) ret      #262144
			(016) ret      #0
			',
	}, # sctp_src_port
	{
		name => 'sctp_dst_port',
		DLT => 'EN10MB',
		aliases => [
			'sctp dst port 5672',
			# degenerate "dst portrange"
			'sctp dst portrange 5672-5672',
			'sctp dst portrange 5672',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 6
			(002) ldb      [20]
			(003) jeq      #0x84            jt 4	jf 15
			(004) ldh      [56]
			(005) jeq      #0x1628          jt 14	jf 15
			(006) jeq      #0x800           jt 7	jf 15
			(007) ldb      [23]
			(008) jeq      #0x84            jt 9	jf 15
			(009) ldh      [20]
			(010) jset     #0x1fff          jt 15	jf 11
			(011) ldxb     4*([14]&0xf)
			(012) ldh      [x + 16]
			(013) jeq      #0x1628          jt 14	jf 15
			(014) ret      #262144
			(015) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 6
			(002) ldb      [20]
			(003) jeq      #0x84            jt 4	jf 6
			(004) ldh      [56]
			(005) jeq      #0x1628          jt 15	jf 6
			(006) ldh      [12]
			(007) jeq      #0x800           jt 8	jf 16
			(008) ldb      [23]
			(009) jeq      #0x84            jt 10	jf 16
			(010) ldh      [20]
			(011) jset     #0x1fff          jt 16	jf 12
			(012) ldxb     4*([14]&0xf)
			(013) ldh      [x + 16]
			(014) jeq      #0x1628          jt 15	jf 16
			(015) ret      #262144
			(016) ret      #0
			',
	}, # sctp_dst_port
	{
		name => 'sctp_portrange',
		DLT => 'EN10MB',
		aliases => [
			'sctp portrange 1-1023',
			'sctp portrange 1023-1',
			'sctp src or dst portrange 1-1023',
			'sctp src or dst portrange 1023-1',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 9
			(002) ldb      [20]
			(003) jeq      #0x84            jt 4	jf 22
			(004) ldh      [54]
			(005) jge      #0x1             jt 6	jf 7
			(006) jgt      #0x3ff           jt 7	jf 21
			(007) ldh      [56]
			(008) jge      #0x1             jt 20	jf 22
			(009) jeq      #0x800           jt 10	jf 22
			(010) ldb      [23]
			(011) jeq      #0x84            jt 12	jf 22
			(012) ldh      [20]
			(013) jset     #0x1fff          jt 22	jf 14
			(014) ldxb     4*([14]&0xf)
			(015) ldh      [x + 14]
			(016) jge      #0x1             jt 17	jf 18
			(017) jgt      #0x3ff           jt 18	jf 21
			(018) ldh      [x + 16]
			(019) jge      #0x1             jt 20	jf 22
			(020) jgt      #0x3ff           jt 22	jf 21
			(021) ret      #262144
			(022) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 12
			(002) ldb      [20]
			(003) jeq      #0x84            jt 4	jf 12
			(004) ldh      [54]
			(005) jge      #0x1             jt 6	jf 8
			(006) ldh      [54]
			(007) jgt      #0x3ff           jt 8	jf 30
			(008) ldh      [56]
			(009) jge      #0x1             jt 10	jf 12
			(010) ldh      [56]
			(011) jgt      #0x3ff           jt 12	jf 30
			(012) ldh      [12]
			(013) jeq      #0x800           jt 14	jf 31
			(014) ldb      [23]
			(015) jeq      #0x84            jt 16	jf 31
			(016) ldh      [20]
			(017) jset     #0x1fff          jt 31	jf 18
			(018) ldxb     4*([14]&0xf)
			(019) ldh      [x + 14]
			(020) jge      #0x1             jt 21	jf 24
			(021) ldxb     4*([14]&0xf)
			(022) ldh      [x + 14]
			(023) jgt      #0x3ff           jt 24	jf 30
			(024) ldxb     4*([14]&0xf)
			(025) ldh      [x + 16]
			(026) jge      #0x1             jt 27	jf 31
			(027) ldxb     4*([14]&0xf)
			(028) ldh      [x + 16]
			(029) jgt      #0x3ff           jt 31	jf 30
			(030) ret      #262144
			(031) ret      #0
			',
	}, # sctp_portrange
	{
		name => 'sctp_src_portrange',
		DLT => 'EN10MB',
		aliases => [
			'sctp src portrange 1-1023',
			'sctp src portrange 1023-1',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 6
			(002) ldb      [20]
			(003) jeq      #0x84            jt 4	jf 16
			(004) ldh      [54]
			(005) jge      #0x1             jt 14	jf 16
			(006) jeq      #0x800           jt 7	jf 16
			(007) ldb      [23]
			(008) jeq      #0x84            jt 9	jf 16
			(009) ldh      [20]
			(010) jset     #0x1fff          jt 16	jf 11
			(011) ldxb     4*([14]&0xf)
			(012) ldh      [x + 14]
			(013) jge      #0x1             jt 14	jf 16
			(014) jgt      #0x3ff           jt 16	jf 15
			(015) ret      #262144
			(016) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 8
			(002) ldb      [20]
			(003) jeq      #0x84            jt 4	jf 8
			(004) ldh      [54]
			(005) jge      #0x1             jt 6	jf 8
			(006) ldh      [54]
			(007) jgt      #0x3ff           jt 8	jf 20
			(008) ldh      [12]
			(009) jeq      #0x800           jt 10	jf 21
			(010) ldb      [23]
			(011) jeq      #0x84            jt 12	jf 21
			(012) ldh      [20]
			(013) jset     #0x1fff          jt 21	jf 14
			(014) ldxb     4*([14]&0xf)
			(015) ldh      [x + 14]
			(016) jge      #0x1             jt 17	jf 21
			(017) ldxb     4*([14]&0xf)
			(018) ldh      [x + 14]
			(019) jgt      #0x3ff           jt 21	jf 20
			(020) ret      #262144
			(021) ret      #0
			',
	}, # sctp_src_portrange
	{
		name => 'sctp_dst_portrange',
		DLT => 'EN10MB',
		aliases => [
			'sctp dst portrange 1-1023',
			'sctp dst portrange 1023-1',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 6
			(002) ldb      [20]
			(003) jeq      #0x84            jt 4	jf 16
			(004) ldh      [56]
			(005) jge      #0x1             jt 14	jf 16
			(006) jeq      #0x800           jt 7	jf 16
			(007) ldb      [23]
			(008) jeq      #0x84            jt 9	jf 16
			(009) ldh      [20]
			(010) jset     #0x1fff          jt 16	jf 11
			(011) ldxb     4*([14]&0xf)
			(012) ldh      [x + 16]
			(013) jge      #0x1             jt 14	jf 16
			(014) jgt      #0x3ff           jt 16	jf 15
			(015) ret      #262144
			(016) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 8
			(002) ldb      [20]
			(003) jeq      #0x84            jt 4	jf 8
			(004) ldh      [56]
			(005) jge      #0x1             jt 6	jf 8
			(006) ldh      [56]
			(007) jgt      #0x3ff           jt 8	jf 20
			(008) ldh      [12]
			(009) jeq      #0x800           jt 10	jf 21
			(010) ldb      [23]
			(011) jeq      #0x84            jt 12	jf 21
			(012) ldh      [20]
			(013) jset     #0x1fff          jt 21	jf 14
			(014) ldxb     4*([14]&0xf)
			(015) ldh      [x + 16]
			(016) jge      #0x1             jt 17	jf 21
			(017) ldxb     4*([14]&0xf)
			(018) ldh      [x + 16]
			(019) jgt      #0x3ff           jt 21	jf 20
			(020) ret      #262144
			(021) ret      #0
			',
	}, # sctp_dst_portrange
	{
		name => 'port',
		DLT => 'EN10MB',
		aliases => [
			'port 7',
			'src or dst port 7',
			# Do not try a service name due to SCTP.
			# degenerate "portrange"
			'portrange 7-7',
			'portrange 7',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 10
			(002) ldb      [20]
			(003) jeq      #0x84            jt 6	jf 4
			(004) jeq      #0x6             jt 6	jf 5
			(005) jeq      #0x11            jt 6	jf 23
			(006) ldh      [54]
			(007) jeq      #0x7             jt 22	jf 8
			(008) ldh      [56]
			(009) jeq      #0x7             jt 22	jf 23
			(010) jeq      #0x800           jt 11	jf 23
			(011) ldb      [23]
			(012) jeq      #0x84            jt 15	jf 13
			(013) jeq      #0x6             jt 15	jf 14
			(014) jeq      #0x11            jt 15	jf 23
			(015) ldh      [20]
			(016) jset     #0x1fff          jt 23	jf 17
			(017) ldxb     4*([14]&0xf)
			(018) ldh      [x + 14]
			(019) jeq      #0x7             jt 22	jf 20
			(020) ldh      [x + 16]
			(021) jeq      #0x7             jt 22	jf 23
			(022) ret      #262144
			(023) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 12
			(002) ldb      [20]
			(003) jeq      #0x84            jt 8	jf 4
			(004) ldb      [20]
			(005) jeq      #0x6             jt 8	jf 6
			(006) ldb      [20]
			(007) jeq      #0x11            jt 8	jf 12
			(008) ldh      [54]
			(009) jeq      #0x7             jt 28	jf 10
			(010) ldh      [56]
			(011) jeq      #0x7             jt 28	jf 12
			(012) ldh      [12]
			(013) jeq      #0x800           jt 14	jf 29
			(014) ldb      [23]
			(015) jeq      #0x84            jt 20	jf 16
			(016) ldb      [23]
			(017) jeq      #0x6             jt 20	jf 18
			(018) ldb      [23]
			(019) jeq      #0x11            jt 20	jf 29
			(020) ldh      [20]
			(021) jset     #0x1fff          jt 29	jf 22
			(022) ldxb     4*([14]&0xf)
			(023) ldh      [x + 14]
			(024) jeq      #0x7             jt 28	jf 25
			(025) ldxb     4*([14]&0xf)
			(026) ldh      [x + 16]
			(027) jeq      #0x7             jt 28	jf 29
			(028) ret      #262144
			(029) ret      #0
			',
	}, # port
	{
		name => 'src_port',
		DLT => 'EN10MB',
		aliases => [
			'src port 7',
			# degenerate "src portrange"
			'src portrange 7-7',
			'src portrange 7',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 8
			(002) ldb      [20]
			(003) jeq      #0x84            jt 6	jf 4
			(004) jeq      #0x6             jt 6	jf 5
			(005) jeq      #0x11            jt 6	jf 19
			(006) ldh      [54]
			(007) jeq      #0x7             jt 18	jf 19
			(008) jeq      #0x800           jt 9	jf 19
			(009) ldb      [23]
			(010) jeq      #0x84            jt 13	jf 11
			(011) jeq      #0x6             jt 13	jf 12
			(012) jeq      #0x11            jt 13	jf 19
			(013) ldh      [20]
			(014) jset     #0x1fff          jt 19	jf 15
			(015) ldxb     4*([14]&0xf)
			(016) ldh      [x + 14]
			(017) jeq      #0x7             jt 18	jf 19
			(018) ret      #262144
			(019) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 10
			(002) ldb      [20]
			(003) jeq      #0x84            jt 8	jf 4
			(004) ldb      [20]
			(005) jeq      #0x6             jt 8	jf 6
			(006) ldb      [20]
			(007) jeq      #0x11            jt 8	jf 10
			(008) ldh      [54]
			(009) jeq      #0x7             jt 23	jf 10
			(010) ldh      [12]
			(011) jeq      #0x800           jt 12	jf 24
			(012) ldb      [23]
			(013) jeq      #0x84            jt 18	jf 14
			(014) ldb      [23]
			(015) jeq      #0x6             jt 18	jf 16
			(016) ldb      [23]
			(017) jeq      #0x11            jt 18	jf 24
			(018) ldh      [20]
			(019) jset     #0x1fff          jt 24	jf 20
			(020) ldxb     4*([14]&0xf)
			(021) ldh      [x + 14]
			(022) jeq      #0x7             jt 23	jf 24
			(023) ret      #262144
			(024) ret      #0
			',
	}, # src_port
	{
		name => 'dst_port',
		DLT => 'EN10MB',
		aliases => [
			'dst port 7',
			# degenerate "dst portrange"
			'dst portrange 7-7',
			'dst portrange 7',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 8
			(002) ldb      [20]
			(003) jeq      #0x84            jt 6	jf 4
			(004) jeq      #0x6             jt 6	jf 5
			(005) jeq      #0x11            jt 6	jf 19
			(006) ldh      [56]
			(007) jeq      #0x7             jt 18	jf 19
			(008) jeq      #0x800           jt 9	jf 19
			(009) ldb      [23]
			(010) jeq      #0x84            jt 13	jf 11
			(011) jeq      #0x6             jt 13	jf 12
			(012) jeq      #0x11            jt 13	jf 19
			(013) ldh      [20]
			(014) jset     #0x1fff          jt 19	jf 15
			(015) ldxb     4*([14]&0xf)
			(016) ldh      [x + 16]
			(017) jeq      #0x7             jt 18	jf 19
			(018) ret      #262144
			(019) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 10
			(002) ldb      [20]
			(003) jeq      #0x84            jt 8	jf 4
			(004) ldb      [20]
			(005) jeq      #0x6             jt 8	jf 6
			(006) ldb      [20]
			(007) jeq      #0x11            jt 8	jf 10
			(008) ldh      [56]
			(009) jeq      #0x7             jt 23	jf 10
			(010) ldh      [12]
			(011) jeq      #0x800           jt 12	jf 24
			(012) ldb      [23]
			(013) jeq      #0x84            jt 18	jf 14
			(014) ldb      [23]
			(015) jeq      #0x6             jt 18	jf 16
			(016) ldb      [23]
			(017) jeq      #0x11            jt 18	jf 24
			(018) ldh      [20]
			(019) jset     #0x1fff          jt 24	jf 20
			(020) ldxb     4*([14]&0xf)
			(021) ldh      [x + 16]
			(022) jeq      #0x7             jt 23	jf 24
			(023) ret      #262144
			(024) ret      #0
			',
	}, # dst_port
	{
		name => 'portrange',
		DLT => 'EN10MB',
		aliases => [
			'portrange 1-1023',
			'portrange 1023-1',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 11
			(002) ldb      [20]
			(003) jeq      #0x84            jt 6	jf 4
			(004) jeq      #0x6             jt 6	jf 5
			(005) jeq      #0x11            jt 6	jf 26
			(006) ldh      [54]
			(007) jge      #0x1             jt 8	jf 9
			(008) jgt      #0x3ff           jt 9	jf 25
			(009) ldh      [56]
			(010) jge      #0x1             jt 24	jf 26
			(011) jeq      #0x800           jt 12	jf 26
			(012) ldb      [23]
			(013) jeq      #0x84            jt 16	jf 14
			(014) jeq      #0x6             jt 16	jf 15
			(015) jeq      #0x11            jt 16	jf 26
			(016) ldh      [20]
			(017) jset     #0x1fff          jt 26	jf 18
			(018) ldxb     4*([14]&0xf)
			(019) ldh      [x + 14]
			(020) jge      #0x1             jt 21	jf 22
			(021) jgt      #0x3ff           jt 22	jf 25
			(022) ldh      [x + 16]
			(023) jge      #0x1             jt 24	jf 26
			(024) jgt      #0x3ff           jt 26	jf 25
			(025) ret      #262144
			(026) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 16
			(002) ldb      [20]
			(003) jeq      #0x84            jt 8	jf 4
			(004) ldb      [20]
			(005) jeq      #0x6             jt 8	jf 6
			(006) ldb      [20]
			(007) jeq      #0x11            jt 8	jf 16
			(008) ldh      [54]
			(009) jge      #0x1             jt 10	jf 12
			(010) ldh      [54]
			(011) jgt      #0x3ff           jt 12	jf 38
			(012) ldh      [56]
			(013) jge      #0x1             jt 14	jf 16
			(014) ldh      [56]
			(015) jgt      #0x3ff           jt 16	jf 38
			(016) ldh      [12]
			(017) jeq      #0x800           jt 18	jf 39
			(018) ldb      [23]
			(019) jeq      #0x84            jt 24	jf 20
			(020) ldb      [23]
			(021) jeq      #0x6             jt 24	jf 22
			(022) ldb      [23]
			(023) jeq      #0x11            jt 24	jf 39
			(024) ldh      [20]
			(025) jset     #0x1fff          jt 39	jf 26
			(026) ldxb     4*([14]&0xf)
			(027) ldh      [x + 14]
			(028) jge      #0x1             jt 29	jf 32
			(029) ldxb     4*([14]&0xf)
			(030) ldh      [x + 14]
			(031) jgt      #0x3ff           jt 32	jf 38
			(032) ldxb     4*([14]&0xf)
			(033) ldh      [x + 16]
			(034) jge      #0x1             jt 35	jf 39
			(035) ldxb     4*([14]&0xf)
			(036) ldh      [x + 16]
			(037) jgt      #0x3ff           jt 39	jf 38
			(038) ret      #262144
			(039) ret      #0
			',
	}, # portrange
	{
		name => 'src_portrange',
		DLT => 'EN10MB',
		aliases => [
			'src portrange 1-1023',
			'src portrange 1023-1',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 8
			(002) ldb      [20]
			(003) jeq      #0x84            jt 6	jf 4
			(004) jeq      #0x6             jt 6	jf 5
			(005) jeq      #0x11            jt 6	jf 20
			(006) ldh      [54]
			(007) jge      #0x1             jt 18	jf 20
			(008) jeq      #0x800           jt 9	jf 20
			(009) ldb      [23]
			(010) jeq      #0x84            jt 13	jf 11
			(011) jeq      #0x6             jt 13	jf 12
			(012) jeq      #0x11            jt 13	jf 20
			(013) ldh      [20]
			(014) jset     #0x1fff          jt 20	jf 15
			(015) ldxb     4*([14]&0xf)
			(016) ldh      [x + 14]
			(017) jge      #0x1             jt 18	jf 20
			(018) jgt      #0x3ff           jt 20	jf 19
			(019) ret      #262144
			(020) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 12
			(002) ldb      [20]
			(003) jeq      #0x84            jt 8	jf 4
			(004) ldb      [20]
			(005) jeq      #0x6             jt 8	jf 6
			(006) ldb      [20]
			(007) jeq      #0x11            jt 8	jf 12
			(008) ldh      [54]
			(009) jge      #0x1             jt 10	jf 12
			(010) ldh      [54]
			(011) jgt      #0x3ff           jt 12	jf 28
			(012) ldh      [12]
			(013) jeq      #0x800           jt 14	jf 29
			(014) ldb      [23]
			(015) jeq      #0x84            jt 20	jf 16
			(016) ldb      [23]
			(017) jeq      #0x6             jt 20	jf 18
			(018) ldb      [23]
			(019) jeq      #0x11            jt 20	jf 29
			(020) ldh      [20]
			(021) jset     #0x1fff          jt 29	jf 22
			(022) ldxb     4*([14]&0xf)
			(023) ldh      [x + 14]
			(024) jge      #0x1             jt 25	jf 29
			(025) ldxb     4*([14]&0xf)
			(026) ldh      [x + 14]
			(027) jgt      #0x3ff           jt 29	jf 28
			(028) ret      #262144
			(029) ret      #0
			',
	}, # src_portrange
	{
		name => 'dst_portrange',
		DLT => 'EN10MB',
		aliases => [
			'dst portrange 1-1023',
			'dst portrange 1023-1',
		],
		opt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 8
			(002) ldb      [20]
			(003) jeq      #0x84            jt 6	jf 4
			(004) jeq      #0x6             jt 6	jf 5
			(005) jeq      #0x11            jt 6	jf 20
			(006) ldh      [56]
			(007) jge      #0x1             jt 18	jf 20
			(008) jeq      #0x800           jt 9	jf 20
			(009) ldb      [23]
			(010) jeq      #0x84            jt 13	jf 11
			(011) jeq      #0x6             jt 13	jf 12
			(012) jeq      #0x11            jt 13	jf 20
			(013) ldh      [20]
			(014) jset     #0x1fff          jt 20	jf 15
			(015) ldxb     4*([14]&0xf)
			(016) ldh      [x + 16]
			(017) jge      #0x1             jt 18	jf 20
			(018) jgt      #0x3ff           jt 20	jf 19
			(019) ret      #262144
			(020) ret      #0
			',
		unopt => '
			(000) ldh      [12]
			(001) jeq      #0x86dd          jt 2	jf 12
			(002) ldb      [20]
			(003) jeq      #0x84            jt 8	jf 4
			(004) ldb      [20]
			(005) jeq      #0x6             jt 8	jf 6
			(006) ldb      [20]
			(007) jeq      #0x11            jt 8	jf 12
			(008) ldh      [56]
			(009) jge      #0x1             jt 10	jf 12
			(010) ldh      [56]
			(011) jgt      #0x3ff           jt 12	jf 28
			(012) ldh      [12]
			(013) jeq      #0x800           jt 14	jf 29
			(014) ldb      [23]
			(015) jeq      #0x84            jt 20	jf 16
			(016) ldb      [23]
			(017) jeq      #0x6             jt 20	jf 18
			(018) ldb      [23]
			(019) jeq      #0x11            jt 20	jf 29
			(020) ldh      [20]
			(021) jset     #0x1fff          jt 29	jf 22
			(022) ldxb     4*([14]&0xf)
			(023) ldh      [x + 16]
			(024) jge      #0x1             jt 25	jf 29
			(025) ldxb     4*([14]&0xf)
			(026) ldh      [x + 16]
			(027) jgt      #0x3ff           jt 29	jf 28
			(028) ret      #262144
			(029) ret      #0
			',
	}, # dst_portrange
	{
		name => 'byte_eq',
		DLT => 'IPV4',
		aliases => [
			'byte 8 = 5',
			'byte 8 == 5',
		],
		unopt => '
			(000) ldb      [8]
			(001) jeq      #0x5             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # byte_eq
	{
		name => 'byte_lt',
		DLT => 'IPV4',
		aliases => ['byte 8 < 5'],
		unopt => '
			(000) ldb      [8]
			(001) jge      #0x5             jt 2	jf 3
			(002) ret      #0
			(003) ret      #262144
			',
	}, # byte_lt
	{
		name => 'byte_gt',
		DLT => 'IPV4',
		aliases => ['byte 8 > 5'],
		unopt => '
			(000) ldb      [8]
			(001) jgt      #0x5             jt 2	jf 3
			(002) ret      #262144
			(003) ret      #0
			',
	}, # byte_gt
	{
		name => 'byte_or',
		DLT => 'IPV4',
		aliases => ['byte 8 | 5'],
		unopt => '
			(000) ldb      [8]
			(001) or       #0x5
			(002) jeq      #0x0             jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # byte_or
	{
		name => 'byte_and',
		DLT => 'IPV4',
		aliases => ['byte 8 & 5'],
		unopt => '
			(000) ldb      [8]
			(001) and      #0x5
			(002) jeq      #0x0             jt 3	jf 4
			(003) ret      #0
			(004) ret      #262144
			',
	}, # byte_and
);

# In apply_blocks each test block always generates two tests: optimized and
# unoptimized.  (Small tests often produce short bytecode that is already
# optimal, in which case testing the "optimized" version again is a duplicate
# work.  However, it is not clear yet what would be the right way to avoid the
# duplicate work without creating gaps in the test coverage.)  A test block is
# a hash, where the keys have the following meaning:
#
# * name, expr and netmask: same as in accept_blocks above
# * savefile (mandatory, string): the file in tests/filter/ to use with
#   "filtertest -r", this should not have too many packets
# * results (mandatory, array): the list of program filter results to expect
my @apply_blocks = (
	{
		name => 'pppoed_nullary_on_ctp',
		savefile => 'loopback.pcap',
		expr => 'pppoed',
		results => [0, 0, 0, 0, 0, 0],
	},
	{
		name => 'pppoed_nullary_on_pppoed',
		savefile => 'pppoe.pcap',
		expr => 'pppoed',
		results => [1508],
	},
	{
		name => 'pppoed_nullary_on_pppoes',
		savefile => 'pppoes.pcap',
		expr => 'pppoed',
		results => [0, 0],
	},
	{
		name => 'pppoes_nullary_on_ctp',
		savefile => 'loopback.pcap',
		expr => 'pppoes',
		results => [0, 0, 0, 0, 0, 0],
	},
	{
		name => 'pppoes_nullary_on_pppoed',
		savefile => 'pppoe.pcap',
		expr => 'pppoes',
		results => [0],
	},
	{
		name => 'pppoes_nullary_on_pppoes',
		savefile => 'pppoes.pcap',
		expr => 'pppoes',
		results => [2000, 2000],
	},
	{
		name => 'pppoes_unary_on_ctp',
		savefile => 'loopback.pcap',
		expr => 'pppoes 0x3b',
		results => [0, 0, 0, 0, 0, 0],
	},
	{
		name => 'pppoes_unary_on_pppoed',
		savefile => 'pppoe.pcap',
		expr => 'pppoes 0x3b',
		results => [0],
	},
	{
		name => 'pppoes_unary_on_pppoes',
		savefile => 'pppoes.pcap',
		expr => 'pppoes 0x3b',
		results => [0, 2000],
	},
	{
		name => 'decnet_on_pppoed',
		savefile => 'pppoe.pcap',
		expr => 'decnet',
		results => [0],
	},
	{
		name => 'decnet_on_decnet',
		savefile => 'decnet.pcap',
		expr => 'decnet',
		# This tests EtherType, so every packet matches.
		results => [65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535],
	},
	{
		name => 'decnet_src_1_1_on_pppoed',
		savefile => 'pppoe.pcap',
		expr => 'decnet src 1.1',
		results => [0],
	},
	{
		name => 'decnet_dst_1_1_on_pppoed',
		savefile => 'pppoe.pcap',
		expr => 'decnet dst 1.1',
		results => [0],
	},

	# This tests a DECnet address, which in the current implementation works
	# for data packets only.  The first packet is an Ethernet Endnode Hello
	# message, so it does not match even though the packet is from node 1.1.
	{
		name => 'decnet_src_1_1_on_decnet',
		savefile => 'decnet.pcap',
		expr => 'decnet src 1.1',
		results => [0, 65535, 65535, 65535, 65535, 65535, 65535, 65535],
	},
	{
		name => 'decnet_dst_1_1_on_decnet',
		savefile => 'decnet.pcap',
		expr => 'decnet dst 1.1',
		results => [0, 65535, 65535, 65535, 65535, 65535, 65535, 65535],
	},
	{
		name => 'decnet_src_not_1_1_on_decnet',
		savefile => 'decnet.pcap',
		expr => 'decnet src not 1.1',
		results => [65535, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'decnet_dst_not_1_1_on_decnet',
		savefile => 'decnet.pcap',
		expr => 'decnet dst not 1.1',
		results => [65535, 0, 0, 0, 0, 0, 0, 0],
	},

	# The first result is correct from a formal point of view, but the actual
	# reason is the same as above.
	{
		name => 'decnet_src_63_1023_on_decnet',
		savefile => 'decnet.pcap',
		expr => 'decnet src 63.1023',
		results => [0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'decnet_dst_63_1023_on_decnet',
		savefile => 'decnet.pcap',
		expr => 'decnet dst 63.1023',
		results => [0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'decnet_src_not_63_1023_on_decnet',
		savefile => 'decnet.pcap',
		expr => 'decnet src not 63.1023',
		results => [65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535],
	},
	{
		name => 'decnet_dst_not_63_1023_on_decnet',
		savefile => 'decnet.pcap',
		expr => 'decnet dst not 63.1023',
		results => [65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535],
	},

	# The meaning of this expression is NOT the intuitive "any DECnet packets
	# that do not have the source address set to 1.1", but this is not
	# specific to DECnet.  Let's test it anyway.
	{
		name => 'decnet_src_not_1_1_on_pppoed',
		savefile => 'pppoe.pcap',
		expr => 'decnet src not 1.1',
		results => [1508],
	},
	{
		name => 'decnet_dst_not_1_1_on_pppoed',
		savefile => 'pppoe.pcap',
		expr => 'decnet dst not 1.1',
		results => [1508],
	},
	{
		name => 'decnet_src_not_63_1023_on_pppoed',
		savefile => 'pppoe.pcap',
		expr => 'decnet src not 63.1023',
		results => [1508],
	},
	{
		name => 'decnet_dst_not_63_1023_on_pppoed',
		savefile => 'pppoe.pcap',
		expr => 'decnet dst not 63.1023',
		results => [1508],
	},

	{
		name => 'decnet_src_63_1023_on_pppoed',
		savefile => 'pppoe.pcap',
		expr => 'decnet src 63.1023',
		results => [0],
	},
	{
		name => 'decnet_dst_63_1023_on_pppoed',
		savefile => 'pppoe.pcap',
		expr => 'decnet src 63.1023',
		results => [0],
	},

	{
		name => 'dpc_eq_1',
		savefile => 'isup_load_generator.pcap',
		expr => 'dpc == 1',
		results => [0, 279, 0, 279, 279, 0, 279, 0, 279, 0],
	},
	{
		name => 'dpc_lt_2',
		savefile => 'isup_load_generator.pcap',
		expr => 'dpc < 2',
		results => [0, 279, 0, 279, 279, 0, 279, 0, 279, 0],
	},
	{
		name => 'dpc_eq_2',
		savefile => 'isup_load_generator.pcap',
		expr => 'dpc == 2',
		results => [279, 0, 279, 0, 0, 279, 0, 279, 0, 279],
	},
	{
		name => 'dpc_gt_2',
		savefile => 'isup_load_generator.pcap',
		expr => 'dpc > 2',
		results => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'dpc_le_2',
		savefile => 'isup_load_generator.pcap',
		expr => 'dpc <= 2',
		results => [279, 279, 279, 279, 279, 279, 279, 279, 279, 279],
	},
	{
		name => 'dpc_ne_0',
		savefile => 'isup_load_generator.pcap',
		expr => 'dpc != 0',
		results => [279, 279, 279, 279, 279, 279, 279, 279, 279, 279],
	},
	{
		name => 'opc_eq_2',
		savefile => 'isup_load_generator.pcap',
		expr => 'opc == 2',
		results => [0, 279, 0, 279, 279, 0, 279, 0, 279, 0],
	},
	{
		name => 'opc_ge_2',
		savefile => 'isup_load_generator.pcap',
		expr => 'opc >= 2',
		results => [0, 279, 0, 279, 279, 0, 279, 0, 279, 0],
	},
	{
		name => 'opc_gt_2',
		savefile => 'isup_load_generator.pcap',
		expr => 'opc > 2',
		results => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'opc_le_2',
		savefile => 'isup_load_generator.pcap',
		expr => 'opc <= 2',
		results => [279, 279, 279, 279, 279, 279, 279, 279, 279, 279],
	},
	{
		name => 'opc_ne_0',
		savefile => 'isup_load_generator.pcap',
		expr => 'opc != 0',
		results => [279, 279, 279, 279, 279, 279, 279, 279, 279, 279],
	},
	{
		name => 'sls_eq_9',
		savefile => 'isup_load_generator.pcap',
		expr => 'sls == 9',
		results => [279, 279, 279, 279, 279, 279, 279, 279, 279, 279],
	},
	{
		name => 'sls_ne_9',
		savefile => 'isup_load_generator.pcap',
		expr => 'sls != 9',
		results => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'link_host_c0_on_arcnet',
		savefile => 'bacnet-arcnet-linux.pcap',
		expr => 'link host $c0',
		results => [65535, 65535, 0, 65535, 65535, 65535, 65535, 65535, 65535, 0],
	},
	{
		name => 'link_src_host_c0_on_arcnet',
		savefile => 'bacnet-arcnet-linux.pcap',
		expr => 'link src host $c0',
		results => [65535, 65535, 0, 65535, 0, 65535, 0, 65535, 0, 0],
	},
	{
		name => 'link_dst_host_c0_on_arcnet',
		savefile => 'bacnet-arcnet-linux.pcap',
		expr => 'link dst host $c0',
		results => [0, 0, 0, 0, 65535, 0, 65535, 0, 65535, 0],
	},
	{
		name => 'link_host_not_c0_on_arcnet',
		savefile => 'bacnet-arcnet-linux.pcap',
		expr => 'link host not $c0',
		results => [0, 0, 65535, 0, 0, 0, 0, 0, 0, 65535],
	},
	# This primitive is not specific to ARCnet protocol ID, so the
	# difference between RFC 1051 and RFC 1201 is irrelevant.
	{
		name => 'arcnet_broadcast',
		savefile => 'arcnet-rfc1201-arp-icmp-http.pcap',
		expr => 'broadcast',
		results => [65535, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	# For RFC 1051 encoding the current implementation is able to tell ARP
	# and IPv4 from other protocols, but uses incorrect offsets for the
	# protocol headers (bug 1516).  This is why "(arp|ip)" work correctly,
	# but "(arp|ip) (src|dst) (host|net)" do not.  The latter will likely
	# not be fixed, so do not test it.
	# cases.
	{
		name => 'rfc1051_arp',
		savefile => 'arcnet-rfc1051-arp-icmp-http.pcap',
		expr => 'arp',
		results => [65535, 65535, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'rfc1051_ip',
		savefile => 'arcnet-rfc1051-arp-icmp-http.pcap',
		expr => 'ip',
		results => [0, 0, 65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535],
	},
	# For RFC 1201 encoding the current implementation works correctly for
	# IPv4, but ARP header parsing is incorrect (bug 1515).  The latter
	# may be fixed later, so test it such that if somebody fixes the bug
	# later, the test case will fail and make it obvious that the fixed
	# code needs a working test.
	{
		name => 'rfc1201_arp',
		savefile => 'arcnet-rfc1201-arp-icmp-http.pcap',
		expr => 'arp',
		results => [65535, 65535, 0, 0, 0, 0, 65535, 65535, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'rfc1201_arp_src_host',
		savefile => 'arcnet-rfc1201-arp-icmp-http.pcap',
		expr => 'arp src host ***********',
		results => [0, 65535, 0, 0, 0, 0, 65535, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'rfc1201_ip',
		savefile => 'arcnet-rfc1201-arp-icmp-http.pcap',
		expr => 'ip',
		results => [0, 0, 65535, 65535, 65535, 65535, 0, 0, 65535, 65535, 65535, 65535, 65535, 65535, 65535],
	},
	{
		name => 'rfc1201_tcp_dst_port',
		savefile => 'arcnet-rfc1201-arp-icmp-http.pcap',
		expr => 'tcp dst port 80',
		results => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 65535, 0, 65535],
	},
	{
		name => 'ip_broadcast',
		savefile => 'dhcp-rfc3004.pcap',
		netmask => '*************',
		expr => 'ip broadcast',
		results => [262144, 0, 262144, 0],
	},
	{
		name => 'arp',
		savefile => 'isakmp4500.pcap',
		expr => 'arp',
		results => [1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'arp_host',
		savefile => 'isakmp4500.pcap',
		expr => 'arp host ***********',
		results => [1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'arp_src_1',
		savefile => 'isakmp4500.pcap',
		expr => 'arp src ***********',
		results => [1536, 0, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'arp_src_2',
		savefile => 'isakmp4500.pcap',
		expr => 'arp src **********',
		results => [0, 1536, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'arp_dst_1',
		savefile => 'isakmp4500.pcap',
		expr => 'arp dst ***********',
		results => [0, 1536, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'arp_dst_2',
		savefile => 'isakmp4500.pcap',
		expr => 'arp dst **********',
		results => [1536, 0, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'rarp',
		savefile => 'rarp_req_reply.pcapng',
		expr => 'rarp',
		results => [65535, 65535],
	},
	{
		name => 'rarp_host_1',
		savefile => 'rarp_req_reply.pcapng',
		expr => 'rarp host 0.0.0.0',
		results => [65535, 0],
	},
	{
		name => 'rarp_host_2',
		savefile => 'rarp_req_reply.pcapng',
		expr => 'rarp host *********0',
		results => [0, 65535],
	},
	{
		name => 'rarp_host_3',
		savefile => 'rarp_req_reply.pcapng',
		expr => 'rarp host *********',
		results => [0, 65535],
	},
	{
		name => 'rarp_src',
		savefile => 'rarp_req_reply.pcapng',
		expr => 'rarp src *********',
		results => [0, 65535],
	},
	{
		name => 'rarp_dst',
		savefile => 'rarp_req_reply.pcapng',
		expr => 'rarp dst *********0',
		results => [0, 65535],
	},
	{
		name => 'ip_1',
		savefile => 'isakmp4500.pcap',
		expr => 'ip',
		results => [0, 0, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536],
	},
	{
		name => 'ip_2',
		savefile => 'vrrp.pcap',
		expr => 'ip',
		results => [0, 0, 65535, 65535, 65535, 65535, 0, 0, 65535, 65535, 65535, 0, 0, 65535, 65535]
	},
	{
		name => 'ip_host',
		savefile => 'isakmp4500.pcap',
		expr => 'ip host **********',
		results => [0, 0, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536],
	},
	{
		name => 'ip_src_1',
		savefile => 'isakmp4500.pcap',
		expr => 'ip src ***********',
		results => [0, 0, 1536, 0, 1536, 0, 1536, 0, 1536, 0],
	},
	{
		name => 'ip_src_2',
		savefile => 'dhcp-rfc3004.pcap',
		expr => 'ip src 0.0.0.0',
		results => [262144, 0, 262144, 0],
	},
	{
		name => 'ip_dst_1',
		savefile => 'isakmp4500.pcap',
		expr => 'ip dst ***********',
		results => [0, 0, 0, 1536, 0, 1536, 0, 1536, 0, 1536],
	},
	{
		name => 'ip_dst_2',
		savefile => 'dhcp-rfc3004.pcap',
		expr => 'ip dst ***********',
		results => [0, 262144, 0, 262144],
	},
	{
		name => 'ip6',
		savefile => 'vrrp.pcap',
		expr => 'ip6',
		results => [65535, 65535, 0, 0, 0, 0, 65535, 65535, 0, 0, 0, 65535, 65535, 0, 0],
	},
	{
		name => 'ip6_src_1',
		savefile => 'vrrp.pcap',
		expr => 'ip6 src fe80::d6ca:6dff:fe66:cf60',
		results => [65535, 65535, 0, 0, 0, 0, 65535, 65535, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'ip6_src_2',
		savefile => 'pim-packet-assortment.pcap',
		expr => 'ip6 src 10::1',
		results => [0, 0, 0, 0, 0, 0, 0, 65535, 65535, 65535],
	},
	{
		name => 'ip6_dst_1',
		savefile => 'vrrp.pcap',
		expr => 'ip6 dst ff02::12',
		results => [65535, 65535, 0, 0, 0, 0, 65535, 65535, 0, 0, 0, 65535, 65535, 0, 0],
	},
	{
		name => 'ip6_dst_2',
		savefile => 'pim-packet-assortment.pcap',
		expr => 'ip6 dst 10::1',
		results => [0, 0, 0, 0, 0, 0, 65535, 0, 0, 0],
	},
	{
		name => 'slip_inbound_on_invalid',
		savefile => 'slip-bad-direction.pcap',
		expr => 'inbound',
		results => [0],
	},
	{
		name => 'slip_outbound_on_invalid',
		savefile => 'slip-bad-direction.pcap',
		expr => 'outbound',
		results => [0],
	},
	{
		name => 'slip_inbound_on_rx',
		savefile => 'slip-compressed_sl_print-oobr.pcap',
		expr => 'inbound',
		results => [46],
	},
	{
		name => 'slip_outbound_on_rx',
		savefile => 'slip-compressed_sl_print-oobr.pcap',
		expr => 'outbound',
		results => [0],
	},
	{
		name => 'slip_inbound_on_tx',
		savefile => 'slip-sliplink_print-oobr.pcap',
		expr => 'inbound',
		results => [0],
	},
	{
		name => 'slip_outbound_on_tx',
		savefile => 'slip-sliplink_print-oobr.pcap',
		expr => 'outbound',
		results => [46],
	},
	{
		name => 'wlan_type_mgt',
		savefile => 'wpa2linkuppassphraseiswireshark.pcap',
		expr => 'wlan type mgt',
		results => [65536, 65536, 65536, 65536, 65536, 65536, 65536, 0, 0, 0, 0, 0, 0, 0, 0, 65536],
	},
	{
		name => 'wlan_subtype_beacon',
		savefile => 'Network_Join_Nokia_Mobile.pcap',
		expr => 'wlan subtype beacon',
		results => [0, 0, 0, 0, 0, 0, 0, 0, 2344, 2344],
	},
	{
		name => 'wlan_subtype_probe_req',
		savefile => 'Network_Join_Nokia_Mobile.pcap',
		expr => 'wlan subtype probe-req',
		results => [2344, 0, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'wlan_subtype_probe_resp',
		savefile => 'Network_Join_Nokia_Mobile.pcap',
		expr => 'wlan subtype probe-resp',
		results => [0, 2344, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'wlan_subtype_auth',
		savefile => 'wpa2linkuppassphraseiswireshark.pcap',
		expr => 'wlan subtype auth',
		results => [0, 0, 0, 65536, 65536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'wlan_subtype_deauth',
		savefile => 'Network_Join_Nokia_Mobile.pcap',
		expr => 'wlan subtype deauth',
		results => [0, 0, 0, 0, 0, 0, 2344, 0, 0, 0],
	},
	{
		name => 'wlan_subtype_assoc_req',
		savefile => 'wpa2linkuppassphraseiswireshark.pcap',
		expr => 'wlan subtype assoc-req',
		results => [0, 0, 0, 0, 0, 65536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'wlan_subtype_assoc_resp',
		savefile => 'wpa2linkuppassphraseiswireshark.pcap',
		expr => 'wlan subtype assoc-resp',
		results => [0, 0, 0, 0, 0, 0, 65536, 0, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'wlan_subtype_disassoc',
		savefile => 'wpa2linkuppassphraseiswireshark.pcap',
		expr => 'wlan subtype disassoc',
		results => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 65536],
	},
	{
		name => 'wlan_type_ctl',
		savefile => 'Network_Join_Nokia_Mobile.pcap',
		expr => 'wlan type ctl',
		results => [0, 0, 0, 2344, 0, 2344, 0, 2344, 0, 0],
	},
	{
		name => 'wlan_subtype_ack',
		savefile => 'Network_Join_Nokia_Mobile.pcap',
		expr => 'wlan subtype ack',
		results => [0, 0, 0, 2344, 0, 2344, 0, 2344, 0, 0],
	},
	{
		name => 'wlan_subtype_cts',
		savefile => 'wpa-Induction.pcap',
		expr => 'wlan subtype cts',
		results => [0, 0, 0, 0, 65535, 0, 0, 65535, 0, 0],
	},
	{
		name => 'wlan_type_data',
		savefile => 'Network_Join_Nokia_Mobile.pcap',
		expr => 'wlan type data',
		results => [0, 0, 2344, 0, 2344, 0, 0, 0, 0, 0],
	},
	{
		name => 'wlan_subtype_qos_data',
		savefile => 'wpa2linkuppassphraseiswireshark.pcap',
		expr => 'wlan subtype qos-data',
		results => [0, 0, 0, 0, 0, 0, 0, 65536, 65536, 65536, 65536, 65536, 65536, 65536, 65536, 0],
	},
	{
		name => 'wlan_subtype_data',
		savefile => 'Network_Join_Nokia_Mobile.pcap',
		expr => 'wlan subtype data',
		results => [0, 0, 2344, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'wlan_subtype_null',
		savefile => 'Network_Join_Nokia_Mobile.pcap',
		expr => 'wlan subtype null',
		results => [0, 0, 0, 0, 2344, 0, 0, 0, 0, 0],
	},
	{
		name => 'wlan_dir_tods_IEEE802_11',
		savefile => 'Network_Join_Nokia_Mobile.pcap',
		expr => 'wlan dir tods',
		results => [0, 0, 2344, 0, 2344, 0, 0, 0, 0, 0],
	},
	{
		name => 'wlan_dir_fromds_IEEE802_11_RADIO',
		savefile => 'wpa2linkuppassphraseiswireshark.pcap',
		expr => 'wlan dir fromds',
		results => [0, 0, 0, 0, 0, 0, 0, 65536, 0, 65536, 0, 65536, 0, 65536, 0, 0],
	},
	{
		name => 'wlan_subtype_ack_PPI',
		savefile => 'http_PPI.pcap',
		expr => 'wlan subtype ack',
		results => [0, 65535, 0, 65535, 0],
	},
	{
		name => 'wlan_dir_nods_PPI',
		savefile => 'http_PPI.pcap',
		expr => 'wlan dir nods',
		results => [0, 65535, 0, 65535, 0],
	},
	{
		name => 'iso_proto_isis_EN10MB',
		savefile => 'ISIS_external_lsp.pcap',
		expr => 'iso proto \isis',
		results => [8192, 8192, 8192, 8192, 8192, 8192, 8192, 8192, 8192, 8192],
	},
	{
		name => 'iso_proto_esis_EN10MB',
		savefile => 'ISIS_external_lsp.pcap',
		expr => 'iso proto \esis',
		results => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'csnp_EN10MB',
		savefile => 'ISIS_external_lsp.pcap',
		expr => 'csnp',
		results => [8192, 0, 0, 0, 0, 8192, 0, 0, 0, 0],
	},
	{
		name => 'iso_proto_isis_C_HDLC',
		savefile => 'ISIS_p2p_adjacency.pcap',
		expr => 'iso proto \isis',
		results => [8192, 8192, 8192, 8192, 8192, 8192, 8192, 8192, 8192, 8192, 8192, 8192, 8192, 8192],
	},
	{
		name => 'iso_proto_esis_C_HDLC',
		savefile => 'ISIS_p2p_adjacency.pcap',
		expr => 'iso proto \esis',
		results => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'csnp_C_HDLC',
		savefile => 'ISIS_p2p_adjacency.pcap',
		expr => 'csnp',
		results => [0, 0, 0, 0, 0, 8192, 8192, 8192, 8192, 0, 0, 0, 0, 0],
	},
	{
		name => 'vxlan',
		savefile => 'vxlan.pcap',
		expr => 'vxlan',
		results => [1500, 1500, 1500, 1500, 1500, 1500, 1500, 1500, 1500, 1500],
	},
	{
		name => 'vxlan_100',
		savefile => 'vxlan.pcap',
		expr => 'vxlan 100',
		results => [1500, 1500, 1500, 1500, 1500, 1500, 1500, 1500, 1500, 1500],
	},
	{
		name => 'vxlan_101',
		savefile => 'vxlan.pcap',
		expr => 'vxlan 101',
		results => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'geneve',
		savefile => 'geneve.pcap',
		expr => 'geneve',
		results => [262144, 262144, 262144, 262144, 262144, 262144, 262144, 262144, 262144, 262144],
	},
	{
		name => 'geneve_10',
		savefile => 'geneve.pcap',
		expr => 'geneve 10',
		results => [262144, 0, 0, 262144, 0, 262144, 0, 0, 262144, 0],
	},
	{
		name => 'geneve_11',
		savefile => 'geneve.pcap',
		expr => 'geneve 11',
		results => [0, 262144, 262144, 0, 262144, 0, 262144, 262144, 0, 262144],
	},
	{
		name => 'ip_protochain_17_deepstack',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		savefile => 'ipv6_ext_headers.pcap',
		expr => 'ip protochain 17',
		results => [0],
	},
	{
		name => 'ip6_protochain_17_deepstack',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		savefile => 'ipv6_ext_headers.pcap',
		expr => 'ip6 protochain 17',
		results => [65535],
	},
	{
		name => 'ip6_protochain_51_tunnel',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		savefile => 'AH-IPcomp-IPv6.pcap',
		expr => 'ip6 protochain 51',
		# AH is the first protocol header.
		results => [65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535],
	},
	{
		name => 'ip6_protochain_41_tunnel',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		savefile => 'AH-IPcomp-IPv6.pcap',
		expr => 'ip6 protochain 41',
		# When an IPComp header is present, it precedes the inner
		# IPv6 header, which no longer matches.
		results => [65535, 65535, 0, 0, 0, 0, 0, 65535, 0, 0, 65535, 65535, 65535, 65535, 65535],
	},
	{
		name => 'ip6_protochain_108_tunnel',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		savefile => 'AH-IPcomp-IPv6.pcap',
		expr => 'ip6 protochain 108',
		results => [0, 0, 65535, 65535, 65535, 65535, 65535, 0, 65535, 65535, 0, 0, 0, 0, 0],
	},
	{
		name => 'ip6_protochain_6_tunnel',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		savefile => 'AH-IPcomp-IPv6.pcap',
		expr => 'ip6 protochain 6',
		# All TCP packets have the TCP header behind the inner IPv6 header (41).
		results => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'ip6_protochain_17_mixed',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		savefile => 'ipv6_ah_modes.pcap',
		expr => 'ip6 protochain 17',
		results => [65535, 0],
	},
	{
		name => 'ip6_protochain_41_mixed',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		savefile => 'ipv6_ah_modes.pcap',
		expr => 'ip6 protochain 41',
		results => [0, 65535],
	},
	{
		name => 'ip_protochain_51_tunnel',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		savefile => 'AH-IPcomp-IPv4.pcap',
		expr => 'ip protochain 51',
		results => [65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535],
	},
	{
		name => 'ip_protochain_4_tunnel',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		savefile => 'AH-IPcomp-IPv4.pcap',
		expr => 'ip protochain 4',
		results => [65535, 65535, 0, 0, 0, 65535, 0, 65535, 65535, 65535],
	},
	{
		name => 'ip_protochain_108_tunnel',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		savefile => 'AH-IPcomp-IPv4.pcap',
		expr => 'ip protochain 108',
		results => [0, 0, 65535, 65535, 65535, 0, 65535, 0, 0, 0],
	},
	{
		name => 'ip_protochain_51_transport',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		savefile => 'ah-ipip-ping.pcap',
		expr => 'ip protochain 51',
		results => [65535, 65535, 65535, 65535],
	},
	{
		name => 'ip_protochain_1_transport',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		savefile => 'ah-ipip-ping.pcap',
		expr => 'ip protochain 1',
		results => [65535, 0, 0, 0],
	},
	{
		name => 'llc',
		savefile => 'llc.pcap',
		expr => 'llc',
		results => [262144, 262144, 0, 262144, 0, 262144, 0, 0, 262144, 262144],
	},
	{
		name => 'llc_i',
		savefile => 'llc.pcap',
		expr => 'llc i',
		results => [0, 0, 0, 0, 0, 0, 0, 0, 0, 262144],
	},
	{
		name => 'llc_s',
		savefile => 'llc.pcap',
		expr => 'llc s',
		results => [0, 0, 0, 0, 0, 262144, 0, 0, 262144, 0],
	},
	{
		name => 'llc_rr',
		savefile => 'llc.pcap',
		expr => 'llc rr',
		results => [0, 0, 0, 0, 0, 262144, 0, 0, 262144, 0],
	},
	{
		name => 'llc_rnr',
		savefile => 'llc.pcap',
		expr => 'llc rnr',
		results => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'llc_u',
		savefile => 'llc.pcap',
		expr => 'llc u',
		results => [262144, 262144, 0, 262144, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'llc_ui',
		savefile => 'llc.pcap',
		expr => 'llc ui',
		results => [262144, 0, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'llc_sabme',
		savefile => 'llc.pcap',
		expr => 'llc sabme',
		results => [0, 262144, 0, 0, 0, 0, 0, 0, 0, 0],
	},
	{
		name => 'llc_ua',
		savefile => 'llc.pcap',
		expr => 'llc ua',
		results => [0, 0, 0, 262144, 0, 0, 0, 0, 0, 0],
	},
);

# * name, DLT, expr, netmask and skip: same as in accept_blocks above
# * errstr (mandatory, string): a substring that must appear in standard error
#   from filtertest (this verifies that the reason for rejecting the expression
#   is what the test expects, rather than some unrelated cause).
my @reject_tests = (
	{
		name => 'ether_host',
		DLT => 'EN10MB',
		expr => 'ether ab:cd:ef:0g:00:00',
		errstr => 'bogus ethernet address',
	},
	{
		name => 'pppoes_value',
		DLT => 'EN10MB',
		expr => 'pppoes 65536',
		errstr => 'PPPoE session number 65536 greater than maximum 65535',
	},
	{
		name => 'mtp2_sio',
		DLT => 'MTP2',
		expr => 'sio 256',
		errstr => 'sio 256 greater than maximum 255',
	},
	{
		name => 'mtp3_dpc',
		DLT => 'MTP2',
		expr => 'dpc 16384',
		errstr => 'dpc 16384 greater than maximum 16383',
	},
	{
		name => 'mtp3_opc',
		DLT => 'MTP2',
		expr => 'opc 16384',
		errstr => 'opc 16384 greater than maximum 16383',
	},
	{
		name => 'mtp3_sls',
		DLT => 'MTP2',
		expr => 'sls 16',
		errstr => 'sls 16 greater than maximum 15',
	},
	{
		name => 'mtp2_hsio',
		DLT => 'MTP2',
		expr => 'hsio 256',
		errstr => 'hsio 256 greater than maximum 255',
	},
	{
		name => 'mtp3_hdpc',
		DLT => 'MTP2',
		expr => 'hdpc 16384',
		errstr => 'hdpc 16384 greater than maximum 16383',
	},
	{
		name => 'mtp3_hopc',
		DLT => 'MTP2',
		expr => 'hopc 16384',
		errstr => 'hopc 16384 greater than maximum 16383',
	},
	{
		name => 'mtp3_hsls',
		DLT => 'MTP2',
		expr => 'hsls 16',
		errstr => 'hsls 16 greater than maximum 15',
	},
	{
		name => 'atm_vpi',
		DLT => 'SUNATM',
		expr => 'vpi 256',
		errstr => 'VPI 256 greater than maximum 255',
	},
	{
		name => 'atm_vci',
		DLT => 'SUNATM',
		expr => 'vci 65536',
		errstr => 'VCI 65536 greater than maximum 65535',
	},
	{
		name => 'wlan_type',
		DLT => 'IEEE802_11',
		# Type value out of range.
		expr => 'wlan type 16',
		errstr => 'invalid 802.11 type value',
	},
	{
		name => 'wlan_subtype',
		DLT => 'IEEE802_11',
		# Invalid syntax (numeric subtype is ambiguous and requires a type).
		expr => 'wlan subtype 0',
		errstr => 'syntax error',
	},
	{
		name => 'wlan_type_subtype',
		DLT => 'IEEE802_11',
		# Subtype value out of range.
		expr => 'wlan type 0 subtype 0xff',
		errstr => 'invalid 802.11 subtype value',
	},
	{
		name => 'wlan_dir_invalid1',
		DLT => 'IEEE802_11',
		expr => 'wlan dir abc',
		errstr => 'unknown 802.11 direction',
	},
	{
		name => 'wlan_dir_invalid2',
		DLT => 'IEEE802_11',
		expr => 'wlan dir 4',
		errstr => 'invalid 802.11 direction',
	},
	{
		name => 'pppoed_unary',
		DLT => 'EN10MB',
		expr => 'pppoed 1234',
		errstr => 'syntax error',
	},
	{
		name => 'llc_noneth',
		DLT => 'RAW',
		expr => 'llc',
		errstr => 'not supported',
	},
	{
		name => 'decnet_area',
		DLT => 'EN10MB',
		expr => 'decnet host 64.120',
		errstr => 'invalid DECnet address',
	},
	{
		name => 'decnet_node',
		DLT => 'EN10MB',
		expr => 'decnet host 17.1024',
		errstr => 'invalid DECnet address',
	},
	{
		name => 'ip_host',
		DLT => 'RAW',
		expr => 'ip host 256.256.256.256',
		errstr => 'invalid IPv4 address',
	},
	{
		name => 'ip6_host_toolong',
		DLT => 'RAW',
		expr => 'ip6 host fe80:0:0:0:0:0:0:0:0',
		errstr => 'syntax error',
	},
	{
		name => 'ip6_host_ipv4addr',
		DLT => 'RAW',
		expr => 'ip6 host *******',
		errstr => '\'ip6\' is not a valid qualifier for \'ip host\'',
	},
	{
		name => 'ip6_host_ipv4name',
		skip => skip_no_hosts(),
		DLT => 'RAW',
		expr => 'ip6 host noeth-ipv4-noipv6.host123.libpcap.test',
		errstr => "unknown host \'noeth-ipv4-noipv6.host123.libpcap.test' for specified address family",
	},
# This test has been flaky because it depends on an external effect (DNS
# lookup), which sometimes times out.  Let's disable it until there is a good
# way to address it.
#	{
#		name => 'ip6_host_nonhex',
#		DLT => 'RAW',
#		expr => 'ip6 host fe80:0:0:0:0:0:0:g',
#		errstr => 'unknown host',
#	},
	{
		name => 'ip_net_bits1',
		DLT => 'RAW',
		expr => 'net 192.168/8',
		errstr => 'non-network bits set in',
	},
	{
		name => 'ip_net_bits2',
		DLT => 'RAW',
		expr => 'net 192.168 mask *********',
		errstr => 'non-network bits set in',
	},
	{
		name => 'ip_net_nonhid1',
		DLT => 'RAW',
		expr => 'net 10 mask *********',
		errstr => 'syntax error',
	},
	{
		name => 'ip_net_nonhid2',
		DLT => 'RAW',
		expr => 'net 10/8',
		errstr => 'syntax error',
	},
	{
		name => 'ip_net_mask_nonhid',
		DLT => 'RAW',
		expr => 'net 10.0.0.0 mask 255',
		errstr => 'syntax error',
	},
	{
		name => 'ip_net_nonhid_mask_nonhid',
		DLT => 'RAW',
		expr => 'net 10 mask 255',
		errstr => 'syntax error',
	},
	{
		name => 'ip_net_unknown',
		DLT => 'EN10MB',
		expr => "ip net ${nonexistent}",
		errstr => "unknown network '${nonexistent}'",
	},
	{
		name => 'ip_src_net_unknown',
		DLT => 'EN10MB',
		expr => "ip src net ${nonexistent}",
		errstr => "unknown network '${nonexistent}'",
	},
	{
		name => 'ip_dst_net_unknown',
		DLT => 'EN10MB',
		expr => "ip dst net ${nonexistent}",
		errstr => "unknown network '${nonexistent}'",
	},
	{
		name => 'net_unknown',
		DLT => 'EN10MB',
		expr => "net ${nonexistent}",
		errstr => "unknown network '${nonexistent}'",
	},
	{
		name => 'src_net_unknown',
		DLT => 'EN10MB',
		expr => "src net ${nonexistent}",
		errstr => "unknown network '${nonexistent}'",
	},
	{
		name => 'dst_net_unknown',
		DLT => 'EN10MB',
		expr => "dst net ${nonexistent}",
		errstr => "unknown network '${nonexistent}'",
	},
	{
		name => 'src_or_dst_net_unknown',
		DLT => 'EN10MB',
		expr => "src or dst net ${nonexistent}",
		errstr => "unknown network '${nonexistent}'",
	},
	{
		name => 'ip6_net_prefix',
		DLT => 'RAW',
		expr => 'ip6 net fe80:0:0:0:0:0:0:0:0/64',
		errstr => 'syntax error',
	},
	{
		name => 'ip6_net_masklen',
		DLT => 'RAW',
		expr => 'ip6 net fe80:0:0:0:0:0:0:0/129',
		errstr => 'mask length must be <= 128',
	},
	{
		name => 'ip6_net_bits',
		DLT => 'RAW',
		expr => 'net fe80:1234:5678::/32',
		errstr => 'non-network bits set in',
	},
	{
		name => 'tcp_port',
		DLT => 'IPV4',
		expr => 'tcp port 70000',
		errstr => 'illegal port number',
	},
	{
		name => 'udp_port',
		DLT => 'IPV4',
		expr => 'udp port 70000',
		errstr => 'illegal port number',
	},
	{
		name => 'sctp_port',
		DLT => 'IPV4',
		expr => 'sctp port 70000',
		errstr => 'illegal port number',
	},
	{
		name => 'tcp_portrange1',
		DLT => 'IPV4',
		expr => 'tcp portrange 1-70000',
		errstr => 'illegal port number',
	},
	{
		name => 'tcp_portrange2',
		DLT => 'IPV4',
		expr => 'tcp portrange 23-',
		errstr => 'syntax error',
	},
	{
		name => 'tcp_portrange3',
		DLT => 'IPV4',
		expr => 'tcp portrange -512',
		errstr => 'syntax error',
	},
	{
		name => 'tcp_portrange4',
		DLT => 'IPV4',
		expr => 'tcp portrange 70000',
		errstr => 'illegal port number',
	},
	{
		name => 'udp_portrange',
		DLT => 'IPV4',
		expr => 'udp portrange 70000-1',
		errstr => 'illegal port number',
	},
	{
		name => 'sctp_portrange',
		DLT => 'IPV4',
		expr => 'sctp portrange 70000-80000',
		errstr => 'illegal port number',
	},
	{
		name => 'pppoes_and_vlan',
		DLT => 'EN10MB',
		expr => 'pppoes and vlan',
		errstr => 'no VLAN support for',
	},
	{
		name => 'vlan_invalid_id1',
		DLT => 'EN10MB',
		expr => 'vlan 4096',
		errstr => 'greater than maximum',
	},
	{
		name => 'vlan_invalid_id2',
		DLT => 'EN10MB',
		expr => 'vlan any',
		errstr => 'syntax error',
	},
	{
		name => 'mpls_invalid_id',
		DLT => 'EN10MB',
		expr => 'mpls 1048576',
		errstr => 'greater than maximum',
	},
	{
		name => 'arcnet_address1',
		DLT => 'ARCNET',
		expr => 'link host $123',
		errstr => 'syntax error',
	},
	{
		name => 'arcnet_address2',
		DLT => 'ARCNET',
		expr => 'link host $x',
		errstr => 'syntax error',
	},
	{
		name => 'arcnet_address3',
		DLT => 'ARCNET',
		expr => 'link host $',
		errstr => 'syntax error',
	},
	{
		name => 'arcnet_address4',
		DLT => 'ARCNET',
		expr => 'link host 120',
		errstr => 'illegal link layer address',
	},
	{
		name => 'ip_broadcast_implicit',
		DLT => 'EN10MB',
		expr => 'ip broadcast',
		errstr => "netmask not known, so 'ip broadcast' not supported",
	},
	{
		name => 'ip_broadcast_explicit',
		DLT => 'EN10MB',
		netmask => '***************',
		expr => 'ip broadcast',
		errstr => "netmask not known, so 'ip broadcast' not supported",
	},
	{
		name => 'arp_host_ipv4_ipv6',
		skip => skip_no_hosts(),
		DLT => 'FDDI',
		expr => 'arp host eth-ipv4-ipv6.host123.libpcap.test',
		errstr => '\'arp\' is not a valid qualifier for \'ip6 host\'',
	},
	{
		name => 'rarp_host_ipv4_ipv6',
		skip => skip_no_hosts(),
		DLT => 'FDDI',
		expr => 'rarp host eth-ipv4-ipv6.host123.libpcap.test',
		errstr => '\'rarp\' is not a valid qualifier for \'ip6 host\'',
	},
	{
		name => 'arp_host_noipv4_noipv6',
		skip => skip_no_hosts(),
		DLT => 'FDDI',
		expr => 'arp host eth-noipv4-noipv6.host123.libpcap.test',
		errstr => 'unknown host',
	},
	{
		name => 'rarp_host_noipv4_noipv6',
		skip => skip_no_hosts(),
		DLT => 'FDDI',
		expr => 'rarp host eth-noipv4-noipv6.host123.libpcap.test',
		errstr => 'unknown host',
	},
	{
		name => 'protochain_disabled',
		skip => skip_config_undef ('NO_PROTOCHAIN'),
		DLT => 'EN10MB',
		expr => 'protochain 17',
		errstr => 'protochain not supported',
	},
	# ID -> gen_scode() -> case Q_PROTOCHAIN -> lookup_proto() -> q.proto == Q_DEFAULT, v == PROTO_UNDEF
	{
		name => 'protochain_nosuchprotocol',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		DLT => 'RAW',
		expr => 'protochain nosuchprotocol',
		errstr => 'unknown \'protochain\' value \'nosuchprotocol\'',
	},
	{
		name => 'protochain_256',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		DLT => 'RAW',
		expr => 'protochain 256',
		errstr => 'protocol number 256 greater than maximum 255',
	},
	{
		name => 'ip_protochain_256',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		DLT => 'RAW',
		expr => 'ip protochain 256',
		errstr => 'protocol number 256 greater than maximum 255',
	},
	{
		name => 'ip6_protochain_256',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		DLT => 'RAW',
		expr => 'ip6 protochain 256',
		errstr => 'protocol number 256 greater than maximum 255',
	},
	{
		name => 'geneve_and_protochain_4',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		DLT => 'EN10MB',
		expr => 'geneve and protochain 4',
		errstr => '\'protochain\' not supported with variable length headers',
	},
	{
		name => 'vxlan_and_protochain_4',
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		DLT => 'EN10MB',
		expr => 'vxlan and protochain 4',
		errstr => '\'protochain\' not supported with variable length headers',
	},
	# ID -> gen_scode() -> case Q_PROTO -> lookup_proto() -> q.proto == Q_DEFAULT, v == PROTO_UNDEF
	{
		name => 'proto_nosuchprotocol',
		DLT => 'RAW',
		expr => 'proto nosuchprotocol',
		errstr => 'unknown \'proto\' value \'nosuchprotocol\'',
	},
	{
		name => 'proto_256',
		DLT => 'RAW',
		expr => 'proto 256',
		errstr => 'protocol number 256 greater than maximum 255',
	},
	{
		name => 'ip_proto_256',
		DLT => 'RAW',
		expr => 'ip proto 256',
		errstr => 'protocol number 256 greater than maximum 255',
	},
	{
		name => 'ip6_proto_256',
		DLT => 'RAW',
		expr => 'ip6 proto 256',
		errstr => 'protocol number 256 greater than maximum 255',
	},
	{
		name => 'proto_1_2_3_4',
		DLT => 'RAW',
		expr => 'proto *******',
		errstr => '\'proto\' qualifier applied to IPv4 address',
	},
	{
		name => 'decnet_host',
		DLT => 'EN10MB',
		expr => "decnet host ${nonexistent}",
		errstr => "invalid DECnet address '${nonexistent}'",
	},
	# gen_scode() -> case Q_GATEWAY -> pcap_nametoaddrinfo() == NULL
	# Invokes bpf_error() after pcap_ether_hostton().
	{
		name => 'gateway_noipv4_noipv6',
		skip => skip_no_ethers(),
		DLT => 'EN10MB',
		expr => 'gateway eth-noipv4-noipv6.host123.libpcap.test',
		errstr => 'unknown host', # no IPv4 address in /etc/hosts
	},
	# gen_scode() -> case Q_GATEWAY -> pcap_nametoaddrinfo() == NULL
	# Invokes bpf_error() after pcap_ether_hostton().
	{
		name => 'gateway_noipv4_ipv6',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'EN10MB',
		expr => 'gateway eth-noipv4-ipv6.host123.libpcap.test',
		errstr => 'unknown host', # no IPv4 address in /etc/hosts
	},
	{
		name => 'src_gateway',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'EN10MB',
		expr => 'src gateway eth-ipv4-noipv6.host123.libpcap.test',
		errstr => 'syntax error',
	},
	{
		name => 'dst_gateway',
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => 'EN10MB',
		expr => 'dst gateway eth-ipv4-noipv6.host123.libpcap.test',
		errstr => 'syntax error',
	},
	{
		name => 'src_proto_NUM',
		DLT => 'EN10MB',
		expr => 'src proto 1',
		errstr => 'syntax error',
	},
	{
		name => 'dst_proto_NUM',
		DLT => 'EN10MB',
		expr => 'dst proto 1',
		errstr => 'syntax error',
	},
	{
		name => 'src_proto_ID',
		DLT => 'EN10MB',
		expr => 'src proto \tcp',
		errstr => 'syntax error',
	},
	{
		name => 'dst_proto_ID',
		DLT => 'EN10MB',
		expr => 'dst proto \tcp',
		errstr => 'syntax error',
	},
	{
		name => 'src_protochain_NUM',
		DLT => 'EN10MB',
		expr => 'src protochain 1',
		errstr => 'syntax error',
	},
	{
		name => 'dst_protochain_NUM',
		DLT => 'EN10MB',
		expr => 'dst protochain 1',
		errstr => 'syntax error',
	},
	{
		name => 'src_protochain_ID',
		DLT => 'EN10MB',
		expr => 'src protochain \tcp',
		errstr => 'syntax error',
	},
	{
		name => 'dst_protochain_ID',
		DLT => 'EN10MB',
		expr => 'dst protochain \tcp',
		errstr => 'syntax error',
	},
	{
		name => 'gateway_1',
		DLT => 'EN10MB',
		expr => 'gateway 1',
		errstr => '\'gateway\' requires a name',
	},
	{
		name => 'gateway_1_2',
		DLT => 'EN10MB',
		expr => 'gateway 1.2',
		errstr => '\'gateway\' requires a name',
	},
	{
		name => 'gateway_1_2_3',
		DLT => 'EN10MB',
		expr => 'gateway 1.2.3',
		errstr => '\'gateway\' requires a name',
	},
	{
		name => 'gateway_1_2_3_4',
		DLT => 'EN10MB',
		expr => 'gateway *******',
		errstr => '\'gateway\' requires a name',
	},
	{
		name => 'gateway_mac48',
		DLT => 'EN10MB',
		expr => 'gateway 11:22:33:44:55:66',
		errstr => 'ethernet address used in non-ether expression',
	},
	{
		name => 'gateway_arcnet',
		DLT => 'EN10MB',
		expr => 'gateway $af',
		errstr => 'aid supported only on ARCnet',
	},
	{
		name => 'index_size_neg',
		DLT => 'RAW',
		expr => 'link[0:-1] != 0',
		errstr => 'syntax error',
	},
	{
		name => 'index_size_0',
		DLT => 'RAW',
		expr => 'link[0:0] != 0',
		errstr => 'data size must be 1, 2, or 4',
	},
	{
		name => 'index_size_3',
		DLT => 'RAW',
		expr => 'link[0:3] != 0',
		errstr => 'data size must be 1, 2, or 4',
	},
	{
		name => 'index_size_5',
		DLT => 'RAW',
		expr => 'link[0:5] != 0',
		errstr => 'data size must be 1, 2, or 4',
	},
	{
		name => 'bare_radio',
		DLT => 'IEEE802_11_RADIO',
		expr => 'radio',
		errstr => '\'radio\' cannot be used as an abbreviation',
	},
	{
		name => 'bare_link',
		DLT => 'IEEE802_11_RADIO',
		expr => 'link',
		errstr => '\'link\' cannot be used as an abbreviation',
	},
	{
		name => 'link_host_ipv4addr',
		DLT => 'RAW',
		expr => 'link host *******',
		errstr => 'illegal link layer address',
	},
	{
		name => 'link_net_ipv4addr',
		DLT => 'RAW',
		expr => 'link net *******',
		errstr => 'illegal link layer address',
	},
	{
		name => 'link_host_ipv6addr',
		DLT => 'RAW',
		expr => 'link host fe80::',
		errstr => '\'link\' is not a valid qualifier for \'ip6 host\'',
	},
	{
		name => 'link_net_ipv6addr',
		DLT => 'RAW',
		expr => 'link net fe80::',
		errstr => '\'link\' is not a valid qualifier for \'ip6 net\'',
	},
	{
		skip => skip_no_networks(),
		name => 'link_net_ipv4name',
		DLT => 'RAW',
		expr => 'link net net-10-20-30-0.libpcap.test',
		errstr => '\'link\' is not a valid qualifier for \'ip net\'',
	},
	{
		name => 'reason_invalid_PFLOG',
		DLT => 'PFLOG',
		expr => 'reason invalid',
		errstr => 'unknown PF reason "invalid"',
	},
	{
		name => 'action_invalid_PFLOG',
		DLT => 'PFLOG',
		expr => 'action invalid',
		errstr => 'unknown PF action "invalid"',
	},
	{
		name => 'iso_proto_256',
		DLT => 'EN10MB',
		expr => 'iso proto 256',
		errstr => 'ISO protocol 256 greater than maximum 255',
	},
	{
		name => 'isis_proto_32',
		DLT => 'EN10MB',
		expr => 'isis proto 32',
		errstr => 'IS-IS PDU type 32 greater than maximum 31',
	},
	{
		name => 'byte_ne',
		DLT => 'IPV4',
		expr => 'byte 1 != 2',
		errstr => 'syntax error',
	},
	{
		name => 'byte_le',
		DLT => 'IPV4',
		expr => 'byte 1 <= 2',
		errstr => 'syntax error',
	},
	{
		name => 'byte_ge',
		DLT => 'IPV4',
		expr => 'byte 1 >= 2',
		errstr => 'syntax error',
	},
	{
		name => 'byte_xor',
		DLT => 'IPV4',
		expr => 'byte 1 ^ 2',
		errstr => 'syntax error',
	},
	{
		name => 'byte_lsh',
		DLT => 'IPV4',
		expr => 'byte 1 << 2',
		errstr => 'syntax error',
	},
	{
		name => 'byte_rsh',
		DLT => 'IPV4',
		expr => 'byte 1 >> 2',
		errstr => 'syntax error',
	},
	{
		name => 'byte_eq_256',
		DLT => 'IPV4',
		expr => 'byte 1 = 256',
		errstr => 'byte argument 256 greater than maximum 255',
	},
	{
		name => 'byte_lt_256',
		DLT => 'IPV4',
		expr => 'byte 1 < 256',
		errstr => 'byte argument 256 greater than maximum 255',
	},
	{
		name => 'byte_gt_256',
		DLT => 'IPV4',
		expr => 'byte 1 > 256',
		errstr => 'byte argument 256 greater than maximum 255',
	},
	{
		name => 'byte_and_256',
		DLT => 'IPV4',
		expr => 'byte 1 & 256',
		errstr => 'byte argument 256 greater than maximum 255',
	},
	{
		name => 'byte_or_256',
		DLT => 'IPV4',
		expr => 'byte 1 | 256',
		errstr => 'byte argument 256 greater than maximum 255',
	},
	{
		name => 'vxlan_invalid',
		DLT => 'EN10MB',
		expr => 'vxlan invalid',
		errstr => 'syntax error',
	},
	{
		name => 'vxlan_*********',
		DLT => 'EN10MB',
		expr => 'vxlan *********',
		errstr => 'VXLAN VNI ********* greater than maximum 16777215',
	},
	{
		name => 'geneve_invalid',
		DLT => 'EN10MB',
		expr => 'geneve invalid',
		errstr => 'syntax error',
	},
	{
		name => 'geneve_*********',
		DLT => 'EN10MB',
		expr => 'geneve *********',
		errstr => 'Geneve VNI ********* greater than maximum 16777215',
	},
	# gen_linktype()
	{
		name => 'link_proto_65536_C_HDLC',
		DLT => 'C_HDLC',
		expr => 'link proto 65536',
		errstr => 'HDLC protocol 65536 greater than maximum 65535',
	},
	{
		name => 'link_proto_65536_PPP',
		DLT => 'PPP',
		expr => 'link proto 65536',
		errstr => 'PPP protocol 65536 greater than maximum 65535',
	},
	{
		name => 'link_proto_65536_PPP_BSDOS',
		DLT => 'PPP_BSDOS',
		expr => 'link proto 65536',
		errstr => 'PPP protocol 65536 greater than maximum 65535',
	},
	{
		name => 'link_proto_65536_APPLE_IP_OVER_IEEE1394',
		DLT => 'APPLE_IP_OVER_IEEE1394', # the default case
		expr => 'link proto 65536',
		errstr => 'EtherType 65536 greater than maximum 65535',
	},
	# gen_ether_linktype()
	{
		name => 'link_proto_65536_EN10MB',
		DLT => 'EN10MB',
		expr => 'link proto 65536',
		errstr => 'EtherType 65536 greater than maximum 65535',
	},
	{
		name => 'link_proto_1500_EN10MB',
		DLT => 'EN10MB',
		expr => 'link proto 1500',
		errstr => 'LLC DSAP 1500 greater than maximum 255',
	},
	# gen_llc_linktype
	{
		name => 'link_proto_65536_IP_OVER_FC',
		DLT => 'IP_OVER_FC',
		expr => 'link proto 65536',
		errstr => 'EtherType 65536 greater than maximum 65535',
	},
	{
		name => 'link_proto_1500_IP_OVER_FC',
		DLT => 'IP_OVER_FC',
		expr => 'link proto 1500',
		errstr => 'LLC DSAP 1500 greater than maximum 255',
	},
	# gen_linux_sll_linktype
	{
		name => 'link_proto_65536_LINUX_SLL',
		DLT => 'LINUX_SLL',
		expr => 'link proto 65536',
		errstr => 'EtherType 65536 greater than maximum 65535',
	},
	{
		name => 'link_proto_1500_LINUX_SLL',
		DLT => 'LINUX_SLL',
		expr => 'link proto 1500',
		errstr => 'LLC DSAP 1500 greater than maximum 255',
	},
);

# "proto" qualifiers without any lexer-level aliases (the entries correspond
# to Q_LINK~Q_CARP from gencode.h and are ordered by name).
my %pqual_features = (
	aarp => {
	},
	ah => {
	},
	arp => {
		index => 1,
		host => 1,
		gateway => 1,
	},
	atalk => {
		index => 1,
	},
	carp => {
		index => 1,
	},
	clnp => {
	},
	csnp => {
	},
	decnet => {
		index => 1,
		host => 1,
	},
	esis => {
	},
	esp => {
	},
	icmp => {
		index => 1,
	},
	icmp6 => {
		index => 1,
	},
	igmp => {
		index => 1,
	},
	igrp => {
		index => 1,
	},
	iih => {
	},
	ip => {
		index => 1,
		host => 1,
		gateway => 1,
		protochain => 1,
		proto => 1,
	},
	ip6 => {
		index => 1,
		host => 1,
		protochain => 1,
		proto => 1,
	},
	ipx => {
	},
	isis => {
		proto => 1,
	},
	iso => {
		proto => 1,
	},
	l1 => {
	},
	l2 => {
	},
	lat => {
		index => 1,
	},
	link => {
		index => 1,
		host => 1,
		proto => 1,
	},
	lsp => {
	},
	mopdl => {
		index => 1,
	},
	moprc => {
		index => 1,
	},
	netbeui => {
	},
	pim => {
		index => 1,
	},
	psnp => {
	},
	radio => {
		index => 1,
	},
	rarp => {
		index => 1,
		host => 1,
		gateway => 1,
	},
	sca => {
		index => 1,
	},
	sctp => {
		index => 1,
	},
	snp => {
	},
	stp => {
	},
	tcp => {
		index => 1,
	},
	udp => {
		index => 1,
	},
	vrrp => {
		index => 1,
	},
);

sub item_with_without {
	my $hashref = shift;
	my $feature = shift;
	my $wanted_with = shift;
	my $actual_with = exists $hashref->{$feature} && $hashref->{$feature} == 1;
	return $wanted_with == $actual_with;
}

sub list_with_without {
	my $hohref = shift;
	my $feature = shift;
	my $wanted_with = shift;
	my @ret;
	foreach (sort keys %$hohref) {
		next unless item_with_without $hohref->{$_}, $feature, $wanted_with;
		push @ret, $_;
		last if $only_short;
	}
	return @ret;
}

sub pquals_with {
	return list_with_without \%pqual_features, shift, 1;
}

sub pquals_without {
	return list_with_without \%pqual_features, shift, 0;
}

# "dir" qualifiers
my %dqual_features = (
	src => {
	},
	dst => {
	},
	'src or dst' => {
	},
	'src and dst' => {
	},
	addr1 => {
		wlan => 1,
	},
	addr2 => {
		wlan => 1,
	},
	addr3 => {
		wlan => 1,
	},
	addr4 => {
		wlan => 1,
	},
	ra => {
		wlan => 1,
	},
	ta => {
		wlan => 1,
	},
);

sub dquals_with {
	return list_with_without \%dqual_features, shift, 1;
}

sub dquals_without {
	return list_with_without \%dqual_features, shift, 0;
}

# All DLTs pcap_datalink_name_to_val() recognizes, ordered by name.
my %DLTfeatures = (
	A429 => {
	},
	A653_ICM => {
	},
	AOS => {
	},
	APPLE_IP_OVER_IEEE1394 => {
		link_proto => 1, # gen_linktype() default case
	},
	ARCNET => {
		link_proto => 1,
		link_broadcast => 1,
		link_multicast => 1,
		link_host_mac8 => 1,
	},
	ARCNET_LINUX => {
		link_proto => 1,
		link_broadcast => 1,
		link_multicast => 1,
		link_host_mac8 => 1,
	},
	ATM_CLIP => {
		link_proto => 1,
	},
	ATM_RFC1483 => {
		link_proto => 1,
		llc => 1,
	},
	ATSC_ALP => {
	},
	AUERSWALD_LOG => {
	},
	AX25_KISS => {
	},
	BACNET_MS_TP => {
		link_proto => 1,
	},
	BLUETOOTH_BREDR_BB => {
	},
	BLUETOOTH_HCI_H4 => {
	},
	BLUETOOTH_HCI_H4_WITH_PHDR => {
	},
	BLUETOOTH_LE_LL => {
	},
	BLUETOOTH_LE_LL_WITH_PHDR => {
	},
	BLUETOOTH_LINUX_MONITOR => {
	},
	CAN20B => {
	},
	CAN_SOCKETCAN => {
	},
	C_HDLC => {
		link_proto => 1,
		mpls => 1,
	},
	DBUS => {
	},
	DECT => {
	},
	DECT_NR => {
	},
	DISPLAYPORT_AUX => {
	},
	DOCSIS => {
	},
	DOCSIS31_XRA31 => {
	},
	DSA_TAG_BRCM => {
	},
	DSA_TAG_BRCM_PREPEND => {
	},
	DSA_TAG_DSA => {
	},
	DSA_TAG_EDSA => {
	},
	DVB_CI => {
	},
	EBHSCR => {
	},
	ELEE => {
	},
	EN10MB => {
		link_proto => 1,
		link_broadcast => 1,
		link_multicast => 1,
		link_host_mac48 => 1,
		vlan => 1,
		mpls => 1,
		llc => 1,
	},
	ENC => {
		link_proto => 1,
	},
	EPON => {
	},
	ERF => {
		ss7 => 1,
	},
	ERF_ETH => {
	},
	ERF_POS => {
	},
	ETHERNET_MPACKET => {
	},
	ETW => {
	},
	FC_2 => {
	},
	FC_2_WITH_FRAME_DELIMS => {
	},
	FDDI => {
		link_proto => 1,
		link_broadcast => 1,
		link_multicast => 1,
		link_host_mac48 => 1,
		llc => 1,
	},
	FIRA_UCI => {
	},
	FRELAY => {
		link_proto => 1,
	},
	GPF_F => {
	},
	GPF_T => {
	},
	GPRS_LLC => {
	},
	I2C_LINUX => {
	},
	IEEE802 => {
		link_proto => 1,
		link_broadcast => 1,
		link_multicast => 1,
		link_host_mac48 => 1,
		llc => 1,
	},
	IEEE802_11 => {
		link_proto => 1,
		link_broadcast => 1,
		link_multicast => 1,
		link_host_mac48 => 1,
		vlan => 1,
		llc => 1,
		wlan => 1,
		var_off_linkpl => 1,
	},
	IEEE802_11_RADIO => {
		link_proto => 1,
		link_broadcast => 1,
		link_multicast => 1,
		link_host_mac48 => 1,
		vlan => 1,
		llc => 1,
		wlan => 1,
		var_off_linkpl => 1,
	},
	IEEE802_11_RADIO_AVS => {
		link_proto => 1,
		link_broadcast => 1,
		link_multicast => 1,
		link_host_mac48 => 1,
		vlan => 1,
		llc => 1,
		wlan => 1,
		var_off_linkpl => 1,
	},
	IEEE802_15_4 => {
	},
	IEEE802_15_4_LINUX => {
	},
	IEEE802_15_4_NOFCS => {
	},
	IEEE802_15_4_NONASK_PHY => {
	},
	IEEE802_15_4_TAP => {
	},
	IEEE802_16_MAC_CPS => {
	},
	IEEE802_16_MAC_CPS_RADIO => {
	},
	INFINIBAND => {
	},
	IPMB_KONTRON => {
	},
	IPMI_HPM_2 => {
	},
	IPNET => {
		link_proto => 1,
		inout => 1,
	},
	IPOIB => {
	},
	IP_OVER_FC => {
		link_proto => 1,
		link_broadcast => 1,
		link_multicast => 1,
		link_host_mac48 => 1,
	},
	IPV4 => {
		link_proto => 1,
	},
	IPV6 => {
		link_proto => 1,
	},
	ISO_14443 => {
	},
	JUNIPER_ATM1 => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_ATM2 => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_ATM_CEMIC => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_CHDLC => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_ES => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_ETHER => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_FIBRECHANNEL => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_FRELAY => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_GGSN => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_ISM => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_MFR => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_MLFR => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_MLPPP => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_MONITOR => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_PIC_PEER => {
	},
	JUNIPER_PPP => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_PPPOE => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_PPPOE_ATM => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_SERVICES => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_SRX_E2E => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_ST => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_VP => {
		link_proto => 1,
		inout => 1,
	},
	JUNIPER_VS => {
		link_proto => 1,
		inout => 1,
	},
	LINUX_EVDEV => {
	},
	LINUX_IRDA => {
	},
	LINUX_LAPD => {
	},
	LINUX_SLL => {
		link_proto => 1,
		inout => 1,
	},
	LINUX_SLL2 => {
		link_proto => 1, # gen_linktype() default case
		inout => 1,
		ifindex => 1,
	},
	LOOP => {
		link_proto => 1,
	},
	LTALK => {
		link_proto => 1,
	},
	MDB => {
	},
	MFR => {
	},
	MPEG_2_TS => {
	},
	MPLS => {
	},
	MTP2 => {
		ss7 => 1,
	},
	MTP2_WITH_PHDR => {
		ss7 => 1,
	},
	MTP3 => {
	},
	MUX27010 => {
	},
	NETANALYZER => {
		link_proto => 1,
		link_broadcast => 1,
		link_multicast => 1,
		link_host_mac48 => 1,
		vlan => 1,
		mpls => 1,
	},
	NETANALYZER_NG => {
	},
	NETANALYZER_TRANSPARENT => {
		link_proto => 1,
		link_broadcast => 1,
		link_multicast => 1,
		link_host_mac48 => 1,
		vlan => 1,
		mpls => 1,
	},
	NETLINK => {
	},
	NFC_LLCP => {
	},
	NFLOG => {
	},
	NG40 => {
	},
	NORDIC_BLE => {
	},
	NULL => {
		link_proto => 1,
	},
	OPENFLOW => {
	},
	OPENVIZSLA => {
	},
	PFLOG => {
		link_proto => 1,
		inout => 1,
		pflog => 1,
	},
	PFSYNC => {
	},
	PKTAP => {
	},
	PPI => {
		link_proto => 1,
		link_broadcast => 1,
		link_multicast => 1,
		link_host_mac48 => 1,
		llc => 1,
		wlan => 1,
		var_off_linkpl => 1,
	},
	PPP => {
		link_proto => 1,
		mpls => 1,
	},
	PPP_BSDOS => {
		link_proto => 1,
	},
	PPP_ETHER => {
		link_proto => 1,
	},
	PPP_PPPD => {
		link_proto => 1,
		inout => 1,
	},
	PPP_SERIAL => {
		link_proto => 1,
	},
	PRISM_HEADER => {
		link_proto => 1,
		link_broadcast => 1,
		link_multicast => 1,
		link_host_mac48 => 1,
		vlan => 1,
		llc => 1,
		wlan => 1,
		var_off_linkpl => 1,
	},
	PROFIBUS_DL => {
	},
	RAIF1 => {
	},
	RAW => {
		link_proto => 1,
	},
	RDS => {
	},
	RTAC_SERIAL => {
	},
	SCCP => {
	},
	SCTP => {
	},
	SDLC => {
	},
	SILABS_DEBUG_CHANNEL => {
	},
	SITA => {
	},
	SLIP => {
		link_proto => 1,
		inout => 1,
	},
	SLIP_BSDOS => {
		link_proto => 1,
	},
	STANAG_5066_D_PDU => {
	},
	SUNATM => {
		link_proto => 1,
		llc => 1,
		atm => 1,
	},
	SYMANTEC_FIREWALL => {
		link_proto => 1, # gen_linktype() default case
	},
	TI_LLN_SNIFFER => {
	},
	USB_2_0 => {
	},
	USB_2_0_FULL_SPEED => {
	},
	USB_2_0_HIGH_SPEED => {
	},
	USB_2_0_LOW_SPEED => {
	},
	USB_DARWIN => {
	},
	USB_FREEBSD => {
	},
	USB_LINUX => {
	},
	USB_LINUX_MMAPPED => {
	},
	USBPCAP => {
	},
	VPP_DISPATCH => {
	},
	VSOCK => {
	},
	WATTSTOPPER_DLM => {
	},
	WIHART => {
	},
	ZBOSS_NCP => {
	},
	ZWAVE_R1_R2 => {
	},
	ZWAVE_R3 => {
	},
	Z_WAVE_SERIAL => {
	},
	ZWAVE_TAP => {
	},
);

sub DLTs_with {
	return list_with_without \%DLTfeatures, shift, 1;
}

sub DLTs_without {
	return list_with_without \%DLTfeatures, shift, 0;
}

sub DLT_feature {
	my $name = shift;
	my $feature = shift;
	return item_with_without $DLTfeatures{$name}, $feature, 1;
}

# gen_load_internal() -> default
foreach (pquals_without 'index') {
	push @reject_tests, {
		name => "noindex_${_}",
		DLT => 'EN10MB',
		expr => "${_}[0] == 0",
		errstr => "'${_}' does not support the index operation",
	};
}

# "link host IPV4ADDR" and "link net IPV4ADDR" use a different code path with
# a different error message and are tested above.  "link net NAME" uses yet
# another different code path with yet another different error message and is
# tested above.  "(link|ip|rarp|arp|decnet|ip6) host" can be valid syntax or
# not (depending on the primitive ID), which is tested above.
foreach (pquals_without 'host') {
	# HID -> gen_ncode() -> gen_host() -> default
	push @reject_tests, {
		name => "inv_qual_${_}_host_ipv4addr",
		DLT => 'EN10MB',
		expr => "${_} host *******",
		errstr => "'${_}' is not a valid qualifier for 'ip host'",
	};
	# HID -> gen_ncode() -> gen_host() -> default
	push @reject_tests, {
		name => "inv_qual_${_}_net_ipv4addr",
		DLT => 'EN10MB',
		expr => "${_} net *******",
		errstr => "'${_}' is not a valid qualifier for 'ip net'",
	};
	# HID6 -> gen_mcode6() -> gen_host6() -> default
	push @reject_tests, {
		name => "inv_qual_${_}_host_ipv6addr",
		DLT => 'EN10MB',
		expr => "${_} host fe80::",
		errstr => "'${_}' is not a valid qualifier for 'ip6 host'",
	};
	# HID6 -> gen_mcode6() -> gen_host6() -> default
	push @reject_tests, {
		name => "inv_qual_${_}_net_ipv6addr",
		DLT => 'EN10MB',
		expr => "${_} net fe80::",
		errstr => "'${_}' is not a valid qualifier for 'ip6 net'",
	};

	# HID NETMASK HID -> gen_mcode() -> default
	push @reject_tests, {
		name => "inv_qual_${_}_host_ipv4mask",
		DLT => 'EN10MB',
		expr => "${_} host ******* mask *************",
		errstr => 'Mask syntax for networks only',
	};
	# HID NETMASK HID -> gen_mcode() -> gen_host() -> default
	push @reject_tests, {
		name => "inv_qual_${_}_net_ipv4mask",
		DLT => 'EN10MB',
		expr => "${_} net ******* mask *************",
		errstr => "'${_}' is not a valid qualifier for 'ip net'",
	};
	# ("mask" is IPv4-only)

	# HID '/' NUM -> gen_mcode() -> default
	push @reject_tests, {
		name => "inv_qual_${_}_host_ipv4cidr",
		DLT => 'EN10MB',
		expr => "${_} host *******/24",
		errstr => 'Mask syntax for networks only',
	};
	# HID '/' NUM -> gen_mcode() -> gen_host() -> default
	push @reject_tests, {
		name => "inv_qual_${_}_net_ipv4cidr",
		DLT => 'EN10MB',
		expr => "${_} net *******/24",
		errstr => "'${_}' is not a valid qualifier for 'ip net'",
	};
	# HID6 '/' NUM -> gen_mcode6() -> default
	push @reject_tests, {
		name => "inv_qual_${_}_host_ipv6cidr",
		DLT => 'EN10MB',
		expr => "${_} host fe80::/16",
		errstr => 'Mask syntax for networks only',
	};
	# HID6 '/' NUM -> gen_mcode6() -> gen_host6() -> default
	push @reject_tests, {
		name => "inv_qual_${_}_net_ipv6cidr",
		DLT => 'EN10MB',
		expr => "${_} net fe80::/16",
		errstr => "'${_}' is not a valid qualifier for 'ip6 net'",
	};

	# ID -> gen_scode() -> gen_host() -> default
	push @reject_tests, {
		skip => skip_no_hosts(),
		name => "inv_qual_${_}_host_ipv4name",
		DLT => 'EN10MB',
		expr => "${_} host noeth-ipv4-noipv6.host123.libpcap.test",
		errstr => "'${_}' is not a valid qualifier for 'ip host'",
	};
	# ID -> gen_scode() -> gen_host() -> default
	push @reject_tests, {
		skip => skip_no_networks(),
		name => "inv_qual_${_}_net_ipv4name",
		DLT => 'EN10MB',
		expr => "${_} net net-10-20-30-0.libpcap.test",
		errstr => "'${_}' is not a valid qualifier for 'ip net'",
	};
	# ID -> gen_scode() -> gen_host6() -> default
	push @reject_tests, {
		skip => skip_no_hosts(),
		name => "inv_qual_${_}_host_ipv6name",
		DLT => 'EN10MB',
		expr => "${_} host noeth-noipv4-ipv6.host123.libpcap.test",
		errstr => "'${_}' is not a valid qualifier for 'ip6 host'",
	};
	# (IPv6 networks cannot have names)
}

# ID -> gen_scode() -> case Q_GATEWAY -> gen_gateway() -> default
# Invokes bpf_error() after pcap_ether_hostton().
foreach (pquals_without 'gateway') {
	push @reject_tests, {
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		name => "inv_qual_${_}_gateway_ipv4name",
		DLT => 'EN10MB',
		expr => "${_} gateway eth-ipv4-noipv6.host123.libpcap.test",
		errstr => "'${_}' is not a valid qualifier for 'gateway'",
	};
}

foreach (DLTs_without 'link_host_mac48') {
	# ID -> gen_scode() -> case Q_GATEWAY -> gen_gateway() -> case Q_DEFAULT -> default
	# Invokes bpf_error() after pcap_ether_hostton().
	push @reject_tests, {
		name => "gateway_ipv4_noipv6_${_}",
			skip => skip_no_ethers() ||
				skip_no_hosts(),
		DLT => $_,
		expr => 'gateway eth-ipv4-noipv6.host123.libpcap.test',
		errstr => '\'gateway\' not supported on',
	};
	# ID -> gen_scode() -> case Q_GATEWAY -> gen_gateway() -> case Q_DEFAULT -> default
	# Invokes bpf_error() after pcap_ether_hostton().
	push @reject_tests, {
		name => "gateway_ipv4_ipv6_${_}",
			skip => skip_no_ethers() ||
				skip_no_hosts(),
		DLT => $_,
		expr => 'gateway eth-ipv4-ipv6.host123.libpcap.test',
		errstr => '\'gateway\' not supported on',
	};
}

foreach my $pq (pquals_without '') {
	# HID -> gen_ncode() -> case Q_GATEWAY
	push @reject_tests, {
		name => "${pq}_gateway_HID",
		DLT => 'EN10MB',
		expr => "$pq gateway ***********",
		errstr => $pq eq 'decnet' ?
			'invalid DECnet address \'***********\'' :
			'\'gateway\' requires a name',
	};
	# HID
	push @reject_tests, {
		name => "${pq}_${_}_HID",
		skip => $_ eq 'protochain' ? skip_config_def1 ('NO_PROTOCHAIN') : '',
		DLT => 'EN10MB',
		expr => "$pq $_ ***********",
		errstr => "'$_' qualifier applied to IPv4 address",
	} foreach qw(port portrange proto protochain);
	# HID '/' NUM -> gen_mcode() -> default
	push @reject_tests, {
		name => "${pq}_gateway_HID_NUM",
		DLT => 'EN10MB',
		expr => "$pq gateway *********/16",
		errstr => 'Mask syntax for networks only',
	};
	# HID '/' NUM
	push @reject_tests, {
		name => "${pq}_${_}_HID_NUM",
		skip => $_ eq 'protochain' ? skip_config_def1 ('NO_PROTOCHAIN') : '',
		DLT => 'EN10MB',
		expr => "$pq $_ *********/16",
		errstr => "'$_' qualifier applied to IPv4 address and prefix length",
	} foreach qw(port portrange proto protochain);
	# HID NETMASK HID -> gen_mcode() -> default
	push @reject_tests, {
		name => "${pq}_gateway_HID_mask_HID",
		DLT => 'EN10MB',
		expr => "$pq gateway ********* mask ***********",
		errstr => 'Mask syntax for networks only',
	};
	# HID NETMASK HID
	push @reject_tests, {
		name => "${pq}_${_}_HID_mask_HID",
		skip => $_ eq 'protochain' ? skip_config_def1 ('NO_PROTOCHAIN') : '',
		DLT => 'EN10MB',
		expr => "$pq $_ ********* mask ***********",
		errstr => "'$_' qualifier applied to IPv4 address and netmask",
	} foreach qw(port portrange proto protochain);
	# HID6 -> gen_mcode6() -> default
	push @reject_tests, {
		name => "${pq}_gateway_HID6",
		DLT => 'EN10MB',
		expr => "$pq gateway fe80::0",
		errstr => 'invalid qualifier against IPv6 address',
	};
	# HID6
	push @reject_tests, {
		name => "${pq}_${_}_HID6",
		skip => $_ eq 'protochain' ? skip_config_def1 ('NO_PROTOCHAIN') : '',
		DLT => 'EN10MB',
		expr => "$pq $_ fe80::0",
		errstr => "'$_' qualifier applied to IPv6 address",
	} foreach qw(port portrange proto protochain);
	# HID6 '/' NUM -> gen_mcode6() -> default
	push @reject_tests, {
		name => "${pq}_gateway_HID6_NUM",
		DLT => 'EN10MB',
		expr => "$pq gateway fe80::0/64",
		errstr => 'invalid qualifier against IPv6 address',
	};
	# HID6 '/' NUM
	push @reject_tests, {
		name => "${pq}_${_}_HID6_NUM",
		skip => $_ eq 'protochain' ? skip_config_def1 ('NO_PROTOCHAIN') : '',
		DLT => 'EN10MB',
		expr => "$pq $_ fe80::0/64",
		errstr => "'$_' qualifier applied to IPv6 address and prefix length",
	} foreach qw(port portrange proto protochain);
	last if $only_short;
}

foreach (pquals_without 'protochain') {
	# pnum -> gen_ncode() -> case Q_PROTOCHAIN -> gen_protochain() -> default
	push @reject_tests, {
		name => "${_}_protochain_17",
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		DLT => 'EN10MB',
		expr => "${_} protochain 17",
		errstr => $_ eq 'decnet' ?
			'invalid DECnet address \'17\'' :
			"'${_}' is not a valid qualifier for 'protochain'",
	};
	last if $only_short;
}

foreach (pquals_without 'proto') {
	# pnum -> gen_ncode() -> case Q_PROTO -> gen_proto() -> default
	push @reject_tests, {
		name => "${_}_proto_17",
		DLT => 'EN10MB',
		expr => "${_} proto 17",
		errstr => $_ eq 'decnet' ?
			'invalid DECnet address \'17\'' :
			"'${_}' is not a valid qualifier for 'proto'",
	};
	last if $only_short;
}

foreach (pquals_without '') {
	# ID -> gen_scode() -> case Q_PROTO -> lookup_proto() -> q.proto != Q_DEFAULT, v == PROTO_UNDEF
	push @reject_tests, {
		name => "${_}_proto_nosuchprotocol",
		DLT => 'EN10MB',
		expr => "${_} proto nosuchprotocol",
		errstr => "unknown '$_ proto' value 'nosuchprotocol'",
	};
	# ID -> gen_scode() -> case Q_PROTOCHAIN -> lookup_proto() -> q.proto != Q_DEFAULT, v == PROTO_UNDEF
	push @reject_tests, {
		name => "${_}_protochain_nosuchprotocol",
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		DLT => 'EN10MB',
		expr => "${_} protochain nosuchprotocol",
		errstr => "unknown '$_ protochain' value 'nosuchprotocol'",
	};
	last if $only_short;
}

# Use a separate foreach loop for each feature because different loops skip
# different DLTs and can terminate early.

# gen_linktype() -> default
foreach (DLTs_without 'link_proto') {
	push @reject_tests, {
		name => "link_proto_1_${_}",
		DLT => $_,
		expr => 'link proto 1',
		errstr => 'link-layer type filtering not implemented for',
	};
}

# gen_broadcast() -> case Q_LINK -> gen_mac48host() -> fail_kw_on_dlt()
foreach (DLTs_without 'link_broadcast') {
	push @reject_tests, {
		name => "link_broadcast_${_}",
		DLT => $_,
		expr => 'link broadcast',
		errstr => '\'broadcast\' not supported on',
	};
}

# gen_multicast() -> case Q_LINK -> default
foreach (DLTs_without 'link_multicast') {
	push @reject_tests, {
		name => "link_multicast_${_}",
		DLT => $_,
		expr => 'link multicast',
		errstr => '\'multicast\' not supported on',
	};
}

# gen_vlan() -> default
foreach (DLTs_without 'vlan') {
	push @reject_tests, {
		name => "vlan_${_}",
		DLT => $_,
		expr => 'vlan',
		errstr => 'no VLAN support for',
	};
}

# gen_mpls_internal() -> default
foreach (DLTs_without 'mpls') {
	push @reject_tests, {
		name => "mpls_${_}",
		DLT => $_,
		expr => 'mpls',
		errstr => 'no MPLS support for',
	};
}

# gen_llc_internal() -> default
foreach (DLTs_without 'llc') {
	push @reject_tests, {
		name => "llc_${_}",
		DLT => $_,
		expr => 'llc',
		errstr => '\'llc\' not supported on',
	};
}

foreach (DLTs_without 'wlan') {
	# gen_p80211_type() -> default
	push @reject_tests, {
		name => "type_data_${_}",
		DLT => $_,
		expr => 'type data',
		errstr => '\'type/subtype\' not supported on',
	};
	# gen_p80211_fcdir() -> default
	push @reject_tests, {
		name => "dir_fromds_${_}",
		DLT => $_,
		expr => 'dir fromds',
		errstr => '\'dir\' not supported on',
	};
}

# gen_scode() -> case Q_HOST -> proto == Q_LINK -> non-WLAN case -> gen_mac48host() -> default case
# Invokes bpf_error() after pcap_ether_hostton().
foreach my $DLT (DLTs_without '') {
	next if ! DLT_feature ($DLT, 'link_host_mac48') || DLT_feature ($DLT, 'wlan');
	push @reject_tests, {
		name => "link_${_}_name_${DLT}",
		skip => skip_no_ethers(),
		DLT => $DLT,
		expr => "link $_ eth-noipv4-noipv6.host123.libpcap.test",
		errstr => "'$_' is valid for 802.11 syntax only",
	} foreach dquals_with 'wlan';
	last if $only_short;
}

# gen_acode() -> default
foreach (DLTs_without 'link_host_mac8') {
	push @reject_tests, {
		name => "link_host_21_${_}",
		DLT => $_,
		expr => 'link host $21',
		errstr => 'aid supported only on ARCnet',
	};
}

# assert_ss7() -> default
foreach (DLTs_without 'ss7') {
	push @reject_tests, {
		name => "fisu_${_}",
		DLT => $_,
		expr => 'fisu',
		errstr => '\'fisu\' supported only on SS7',
	};
}

foreach (DLTs_without 'link_host_mac48') {
	# gen_scode() -> case Q_GATEWAY -> fail_kw_on_dlt()
	push @reject_tests, {
		name => "gateway_name_${_}",
		skip => skip_no_ethers() ||
			skip_no_hosts(),
		DLT => $_,
		expr => 'gateway eth-ipv4-noipv6.host123.libpcap.test',
		errstr => '\'gateway\' not supported on',
	};
	# gen_scode() -> case Q_DEFAULT -> Q_LINK -> fail_kw_on_dlt()
	push @reject_tests, {
		name => "link_host_name_${_}",
		skip => skip_no_ethers(),
		DLT => $_,
		expr => 'link host eth-noipv4-noipv6.host123.libpcap.test',
		errstr => '\'link host NAME\' not supported on',
	};
	# gen_ecode() -> fail_kw_on_dlt()
	push @reject_tests, {
		name => "link_host_mac48_${_}",
		DLT => $_,
		expr => 'link host a:b:c:d:e:f',
		errstr => '\'link host XX:XX:XX:XX:XX:XX\' not supported on',
	};
}

foreach (DLTs_with 'link_host_mac48') {
	# gen_scode() -> case Q_GATEWAY -> eaddrp == NULL
	push @reject_tests, {
		name => "gateway_nonex_${_}",
		skip => skip_no_hosts(),
		DLT => $_,
		expr => 'gateway noeth-ipv4-noipv6.host123.libpcap.test',
		errstr => "unknown Ethernet-like host 'noeth-ipv4-noipv6.host123.libpcap.test'",
	};
	# In the test below the hostname normally should not matter because the
	# lookup would be made not in the IPv4/IPv6 space, or not at all.  Still
	# use a hostname that does not exist on the Internet, just in case.
	# gen_scode() -> case Q_HOST -> proto == Q_LINK -> eaddrp == NULL
	push @reject_tests, {
		name => "link_host_nonex_${_}",
		DLT => $_,
		expr => "link host ${nonexistent}",
		errstr => "unknown Ethernet-like host '${nonexistent}'",
	};
}

# gen_inbound_outbound() -> default
foreach (DLTs_without 'inout') {
	push @reject_tests, {
		name => "inbound_linux_${_}",
		skip => skip_os_not ('linux'),
		DLT => $_,
		expr => 'inbound',
		errstr => 'not a live capture',
	};
	push @reject_tests, {
		name => "outbound_linux_${_}",
		skip => skip_os_not ('linux'),
		DLT => $_,
		expr => 'outbound',
		errstr => 'not a live capture',
	};
	push @reject_tests, {
		name => "inbound_other_${_}",
		skip => skip_os ('linux'),
		DLT => $_,
		expr => 'inbound',
		errstr => 'not supported on',
	};
	push @reject_tests, {
		name => "outbound_other_${_}",
		skip => skip_os ('linux'),
		DLT => $_,
		expr => 'outbound',
		errstr => 'not supported on',
	};
}

# assert_pflog() -> default
foreach (DLTs_without 'pflog') {
	push @reject_tests, {
		name => "reason_congestion_${_}",
		DLT => $_,
		expr => 'reason congestion',
		errstr => '\'reason\' supported only on PFLOG linktype',
	};
}

# assert_atm() -> default
foreach (DLTs_without 'atm') {
	push @reject_tests, {
		name => "vpi_1_${_}",
		DLT => $_,
		expr => 'vpi 1',
		errstr => '\'vpi\' supported only on SUNATM',
	};
}

# gen_ifindex -> default
foreach (DLTs_without 'ifindex') {
	push @reject_tests, {
		name => "ifindex_1_${_}",
		skip => skip_os ('linux'),
		DLT => $_,
		expr => 'ifindex 1',
		errstr => '\'ifindex\' not supported on',
	};
	push @reject_tests, {
		name => "ifindex_2_${_}",
		skip => skip_os_not ('linux'),
		DLT => $_,
		expr => 'ifindex 2',
		errstr => 'not a live capture',
	};
}

# the prerequisite in gen_protochain()
foreach (DLTs_with 'var_off_linkpl') {
	push @reject_tests, {
		name => "protochain_4_${_}",
		skip => skip_config_def1 ('NO_PROTOCHAIN'),
		DLT => $_,
		expr => 'protochain 4',
		errstr => '\'protochain\' not supported with variable length headers',
	};
}

sub accept_test_label {
	return join '_', ('accept', @_);
}

sub apply_test_label {
	return join '_', ('apply', @_);
}

sub reject_test_label {
	return join '_', ('reject', @_);
}

sub time_test_command {
	my $cmdline = join ' ', @_;
	my $r;
	my $T;
	if (! $print_passed) {
		$r = system $cmdline;
	} else {
		my $t0 = Time::HiRes::time;
		$r = system $cmdline;
		$T = Time::HiRes::time - $t0;
	}
	return ($r >>= 8, $T);
}

sub validate_stdout_test {
	my ($r, $T) = time_test_command @_;

	return result_timed_out 'filtertest timeout' if $r == TIMED_OUT;

	return result_failed (
		'filtertest error',
		file_get_contents mytmpfile $filename_stdout
	) if $r;

	return result_failed (
		'diff error',
		file_get_contents mytmpfile $filename_diags
	) if system sprintf "diff $diff_flags %s %s >%s 2>&1",
		mytmpfile ($filename_expected),
		mytmpfile ($filename_stdout),
		mytmpfile ($filename_diags);

	return result_passed $T;
}

sub common_filtertest_args {
	my $test = shift;
	# BSD timeout(1) does not implement --verbose.
	my @args = defined $timeout_bin ? ($timeout_bin, $test_timeout) : ();
	push @args, $filtertest;
	push @args, ('-s', $test->{snaplen}) if defined $test->{snaplen};
	push @args, ('-m', $test->{netmask}) if defined $test->{netmask};
	push @args, '-O' unless $test->{optimize};
	push @args, '-l' if $test->{linuxext};
	return @args;
}

sub run_accept_test {
	my $test = shift;
	my @args = common_filtertest_args $test;
	# Write the filter expression to a file because the version of
	# system() that takes a list does not support redirecting stdout,
	# and the version of system() that takes a string does not escape
	# special characters in the filter expression, which becomes
	# invalid shell syntax.
	file_put_contents mytmpfile ($filename_filter), $test->{expr};
	file_put_contents mytmpfile ($filename_expected), $test->{expected};
	push @args, (
		'-F',
		mytmpfile ($filename_filter),
		$test->{DLT},
		'>' . mytmpfile ($filename_stdout),
		"2>&1"
	);
	return validate_stdout_test @args;
}

sub run_apply_test {
	my $test = shift;
	my @args = common_filtertest_args $test;
	file_put_contents mytmpfile ($filename_filter), $test->{expr};
	file_put_contents mytmpfile ($filename_expected), $test->{expected};
	push @args, (
		'-F',
		mytmpfile ($filename_filter),
		'-r',
		SAVEFILE_DIR . $test->{savefile},
		'>' . mytmpfile ($filename_stdout),
		"2>&1"
	);
	return validate_stdout_test @args;
}

sub run_reject_test {
	my $test = shift;
	my @args = common_filtertest_args $test;
	file_put_contents mytmpfile ($filename_filter), $test->{expr};
	push @args, (
		'-F',
		mytmpfile ($filename_filter),
		$test->{DLT},
		'>' . mytmpfile ($filename_stdout),
		"2>&1",
	);
	my ($r, $T) = time_test_command @args;

	return result_failed (
		'no filtertest error',
		file_get_contents mytmpfile $filename_stdout
	) if $r == 0;

	return result_timed_out 'filtertest timeout' if $r == TIMED_OUT;

	return result_failed (
		"filtertest status $r",
		file_get_contents mytmpfile $filename_stdout
	) if $r != EX_DATAERR;

	return result_failed (
		'error string mismatch',
		file_get_contents mytmpfile $filename_stdout
	) if ! string_in_file ($test->{expected}, mytmpfile $filename_stdout);

	return result_passed $T;
}

# Sort all hash elements by key, otherwise the pseudo-random ordering in Perl
# hashes will make it difficult to compare outputs of two invocations.
# Validate all accept test blocks and all reject tests, decide if this is a
# "run all tests" or a "run only this specific test or test block" invocation
# and produce the required test(s) using appropriate permutations of the main
# expression, any aliases and the bytecode version (optimized/unoptimized).
#
# The resulting flat ordered list of tests includes all skipped tests at their
# original positions, this makes it simple to distribute the tests and to
# collect the results preserving the ordering.
my @ready_to_run;
foreach my $test (@accept_blocks) {
	if (! defined $test->{name} || $test->{name} eq '') {
		die "Internal error: accept test block <UNKNOWN> does not define key 'name'";
	}
	foreach ('DLT', 'aliases') {
		next if defined $test->{$_};
		die "Internal error: accept test block '$test->{name}' does not define key '$_'";
	}
	if ($test->{DLT} eq '') {
		die "Internal error: key 'DLT' is an empty string in apply test block '$test->{name}'";
	}
	if (! scalar @{$test->{aliases}}) {
		die "Internal error: accept test block '$test->{name}' defines zero aliases";
	} else {
		my %unique = map {$_ => 1} @{$test->{aliases}};
		if (scalar (keys %unique) != scalar @{$test->{aliases}}) {
			die "Internal error: accept test block '$test->{name}' defines duplicate aliases";
		}
	}
	if (! defined $test->{unopt} && ! defined $test->{opt}) {
		die "Internal error: accept test block '$test->{name}' defines neither 'unopt' nor 'opt'";
	}
	# Make the number of skip requests equal to the number of tests, but
	# provide the reason for the first skip request only.  This avoid wasting
	# the vertical scroll space when skipping test blocks with many aliases.
	my $skip_reason = (defined $test->{skip} && $test->{skip} ne '') ?
		$test->{skip} : undef;
	foreach my $optunopt ('unopt', 'opt') {
		next unless defined $test->{$optunopt};

		if (defined $skip_reason) {
			my $i = 0;
			foreach (@{$test->{aliases}}) {
				my $label = accept_test_label $test->{name}, $optunopt, $i++;
				next if defined $only_one && $only_one ne $label;
				push @ready_to_run, {
					label => $label,
					func => \&run_skip_test,
					skip => $print_skipped ? $skip_reason : '',
				};
				$skip_reason = '';
			}
		} else {
			# Dedent and trim to restore the format of bpf_dump().
			my $multiline = '';
			foreach (split /^/o, $test->{$optunopt}) {
				$multiline .= "$1\n" if /^[\t]*(\(.+)$/o;
			}
			my $i = 0;
			foreach (@{$test->{aliases}}) {
				my $label = accept_test_label $test->{name}, $optunopt, $i++;
				next if defined $only_one && $only_one ne $label;
				push @ready_to_run, {
					label => $label,
					func => \&run_accept_test,
					DLT => $test->{DLT},
					expr => $_,
					snaplen => defined $test->{snaplen} ? $test->{snaplen} : undef,
					netmask => defined $test->{netmask} ? $test->{netmask} : undef,
					optimize => int ($optunopt eq 'opt'),
					linuxext => defined $test->{linuxext} && $test->{linuxext} == 1,
					expected => $multiline,
				};
			}
		}
	}
}
foreach my $block (@apply_blocks) {
	if (! defined $block->{name} || $block->{name} eq '') {
		die "Internal error: apply test block <UNKNOWN> does not define key 'name'";
	}
	foreach ('savefile', 'expr', 'results') {
		next if defined $block->{$_};
		die "Internal error: apply test block '$block->{name}' does not define key '$_'";
	}
	foreach ('savefile', 'expr') {
		next if $block->{$_} ne '';
		die "Internal error: key '$_' is an empty string in apply test block '$block->{name}'";
	}
	if (! scalar @{$block->{results}}) {
		die "Internal error: apply test block '$block->{name}' defines zero results";
	}
	my $skip_reason = (defined $block->{skip} && $block->{skip} ne '') ?
		$block->{skip} : undef;
	# Convert the array to filtertest output format.
	my $multiline = join ("\n", @{$block->{results}}) . "\n";
	foreach my $optunopt ('unopt', 'opt') {
		my $label = apply_test_label ($block->{name}, $optunopt);
		next if defined $only_one && $only_one ne $label;

		if (defined $skip_reason) {
			push @ready_to_run, {
				label => $label,
				func => \&run_skip_test,
				skip => $print_skipped ? $skip_reason : '',
			};
			$skip_reason = '';
			next;
		}

		push @ready_to_run, {
			label => $label,
			func => \&run_apply_test,
			netmask => defined $block->{netmask} ? $block->{netmask} : undef,
			optimize => int ($optunopt eq 'opt'),
			expr => $block->{expr},
			expected => $multiline,
			savefile => $block->{savefile},
		};
	}
}
foreach my $test (@reject_tests) {
	if (! defined $test->{name} || $test->{name} eq '') {
		die "Internal error: reject test <UNKNOWN> does not define key 'name'";
	}
	foreach ('DLT', 'expr', 'errstr') {
		next if defined $test->{$_} && $test->{$_} ne '';
		die "Internal error: reject test '$test->{name}' does not define key '$_'";
	}
	my $label = reject_test_label $test->{name};
	next if defined $only_one && $only_one ne $label;

	if (defined $test->{skip} && $test->{skip} ne '') {
		push @ready_to_run, {
			label => $label,
			func => \&run_skip_test,
			skip => $print_skipped ? $test->{skip} : '',
		};
	} else {
		push @ready_to_run, {
			label => $label,
			func => \&run_reject_test,
			DLT => $test->{DLT},
			netmask => defined $test->{netmask} ? $test->{netmask} : undef,
			expr => $test->{expr},
			expected => $test->{errstr},
		};
	}
}

if (! scalar @ready_to_run) {
	die "ERROR: Unknown test label '${only_one}'" if defined $only_one;
	die 'Internal error: no tests defined to run!'
}
if ($only_list) {
	print $_->{label} . "\n" foreach @ready_to_run;
	exit EX_OK;
}

$diff_flags = get_diff_flags;

$filtertest = defined $ENV{FILTERTEST_BIN} ? $ENV{FILTERTEST_BIN} :
	string_in_file ('/* cmakeconfig.h.in */', $config_h) ? './run/filtertest' :
	'./testprogs/filtertest';

# In this libpcap version "filtertest -h" prints to stdout and exits normally.
if (system ("$filtertest -h >/dev/null 2>&1") >> 8) {
	# Make it easier to see what the problem is.
	system $filtertest;
	print STDERR "ERROR: $filtertest is not usable\n";
	exit 2;
}

# Every test in this file uses an expression that under normal conditions takes
# well under one second to process, so if a filtertest invocation is taking
# longer, it is likely a regression.  Or an invocation via Valgrind, which
# demands a sensible host-specific override of the timeout value.
$test_timeout = defined $ENV{FILTERTEST_TIMEOUT} ?
	$ENV{FILTERTEST_TIMEOUT} : 1;

if ($test_timeout eq '0') {
	print "INFO: Not using a test timeout (FILTERTEST_TIMEOUT=0).\n";
} elsif (defined $ENV{TIMEOUT_BIN}) {
	$timeout_bin = $ENV{TIMEOUT_BIN};
	if (system ($timeout_bin, '0.1', 'sleep', '10') >> 8 != TIMED_OUT) {
		print STDERR "ERROR: TIMEOUT_BIN='$timeout_bin' is not usable.\n";
		exit 1;
	}
	print "INFO: Using a test timeout of $test_timeout (TIMEOUT_BIN='$timeout_bin').\n";
} elsif (system ('timeout', '0.1', 'sleep', '10') >> 8 != TIMED_OUT) {
	print "WARNING: Not using a test timeout (the default 'timeout' is not usable).\n";
} else {
	$timeout_bin = 'timeout';
	printf "INFO: Using a test timeout of %s.\n", $test_timeout;
}

init_tmpdir 'libpcap_TESTrun';
exit test_and_report @ready_to_run;
