    1  04:48:55.079276 BRCM tag OP: EG, CID: 0, RC: exception, TC: 0, port: 5, 68:05:ca:18:47:70 > 8a:62:38:14:5d:0b, ethertype IPv4 (0x0800), length 98: *********** > ***********51: ICMP echo request, id 2129, seq 1, length 64
    2  04:48:55.079338 BRCM tag OP: IG, TC: 0, TE: None, TS: 0, DST map: 0x0020, 8a:62:38:14:5d:0b > 68:05:ca:18:47:70, ethertype IPv4 (0x0800), length 98: ***********51 > ***********: ICMP echo reply, id 2129, seq 1, length 64
    3  04:48:56.088510 BRCM tag OP: EG, CID: 0, RC: exception, TC: 0, port: 5, 68:05:ca:18:47:70 > 8a:62:38:14:5d:0b, ethertype IPv4 (0x0800), length 98: *********** > ***********51: ICMP echo request, id 2129, seq 2, length 64
    4  04:48:56.088532 BRCM tag OP: IG, TC: 0, TE: None, TS: 0, DST map: 0x0020, 8a:62:38:14:5d:0b > 68:05:ca:18:47:70, ethertype IPv4 (0x0800), length 98: ***********51 > ***********: ICMP echo reply, id 2129, seq 2, length 64
    5  04:48:57.112480 BRCM tag OP: EG, CID: 0, RC: exception, TC: 0, port: 5, 68:05:ca:18:47:70 > 8a:62:38:14:5d:0b, ethertype IPv4 (0x0800), length 98: *********** > ***********51: ICMP echo request, id 2129, seq 3, length 64
    6  04:48:57.112498 BRCM tag OP: IG, TC: 0, TE: None, TS: 0, DST map: 0x0020, 8a:62:38:14:5d:0b > 68:05:ca:18:47:70, ethertype IPv4 (0x0800), length 98: ***********51 > ***********: ICMP echo reply, id 2129, seq 3, length 64
    7  04:48:58.136493 BRCM tag OP: EG, CID: 0, RC: exception, TC: 0, port: 5, 68:05:ca:18:47:70 > 8a:62:38:14:5d:0b, ethertype IPv4 (0x0800), length 98: *********** > ***********51: ICMP echo request, id 2129, seq 4, length 64
    8  04:48:58.136510 BRCM tag OP: IG, TC: 0, TE: None, TS: 0, DST map: 0x0020, 8a:62:38:14:5d:0b > 68:05:ca:18:47:70, ethertype IPv4 (0x0800), length 98: ***********51 > ***********: ICMP echo reply, id 2129, seq 4, length 64
    9  04:49:00.184465 BRCM tag OP: EG, CID: 0, RC: exception, TC: 0, port: 5, 68:05:ca:18:47:70 > 8a:62:38:14:5d:0b, ethertype ARP (0x0806), length 60: Request who-has ***********51 tell ***********, length 46
   10  04:49:00.184481 BRCM tag OP: IG, TC: 0, TE: None, TS: 0, DST map: 0x0020, 8a:62:38:14:5d:0b > 68:05:ca:18:47:70, ethertype ARP (0x0806), length 64: Reply ***********51 is-at 8a:62:38:14:5d:0b, length 50
   11  04:49:00.383017 BRCM tag OP: IG, TC: 0, TE: None, TS: 0, DST map: 0x0020, 8a:62:38:14:5d:0b > 68:05:ca:18:47:70, ethertype ARP (0x0806), length 64: Request who-has *********** tell ***********51, length 50
   12  04:49:00.383155 BRCM tag OP: EG, CID: 0, RC: exception, TC: 0, port: 5, 68:05:ca:18:47:70 > 8a:62:38:14:5d:0b, ethertype ARP (0x0806), length 60: Reply *********** is-at 68:05:ca:18:47:70, length 46
   13  04:49:00.607178 BRCM tag OP: EG, CID: 0, RC: exception, TC: 0, port: 5, 68:05:ca:18:47:70 > ff:ff:ff:ff:ff:ff, ethertype IPv4 (0x0800), length 98: *********** > *************: ICMP echo request, id 2132, seq 1, length 64
   14  04:49:01.624629 BRCM tag OP: EG, CID: 0, RC: exception, TC: 0, port: 5, 68:05:ca:18:47:70 > ff:ff:ff:ff:ff:ff, ethertype IPv4 (0x0800), length 98: *********** > *************: ICMP echo request, id 2132, seq 2, length 64
   15  04:49:02.648563 BRCM tag OP: EG, CID: 0, RC: exception, TC: 0, port: 5, 68:05:ca:18:47:70 > ff:ff:ff:ff:ff:ff, ethertype IPv4 (0x0800), length 98: *********** > *************: ICMP echo request, id 2132, seq 3, length 64
