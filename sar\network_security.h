/*
 * network_security.h: 网络安全监控相关的数据结构和函数声明
 */

#ifndef _NETWORK_SECURITY_H
#define _NETWORK_SECURITY_H

// 网络安全统计数据结构（按照sysstat风格定义）
struct stats_net_security {
    // TCP连接统计
    unsigned int total_connections;
    unsigned int established_connections;
    unsigned int syn_recv_connections;
    unsigned int time_wait_connections;
    unsigned int listen_connections;

    // 单IP连接统计
    unsigned int max_connections_per_ip;

    // MySQL数据库连接监控统计
    unsigned int mysql_connections_per_second;   // MySQL连接数/秒
    unsigned int mysql_aborted_connections;      // MySQL断开连接数
    unsigned int max_mysql_conn_per_ip;          // 单IP最大MySQL连接数
    char high_freq_mysql_client_ip[16];          // 高频MySQL客户端IP
    unsigned int mysql_monitoring_enabled;       // MySQL监控是否启用(0=禁用,1=启用)
    char mysql_variant[32];                      // MySQL发行版类型(MariaDB/MySQL/Percona等)
    int mysql_ports[8];                          // MySQL监听的端口列表
    int mysql_port_count;                        // MySQL端口数量
    char mysql_log_status[64];                   // MySQL日志状态说明
    char mysql_log_path[256];                    // MySQL日志文件路径

    // 网络流量统计
    unsigned long long rx_bytes;
    unsigned long long tx_bytes;
    unsigned long long rx_packets;
    unsigned long long tx_packets;
    unsigned long long rx_bytes_per_sec;
    unsigned long long tx_bytes_per_sec;
    unsigned long long rx_packets_per_sec;
    unsigned long long tx_packets_per_sec;

    // 异常检测
    int anomaly_score;
    int trigger_packet_analysis;
    char anomaly_reason[64];
    
    // 端口统计
    struct {
        int port;                   // 端口号
        int total_connections;      // 总连接数
        int established;            // ESTABLISHED连接数
        int time_wait;              // TIME_WAIT连接数
        int syn_recv;               // SYN_RECV连接数
        int listen;                 // LISTEN连接数
        char service_name[16];      // 服务名称
    } port_stats[10];               // 最多统计10个端口
    int port_stats_count;           // 实际统计的端口数量

    // 详细IP+端口统计
    struct {
        char ip_address[16];        // IP地址
        int port;                   // 端口号
        int connection_count;       // 连接数
        char service_name[16];      // 服务名称
        char process_info[32];      // 进程信息：进程名(PID)
    } ip_port_stats[20];            // 最多统计20个IP+端口组合
    int ip_port_stats_count;        // 实际统计的IP+端口组合数量
};

#define STATS_NET_SECURITY_SIZE (sizeof(struct stats_net_security))

// 函数声明
void read_net_security(struct stats_net_security *st_net_security, int deep_monitoring_mode);
void print_net_security_stats(struct stats_net_security *st_net_security);

#endif /* _NETWORK_SECURITY_H */
