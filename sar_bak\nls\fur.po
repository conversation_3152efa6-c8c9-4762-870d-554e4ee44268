# FRIULIAN TRANSLATION OF SYSSTAT.
# Copyright (C) 2016 THE SYSSTAT'S COPYRIGHT HOLDER
# This file is distributed under the same license as the sysstat package.
# <AUTHOR> <EMAIL>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: sysstat-11.5.3\n"
"Report-Msgid-Bugs-To: sysstat <at> orange.fr\n"
"POT-Creation-Date: 2016-12-06 09:16+0100\n"
"PO-Revision-Date: 2016-12-28 22:19+0100\n"
"Last-Translator: Fabio Tom<PERSON> <<EMAIL>>\n"
"Language-Team: Friulian <<EMAIL>>\n"
"Language: fur\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.11\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: cifsiostat.c:70 mpstat.c:98 pidstat.c:87 iostat.c:86 sar.c:96 tapestat.c:95
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ]\n"
msgstr "Ûs: %s [ opzions ] [ <interval> [ <voltis> ] ]\n"

#: cifsiostat.c:74
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"
msgstr ""
"Lis opzions a son:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"

#: cifsiostat.c:77
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
msgstr ""
"Lis opzions a son:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#: sadc.c:89
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
msgstr "Ûs: %s [ opzions ] [ <interval> [ <voltis> ] ] [ <filedijessude> ]\n"

#: sadc.c:92
#, c-format
msgid ""
"Options are:\n"
"[ -C <comment> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"
msgstr ""
"Lis opzions a son:\n"
"[ -C <coment> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"

#: sadc.c:267
#, c-format
msgid "Cannot write data to system activity file: %s\n"
msgstr "Impussibil scrivi dâts tal file des ativitâts di sisteme: %s\n"

#: sadc.c:563
#, c-format
msgid "Cannot write system activity file header: %s\n"
msgstr "Impussibil scrivi la intestazion dal file des ativitâts di sisteme: %s\n"

#: sadc.c:763 sadc.c:772 sadc.c:839 ioconf.c:510 rd_stats.c:69
#: sa_common.c:1243 count.c:118
#, c-format
msgid "Cannot open %s: %s\n"
msgstr "Impussibil vierzi %s: %s\n"

#: sadc.c:1019
#, c-format
msgid "Cannot append data to that file (%s)\n"
msgstr "Impussibil zontâ dâts a chel file (%s)\n"

#: mpstat.c:101
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -o JSON ] [ -P { <cpu_list> | ON | ALL } ]\n"
msgstr ""
"Lis opzions a son:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -o JSON ] [ -P { <liste_cpu> | ON | ALL } ]\n"

#: mpstat.c:1210 pidstat.c:2334 sar.c:359
msgid "Average:"
msgstr "Medie:"

#: sadf.c:86
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <datafile> | -[0-9]+ ]\n"
msgstr "Ûs: %s [ opzions ] [ <interval> [ <voltis> ] ] [ <filedaidâts> | -[0-9]+ ]\n"

#: sadf.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -C ] [ -c | -d | -g | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -O <opts> [,...] ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
"[ -- <sar_options> ]\n"
msgstr ""
"Lis opzions a son:\n"
"[ -C ] [ -c | -d | -g | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -O <opts> [,...] ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
"[ -- <opzions_sar> ]\n"

#: sa_common.c:1039
#, c-format
msgid "Error while reading system activity file: %s\n"
msgstr "Erôr inte leture dal file des ativitâts di sisteme: %s\n"

#: sa_common.c:1049
#, c-format
msgid "End of system activity file unexpected\n"
msgstr "Fin inspietade dal file des ativitâts di sisteme\n"

#: sa_common.c:1068
#, c-format
msgid "File created by sar/sadc from sysstat version %d.%d.%d"
msgstr "File creât doprant sar/sadc di sysstat version %d.%d.%d"

#: sa_common.c:1101
#, c-format
msgid "Invalid system activity file: %s\n"
msgstr "File des ativitâts di sisteme no valit: %s\n"

#: sa_common.c:1113
#, c-format
msgid "Endian format mismatch\n"
msgstr "Formât di ordin di byte (Endian) no corispuindint\n"

#: sa_common.c:1117
#, c-format
msgid "Current sysstat version cannot read the format of this file (%#x)\n"
msgstr "La version atuâl di sysstat no rive a lei il formât di chest file (%#x)\n"

#: sa_common.c:1246
#, c-format
msgid "Please check if data collecting is enabled\n"
msgstr "Controle se la colezion dâts e je abilitade\n"

#: sa_common.c:1439
#, c-format
msgid "Requested activities not available in file %s\n"
msgstr "Lis ativitâts domandadis no son disponibilis tal file %s\n"

#: sa_common.c:1628 iostat.c:1682
#, c-format
msgid "Invalid type of persistent device name\n"
msgstr "Gjenar di non dispositîf persistent no valit\n"

#: pidstat.c:90
#, c-format
msgid ""
"Options are:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ] [ -U [ <username> ] ]\n"
"[ -u ] [ -V ] [ -v ] [ -w ] [ -C <command> ] [ -G <process_name> ] [ --human ]\n"
"[ -p { <pid> [,...] | SELF | ALL } ] [ -T { TASK | CHILD | ALL } ]\n"
msgstr ""
"Lis opzions a son:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ] [ -U [ <nonutent> ] ]\n"
"[ -u ] [ -V ] [ -v ] [ -w ] [ -C <comant> ] [ -G <non_procès> ] [ --human ]\n"
"[ -p { <pid> [,...] | SELF | ALL } ] [ -T { TASK | CHILD | ALL } ]\n"

#: pidstat.c:239 sar.c:1045
#, c-format
msgid "Requested activities not available\n"
msgstr "Lis ativitâts domandadis no son disponibilis\n"

#: sadf_misc.c:749
#, c-format
msgid "System activity data file: %s (%#x)\n"
msgstr "File dâts des ativitâts dal sisteme: %s (%#x)\n"

#: sadf_misc.c:758
#, c-format
msgid "Genuine sa datafile: %s (%x)\n"
msgstr "File di dâts sa autentic: %s (%x)\n"

#: sadf_misc.c:759
msgid "no"
msgstr "no"

#: sadf_misc.c:759
msgid "yes"
msgstr "sì"

#: sadf_misc.c:762
#, c-format
msgid "Host: "
msgstr "Host: "

#: sadf_misc.c:769
#, c-format
msgid "Number of CPU for last samples in file: %u\n"
msgstr "Numar di CPU pai ultins campionaments tal file: %u\n"

#: sadf_misc.c:775
#, c-format
msgid "File date: %s\n"
msgstr "Date file: %s\n"

#: sadf_misc.c:778
#, c-format
msgid "File time: "
msgstr "Ore file: "

#: sadf_misc.c:783
#, c-format
msgid "Size of a long int: %d\n"
msgstr "Dimension di un intîr lunc: %d\n"

#: sadf_misc.c:789
#, c-format
msgid "List of activities:\n"
msgstr "Liste des ativitâts:\n"

#: sadf_misc.c:802
#, c-format
msgid "\t[Unknown activity format]"
msgstr "\t[Formât ativitât no cognossût]"

#: count.c:169
#, c-format
msgid "Cannot handle so many processors!\n"
msgstr "Impussibil gjestî cussì tancj processôrs!\n"

#: iostat.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ] [ --debuginfo ]\n"
msgstr ""
"Lis opzions a son:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <non_grup> ] [ -p [ <dispositîf> [,...] | ALL ] ]\n"
"[ <dispositîf> [...] | ALL ] [ --debuginfo ]\n"

#: iostat.c:95
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ]\n"
msgstr ""
"Lis opzions a son:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <non_grup> ] [ -p [ <dispositîf> [,...] | ALL ] ]\n"
"[ <dispositîf> [...] | ALL ]\n"

#: iostat.c:326
#, c-format
msgid "Cannot find disk data\n"
msgstr "Impussibil cjatâ i dâts dal disc\n"

#: common.c:78
#, c-format
msgid "sysstat version %s\n"
msgstr "sysstat version %s\n"

#: pr_stats.c:2527 pr_stats.c:2538 pr_stats.c:2645 pr_stats.c:2656
msgid "Summary:"
msgstr "Sintesi:"

#: pr_stats.c:2580
msgid "Other devices not listed here"
msgstr "Altris dispositîfs no in liste chi"

#: sar.c:111
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ MOUNT ] ] [ -H ] [ -h ]\n"
"[ -p ] [ -q ] [ -R ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --human ] [ --sadc ]\n"
"[ -I { <int_list> | SUM | ALL } ] [ -P { <cpu_list> | ALL } ]\n"
"[ -m { <keyword> [,...] | ALL } ] [ -n { <keyword> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filename> ] | -o [ <filename> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
msgstr ""
"Lis opzions a son:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ MOUNT ] ] [ -H ] [ -h ]\n"
"[ -p ] [ -q ] [ -R ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --human ] [ --sadc ]\n"
"[ -I { <liste_interuzions> | SUM | ALL } ] [ -P { <liste_cpu> | ALL } ]\n"
"[ -m { <perauleclâf> [,...] | ALL } ] [ -n { <perauleclâf> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <nonfile> ] | -o [ <nonfile> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"

#: sar.c:134
#, c-format
msgid "Main options and reports:\n"
msgstr "Principâls opzions e rapuarts:\n"

#: sar.c:135
#, c-format
msgid "\t-B\tPaging statistics\n"
msgstr "\t-B\tStatistichis su la pagjinazion\n"

#: sar.c:136
#, c-format
msgid "\t-b\tI/O and transfer rate statistics\n"
msgstr "\t-b\tStatistichis su I/O e velocitât di trasferiment\n"

#: sar.c:137
#, c-format
msgid "\t-d\tBlock devices statistics\n"
msgstr "\t-d\tStatistichis sui dispositîfs a blocs\n"

#: sar.c:138
#, c-format
msgid "\t-F [ MOUNT ]\n"
msgstr "\t-F [ MOUNT ]\n"

#: sar.c:139
#, c-format
msgid "\t\tFilesystems statistics\n"
msgstr "\t\tStatistichis sui filesystem\n"

#: sar.c:140
#, c-format
msgid "\t-H\tHugepages utilization statistics\n"
msgstr "\t-H\tStatistichis sul ûs di Hugepages\n"

#: sar.c:141
#, c-format
msgid ""
"\t-I { <int_list> | SUM | ALL }\n"
"\t\tInterrupts statistics\n"
msgstr ""
"\t-I { <liste_interuzions> | SUM | ALL }\n"
"\t\tStatistichis sui interrupt\n"

#: sar.c:143
#, c-format
msgid ""
"\t-m { <keyword> [,...] | ALL }\n"
"\t\tPower management statistics\n"
"\t\tKeywords are:\n"
"\t\tCPU\tCPU instantaneous clock frequency\n"
"\t\tFAN\tFans speed\n"
"\t\tFREQ\tCPU average clock frequency\n"
"\t\tIN\tVoltage inputs\n"
"\t\tTEMP\tDevices temperature\n"
"\t\tUSB\tUSB devices plugged into the system\n"
msgstr ""
"\t-m { <perauleclâf> [,...] | ALL }\n"
"\t\tStatistichis su la gjestion de alimentazion\n"
"\t\tLis peraulis clâf a son:\n"
"\t\tCPU\tFrecuence di clock istantanie dal CPU\n"
"\t\tFAN\tVelocitât svintulis\n"
"\t\tFREQ\tFrecuence di clock medie dal CPU\n"
"\t\tIN\tVoltaç di ingrès\n"
"\t\tTEMP\tTemperadure dispositîfs\n"
"\t\tUSB\tDispositîfs USB tacâts tal sisteme\n"

#: sar.c:152
#, c-format
msgid ""
"\t-n { <keyword> [,...] | ALL }\n"
"\t\tNetwork statistics\n"
"\t\tKeywords are:\n"
"\t\tDEV\tNetwork interfaces\n"
"\t\tEDEV\tNetwork interfaces (errors)\n"
"\t\tNFS\tNFS client\n"
"\t\tNFSD\tNFS server\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP traffic\t(v4)\n"
"\t\tEIP\tIP traffic\t(v4) (errors)\n"
"\t\tICMP\tICMP traffic\t(v4)\n"
"\t\tEICMP\tICMP traffic\t(v4) (errors)\n"
"\t\tTCP\tTCP traffic\t(v4)\n"
"\t\tETCP\tTCP traffic\t(v4) (errors)\n"
"\t\tUDP\tUDP traffic\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP traffic\t(v6)\n"
"\t\tEIP6\tIP traffic\t(v6) (errors)\n"
"\t\tICMP6\tICMP traffic\t(v6)\n"
"\t\tEICMP6\tICMP traffic\t(v6) (errors)\n"
"\t\tUDP6\tUDP traffic\t(v6)\n"
"\t\tFC\tFibre channel HBAs\n"
"\t\tSOFT\tSoftware-based network processing\n"
msgstr ""
"\t-n { <perauleclâf> [,...] | ALL }\n"
"\t\tStatistichis rêt\n"
"\t\tLis peraulis clâf a son:\n"
"\t\tDEV\tInterfacis rêt\n"
"\t\tEDEV\tInterfacis rêt(erôrs)\n"
"\t\tNFS\tClient NFS\n"
"\t\tNFSD\tServidôr NFS\n"
"\t\tSOCK\tSocket\t(v4)\n"
"\t\tIP\tTrafic IP\t(v4)\n"
"\t\tEIP\tTrafic IP\t(v4) (erôrs)\n"
"\t\tICMP\tTrafic ICMP\t(v4)\n"
"\t\tEICMP\tTrafic ICMP\t(v4) (erôrs)\n"
"\t\tTCP\tTrafic TCP\t(v4)\n"
"\t\tETCP\tTrafic TCP\t(v4) (erôrs)\n"
"\t\tUDP\tTrafic UDP\t(v4)\n"
"\t\tSOCK6\tSocket\t(v6)\n"
"\t\tIP6\tTrafic IP\t(v6)\n"
"\t\tEIP6\tTrafic IP\t(v6) (erôrs)\n"
"\t\tICMP6\tTrafic ICMP\t(v6)\n"
"\t\tEICMP6\tTrafic ICMP\t(v6) (erôrs)\n"
"\t\tUDP6\tTrafic UDP\t(v6)\n"
"\t\tFC\tCanâl di fibre HBAs\n"
"\t\tSOFT\tElaborazion rêt basade su software\n"

#: sar.c:175
#, c-format
msgid "\t-q\tQueue length and load average statistics\n"
msgstr "\t-q\tStatistichis su la lungjece de code e il caric medi\n"

#: sar.c:176
#, c-format
msgid "\t-R\tMemory statistics\n"
msgstr "\t-R\tStatistichis memorie\n"

#: sar.c:177
#, c-format
msgid ""
"\t-r [ ALL ]\n"
"\t\tMemory utilization statistics\n"
msgstr ""
"\t-r [ ALL ]\n"
"\t\tStatistichis di utilizazion de memorie\n"

#: sar.c:179
#, c-format
msgid "\t-S\tSwap space utilization statistics\n"
msgstr "\t-S\tStatistichis di utilizazion dal spazi di swap\n"

#: sar.c:180
#, c-format
msgid ""
"\t-u [ ALL ]\n"
"\t\tCPU utilization statistics\n"
msgstr ""
"\t-u [ ALL ]\n"
"\t\tStatistichis di utilizazion de CPU\n"

#: sar.c:182
#, c-format
msgid "\t-v\tKernel tables statistics\n"
msgstr "\t-v\tStatistichis su lis tabelis dal kernel\n"

#: sar.c:183
#, c-format
msgid "\t-W\tSwapping statistics\n"
msgstr "\t-W\tStatistichis sul swap\n"

#: sar.c:184
#, c-format
msgid "\t-w\tTask creation and system switching statistics\n"
msgstr "\t-w\tStatistichis su la creazion dal compit e sui cambiaments dal sisteme\n"

#: sar.c:185
#, c-format
msgid "\t-y\tTTY devices statistics\n"
msgstr "\t-y\tStatistichis dispositîfs TTY\n"

#: sar.c:199
#, c-format
msgid "Data collector will be sought in PATH\n"
msgstr "Il coletôr dâts al vignarà cirût intal PATH\n"

#: sar.c:202
#, c-format
msgid "Data collector found: %s\n"
msgstr "Cjatât coletôr dâts: %s\n"

#: sar.c:261
#, c-format
msgid "End of data collecting unexpected\n"
msgstr "Fin de colezion dâts inspietade\n"

#: sar.c:814
#, c-format
msgid "Using a wrong data collector from a different sysstat version\n"
msgstr "Si sta doprant un coletôr dâts sbaliât che al ven di une version di sysstat diferente\n"

#: sar.c:866
#, c-format
msgid "Inconsistent input data\n"
msgstr "Dâts di jentrade inconsistents\n"

#: sar.c:1352
#, c-format
msgid "-f and -o options are mutually exclusive\n"
msgstr "Lis opzions -f e -o si escludin un cun chel altri\n"

#: sar.c:1358
#, c-format
msgid "Not reading from a system activity file (use -f option)\n"
msgstr "No si sta leint di un file di ativitât di sisteme (dopre la opzion -f)\n"

#: sar.c:1494
#, c-format
msgid "Cannot find the data collector (%s)\n"
msgstr "Impussibil cjatâ il coletôr dâts (%s)\n"

#: sa_conv.c:69
#, c-format
msgid "Cannot convert the format of this file\n"
msgstr "Impussibil convertî il formât di chest file\n"

#: sa_conv.c:562
#, c-format
msgid ""
"\n"
"CPU activity not found in file. Aborting...\n"
msgstr ""
"\n"
"No son stadis cjatadis ativitâts de CPU intal file. Daûr a interompi...\n"

#: sa_conv.c:578
#, c-format
msgid ""
"\n"
"Invalid data found. Aborting...\n"
msgstr ""
"\n"
"Cjatâts dâts no valits. Daûr a interompi...\n"

#: sa_conv.c:897
#, c-format
msgid "Statistics: "
msgstr "Statistichis: "

#: sa_conv.c:1016
#, c-format
msgid ""
"\n"
"File successfully converted to sysstat format version %s\n"
msgstr ""
"\n"
"File convertît cun sucès al formât sysstat version %s\n"

#: tapestat.c:97
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"
msgstr ""
"Lis opzions a son:\n"
"[ --human ] [ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"

#: tapestat.c:263
#, c-format
msgid "No tape drives with statistics found\n"
msgstr "Nissune unitât a curdele magnetiche cun statistichis cjatade\n"
