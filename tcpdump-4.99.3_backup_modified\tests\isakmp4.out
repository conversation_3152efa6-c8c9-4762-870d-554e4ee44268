    1  00:00:00.000000 ARP, Request who-has ********** tell ***********, length 28
    2  00:00:00.000000 ARP, Reply ********** is-at 10:00:00:64:64:23, length 28
    3  00:00:00.000000 IP ***********.500 > **********.500: isakmp: phase 1 I ident
    4  00:00:00.000000 IP **********.500 > ***********.500: isakmp: phase 1 R ident
    5  00:00:00.000000 IP ***********.500 > **********.500: isakmp: phase 1 I ident
    6  00:00:00.000000 IP **********.500 > ***********.500: isakmp: phase 1 R ident
    7  00:00:00.000000 IP ***********.4500 > **********.4500: NONESP-encap: isakmp: phase 1 I ident[E]
    8  00:00:00.000000 IP **********.4500 > ***********.4500: NONESP-encap: isakmp: phase 1 R ident[E]
    9  00:00:00.000000 IP ***********.4500 > **********.4500: NONESP-encap: isakmp: phase 2/others I oakley-quick[E]
   10  00:00:00.000000 IP **********.4500 > ***********.4500: NONESP-encap: isakmp: phase 2/others R oakley-quick[E]
   11  00:00:00.000000 IP ***********.4500 > **********.4500: NONESP-encap: isakmp: phase 2/others I oakley-quick[E]
   12  00:00:00.000000 IP ***********.4500 > **********.4500: UDP-encap: ESP(spi=0xf4dc0ae5,seq=0x1), length 132
   13  00:00:00.000000 ARP, Request who-has *********** tell **********, length 28
   14  00:00:00.000000 ARP, Reply *********** is-at 10:00:00:de:ad:ba, length 28
   15  00:00:00.000000 IP **********.4500 > ***********.4500: NONESP-encap: isakmp: phase 2/others R oakley-quick[E]
   16  00:00:00.000000 IP ***********.4500 > **********.4500: NONESP-encap: isakmp: phase 2/others I oakley-quick[E]
   17  00:00:00.000000 IP ***********.4500 > **********.4500: UDP-encap: ESP(spi=0xf4dc0ae5,seq=0x2), length 132
   18  00:00:00.000000 IP ***********.4500 > **********.4500: isakmp-nat-keep-alive
   19  00:00:00.000000 IP ***********.4500 > **********.4500: UDP-encap: ESP(spi=0xf4dc0ae5,seq=0x3), length 132
   20  00:00:00.000000 IP **********.4500 > ***********.4500: NONESP-encap: isakmp: phase 2/others R oakley-quick[E]
   21  00:00:00.000000 IP ***********.4500 > **********.4500: NONESP-encap: isakmp: phase 2/others I oakley-quick[E]
   22  00:00:00.000000 IP ***********.4500 > **********.4500: UDP-encap: ESP(spi=0xf4dc0ae5,seq=0x4), length 132
   23  00:00:00.000000 IP ***********.4500 > **********.4500: isakmp-nat-keep-alive
   24  00:00:00.000000 IP ***********.4500 > **********.4500: UDP-encap: ESP(spi=0xf4dc0ae5,seq=0x5), length 132
   25  00:00:00.000000 IP ***********.4500 > **********.4500: UDP-encap: ESP(spi=0xf4dc0ae5,seq=0x6), length 132
   26  00:00:00.000000 ARP, Request who-has ********** tell ***********, length 28
   27  00:00:00.000000 ARP, Reply ********** is-at 10:00:00:64:64:23, length 28
   28  00:00:00.000000 IP ***********.4500 > **********.4500: isakmp-nat-keep-alive
   29  00:00:00.000000 IP ***********.4500 > **********.4500: UDP-encap: ESP(spi=0xf4dc0ae5,seq=0x7), length 132
   30  00:00:00.000000 IP **********.4500 > ***********.4500: NONESP-encap: isakmp: phase 2/others R oakley-quick[E]
   31  00:00:00.000000 IP ***********.4500 > **********.4500: UDP-encap: ESP(spi=0xf4dc0ae5,seq=0x8), length 132
   32  00:00:00.000000 ARP, Request who-has *********** tell **********, length 28
   33  00:00:00.000000 ARP, Reply *********** is-at 10:00:00:de:ad:ba, length 28
   34  00:00:00.000000 IP ***********.4500 > **********.4500: isakmp-nat-keep-alive
   35  00:00:00.000000 IP **********.4500 > ***********.4500: NONESP-encap: isakmp: phase 2/others R inf[E]
