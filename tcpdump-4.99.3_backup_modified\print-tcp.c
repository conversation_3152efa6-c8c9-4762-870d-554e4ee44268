/*	$NetBSD: print-tcp.c,v 1.9 2007/07/26 18:15:12 plunky Exp $	*/

/*
 * Copyright (c) 1988, 1989, 1990, 1991, 1992, 1993, 1994, 1995, 1996, 1997
 *	The Regents of the University of California.  All rights reserved.
 *
 * Copyright (c) 1999-2004 The tcpdump.org project
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that: (1) source code distributions
 * retain the above copyright notice and this paragraph in its entirety, (2)
 * distributions including binary code include the above copyright notice and
 * this paragraph in its entirety in the documentation or other materials
 * provided with the distribution, and (3) all advertising materials mentioning
 * features or use of this software display the following acknowledgement:
 * ``This product includes software developed by the University of California,
 * Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
 * the University nor the names of its contributors may be used to endorse
 * or promote products derived from this software without specific prior
 * written permission.
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
 * WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
 */

/*
 * TCP Flags Reference:
 *
 * Individual flags (bit values):
 * FIN = 1   (0x01) - Finish, no more data
 * SYN = 2   (0x02) - Synchronize sequence numbers
 * RST = 4   (0x04) - Reset connection
 * PSH = 8   (0x08) - Push data immediately
 * ACK = 16  (0x10) - Acknowledgment field significant
 * URG = 32  (0x20) - Urgent pointer field significant
 *
 * Common flag combinations:
 * flags == 2  = SYN           (S)   - Connection initiation
 * flags == 18 = SYN+ACK       (S.)  - Connection acknowledgment (2+16=18)
 * flags == 16 = ACK           (.)   - Data acknowledgment
 * flags == 24 = ACK+PSH       (P.)  - Data push (16+8=24)
 * flags == 17 = FIN+ACK       (F.)  - Connection termination (1+16=17)
 * flags == 4  = RST           (R)   - Connection reset
 * flags == 20 = ACK+RST       (R.)  - Reset with ack (4+16=20)
 * flags == 25 = FIN+ACK+PSH   (FP.) - Finish with push (1+16+8=25)
 */

/* \summary: TCP printer */

#ifndef lint
#else
__RCSID("$NetBSD: print-tcp.c,v 1.8 2007/07/24 11:53:48 drochner Exp $");
#endif

#ifdef HAVE_CONFIG_H
#include <config.h>
#endif

#include "netdissect-stdinc.h"

#include <stdlib.h>
#include <string.h>

#include "netdissect.h"
#include "addrtoname.h"
#include "extract.h"

#include "diag-control.h"

#include "tcp.h"
// add 2024
#include "my.h"
#include "../sar/sa1.h"
#include <time.h>

// add 2024-11-4
#include <sys/time.h>
#include "../libpcap/pcap.h"
#include "../agent/top.h"

#include "ip.h"
#include "ip6.h"
#include "ipproto.h"
#include "rpc_auth.h"
#include "rpc_msg.h"

#ifdef HAVE_LIBCRYPTO
#include <openssl/md5.h>
#include "signature.h"

static int tcp_verify_signature(netdissect_options *ndo,
                                const struct ip *ip, const struct tcphdr *tp,
                                const u_char *data, u_int length, const u_char *rcvsig);
#endif

static void print_tcp_rst_data(netdissect_options *, const u_char *sp, u_int length);
static void print_tcp_fastopen_option(netdissect_options *ndo, const u_char *cp,
                                      u_int datalen, int exp);

#define MAX_RST_DATA_LEN 30
extern int group;
extern int o2;
int test;
extern os_data *os;

struct tha
{
        nd_ipv4 src;
        nd_ipv4 dst;
        u_int port;
};

struct tcp_seq_hash
{
        struct tcp_seq_hash *nxt;
        struct tha addr;
        uint32_t seq;
        uint32_t ack;
};

struct tha6
{
        nd_ipv6 src;
        nd_ipv6 dst;
        u_int port;
};

struct tcp_seq_hash6
{
        struct tcp_seq_hash6 *nxt;
        struct tha6 addr;
        uint32_t seq;
        uint32_t ack;
};

#define TSEQ_HASHSIZE 919

/* These tcp options do not have the size octet */
#define ZEROLENOPT(o) ((o) == TCPOPT_EOL || (o) == TCPOPT_NOP)

static struct tcp_seq_hash tcp_seq_hash4[TSEQ_HASHSIZE];
static struct tcp_seq_hash6 tcp_seq_hash6[TSEQ_HASHSIZE];

static const struct tok tcp_flag_values[] = {
    {TH_FIN, "F"},
    {TH_SYN, "S"},
    {TH_RST, "R"},
    {TH_PUSH, "P"},
    {TH_ACK, "."},
    {TH_URG, "U"},
    {TH_ECNECHO, "E"},
    {TH_CWR, "W"},
    {0, NULL}};

static const struct tok tcp_option_values[] = {
    {TCPOPT_EOL, "eol"},
    {TCPOPT_NOP, "nop"},
    {TCPOPT_MAXSEG, "mss"},
    {TCPOPT_WSCALE, "wscale"},
    {TCPOPT_SACKOK, "sackOK"},
    {TCPOPT_SACK, "sack"},
    {TCPOPT_ECHO, "echo"},
    {TCPOPT_ECHOREPLY, "echoreply"},
    {TCPOPT_TIMESTAMP, "TS"},
    {TCPOPT_CC, "cc"},
    {TCPOPT_CCNEW, "ccnew"},
    {TCPOPT_CCECHO, ""},
    {TCPOPT_SIGNATURE, "md5"},
    {TCPOPT_SCPS, "scps"},
    {TCPOPT_UTO, "uto"},
    {TCPOPT_TCPAO, "tcp-ao"},
    {TCPOPT_MPTCP, "mptcp"},
    {TCPOPT_FASTOPEN, "tfo"},
    {TCPOPT_EXPERIMENT2, "exp"},
    {0, NULL}};

static uint16_t
tcp_cksum(netdissect_options *ndo,
          const struct ip *ip,
          const struct tcphdr *tp,
          u_int len)
{
        return nextproto4_cksum(ndo, ip, (const uint8_t *)tp, len, len,
                                IPPROTO_TCP);
}

static uint16_t
tcp6_cksum(netdissect_options *ndo,
           const struct ip6_hdr *ip6,
           const struct tcphdr *tp,
           u_int len)
{
        return nextproto6_cksum(ndo, ip6, (const uint8_t *)tp, len, len,
                                IPPROTO_TCP);
}

static void *alloc_r(void *ptr, size_t num)
{
        void *pv;

        if (!num)
                ++num;
        if (!(pv = realloc(ptr, num)))
                perror("alloc_r failed");
        ;
        return pv;
} // end: alloc_r

int fast_compare(const char *ptr0, const char *ptr1, int len)
{
        int fast = len / sizeof(size_t) + 1;
        int offset = (fast - 1) * sizeof(size_t);
        int current_block = 0;

        if (len <= sizeof(size_t))
        {
                fast = 0;
        }

        size_t *lptr0 = (size_t *)ptr0;
        size_t *lptr1 = (size_t *)ptr1;

        while (current_block < fast)
        {
                if ((lptr0[current_block] ^ lptr1[current_block]))
                {
                        int pos;
                        for (pos = current_block * sizeof(size_t); pos < len; ++pos)
                        {
                                if ((ptr0[pos] ^ ptr1[pos]) || (ptr0[pos] == 0) || (ptr1[pos] == 0))
                                {
                                        return (int)((unsigned char)ptr0[pos] - (unsigned char)ptr1[pos]);
                                }
                        }
                }

                ++current_block;
        }

        while (len > offset)
        {
                if ((ptr0[offset] ^ ptr1[offset]))
                {
                        return (int)((unsigned char)ptr0[offset] - (unsigned char)ptr1[offset]);
                }
                ++offset;
        }

        return 0;
}

int print_tcp1_old( const char* src,const char* dst,uint16_t sport,uint16_t dport){
                        int f=8000;
                        if ( my_tcp1 == NULL )
                        {
                        //update 2024-10-12
                        my_tcp1=calloc(1,f*sizeof(my_tcp));
                        //first
                        snprintf(my_tcp1->ip_src,16,"%s",src);
                        snprintf(my_tcp1->ip_dst,16,"%s",dst);
                        my_tcp1->count++;
//			free(src);
//			free(dst);
                        group++;
                        goto next;
                        }

                        int i;
                        for (i=0;i< group;i++)
                        {
                                 my_tcp *m=&my_tcp1[i];
    //                            if ( strcmp(src,m->ip_src) == 0 && strcmp(dst,m->ip_dst) == 0){

                                if ( fast_compare(src,m->ip_src,16) == 0 && fast_compare(dst,m->ip_dst,16) == 0 ){
                                m->count++;
//				free(src);
//				free(dst);
                                goto next;
                                }


                        }
                        // not equal group +1
                        if ( strcmp(src,"*************")==0 || strcmp(dst,"*************")==0 ) return 0;
                         group++;
                         if ( group ==  f){
                         my_tcp1=alloc_r(my_tcp1,(group+1)*sizeof(my_tcp));
                         memset(my_tcp1+f,0,1*sizeof(my_tcp));
                         f++;
                         }
                         my_tcp *m1=&my_tcp1[group-1];
                         snprintf(m1->ip_src,16,"%s",src);
                         snprintf(m1->ip_dst,16,"%s",dst);
                         m1->count++;

                        next:
                //      printf("ip_src=%s sport=%s ip_dst=%s dport=%s\n",GET_IPADDR_STRING(ip->ip_src),tcpport_string(ndo, sport),GET_IPADDR_STRING(ip->ip_dst),tcpport_string(ndo, dport));
                        ;




}

int print_tcp_common(const char *src, const char *dst, uint16_t sport, uint16_t dport, u_char flags)
{
        // 安全检查
        if (!src || !dst || !os) {
                return 1; // 参数无效
        }

        // 根据 flags 设置操作的结构体和计数器
        unsigned int *group_counter;

        // 统计各种类型的包
        switch (flags) {
                case 2:
                        os->connection_stats.syn_count++;
                        update_port_stats(os->connection_stats.syn_ports, &os->connection_stats.syn_port_count, dport);
                        break;
                case 18: os->connection_stats.synack_count++; break;
                case 16: os->connection_stats.ack_count++; break;
                case 24: os->connection_stats.push_count++; break;
                case 17:
                        os->connection_stats.fin_count++;
                        update_port_stats(os->connection_stats.fin_ports, &os->connection_stats.fin_port_count, dport);
                        // 调用原来的print_tcp1函数记录F.包详细信息
                        // print_tcp1_old(src, dst, sport, dport);
                        break;
                case 4:
                        os->connection_stats.rst_count++;
                        update_port_stats(os->connection_stats.rst_ports, &os->connection_stats.rst_port_count, dport);
                        break;
        }

        // 跟踪真实连接状态
        track_tcp_connection(src, dst, sport, dport, flags);

        // 更新IP统计
        update_ip_stats(src, dst, flags);

        // 简化返回，不再进行详细的包分类存储
        return 0;

        /* 注释掉详细包分类系统 - 以后可能需要
        if (flags == 18) {
                // flags=18: SYN+ACK (S.) - 连接确认包 (SYN=2 + ACK=16 = 18)
                group_counter = &os->group_s;
        } else if (flags == 2) {
                // flags=2: SYN (S) - 连接初始化包 (SYN=2)
                group_counter = &os->group_s1;
        } else if (flags == 24) {
                // flags=24: ACK+PSH (P.) - 数据推送包 (ACK=16 + PSH=8 = 24)
                group_counter = &os->group_p;
        } else if (flags == 17) {
                // flags=17: FIN+ACK (F.) - 连接终止包 (FIN=1 + ACK=16 = 17)
                group_counter = &os->group_f;
        } else if (flags == 4) {
                // flags=4: RST (R) - 连接重置包 (RST=4)
                group_counter = &os->group_r;
        } else if (flags == 16) {
                // flags=16: ACK (.) - 纯确认包，三次握手第三步 (ACK=16)
                group_counter = &os->group_ack;
        } else {
                return 1; // 不支持的 flags
        }

        /*
        if (*group_counter <= max_tcpgroup_count)
        {
                if (*group_counter == 0)
                {
                        // first - 根据 flags 操作不同的结构体
                        if (flags == 18) {
                                // SYN+ACK (S.) - 使用 m_tcp_s 结构体
                                snprintf(os->m_tcp_s.ip_src, 16, "%s", src);
                                snprintf(os->m_tcp_s.ip_dst, 16, "%s", dst);
                                os->m_tcp_s.sport = sport;
                                os->m_tcp_s.dport = dport;
                                os->m_tcp_s.count++;
                        } else if (flags == 2) {
                                // SYN (S) - 使用 m_tcp_s1 结构体
                                snprintf(os->m_tcp_s1.ip_src, 16, "%s", src);
                                snprintf(os->m_tcp_s1.ip_dst, 16, "%s", dst);
                                os->m_tcp_s1.sport = sport;
                                os->m_tcp_s1.dport = dport;
                                os->m_tcp_s1.count++;
                        } else if (flags == 24) {
                                // ACK+PSH (P.) - 使用 m_tcp_p 结构体
                                snprintf(os->m_tcp_p.ip_src, 16, "%s", src);
                                snprintf(os->m_tcp_p.ip_dst, 16, "%s", dst);
                                os->m_tcp_p.sport = sport;
                                os->m_tcp_p.dport = dport;
                                os->m_tcp_p.count++;
                        } else if (flags == 17) {
                                // FIN+ACK (F.) - 使用 m_tcp_f 结构体
                                snprintf(os->m_tcp_f.ip_src, 16, "%s", src);
                                snprintf(os->m_tcp_f.ip_dst, 16, "%s", dst);
                                os->m_tcp_f.sport = sport;
                                os->m_tcp_f.dport = dport;
                                os->m_tcp_f.count++;
                        } else if (flags == 4) {
                                // RST (R) - 使用 m_tcp_r 结构体
                                snprintf(os->m_tcp_r.ip_src, 16, "%s", src);
                                snprintf(os->m_tcp_r.ip_dst, 16, "%s", dst);
                                os->m_tcp_r.sport = sport;
                                os->m_tcp_r.dport = dport;
                                os->m_tcp_r.count++;
                        } else if (flags == 16) {
                                // ACK (.) - 使用 m_tcp_ack 结构体
                                snprintf(os->m_tcp_ack.ip_src, 16, "%s", src);
                                snprintf(os->m_tcp_ack.ip_dst, 16, "%s", dst);
                                os->m_tcp_ack.sport = sport;
                                os->m_tcp_ack.dport = dport;
                                os->m_tcp_ack.count++;
                        }
                        (*group_counter)++;
                        return 0;
                }

                // 循环查找现有连接
                int i;
                for (i = 0; i < *group_counter; i++)
                {
                        os_data *m = &os[i];
                        if (flags == 18) {
                                // SYN+ACK (S.) - 查找现有 m_tcp_s 连接
                                if (strcmp(src, m->m_tcp_s.ip_src) == 0 && strcmp(dst, m->m_tcp_s.ip_dst) == 0) {
                                        m->m_tcp_s.count++;
                                        return 0;
                                }
                        } else if (flags == 2) {
                                // SYN (S) - 查找现有 m_tcp_s1 连接
                                if (strcmp(src, m->m_tcp_s1.ip_src) == 0 && strcmp(dst, m->m_tcp_s1.ip_dst) == 0) {
                                        m->m_tcp_s1.count++;
                                        return 0;
                                }
                        } else if (flags == 24) {
                                // ACK+PSH (P.) - 查找现有 m_tcp_p 连接
                                if (strcmp(src, m->m_tcp_p.ip_src) == 0 && strcmp(dst, m->m_tcp_p.ip_dst) == 0) {
                                        m->m_tcp_p.count++;
                                        return 0;
                                }
                        } else if (flags == 17) {
                                // FIN+ACK (F.) - 查找现有 m_tcp_f 连接
                                if (strcmp(src, m->m_tcp_f.ip_src) == 0 && strcmp(dst, m->m_tcp_f.ip_dst) == 0) {
                                        m->m_tcp_f.count++;
                                        return 0;
                                }
                        } else if (flags == 4) {
                                // RST (R) - 查找现有 m_tcp_r 连接
                                if (strcmp(src, m->m_tcp_r.ip_src) == 0 && strcmp(dst, m->m_tcp_r.ip_dst) == 0) {
                                        m->m_tcp_r.count++;
                                        return 0;
                                }
                        } else if (flags == 16) {
                                // ACK (.) - 查找现有 m_tcp_ack 连接
                                if (strcmp(src, m->m_tcp_ack.ip_src) == 0 && strcmp(dst, m->m_tcp_ack.ip_dst) == 0) {
                                        m->m_tcp_ack.count++;
                                        return 0;
                                }
                        }
                }

                // not equal group +1
                (*group_counter)++;
                // 如果group_counter 等于o2需要重新分配内存
                pthread_mutex_lock(&xxx);
                if (*group_counter == o2)
                {
                        os = alloc_r(os, (o2 + 1) * sizeof(os_data));
                        memset(os + o2, 0, 1 * sizeof(os_data));
                        o2++;
                }
                pthread_mutex_unlock(&xxx);

                // 添加新连接 - 根据 flags 操作不同的结构体
                // 安全检查：确保索引有效
                if (*group_counter == 0 || os == NULL) {
                        return 1; // 避免数组越界
                }
                os_data *m2 = &os[*group_counter - 1];
                if (flags == 18) {
                        // SYN+ACK (S.) - 添加到 m_tcp_s 结构体
                        snprintf(m2->m_tcp_s.ip_src, 16, "%s", src);
                        snprintf(m2->m_tcp_s.ip_dst, 16, "%s", dst);
                        m2->m_tcp_s.sport = sport;
                        m2->m_tcp_s.dport = dport;
                        m2->m_tcp_s.count++;
                } else if (flags == 2) {
                        // SYN (S) - 添加到 m_tcp_s1 结构体
                        snprintf(m2->m_tcp_s1.ip_src, 16, "%s", src);
                        snprintf(m2->m_tcp_s1.ip_dst, 16, "%s", dst);
                        m2->m_tcp_s1.sport = sport;
                        m2->m_tcp_s1.dport = dport;
                        m2->m_tcp_s1.count++;
                } else if (flags == 24) {
                        // ACK+PSH (P.) - 添加到 m_tcp_p 结构体
                        snprintf(m2->m_tcp_p.ip_src, 16, "%s", src);
                        snprintf(m2->m_tcp_p.ip_dst, 16, "%s", dst);
                        m2->m_tcp_p.sport = sport;
                        m2->m_tcp_p.dport = dport;
                        m2->m_tcp_p.count++;
                } else if (flags == 17) {
                        // FIN+ACK (F.) - 添加到 m_tcp_f 结构体
                        snprintf(m2->m_tcp_f.ip_src, 16, "%s", src);
                        snprintf(m2->m_tcp_f.ip_dst, 16, "%s", dst);
                        m2->m_tcp_f.sport = sport;
                        m2->m_tcp_f.dport = dport;
                        m2->m_tcp_f.count++;
                } else if (flags == 4) {
                        // RST (R) - 添加到 m_tcp_r 结构体
                        snprintf(m2->m_tcp_r.ip_src, 16, "%s", src);
                        snprintf(m2->m_tcp_r.ip_dst, 16, "%s", dst);
                        m2->m_tcp_r.sport = sport;
                        m2->m_tcp_r.dport = dport;
                        m2->m_tcp_r.count++;
                } else if (flags == 16) {
                        // ACK (.) - 添加到 m_tcp_ack 结构体
                        snprintf(m2->m_tcp_ack.ip_src, 16, "%s", src);
                        snprintf(m2->m_tcp_ack.ip_dst, 16, "%s", dst);
                        m2->m_tcp_ack.sport = sport;
                        m2->m_tcp_ack.dport = dport;
                        m2->m_tcp_ack.count++;
                }
                return 0;
        }
        else
        {
                return 1;
        }
        */ // 详细包分类系统注释结束
}

// 查找或创建连接记录
tcp_connection* find_or_create_connection(const char* src, const char* dst,
                                        uint16_t sport, uint16_t dport) {
        // 安全检查
        if (!src || !dst || !os) {
                return NULL;
        }

        connection_tracker* tracker = &os->conn_tracker;
        time_t now = time(NULL);

        // 首先查找现有连接
        for (int i = 0; i < tracker->connection_count; i++) {
                tcp_connection* conn = &tracker->connections[i];
                if (strcmp(src, conn->src_ip) == 0 && strcmp(dst, conn->dst_ip) == 0 &&
                    sport == conn->src_port && dport == conn->dst_port) {
                        conn->last_seen = now;
                        return conn;
                }
        }

        // 创建新连接记录
        if (tracker->connection_count < MAX_TRACKED_CONNECTIONS) {
                tcp_connection* new_conn = &tracker->connections[tracker->connection_count];
                snprintf(new_conn->src_ip, 16, "%s", src);
                snprintf(new_conn->dst_ip, 16, "%s", dst);
                new_conn->src_port = sport;
                new_conn->dst_port = dport;
                new_conn->state = TCP_CLOSED;
                new_conn->start_time = now;
                new_conn->last_seen = now;
                new_conn->packet_count = 0;
                tracker->connection_count++;
                return new_conn;
        } else {
                // 连接表满了，使用循环覆盖
                tcp_connection* reuse_conn = &tracker->connections[tracker->next_slot];
                snprintf(reuse_conn->src_ip, 16, "%s", src);
                snprintf(reuse_conn->dst_ip, 16, "%s", dst);
                reuse_conn->src_port = sport;
                reuse_conn->dst_port = dport;
                reuse_conn->state = TCP_CLOSED;
                reuse_conn->start_time = now;
                reuse_conn->last_seen = now;
                reuse_conn->packet_count = 0;
                tracker->next_slot = (tracker->next_slot + 1) % MAX_TRACKED_CONNECTIONS;
                return reuse_conn;
        }
}

// 跟踪TCP连接状态
void track_tcp_connection(const char* src, const char* dst,
                         uint16_t sport, uint16_t dport, u_char flags) {
        // 安全检查
        if (!src || !dst || !os) {
                return;
        }

        tcp_connection* conn = find_or_create_connection(src, dst, sport, dport);
        tcp_connection* reverse_conn = NULL;

        if (!conn) {
                return; // 连接创建失败
        }

        conn->packet_count++;

        switch (flags) {
                case 2:  // SYN包
                        if (conn->state == TCP_CLOSED) {
                                conn->state = TCP_SYN_SENT;
                                os->connection_stats.connection_attempts++;
                        }
                        break;

                case 18: // SYN+ACK包
                        // 查找对应的反向连接 (dst->src)
                        reverse_conn = find_or_create_connection(dst, src, dport, sport);
                        if (reverse_conn->state == TCP_SYN_SENT) {
                                reverse_conn->state = TCP_SYN_RECEIVED;
                        }
                        break;

                case 16: // ACK包
                        if (conn->state == TCP_SYN_RECEIVED) {
                                conn->state = TCP_ESTABLISHED;
                                os->connection_stats.connections_established++;
                                os->connection_stats.handshake_complete++;
                                os->connection_stats.connections_active++;
                        }
                        break;

                case 17: // FIN+ACK包
                        if (conn->state == TCP_ESTABLISHED) {
                                conn->state = TCP_CLOSING;
                        }
                        break;

                case 4:  // RST包
                        if (conn->state != TCP_CLOSED) {
                                conn->state = TCP_CLOSED;
                                os->connection_stats.connections_failed++;
                                if (os->connection_stats.connections_active > 0) {
                                        os->connection_stats.connections_active--;
                                }
                        }
                        break;
        }
}

// 获取端口对应的服务名
const char* get_port_service_name(uint16_t port) {
        switch (port) {
                case 22: return "SSH";
                case 80: return "HTTP";
                case 443: return "HTTPS";
                case 3306: return "MySQL";
                case 5432: return "PostgreSQL";
                case 6379: return "Redis";
                case 27017: return "MongoDB";
                case 21: return "FTP";
                case 25: return "SMTP";
                case 53: return "DNS";
                case 110: return "POP3";
                case 143: return "IMAP";
                case 993: return "IMAPS";
                case 995: return "POP3S";
                case 8080: return "HTTP-Alt";
                case 8443: return "HTTPS-Alt";
                default: return "Unknown";
        }
}

// 更新端口统计信息
void update_port_stats(port_packet_stats* port_stats, int* port_count, uint16_t port) {
        if (!port_stats || !port_count) return;

        // 查找现有端口
        for (int i = 0; i < *port_count; i++) {
                if (port_stats[i].port == port) {
                        port_stats[i].count++;
                        return;
                }
        }

        // 添加新端口（如果还有空间）
        if (*port_count < 5) {
                port_stats[*port_count].port = port;
                port_stats[*port_count].count = 1;
                strncpy(port_stats[*port_count].service_name, get_port_service_name(port), 15);
                port_stats[*port_count].service_name[15] = '\0';
                (*port_count)++;
        } else {
                // 找到计数最小的端口并替换
                int min_idx = 0;
                for (int i = 1; i < 5; i++) {
                        if (port_stats[i].count < port_stats[min_idx].count) {
                                min_idx = i;
                        }
                }
                if (port_stats[min_idx].count == 1) {  // 只有当最小计数为1时才替换
                        port_stats[min_idx].port = port;
                        port_stats[min_idx].count = 1;
                        strncpy(port_stats[min_idx].service_name, get_port_service_name(port), 15);
                        port_stats[min_idx].service_name[15] = '\0';
                }
        }
}

// 查找或创建IP统计记录
ip_connection_stats* find_or_create_ip_stats(const char* ip_address) {
        // 安全检查
        if (!ip_address || !os) {
                return NULL;
        }

        ip_stats_tracker* tracker = &os->ip_tracker;
        time_t now = time(NULL);

        // 查找现有IP统计
        for (int i = 0; i < tracker->ip_count; i++) {
                if (strcmp(ip_address, tracker->ip_stats[i].ip_address) == 0) {
                        tracker->ip_stats[i].last_seen = now;
                        return &tracker->ip_stats[i];
                }
        }

        // 创建新的IP统计记录
        if (tracker->ip_count < MAX_IP_STATS) {
                ip_connection_stats* new_stats = &tracker->ip_stats[tracker->ip_count];
                snprintf(new_stats->ip_address, 16, "%s", ip_address);
                new_stats->total_attempts = 0;
                new_stats->successful_connections = 0;
                new_stats->failed_connections = 0;
                new_stats->active_connections = 0;
                new_stats->as_client = 0;
                new_stats->as_server = 0;
                new_stats->last_seen = now;
                tracker->ip_count++;
                return new_stats;
        }

        return NULL; // IP统计表满了
}

// 更新IP统计信息
void update_ip_stats(const char* src, const char* dst, u_char flags) {
        ip_connection_stats* src_stats = find_or_create_ip_stats(src);
        ip_connection_stats* dst_stats = find_or_create_ip_stats(dst);

        if (!src_stats || !dst_stats) return;

        switch (flags) {
                case 2:  // SYN包 - 源IP作为客户端发起连接
                        src_stats->total_attempts++;
                        src_stats->as_client++;
                        dst_stats->as_server++;
                        break;

                case 18: // SYN+ACK包 - 目标IP作为服务器响应
                        // 统计已在SYN包中处理
                        break;

                case 16: // ACK包 - 连接建立成功
                        // 需要检查是否是三次握手的最后一步
                        // 这里简化处理，假设ACK包表示连接成功
                        src_stats->successful_connections++;
                        src_stats->active_connections++;
                        break;

                case 4:  // RST包 - 连接失败
                        src_stats->failed_connections++;
                        if (src_stats->active_connections > 0) {
                                src_stats->active_connections--;
                        }
                        dst_stats->failed_connections++;
                        if (dst_stats->active_connections > 0) {
                                dst_stats->active_connections--;
                        }
                        break;

                case 17: // FIN+ACK包 - 连接关闭
                        if (src_stats->active_connections > 0) {
                                src_stats->active_connections--;
                        }
                        break;
        }
}

 

void tcp_print(netdissect_options *ndo,
               const u_char *bp, u_int length,
               const u_char *bp2, int fragmented)
{
        const struct tcphdr *tp;
        const struct ip *ip;
        u_char flags;
        u_int hlen;
        char ch;
        uint16_t sport, dport, win, urp;
        uint32_t seq, ack, thseq, thack;
        u_int utoval;
        uint16_t magic;
        int rev;
        const struct ip6_hdr *ip6;
        u_int header_len; /* Header length in bytes */

        ndo->ndo_protocol = "tcp";
        tp = (const struct tcphdr *)bp;
        ip = (const struct ip *)bp2;

        // 测试每S经过传输层的有多？
        test++;

        if (IP_V(ip) == 6)
                ip6 = (const struct ip6_hdr *)bp2;
        else
                ip6 = NULL;
        ch = '\0';

        sport = GET_BE_U_2(tp->th_sport);
        dport = GET_BE_U_2(tp->th_dport);
        flags = GET_U_1(tp->th_flags);
        if (ip6)
        {
                if (GET_U_1(ip6->ip6_nxt) == IPPROTO_TCP)
                {

                        ;
                }
                else
                {
                        ND_PRINT("%s > %s: ",
                                 tcpport_string(ndo, sport), tcpport_string(ndo, dport));
                }
        }
        else
        {
                if (GET_U_1(ip->ip_p) == IPPROTO_TCP)
                {

                        /* if ( ndo->tcp==18 ){


                                if ( ip_count < max_tcpgroup_count-2){
                                 // printf("DEBUG3 ip_str[%d]=%p\n", ip_count, ip_str[ip_count]);
                                // 获取源IP和目标IP字符串
                                const char *src_ip = GET_IPADDR_STRING1(ip->ip_src);
                                const char *dst_ip = GET_IPADDR_STRING1(ip->ip_dst);

                                // 只有当两个IP都不是空字符串时才执行print_tcp1
                                if (src_ip[0] != '\0' && dst_ip[0] != '\0') {
                                        print_tcp1(src_ip, dst_ip, sport, dport);
                                }
                                printf("DEBUG4 ip_str[%d]=%p\n", ip_count, ip_str[ip_count]);
                                fflush(stdout);
                        } */
                        // 根据实际的 TCP flags 值来判断包类型
                        // flags=18:SYN+ACK(S.) flags=2:SYN(S) flags=24:ACK+PSH(P.) flags=17:FIN+ACK(F.) flags=4:RST(R)
                        // 注意：flags=16:ACK(.) 已在BPF过滤器中排除，不会到达这里
                        if (flags == 18 || flags == 2 || flags == 24 || flags == 17 || flags == 4)
                        {
                                if (ip_count < max_tcpgroup_count-2) {
                                        const char *src_ip = GET_IPADDR_STRING1(ip->ip_src);
                                        const char *dst_ip = GET_IPADDR_STRING1(ip->ip_dst);

                                        // 只有当两个IP都不是空字符串时才执行通用函数
                                        if (src_ip[0] != '\0' && dst_ip[0] != '\0')
                                        {
                                                print_tcp_common(src_ip, dst_ip, sport, dport, flags);
                                        }
                                }
                        }
                }
                else
                {
                        ND_PRINT("%s > %s: ",
                                 tcpport_string(ndo, sport), tcpport_string(ndo, dport));
                }
        }
}

/*
static void print_tcp_rst_data(netdissect_options *ndo,const u_char *sp, u_int length)
{
        u_char c;

        ND_PRINT(ND_TTEST_LEN(sp, length) ? " [RST" : " [!RST");
        if (length > MAX_RST_DATA_LEN) {
                length = MAX_RST_DATA_LEN;
        }
        ND_PRINT(" ");
        while (length && sp < ndo->ndo_snapend) {
                c = GET_U_1(sp);
                sp++;
                fn_print_char(ndo, c);
                length--;
        }
        ND_PRINT("]");
}

static void
print_tcp_fastopen_option(netdissect_options *ndo, const u_char *cp,
                          u_int datalen, int exp)
{
        u_int i;

        if (exp)
                ND_PRINT("tfo");

        if (datalen == 0) {
                ND_PRINT(" cookiereq");
        } else {
                if (datalen % 2 != 0 || datalen < 4 || datalen > 16) {
                        nd_print_invalid(ndo);
                } else {
                        ND_PRINT(" cookie ");
                        for (i = 0; i < datalen; ++i)
                                ND_PRINT("%02x", GET_U_1(cp + i));
                }
        }
}

*/

#ifdef HAVE_LIBCRYPTO
DIAG_OFF_DEPRECATION
static int
tcp_verify_signature(netdissect_options *ndo,
                     const struct ip *ip, const struct tcphdr *tp,
                     const u_char *data, u_int length, const u_char *rcvsig)
{
        struct tcphdr tp1;
        u_char sig[TCP_SIGLEN];
        char zero_proto = 0;
        MD5_CTX ctx;
        uint16_t savecsum, tlen;
        const struct ip6_hdr *ip6;
        uint32_t len32;
        uint8_t nxt;

        if (data + length > ndo->ndo_snapend)
        {
                ND_PRINT("snaplen too short, ");
                return (CANT_CHECK_SIGNATURE);
        }

        tp1 = *tp;

        if (ndo->ndo_sigsecret == NULL)
        {
                ND_PRINT("shared secret not supplied with -M, ");
                return (CANT_CHECK_SIGNATURE);
        }

        MD5_Init(&ctx);
        /*
         * Step 1: Update MD5 hash with IP pseudo-header.
         */
        if (IP_V(ip) == 4)
        {
                MD5_Update(&ctx, (const char *)&ip->ip_src, sizeof(ip->ip_src));
                MD5_Update(&ctx, (const char *)&ip->ip_dst, sizeof(ip->ip_dst));
                MD5_Update(&ctx, (const char *)&zero_proto, sizeof(zero_proto));
                MD5_Update(&ctx, (const char *)&ip->ip_p, sizeof(ip->ip_p));
                tlen = GET_BE_U_2(ip->ip_len) - IP_HL(ip) * 4;
                tlen = htons(tlen);
                MD5_Update(&ctx, (const char *)&tlen, sizeof(tlen));
        }
        else if (IP_V(ip) == 6)
        {
                ip6 = (const struct ip6_hdr *)ip;
                MD5_Update(&ctx, (const char *)&ip6->ip6_src, sizeof(ip6->ip6_src));
                MD5_Update(&ctx, (const char *)&ip6->ip6_dst, sizeof(ip6->ip6_dst));
                len32 = htonl(GET_BE_U_2(ip6->ip6_plen));
                MD5_Update(&ctx, (const char *)&len32, sizeof(len32));
                nxt = 0;
                MD5_Update(&ctx, (const char *)&nxt, sizeof(nxt));
                MD5_Update(&ctx, (const char *)&nxt, sizeof(nxt));
                MD5_Update(&ctx, (const char *)&nxt, sizeof(nxt));
                nxt = IPPROTO_TCP;
                MD5_Update(&ctx, (const char *)&nxt, sizeof(nxt));
        }
        else
        {
                ND_PRINT("IP version not 4 or 6, ");
                return (CANT_CHECK_SIGNATURE);
        }

        /*
         * Step 2: Update MD5 hash with TCP header, excluding options.
         * The TCP checksum must be set to zero.
         */
        memcpy(&savecsum, tp1.th_sum, sizeof(savecsum));
        memset(tp1.th_sum, 0, sizeof(tp1.th_sum));
        MD5_Update(&ctx, (const char *)&tp1, sizeof(struct tcphdr));
        memcpy(tp1.th_sum, &savecsum, sizeof(tp1.th_sum));
        /*
         * Step 3: Update MD5 hash with TCP segment data, if present.
         */
        if (length > 0)
                MD5_Update(&ctx, data, length);
        /*
         * Step 4: Update MD5 hash with shared secret.
         */
        MD5_Update(&ctx, ndo->ndo_sigsecret, strlen(ndo->ndo_sigsecret));
        MD5_Final(sig, &ctx);

        if (memcmp(rcvsig, sig, TCP_SIGLEN) == 0)
                return (SIGNATURE_VALID);
        else
                return (SIGNATURE_INVALID);
}
DIAG_ON_DEPRECATION
#endif /* HAVE_LIBCRYPTO */
