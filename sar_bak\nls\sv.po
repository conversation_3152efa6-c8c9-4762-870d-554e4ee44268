# Swedish translation of the sysstat package.
# This file is distributed under the same license as the sysstat package.
# Copyright © 1999-2016, 2017 Free Software Foundation, Inc.
#
# <PERSON> <<EMAIL>>, 2006, 2008.
# <PERSON> <<EMAIL>>, 2015, 2016.
# <PERSON><PERSON><PERSON> Uddeborg <<EMAIL>>, 2016, 2017.
msgid ""
msgstr ""
"Project-Id-Version: sysstat 11.6.0\n"
"Report-Msgid-Bugs-To: sysstat <at> orange.fr\n"
"POT-Creation-Date: 2017-08-14 08:32+0200\n"
"PO-Revision-Date: 2017-08-15 16:53+0200\n"
"Last-Translator: Göran Uddeborg <<EMAIL>>\n"
"Language-Team: Swedish <<EMAIL>>\n"
"Language: sv\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"X-Generator: Poedit *******\n"

#: iostat.c:86 cifsiostat.c:70 mpstat.c:126 sar.c:96 tapestat.c:95
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ]\n"
msgstr "Användning: %s [ flaggor ] [ <intervall> [ <antal> ] ]\n"

#: iostat.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ] [ --debuginfo ]\n"
msgstr ""
"Flaggor är:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | … } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <gruppnamn> ] [ -p [ <enhet> [,…] | ALL ] ]\n"
"[ <enhet> […] | ALL ] [ --debuginfo ]\n"

#: iostat.c:95
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ]\n"
msgstr ""
"Flaggor är:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -s ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | … } ] [ --human ] [ -o JSON ]\n"
"[ [ -H ] -g <gruppnamn> ] [ -p [ <enhet> [,…] | ALL ] ]\n"
"[ <enhet> […] | ALL ]\n"

#: iostat.c:326
#, c-format
msgid "Cannot find disk data\n"
msgstr "Kan inte hitta diskdata\n"

#: iostat.c:1812 sa_common.c:1590
#, c-format
msgid "Invalid type of persistent device name\n"
msgstr "Ogiltig typ av persistent enhetsnamn\n"

#: sadc.c:89
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
msgstr "Användning: %s [ flaggor ] [ <intervall> [ <antal> ] ] [ <utfil> ]\n"

#: sadc.c:92
#, c-format
msgid ""
"Options are:\n"
"[ -C <comment> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"
msgstr ""
"Flaggor är:\n"
"[ -C <kommentar> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"

#: sadc.c:267
#, c-format
msgid "Cannot write data to system activity file: %s\n"
msgstr "Kan inte skriva data till systemaktivitetsfil: %s\n"

#: sadc.c:563
#, c-format
msgid "Cannot write system activity file header: %s\n"
msgstr "Kan inte skriva systemaktivitetsfilhuvud: %s\n"

#: sadc.c:763 sadc.c:772 sadc.c:839 ioconf.c:510 rd_stats.c:69
#: sa_common.c:1204 count.c:118
#, c-format
msgid "Cannot open %s: %s\n"
msgstr "Kan inte öppna %s: %s\n"

#: sadc.c:1019
#, c-format
msgid "Cannot append data to that file (%s)\n"
msgstr "Kan inte lägga till data till den filen (%s)\n"

#: common.c:77
#, c-format
msgid "sysstat version %s\n"
msgstr "sysstat version %s\n"

#: cifsiostat.c:74
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"
msgstr ""
"Flaggor är:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"

#: cifsiostat.c:77
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
msgstr ""
"Flaggor är:\n"
"[ --human ] [ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#: mpstat.c:129
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -n ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -N { <node_list> | ALL } ] [ -o JSON ] [ -P { <cpu_list> | ON | ALL } ]\n"
msgstr ""
"Flaggor är:\n"
"[ -A ] [ -n ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -N { <nodlista> | ALL } ] [ -o JSON ] [ -P { <processorlista> | ON | ALL } ]\n"

# sar.c:
#: mpstat.c:1672 sar.c:358 pidstat.c:2406
msgid "Average:"
msgstr "Genomsnitt:"

#: sadf.c:87
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <datafile> | -[0-9]+ ]\n"
msgstr "Användning: %s [ flaggor ] [ <intervall> [ <antal> ] ] [ <datafil> | -[0-9]+ ]\n"

#: sadf.c:90
#, c-format
msgid ""
"Options are:\n"
"[ -C ] [ -c | -d | -g | -j | -p | -r | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -O <opts> [,...] ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
"[ -- <sar_options> ]\n"
msgstr ""
"Flaggor är:\n"
"[ -C ] [ -c | -d | -g | -j | -p | -r | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -O <flaggor> [,...] ] [ -P { <processor> [,…] | ALL } ]\n"
"[ -s [ <tt:mm[:ss]> ] ] [ -e [ <tt:mm[:ss]> ] ]\n"
"[ -- <sar-flaggor> ]\n"

#: pr_stats.c:2538 pr_stats.c:2549 pr_stats.c:2656 pr_stats.c:2667
msgid "Summary:"
msgstr "Sammanfattning:"

#: pr_stats.c:2591
msgid "Other devices not listed here"
msgstr "Andra enheter som inte listas här"

#: sar.c:111
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ MOUNT ] ] [ -H ] [ -h ]\n"
"[ -p ] [ -q ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --human ] [ --sadc ]\n"
"[ -I { <int_list> | SUM | ALL } ] [ -P { <cpu_list> | ALL } ]\n"
"[ -m { <keyword> [,...] | ALL } ] [ -n { <keyword> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filename> ] | -o [ <filename> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
msgstr ""
"Flaggor är:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ MOUNT ] ] [ -H ] [ -h ]\n"
"[ -p ] [ -q ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --human ] [ --sadc ]\n"
"[ -I { <heltalslista> | SUM | ALL } ] [ -P { <cpulista> | ALL } ]\n"
"[ -m { <nyckelord> [,…] | ALL } ] [ -n { <nyckelord> [,…] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | … } ]\n"
"[ -f [ <filnamn> ] | -o [ <filnamn> ] | -[0-9]+ ]\n"
"[ -i <intervall> ] [ -s [ <tt:mm[:ss]> ] ] [ -e [ <tt:mm[:ss]> ] ]\n"

#: sar.c:134
#, c-format
msgid "Main options and reports:\n"
msgstr "Huvudflaggor och rapporter:\n"

#: sar.c:135
#, c-format
msgid "\t-B\tPaging statistics\n"
msgstr "\t-B\tStatistik över sidväxling\n"

#: sar.c:136
#, c-format
msgid "\t-b\tI/O and transfer rate statistics\n"
msgstr "\t-b\tStatistik över I/O och överföringshastighet\n"

#: sar.c:137
#, c-format
msgid "\t-d\tBlock devices statistics\n"
msgstr "\t-d\tStatistik över blockenheter\n"

#: sar.c:138
#, c-format
msgid "\t-F [ MOUNT ]\n"
msgstr "\t-F [ MOUNT ]\n"

#: sar.c:139
#, c-format
msgid "\t\tFilesystems statistics\n"
msgstr "\t\tStatistik över filsystem\n"

#: sar.c:140
#, c-format
msgid "\t-H\tHugepages utilization statistics\n"
msgstr "\t-H\tStatistik över användning av stora sidor\n"

#: sar.c:141
#, c-format
msgid ""
"\t-I { <int_list> | SUM | ALL }\n"
"\t\tInterrupts statistics\n"
msgstr ""
"\t-I { <heltalslista> | SUM | ALL }\n"
"\t\tStatistik över avbrott\n"

#: sar.c:143
#, c-format
msgid ""
"\t-m { <keyword> [,...] | ALL }\n"
"\t\tPower management statistics\n"
"\t\tKeywords are:\n"
"\t\tCPU\tCPU instantaneous clock frequency\n"
"\t\tFAN\tFans speed\n"
"\t\tFREQ\tCPU average clock frequency\n"
"\t\tIN\tVoltage inputs\n"
"\t\tTEMP\tDevices temperature\n"
"\t\tUSB\tUSB devices plugged into the system\n"
msgstr ""
"\t-m { <nyckelord> [,…] | ALL }\n"
"\t\tStatistik över strömhantering\n"
"\t\tNyckelord:\n"
"\t\tCPU\tProcessorns momentana klockfrekvens\n"
"\t\tFAN\tFläkthastigheter\n"
"\t\tFREQ\tProcessorns genomsnittliga klockfrekvens\n"
"\t\tIN\tSpänningsingångar\n"
"\t\tTEMP\tEnhetstemperaturer\n"
"\t\tUSB\tUSB-enheter som finns kopplade till systemet\n"

#: sar.c:152
#, c-format
msgid ""
"\t-n { <keyword> [,...] | ALL }\n"
"\t\tNetwork statistics\n"
"\t\tKeywords are:\n"
"\t\tDEV\tNetwork interfaces\n"
"\t\tEDEV\tNetwork interfaces (errors)\n"
"\t\tNFS\tNFS client\n"
"\t\tNFSD\tNFS server\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP traffic\t(v4)\n"
"\t\tEIP\tIP traffic\t(v4) (errors)\n"
"\t\tICMP\tICMP traffic\t(v4)\n"
"\t\tEICMP\tICMP traffic\t(v4) (errors)\n"
"\t\tTCP\tTCP traffic\t(v4)\n"
"\t\tETCP\tTCP traffic\t(v4) (errors)\n"
"\t\tUDP\tUDP traffic\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP traffic\t(v6)\n"
"\t\tEIP6\tIP traffic\t(v6) (errors)\n"
"\t\tICMP6\tICMP traffic\t(v6)\n"
"\t\tEICMP6\tICMP traffic\t(v6) (errors)\n"
"\t\tUDP6\tUDP traffic\t(v6)\n"
"\t\tFC\tFibre channel HBAs\n"
"\t\tSOFT\tSoftware-based network processing\n"
msgstr ""
"\t-n { <nyckelord> [,…] | ALL }\n"
"\t\tStatistik över nätverk\n"
"\t\tNyckelord:\n"
"\t\tDEV\tNätverksgränssnitt\n"
"\t\tEDEV\tNätverksgränssnitt (fel)\n"
"\t\tNFS\tNFS-klient\n"
"\t\tNFSD\tNFS-server\n"
"\t\tSOCK\tUttag\t(v4)\n"
"\t\tIP\tIP-trafik\t(v4)\n"
"\t\tEIP\tIP-trafik\t(v4) (fel)\n"
"\t\tICMP\tICMP-trafik\t(v4)\n"
"\t\tEICMP\tICMP-trafik\t(v4) (fel)\n"
"\t\tTCP\tTCP-trafik\t(v4)\n"
"\t\tETCP\tTCP-trafik\t(v4) (fel)\n"
"\t\tUDP\tUDP-trafik\t(v4)\n"
"\t\tSOCK6\tUttag\t(v6)\n"
"\t\tIP6\tIP-trafik\t(v6)\n"
"\t\tEIP6\tIP-trafik\t(v6) (fel)\n"
"\t\tICMP6\tICMP-trafik\t(v6)\n"
"\t\tEICMP6\tICMP-trafik\t(v6) (fel)\n"
"\t\tUDP6\tUDP-trafik\t(v6)\n"
"\t\tFC\tFiberkanal HBA:er\n"
"\t\tSOFT\tProgramvarubaserad nätverksbearbetning\n"

#: sar.c:175
#, c-format
msgid "\t-q\tQueue length and load average statistics\n"
msgstr "\t-q\tKölängd och statistik över genomsnittlig last\n"

#: sar.c:176
#, c-format
msgid ""
"\t-r [ ALL ]\n"
"\t\tMemory utilization statistics\n"
msgstr ""
"\t-r [ ALL ]\n"
"\t\tStatistik över minnesanvändning\n"

#: sar.c:178
#, c-format
msgid "\t-S\tSwap space utilization statistics\n"
msgstr "\t-S\tStatistik över användning av växlingsutrymme\n"

#: sar.c:179
#, c-format
msgid ""
"\t-u [ ALL ]\n"
"\t\tCPU utilization statistics\n"
msgstr ""
"\t-u [ ALL ]\n"
"\t\tStatistik över processoranvändning\n"

#: sar.c:181
#, c-format
msgid "\t-v\tKernel tables statistics\n"
msgstr "\t-v\tStatistik över kärntabeller\n"

#: sar.c:182
#, c-format
msgid "\t-W\tSwapping statistics\n"
msgstr "\t-W\tStatistik över växling\n"

#: sar.c:183
#, c-format
msgid "\t-w\tTask creation and system switching statistics\n"
msgstr "\t-w\tStatistik över processkapande och systemväxling\n"

#: sar.c:184
#, c-format
msgid "\t-y\tTTY devices statistics\n"
msgstr "\t-y\tStatistik över TTY-enheter\n"

#: sar.c:198
#, c-format
msgid "Data collector will be sought in PATH\n"
msgstr "Datainsamlare kommer att eftersökas i SÖKVÄG\n"

#: sar.c:201
#, c-format
msgid "Data collector found: %s\n"
msgstr "Datainsamlare hittade: %s\n"

#: sar.c:260
#, c-format
msgid "End of data collecting unexpected\n"
msgstr "Oväntat slut på datainsamling\n"

#: sar.c:813
#, c-format
msgid "Using a wrong data collector from a different sysstat version\n"
msgstr "Använder fel datainsamlare från en annan version av sysstat\n"

#: sar.c:865
#, c-format
msgid "Inconsistent input data\n"
msgstr "Inkonsekvent indata\n"

#: sar.c:1044 pidstat.c:239
#, c-format
msgid "Requested activities not available\n"
msgstr "Begärda aktiviteter är inte tillgängliga\n"

#: sar.c:1347
#, c-format
msgid "-f and -o options are mutually exclusive\n"
msgstr "flaggorna -f och -o är ömsesidigt uteslutande\n"

#: sar.c:1353
#, c-format
msgid "Not reading from a system activity file (use -f option)\n"
msgstr "Läser inte från en systemaktivitetsfil (använd flaggan -f)\n"

#: sar.c:1489
#, c-format
msgid "Cannot find the data collector (%s)\n"
msgstr "Kan inte hitta datainsamlaren (%s)\n"

#: sa_conv.c:69
#, c-format
msgid "Cannot convert the format of this file\n"
msgstr "Kan inte konvertera denna fils format\n"

#: sa_conv.c:562
#, c-format
msgid ""
"\n"
"CPU activity not found in file. Aborting...\n"
msgstr ""
"\n"
"Processoraktivitet hittades inte i fil. Avbryter…\n"

#: sa_conv.c:578
#, c-format
msgid ""
"\n"
"Invalid data found. Aborting...\n"
msgstr ""
"\n"
"Ogiltig data hittad. Avbryter…\n"

#: sa_conv.c:897
#, c-format
msgid "Statistics: "
msgstr "Statistik:"

#: sa_conv.c:1016
#, c-format
msgid ""
"\n"
"File successfully converted to sysstat format version %s\n"
msgstr ""
"\n"
"Fil konverterades framgångsrikt till sysstat-format version %s\n"

#: pidstat.c:87
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ -e <program> <args> ]\n"
msgstr "Användning: %s [ flaggor ] [ <intervall> [ <antal> ] ] [ -e <program> <argument> ]\n"

#: pidstat.c:90
#, c-format
msgid ""
"Options are:\n"
"[ -d ] [ -H ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ] [ -U [ <username> ] ]\n"
"[ -u ] [ -V ] [ -v ] [ -w ] [ -C <command> ] [ -G <process_name> ] [ --human ]\n"
"[ -p { <pid> [,...] | SELF | ALL } ] [ -T { TASK | CHILD | ALL } ]\n"
msgstr ""
"Flaggor är:\n"
"[ -d ] [ -H ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ] [ -U [ <användarnamn> ] ]\n"
"[ -u ] [ -V ] [ -v ] [ -w ] [ -C <kommando> ] [ -G <processnamn> ] [ --human ]\n"
"[ -p { <pid> [,…] | SELF | ALL } ] [ -T { TASK | CHILD | ALL } ]\n"

#: sa_common.c:1000
#, c-format
msgid "Error while reading system activity file: %s\n"
msgstr "Fel vid inläsning av systemaktivitetsfil: %s\n"

#: sa_common.c:1010
#, c-format
msgid "End of system activity file unexpected\n"
msgstr "Oväntat slut på systemaktivitetsfil\n"

#: sa_common.c:1029
#, c-format
msgid "File created by sar/sadc from sysstat version %d.%d.%d"
msgstr "Fil skapad av sar/sadc från sysstat version %d.%d.%d"

#: sa_common.c:1062
#, c-format
msgid "Invalid system activity file: %s\n"
msgstr "Ogiltig systemaktivitetsfil: %s\n"

#: sa_common.c:1074
#, c-format
msgid "Endian format mismatch\n"
msgstr ""
"Endian-format är inkompatibla\n"
"\n"

#: sa_common.c:1078
#, c-format
msgid "Current sysstat version cannot read the format of this file (%#x)\n"
msgstr "Aktuell sysstat-version kan inte läsa formatet på denna fil (%#x)\n"

#: sa_common.c:1207
#, c-format
msgid "Please check if data collecting is enabled\n"
msgstr "Kontrollera om datainsamling är aktiverad\n"

#: sa_common.c:1400
#, c-format
msgid "Requested activities not available in file %s\n"
msgstr "Begärda aktiviteter inte tillgängliga i filen %s\n"

#: tapestat.c:97
#, c-format
msgid ""
"Options are:\n"
"[ --human ] [ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"
msgstr ""
"Flaggor är:\n"
"[ --human ] [ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"

#: tapestat.c:263
#, c-format
msgid "No tape drives with statistics found\n"
msgstr "Inga bandenheter med statistik hittades\n"

#: count.c:169
#, c-format
msgid "Cannot handle so many processors!\n"
msgstr "Kan inte hantera så många processorer!\n"

#: sadf_misc.c:834
#, c-format
msgid "System activity data file: %s (%#x)\n"
msgstr "Systemaktivitetsfil: %s (%#x)\n"

#: sadf_misc.c:843
#, c-format
msgid "Genuine sa datafile: %s (%x)\n"
msgstr "Genuin sa-datafil: %s (%x)\n"

#: sadf_misc.c:844
msgid "no"
msgstr "nej"

#: sadf_misc.c:844
msgid "yes"
msgstr "ja"

#: sadf_misc.c:847
#, c-format
msgid "Host: "
msgstr "Värd: "

# Osäker
#: sadf_misc.c:854
#, c-format
msgid "Number of CPU for last samples in file: %u\n"
msgstr "Antal processorer för det senaste samplet i fil: %u\n"

#: sadf_misc.c:860
#, c-format
msgid "File date: %s\n"
msgstr "Fildatum: %s\n"

#: sadf_misc.c:863
#, c-format
msgid "File time: "
msgstr "Filtid: "

#: sadf_misc.c:868
#, c-format
msgid "Size of a long int: %d\n"
msgstr "Storlek på en lång int: %d\n"

#: sadf_misc.c:874
#, c-format
msgid "List of activities:\n"
msgstr "Lista över aktiviteter:\n"

#: sadf_misc.c:887
#, c-format
msgid "\t[Unknown activity format]"
msgstr "\t[Okänt aktivitetsformat]"
