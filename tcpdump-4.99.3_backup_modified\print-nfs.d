print-nfs.o: print-nfs.c config.h netdissect-stdinc.h ftmacros.h \
 compiler-tests.h varattrs.h funcattrs.h netdissect.h status-exit-codes.h \
 diag-control.h ../libpcap/pcap.h ../libpcap/pcap/pcap.h \
 ../libpcap/pcap/funcattrs.h ../libpcap/pcap/compiler-tests.h \
 ../libpcap/pcap/pcap-inttypes.h ../libpcap/pcap/socket.h \
 ../libpcap/pcap/bpf.h ../libpcap/pcap/dlt.h ip.h ip6.h addrtoname.h \
 extract.h nfs.h nfsfh.h rpc_auth.h rpc_msg.h
config.h:
netdissect-stdinc.h:
ftmacros.h:
compiler-tests.h:
varattrs.h:
funcattrs.h:
netdissect.h:
status-exit-codes.h:
diag-control.h:
../libpcap/pcap.h:
../libpcap/pcap/pcap.h:
../libpcap/pcap/funcattrs.h:
../libpcap/pcap/compiler-tests.h:
../libpcap/pcap/pcap-inttypes.h:
../libpcap/pcap/socket.h:
../libpcap/pcap/bpf.h:
../libpcap/pcap/dlt.h:
ip.h:
ip6.h:
addrtoname.h:
extract.h:
nfs.h:
nfsfh.h:
rpc_auth.h:
rpc_msg.h:
