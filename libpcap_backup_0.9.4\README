@(#) $Header: /tcpdump/master/libpcap/README,v 1.30 2004/10/12 02:02:28 guy Exp $ (LBL)

LIBPCAP 0.9
Now maintained by "The Tcpdump Group"
See 		www.tcpdump.org

Please send inquiries/comments/reports to 	<EMAIL>

Anonymous CVS is available via:
	cvs -d :pserver:<EMAIL>:/tcpdump/master login
	(password "anoncvs")
	cvs -d :pserver:<EMAIL>:/tcpdump/master checkout libpcap

Version 0.9 of LIBPCAP can be retrieved with the CVS tag "libpcap_0_9rel1":
	cvs -d :pserver:<EMAIL>:/tcpdump/master checkout -r libpcap_0_9rel1 libpcap

Please send patches against the master <NAME_EMAIL>.

formerly from 	Lawrence Berkeley National Laboratory
		Network Research Group <<EMAIL>>
		ftp://ftp.ee.lbl.gov/libpcap.tar.Z (0.4)

This directory contains source code for libpcap, a system-independent
interface for user-level packet capture.  libpcap provides a portable
framework for low-level network monitoring.  Applications include
network statistics collection, security monitoring, network debugging,
etc.  Since almost every system vendor provides a different interface
for packet capture, and since we've developed several tools that
require this functionality, we've created this system-independent API
to ease in porting and to alleviate the need for several
system-dependent packet capture modules in each application.

Note well: this interface is new and is likely to change.

For some platforms there are README.{system} files that discuss issues
with the OS's interface for packet capture on those platforms, such as
how to enable support for that interface in the OS, if it's not built in
by default.

The libpcap interface supports a filtering mechanism based on the
architecture in the BSD packet filter.  BPF is described in the 1993
Winter Usenix paper ``The BSD Packet Filter: A New Architecture for
User-level Packet Capture''.  A compressed PostScript version can be
found at

	ftp://ftp.ee.lbl.gov/papers/bpf-usenix93.ps.Z

or

	http://www.tcpdump.org/papers/bpf-usenix93.ps.Z

and a gzipped version can be found at

	http://www.tcpdump.org/papers/bpf-usenix93.ps.gz

A PDF version can be found at

	http://www.tcpdump.org/papers/bpf-usenix93.pdf

Although most packet capture interfaces support in-kernel filtering,
libpcap utilizes in-kernel filtering only for the BPF interface.
On systems that don't have BPF, all packets are read into user-space
and the BPF filters are evaluated in the libpcap library, incurring
added overhead (especially, for selective filters).  Ideally, libpcap
would translate BPF filters into a filter program that is compatible
with the underlying kernel subsystem, but this is not yet implemented.

BPF is standard in 4.4BSD, BSD/OS, NetBSD, FreeBSD, and OpenBSD.  DEC
OSF/1/Digital UNIX/Tru64 UNIX uses the packetfilter interface but has
been extended to accept BPF filters (which libpcap utilizes).  Also, you
can add BPF filter support to Ultrix using the kernel source and/or
object patches available in:

	ftp://gatekeeper.dec.com/pub/DEC/net/bpfext42.tar.Z.

Linux, in the 2.2 kernel and later kernels, has a "Socket Filter"
mechanism that accepts BPF filters; see the README.linux file for
information on configuring that option.

Problems, bugs, questions, desirable enhancements, etc. should be sent
to the address "<EMAIL>".  Bugs, support requests,
and feature requests may also be submitted on the SourceForge site for
libpcap at

	http://sourceforge.net/projects/libpcap/

Source code contributions, etc. should be sent to the email address
"<EMAIL>", or submitted as patches on the SourceForge site
for libpcap.

Current versions can be found at www.tcpdump.org, or the SourceForge
site for libpcap.

 - The TCPdump team
