    1  06:57:52.145303 IP (tos 0xc0, ttl 1, id 130, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0x41fe (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd76fc4dc
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
    2  06:57:52.809369 IP (tos 0xc0, ttl 1, id 129, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0xb52e (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd77051ab
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
    3  06:58:02.994044 IP (tos 0xc0, ttl 1, id 139, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Join / Prune, cksum 0x5ae5 (correct), upstream-neighbor: *********
	  1 group(s), holdtime: 3m30s
	    group #1: ***************, joined sources: 1, pruned sources: 0
	      joined source #1: *******(SWR)
    4  06:58:21.507543 IP (tos 0xc0, ttl 1, id 150, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0x41fe (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd76fc4dc
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
    5  06:58:22.315022 IP (tos 0xc0, ttl 1, id 146, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0xb52e (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd77051ab
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
    6  06:58:51.500694 IP (tos 0xc0, ttl 1, id 169, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0x41fe (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd76fc4dc
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
    7  06:58:51.548732 IP (tos 0xc0, ttl 1, id 163, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0xb52e (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd77051ab
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
    8  06:59:01.317976 IP (tos 0xc0, ttl 1, id 175, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Join / Prune, cksum 0x5ae5 (correct), upstream-neighbor: *********
	  1 group(s), holdtime: 3m30s
	    group #1: ***************, joined sources: 1, pruned sources: 0
	      joined source #1: *******(SWR)
    9  06:59:20.870535 IP (tos 0xc0, ttl 1, id 186, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0x41fe (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd76fc4dc
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   10  06:59:21.414583 IP (tos 0xc0, ttl 1, id 179, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0xb52e (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd77051ab
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   11  06:59:33.191272 IP (tos 0xc0, ttl 1, id 186, offset 0, flags [none], proto IGMP (2), length 44)
    ******* > *********: igmp pimv1 RP-reachable group *************** RP ******* hold 4m30s
   12  06:59:50.376254 IP (tos 0xc0, ttl 1, id 205, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0x41fe (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd76fc4dc
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   13  06:59:50.648871 IP (tos 0xc0, ttl 1, id 197, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0xb52e (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd77051ab
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   14  07:00:01.224934 IP (tos 0xc0, ttl 1, id 213, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Join / Prune, cksum 0x5ae5 (correct), upstream-neighbor: *********
	  1 group(s), holdtime: 3m30s
	    group #1: ***************, joined sources: 1, pruned sources: 0
	      joined source #1: *******(SWR)
   15  07:00:20.218579 IP (tos 0xc0, ttl 1, id 224, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0x41fe (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd76fc4dc
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   16  07:00:20.226601 IP (tos 0xc0, ttl 1, id 214, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0xb52e (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd77051ab
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   17  07:00:49.944357 IP (tos 0xc0, ttl 1, id 230, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0xb52e (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd77051ab
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   18  07:00:50.072355 IP (tos 0xc0, ttl 1, id 243, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0x41fe (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd76fc4dc
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   19  07:00:59.672391 IP (tos 0xc0, ttl 1, id 250, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Join / Prune, cksum 0x5ae5 (correct), upstream-neighbor: *********
	  1 group(s), holdtime: 3m30s
	    group #1: ***************, joined sources: 1, pruned sources: 0
	      joined source #1: *******(SWR)
   20  07:01:03.337108 IP (tos 0xc0, ttl 1, id 240, offset 0, flags [none], proto IGMP (2), length 44)
    ******* > *********: igmp pimv1 RP-reachable group *************** RP ******* hold 4m30s
   21  07:01:19.770051 IP (tos 0xc0, ttl 1, id 250, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0xb52e (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd77051ab
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   22  07:01:19.938036 IP (tos 0xc0, ttl 1, id 261, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0x41fe (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd76fc4dc
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   23  07:01:49.087751 IP (tos 0xc0, ttl 1, id 280, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0x41fe (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd76fc4dc
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   24  07:01:49.231769 IP (tos 0xc0, ttl 1, id 266, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0xb52e (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd77051ab
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   25  07:01:58.735847 IP (tos 0xc0, ttl 1, id 287, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Join / Prune, cksum 0x5ae5 (correct), upstream-neighbor: *********
	  1 group(s), holdtime: 3m30s
	    group #1: ***************, joined sources: 1, pruned sources: 0
	      joined source #1: *******(SWR)
   26  07:02:18.649136 IP (tos 0xc0, ttl 1, id 300, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0x41fe (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd76fc4dc
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   27  07:02:18.945068 IP (tos 0xc0, ttl 1, id 285, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0xb52e (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd77051ab
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   28  07:02:33.485950 IP (tos 0xc0, ttl 1, id 292, offset 0, flags [none], proto IGMP (2), length 44)
    ******* > *********: igmp pimv1 RP-reachable group *************** RP ******* hold 4m30s
   29  07:02:48.515439 IP (tos 0xc0, ttl 1, id 316, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0x41fe (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd76fc4dc
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   30  07:02:48.603341 IP (tos 0xc0, ttl 1, id 301, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0xb52e (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd77051ab
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   31  07:02:57.455847 IP (tos 0xc0, ttl 1, id 326, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Join / Prune, cksum 0x5ae5 (correct), upstream-neighbor: *********
	  1 group(s), holdtime: 3m30s
	    group #1: ***************, joined sources: 1, pruned sources: 0
	      joined source #1: *******(SWR)
   32  07:03:17.925048 IP (tos 0xc0, ttl 1, id 337, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0x41fe (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd76fc4dc
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   33  07:03:18.053082 IP (tos 0xc0, ttl 1, id 319, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0xb52e (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd77051ab
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   34  07:03:47.046253 IP (tos 0xc0, ttl 1, id 353, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0x41fe (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd76fc4dc
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   35  07:03:47.334300 IP (tos 0xc0, ttl 1, id 335, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0xb52e (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd77051ab
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   36  07:03:56.910879 IP (tos 0xc0, ttl 1, id 362, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Join / Prune, cksum 0x5ae5 (correct), upstream-neighbor: *********
	  1 group(s), holdtime: 3m30s
	    group #1: ***************, joined sources: 1, pruned sources: 0
	      joined source #1: *******(SWR)
   37  07:04:03.639355 IP (tos 0xc0, ttl 1, id 346, offset 0, flags [none], proto IGMP (2), length 44)
    ******* > *********: igmp pimv1 RP-reachable group *************** RP ******* hold 4m30s
   38  07:04:16.199987 IP (tos 0xc0, ttl 1, id 374, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0x41fe (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd76fc4dc
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   39  07:04:16.488041 IP (tos 0xc0, ttl 1, id 354, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0xb52e (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd77051ab
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   40  07:04:45.714322 IP (tos 0xc0, ttl 1, id 390, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0x41fe (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd76fc4dc
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   41  07:04:46.186375 IP (tos 0xc0, ttl 1, id 372, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0xb52e (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd77051ab
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   42  07:04:56.018349 IP (tos 0xc0, ttl 1, id 399, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Join / Prune, cksum 0x5ae5 (correct), upstream-neighbor: *********
	  1 group(s), holdtime: 3m30s
	    group #1: ***************, joined sources: 1, pruned sources: 0
	      joined source #1: *******(SWR)
   43  07:05:15.416053 IP (tos 0xc0, ttl 1, id 412, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0x41fe (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd76fc4dc
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   44  07:05:15.824099 IP (tos 0xc0, ttl 1, id 388, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0xb52e (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd77051ab
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   45  07:05:26.200107 IP (tos 0xc0, ttl 1, id 420, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Join / Prune, cksum 0x5ae5 (correct), upstream-neighbor: *********
	  1 group(s), holdtime: 3m30s
	    group #1: ***************, joined sources: 0, pruned sources: 1
	      pruned source #1: *******(SWR)
   46  07:05:44.917811 IP (tos 0xc0, ttl 1, id 431, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0x41fe (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd76fc4dc
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
   47  07:05:45.085883 IP (tos 0xc0, ttl 1, id 404, offset 0, flags [none], proto PIM (103), length 54)
    ********* > **********: PIMv2, length 34
	Hello, cksum 0xb52e (correct)
	  Hold Time Option (1), length 2, Value: 1m45s
	  Generation ID Option (20), length 4, Value: 0xd77051ab
	  DR Priority Option (19), length 4, Value: 1
	  State Refresh Capability Option (21), length 4, Value: v1
