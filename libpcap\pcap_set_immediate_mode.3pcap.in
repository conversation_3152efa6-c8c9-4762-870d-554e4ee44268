.\"
.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_SET_IMMEDIATE_MODE 3PCAP "27 January 2024"
.SH NAME
pcap_set_immediate_mode \- set immediate mode for a not-yet-activated capture
handle
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.LP
.ft B
int pcap_set_immediate_mode(pcap_t *p, int immediate_mode);
.ft
.fi
.SH DESCRIPTION
.BR pcap_set_immediate_mode ()
sets whether immediate mode should be set on a capture handle when
the handle is activated.  In immediate mode, packets are always
delivered as soon as they arrive, with no buffering.
If
.I immediate_mode
is non-zero, immediate mode will be set, otherwise it will not be set.
.SH RETURN VALUE
.BR pcap_set_immediate_mode ()
returns
.B 0
on success or
.B PCAP_ERROR_ACTIVATED
if called on a capture handle that has been activated.
.SH BACKWARD COMPATIBILITY
.PP
This function became available in libpcap release 1.5.0.  In previous
releases, if immediate delivery of packets is required:
.IP
on FreeBSD, NetBSD, OpenBSD, DragonFly BSD, macOS, and Solaris 11,
immediate mode must be turned on with a
.B BIOCIMMEDIATE
.BR ioctl (2),
as documented in
.BR bpf (@MAN_DEVICES@),
on the descriptor returned by
.BR pcap_fileno (3PCAP),
after
.BR pcap_activate (3PCAP)
is called;
.IP
on Solaris 10 and earlier versions of Solaris, immediate mode must be
turned on by using a read timeout of 0 when opening the device (this
will not provide immediate delivery of packets on other platforms, so
don't assume it's sufficient);
.IP
on Windows, immediate mode must be turned on by calling
.BR pcap_setmintocopy ()
with a size of 0.
.PP
On Linux, with previous releases of libpcap, capture devices are always
in immediate mode; however, in 1.5.0 and later, they are, by default,
.B not
in immediate mode, so if
.BR pcap_set_immediate_mode ()
is available, it should be used.
.PP
On other platforms, capture devices are always in immediate mode.
.SH SEE ALSO
.BR pcap (3PCAP),
.BR pcap_create (3PCAP),
.BR pcap_activate (3PCAP)
