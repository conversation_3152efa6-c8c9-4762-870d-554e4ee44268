    1  21:46:16.463334 IP *************.7001 > ************.7000:  rx data fs call fetch-status fid 536871098/846/1049757 (44)
    2  21:46:16.483206 IP ************.7000 > *************.7001:  rx data fs reply fetch-status (148)
    3  21:46:16.889677 IP *************.7001 > ************.7000:  rx ack first 2 serial 347 reason delay (65)
    4  21:46:24.151512 IP *************.7001 > ************.7000:  rx data fs call makedir fid 536871098/1/1 "tmpdir" StoreStatus date 1999/11/11 21:46:24 group 0 mode 755 (80)
    5  21:46:24.245048 IP ************.7000 > *************.7001:  rx data cb call callback fid 536871098/1/1 (52)
    6  21:46:24.255513 IP *************.7001 > ************.7000:  rx data (28)
    7  21:46:24.255528 IP *************.7001 > ************.7000:  rx data (28)
    8  21:46:24.282365 IP ************.7000 > *************.7001:  rx data fs reply makedir new fid 536871098/677/1097448 (244)
    9  21:46:24.283047 IP *************.7001 > ************.7000:  rx data fs call fetch-status fid 536871098/677/1097448 (44)
   10  21:46:24.284042 IP ************.7000 > *************.7001:  rx data fs reply fetch-status (148)
   11  21:46:24.679610 IP *************.7001 > ************.7000:  rx ack first 2 serial 349 reason delay (65)
   12  21:46:24.781785 IP ************.7000 > *************.7001:  rx ack first 1 serial 1154 reason delay acked 1 (62)
   13  21:46:28.541035 IP *************.7001 > ************.7000:  rx data fs call rmdir fid 536871098/1/1 "tmpdir" (56)
   14  21:46:28.544636 IP ************.7000 > *************.7001:  rx data fs reply rmdir (136)
   15  21:46:28.949547 IP *************.7001 > ************.7000:  rx ack first 2 serial 350 reason delay (65)
   16  21:46:38.681457 IP ************.7000 > *************.7001:  rx data cb call probe (32)
   17  21:46:38.690316 IP *************.7001 > ************.7000:  rx data (28)
   18  21:46:38.690352 IP *************.7001 > ************.7000:  rx data (28)
   19  21:46:39.196737 IP ************.7000 > *************.7001:  rx ack first 2 serial 656 reason delay (61)
   20  21:46:48.590067 IP *************.7001 > *************.7000:  rx data fs call fetch-status fid 536977399/40/27 (44)
   21  21:46:48.619971 IP *************.7000 > *************.7001:  rx data fs reply fetch-status (148)
   22  21:46:48.810858 IP *************.1792 > ************.7003:  rx data vldb call get-entry-by-name "root.cell" (48)
   23  21:46:48.812595 IP ************.7003 > *************.1792:  rx data vldb reply get-entry-by-name "root.cell" numservers 6 servers ************* ************ ************* ************ ************ ************ partitions a a a a a a rwvol 536870915 rovol 536870916 backup 536870917 (412)
   24  21:46:48.813282 IP *************.7001 > ************.7000:  rx data fs call symlink fid 536871098/1/1 "rotcel" link to "#root.cell." (96)
   25  21:46:48.830808 IP ************.7000 > *************.7001:  rx data fs reply symlink (232)
   26  21:46:49.029316 IP *************.7001 > *************.7000:  rx ack first 2 serial 2519 reason delay (65)
   27  21:46:49.229306 IP *************.7001 > ************.7000:  rx ack first 2 serial 351 reason delay (65)
   28  21:46:51.218454 IP ************.7003 > *************.1792:  rx data vldb reply get-entry-by-name "root.cell" numservers 6 servers ************* ************ ************* ************ ************ ************ partitions a a a a a a rwvol 536870915 rovol 536870916 backup 536870917 (412)
   29  21:46:51.218541 IP ************* > ************: ICMP ************* udp port 1792 unreachable, length 448
   30  21:46:52.805338 IP *************.7001 > ************.7000:  rx data fs call remove-file fid 536871098/1/1 "rotcel" (56)
   31  21:46:52.810150 IP ************.7000 > *************.7001:  rx data fs reply remove-file (136)
   32  21:46:53.209266 IP *************.7001 > ************.7000:  rx ack first 2 serial 352 reason delay (65)
   33  21:46:53.878655 IP ************.7003 > *************.1792:  rx data vldb reply get-entry-by-name "root.cell" numservers 6 servers ************* ************ ************* ************ ************ ************ partitions a a a a a a rwvol 536870915 rovol 536870916 backup 536870917 (412)
   34  21:46:53.878718 IP ************* > ************: ICMP ************* udp port 1792 unreachable, length 448
   35  21:46:56.242994 IP *************.7001 > *************.7000:  rx data fs call fetch-status fid 536977399/86/51 (44)
   36  21:46:56.245019 IP *************.7000 > *************.7001:  rx data fs reply fetch-status (148)
   37  21:46:56.518772 IP *************.1792 > ************.7003:  rx data vldb call get-entry-by-name-n "users.nneul" (48)
   38  21:46:56.519452 IP ************.7003 > *************.1792:  rx challenge (44)
   39  21:46:56.523136 IP *************.1792 > ************.7003:  rx response (140)
   40  21:46:56.525522 IP ************.7003 > *************.1792:  rx data vldb reply get-entry-by-name-n "users.nneul" numservers 1 servers ************ partitions b rwvol 536871098 rovol 536871099 backup 536871100 (504)
   41  21:46:56.525791 IP *************.1792 > ************.7003:  rx data vldb call get-entry-by-id-n volid 536871098 (40)
   42  21:46:56.527259 IP ************.7003 > *************.1792:  rx data vldb reply get-entry-by-id-n "users.nneul" numservers 1 servers ************ partitions b rwvol 536871098 rovol 536871099 backup 536871100 (504)
   43  21:46:56.527629 IP *************.1792 > ************.7005:  rx data vol call list-one-volume partid 1 volid 536871098 (40)
   44  21:46:56.637381 IP ************.7005 > *************.1792:  rx challenge (44)
   45  21:46:56.637779 IP *************.1792 > ************.7005:  rx response (140)
   46  21:46:56.639215 IP *************.7001 > *************.7000:  rx ack first 2 serial 2520 reason delay (65)
   47  21:46:56.920017 IP *************.1792 > ************.7003:  rx ack first 2 serial 3 reason delay (65)
   48  21:46:57.036390 IP ************.7005 > *************.1792:  rx data vol reply list-one-volume name "users.nneul" volid 536871098 type (252)
   49  21:46:57.048744 IP ************.7003 > *************.1792:  rx data vldb reply get-entry-by-name "root.cell" numservers 6 servers ************* ************ ************* ************ ************ ************ partitions a a a a a a rwvol 536870915 rovol 536870916 backup 536870917 (412)
   50  21:46:57.061382 IP *************.1792 > ************.7005:  rx ackall (28)
   51  21:47:00.778759 IP ************.7003 > *************.1792:  rx data vldb reply get-entry-by-name "root.cell" numservers 6 servers ************* ************ ************* ************ ************ ************ partitions a a a a a a rwvol 536870915 rovol 536870916 backup 536870917 (412)
   52  21:47:00.778818 IP ************* > ************: ICMP ************* udp port 1792 unreachable, length 448
   53  21:47:00.817967 IP *************.7001 > *************.7000:  rx data fs call fetch-status fid 536977399/14/14 (44)
   54  21:47:00.820615 IP *************.7000 > *************.7001:  rx data fs reply fetch-status (148)
   55  21:47:00.995692 IP *************.1799 > ************.7002:  rx data pt call name-to-id "users.nneul" (292)
   56  21:47:00.996639 IP ************.7002 > *************.1799:  rx challenge (44)
   57  21:47:00.996822 IP *************.1799 > ************.7002:  rx response (140)
   58  21:47:00.998994 IP ************.7002 > *************.1799:  rx data pt reply name-to-id ids: 32766 (36)
   59  21:47:01.000150 IP *************.1799 > ************.7002:  rx data pt call name-to-id "users.nneul" (292)
   60  21:47:01.001268 IP ************.7002 > *************.1799:  rx data pt reply name-to-id ids: 32766 (36)
   61  21:47:01.005342 IP *************.1799 > ************.7002:  rx data pt call id-to-name ids: <none!> (36)
   62  21:47:01.005915 IP ************.7002 > *************.1799:  rx data pt reply id-to-name <none!> (32)
   63  21:47:01.006087 IP *************.1799 > ************.7002:  rx ackall (28)
   64  21:47:01.219166 IP *************.7001 > *************.7000:  rx ack first 2 serial 2521 reason delay (65)
   65  21:47:03.010034 IP *************.7001 > ************.7000:  rx data fs call give-cbs (112)
   66  21:47:03.011088 IP ************.7000 > *************.7001:  rx data (28)
   67  21:47:03.409140 IP *************.7001 > ************.7000:  rx ack first 2 serial 55 reason delay (65)
   68  21:47:05.869072 IP *************.1799 > ************.7002:  rx data pt call name-to-id "nneul" (292)
   69  21:47:05.869722 IP ************.7002 > *************.1799:  rx challenge (44)
   70  21:47:05.870422 IP *************.1799 > ************.7002:  rx response (140)
   71  21:47:05.872757 IP ************.7002 > *************.1799:  rx data pt reply name-to-id ids: 5879 (36)
   72  21:47:05.873149 IP *************.1799 > ************.7002:  rx data pt call name-to-id "nneul" (292)
   73  21:47:05.874355 IP ************.7002 > *************.1799:  rx data pt reply name-to-id ids: 5879 (36)
   74  21:47:05.874531 IP *************.1799 > ************.7002:  rx data pt call id-to-name ids: <none!> (36)
   75  21:47:05.875156 IP ************.7002 > *************.1799:  rx data pt reply id-to-name <none!> (32)
   76  21:47:05.875335 IP *************.1799 > ************.7002:  rx data pt call list-entry id 5879 (36)
   77  21:47:05.877704 IP ************.7002 > *************.1799:  rx data pt reply list-entry (332)
   78  21:47:05.877925 IP *************.1799 > ************.7002:  rx data pt call id-to-name ids: -204 5113 (44)
   79  21:47:05.879692 IP ************.7002 > *************.1799:  rx data pt reply id-to-name "system:administrators" "5113" (544)
   80  21:47:05.883080 IP *************.1799 > ************.7002:  rx data pt call name-to-id "nneul" "system:administrators" (548)
   81  21:47:05.884646 IP ************.7002 > *************.1799:  rx data pt reply name-to-id ids: 5879 -204 (40)
   82  21:47:05.884950 IP *************.1799 > ************.7002:  rx data pt call same-mbr-of uid 5879 gid -204 (40)
   83  21:47:05.886482 IP ************.7002 > *************.1799:  rx data pt reply same-mbr-of (32)
   84  21:47:05.888922 IP *************.1799 > ************.7002:  rx ackall (28)
   85  21:47:06.559070 IP ************.7003 > *************.1792:  rx data vldb reply get-entry-by-name "root.cell" numservers 6 servers ************* ************ ************* ************ ************ ************ partitions a a a a a a rwvol 536870915 rovol 536870916 backup 536870917 (412)
   86  21:47:06.559143 IP ************* > ************: ICMP ************* udp port 1792 unreachable, length 448
   87  21:47:08.697010 IP *************.1799 > ************.7002:  rx data pt call name-to-id "nneul" (292)
   88  21:47:08.697702 IP ************.7002 > *************.1799:  rx challenge (44)
   89  21:47:08.697886 IP *************.1799 > ************.7002:  rx response (140)
   90  21:47:08.700814 IP ************.7002 > *************.1799:  rx data pt reply name-to-id ids: 5879 (36)
   91  21:47:08.701061 IP *************.1799 > ************.7002:  rx data pt call name-to-id "nneul" (292)
   92  21:47:08.702243 IP ************.7002 > *************.1799:  rx data pt reply name-to-id ids: 5879 (36)
   93  21:47:08.702422 IP *************.1799 > ************.7002:  rx data pt call id-to-name ids: <none!> (36)
   94  21:47:08.703045 IP ************.7002 > *************.1799:  rx data pt reply id-to-name <none!> (32)
   95  21:47:08.703345 IP *************.1799 > ************.7002:  rx data pt call list-elements id 5879 (36)
   96  21:47:08.705113 IP ************.7002 > *************.1799:  rx data pt reply list-entry (80)
   97  21:47:08.705296 IP *************.1799 > ************.7002:  rx data pt call id-to-name ids: -641 -569 -564 -478 -472 -441 -427 -424 -355 -348 -254 (80)
   98  21:47:08.738631 IP ************.7002 > *************.1799:  rx data pt reply id-to-name "nneul:cs301" "cc-staff" "obrennan:sysprog" "software" "bbc:mtw" [|pt] (1472)
   99  21:47:08.740294 IP ************.7002 > *************.1799:  rx data (1404)
  100  21:47:08.740581 IP *************.1799 > ************.7002:  rx ack first 2 serial 7 reason delay acked 2 (66)
  101  21:47:16.440550 IP ************.7003 > *************.1792:  rx data vldb reply get-entry-by-name "root.cell" numservers 6 servers ************* ************ ************* ************ ************ ************ partitions a a a a a a rwvol 536870915 rovol 536870916 backup 536870917 (412)
  102  21:47:16.440614 IP ************* > ************: ICMP ************* udp port 1792 unreachable, length 448
  103  21:47:22.963348 IP *************.1799 > ************.7002:  rx data pt call name-to-id "cc-staff" (292)
  104  21:47:22.964051 IP ************.7002 > *************.1799:  rx challenge (44)
  105  21:47:22.964237 IP *************.1799 > ************.7002:  rx response (140)
  106  21:47:22.966418 IP ************.7002 > *************.1799:  rx data pt reply name-to-id ids: -569 (36)
  107  21:47:22.966644 IP *************.1799 > ************.7002:  rx data pt call name-to-id "cc-staff" (292)
  108  21:47:22.967810 IP ************.7002 > *************.1799:  rx data pt reply name-to-id ids: -569 (36)
  109  21:47:22.967987 IP *************.1799 > ************.7002:  rx data pt call id-to-name ids: <none!> (36)
  110  21:47:22.968556 IP ************.7002 > *************.1799:  rx data pt reply id-to-name <none!> (32)
  111  21:47:22.969841 IP *************.1799 > ************.7002:  rx data pt call list-elements id -569 (36)
  112  21:47:22.971342 IP ************.7002 > *************.1799:  rx data pt reply list-entry (112)
  113  21:47:22.971544 IP *************.1799 > ************.7002:  rx data pt call id-to-name ids: 5002 5004 5013 5016 5021 5022 5150 5171 5195 5211 5220 5339 5408 5879 13081 17342 19999 20041 20176 (112)
  114  21:47:23.005534 IP ************.7002 > *************.1799:  rx data pt reply id-to-name "rms" "rwa" "uetrecht" "dwd" "kjh" [|pt] (1444)
  115  21:47:23.006602 IP ************.7002 > *************.1799:  rx data (1444)
  116  21:47:23.007048 IP *************.1799 > ************.7002:  rx ack first 2 serial 7 reason delay acked 2 (66)
  117  21:47:23.007745 IP ************.7002 > *************.1799:  rx data (1444)
  118  21:47:23.008408 IP ************.7002 > *************.1799:  rx data (648)
  119  21:47:23.008550 IP *************.1799 > ************.7002:  rx ack first 4 serial 9 reason delay acked 4 (66)
  120  21:47:26.569758 IP ************.7003 > *************.1792:  rx data vldb reply get-entry-by-name "root.cell" numservers 6 servers ************* ************ ************* ************ ************ ************ partitions a a a a a a rwvol 536870915 rovol 536870916 backup 536870917 (412)
  121  21:47:26.569822 IP ************* > ************: ICMP ************* udp port 1792 unreachable, length 448
  122  21:47:31.825501 IP *************.7001 > *************.7000:  rx data fs call fetch-status fid 536977399/16/15 (44)
  123  21:47:31.827985 IP *************.7000 > *************.7001:  rx data fs reply fetch-status (148)
  124  21:47:31.829082 IP *************.7001 > *************.7000:  rx data fs call fetch-data fid 536977399/16/15 offset 0 length 65536 (52)
  125  21:47:31.872588 IP *************.7000 > *************.7001:  rx data fs reply fetch-data (1472)
  126  21:47:31.873045 IP ************* > *************: ip-proto-17
  127  21:47:31.873238 IP ************* > *************: ip-proto-17
  128  21:47:31.873323 IP ************* > *************: ip-proto-17
  129  21:47:31.874199 IP *************.7000 > *************.7001:  rx data (1472)
  130  21:47:31.874320 IP ************* > *************: ip-proto-17
  131  21:47:31.874444 IP ************* > *************: ip-proto-17
  132  21:47:31.874527 IP ************* > *************: ip-proto-17
  133  21:47:31.874656 IP *************.7001 > *************.7000:  rx ack first 2 serial 2524 reason ack requested acked 2 (66)
  134  21:47:31.911711 IP *************.7000 > *************.7001:  rx data (1472)
  135  21:47:31.911830 IP ************* > *************: ip-proto-17
  136  21:47:31.911963 IP ************* > *************: ip-proto-17
  137  21:47:31.912047 IP ************* > *************: ip-proto-17
  138  21:47:31.912793 IP *************.7000 > *************.7001:  rx data (1472)
  139  21:47:31.912917 IP ************* > *************: ip-proto-17
  140  21:47:31.913050 IP ************* > *************: ip-proto-17
  141  21:47:31.913123 IP ************* > *************: ip-proto-17
  142  21:47:31.913290 IP *************.7001 > *************.7000:  rx ack first 4 serial 2526 reason ack requested acked 4 (66)
  143  21:47:31.914161 IP *************.7000 > *************.7001:  rx data (1472)
  144  21:47:31.914283 IP ************* > *************: ip-proto-17
  145  21:47:31.914405 IP ************* > *************: ip-proto-17
  146  21:47:31.914488 IP ************* > *************: ip-proto-17
  147  21:47:31.915372 IP *************.7000 > *************.7001:  rx data (1472)
  148  21:47:31.915494 IP ************* > *************: ip-proto-17
  149  21:47:31.915618 IP ************* > *************: ip-proto-17
  150  21:47:31.915702 IP ************* > *************: ip-proto-17
  151  21:47:31.915835 IP *************.7001 > *************.7000:  rx ack first 6 serial 2528 reason ack requested acked 6 (66)
  152  21:47:31.921854 IP *************.7000 > *************.7001:  rx data (1472)
  153  21:47:31.921976 IP ************* > *************: ip-proto-17
  154  21:47:31.922099 IP ************* > *************: ip-proto-17
  155  21:47:31.922182 IP ************* > *************: ip-proto-17
  156  21:47:31.923223 IP *************.7000 > *************.7001:  rx data (1472)
  157  21:47:31.923347 IP ************* > *************: ip-proto-17
  158  21:47:31.923470 IP ************* > *************: ip-proto-17
  159  21:47:31.923553 IP ************* > *************: ip-proto-17
  160  21:47:31.923698 IP *************.7001 > *************.7000:  rx ack first 8 serial 2530 reason ack requested acked 8 (66)
  161  21:47:31.924962 IP *************.7000 > *************.7001:  rx data (1472)
  162  21:47:31.925085 IP ************* > *************: ip-proto-17
  163  21:47:31.925207 IP ************* > *************: ip-proto-17
  164  21:47:31.925291 IP ************* > *************: ip-proto-17
  165  21:47:31.926314 IP *************.7000 > *************.7001:  rx data (1472)
  166  21:47:31.926436 IP ************* > *************: ip-proto-17
  167  21:47:31.926560 IP ************* > *************: ip-proto-17
  168  21:47:31.926641 IP ************* > *************: ip-proto-17
  169  21:47:31.926761 IP *************.7001 > *************.7000:  rx ack first 10 serial 2532 reason ack requested acked 10 (66)
  170  21:47:31.927670 IP *************.7000 > *************.7001:  rx data (1472)
  171  21:47:31.927794 IP ************* > *************: ip-proto-17
  172  21:47:31.927917 IP ************* > *************: ip-proto-17
  173  21:47:31.927999 IP ************* > *************: ip-proto-17
  174  21:47:31.928955 IP *************.7000 > *************.7001:  rx data (1472)
  175  21:47:31.929070 IP ************* > *************: ip-proto-17
  176  21:47:31.929090 IP ************* > *************: ip-proto-17
  177  21:47:31.929216 IP *************.7001 > *************.7000:  rx ack first 12 serial 2534 reason delay acked 12 (66)
  178  21:47:31.931311 IP *************.7001 > *************.7000:  rx data fs call fetch-data fid 536977399/16/15 offset 131072 length 56972 (52)
  179  21:47:31.946920 IP *************.7000 > *************.7001:  rx data fs reply fetch-data (1472)
  180  21:47:31.947042 IP ************* > *************: ip-proto-17
  181  21:47:31.947179 IP ************* > *************: ip-proto-17
  182  21:47:31.947258 IP ************* > *************: ip-proto-17
  183  21:47:31.948245 IP *************.7000 > *************.7001:  rx data (1472)
  184  21:47:31.948368 IP ************* > *************: ip-proto-17
  185  21:47:31.948492 IP ************* > *************: ip-proto-17
  186  21:47:31.948574 IP ************* > *************: ip-proto-17
  187  21:47:31.948714 IP *************.7001 > *************.7000:  rx ack first 2 serial 2536 reason ack requested acked 2 (66)
  188  21:47:31.949601 IP *************.7000 > *************.7001:  rx data (1472)
  189  21:47:31.949715 IP ************* > *************: ip-proto-17
  190  21:47:31.949838 IP ************* > *************: ip-proto-17
  191  21:47:31.949921 IP ************* > *************: ip-proto-17
  192  21:47:31.950714 IP *************.7000 > *************.7001:  rx data (1472)
  193  21:47:31.950835 IP ************* > *************: ip-proto-17
  194  21:47:31.950959 IP ************* > *************: ip-proto-17
  195  21:47:31.951042 IP ************* > *************: ip-proto-17
  196  21:47:31.951177 IP *************.7001 > *************.7000:  rx ack first 4 serial 2538 reason ack requested acked 4 (66)
  197  21:47:31.952808 IP *************.7000 > *************.7001:  rx data (1472)
  198  21:47:31.952930 IP ************* > *************: ip-proto-17
  199  21:47:31.953063 IP ************* > *************: ip-proto-17
  200  21:47:31.953145 IP ************* > *************: ip-proto-17
  201  21:47:31.954021 IP *************.7000 > *************.7001:  rx data (1472)
  202  21:47:31.954153 IP ************* > *************: ip-proto-17
  203  21:47:31.954266 IP ************* > *************: ip-proto-17
  204  21:47:31.954351 IP ************* > *************: ip-proto-17
  205  21:47:31.954501 IP *************.7001 > *************.7000:  rx ack first 6 serial 2540 reason ack requested acked 6 (66)
  206  21:47:31.955104 IP *************.7000 > *************.7001:  rx data (1472)
  207  21:47:31.955226 IP ************* > *************: ip-proto-17
  208  21:47:31.955349 IP ************* > *************: ip-proto-17
  209  21:47:31.955433 IP ************* > *************: ip-proto-17
  210  21:47:31.956561 IP *************.7000 > *************.7001:  rx data (1472)
  211  21:47:31.956683 IP ************* > *************: ip-proto-17
  212  21:47:31.956807 IP ************* > *************: ip-proto-17
  213  21:47:31.956897 IP ************* > *************: ip-proto-17
  214  21:47:31.957074 IP *************.7001 > *************.7000:  rx ack first 8 serial 2542 reason ack requested acked 8 (66)
  215  21:47:31.958291 IP *************.7000 > *************.7001:  rx data (1472)
  216  21:47:31.958413 IP ************* > *************: ip-proto-17
  217  21:47:31.958536 IP ************* > *************: ip-proto-17
  218  21:47:31.958620 IP ************* > *************: ip-proto-17
  219  21:47:31.959648 IP *************.7000 > *************.7001:  rx data (1472)
  220  21:47:31.959768 IP ************* > *************: ip-proto-17
  221  21:47:31.959881 IP ************* > *************: ip-proto-17
  222  21:47:31.959978 IP ************* > *************: ip-proto-17
  223  21:47:31.959997 IP *************.7000 > *************.7001:  rx data (484)
  224  21:47:31.960153 IP *************.7001 > *************.7000:  rx ack first 10 serial 2544 reason ack requested acked 10 (66)
  225  21:47:31.968719 IP *************.7001 > *************.7000:  rx data fs call fetch-data fid 536977399/16/15 offset 65536 length 65536 (52)
  226  21:47:31.973708 IP *************.7000 > *************.7001:  rx data fs reply fetch-data (1472)
  227  21:47:31.973826 IP ************* > *************: ip-proto-17
  228  21:47:31.973953 IP ************* > *************: ip-proto-17
  229  21:47:31.974036 IP ************* > *************: ip-proto-17
  230  21:47:31.975130 IP *************.7000 > *************.7001:  rx data (1472)
  231  21:47:31.975251 IP ************* > *************: ip-proto-17
  232  21:47:31.975374 IP ************* > *************: ip-proto-17
  233  21:47:31.975457 IP ************* > *************: ip-proto-17
  234  21:47:31.975644 IP *************.7001 > *************.7000:  rx ack first 2 serial 2547 reason ack requested acked 2 (66)
  235  21:47:31.976494 IP *************.7000 > *************.7001:  rx data (1472)
  236  21:47:31.976614 IP ************* > *************: ip-proto-17
  237  21:47:31.976732 IP ************* > *************: ip-proto-17
  238  21:47:31.976816 IP ************* > *************: ip-proto-17
  239  21:47:31.977547 IP *************.7000 > *************.7001:  rx data (1472)
  240  21:47:31.977658 IP ************* > *************: ip-proto-17
  241  21:47:31.977781 IP ************* > *************: ip-proto-17
  242  21:47:31.977865 IP ************* > *************: ip-proto-17
  243  21:47:31.978006 IP *************.7001 > *************.7000:  rx ack first 4 serial 2549 reason ack requested acked 4 (66)
  244  21:47:31.978903 IP *************.7000 > *************.7001:  rx data (1472)
  245  21:47:31.979022 IP ************* > *************: ip-proto-17
  246  21:47:31.979152 IP ************* > *************: ip-proto-17
  247  21:47:31.979234 IP ************* > *************: ip-proto-17
  248  21:47:31.980103 IP *************.7000 > *************.7001:  rx data (1472)
  249  21:47:31.980225 IP ************* > *************: ip-proto-17
  250  21:47:31.980348 IP ************* > *************: ip-proto-17
  251  21:47:31.980442 IP ************* > *************: ip-proto-17
  252  21:47:31.980584 IP *************.7001 > *************.7000:  rx ack first 6 serial 2551 reason ack requested acked 6 (66)
  253  21:47:31.981466 IP *************.7000 > *************.7001:  rx data (1472)
  254  21:47:31.981612 IP ************* > *************: ip-proto-17
  255  21:47:31.981736 IP ************* > *************: ip-proto-17
  256  21:47:31.981819 IP ************* > *************: ip-proto-17
  257  21:47:31.982687 IP *************.7000 > *************.7001:  rx data (1472)
  258  21:47:31.982809 IP ************* > *************: ip-proto-17
  259  21:47:31.982931 IP ************* > *************: ip-proto-17
  260  21:47:31.983013 IP ************* > *************: ip-proto-17
  261  21:47:31.983173 IP *************.7001 > *************.7000:  rx ack first 8 serial 2553 reason ack requested acked 8 (66)
  262  21:47:31.984600 IP *************.7000 > *************.7001:  rx data (1472)
  263  21:47:31.984721 IP ************* > *************: ip-proto-17
  264  21:47:31.984846 IP ************* > *************: ip-proto-17
  265  21:47:31.984929 IP ************* > *************: ip-proto-17
  266  21:47:31.985969 IP *************.7000 > *************.7001:  rx data (1472)
  267  21:47:31.986089 IP ************* > *************: ip-proto-17
  268  21:47:31.986212 IP ************* > *************: ip-proto-17
  269  21:47:31.986306 IP ************* > *************: ip-proto-17
  270  21:47:31.986455 IP *************.7001 > *************.7000:  rx ack first 10 serial 2555 reason ack requested acked 10 (66)
  271  21:47:31.987315 IP *************.7000 > *************.7001:  rx data (1472)
  272  21:47:31.987436 IP ************* > *************: ip-proto-17
  273  21:47:31.987559 IP ************* > *************: ip-proto-17
  274  21:47:31.987643 IP ************* > *************: ip-proto-17
  275  21:47:31.988562 IP *************.7000 > *************.7001:  rx data (1472)
  276  21:47:31.988678 IP ************* > *************: ip-proto-17
  277  21:47:31.988696 IP ************* > *************: ip-proto-17
  278  21:47:31.989166 IP *************.7001 > *************.7000:  rx ack first 12 serial 2557 reason delay acked 12 (66)
  279  21:47:36.960670 IP ************.7003 > *************.1792:  rx data vldb reply get-entry-by-name "root.cell" numservers 6 servers ************* ************ ************* ************ ************ ************ partitions a a a a a a rwvol 536870915 rovol 536870916 backup 536870917 (412)
  280  21:47:36.960736 IP ************* > ************: ICMP ************* udp port 1792 unreachable, length 448
  281  21:47:38.824245 IP ************.7000 > *************.7001:  rx data cb call probe (32)
  282  21:47:38.832720 IP *************.7001 > ************.7000:  rx data (28)
  283  21:47:38.832736 IP *************.7001 > ************.7000:  rx data (28)
  284  21:47:39.340205 IP ************.7000 > *************.7001:  rx ack first 2 serial 1155 reason delay (61)
  285  21:47:47.600747 IP ************.7003 > *************.1792:  rx data vldb reply get-entry-by-name "root.cell" numservers 6 servers ************* ************ ************* ************ ************ ************ partitions a a a a a a rwvol 536870915 rovol 536870916 backup 536870917 (412)
  286  21:47:47.600817 IP ************* > ************: ICMP ************* udp port 1792 unreachable, length 448
  287  21:47:50.558379 IP *************.7001 > *************.7000:  rx data fs call fetch-status fid 536977399/30/22 (44)
  288  21:47:50.559765 IP *************.7000 > *************.7001:  rx data fs reply fetch-status (148)
  289  21:47:50.560341 IP *************.7001 > *************.7000:  rx data fs call fetch-data fid 536977399/30/22 offset 0 length 65536 (52)
  290  21:47:50.586027 IP *************.7000 > *************.7001:  rx data fs reply fetch-data (1472)
  291  21:47:50.586148 IP ************* > *************: ip-proto-17
  292  21:47:50.586270 IP ************* > *************: ip-proto-17
  293  21:47:50.586353 IP ************* > *************: ip-proto-17
  294  21:47:50.598397 IP *************.7000 > *************.7001:  rx data (1472)
  295  21:47:50.598517 IP ************* > *************: ip-proto-17
  296  21:47:50.598641 IP ************* > *************: ip-proto-17
  297  21:47:50.598723 IP ************* > *************: ip-proto-17
  298  21:47:50.599028 IP *************.7001 > *************.7000:  rx ack first 2 serial 2560 reason ack requested acked 2 (66)
  299  21:47:50.613313 IP *************.7000 > *************.7001:  rx data (1472)
  300  21:47:50.613434 IP ************* > *************: ip-proto-17
  301  21:47:50.613557 IP ************* > *************: ip-proto-17
  302  21:47:50.613640 IP ************* > *************: ip-proto-17
  303  21:47:50.614408 IP *************.7000 > *************.7001:  rx data (1472)
  304  21:47:50.614529 IP ************* > *************: ip-proto-17
  305  21:47:50.614653 IP ************* > *************: ip-proto-17
  306  21:47:50.614736 IP ************* > *************: ip-proto-17
  307  21:47:50.614884 IP *************.7001 > *************.7000:  rx ack first 4 serial 2562 reason ack requested acked 4 (66)
  308  21:47:50.615759 IP *************.7000 > *************.7001:  rx data (1472)
  309  21:47:50.615881 IP ************* > *************: ip-proto-17
  310  21:47:50.616003 IP ************* > *************: ip-proto-17
  311  21:47:50.616086 IP ************* > *************: ip-proto-17
  312  21:47:50.617064 IP *************.7000 > *************.7001:  rx data (1472)
  313  21:47:50.617195 IP ************* > *************: ip-proto-17
  314  21:47:50.617309 IP ************* > *************: ip-proto-17
  315  21:47:50.617392 IP ************* > *************: ip-proto-17
  316  21:47:50.617571 IP *************.7001 > *************.7000:  rx ack first 6 serial 2564 reason ack requested acked 6 (66)
  317  21:47:50.618132 IP *************.7000 > *************.7001:  rx data (1472)
  318  21:47:50.618264 IP ************* > *************: ip-proto-17
  319  21:47:50.618388 IP ************* > *************: ip-proto-17
  320  21:47:50.618470 IP ************* > *************: ip-proto-17
  321  21:47:50.619700 IP *************.7000 > *************.7001:  rx data (1472)
  322  21:47:50.619811 IP ************* > *************: ip-proto-17
  323  21:47:50.619936 IP ************* > *************: ip-proto-17
  324  21:47:50.620017 IP ************* > *************: ip-proto-17
  325  21:47:50.620153 IP *************.7001 > *************.7000:  rx ack first 8 serial 2566 reason ack requested acked 8 (66)
  326  21:47:50.621466 IP *************.7000 > *************.7001:  rx data (1472)
  327  21:47:50.621587 IP ************* > *************: ip-proto-17
  328  21:47:50.621710 IP ************* > *************: ip-proto-17
  329  21:47:50.621794 IP ************* > *************: ip-proto-17
  330  21:47:50.622905 IP *************.7000 > *************.7001:  rx data (1472)
  331  21:47:50.623020 IP ************* > *************: ip-proto-17
  332  21:47:50.623158 IP ************* > *************: ip-proto-17
  333  21:47:50.623227 IP ************* > *************: ip-proto-17
  334  21:47:50.623423 IP *************.7001 > *************.7000:  rx ack first 10 serial 2568 reason ack requested acked 10 (66)
  335  21:47:50.624233 IP *************.7000 > *************.7001:  rx data (1472)
  336  21:47:50.624358 IP ************* > *************: ip-proto-17
  337  21:47:50.624479 IP ************* > *************: ip-proto-17
  338  21:47:50.624562 IP ************* > *************: ip-proto-17
  339  21:47:50.625618 IP *************.7000 > *************.7001:  rx data (1472)
  340  21:47:50.625734 IP ************* > *************: ip-proto-17
  341  21:47:50.625753 IP ************* > *************: ip-proto-17
  342  21:47:50.625870 IP *************.7001 > *************.7000:  rx ack first 12 serial 2570 reason delay acked 12 (66)
  343  21:47:50.627406 IP *************.7001 > *************.7000:  rx data fs call fetch-data fid 536977399/30/22 offset 65536 length 26996 (52)
  344  21:47:50.630017 IP *************.7000 > *************.7001:  rx data fs reply fetch-data (1472)
  345  21:47:50.630141 IP ************* > *************: ip-proto-17
  346  21:47:50.630263 IP ************* > *************: ip-proto-17
  347  21:47:50.630347 IP ************* > *************: ip-proto-17
  348  21:47:50.631301 IP *************.7000 > *************.7001:  rx data (1472)
  349  21:47:50.631423 IP ************* > *************: ip-proto-17
  350  21:47:50.631547 IP ************* > *************: ip-proto-17
  351  21:47:50.631630 IP ************* > *************: ip-proto-17
  352  21:47:50.631783 IP *************.7001 > *************.7000:  rx ack first 2 serial 2572 reason ack requested acked 2 (66)
  353  21:47:50.633172 IP *************.7000 > *************.7001:  rx data (1472)
  354  21:47:50.633294 IP ************* > *************: ip-proto-17
  355  21:47:50.633417 IP ************* > *************: ip-proto-17
  356  21:47:50.633500 IP ************* > *************: ip-proto-17
  357  21:47:50.634225 IP *************.7000 > *************.7001:  rx data (1472)
  358  21:47:50.634348 IP ************* > *************: ip-proto-17
  359  21:47:50.634470 IP ************* > *************: ip-proto-17
  360  21:47:50.634554 IP ************* > *************: ip-proto-17
  361  21:47:50.634697 IP *************.7001 > *************.7000:  rx ack first 4 serial 2574 reason ack requested acked 4 (66)
  362  21:47:50.635315 IP *************.7000 > *************.7001:  rx data (1472)
  363  21:47:50.635437 IP ************* > *************: ip-proto-17
  364  21:47:50.635545 IP ************* > *************: ip-proto-17
  365  21:47:50.635555 IP *************.7000 > *************.7001:  rx data (148)
  366  21:47:50.635705 IP *************.7001 > *************.7000:  rx ack first 5 serial 2576 reason delay acked 5-6 (67)
  367  21:47:53.906701 IP *************.7001 > *************.7000:  rx data fs call fetch-status fid 536977399/88/52 (44)
  368  21:47:53.946230 IP *************.7000 > *************.7001:  rx data fs reply fetch-status (148)
  369  21:47:54.163340 IP *************.1799 > ************.7021: UDP, length 32
  370  21:47:54.338581 IP *************.7001 > *************.7000:  rx ack first 2 serial 1 reason delay (65)
  371  21:47:54.799371 IP ************.7021 > *************.1799: UDP, length 61
  372  21:47:55.159236 IP ************.7021 > *************.1799: UDP, length 32
  373  21:47:55.165136 IP *************.1799 > ************.7021: UDP, length 1444
  374  21:47:55.166071 IP ************.7021 > *************.1799: UDP, length 62
  375  21:47:55.166321 IP *************.1799 > ************.7021: UDP, length 1444
  376  21:47:55.166447 IP *************.1799 > ************.7021: UDP, length 1444
  377  21:47:55.199519 IP ************.7021 > *************.1799: UDP, length 62
  378  21:47:55.199686 IP *************.1799 > ************.7021: UDP, length 1444
  379  21:47:55.199809 IP *************.1799 > ************.7021: UDP, length 1444
  380  21:47:55.200825 IP ************.7021 > *************.1799: UDP, length 62
  381  21:47:55.200977 IP *************.1799 > ************.7021: UDP, length 1444
  382  21:47:55.201325 IP *************.1799 > ************.7021: UDP, length 136
  383  21:47:55.202977 IP ************.7021 > *************.1799: UDP, length 62
  384  21:47:55.251632 IP ************.7021 > *************.1799: UDP, length 1472
  385  21:47:55.252731 IP ************.7021 > *************.1799: UDP, length 1472
  386  21:47:55.253147 IP *************.1799 > ************.7021: UDP, length 66
  387  21:47:55.253858 IP ************.7021 > *************.1799: UDP, length 1472
  388  21:47:55.254848 IP ************.7021 > *************.1799: UDP, length 1472
  389  21:47:55.255035 IP *************.1799 > ************.7021: UDP, length 66
  390  21:47:55.255955 IP ************.7021 > *************.1799: UDP, length 1472
  391  21:47:55.257190 IP ************.7021 > *************.1799: UDP, length 32
  392  21:47:55.257491 IP *************.1799 > ************.7021: UDP, length 1444
  393  21:47:55.258405 IP ************.7021 > *************.1799: UDP, length 62
  394  21:47:55.262318 IP *************.1799 > ************.7021: UDP, length 1444
  395  21:47:55.262601 IP *************.1799 > ************.7021: UDP, length 1444
  396  21:47:55.263258 IP ************.7021 > *************.1799: UDP, length 62
  397  21:47:55.263401 IP *************.1799 > ************.7021: UDP, length 1444
  398  21:47:55.263685 IP *************.1799 > ************.7021: UDP, length 1444
  399  21:47:55.264640 IP ************.7021 > *************.1799: UDP, length 62
  400  21:47:55.264850 IP *************.1799 > ************.7021: UDP, length 1444
  401  21:47:55.264965 IP *************.1799 > ************.7021: UDP, length 144
  402  21:47:55.267052 IP ************.7021 > *************.1799: UDP, length 62
  403  21:47:55.796405 IP ************.7021 > *************.1799: UDP, length 61
  404  21:47:57.009474 IP *************.7001 > ************.7000:  rx data fs call give-cbs (112)
  405  21:47:57.010421 IP ************.7000 > *************.7001:  rx data (28)
  406  21:47:57.340299 IP ************.7021 > *************.1799: UDP, length 1444
  407  21:47:57.341607 IP ************.7021 > *************.1799: UDP, length 1444
  408  21:47:57.341937 IP *************.1799 > ************.7021: UDP, length 66
  409  21:47:57.342924 IP ************.7021 > *************.1799: UDP, length 1444
  410  21:47:57.344154 IP ************.7021 > *************.1799: UDP, length 1444
  411  21:47:57.345387 IP ************.7021 > *************.1799: UDP, length 1444
  412  21:47:57.345878 IP *************.1799 > ************.7021: UDP, length 66
  413  21:47:57.346737 IP ************.7021 > *************.1799: UDP, length 1444
  414  21:47:57.346990 IP *************.1799 > ************.7021: UDP, length 66
  415  21:47:57.348062 IP ************.7021 > *************.1799: UDP, length 104
  416  21:47:57.348264 IP *************.1799 > ************.7021: UDP, length 60
  417  21:47:57.408506 IP *************.7001 > ************.7000:  rx ack first 2 serial 56 reason delay (65)
  418  21:47:57.436536 IP ************.7021 > *************.1799: UDP, length 1444
  419  21:47:57.438563 IP ************.7021 > *************.1799: UDP, length 1444
  420  21:47:57.439547 IP *************.1799 > ************.7021: UDP, length 66
  421  21:47:57.440789 IP ************.7021 > *************.1799: UDP, length 1356
  422  21:47:57.441114 IP *************.1799 > ************.7021: UDP, length 60
  423  21:47:57.460401 IP ************.7021 > *************.1799: UDP, length 1444
  424  21:47:57.461517 IP ************.7021 > *************.1799: UDP, length 1444
  425  21:47:57.461928 IP *************.1799 > ************.7021: UDP, length 66
  426  21:47:57.462859 IP ************.7021 > *************.1799: UDP, length 1356
  427  21:47:57.463197 IP *************.1799 > ************.7021: UDP, length 60
  428  21:47:57.474817 IP ************.7021 > *************.1799: UDP, length 1444
  429  21:47:57.475890 IP ************.7021 > *************.1799: UDP, length 1444
  430  21:47:57.476056 IP *************.1799 > ************.7021: UDP, length 66
  431  21:47:57.477328 IP ************.7021 > *************.1799: UDP, length 1356
  432  21:47:57.477777 IP *************.1799 > ************.7021: UDP, length 60
  433  21:47:57.487546 IP ************.7021 > *************.1799: UDP, length 1444
  434  21:47:57.488558 IP ************.7021 > *************.1799: UDP, length 1444
  435  21:47:57.489407 IP *************.1799 > ************.7021: UDP, length 66
  436  21:47:57.489821 IP ************.7021 > *************.1799: UDP, length 1356
  437  21:47:57.490288 IP *************.1799 > ************.7021: UDP, length 60
  438  21:47:57.492785 IP ************.7021 > *************.1799: UDP, length 1444
  439  21:47:57.493778 IP ************.7021 > *************.1799: UDP, length 1444
  440  21:47:57.495046 IP ************.7021 > *************.1799: UDP, length 1356
  441  21:47:57.497159 IP *************.1799 > ************.7021: UDP, length 66
  442  21:47:57.497606 IP *************.1799 > ************.7021: UDP, length 60
  443  21:47:57.514885 IP ************.7021 > *************.1799: UDP, length 1444
  444  21:47:57.515935 IP ************.7021 > *************.1799: UDP, length 1444
  445  21:47:57.516104 IP *************.1799 > ************.7021: UDP, length 66
  446  21:47:57.517280 IP ************.7021 > *************.1799: UDP, length 1356
  447  21:47:57.517812 IP *************.1799 > ************.7021: UDP, length 60
  448  21:47:57.520085 IP ************.7021 > *************.1799: UDP, length 1444
  449  21:47:57.521128 IP ************.7021 > *************.1799: UDP, length 1444
  450  21:47:57.522427 IP ************.7021 > *************.1799: UDP, length 1356
  451  21:47:57.530098 IP *************.1799 > ************.7021: UDP, length 66
  452  21:47:57.530654 IP *************.1799 > ************.7021: UDP, length 60
  453  21:47:57.533186 IP ************.7021 > *************.1799: UDP, length 1444
  454  21:47:57.534230 IP ************.7021 > *************.1799: UDP, length 1444
  455  21:47:57.534487 IP *************.1799 > ************.7021: UDP, length 66
  456  21:47:57.535724 IP ************.7021 > *************.1799: UDP, length 1356
  457  21:47:57.540121 IP *************.1799 > ************.7021: UDP, length 60
  458  21:47:57.542840 IP ************.7021 > *************.1799: UDP, length 1444
  459  21:47:57.544805 IP ************.7021 > *************.1799: UDP, length 1444
  460  21:47:57.545061 IP *************.1799 > ************.7021: UDP, length 66
  461  21:47:57.547074 IP ************.7021 > *************.1799: UDP, length 1356
  462  21:47:57.547384 IP *************.1799 > ************.7021: UDP, length 60
  463  21:47:57.549677 IP ************.7021 > *************.1799: UDP, length 1444
  464  21:47:57.550730 IP ************.7021 > *************.1799: UDP, length 1444
  465  21:47:57.550981 IP *************.1799 > ************.7021: UDP, length 66
  466  21:47:57.552136 IP ************.7021 > *************.1799: UDP, length 1356
  467  21:47:57.552446 IP *************.1799 > ************.7021: UDP, length 60
  468  21:47:57.554703 IP ************.7021 > *************.1799: UDP, length 1444
  469  21:47:57.555704 IP ************.7021 > *************.1799: UDP, length 1444
  470  21:47:57.555872 IP *************.1799 > ************.7021: UDP, length 66
  471  21:47:57.557029 IP ************.7021 > *************.1799: UDP, length 1356
  472  21:47:57.557342 IP *************.1799 > ************.7021: UDP, length 60
  473  21:47:57.559640 IP ************.7021 > *************.1799: UDP, length 1444
  474  21:47:57.560653 IP ************.7021 > *************.1799: UDP, length 1444
  475  21:47:57.560814 IP *************.1799 > ************.7021: UDP, length 66
  476  21:47:57.562026 IP ************.7021 > *************.1799: UDP, length 1356
  477  21:47:57.562466 IP *************.1799 > ************.7021: UDP, length 60
  478  21:47:57.564746 IP ************.7021 > *************.1799: UDP, length 1444
  479  21:47:57.565755 IP ************.7021 > *************.1799: UDP, length 1444
  480  21:47:57.565920 IP *************.1799 > ************.7021: UDP, length 66
  481  21:47:57.567069 IP ************.7021 > *************.1799: UDP, length 1356
  482  21:47:57.567593 IP *************.1799 > ************.7021: UDP, length 60
  483  21:47:57.569928 IP ************.7021 > *************.1799: UDP, length 1444
  484  21:47:57.570928 IP ************.7021 > *************.1799: UDP, length 1444
  485  21:47:57.571095 IP *************.1799 > ************.7021: UDP, length 66
  486  21:47:57.572322 IP ************.7021 > *************.1799: UDP, length 1356
  487  21:47:57.572720 IP *************.1799 > ************.7021: UDP, length 60
  488  21:47:57.575003 IP ************.7021 > *************.1799: UDP, length 1444
  489  21:47:57.576024 IP ************.7021 > *************.1799: UDP, length 1444
  490  21:47:57.576388 IP *************.1799 > ************.7021: UDP, length 66
  491  21:47:57.577373 IP ************.7021 > *************.1799: UDP, length 1356
  492  21:47:57.577724 IP *************.1799 > ************.7021: UDP, length 60
  493  21:47:57.580189 IP ************.7021 > *************.1799: UDP, length 1444
  494  21:47:57.581306 IP ************.7021 > *************.1799: UDP, length 1444
  495  21:47:57.581548 IP *************.1799 > ************.7021: UDP, length 66
  496  21:47:57.582806 IP ************.7021 > *************.1799: UDP, length 1356
  497  21:47:57.583269 IP *************.1799 > ************.7021: UDP, length 60
  498  21:47:57.585922 IP ************.7021 > *************.1799: UDP, length 1444
  499  21:47:57.587914 IP ************.7021 > *************.1799: UDP, length 1444
  500  21:47:57.588147 IP *************.1799 > ************.7021: UDP, length 66
  501  21:47:57.590180 IP ************.7021 > *************.1799: UDP, length 1356
  502  21:47:57.590496 IP *************.1799 > ************.7021: UDP, length 60
  503  21:47:57.593543 IP ************.7021 > *************.1799: UDP, length 1444
  504  21:47:57.594586 IP ************.7021 > *************.1799: UDP, length 1444
  505  21:47:57.594999 IP *************.1799 > ************.7021: UDP, length 66
  506  21:47:57.595945 IP ************.7021 > *************.1799: UDP, length 1356
  507  21:47:57.596253 IP *************.1799 > ************.7021: UDP, length 60
  508  21:47:57.598753 IP ************.7021 > *************.1799: UDP, length 1444
  509  21:47:57.599796 IP ************.7021 > *************.1799: UDP, length 1444
  510  21:47:57.599958 IP *************.1799 > ************.7021: UDP, length 66
  511  21:47:57.601168 IP ************.7021 > *************.1799: UDP, length 1356
  512  21:47:57.601637 IP *************.1799 > ************.7021: UDP, length 60
  513  21:47:57.609736 IP ************.7021 > *************.1799: UDP, length 1444
  514  21:47:57.610744 IP ************.7021 > *************.1799: UDP, length 1444
  515  21:47:57.610914 IP *************.1799 > ************.7021: UDP, length 66
  516  21:47:57.612128 IP ************.7021 > *************.1799: UDP, length 1356
  517  21:47:57.612774 IP *************.1799 > ************.7021: UDP, length 1444
  518  21:47:57.613784 IP ************.7021 > *************.1799: UDP, length 62
  519  21:47:57.613939 IP *************.1799 > ************.7021: UDP, length 1444
  520  21:47:57.614059 IP *************.1799 > ************.7021: UDP, length 1444
  521  21:47:57.615404 IP ************.7021 > *************.1799: UDP, length 62
  522  21:47:57.615552 IP *************.1799 > ************.7021: UDP, length 1444
  523  21:47:57.615674 IP *************.1799 > ************.7021: UDP, length 1444
  524  21:47:57.618644 IP ************.7021 > *************.1799: UDP, length 62
  525  21:47:57.623150 IP ************.7021 > *************.1799: UDP, length 62
  526  21:47:57.623823 IP *************.1799 > ************.7021: UDP, length 1444
  527  21:47:57.624002 IP *************.1799 > ************.7021: UDP, length 144
  528  21:47:57.682626 IP ************.7021 > *************.1799: UDP, length 1444
  529  21:47:57.683198 IP ************.7021 > *************.1799: UDP, length 764
  530  21:47:57.683616 IP *************.1799 > ************.7021: UDP, length 66
  531  21:47:57.683844 IP *************.1799 > ************.7021: UDP, length 60
  532  21:47:57.689047 IP ************.7021 > *************.1799: UDP, length 1444
  533  21:47:57.728006 IP ************.7021 > *************.1799: UDP, length 1444
  534  21:47:57.728199 IP *************.1799 > ************.7021: UDP, length 66
  535  21:47:57.771925 IP ************.7021 > *************.1799: UDP, length 1356
  536  21:47:57.772583 IP *************.1799 > ************.7021: UDP, length 60
  537  21:47:57.776216 IP ************.7021 > *************.1799: UDP, length 1444
  538  21:47:57.778379 IP ************.7021 > *************.1799: UDP, length 1444
  539  21:47:57.780051 IP *************.1799 > ************.7021: UDP, length 66
  540  21:47:57.780898 IP ************.7021 > *************.1799: UDP, length 1356
  541  21:47:57.781374 IP *************.1799 > ************.7021: UDP, length 60
  542  21:47:57.786649 IP ************.7021 > *************.1799: UDP, length 1444
  543  21:47:57.787702 IP ************.7021 > *************.1799: UDP, length 1444
  544  21:47:57.788103 IP *************.1799 > ************.7021: UDP, length 66
  545  21:47:57.789230 IP ************.7021 > *************.1799: UDP, length 1356
  546  21:47:57.789699 IP *************.1799 > ************.7021: UDP, length 60
  547  21:47:57.792483 IP ************.7021 > *************.1799: UDP, length 1444
  548  21:47:57.794457 IP ************.7021 > *************.1799: UDP, length 1444
  549  21:47:57.794696 IP *************.1799 > ************.7021: UDP, length 66
  550  21:47:57.796695 IP ************.7021 > *************.1799: UDP, length 1356
  551  21:47:57.797247 IP *************.1799 > ************.7021: UDP, length 60
  552  21:47:57.800461 IP ************.7021 > *************.1799: UDP, length 1444
  553  21:47:57.802376 IP ************.7021 > *************.1799: UDP, length 1444
  554  21:47:57.802546 IP *************.1799 > ************.7021: UDP, length 66
  555  21:47:57.803728 IP ************.7021 > *************.1799: UDP, length 1356
  556  21:47:58.221671 IP ************.7021 > *************.1799: UDP, length 1356
  557  21:47:58.221744 IP ************* > ************: ICMP ************* udp port 1799 unreachable, length 556
  558  21:47:58.501236 IP ************.7003 > *************.1792:  rx data vldb reply get-entry-by-name "root.cell" numservers 6 servers ************* ************ ************* ************ ************ ************ partitions a a a a a a rwvol 536870915 rovol 536870916 backup 536870917 (412)
  559  21:47:58.501301 IP ************* > ************: ICMP ************* udp port 1792 unreachable, length 448
  560  21:47:59.291588 IP ************.7021 > *************.1799: UDP, length 1356
  561  21:47:59.291652 IP ************* > ************: ICMP ************* udp port 1799 unreachable, length 556
  562  21:48:00.871744 IP ************.7021 > *************.1799: UDP, length 1356
  563  21:48:00.871814 IP ************* > ************: ICMP ************* udp port 1799 unreachable, length 556
  564  21:48:03.249685 IP *************.1799 > ************.88:  v5
  565  21:48:03.255469 IP ************.88 > *************.1799:  v5
  566  21:48:03.283149 IP *************.7001 > ************.7000:  rx data fs call fetch-status fid 536870913/4/3 (44)
  567  21:48:03.284549 IP ************.7000 > *************.7001:  rx data fs reply fetch-status (148)
  568  21:48:03.377621 IP *************.1799 > ************.88: 
  569  21:48:03.410404 IP ************.88 > *************.1799: 
  570  21:48:03.413361 IP *************.1799 > ************.4444: UDP, length 209
  571  21:48:03.413986 IP ************ > *************: ICMP ************ udp port 4444 unreachable, length 92
  572  21:48:03.414378 IP *************.1799 > ************.4444: UDP, length 209
  573  21:48:03.481783 IP ************.7021 > *************.1799: UDP, length 1356
  574  21:48:03.481851 IP ************* > ************: ICMP ************* udp port 1799 unreachable, length 556
  575  21:48:03.678443 IP *************.7001 > ************.7000:  rx ack first 2 serial 1 reason delay (65)
  576  21:48:04.409193 IP *************.1799 > ************.4444: UDP, length 209
  577  21:48:04.409495 IP ************ > *************: ICMP ************ udp port 4444 unreachable, length 92
  578  21:48:04.409893 IP *************.1799 > *************.4444: UDP, length 209
  579  21:48:04.414101 IP *************.4444 > *************.1799: UDP, length 1266
  580  21:48:04.426446 IP *************.1799 > *************.7002:  rx data pt call name-to-id "nneul" (292)
  581  21:48:04.449366 IP *************.7002 > *************.1799:  rx data pt reply name-to-id ids: 5879 (36)
  582  21:48:06.833046 IP *************.7002 > *************.1799:  rx data pt reply name-to-id ids: 5879 (36)
  583  21:48:06.833100 IP ************* > *************: ICMP ************* udp port 1799 unreachable, length 72
  584  21:48:08.131961 IP ************.7021 > *************.1799: UDP, length 1356
  585  21:48:08.132033 IP ************* > ************: ICMP ************* udp port 1799 unreachable, length 556
  586  21:48:09.492664 IP *************.7002 > *************.1799:  rx data pt reply name-to-id ids: 5879 (36)
  587  21:48:09.492716 IP ************* > *************: ICMP ************* udp port 1799 unreachable, length 72
  588  21:48:09.661704 IP ************.7003 > *************.1792:  rx data vldb reply get-entry-by-name "root.cell" numservers 6 servers ************* ************ ************* ************ ************ ************ partitions a a a a a a rwvol 536870915 rovol 536870916 backup 536870917 (412)
  589  21:48:09.661762 IP ************* > ************: ICMP ************* udp port 1792 unreachable, length 448
  590  21:48:12.662982 IP *************.7002 > *************.1799:  rx data pt reply name-to-id ids: 5879 (36)
  591  21:48:12.663034 IP ************* > *************: ICMP ************* udp port 1799 unreachable, length 72
  592  21:48:16.863261 IP *************.7002 > *************.1799:  rx data pt reply name-to-id ids: 5879 (36)
  593  21:48:16.863314 IP ************* > *************: ICMP ************* udp port 1799 unreachable, length 72
  594  21:48:16.882406 IP ************.7021 > *************.1799: UDP, length 1356
  595  21:48:16.882456 IP ************* > ************: ICMP ************* udp port 1799 unreachable, length 556
  596  21:48:21.072280 IP ************.7003 > *************.1792:  rx data vldb reply get-entry-by-name "root.cell" numservers 6 servers ************* ************ ************* ************ ************ ************ partitions a a a a a a rwvol 536870915 rovol 536870916 backup 536870917 (412)
  597  21:48:21.072337 IP ************* > ************: ICMP ************* udp port 1792 unreachable, length 448
  598  21:48:23.103590 IP *************.7002 > *************.1799:  rx data pt reply name-to-id ids: 5879 (36)
  599  21:48:23.103644 IP ************* > *************: ICMP ************* udp port 1799 unreachable, length 72
  600  21:48:25.892793 IP ************.7021 > *************.1799: UDP, length 1356
  601  21:48:25.892866 IP ************* > ************: ICMP ************* udp port 1799 unreachable, length 556
