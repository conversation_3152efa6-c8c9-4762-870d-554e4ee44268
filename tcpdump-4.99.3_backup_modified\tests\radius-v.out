    1  22:52:17.872968 IP (tos 0x0, ttl 255, id 70, offset 0, flags [none], proto UDP (17), length 167)
    ********.1645 > ********00.1812: RADIUS, length: 139
	Access-Request (1), id: 0x05, Authenticator: ecfe3d2fe4473ec6299095ee46aedf77
	  NAS-IP-Address Attribute (4), length: 6, Value: ********
	  NAS-Port Attribute (5), length: 6, Value: 50012
	  NAS-Port-Type Attribute (61), length: 6, Value: Ethernet
	  User-Name Attribute (1), length: 14, Value: <PERSON><PERSON>McGuirk
	  Called-Station-Id Attribute (30), length: 19, Value: 00-19-06-EA-B8-8C
	  Calling-Station-Id Attribute (31), length: 19, Value: 00-14-22-E9-54-5E
	  Service-Type Attribute (6), length: 6, Value: Framed
	  Framed-MTU Attribute (12), length: 6, Value: 1500
	  EAP-Message Attribute (79), length: 19, Value: Response (2), id 0, len 17
		 Type Identity (1), Identity: <PERSON><PERSON>McGuirk
	  Message-Authenticator Attribute (80), length: 18, Value: (....$..p.Q1o.x.
    2  22:52:17.875771 IP (tos 0x0, ttl 64, id 0, offset 0, flags [DF], proto UDP (17), length 137)
    ********00.1812 > ********.1645: RADIUS, length: 109
	Access-Challenge (11), id: 0x05, Authenticator: f050649184625d36f14c9075b7a48b83
	  Framed-IP-Address Attribute (8), length: 6, Value: NAS Select
	  Framed-MTU Attribute (12), length: 6, Value: 576
	  Service-Type Attribute (6), length: 6, Value: Framed
	  Reply-Message Attribute (18), length: 11, Value: Hello, %u
	  EAP-Message Attribute (79), length: 24, Value: Request (1), id 1, len 22
		 Type MD5-challenge (4)
	  Message-Authenticator Attribute (80), length: 18, Value: ...<.(.X.13..t4.
	  State Attribute (24), length: 18, Value: ..../.0$.s..1..w
    3  22:52:17.916736 IP (tos 0x0, ttl 255, id 71, offset 0, flags [none], proto UDP (17), length 202)
    ********.1645 > ********00.1812: RADIUS, length: 174
	Access-Request (1), id: 0x06, Authenticator: 6a6f38e6dae830304d2333e5d5364643
	  NAS-IP-Address Attribute (4), length: 6, Value: ********
	  NAS-Port Attribute (5), length: 6, Value: 50012
	  NAS-Port-Type Attribute (61), length: 6, Value: Ethernet
	  User-Name Attribute (1), length: 14, Value: John.McGuirk
	  Called-Station-Id Attribute (30), length: 19, Value: 00-19-06-EA-B8-8C
	  Calling-Station-Id Attribute (31), length: 19, Value: 00-14-22-E9-54-5E
	  Service-Type Attribute (6), length: 6, Value: Framed
	  Framed-MTU Attribute (12), length: 6, Value: 1500
	  State Attribute (24), length: 18, Value: ..../.0$.s..1..w
	  EAP-Message Attribute (79), length: 36, Value: Response (2), id 1, len 34
		 Type MD5-challenge (4)
	  Message-Authenticator Attribute (80), length: 18, Value: '&.q1.....Ojb..8
    4  22:52:17.916850 IP (tos 0x0, ttl 64, id 0, offset 0, flags [DF], proto UDP (17), length 125)
    ********00.1812 > ********.1645: RADIUS, length: 97
	Access-Accept (2), id: 0x06, Authenticator: fbba6a784c7decb314caf0f27944a37b
	  Framed-IP-Address Attribute (8), length: 6, Value: NAS Select
	  Framed-MTU Attribute (12), length: 6, Value: 576
	  Service-Type Attribute (6), length: 6, Value: Framed
	  Reply-Message Attribute (18), length: 21, Value: Hello, John.McGuirk
	  EAP-Message Attribute (79), length: 6, Value: Success (3), id 1, len 4
	  Message-Authenticator Attribute (80), length: 18, Value: ...b...2.^..NLc`
	  User-Name Attribute (1), length: 14, Value: John.McGuirk
