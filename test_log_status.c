#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

// 检测MySQL日志配置状态
static const char* check_mysql_log_status(const char* variant) {
    FILE* fp;
    char line[256];
    char command[256];
    
    printf("检测 %s 的日志状态...\n", variant);
    
    // 检查systemd journal是否有MySQL日志
    if (strcmp(variant, "MariaDB") == 0) {
        snprintf(command, sizeof(command), "journalctl -u mariadb --since '24 hours ago' 2>/dev/null | wc -l");
    } else {
        snprintf(command, sizeof(command), "journalctl -u mysql --since '24 hours ago' 2>/dev/null | wc -l");
    }
    
    printf("执行命令: %s\n", command);
    
    fp = popen(command, "r");
    if (fp != NULL) {
        if (fgets(line, sizeof(line), fp) != NULL) {
            int log_lines = atoi(line);
            pclose(fp);
            
            printf("日志行数: %d\n", log_lines);
            
            if (log_lines > 100) {
                return "journal日志正常";
            } else if (log_lines > 0) {
                return "journal日志较少";
            } else {
                return "journal无日志记录";
            }
        }
        pclose(fp);
    }
    
    printf("journal检测失败，检查传统日志文件...\n");
    
    // 检查传统日志文件
    const char* log_files[] = {
        "/var/log/mysql/error.log",
        "/var/log/mariadb/mariadb.log",
        "/var/log/mysqld.log",
        NULL
    };
    
    for (int i = 0; log_files[i] != NULL; i++) {
        printf("检查日志文件: %s\n", log_files[i]);
        if (access(log_files[i], R_OK) == 0) {
            return "使用文件日志";
        }
    }
    
    return "未找到日志配置";
}

int main() {
    printf("=== MySQL日志状态检测测试 ===\n\n");
    
    const char* status = check_mysql_log_status("MariaDB");
    printf("\n结果: %s\n", status);
    
    return 0;
}
