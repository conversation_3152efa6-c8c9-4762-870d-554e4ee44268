    1  2025-05-10 13:17:29.141151 be 00 ed 23: Request who-has 10.80.131.254 tell 10.80.131.1, length 18
    2  2025-05-10 13:17:29.141208 50 be 00 23: Reply 10.80.131.254 is-at 50, length 18
    3  2025-05-10 13:17:29.142411 be 50 ab 89: 10.80.131.1 > 10.80.131.254: ICMP echo request, id 58112, seq 0, length 64
    4  2025-05-10 13:17:29.142586 50 be ed 89: 10.80.131.254 > 10.80.131.1: ICMP echo reply, id 58112, seq 0, length 64
    5  2025-05-10 13:17:30.138392 be 50 ab 89: 10.80.131.1 > 10.80.131.254: ICMP echo request, id 58112, seq 256, length 64
    6  2025-05-10 13:17:30.138478 50 be ab 89: 10.80.131.254 > 10.80.131.1: ICMP echo reply, id 58112, seq 256, length 64
    7  2025-05-10 13:17:33.388386 be 50 c6 62: 10.80.131.1.1024 > 10.80.3.254.53: 54328+ A? tcpdump.org. (29)
    8  2025-05-10 13:17:33.580847 50 be 06 78: 10.80.3.254.53 > 10.80.131.1.1024: 54328 1/0/0 A 146.190.240.163 (45)
    9  2025-05-10 13:17:33.583942 be 50 b5 79: 10.80.131.1.1024 > 10.80.3.254.53: 54329+ PTR? 163.240.190.146.in-addr.arpa. (46)
   10  2025-05-10 13:17:33.625941 50 be 06 115: 10.80.3.254.53 > 10.80.131.1.1024: 54329 1/0/0 PTR flagpole-4.tcpdump.org. (82)
   11  2025-05-10 13:17:33.628611 be 50 c3 65: 10.80.131.1.1026 > 146.190.240.163.80: Flags [S], seq 2399122329, win 1872, options [mss 468,sackOK,TS val 287949 ecr 0,nop,wscale 0], length 0
   12  2025-05-10 13:17:33.672113 50 be 06 65: 146.190.240.163.80 > 10.80.131.1.1026: Flags [S.], seq 3394579464, ack 2399122330, win 14600, options [mss 1320,nop,wscale 12,sackOK,TS val 1078823769 ecr 287949], length 0
   13  2025-05-10 13:17:33.673539 be 50 cb 57: 10.80.131.1.1026 > 146.190.240.163.80: Flags [.], ack 1, win 1872, options [nop,nop,TS val 287954 ecr 1078823769], length 0
   14  2025-05-10 13:17:33.676852 be 50 09 251: 10.80.131.1.1026 > 146.190.240.163.80: Flags [P.], seq 1:195, ack 1, win 1872, options [nop,nop,TS val 287954 ecr 1078823769], length 194: HTTP: GET / HTTP/1.1
   15  2025-05-10 13:17:33.713912 50 be 06 57: 146.190.240.163.80 > 10.80.131.1.1026: Flags [.], ack 1, win 1024, options [nop,nop,TS val 1078823809 ecr 287954], length 0
   16  2025-05-10 13:17:33.737808 50 be 06 57: 146.190.240.163.80 > 10.80.131.1.1026: Flags [.], ack 195, win 1024, options [nop,nop,TS val 1078823832 ecr 287954], length 0
   17  2025-05-10 13:17:33.881496 50 be 06 242: 146.190.240.163.80 > 10.80.131.1.1026: Flags [P.], seq 1:186, ack 195, win 1024, options [nop,nop,TS val 1078823978 ecr 287954], length 185: HTTP: HTTP/1.1 301 Moved Permanently
   18  2025-05-10 13:17:33.883099 50 be 06 285: 146.190.240.163.80 > 10.80.131.1.1026: Flags [P.], seq 186:414, ack 195, win 1024, options [nop,nop,TS val 1078823978 ecr 287954], length 228: HTTP
   19  2025-05-10 13:17:33.885423 be 50 cb 57: 10.80.131.1.1026 > 146.190.240.163.80: Flags [.], ack 186, win 2736, options [nop,nop,TS val 287975 ecr 1078823978], length 0
   20  2025-05-10 13:17:33.886121 be 50 cb 57: 10.80.131.1.1026 > 146.190.240.163.80: Flags [.], ack 414, win 3648, options [nop,nop,TS val 287975 ecr 1078823978], length 0
   21  2025-05-10 13:17:33.888015 be 50 cb 57: 10.80.131.1.1026 > 146.190.240.163.80: Flags [F.], seq 195, ack 414, win 3648, options [nop,nop,TS val 287975 ecr 1078823978], length 0
   22  2025-05-10 13:17:33.924571 50 be 06 57: 146.190.240.163.80 > 10.80.131.1.1026: Flags [.], ack 196, win 1024, options [nop,nop,TS val 1078824024 ecr 287975], length 0
   23  2025-05-10 13:17:33.925800 50 be 06 57: 146.190.240.163.80 > 10.80.131.1.1026: Flags [F.], seq 414, ack 196, win 1024, options [nop,nop,TS val 1078824024 ecr 287975], length 0
   24  2025-05-10 13:17:33.927071 be 50 cb 57: 10.80.131.1.1026 > 146.190.240.163.80: Flags [.], ack 415, win 3648, options [nop,nop,TS val 287979 ecr 1078824024], length 0
   25  2025-05-10 13:17:34.140710 50 be 00 23: Request who-has 10.80.131.1 tell 10.80.131.254, length 18
   26  2025-05-10 13:17:34.141487 be 50 ed 23: Reply 10.80.131.1 is-at be, length 18
