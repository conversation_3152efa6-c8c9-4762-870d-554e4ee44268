info.log:     if (!hcreate_r(n + (n / 4), &info->hashtab))
info.log:   if (!hsearch_r(e, ENTER, &ep, &info->hashtab)) return 1;
meminfo.c:    while (info->extents) {
meminfo.c:        struct stacks_extent *p = info->extents;
meminfo.c:        info->extents = info->extents->next;
meminfo.c: #define htVAL(f) e.key = STRINGIFY(f); e.data = &info->hist.new. f; \
meminfo.c:  if (!hsearch_r(e, ENTER, &ep, &info->hashtab)) return 1;
meminfo.c: #define htXTRA(k,f) e.key = STRINGIFY(k); e.data = &info->hist.new. f; \
meminfo.c:  if (!hsearch_r(e, ENTER, &ep, &info->hashtab)) return 1;
meminfo.c:    if (!hcreate_r(n + (n / 4), &info->hashtab))
meminfo.c: #define mHr(f) info->hist.new. f
meminfo.c:    memcpy(&info->hist.old, &info->hist.new, sizeof(struct meminfo_data));
meminfo.c:    memset(&info->hist.new, 0, sizeof(struct meminfo_data));
meminfo.c:    if (-1 == info->meminfo_fd
meminfo.c:    && (-1 == (info->meminfo_fd = open(MEMINFO_FILE, O_RDONLY))))
meminfo.c:    if (lseek(info->meminfo_fd, 0L, SEEK_SET) == -1)
meminfo.c:        if ((size = read(info->meminfo_fd, buf, sizeof(buf)-1)) < 0) {
meminfo.c:        if (hsearch_r(e, FIND, &ep, &info->hashtab))
meminfo.c:    list_size  = sizeof(struct meminfo_result)*info->numitems;  // any single results stack |
meminfo.c:    p_blob->next = info->extents;                               // push this extent onto... |
meminfo.c:    info->extents = p_blob;                                     // ...some existing extents |
meminfo.c:        p_head->head = meminfo_itemize_stack((struct meminfo_result *)v_list, info->numitems, info->items);
meminfo.c:    info->refcount++;
meminfo.c:    return info->refcount;
meminfo.c:    if (1 <= cur_secs - info->sav_secs) {
meminfo.c:        info->sav_secs = cur_secs;
meminfo.c:    info->get_this.item = item;
meminfo.c:    info->get_this.result.ul_int = 0;
meminfo.c:    Item_table[item].setsfunc(&info->get_this, &info->hist);
meminfo.c:    return &info->get_this;
meminfo.c:    if (info->numitems != numitems + 1
meminfo.c:    || memcmp(info->items, items, sizeof(enum meminfo_item) * numitems)) {
meminfo.c:        if (!(info->items = realloc(info->items, sizeof(enum meminfo_item) * (numitems + 1))))
meminfo.c:        memcpy(info->items, items, sizeof(enum meminfo_item) * numitems);
meminfo.c:        info->items[numitems] = MEMINFO_logical_end;
meminfo.c:        info->numitems = numitems + 1;
meminfo.c:        if (info->extents)
meminfo.c:    if (!info->extents
meminfo.c:    meminfo_assign_results(info->extents->stacks[0], &info->hist);
meminfo.c:    return info->extents->stacks[0];
pids.bak:#define Hr(x)  info->hist->x           // 'hist ref', minimize stolen impact
pids.bak:    int slot = info->hist->num_tasks;
pids.bak:    info->hist->num_tasks++;
pids.bak:    int slot = info->hist->num_tasks1;
pids.bak:    info->hist->num_tasks1++;
pids.bak:    info->hist->num_tasks = 0;
pids.bak:    info->hist->num_tasks1 = 0;
pids.bak:        , info->hist->num_tasks
pids.bak:    SET_t *that = &info->func_array[0];
pids.bak:    info->seterr = 0;
pids.bak:    return !info->seterr;
pids.bak:    struct stacks_extent *ext = info->extents;
pids.bak:    struct stacks_extent *p = info->extents;
pids.bak:            info->extents = p->next;
pids.bak:    struct stacks_extent *ext = info->extents;
pids.bak:            pids_itemize_stack(ext->stacks[i]->head, info->maxitems, info->items);
pids.bak:    info->oldflags = info->history_yes = 0;
pids.bak:    for (i = 0; i < info->maxitems; i++) {
pids.bak:        if (((e = info->items[i])) >= PIDS_logical_end)
pids.bak:        info->oldflags |= Item_table[e].oldflags;
pids.bak:        info->history_yes |= Item_table[e].needhist;
pids.bak:    if (info->oldflags & f_either) {
pids.bak:        if (!(info->oldflags & (f_stat | f_status)))
pids.bak:            info->oldflags |= f_stat;
pids.bak:    if (!(info->func_array = realloc(info->func_array, sizeof(SET_t) * info->maxitems)))
pids.bak:    for (i = 0; i < info->maxitems -1; i++){
pids.bak:        info->func_array[i] = Item_table[info->items[i]].setsfunc;
pids.bak:                info->func_array[i] = (SET_t) set_pids_ID_TGID;
pids.bak:    info->func_array[i] = NULL;
pids.bak:    if (info->history_yes)
pids.bak:    if (info->history_yes)
pids.bak:    list_size  = sizeof(struct pids_result) * info->maxitems;  // any single results stack |
pids.bak:    p_blob->next = info->extents;                              // push this extent onto... |
pids.bak:    info->extents = p_blob;                                    // ...some existing extents |
pids.bak:        p_head->head = pids_itemize_stack((struct pids_result *)v_list, info->maxitems, info->items);
pids.bak: #define n_alloc  info->fetch.n_alloc
pids.bak: #define n_inuse  info->fetch.n_inuse
pids.bak: #define n_saved  info->fetch.n_alloc_save
pids.bak:    if (!info->fetch.anchor) {
pids.bak:        if (!(info->fetch.anchor = calloc(STACKS_INIT, sizeof(void *))))
pids.bak:        memcpy(info->fetch.anchor, ext->stacks, sizeof(void *) * STACKS_INIT);
pids.bak:    memset(&info->fetch.counts, 0, sizeof(struct pids_counts));
pids.bak:    while (info->read_something(info->fetch_PT, &info->fetch_proc)) {
pids.bak:            if (!(info->fetch.anchor = realloc(info->fetch.anchor, sizeof(void *) * n_alloc))
pids.bak:            memcpy(info->fetch.anchor + n_inuse, ext->stacks, sizeof(void *) * STACKS_GROW);
pids.bak:        if (!pids_proc_tally(info, &info->fetch.counts, &info->fetch_proc))
pids.bak:        if (!pids_assign_results(info, info->fetch.anchor[n_inuse++], &info->fetch_proc))
pids.bak:        if (!(info->fetch.results.stacks = realloc(info->fetch.results.stacks, sizeof(void *) * n_saved)))
pids.bak:    memcpy(info->fetch.results.stacks, info->fetch.anchor, sizeof(void *) * n_inuse);
pids.bak:    info->fetch.results.stacks[n_inuse] = NULL;
pids.bak: #define n_alloc  info->fetch.n_alloc
pids.bak: #define n_inuse  info->fetch.n_inuse
pids.bak: #define n_saved  info->fetch.n_alloc_save
pids.bak:    if (!info->fetch.anchor) {
pids.bak:        if (!(info->fetch.anchor = calloc(STACKS_INIT, sizeof(void *))))
pids.bak:        memcpy(info->fetch.anchor, ext->stacks, sizeof(void *) * STACKS_INIT);
pids.bak:    memset(&info->fetch.counts, 0, sizeof(struct pids_counts));
pids.bak:    while (info->read_something(info->fetch_PT, &info->fetch_proc)) {
pids.bak:            if (!(info->fetch.anchor = realloc(info->fetch.anchor, sizeof(void *) * n_alloc))
pids.bak:            memcpy(info->fetch.anchor + n_inuse, ext->stacks, sizeof(void *) * STACKS_GROW);
pids.bak:        if (!pids_proc_tally1(info, &info->fetch.counts, &info->fetch_proc))
pids.bak:        if (!pids_assign_results(info, info->fetch.anchor[n_inuse++], &info->fetch_proc))
pids.bak:        if (!(info->fetch.results.stacks = realloc(info->fetch.results.stacks, sizeof(void *) * n_saved)))
pids.bak:    memcpy(info->fetch.results.stacks, info->fetch.anchor, sizeof(void *) * n_inuse);
pids.bak:    info->fetch.results.stacks[n_inuse] = NULL;
pids.bak:    info->refcount++;
pids.bak:    return info->refcount;
pids.bak:    if (!info->maxitems)
pids.bak:    if (!info->get_ext) {
pids.bak:        if (!(info->get_ext = pids_stacks_alloc(info, 1)))
pids.bak:        if (!pids_oldproc_open(&info->get_PT, info->oldflags))
pids.bak:        info->get_type = which;
pids.bak:        info->read_something = which ? readeither : readproc;
pids.bak:    if (info->get_type != which) {
pids.bak:        pids_oldproc_close(&info->get_PT);
pids.bak:    info->boot_tics = 0;
pids.bak:        info->boot_tics = up_secs * info->hertz;
pids.bak:    if (NULL == info->read_something(info->get_PT, &info->get_proc))
pids.bak:    if (!pids_assign_results(info, info->get_ext->stacks[0], &info->get_proc))
pids.bak:    return info->get_ext->stacks[0];
pids.bak:    if (!info->maxitems)
pids.bak:    if (!pids_oldproc_open(&info->fetch_PT, info->oldflags))
pids.bak:    info->read_something = which ? readeither : readproc;
pids.bak:    info->boot_tics = 0;
pids.bak:        info->boot_tics = up_secs * info->hertz;
pids.bak:    pids_oldproc_close(&info->fetch_PT);
pids.bak:    return (rc > 0) ? &info->fetch.results : NULL;
pids.bak:    if (!info->maxitems)
pids.bak:    if (!pids_oldproc_open(&info->fetch_PT, info->oldflags))
pids.bak:    info->read_something = which ? readeither : readproc;
pids.bak:    info->boot_tics = 0;
pids.bak:        info->boot_tics = up_secs * info->hertz;
pids.bak:    pids_oldproc_close(&info->fetch_PT);
pids.bak:    return (rc > 0) ? &info->fetch.results : NULL;
pids.bak:    if (info->maxitems == newnumitems + 1
pids.bak:    && !memcmp(info->items, newitems, sizeof(enum pids_item) * newnumitems))
pids.bak:    if (info->maxitems < newnumitems + 1) {
pids.bak:        while (info->extents) {
pids.bak:            struct stacks_extent *p = info->extents;
pids.bak:            info->extents = p->next;
pids.bak:        if (info->get_ext) {
pids.bak:           pids_oldproc_close(&info->get_PT);
pids.bak:           info->get_ext = NULL;
pids.bak:        if (info->fetch.anchor) {
pids.bak:            free(info->fetch.anchor);
pids.bak:            info->fetch.anchor = NULL;
pids.bak:        info->maxitems = newnumitems + 1;
pids.bak:        if (!(info->items = realloc(info->items, sizeof(enum pids_item) * info->maxitems)))
pids.bak:    memcpy(info->items, newitems, sizeof(enum pids_item) * newnumitems);
pids.bak:    info->items[newnumitems] = PIDS_logical_end;
pids.bak:    info->maxitems = newnumitems + 1;
pids.bak:    if (!info->maxitems)
pids.bak:    if (!pids_oldproc_open(&info->fetch_PT, (info->oldflags | which), ids, numthese))
pids.bak:    info->read_something = (which & PIDS_FETCH_THREADS_TOO) ? readeither : readproc;
pids.bak:    info->boot_tics = 0;
pids.bak:        info->boot_tics = up_secs * info->hertz;
pids.bak:    pids_oldproc_close(&info->fetch_PT);
pids.bak:    return (rc >= 0) ? &info->fetch.results : NULL;
pids.bak:        if (offset >= info->maxitems)
pids.c:#define Hr(x)  info->hist->x           // 'hist ref', minimize stolen impact
pids.c:    int slot = info->hist->num_tasks;
pids.c:    info->hist->num_tasks++;
pids.c:    int slot = info->hist->num_tasks1;
pids.c:    info->hist->num_tasks1++;
pids.c:    info->hist->num_tasks = 0;
pids.c:    info->hist->num_tasks1 = 0;
pids.c:        , info->hist->num_tasks
pids.c:    SET_t *that = &info->func_array[0];
pids.c:    info->seterr = 0;
pids.c:    return !info->seterr;
pids.c:    struct stacks_extent *ext = info->extents;
pids.c:    struct stacks_extent *p = info->extents;
pids.c:            info->extents = p->next;
pids.c:    struct stacks_extent *ext = info->extents;
pids.c:            pids_itemize_stack(ext->stacks[i]->head, info->maxitems, info->items);
pids.c:    info->oldflags = info->history_yes = 0;
pids.c:    for (i = 0; i < info->maxitems; i++) {
pids.c:        if (((e = info->items[i])) >= PIDS_logical_end)
pids.c:        info->oldflags |= Item_table[e].oldflags;
pids.c:        info->history_yes |= Item_table[e].needhist;
pids.c:    if (info->oldflags & f_either) {
pids.c:        if (!(info->oldflags & (f_stat | f_status)))
pids.c:            info->oldflags |= f_stat;
pids.c:    if (!(info->func_array = realloc(info->func_array, sizeof(SET_t) * info->maxitems)))
pids.c:    for (i = 0; i < info->maxitems -1; i++){
pids.c:        info->func_array[i] = Item_table[info->items[i]].setsfunc;
pids.c:	 if ( i == 0 ) info->func_array[i] = (SET_t) set_pids_ID_PID;
pids.c:	 //if ( i == 1 ) info->func_array[i] = (SET_t) set_pids_ID_PPID;
pids.c:	 if ( i == 3 ) info->func_array[i] = (SET_t) set_pids_ID_EUSER;
pids.c:	 if ( i == 14 ) info->func_array[i] = (SET_t) set_pids_PRIORITY;
pids.c:	 if ( i == 15 ) info->func_array[i] = (SET_t) set_pids_NICE;
pids.c:	 if ( i == 22 ) info->func_array[i] = (SET_t) set_pids_MEM_VIRT;
pids.c:	 if ( i == 24 ) info->func_array[i] = (SET_t) set_pids_MEM_RES;
pids.c:	 if ( i == 27 ) info->func_array[i] = (SET_t) set_pids_MEM_SHR;
pids.c:	 if ( i == 31 ) info->func_array[i] = (SET_t) set_pids_STATE;
pids.c:	 if ( i == 18 ) info->func_array[i] = (SET_t) set_pids_TICS_ALL_DELTA;
pids.c:	 if ( i == 20 ) info->func_array[i] = (SET_t) set_pids_TICS_ALL;
pids.c:	 if ( i == 32 ) info->func_array[i] = (SET_t) set_pids_CMD;
pids.c:	 if ( i == 16 ) info->func_array[i] = (SET_t) set_pids_NLWP;
pids.c:                info->func_array[i] = (SET_t) set_pids_ID_TGID;
pids.c:    info->func_array[i] = NULL;
pids.c:    info->history_yes=1;
pids.c:    if (info->history_yes)
pids.c:    info->history_yes=1;
pids.c:    if (info->history_yes)
pids.c:    list_size  = sizeof(struct pids_result) * info->maxitems;  // any single results stack |
pids.c:    p_blob->next = info->extents;                              // push this extent onto... |
pids.c:    info->extents = p_blob;                                    // ...some existing extents |
pids.c:        p_head->head = pids_itemize_stack((struct pids_result *)v_list, info->maxitems, info->items);
pids.c: #define n_alloc  info->fetch.n_alloc
pids.c: #define n_inuse  info->fetch.n_inuse
pids.c: #define n_saved  info->fetch.n_alloc_save
pids.c:    if (!info->fetch.anchor) {
pids.c:        if (!(info->fetch.anchor = calloc(STACKS_INIT, sizeof(void *))))
pids.c:        memcpy(info->fetch.anchor, ext->stacks, sizeof(void *) * STACKS_INIT);
pids.c:    memset(&info->fetch.counts, 0, sizeof(struct pids_counts));
pids.c:    while (info->read_something(info->fetch_PT, &info->fetch_proc)) {
pids.c:            if (!(info->fetch.anchor = realloc(info->fetch.anchor, sizeof(void *) * n_alloc))
pids.c:            memcpy(info->fetch.anchor + n_inuse, ext->stacks, sizeof(void *) * STACKS_GROW);
pids.c:        if (!pids_proc_tally(info, &info->fetch.counts, &info->fetch_proc))
pids.c:        if (!pids_assign_results(info, info->fetch.anchor[n_inuse++], &info->fetch_proc))
pids.c:        if (!(info->fetch.results.stacks = realloc(info->fetch.results.stacks, sizeof(void *) * n_saved)))
pids.c:    memcpy(info->fetch.results.stacks, info->fetch.anchor, sizeof(void *) * n_inuse);
pids.c:    info->fetch.results.stacks[n_inuse] = NULL;
pids.c: #define n_alloc  info->fetch.n_alloc
pids.c: #define n_inuse  info->fetch.n_inuse
pids.c: #define n_saved  info->fetch.n_alloc_save
pids.c:    if (!info->fetch.anchor) {
pids.c:        if (!(info->fetch.anchor = calloc(STACKS_INIT, sizeof(void *))))
pids.c:        memcpy(info->fetch.anchor, ext->stacks, sizeof(void *) * STACKS_INIT);
pids.c:    memset(&info->fetch.counts, 0, sizeof(struct pids_counts));
pids.c:    while (info->read_something(info->fetch_PT, &info->fetch_proc)) {
pids.c:            if (!(info->fetch.anchor = realloc(info->fetch.anchor, sizeof(void *) * n_alloc))
pids.c:            memcpy(info->fetch.anchor + n_inuse, ext->stacks, sizeof(void *) * STACKS_GROW);
pids.c:        if (!pids_proc_tally1(info, &info->fetch.counts, &info->fetch_proc))
pids.c:        if (!pids_assign_results(info, info->fetch.anchor[n_inuse++], &info->fetch_proc))
pids.c:        if (!(info->fetch.results.stacks = realloc(info->fetch.results.stacks, sizeof(void *) * n_saved)))
pids.c:    memcpy(info->fetch.results.stacks, info->fetch.anchor, sizeof(void *) * n_inuse);
pids.c:    info->fetch.results.stacks[n_inuse] = NULL;
pids.c:    info->refcount++;
pids.c:    return info->refcount;
pids.c:    if (!info->maxitems)
pids.c:    if (!info->get_ext) {
pids.c:        if (!(info->get_ext = pids_stacks_alloc(info, 1)))
pids.c:        if (!pids_oldproc_open(&info->get_PT, info->oldflags))
pids.c:        info->get_type = which;
pids.c:        info->read_something = which ? readeither : readproc;
pids.c:    if (info->get_type != which) {
pids.c:        pids_oldproc_close(&info->get_PT);
pids.c:    info->boot_tics = 0;
pids.c:        info->boot_tics = up_secs * info->hertz;
pids.c:    if (NULL == info->read_something(info->get_PT, &info->get_proc))
pids.c:    if (!pids_assign_results(info, info->get_ext->stacks[0], &info->get_proc))
pids.c:    return info->get_ext->stacks[0];
pids.c:    if (!info->maxitems)
pids.c:    info->oldflags=268435529;
pids.c:    if (!pids_oldproc_open(&info->fetch_PT, info->oldflags))
pids.c:    info->read_something = which ? readeither : readproc;
pids.c:    info->boot_tics = 0;
pids.c:        info->boot_tics = up_secs * info->hertz;
pids.c:    pids_oldproc_close(&info->fetch_PT);
pids.c:    return (rc > 0) ? &info->fetch.results : NULL;
pids.c:    if (!info->maxitems)
pids.c:    info->oldflags=268435529;
pids.c:    if (!pids_oldproc_open(&info->fetch_PT, info->oldflags))
pids.c:    info->read_something = which ? readeither : readproc;
pids.c:    info->boot_tics = 0;
pids.c:        info->boot_tics = up_secs * info->hertz;
pids.c:    pids_oldproc_close(&info->fetch_PT);
pids.c:    return (rc > 0) ? &info->fetch.results : NULL;
pids.c:    if (info->maxitems == newnumitems + 1
pids.c:    && !memcmp(info->items, newitems, sizeof(enum pids_item) * newnumitems))
pids.c:    if (info->maxitems < newnumitems + 1) {
pids.c:        while (info->extents) {
pids.c:            struct stacks_extent *p = info->extents;
pids.c:            info->extents = p->next;
pids.c:        if (info->get_ext) {
pids.c:           pids_oldproc_close(&info->get_PT);
pids.c:           info->get_ext = NULL;
pids.c:        if (info->fetch.anchor) {
pids.c:            free(info->fetch.anchor);
pids.c:            info->fetch.anchor = NULL;
pids.c:        info->maxitems = newnumitems + 1;
pids.c:        if (!(info->items = realloc(info->items, sizeof(enum pids_item) * info->maxitems)))
pids.c:    memcpy(info->items, newitems, sizeof(enum pids_item) * newnumitems);
pids.c:    info->items[newnumitems] = PIDS_logical_end;
pids.c:    info->maxitems = newnumitems + 1;
pids.c:    if (!info->maxitems)
pids.c:    if (!pids_oldproc_open(&info->fetch_PT, (info->oldflags | which), ids, numthese))
pids.c:    info->read_something = (which & PIDS_FETCH_THREADS_TOO) ? readeither : readproc;
pids.c:    info->boot_tics = 0;
pids.c:        info->boot_tics = up_secs * info->hertz;
pids.c:    pids_oldproc_close(&info->fetch_PT);
pids.c:    return (rc >= 0) ? &info->fetch.results : NULL;
pids.c:        if (offset >= info->maxitems)
pids.c.bak:#define Hr(x)  info->hist->x           // 'hist ref', minimize stolen impact
pids.c.bak:    int slot = info->hist->num_tasks;
pids.c.bak:    info->hist->num_tasks++;
pids.c.bak:    int slot = info->hist->num_tasks1;
pids.c.bak:    info->hist->num_tasks1++;
pids.c.bak:    info->hist->num_tasks = 0;
pids.c.bak:    info->hist->num_tasks1 = 0;
pids.c.bak:        , info->hist->num_tasks
pids.c.bak:    SET_t *that = &info->func_array[0];
pids.c.bak:    info->seterr = 0;
pids.c.bak:    return !info->seterr;
pids.c.bak:    struct stacks_extent *ext = info->extents;
pids.c.bak:    struct stacks_extent *p = info->extents;
pids.c.bak:            info->extents = p->next;
pids.c.bak:    struct stacks_extent *ext = info->extents;
pids.c.bak:            pids_itemize_stack(ext->stacks[i]->head, info->maxitems, info->items);
pids.c.bak:    info->oldflags = info->history_yes = 0;
pids.c.bak:    for (i = 0; i < info->maxitems; i++) {
pids.c.bak:        if (((e = info->items[i])) >= PIDS_logical_end)
pids.c.bak:        info->oldflags |= Item_table[e].oldflags;
pids.c.bak:        info->history_yes |= Item_table[e].needhist;
pids.c.bak:    if (info->oldflags & f_either) {
pids.c.bak:        if (!(info->oldflags & (f_stat | f_status)))
pids.c.bak:            info->oldflags |= f_stat;
pids.c.bak:    if (!(info->func_array = realloc(info->func_array, sizeof(SET_t) * info->maxitems)))
pids.c.bak:    for (i = 0; i < info->maxitems -1; i++)
pids.c.bak:        info->func_array[i] = Item_table[info->items[i]].setsfunc;
pids.c.bak:    info->func_array[i] = NULL;
pids.c.bak:    if (info->history_yes)
pids.c.bak:    if (info->history_yes)
pids.c.bak:    list_size  = sizeof(struct pids_result) * info->maxitems;  // any single results stack |
pids.c.bak:    p_blob->next = info->extents;                              // push this extent onto... |
pids.c.bak:    info->extents = p_blob;                                    // ...some existing extents |
pids.c.bak:        p_head->head = pids_itemize_stack((struct pids_result *)v_list, info->maxitems, info->items);
pids.c.bak: #define n_alloc  info->fetch.n_alloc
pids.c.bak: #define n_inuse  info->fetch.n_inuse
pids.c.bak: #define n_saved  info->fetch.n_alloc_save
pids.c.bak:    if (!info->fetch.anchor) {
pids.c.bak:        if (!(info->fetch.anchor = calloc(STACKS_INIT, sizeof(void *))))
pids.c.bak:        memcpy(info->fetch.anchor, ext->stacks, sizeof(void *) * STACKS_INIT);
pids.c.bak:    memset(&info->fetch.counts, 0, sizeof(struct pids_counts));
pids.c.bak:    while (info->read_something(info->fetch_PT, &info->fetch_proc)) {
pids.c.bak:            if (!(info->fetch.anchor = realloc(info->fetch.anchor, sizeof(void *) * n_alloc))
pids.c.bak:            memcpy(info->fetch.anchor + n_inuse, ext->stacks, sizeof(void *) * STACKS_GROW);
pids.c.bak:        if (!pids_proc_tally(info, &info->fetch.counts, &info->fetch_proc))
pids.c.bak:        if (!pids_assign_results(info, info->fetch.anchor[n_inuse++], &info->fetch_proc))
pids.c.bak:        if (!(info->fetch.results.stacks = realloc(info->fetch.results.stacks, sizeof(void *) * n_saved)))
pids.c.bak:    memcpy(info->fetch.results.stacks, info->fetch.anchor, sizeof(void *) * n_inuse);
pids.c.bak:    info->fetch.results.stacks[n_inuse] = NULL;
pids.c.bak: #define n_alloc  info->fetch.n_alloc
pids.c.bak: #define n_inuse  info->fetch.n_inuse
pids.c.bak: #define n_saved  info->fetch.n_alloc_save
pids.c.bak:    if (!info->fetch.anchor) {
pids.c.bak:        if (!(info->fetch.anchor = calloc(STACKS_INIT, sizeof(void *))))
pids.c.bak:        memcpy(info->fetch.anchor, ext->stacks, sizeof(void *) * STACKS_INIT);
pids.c.bak:    memset(&info->fetch.counts, 0, sizeof(struct pids_counts));
pids.c.bak:    while (info->read_something(info->fetch_PT, &info->fetch_proc)) {
pids.c.bak:            if (!(info->fetch.anchor = realloc(info->fetch.anchor, sizeof(void *) * n_alloc))
pids.c.bak:            memcpy(info->fetch.anchor + n_inuse, ext->stacks, sizeof(void *) * STACKS_GROW);
pids.c.bak:        if (!pids_proc_tally1(info, &info->fetch.counts, &info->fetch_proc))
pids.c.bak:        if (!pids_assign_results(info, info->fetch.anchor[n_inuse++], &info->fetch_proc))
pids.c.bak:        if (!(info->fetch.results.stacks = realloc(info->fetch.results.stacks, sizeof(void *) * n_saved)))
pids.c.bak:    memcpy(info->fetch.results.stacks, info->fetch.anchor, sizeof(void *) * n_inuse);
pids.c.bak:    info->fetch.results.stacks[n_inuse] = NULL;
pids.c.bak:    info->refcount++;
pids.c.bak:    return info->refcount;
pids.c.bak:    if (!info->maxitems)
pids.c.bak:    if (!info->get_ext) {
pids.c.bak:        if (!(info->get_ext = pids_stacks_alloc(info, 1)))
pids.c.bak:        if (!pids_oldproc_open(&info->get_PT, info->oldflags))
pids.c.bak:        info->get_type = which;
pids.c.bak:        info->read_something = which ? readeither : readproc;
pids.c.bak:    if (info->get_type != which) {
pids.c.bak:        pids_oldproc_close(&info->get_PT);
pids.c.bak:    info->boot_tics = 0;
pids.c.bak:        info->boot_tics = up_secs * info->hertz;
pids.c.bak:    if (NULL == info->read_something(info->get_PT, &info->get_proc))
pids.c.bak:    if (!pids_assign_results(info, info->get_ext->stacks[0], &info->get_proc))
pids.c.bak:    return info->get_ext->stacks[0];
pids.c.bak:    if (!info->maxitems)
pids.c.bak:    if (!pids_oldproc_open(&info->fetch_PT, info->oldflags))
pids.c.bak:    info->read_something = which ? readeither : readproc;
pids.c.bak:    info->boot_tics = 0;
pids.c.bak:        info->boot_tics = up_secs * info->hertz;
pids.c.bak:    pids_oldproc_close(&info->fetch_PT);
pids.c.bak:    return (rc > 0) ? &info->fetch.results : NULL;
pids.c.bak:    if (!info->maxitems)
pids.c.bak:    if (!pids_oldproc_open(&info->fetch_PT, info->oldflags))
pids.c.bak:    info->read_something = which ? readeither : readproc;
pids.c.bak:    info->boot_tics = 0;
pids.c.bak:        info->boot_tics = up_secs * info->hertz;
pids.c.bak:    pids_oldproc_close(&info->fetch_PT);
pids.c.bak:    return (rc > 0) ? &info->fetch.results : NULL;
pids.c.bak:    if (info->maxitems == newnumitems + 1
pids.c.bak:    && !memcmp(info->items, newitems, sizeof(enum pids_item) * newnumitems))
pids.c.bak:    if (info->maxitems < newnumitems + 1) {
pids.c.bak:        while (info->extents) {
pids.c.bak:            struct stacks_extent *p = info->extents;
pids.c.bak:            info->extents = p->next;
pids.c.bak:        if (info->get_ext) {
pids.c.bak:           pids_oldproc_close(&info->get_PT);
pids.c.bak:           info->get_ext = NULL;
pids.c.bak:        if (info->fetch.anchor) {
pids.c.bak:            free(info->fetch.anchor);
pids.c.bak:            info->fetch.anchor = NULL;
pids.c.bak:        info->maxitems = newnumitems + 1;
pids.c.bak:        if (!(info->items = realloc(info->items, sizeof(enum pids_item) * info->maxitems)))
pids.c.bak:    memcpy(info->items, newitems, sizeof(enum pids_item) * newnumitems);
pids.c.bak:    info->items[newnumitems] = PIDS_logical_end;
pids.c.bak:    info->maxitems = newnumitems + 1;
pids.c.bak:    if (!info->maxitems)
pids.c.bak:    if (!pids_oldproc_open(&info->fetch_PT, (info->oldflags | which), ids, numthese))
pids.c.bak:    info->read_something = (which & PIDS_FETCH_THREADS_TOO) ? readeither : readproc;
pids.c.bak:    info->boot_tics = 0;
pids.c.bak:        info->boot_tics = up_secs * info->hertz;
pids.c.bak:    pids_oldproc_close(&info->fetch_PT);
pids.c.bak:    return (rc >= 0) ? &info->fetch.results : NULL;
pids.c.bak:        if (offset >= info->maxitems)
s.log:     if (!hcreate_r(n + (n / 4), &info->hashtab))
s.log:   if (!hsearch_r(e, ENTER, &ep, &info->hashtab)) return 1;
stat.c:    struct stat_core *last = NULL, *core = info->cores;
stat.c:    else info->cores = core;
stat.c:    core = info->cores;
stat.c:        core = info->cores;
stat.c:    core = info->cores;
stat.c:    struct stat_core *core = info->cores;
stat.c:    if (!info->cpus.hist.n_alloc) {
stat.c:        info->cpus.hist.tics = calloc(NEWOLD_INCR, sizeof(struct hist_tic));
stat.c:        if (!(info->cpus.hist.tics))
stat.c:        info->cpus.hist.n_alloc = NEWOLD_INCR;
stat.c:        info->cpus.hist.n_inuse = 0;
stat.c:    if (!info->stat_fp
stat.c:    && (!(info->stat_fp = fopen(STAT_FILE, "r"))))
stat.c:    fflush(info->stat_fp);
stat.c:    rewind(info->stat_fp);
stat.c: #define maxSIZ    info->stat_buf_size
stat.c: #define curPOS  ( info->stat_buf + tot_read )
stat.c:    while ((0 < (num = fread(curPOS, 1, curSIZ, info->stat_fp)))) {
stat.c:        if (!(info->stat_buf = realloc(info->stat_buf, maxSIZ)))
stat.c:    if (!feof(info->stat_fp)) {
stat.c:    info->stat_buf[tot_read] = '\0';
stat.c:    bp = info->stat_buf;
stat.c:    sum_ptr = &info->cpu_hist;
stat.c:    cpu_ptr = info->cpus.hist.tics + i;   // adapt to relocated if reap_em_again
stat.c:    } while (i < info->cpus.hist.n_alloc);
stat.c:    if (i == info->cpus.hist.n_alloc && rc >= 8) {
stat.c:        info->cpus.hist.n_alloc += NEWOLD_INCR;
stat.c:        info->cpus.hist.tics = realloc(info->cpus.hist.tics, info->cpus.hist.n_alloc * sizeof(struct hist_tic));
stat.c:        if (!(info->cpus.hist.tics))
stat.c:    info->cpus.total = info->cpus.hist.n_inuse = sum_ptr->count = i;
stat.c:    if (info->cpu_count_hwm < info->cpus.total) {
stat.c:        if (info->cpu_count_hwm) {
stat.c:        info->cpu_count_hwm = info->cpus.total;
stat.c:    memcpy(&info->sys_hist.old, &info->sys_hist.new, sizeof(struct stat_data));
stat.c:    info->sys_hist.new.intr = llnum;
stat.c:    info->sys_hist.new.ctxt = llnum;
stat.c:    info->sys_hist.new.btime = llnum;
stat.c:    info->sys_hist.new.procs_created = llnum;
stat.c:    info->sys_hist.new.procs_blocked = llnum;
stat.c:    info->sys_hist.new.procs_running = llnum;
stat.c:        stat_assign_results(this->anchor[i], &info->sys_hist, &this->hist.tics[i]);
stat.c:    stat_assign_results(this->extents->stacks[0], &info->sys_hist, &info->cpu_hist);
stat.c:    info->refcount++;
stat.c:    return info->refcount;
stat.c:    if (1 <= cur_secs - info->sav_secs) {
stat.c:        info->sav_secs = cur_secs;
stat.c:    info->get_this.item = item;
stat.c:    info->get_this.result.ull_int = 0;
stat.c:    Item_table[item].setsfunc(&info->get_this, &info->sys_hist, &info->cpu_hist);
stat.c:    return &info->get_this;
stat.c:    if (0 > (rc = stat_stacks_reconfig_maybe(&info->cpu_summary, items, numitems)))
stat.c:        stat_extents_free_all(&info->cpus.fetch);
stat.c:        stat_extents_free_all(&info->nodes.fetch);
stat.c:    info->results.summary = stat_update_single_stack(info, &info->cpu_summary);
stat.c:    	printf("info->results->summary--->=%ld\n",info->results.summary->head[ff].result.sl_int);
stat.c:    if (!info->nodes.result.stacks
stat.c:    && (!(info->nodes.result.stacks = malloc(sizeof(void *)))))
stat.c:    info->nodes.result.total = 0;
stat.c:    info->nodes.result.stacks[0] = NULL;
stat.c:            if (0 > stat_stacks_fetch(info, &info->cpus))
stat.c:            if (0 > stat_stacks_fetch(info, &info->nodes))
stat.c:            if (0 > stat_stacks_fetch(info, &info->cpus))
stat.c:    return &info->results;
stat.c:    if (0 > stat_stacks_reconfig_maybe(&info->select, items, numitems))
stat.c:    return stat_update_single_stack(info, &info->select);
stat.c.bak2:    struct stat_core *last = NULL, *core = info->cores;
stat.c.bak2:    else info->cores = core;
stat.c.bak2:    core = info->cores;
stat.c.bak2:        core = info->cores;
stat.c.bak2:    core = info->cores;
stat.c.bak2:    struct stat_core *core = info->cores;
stat.c.bak2:    if (!info->cpus.hist.n_alloc) {
stat.c.bak2:        info->cpus.hist.tics = calloc(NEWOLD_INCR, sizeof(struct hist_tic));
stat.c.bak2:        if (!(info->cpus.hist.tics))
stat.c.bak2:        info->cpus.hist.n_alloc = NEWOLD_INCR;
stat.c.bak2:        info->cpus.hist.n_inuse = 0;
stat.c.bak2:    if (!info->stat_fp
stat.c.bak2:    && (!(info->stat_fp = fopen(STAT_FILE, "r"))))
stat.c.bak2:    fflush(info->stat_fp);
stat.c.bak2:    rewind(info->stat_fp);
stat.c.bak2: #define maxSIZ    info->stat_buf_size
stat.c.bak2: #define curPOS  ( info->stat_buf + tot_read )
stat.c.bak2:    while ((0 < (num = fread(curPOS, 1, curSIZ, info->stat_fp)))) {
stat.c.bak2:        if (!(info->stat_buf = realloc(info->stat_buf, maxSIZ)))
stat.c.bak2:    if (!feof(info->stat_fp)) {
stat.c.bak2:    info->stat_buf[tot_read] = '\0';
stat.c.bak2:    bp = info->stat_buf;
stat.c.bak2:    sum_ptr = &info->cpu_hist;
stat.c.bak2:    cpu_ptr = info->cpus.hist.tics + i;   // adapt to relocated if reap_em_again
stat.c.bak2:    } while (i < info->cpus.hist.n_alloc);
stat.c.bak2:    if (i == info->cpus.hist.n_alloc && rc >= 8) {
stat.c.bak2:        info->cpus.hist.n_alloc += NEWOLD_INCR;
stat.c.bak2:        info->cpus.hist.tics = realloc(info->cpus.hist.tics, info->cpus.hist.n_alloc * sizeof(struct hist_tic));
stat.c.bak2:        if (!(info->cpus.hist.tics))
stat.c.bak2:    info->cpus.total = info->cpus.hist.n_inuse = sum_ptr->count = i;
stat.c.bak2:    if (info->cpu_count_hwm < info->cpus.total) {
stat.c.bak2:        if (info->cpu_count_hwm) {
stat.c.bak2:        info->cpu_count_hwm = info->cpus.total;
stat.c.bak2:    memcpy(&info->sys_hist.old, &info->sys_hist.new, sizeof(struct stat_data));
stat.c.bak2:    info->sys_hist.new.intr = llnum;
stat.c.bak2:    info->sys_hist.new.ctxt = llnum;
stat.c.bak2:    info->sys_hist.new.btime = llnum;
stat.c.bak2:    info->sys_hist.new.procs_created = llnum;
stat.c.bak2:    info->sys_hist.new.procs_blocked = llnum;
stat.c.bak2:    info->sys_hist.new.procs_running = llnum;
stat.c.bak2:        stat_assign_results(this->anchor[i], &info->sys_hist, &this->hist.tics[i]);
stat.c.bak2:    stat_assign_results(this->extents->stacks[0], &info->sys_hist, &info->cpu_hist);
stat.c.bak2:    info->refcount++;
stat.c.bak2:    return info->refcount;
stat.c.bak2:    if (1 <= cur_secs - info->sav_secs) {
stat.c.bak2:        info->sav_secs = cur_secs;
stat.c.bak2:    info->get_this.item = item;
stat.c.bak2:    info->get_this.result.ull_int = 0;
stat.c.bak2:    Item_table[item].setsfunc(&info->get_this, &info->sys_hist, &info->cpu_hist);
stat.c.bak2:    return &info->get_this;
stat.c.bak2:    if (0 > (rc = stat_stacks_reconfig_maybe(&info->cpu_summary, items, numitems)))
stat.c.bak2:        stat_extents_free_all(&info->cpus.fetch);
stat.c.bak2:        stat_extents_free_all(&info->nodes.fetch);
stat.c.bak2:    info->results.summary = stat_update_single_stack(info, &info->cpu_summary);
stat.c.bak2:    	printf("info->results->summary--->=%ld\n",info->results.summary->head[ff].result.sl_int);
stat.c.bak2:    if (!info->nodes.result.stacks
stat.c.bak2:    && (!(info->nodes.result.stacks = malloc(sizeof(void *)))))
stat.c.bak2:    info->nodes.result.total = 0;
stat.c.bak2:    info->nodes.result.stacks[0] = NULL;
stat.c.bak2:            if (0 > stat_stacks_fetch(info, &info->cpus))
stat.c.bak2:            if (0 > stat_stacks_fetch(info, &info->nodes))
stat.c.bak2:            if (0 > stat_stacks_fetch(info, &info->cpus))
stat.c.bak2:    return &info->results;
stat.c.bak2:    if (0 > stat_stacks_reconfig_maybe(&info->select, items, numitems))
stat.c.bak2:    return stat_update_single_stack(info, &info->select);
