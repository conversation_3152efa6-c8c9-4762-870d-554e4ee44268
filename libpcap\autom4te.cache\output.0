@%:@! /bin/sh
@%:@ Guess values for system-dependent variables and create Makefiles.
@%:@ Generated by GNU Autoconf 2.71 for pcap 1.11.0-PRE-GIT.
@%:@
@%:@ Report bugs to <https://github.com/the-tcpdump-group/libpcap/issues>.
@%:@ 
@%:@ 
@%:@ Copyright (C) 1992-1996, 1998-2017, 2020-2021 Free Software Foundation,
@%:@ Inc.
@%:@ 
@%:@ 
@%:@ This configure script is free software; the Free Software Foundation
@%:@ gives unlimited permission to copy, distribute and modify it.
## -------------------- ##
## M4sh Initialization. ##
## -------------------- ##

# Be more Bourne compatible
DUALCASE=1; export DUALCASE # for MKS sh
as_nop=:
if test ${ZSH_VERSION+y} && (emulate sh) >/dev/null 2>&1
then :
  emulate sh
  NULLCMD=:
  # Pre-4.2 versions of Zsh do word splitting on ${1+"$@"}, which
  # is contrary to our usage.  Disable this feature.
  alias -g '${1+"$@"}'='"$@"'
  setopt NO_GLOB_SUBST
else $as_nop
  case `(set -o) 2>/dev/null` in @%:@(
  *posix*) :
    set -o posix ;; @%:@(
  *) :
     ;;
esac
fi



# Reset variables that may have inherited troublesome values from
# the environment.

# IFS needs to be set, to space, tab, and newline, in precisely that order.
# (If _AS_PATH_WALK were called with IFS unset, it would have the
# side effect of setting IFS to empty, thus disabling word splitting.)
# Quoting is to prevent editors from complaining about space-tab.
as_nl='
'
export as_nl
IFS=" ""	$as_nl"

PS1='$ '
PS2='> '
PS4='+ '

# Ensure predictable behavior from utilities with locale-dependent output.
LC_ALL=C
export LC_ALL
LANGUAGE=C
export LANGUAGE

# We cannot yet rely on "unset" to work, but we need these variables
# to be unset--not just set to an empty or harmless value--now, to
# avoid bugs in old shells (e.g. pre-3.0 UWIN ksh).  This construct
# also avoids known problems related to "unset" and subshell syntax
# in other old shells (e.g. bash 2.01 and pdksh 5.2.14).
for as_var in BASH_ENV ENV MAIL MAILPATH CDPATH
do eval test \${$as_var+y} \
  && ( (unset $as_var) || exit 1) >/dev/null 2>&1 && unset $as_var || :
done

# Ensure that fds 0, 1, and 2 are open.
if (exec 3>&0) 2>/dev/null; then :; else exec 0</dev/null; fi
if (exec 3>&1) 2>/dev/null; then :; else exec 1>/dev/null; fi
if (exec 3>&2)            ; then :; else exec 2>/dev/null; fi

# The user is always right.
if ${PATH_SEPARATOR+false} :; then
  PATH_SEPARATOR=:
  (PATH='/bin;/bin'; FPATH=$PATH; sh -c :) >/dev/null 2>&1 && {
    (PATH='/bin:/bin'; FPATH=$PATH; sh -c :) >/dev/null 2>&1 ||
      PATH_SEPARATOR=';'
  }
fi


# Find who we are.  Look in the path if we contain no directory separator.
as_myself=
case $0 in @%:@((
  *[\\/]* ) as_myself=$0 ;;
  *) as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    test -r "$as_dir$0" && as_myself=$as_dir$0 && break
  done
IFS=$as_save_IFS

     ;;
esac
# We did not find ourselves, most probably we were run as `sh COMMAND'
# in which case we are not to be found in the path.
if test "x$as_myself" = x; then
  as_myself=$0
fi
if test ! -f "$as_myself"; then
  printf "%s\n" "$as_myself: error: cannot find myself; rerun with an absolute file name" >&2
  exit 1
fi


# Use a proper internal environment variable to ensure we don't fall
  # into an infinite loop, continuously re-executing ourselves.
  if test x"${_as_can_reexec}" != xno && test "x$CONFIG_SHELL" != x; then
    _as_can_reexec=no; export _as_can_reexec;
    # We cannot yet assume a decent shell, so we have to provide a
# neutralization value for shells without unset; and this also
# works around shells that cannot unset nonexistent variables.
# Preserve -v and -x to the replacement shell.
BASH_ENV=/dev/null
ENV=/dev/null
(unset BASH_ENV) >/dev/null 2>&1 && unset BASH_ENV ENV
case $- in @%:@ ((((
  *v*x* | *x*v* ) as_opts=-vx ;;
  *v* ) as_opts=-v ;;
  *x* ) as_opts=-x ;;
  * ) as_opts= ;;
esac
exec $CONFIG_SHELL $as_opts "$as_myself" ${1+"$@"}
# Admittedly, this is quite paranoid, since all the known shells bail
# out after a failed `exec'.
printf "%s\n" "$0: could not re-execute with $CONFIG_SHELL" >&2
exit 255
  fi
  # We don't want this to propagate to other subprocesses.
          { _as_can_reexec=; unset _as_can_reexec;}
if test "x$CONFIG_SHELL" = x; then
  as_bourne_compatible="as_nop=:
if test \${ZSH_VERSION+y} && (emulate sh) >/dev/null 2>&1
then :
  emulate sh
  NULLCMD=:
  # Pre-4.2 versions of Zsh do word splitting on \${1+\"\$@\"}, which
  # is contrary to our usage.  Disable this feature.
  alias -g '\${1+\"\$@\"}'='\"\$@\"'
  setopt NO_GLOB_SUBST
else \$as_nop
  case \`(set -o) 2>/dev/null\` in @%:@(
  *posix*) :
    set -o posix ;; @%:@(
  *) :
     ;;
esac
fi
"
  as_required="as_fn_return () { (exit \$1); }
as_fn_success () { as_fn_return 0; }
as_fn_failure () { as_fn_return 1; }
as_fn_ret_success () { return 0; }
as_fn_ret_failure () { return 1; }

exitcode=0
as_fn_success || { exitcode=1; echo as_fn_success failed.; }
as_fn_failure && { exitcode=1; echo as_fn_failure succeeded.; }
as_fn_ret_success || { exitcode=1; echo as_fn_ret_success failed.; }
as_fn_ret_failure && { exitcode=1; echo as_fn_ret_failure succeeded.; }
if ( set x; as_fn_ret_success y && test x = \"\$1\" )
then :
  
else \$as_nop
  exitcode=1; echo positional parameters were not saved.
fi
test x\$exitcode = x0 || exit 1
blah=\$(echo \$(echo blah))
test x\"\$blah\" = xblah || exit 1
test -x / || exit 1"
  as_suggested="  as_lineno_1=";as_suggested=$as_suggested$LINENO;as_suggested=$as_suggested" as_lineno_1a=\$LINENO
  as_lineno_2=";as_suggested=$as_suggested$LINENO;as_suggested=$as_suggested" as_lineno_2a=\$LINENO
  eval 'test \"x\$as_lineno_1'\$as_run'\" != \"x\$as_lineno_2'\$as_run'\" &&
  test \"x\`expr \$as_lineno_1'\$as_run' + 1\`\" = \"x\$as_lineno_2'\$as_run'\"' || exit 1
test \$(( 1 + 1 )) = 2 || exit 1"
  if (eval "$as_required") 2>/dev/null
then :
  as_have_required=yes
else $as_nop
  as_have_required=no
fi
  if test x$as_have_required = xyes && (eval "$as_suggested") 2>/dev/null
then :
  
else $as_nop
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
as_found=false
for as_dir in /bin$PATH_SEPARATOR/usr/bin$PATH_SEPARATOR$PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
  as_found=:
  case $as_dir in @%:@(
	 /*)
	   for as_base in sh bash ksh sh5; do
	     # Try only shells that exist, to save several forks.
	     as_shell=$as_dir$as_base
	     if { test -f "$as_shell" || test -f "$as_shell.exe"; } &&
		    as_run=a "$as_shell" -c "$as_bourne_compatible""$as_required" 2>/dev/null
then :
  CONFIG_SHELL=$as_shell as_have_required=yes
		   if as_run=a "$as_shell" -c "$as_bourne_compatible""$as_suggested" 2>/dev/null
then :
  break 2
fi
fi
	   done;;
       esac
  as_found=false
done
IFS=$as_save_IFS
if $as_found
then :
  
else $as_nop
  if { test -f "$SHELL" || test -f "$SHELL.exe"; } &&
	      as_run=a "$SHELL" -c "$as_bourne_compatible""$as_required" 2>/dev/null
then :
  CONFIG_SHELL=$SHELL as_have_required=yes
fi
fi


      if test "x$CONFIG_SHELL" != x
then :
  export CONFIG_SHELL
             # We cannot yet assume a decent shell, so we have to provide a
# neutralization value for shells without unset; and this also
# works around shells that cannot unset nonexistent variables.
# Preserve -v and -x to the replacement shell.
BASH_ENV=/dev/null
ENV=/dev/null
(unset BASH_ENV) >/dev/null 2>&1 && unset BASH_ENV ENV
case $- in @%:@ ((((
  *v*x* | *x*v* ) as_opts=-vx ;;
  *v* ) as_opts=-v ;;
  *x* ) as_opts=-x ;;
  * ) as_opts= ;;
esac
exec $CONFIG_SHELL $as_opts "$as_myself" ${1+"$@"}
# Admittedly, this is quite paranoid, since all the known shells bail
# out after a failed `exec'.
printf "%s\n" "$0: could not re-execute with $CONFIG_SHELL" >&2
exit 255
fi

    if test x$as_have_required = xno
then :
  printf "%s\n" "$0: This script requires a shell more modern than all"
  printf "%s\n" "$0: the shells that I found on your system."
  if test ${ZSH_VERSION+y} ; then
    printf "%s\n" "$0: In particular, zsh $ZSH_VERSION has bugs and should"
    printf "%s\n" "$0: be upgraded to zsh 4.3.4 or later."
  else
    printf "%s\n" "$0: <NAME_EMAIL> and
$0: https://github.com/the-tcpdump-group/libpcap/issues
$0: about your system, including any error possibly output
$0: before this message. Then install a modern shell, or
$0: manually run the script under such a shell if you do
$0: have one."
  fi
  exit 1
fi
fi
fi
SHELL=${CONFIG_SHELL-/bin/sh}
export SHELL
# Unset more variables known to interfere with behavior of common tools.
CLICOLOR_FORCE= GREP_OPTIONS=
unset CLICOLOR_FORCE GREP_OPTIONS

## --------------------- ##
## M4sh Shell Functions. ##
## --------------------- ##
@%:@ as_fn_unset VAR
@%:@ ---------------
@%:@ Portably unset VAR.
as_fn_unset ()
{
  { eval $1=; unset $1;}
}
as_unset=as_fn_unset


@%:@ as_fn_set_status STATUS
@%:@ -----------------------
@%:@ Set @S|@? to STATUS, without forking.
as_fn_set_status ()
{
  return $1
} @%:@ as_fn_set_status

@%:@ as_fn_exit STATUS
@%:@ -----------------
@%:@ Exit the shell with STATUS, even in a "trap 0" or "set -e" context.
as_fn_exit ()
{
  set +e
  as_fn_set_status $1
  exit $1
} @%:@ as_fn_exit
@%:@ as_fn_nop
@%:@ ---------
@%:@ Do nothing but, unlike ":", preserve the value of @S|@?.
as_fn_nop ()
{
  return $?
}
as_nop=as_fn_nop

@%:@ as_fn_mkdir_p
@%:@ -------------
@%:@ Create "@S|@as_dir" as a directory, including parents if necessary.
as_fn_mkdir_p ()
{

  case $as_dir in #(
  -*) as_dir=./$as_dir;;
  esac
  test -d "$as_dir" || eval $as_mkdir_p || {
    as_dirs=
    while :; do
      case $as_dir in #(
      *\'*) as_qdir=`printf "%s\n" "$as_dir" | sed "s/'/'\\\\\\\\''/g"`;; #'(
      *) as_qdir=$as_dir;;
      esac
      as_dirs="'$as_qdir' $as_dirs"
      as_dir=`$as_dirname -- "$as_dir" ||
$as_expr X"$as_dir" : 'X\(.*[^/]\)//*[^/][^/]*/*$' \| \
	 X"$as_dir" : 'X\(//\)[^/]' \| \
	 X"$as_dir" : 'X\(//\)$' \| \
	 X"$as_dir" : 'X\(/\)' \| . 2>/dev/null ||
printf "%s\n" X"$as_dir" |
    sed '/^X\(.*[^/]\)\/\/*[^/][^/]*\/*$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)[^/].*/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`
      test -d "$as_dir" && break
    done
    test -z "$as_dirs" || eval "mkdir $as_dirs"
  } || test -d "$as_dir" || as_fn_error $? "cannot create directory $as_dir"


} @%:@ as_fn_mkdir_p

@%:@ as_fn_executable_p FILE
@%:@ -----------------------
@%:@ Test if FILE is an executable regular file.
as_fn_executable_p ()
{
  test -f "$1" && test -x "$1"
} @%:@ as_fn_executable_p
@%:@ as_fn_append VAR VALUE
@%:@ ----------------------
@%:@ Append the text in VALUE to the end of the definition contained in VAR. Take
@%:@ advantage of any shell optimizations that allow amortized linear growth over
@%:@ repeated appends, instead of the typical quadratic growth present in naive
@%:@ implementations.
if (eval "as_var=1; as_var+=2; test x\$as_var = x12") 2>/dev/null
then :
  eval 'as_fn_append ()
  {
    eval $1+=\$2
  }'
else $as_nop
  as_fn_append ()
  {
    eval $1=\$$1\$2
  }
fi # as_fn_append

@%:@ as_fn_arith ARG...
@%:@ ------------------
@%:@ Perform arithmetic evaluation on the ARGs, and store the result in the
@%:@ global @S|@as_val. Take advantage of shells that can avoid forks. The arguments
@%:@ must be portable across @S|@(()) and expr.
if (eval "test \$(( 1 + 1 )) = 2") 2>/dev/null
then :
  eval 'as_fn_arith ()
  {
    as_val=$(( $* ))
  }'
else $as_nop
  as_fn_arith ()
  {
    as_val=`expr "$@" || test $? -eq 1`
  }
fi # as_fn_arith

@%:@ as_fn_nop
@%:@ ---------
@%:@ Do nothing but, unlike ":", preserve the value of @S|@?.
as_fn_nop ()
{
  return $?
}
as_nop=as_fn_nop

@%:@ as_fn_error STATUS ERROR [LINENO LOG_FD]
@%:@ ----------------------------------------
@%:@ Output "`basename @S|@0`: error: ERROR" to stderr. If LINENO and LOG_FD are
@%:@ provided, also output the error to LOG_FD, referencing LINENO. Then exit the
@%:@ script with STATUS, using 1 if that was 0.
as_fn_error ()
{
  as_status=$1; test $as_status -eq 0 && as_status=1
  if test "$4"; then
    as_lineno=${as_lineno-"$3"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: $2" >&$4
  fi
  printf "%s\n" "$as_me: error: $2" >&2
  as_fn_exit $as_status
} @%:@ as_fn_error

if expr a : '\(a\)' >/dev/null 2>&1 &&
   test "X`expr 00001 : '.*\(...\)'`" = X001; then
  as_expr=expr
else
  as_expr=false
fi

if (basename -- /) >/dev/null 2>&1 && test "X`basename -- / 2>&1`" = "X/"; then
  as_basename=basename
else
  as_basename=false
fi

if (as_dir=`dirname -- /` && test "X$as_dir" = X/) >/dev/null 2>&1; then
  as_dirname=dirname
else
  as_dirname=false
fi

as_me=`$as_basename -- "$0" ||
$as_expr X/"$0" : '.*/\([^/][^/]*\)/*$' \| \
	 X"$0" : 'X\(//\)$' \| \
	 X"$0" : 'X\(/\)' \| . 2>/dev/null ||
printf "%s\n" X/"$0" |
    sed '/^.*\/\([^/][^/]*\)\/*$/{
	    s//\1/
	    q
	  }
	  /^X\/\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\/\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`

# Avoid depending upon Character Ranges.
as_cr_letters='abcdefghijklmnopqrstuvwxyz'
as_cr_LETTERS='ABCDEFGHIJKLMNOPQRSTUVWXYZ'
as_cr_Letters=$as_cr_letters$as_cr_LETTERS
as_cr_digits='0123456789'
as_cr_alnum=$as_cr_Letters$as_cr_digits


  as_lineno_1=$LINENO as_lineno_1a=$LINENO
  as_lineno_2=$LINENO as_lineno_2a=$LINENO
  eval 'test "x$as_lineno_1'$as_run'" != "x$as_lineno_2'$as_run'" &&
  test "x`expr $as_lineno_1'$as_run' + 1`" = "x$as_lineno_2'$as_run'"' || {
  # Blame Lee E. McMahon (1931-1989) for sed's syntax.  :-)
  sed -n '
    p
    /[$]LINENO/=
  ' <$as_myself |
    sed '
      s/[$]LINENO.*/&-/
      t lineno
      b
      :lineno
      N
      :loop
      s/[$]LINENO\([^'$as_cr_alnum'_].*\n\)\(.*\)/\2\1\2/
      t loop
      s/-\n.*//
    ' >$as_me.lineno &&
  chmod +x "$as_me.lineno" ||
    { printf "%s\n" "$as_me: error: cannot create $as_me.lineno; rerun with a POSIX shell" >&2; as_fn_exit 1; }

  # If we had to re-execute with $CONFIG_SHELL, we're ensured to have
  # already done that, so ensure we don't try to do so again and fall
  # in an infinite loop.  This has already happened in practice.
  _as_can_reexec=no; export _as_can_reexec
  # Don't try to exec as it changes $[0], causing all sort of problems
  # (the dirname of $[0] is not the place where we might find the
  # original and so on.  Autoconf is especially sensitive to this).
  . "./$as_me.lineno"
  # Exit status is that of the last command.
  exit
}


# Determine whether it's possible to make 'echo' print without a newline.
# These variables are no longer used directly by Autoconf, but are AC_SUBSTed
# for compatibility with existing Makefiles.
ECHO_C= ECHO_N= ECHO_T=
case `echo -n x` in @%:@(((((
-n*)
  case `echo 'xy\c'` in
  *c*) ECHO_T='	';;	# ECHO_T is single tab character.
  xy)  ECHO_C='\c';;
  *)   echo `echo ksh88 bug on AIX 6.1` > /dev/null
       ECHO_T='	';;
  esac;;
*)
  ECHO_N='-n';;
esac

# For backward compatibility with old third-party macros, we provide
# the shell variables $as_echo and $as_echo_n.  New code should use
# AS_ECHO(["message"]) and AS_ECHO_N(["message"]), respectively.
as_@&t@echo='printf %s\n'
as_@&t@echo_n='printf %s'


rm -f conf$$ conf$$.exe conf$$.file
if test -d conf$$.dir; then
  rm -f conf$$.dir/conf$$.file
else
  rm -f conf$$.dir
  mkdir conf$$.dir 2>/dev/null
fi
if (echo >conf$$.file) 2>/dev/null; then
  if ln -s conf$$.file conf$$ 2>/dev/null; then
    as_ln_s='ln -s'
    # ... but there are two gotchas:
    # 1) On MSYS, both `ln -s file dir' and `ln file dir' fail.
    # 2) DJGPP < 2.04 has no symlinks; `ln -s' creates a wrapper executable.
    # In both cases, we have to default to `cp -pR'.
    ln -s conf$$.file conf$$.dir 2>/dev/null && test ! -f conf$$.exe ||
      as_ln_s='cp -pR'
  elif ln conf$$.file conf$$ 2>/dev/null; then
    as_ln_s=ln
  else
    as_ln_s='cp -pR'
  fi
else
  as_ln_s='cp -pR'
fi
rm -f conf$$ conf$$.exe conf$$.dir/conf$$.file conf$$.file
rmdir conf$$.dir 2>/dev/null

if mkdir -p . 2>/dev/null; then
  as_mkdir_p='mkdir -p "$as_dir"'
else
  test -d ./-p && rmdir ./-p
  as_mkdir_p=false
fi

as_test_x='test -x'
as_executable_p=as_fn_executable_p

# Sed expression to map a string onto a valid CPP name.
as_tr_cpp="eval sed 'y%*$as_cr_letters%P$as_cr_LETTERS%;s%[^_$as_cr_alnum]%_%g'"

# Sed expression to map a string onto a valid variable name.
as_tr_sh="eval sed 'y%*+%pp%;s%[^_$as_cr_alnum]%_%g'"


test -n "$DJDIR" || exec 7<&0 </dev/null
exec 6>&1

# Name of the host.
# hostname on some systems (SVR3.2, old GNU/Linux) returns a bogus exit status,
# so uname gets run too.
ac_hostname=`(hostname || uname -n) 2>/dev/null | sed 1q`

#
# Initializations.
#
ac_default_prefix=/usr/local
ac_clean_files=
ac_config_libobj_dir=.
LIB@&t@OBJS=
cross_compiling=no
subdirs=
MFLAGS=
MAKEFLAGS=

# Identity of this package.
PACKAGE_NAME='pcap'
PACKAGE_TARNAME='libpcap'
PACKAGE_VERSION='1.11.0-PRE-GIT'
PACKAGE_STRING='pcap 1.11.0-PRE-GIT'
PACKAGE_BUGREPORT='https://github.com/the-tcpdump-group/libpcap/issues'
PACKAGE_URL='https://www.tcpdump.org/'

ac_unique_file="pcap.c"
# Factoring default headers for most tests.
ac_includes_default="\
#include <stddef.h>
#ifdef HAVE_STDIO_H
# include <stdio.h>
#endif
#ifdef HAVE_STDLIB_H
# include <stdlib.h>
#endif
#ifdef HAVE_STRING_H
# include <string.h>
#endif
#ifdef HAVE_INTTYPES_H
# include <inttypes.h>
#endif
#ifdef HAVE_STDINT_H
# include <stdint.h>
#endif
#ifdef HAVE_STRINGS_H
# include <strings.h>
#endif
#ifdef HAVE_SYS_TYPES_H
# include <sys/types.h>
#endif
#ifdef HAVE_SYS_STAT_H
# include <sys/stat.h>
#endif
#ifdef HAVE_UNISTD_H
# include <unistd.h>
#endif"

ac_header_c_list=
ac_subst_vars='LTLIBOBJS
RPCAPD_LIBS
INSTALL_RPCAPD
BUILD_RPCAPD
PTHREAD_LIBS
REMOTE_C_SRC
MODULE_C_SRC
PLATFORM_C_SRC
ADDLARCHIVEOBJS
ADDLOBJS
RPATH
V_SONAME_OPT
V_SHLIB_OPT
V_SHLIB_CMD
V_SHLIB_CCOPT
INSTALL_DATA
INSTALL_SCRIPT
INSTALL_PROGRAM
PCAP_SUPPORT_RDMASNIFF
LIBIBVERBS_LIBS_STATIC
LIBIBVERBS_LIBS
LIBIBVERBS_CFLAGS
PCAP_SUPPORT_DBUS
DBUS_LIBS_STATIC
DBUS_LIBS
DBUS_CFLAGS
PCAP_SUPPORT_BT
PCAP_SUPPORT_DPDK
DPDK_LIBS_STATIC
DPDK_LIBS
DPDK_CFLAGS
PCAP_SUPPORT_NETMAP
PCAP_SUPPORT_NETFILTER
PCAP_SUPPORT_LINUX_USBMON
MKDEP
DEPENDENCY_CFLAG
LN_S
AR
RANLIB
MAN_ADMIN_COMMANDS
MAN_MISC_INFO
MAN_FILE_FORMATS
MAN_DEVICES
DYEXT
V_PROG_LDFLAGS_FAT
V_PROG_CCOPT_FAT
V_LIB_LDFLAGS_FAT
V_LIB_CCOPT_FAT
REENTRANT_PARSER
BISON_BYACC
LEXLIB
LEX_OUTPUT_ROOT
LEX
OPENSSL_LIBS_STATIC
OPENSSL_LIBS
OPENSSL_CFLAGS
LIBNL_LIBS_STATIC
LIBNL_LIBS
LIBNL_CFLAGS
BREW
PKG_CONFIG_LIBDIR
PKG_CONFIG_PATH
PKG_CONFIG
VALGRINDTEST_SRC
LIB@&t@OBJS
OBJEXT
EXEEXT
ac_ct_CC
CPPFLAGS
LDFLAGS
CFLAGS
CC
host_os
host_vendor
host_cpu
host
build_os
build_vendor
build_cpu
build
LIBS_PRIVATE
REQUIRES_PRIVATE
LIBS_STATIC
V_INCLS
V_DEFS
V_CCOPT
target_alias
host_alias
build_alias
LIBS
ECHO_T
ECHO_N
ECHO_C
DEFS
mandir
localedir
libdir
psdir
pdfdir
dvidir
htmldir
infodir
docdir
oldincludedir
includedir
runstatedir
localstatedir
sharedstatedir
sysconfdir
datadir
datarootdir
libexecdir
sbindir
bindir
program_transform_name
prefix
exec_prefix
PACKAGE_URL
PACKAGE_BUGREPORT
PACKAGE_STRING
PACKAGE_VERSION
PACKAGE_TARNAME
PACKAGE_NAME
PATH_SEPARATOR
SHELL'
ac_subst_files=''
ac_user_opts='
enable_option_checking
with_gcc
enable_largefile
enable_instrument_functions
enable_protochain
with_pcap
with_libnl
with_dag
with_dag_includes
with_dag_libraries
enable_dag_tx
with_snf
with_snf_includes
with_snf_libraries
enable_remote
enable_optimizer_dbg
enable_yydebug
enable_universal
enable_shared
enable_usb
enable_netmap
with_dpdk
enable_bluetooth
enable_dbus
enable_rdma
'
      ac_precious_vars='build_alias
host_alias
target_alias
CC
CFLAGS
LDFLAGS
LIBS
CPPFLAGS
PKG_CONFIG
PKG_CONFIG_PATH
PKG_CONFIG_LIBDIR
LIBNL_CFLAGS
LIBNL_LIBS
LIBNL_LIBS_STATIC
OPENSSL_CFLAGS
OPENSSL_LIBS
OPENSSL_LIBS_STATIC
DPDK_CFLAGS
DPDK_LIBS
DPDK_LIBS_STATIC
DBUS_CFLAGS
DBUS_LIBS
DBUS_LIBS_STATIC
LIBIBVERBS_CFLAGS
LIBIBVERBS_LIBS
LIBIBVERBS_LIBS_STATIC'


# Initialize some variables set by options.
ac_init_help=
ac_init_version=false
ac_unrecognized_opts=
ac_unrecognized_sep=
# The variables have the same names as the options, with
# dashes changed to underlines.
cache_file=/dev/null
exec_prefix=NONE
no_create=
no_recursion=
prefix=NONE
program_prefix=NONE
program_suffix=NONE
program_transform_name=s,x,x,
silent=
site=
srcdir=
verbose=
x_includes=NONE
x_libraries=NONE

# Installation directory options.
# These are left unexpanded so users can "make install exec_prefix=/foo"
# and all the variables that are supposed to be based on exec_prefix
# by default will actually change.
# Use braces instead of parens because sh, perl, etc. also accept them.
# (The list follows the same order as the GNU Coding Standards.)
bindir='${exec_prefix}/bin'
sbindir='${exec_prefix}/sbin'
libexecdir='${exec_prefix}/libexec'
datarootdir='${prefix}/share'
datadir='${datarootdir}'
sysconfdir='${prefix}/etc'
sharedstatedir='${prefix}/com'
localstatedir='${prefix}/var'
runstatedir='${localstatedir}/run'
includedir='${prefix}/include'
oldincludedir='/usr/include'
docdir='${datarootdir}/doc/${PACKAGE_TARNAME}'
infodir='${datarootdir}/info'
htmldir='${docdir}'
dvidir='${docdir}'
pdfdir='${docdir}'
psdir='${docdir}'
libdir='${exec_prefix}/lib'
localedir='${datarootdir}/locale'
mandir='${datarootdir}/man'

ac_prev=
ac_dashdash=
for ac_option
do
  # If the previous option needs an argument, assign it.
  if test -n "$ac_prev"; then
    eval $ac_prev=\$ac_option
    ac_prev=
    continue
  fi

  case $ac_option in
  *=?*) ac_optarg=`expr "X$ac_option" : '[^=]*=\(.*\)'` ;;
  *=)   ac_optarg= ;;
  *)    ac_optarg=yes ;;
  esac

  case $ac_dashdash$ac_option in
  --)
    ac_dashdash=yes ;;

  -bindir | --bindir | --bindi | --bind | --bin | --bi)
    ac_prev=bindir ;;
  -bindir=* | --bindir=* | --bindi=* | --bind=* | --bin=* | --bi=*)
    bindir=$ac_optarg ;;

  -build | --build | --buil | --bui | --bu)
    ac_prev=build_alias ;;
  -build=* | --build=* | --buil=* | --bui=* | --bu=*)
    build_alias=$ac_optarg ;;

  -cache-file | --cache-file | --cache-fil | --cache-fi \
  | --cache-f | --cache- | --cache | --cach | --cac | --ca | --c)
    ac_prev=cache_file ;;
  -cache-file=* | --cache-file=* | --cache-fil=* | --cache-fi=* \
  | --cache-f=* | --cache-=* | --cache=* | --cach=* | --cac=* | --ca=* | --c=*)
    cache_file=$ac_optarg ;;

  --config-cache | -C)
    cache_file=config.cache ;;

  -datadir | --datadir | --datadi | --datad)
    ac_prev=datadir ;;
  -datadir=* | --datadir=* | --datadi=* | --datad=*)
    datadir=$ac_optarg ;;

  -datarootdir | --datarootdir | --datarootdi | --datarootd | --dataroot \
  | --dataroo | --dataro | --datar)
    ac_prev=datarootdir ;;
  -datarootdir=* | --datarootdir=* | --datarootdi=* | --datarootd=* \
  | --dataroot=* | --dataroo=* | --dataro=* | --datar=*)
    datarootdir=$ac_optarg ;;

  -disable-* | --disable-*)
    ac_useropt=`expr "x$ac_option" : 'x-*disable-\(.*\)'`
    # Reject names that are not valid shell variable names.
    expr "x$ac_useropt" : ".*[^-+._$as_cr_alnum]" >/dev/null &&
      as_fn_error $? "invalid feature name: \`$ac_useropt'"
    ac_useropt_orig=$ac_useropt
    ac_useropt=`printf "%s\n" "$ac_useropt" | sed 's/[-+.]/_/g'`
    case $ac_user_opts in
      *"
"enable_$ac_useropt"
"*) ;;
      *) ac_unrecognized_opts="$ac_unrecognized_opts$ac_unrecognized_sep--disable-$ac_useropt_orig"
	 ac_unrecognized_sep=', ';;
    esac
    eval enable_$ac_useropt=no ;;

  -docdir | --docdir | --docdi | --doc | --do)
    ac_prev=docdir ;;
  -docdir=* | --docdir=* | --docdi=* | --doc=* | --do=*)
    docdir=$ac_optarg ;;

  -dvidir | --dvidir | --dvidi | --dvid | --dvi | --dv)
    ac_prev=dvidir ;;
  -dvidir=* | --dvidir=* | --dvidi=* | --dvid=* | --dvi=* | --dv=*)
    dvidir=$ac_optarg ;;

  -enable-* | --enable-*)
    ac_useropt=`expr "x$ac_option" : 'x-*enable-\([^=]*\)'`
    # Reject names that are not valid shell variable names.
    expr "x$ac_useropt" : ".*[^-+._$as_cr_alnum]" >/dev/null &&
      as_fn_error $? "invalid feature name: \`$ac_useropt'"
    ac_useropt_orig=$ac_useropt
    ac_useropt=`printf "%s\n" "$ac_useropt" | sed 's/[-+.]/_/g'`
    case $ac_user_opts in
      *"
"enable_$ac_useropt"
"*) ;;
      *) ac_unrecognized_opts="$ac_unrecognized_opts$ac_unrecognized_sep--enable-$ac_useropt_orig"
	 ac_unrecognized_sep=', ';;
    esac
    eval enable_$ac_useropt=\$ac_optarg ;;

  -exec-prefix | --exec_prefix | --exec-prefix | --exec-prefi \
  | --exec-pref | --exec-pre | --exec-pr | --exec-p | --exec- \
  | --exec | --exe | --ex)
    ac_prev=exec_prefix ;;
  -exec-prefix=* | --exec_prefix=* | --exec-prefix=* | --exec-prefi=* \
  | --exec-pref=* | --exec-pre=* | --exec-pr=* | --exec-p=* | --exec-=* \
  | --exec=* | --exe=* | --ex=*)
    exec_prefix=$ac_optarg ;;

  -gas | --gas | --ga | --g)
    # Obsolete; use --with-gas.
    with_gas=yes ;;

  -help | --help | --hel | --he | -h)
    ac_init_help=long ;;
  -help=r* | --help=r* | --hel=r* | --he=r* | -hr*)
    ac_init_help=recursive ;;
  -help=s* | --help=s* | --hel=s* | --he=s* | -hs*)
    ac_init_help=short ;;

  -host | --host | --hos | --ho)
    ac_prev=host_alias ;;
  -host=* | --host=* | --hos=* | --ho=*)
    host_alias=$ac_optarg ;;

  -htmldir | --htmldir | --htmldi | --htmld | --html | --htm | --ht)
    ac_prev=htmldir ;;
  -htmldir=* | --htmldir=* | --htmldi=* | --htmld=* | --html=* | --htm=* \
  | --ht=*)
    htmldir=$ac_optarg ;;

  -includedir | --includedir | --includedi | --included | --include \
  | --includ | --inclu | --incl | --inc)
    ac_prev=includedir ;;
  -includedir=* | --includedir=* | --includedi=* | --included=* | --include=* \
  | --includ=* | --inclu=* | --incl=* | --inc=*)
    includedir=$ac_optarg ;;

  -infodir | --infodir | --infodi | --infod | --info | --inf)
    ac_prev=infodir ;;
  -infodir=* | --infodir=* | --infodi=* | --infod=* | --info=* | --inf=*)
    infodir=$ac_optarg ;;

  -libdir | --libdir | --libdi | --libd)
    ac_prev=libdir ;;
  -libdir=* | --libdir=* | --libdi=* | --libd=*)
    libdir=$ac_optarg ;;

  -libexecdir | --libexecdir | --libexecdi | --libexecd | --libexec \
  | --libexe | --libex | --libe)
    ac_prev=libexecdir ;;
  -libexecdir=* | --libexecdir=* | --libexecdi=* | --libexecd=* | --libexec=* \
  | --libexe=* | --libex=* | --libe=*)
    libexecdir=$ac_optarg ;;

  -localedir | --localedir | --localedi | --localed | --locale)
    ac_prev=localedir ;;
  -localedir=* | --localedir=* | --localedi=* | --localed=* | --locale=*)
    localedir=$ac_optarg ;;

  -localstatedir | --localstatedir | --localstatedi | --localstated \
  | --localstate | --localstat | --localsta | --localst | --locals)
    ac_prev=localstatedir ;;
  -localstatedir=* | --localstatedir=* | --localstatedi=* | --localstated=* \
  | --localstate=* | --localstat=* | --localsta=* | --localst=* | --locals=*)
    localstatedir=$ac_optarg ;;

  -mandir | --mandir | --mandi | --mand | --man | --ma | --m)
    ac_prev=mandir ;;
  -mandir=* | --mandir=* | --mandi=* | --mand=* | --man=* | --ma=* | --m=*)
    mandir=$ac_optarg ;;

  -nfp | --nfp | --nf)
    # Obsolete; use --without-fp.
    with_fp=no ;;

  -no-create | --no-create | --no-creat | --no-crea | --no-cre \
  | --no-cr | --no-c | -n)
    no_create=yes ;;

  -no-recursion | --no-recursion | --no-recursio | --no-recursi \
  | --no-recurs | --no-recur | --no-recu | --no-rec | --no-re | --no-r)
    no_recursion=yes ;;

  -oldincludedir | --oldincludedir | --oldincludedi | --oldincluded \
  | --oldinclude | --oldinclud | --oldinclu | --oldincl | --oldinc \
  | --oldin | --oldi | --old | --ol | --o)
    ac_prev=oldincludedir ;;
  -oldincludedir=* | --oldincludedir=* | --oldincludedi=* | --oldincluded=* \
  | --oldinclude=* | --oldinclud=* | --oldinclu=* | --oldincl=* | --oldinc=* \
  | --oldin=* | --oldi=* | --old=* | --ol=* | --o=*)
    oldincludedir=$ac_optarg ;;

  -prefix | --prefix | --prefi | --pref | --pre | --pr | --p)
    ac_prev=prefix ;;
  -prefix=* | --prefix=* | --prefi=* | --pref=* | --pre=* | --pr=* | --p=*)
    prefix=$ac_optarg ;;

  -program-prefix | --program-prefix | --program-prefi | --program-pref \
  | --program-pre | --program-pr | --program-p)
    ac_prev=program_prefix ;;
  -program-prefix=* | --program-prefix=* | --program-prefi=* \
  | --program-pref=* | --program-pre=* | --program-pr=* | --program-p=*)
    program_prefix=$ac_optarg ;;

  -program-suffix | --program-suffix | --program-suffi | --program-suff \
  | --program-suf | --program-su | --program-s)
    ac_prev=program_suffix ;;
  -program-suffix=* | --program-suffix=* | --program-suffi=* \
  | --program-suff=* | --program-suf=* | --program-su=* | --program-s=*)
    program_suffix=$ac_optarg ;;

  -program-transform-name | --program-transform-name \
  | --program-transform-nam | --program-transform-na \
  | --program-transform-n | --program-transform- \
  | --program-transform | --program-transfor \
  | --program-transfo | --program-transf \
  | --program-trans | --program-tran \
  | --progr-tra | --program-tr | --program-t)
    ac_prev=program_transform_name ;;
  -program-transform-name=* | --program-transform-name=* \
  | --program-transform-nam=* | --program-transform-na=* \
  | --program-transform-n=* | --program-transform-=* \
  | --program-transform=* | --program-transfor=* \
  | --program-transfo=* | --program-transf=* \
  | --program-trans=* | --program-tran=* \
  | --progr-tra=* | --program-tr=* | --program-t=*)
    program_transform_name=$ac_optarg ;;

  -pdfdir | --pdfdir | --pdfdi | --pdfd | --pdf | --pd)
    ac_prev=pdfdir ;;
  -pdfdir=* | --pdfdir=* | --pdfdi=* | --pdfd=* | --pdf=* | --pd=*)
    pdfdir=$ac_optarg ;;

  -psdir | --psdir | --psdi | --psd | --ps)
    ac_prev=psdir ;;
  -psdir=* | --psdir=* | --psdi=* | --psd=* | --ps=*)
    psdir=$ac_optarg ;;

  -q | -quiet | --quiet | --quie | --qui | --qu | --q \
  | -silent | --silent | --silen | --sile | --sil)
    silent=yes ;;

  -runstatedir | --runstatedir | --runstatedi | --runstated \
  | --runstate | --runstat | --runsta | --runst | --runs \
  | --run | --ru | --r)
    ac_prev=runstatedir ;;
  -runstatedir=* | --runstatedir=* | --runstatedi=* | --runstated=* \
  | --runstate=* | --runstat=* | --runsta=* | --runst=* | --runs=* \
  | --run=* | --ru=* | --r=*)
    runstatedir=$ac_optarg ;;

  -sbindir | --sbindir | --sbindi | --sbind | --sbin | --sbi | --sb)
    ac_prev=sbindir ;;
  -sbindir=* | --sbindir=* | --sbindi=* | --sbind=* | --sbin=* \
  | --sbi=* | --sb=*)
    sbindir=$ac_optarg ;;

  -sharedstatedir | --sharedstatedir | --sharedstatedi \
  | --sharedstated | --sharedstate | --sharedstat | --sharedsta \
  | --sharedst | --shareds | --shared | --share | --shar \
  | --sha | --sh)
    ac_prev=sharedstatedir ;;
  -sharedstatedir=* | --sharedstatedir=* | --sharedstatedi=* \
  | --sharedstated=* | --sharedstate=* | --sharedstat=* | --sharedsta=* \
  | --sharedst=* | --shareds=* | --shared=* | --share=* | --shar=* \
  | --sha=* | --sh=*)
    sharedstatedir=$ac_optarg ;;

  -site | --site | --sit)
    ac_prev=site ;;
  -site=* | --site=* | --sit=*)
    site=$ac_optarg ;;

  -srcdir | --srcdir | --srcdi | --srcd | --src | --sr)
    ac_prev=srcdir ;;
  -srcdir=* | --srcdir=* | --srcdi=* | --srcd=* | --src=* | --sr=*)
    srcdir=$ac_optarg ;;

  -sysconfdir | --sysconfdir | --sysconfdi | --sysconfd | --sysconf \
  | --syscon | --sysco | --sysc | --sys | --sy)
    ac_prev=sysconfdir ;;
  -sysconfdir=* | --sysconfdir=* | --sysconfdi=* | --sysconfd=* | --sysconf=* \
  | --syscon=* | --sysco=* | --sysc=* | --sys=* | --sy=*)
    sysconfdir=$ac_optarg ;;

  -target | --target | --targe | --targ | --tar | --ta | --t)
    ac_prev=target_alias ;;
  -target=* | --target=* | --targe=* | --targ=* | --tar=* | --ta=* | --t=*)
    target_alias=$ac_optarg ;;

  -v | -verbose | --verbose | --verbos | --verbo | --verb)
    verbose=yes ;;

  -version | --version | --versio | --versi | --vers | -V)
    ac_init_version=: ;;

  -with-* | --with-*)
    ac_useropt=`expr "x$ac_option" : 'x-*with-\([^=]*\)'`
    # Reject names that are not valid shell variable names.
    expr "x$ac_useropt" : ".*[^-+._$as_cr_alnum]" >/dev/null &&
      as_fn_error $? "invalid package name: \`$ac_useropt'"
    ac_useropt_orig=$ac_useropt
    ac_useropt=`printf "%s\n" "$ac_useropt" | sed 's/[-+.]/_/g'`
    case $ac_user_opts in
      *"
"with_$ac_useropt"
"*) ;;
      *) ac_unrecognized_opts="$ac_unrecognized_opts$ac_unrecognized_sep--with-$ac_useropt_orig"
	 ac_unrecognized_sep=', ';;
    esac
    eval with_$ac_useropt=\$ac_optarg ;;

  -without-* | --without-*)
    ac_useropt=`expr "x$ac_option" : 'x-*without-\(.*\)'`
    # Reject names that are not valid shell variable names.
    expr "x$ac_useropt" : ".*[^-+._$as_cr_alnum]" >/dev/null &&
      as_fn_error $? "invalid package name: \`$ac_useropt'"
    ac_useropt_orig=$ac_useropt
    ac_useropt=`printf "%s\n" "$ac_useropt" | sed 's/[-+.]/_/g'`
    case $ac_user_opts in
      *"
"with_$ac_useropt"
"*) ;;
      *) ac_unrecognized_opts="$ac_unrecognized_opts$ac_unrecognized_sep--without-$ac_useropt_orig"
	 ac_unrecognized_sep=', ';;
    esac
    eval with_$ac_useropt=no ;;

  --x)
    # Obsolete; use --with-x.
    with_x=yes ;;

  -x-includes | --x-includes | --x-include | --x-includ | --x-inclu \
  | --x-incl | --x-inc | --x-in | --x-i)
    ac_prev=x_includes ;;
  -x-includes=* | --x-includes=* | --x-include=* | --x-includ=* | --x-inclu=* \
  | --x-incl=* | --x-inc=* | --x-in=* | --x-i=*)
    x_includes=$ac_optarg ;;

  -x-libraries | --x-libraries | --x-librarie | --x-librari \
  | --x-librar | --x-libra | --x-libr | --x-lib | --x-li | --x-l)
    ac_prev=x_libraries ;;
  -x-libraries=* | --x-libraries=* | --x-librarie=* | --x-librari=* \
  | --x-librar=* | --x-libra=* | --x-libr=* | --x-lib=* | --x-li=* | --x-l=*)
    x_libraries=$ac_optarg ;;

  -*) as_fn_error $? "unrecognized option: \`$ac_option'
Try \`$0 --help' for more information"
    ;;

  *=*)
    ac_envvar=`expr "x$ac_option" : 'x\([^=]*\)='`
    # Reject names that are not valid shell variable names.
    case $ac_envvar in #(
      '' | [0-9]* | *[!_$as_cr_alnum]* )
      as_fn_error $? "invalid variable name: \`$ac_envvar'" ;;
    esac
    eval $ac_envvar=\$ac_optarg
    export $ac_envvar ;;

  *)
    # FIXME: should be removed in autoconf 3.0.
    printf "%s\n" "$as_me: WARNING: you should use --build, --host, --target" >&2
    expr "x$ac_option" : ".*[^-._$as_cr_alnum]" >/dev/null &&
      printf "%s\n" "$as_me: WARNING: invalid host type: $ac_option" >&2
    : "${build_alias=$ac_option} ${host_alias=$ac_option} ${target_alias=$ac_option}"
    ;;

  esac
done

if test -n "$ac_prev"; then
  ac_option=--`echo $ac_prev | sed 's/_/-/g'`
  as_fn_error $? "missing argument to $ac_option"
fi

if test -n "$ac_unrecognized_opts"; then
  case $enable_option_checking in
    no) ;;
    fatal) as_fn_error $? "unrecognized options: $ac_unrecognized_opts" ;;
    *)     printf "%s\n" "$as_me: WARNING: unrecognized options: $ac_unrecognized_opts" >&2 ;;
  esac
fi

# Check all directory arguments for consistency.
for ac_var in	exec_prefix prefix bindir sbindir libexecdir datarootdir \
		datadir sysconfdir sharedstatedir localstatedir includedir \
		oldincludedir docdir infodir htmldir dvidir pdfdir psdir \
		libdir localedir mandir runstatedir
do
  eval ac_val=\$$ac_var
  # Remove trailing slashes.
  case $ac_val in
    */ )
      ac_val=`expr "X$ac_val" : 'X\(.*[^/]\)' \| "X$ac_val" : 'X\(.*\)'`
      eval $ac_var=\$ac_val;;
  esac
  # Be sure to have absolute directory names.
  case $ac_val in
    [\\/$]* | ?:[\\/]* )  continue;;
    NONE | '' ) case $ac_var in *prefix ) continue;; esac;;
  esac
  as_fn_error $? "expected an absolute directory name for --$ac_var: $ac_val"
done

# There might be people who depend on the old broken behavior: `$host'
# used to hold the argument of --host etc.
# FIXME: To remove some day.
build=$build_alias
host=$host_alias
target=$target_alias

# FIXME: To remove some day.
if test "x$host_alias" != x; then
  if test "x$build_alias" = x; then
    cross_compiling=maybe
  elif test "x$build_alias" != "x$host_alias"; then
    cross_compiling=yes
  fi
fi

ac_tool_prefix=
test -n "$host_alias" && ac_tool_prefix=$host_alias-

test "$silent" = yes && exec 6>/dev/null


ac_pwd=`pwd` && test -n "$ac_pwd" &&
ac_ls_di=`ls -di .` &&
ac_pwd_ls_di=`cd "$ac_pwd" && ls -di .` ||
  as_fn_error $? "working directory cannot be determined"
test "X$ac_ls_di" = "X$ac_pwd_ls_di" ||
  as_fn_error $? "pwd does not report name of working directory"


# Find the source files, if location was not specified.
if test -z "$srcdir"; then
  ac_srcdir_defaulted=yes
  # Try the directory containing this script, then the parent directory.
  ac_confdir=`$as_dirname -- "$as_myself" ||
$as_expr X"$as_myself" : 'X\(.*[^/]\)//*[^/][^/]*/*$' \| \
	 X"$as_myself" : 'X\(//\)[^/]' \| \
	 X"$as_myself" : 'X\(//\)$' \| \
	 X"$as_myself" : 'X\(/\)' \| . 2>/dev/null ||
printf "%s\n" X"$as_myself" |
    sed '/^X\(.*[^/]\)\/\/*[^/][^/]*\/*$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)[^/].*/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`
  srcdir=$ac_confdir
  if test ! -r "$srcdir/$ac_unique_file"; then
    srcdir=..
  fi
else
  ac_srcdir_defaulted=no
fi
if test ! -r "$srcdir/$ac_unique_file"; then
  test "$ac_srcdir_defaulted" = yes && srcdir="$ac_confdir or .."
  as_fn_error $? "cannot find sources ($ac_unique_file) in $srcdir"
fi
ac_msg="sources are in $srcdir, but \`cd $srcdir' does not work"
ac_abs_confdir=`(
	cd "$srcdir" && test -r "./$ac_unique_file" || as_fn_error $? "$ac_msg"
	pwd)`
# When building in place, set srcdir=.
if test "$ac_abs_confdir" = "$ac_pwd"; then
  srcdir=.
fi
# Remove unnecessary trailing slashes from srcdir.
# Double slashes in file names in object file debugging info
# mess up M-x gdb in Emacs.
case $srcdir in
*/) srcdir=`expr "X$srcdir" : 'X\(.*[^/]\)' \| "X$srcdir" : 'X\(.*\)'`;;
esac
for ac_var in $ac_precious_vars; do
  eval ac_env_${ac_var}_set=\${${ac_var}+set}
  eval ac_env_${ac_var}_value=\$${ac_var}
  eval ac_cv_env_${ac_var}_set=\${${ac_var}+set}
  eval ac_cv_env_${ac_var}_value=\$${ac_var}
done

#
# Report the --help message.
#
if test "$ac_init_help" = "long"; then
  # Omit some internal or obsolete options to make the list less imposing.
  # This message is too long to be a string in the A/UX 3.1 sh.
  cat <<_ACEOF
\`configure' configures pcap 1.11.0-PRE-GIT to adapt to many kinds of systems.

Usage: $0 [OPTION]... [VAR=VALUE]...

To assign environment variables (e.g., CC, CFLAGS...), specify them as
VAR=VALUE.  See below for descriptions of some of the useful variables.

Defaults for the options are specified in brackets.

Configuration:
  -h, --help              display this help and exit
      --help=short        display options specific to this package
      --help=recursive    display the short help of all the included packages
  -V, --version           display version information and exit
  -q, --quiet, --silent   do not print \`checking ...' messages
      --cache-file=FILE   cache test results in FILE [disabled]
  -C, --config-cache      alias for \`--cache-file=config.cache'
  -n, --no-create         do not create output files
      --srcdir=DIR        find the sources in DIR [configure dir or \`..']

Installation directories:
  --prefix=PREFIX         install architecture-independent files in PREFIX
                          @<:@@S|@ac_default_prefix@:>@
  --exec-prefix=EPREFIX   install architecture-dependent files in EPREFIX
                          @<:@PREFIX@:>@

By default, \`make install' will install all the files in
\`$ac_default_prefix/bin', \`$ac_default_prefix/lib' etc.  You can specify
an installation prefix other than \`$ac_default_prefix' using \`--prefix',
for instance \`--prefix=\$HOME'.

For better control, use the options below.

Fine tuning of the installation directories:
  --bindir=DIR            user executables [EPREFIX/bin]
  --sbindir=DIR           system admin executables [EPREFIX/sbin]
  --libexecdir=DIR        program executables [EPREFIX/libexec]
  --sysconfdir=DIR        read-only single-machine data [PREFIX/etc]
  --sharedstatedir=DIR    modifiable architecture-independent data [PREFIX/com]
  --localstatedir=DIR     modifiable single-machine data [PREFIX/var]
  --runstatedir=DIR       modifiable per-process data [LOCALSTATEDIR/run]
  --libdir=DIR            object code libraries [EPREFIX/lib]
  --includedir=DIR        C header files [PREFIX/include]
  --oldincludedir=DIR     C header files for non-gcc [/usr/include]
  --datarootdir=DIR       read-only arch.-independent data root [PREFIX/share]
  --datadir=DIR           read-only architecture-independent data [DATAROOTDIR]
  --infodir=DIR           info documentation [DATAROOTDIR/info]
  --localedir=DIR         locale-dependent data [DATAROOTDIR/locale]
  --mandir=DIR            man documentation [DATAROOTDIR/man]
  --docdir=DIR            documentation root @<:@DATAROOTDIR/doc/libpcap@:>@
  --htmldir=DIR           html documentation [DOCDIR]
  --dvidir=DIR            dvi documentation [DOCDIR]
  --pdfdir=DIR            pdf documentation [DOCDIR]
  --psdir=DIR             ps documentation [DOCDIR]
_ACEOF

  cat <<\_ACEOF

System types:
  --build=BUILD     configure for building on BUILD [guessed]
  --host=HOST       cross-compile to build programs to run on HOST [BUILD]
_ACEOF
fi

if test -n "$ac_init_help"; then
  case $ac_init_help in
     short | recursive ) echo "Configuration of pcap 1.11.0-PRE-GIT:";;
   esac
  cat <<\_ACEOF

Optional Features:
  --disable-option-checking  ignore unrecognized --enable/--with options
  --disable-FEATURE       do not include FEATURE (same as --enable-FEATURE=no)
  --enable-FEATURE[=ARG]  include FEATURE [ARG=yes]
  --disable-largefile     omit support for large files
  --enable-instrument-functions 
                          enable instrument functions code @<:@default=no@:>@
  --disable-protochain    disable \"protochain\" insn
  --enable-dag-tx         enable Endace DAG transmit support (EXPERIMENTAL)
                          @<:@default=no@:>@
		
  --enable-remote         enable remote packet capture @<:@default=no@:>@
  --enable-optimizer-dbg  build optimizer debugging code
  --enable-yydebug        build parser debugging code
  --disable-universal     don't build universal on macOS
  --enable-shared         build shared libraries @<:@default=yes, if support
                          available@:>@
  --enable-usb            enable Linux usbmon USB capture support
                          @<:@default=yes, if support available@:>@
  --enable-netmap         enable netmap support @<:@default=yes, if support
                          available@:>@
  --enable-bluetooth      enable Bluetooth support @<:@default=yes, if support
                          available@:>@
  --enable-dbus           enable D-Bus capture support @<:@default=yes, if
                          support available@:>@
  --enable-rdma           enable RDMA capture support @<:@default=yes, if support
                          available@:>@

Optional Packages:
  --with-PACKAGE[=ARG]    use PACKAGE [ARG=yes]
  --without-PACKAGE       do not use PACKAGE (same as --with-PACKAGE=no)
  --without-gcc           don't use gcc
  --with-pcap=TYPE        use packet capture TYPE
  --without-libnl         disable libnl support @<:@default=yes, on Linux, if
                          present@:>@
  --with-dag@<:@=DIR@:>@        include Endace DAG support (located in directory
                          DIR, if supplied). @<:@default=yes, if present@:>@
  --with-dag-includes=IDIR 
                          Endace DAG include directory, if not DIR/include
  --with-dag-libraries=LDIR 
                          Endace DAG library directory, if not DIR/lib
  --with-snf@<:@=DIR@:>@        include Myricom SNF support (located in directory
                          DIR, if supplied). @<:@default=yes, if present@:>@
  --with-snf-includes=IDIR 
                          Myricom SNF include directory, if not DIR/include
  --with-snf-libraries=LDIR 
                          Myricom SNF library directory, if not DIR/lib
  --with-dpdk@<:@=DIR@:>@       include DPDK support (located in directory DIR, if
                          supplied). @<:@default=yes, if present@:>@

Some influential environment variables:
  CC          C compiler command
  CFLAGS      C compiler flags
  LDFLAGS     linker flags, e.g. -L<lib dir> if you have libraries in a
              nonstandard directory <lib dir>
  LIBS        libraries to pass to the linker, e.g. -l<library>
  CPPFLAGS    (Objective) C/C++ preprocessor flags, e.g. -I<include dir> if
              you have headers in a nonstandard directory <include dir>
  PKG_CONFIG  path to pkg-config utility
  PKG_CONFIG_PATH 
              directories to add to pkg-config's search path
  PKG_CONFIG_LIBDIR 
              path overriding pkg-config's built-in search path
  LIBNL_CFLAGS 
              C compiler flags for libnl-genl-3.0, overriding pkg-config
  LIBNL_LIBS  linker flags for libnl-genl-3.0, overriding pkg-config
  LIBNL_LIBS_STATIC 
              static-link linker flags for libnl-genl-3.0, overriding
              pkg-config
  OPENSSL_CFLAGS 
              C compiler flags for openssl, overriding pkg-config
  OPENSSL_LIBS 
              linker flags for openssl, overriding pkg-config
  OPENSSL_LIBS_STATIC 
              static-link linker flags for openssl, overriding pkg-config
  DPDK_CFLAGS C compiler flags for libdpdk, overriding pkg-config
  DPDK_LIBS   linker flags for libdpdk, overriding pkg-config
  DPDK_LIBS_STATIC 
              static-link linker flags for libdpdk, overriding pkg-config
  DBUS_CFLAGS C compiler flags for dbus-1, overriding pkg-config
  DBUS_LIBS   linker flags for dbus-1, overriding pkg-config
  DBUS_LIBS_STATIC 
              static-link linker flags for dbus-1, overriding pkg-config
  LIBIBVERBS_CFLAGS 
              C compiler flags for libibverbs, overriding pkg-config
  LIBIBVERBS_LIBS 
              linker flags for libibverbs, overriding pkg-config
  LIBIBVERBS_LIBS_STATIC 
              static-link linker flags for libibverbs, overriding pkg-config

Use these variables to override the choices made by `configure' or to help
it to find libraries and programs with nonstandard names/locations.

Report bugs to <https://github.com/the-tcpdump-group/libpcap/issues>.
pcap home page: <https://www.tcpdump.org/>.
_ACEOF
ac_status=$?
fi

if test "$ac_init_help" = "recursive"; then
  # If there are subdirs, report their specific --help.
  for ac_dir in : $ac_subdirs_all; do test "x$ac_dir" = x: && continue
    test -d "$ac_dir" ||
      { cd "$srcdir" && ac_pwd=`pwd` && srcdir=. && test -d "$ac_dir"; } ||
      continue
    ac_builddir=.

case "$ac_dir" in
.) ac_dir_suffix= ac_top_builddir_sub=. ac_top_build_prefix= ;;
*)
  ac_dir_suffix=/`printf "%s\n" "$ac_dir" | sed 's|^\.[\\/]||'`
  # A ".." for each directory in $ac_dir_suffix.
  ac_top_builddir_sub=`printf "%s\n" "$ac_dir_suffix" | sed 's|/[^\\/]*|/..|g;s|/||'`
  case $ac_top_builddir_sub in
  "") ac_top_builddir_sub=. ac_top_build_prefix= ;;
  *)  ac_top_build_prefix=$ac_top_builddir_sub/ ;;
  esac ;;
esac
ac_abs_top_builddir=$ac_pwd
ac_abs_builddir=$ac_pwd$ac_dir_suffix
# for backward compatibility:
ac_top_builddir=$ac_top_build_prefix

case $srcdir in
  .)  # We are building in place.
    ac_srcdir=.
    ac_top_srcdir=$ac_top_builddir_sub
    ac_abs_top_srcdir=$ac_pwd ;;
  [\\/]* | ?:[\\/]* )  # Absolute name.
    ac_srcdir=$srcdir$ac_dir_suffix;
    ac_top_srcdir=$srcdir
    ac_abs_top_srcdir=$srcdir ;;
  *) # Relative name.
    ac_srcdir=$ac_top_build_prefix$srcdir$ac_dir_suffix
    ac_top_srcdir=$ac_top_build_prefix$srcdir
    ac_abs_top_srcdir=$ac_pwd/$srcdir ;;
esac
ac_abs_srcdir=$ac_abs_top_srcdir$ac_dir_suffix

    cd "$ac_dir" || { ac_status=$?; continue; }
    # Check for configure.gnu first; this name is used for a wrapper for
    # Metaconfig's "Configure" on case-insensitive file systems.
    if test -f "$ac_srcdir/configure.gnu"; then
      echo &&
      $SHELL "$ac_srcdir/configure.gnu" --help=recursive
    elif test -f "$ac_srcdir/configure"; then
      echo &&
      $SHELL "$ac_srcdir/configure" --help=recursive
    else
      printf "%s\n" "$as_me: WARNING: no configuration information is in $ac_dir" >&2
    fi || ac_status=$?
    cd "$ac_pwd" || { ac_status=$?; break; }
  done
fi

test -n "$ac_init_help" && exit $ac_status
if $ac_init_version; then
  cat <<\_ACEOF
pcap configure 1.11.0-PRE-GIT
generated by GNU Autoconf 2.71

Copyright (C) 2021 Free Software Foundation, Inc.
This configure script is free software; the Free Software Foundation
gives unlimited permission to copy, distribute and modify it.
_ACEOF
  exit
fi

## ------------------------ ##
## Autoconf initialization. ##
## ------------------------ ##

@%:@ ac_fn_c_try_compile LINENO
@%:@ --------------------------
@%:@ Try to compile conftest.@S|@ac_ext, and return whether this succeeded.
ac_fn_c_try_compile ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  rm -f conftest.$ac_objext conftest.beam
  if { { ac_try="$ac_compile"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_compile") 2>conftest.err
  ac_status=$?
  if test -s conftest.err; then
    grep -v '^ *+' conftest.err >conftest.er1
    cat conftest.er1 >&5
    mv -f conftest.er1 conftest.err
  fi
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; } && {
	 test -z "$ac_c_werror_flag" ||
	 test ! -s conftest.err
       } && test -s conftest.$ac_objext
then :
  ac_retval=0
else $as_nop
  printf "%s\n" "$as_me: failed program was:" >&5
sed 's/^/| /' conftest.$ac_ext >&5

	ac_retval=1
fi
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno
  as_fn_set_status $ac_retval

} @%:@ ac_fn_c_try_compile

@%:@ ac_fn_c_try_link LINENO
@%:@ -----------------------
@%:@ Try to link conftest.@S|@ac_ext, and return whether this succeeded.
ac_fn_c_try_link ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  rm -f conftest.$ac_objext conftest.beam conftest$ac_exeext
  if { { ac_try="$ac_link"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_link") 2>conftest.err
  ac_status=$?
  if test -s conftest.err; then
    grep -v '^ *+' conftest.err >conftest.er1
    cat conftest.er1 >&5
    mv -f conftest.er1 conftest.err
  fi
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; } && {
	 test -z "$ac_c_werror_flag" ||
	 test ! -s conftest.err
       } && test -s conftest$ac_exeext && {
	 test "$cross_compiling" = yes ||
	 test -x conftest$ac_exeext
       }
then :
  ac_retval=0
else $as_nop
  printf "%s\n" "$as_me: failed program was:" >&5
sed 's/^/| /' conftest.$ac_ext >&5

	ac_retval=1
fi
  # Delete the IPA/IPO (Inter Procedural Analysis/Optimization) information
  # created by the PGI compiler (conftest_ipa8_conftest.oo), as it would
  # interfere with the next link command; also delete a directory that is
  # left behind by Apple's compiler.  We do this before executing the actions.
  rm -rf conftest.dSYM conftest_ipa8_conftest.oo
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno
  as_fn_set_status $ac_retval

} @%:@ ac_fn_c_try_link

@%:@ ac_fn_c_try_run LINENO
@%:@ ----------------------
@%:@ Try to run conftest.@S|@ac_ext, and return whether this succeeded. Assumes that
@%:@ executables *can* be run.
ac_fn_c_try_run ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  if { { ac_try="$ac_link"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_link") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; } && { ac_try='./conftest$ac_exeext'
  { { case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_try") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }; }
then :
  ac_retval=0
else $as_nop
  printf "%s\n" "$as_me: program exited with status $ac_status" >&5
       printf "%s\n" "$as_me: failed program was:" >&5
sed 's/^/| /' conftest.$ac_ext >&5

       ac_retval=$ac_status
fi
  rm -rf conftest.dSYM conftest_ipa8_conftest.oo
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno
  as_fn_set_status $ac_retval

} @%:@ ac_fn_c_try_run

@%:@ ac_fn_c_compute_int LINENO EXPR VAR INCLUDES
@%:@ --------------------------------------------
@%:@ Tries to find the compile-time value of EXPR in a program that includes
@%:@ INCLUDES, setting VAR accordingly. Returns whether the value could be
@%:@ computed
ac_fn_c_compute_int ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  if test "$cross_compiling" = yes; then
    # Depending upon the size, compute the lo and hi bounds.
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
static int test_array @<:@1 - 2 * !(($2) >= 0)@:>@;
test_array @<:@0@:>@ = 0;
return test_array @<:@0@:>@;

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_lo=0 ac_mid=0
  while :; do
    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
static int test_array @<:@1 - 2 * !(($2) <= $ac_mid)@:>@;
test_array @<:@0@:>@ = 0;
return test_array @<:@0@:>@;

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_hi=$ac_mid; break
else $as_nop
  as_fn_arith $ac_mid + 1 && ac_lo=$as_val
			if test $ac_lo -le $ac_mid; then
			  ac_lo= ac_hi=
			  break
			fi
			as_fn_arith 2 '*' $ac_mid + 1 && ac_mid=$as_val
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
  done
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
static int test_array @<:@1 - 2 * !(($2) < 0)@:>@;
test_array @<:@0@:>@ = 0;
return test_array @<:@0@:>@;

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_hi=-1 ac_mid=-1
  while :; do
    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
static int test_array @<:@1 - 2 * !(($2) >= $ac_mid)@:>@;
test_array @<:@0@:>@ = 0;
return test_array @<:@0@:>@;

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_lo=$ac_mid; break
else $as_nop
  as_fn_arith '(' $ac_mid ')' - 1 && ac_hi=$as_val
			if test $ac_mid -le $ac_hi; then
			  ac_lo= ac_hi=
			  break
			fi
			as_fn_arith 2 '*' $ac_mid && ac_mid=$as_val
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
  done
else $as_nop
  ac_lo= ac_hi=
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
# Binary search between lo and hi bounds.
while test "x$ac_lo" != "x$ac_hi"; do
  as_fn_arith '(' $ac_hi - $ac_lo ')' / 2 + $ac_lo && ac_mid=$as_val
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
static int test_array @<:@1 - 2 * !(($2) <= $ac_mid)@:>@;
test_array @<:@0@:>@ = 0;
return test_array @<:@0@:>@;

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_hi=$ac_mid
else $as_nop
  as_fn_arith '(' $ac_mid ')' + 1 && ac_lo=$as_val
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
done
case $ac_lo in @%:@((
?*) eval "$3=\$ac_lo"; ac_retval=0 ;;
'') ac_retval=1 ;;
esac
  else
    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
static long int longval (void) { return $2; }
static unsigned long int ulongval (void) { return $2; }
@%:@include <stdio.h>
@%:@include <stdlib.h>
int
main (void)
{

  FILE *f = fopen ("conftest.val", "w");
  if (! f)
    return 1;
  if (($2) < 0)
    {
      long int i = longval ();
      if (i != ($2))
	return 1;
      fprintf (f, "%ld", i);
    }
  else
    {
      unsigned long int i = ulongval ();
      if (i != ($2))
	return 1;
      fprintf (f, "%lu", i);
    }
  /* Do not output a trailing newline, as this causes \r\n confusion
     on some platforms.  */
  return ferror (f) || fclose (f) != 0;

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_run "$LINENO"
then :
  echo >>conftest.val; read $3 <conftest.val; ac_retval=0
else $as_nop
  ac_retval=1
fi
rm -f core *.core core.conftest.* gmon.out bb.out conftest$ac_exeext \
  conftest.$ac_objext conftest.beam conftest.$ac_ext
rm -f conftest.val

  fi
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno
  as_fn_set_status $ac_retval

} @%:@ ac_fn_c_compute_int

@%:@ ac_fn_c_check_header_compile LINENO HEADER VAR INCLUDES
@%:@ -------------------------------------------------------
@%:@ Tests whether HEADER exists and can be compiled using the include files in
@%:@ INCLUDES, setting the cache variable VAR accordingly.
ac_fn_c_check_header_compile ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $2" >&5
printf %s "checking for $2... " >&6; }
if eval test \${$3+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
@%:@include <$2>
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  eval "$3=yes"
else $as_nop
  eval "$3=no"
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
eval ac_res=\$$3
	       { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_res" >&5
printf "%s\n" "$ac_res" >&6; }
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno

} @%:@ ac_fn_c_check_header_compile

@%:@ ac_fn_c_check_member LINENO AGGR MEMBER VAR INCLUDES
@%:@ ----------------------------------------------------
@%:@ Tries to find if the field MEMBER exists in type AGGR, after including
@%:@ INCLUDES, setting cache variable VAR accordingly.
ac_fn_c_check_member ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $2.$3" >&5
printf %s "checking for $2.$3... " >&6; }
if eval test \${$4+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$5
int
main (void)
{
static $2 ac_aggr;
if (ac_aggr.$3)
return 0;
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  eval "$4=yes"
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$5
int
main (void)
{
static $2 ac_aggr;
if (sizeof ac_aggr.$3)
return 0;
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  eval "$4=yes"
else $as_nop
  eval "$4=no"
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
eval ac_res=\$$4
	       { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_res" >&5
printf "%s\n" "$ac_res" >&6; }
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno

} @%:@ ac_fn_c_check_member

@%:@ ac_fn_c_check_func LINENO FUNC VAR
@%:@ ----------------------------------
@%:@ Tests whether FUNC exists, setting the cache variable VAR accordingly
ac_fn_c_check_func ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $2" >&5
printf %s "checking for $2... " >&6; }
if eval test \${$3+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
/* Define $2 to an innocuous variant, in case <limits.h> declares $2.
   For example, HP-UX 11i <limits.h> declares gettimeofday.  */
#define $2 innocuous_$2

/* System header to define __stub macros and hopefully few prototypes,
   which can conflict with char $2 (); below.  */

#include <limits.h>
#undef $2

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
#ifdef __cplusplus
extern "C"
#endif
char $2 ();
/* The GNU C library defines this for functions which it implements
    to always fail with ENOSYS.  Some functions are actually named
    something starting with __ and the normal name is an alias.  */
#if defined __stub_$2 || defined __stub___$2
choke me
#endif

int
main (void)
{
return $2 ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  eval "$3=yes"
else $as_nop
  eval "$3=no"
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
fi
eval ac_res=\$$3
	       { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_res" >&5
printf "%s\n" "$ac_res" >&6; }
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno

} @%:@ ac_fn_c_check_func

@%:@ ac_fn_check_decl LINENO SYMBOL VAR INCLUDES EXTRA-OPTIONS FLAG-VAR
@%:@ ------------------------------------------------------------------
@%:@ Tests whether SYMBOL is declared in INCLUDES, setting cache variable VAR
@%:@ accordingly. Pass EXTRA-OPTIONS to the compiler, using FLAG-VAR.
ac_fn_check_decl ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  as_decl_name=`echo $2|sed 's/ *(.*//'`
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether $as_decl_name is declared" >&5
printf %s "checking whether $as_decl_name is declared... " >&6; }
if eval test \${$3+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  as_decl_use=`echo $2|sed -e 's/(/((/' -e 's/)/) 0&/' -e 's/,/) 0& (/g'`
  eval ac_save_FLAGS=\$$6
  as_fn_append $6 " $5"
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
#ifndef $as_decl_name
#ifdef __cplusplus
  (void) $as_decl_use;
#else
  (void) $as_decl_name;
#endif
#endif

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  eval "$3=yes"
else $as_nop
  eval "$3=no"
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
  eval $6=\$ac_save_FLAGS

fi
eval ac_res=\$$3
	       { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_res" >&5
printf "%s\n" "$ac_res" >&6; }
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno

} @%:@ ac_fn_check_decl

@%:@ ac_fn_c_check_type LINENO TYPE VAR INCLUDES
@%:@ -------------------------------------------
@%:@ Tests whether TYPE exists after having included INCLUDES, setting cache
@%:@ variable VAR accordingly.
ac_fn_c_check_type ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $2" >&5
printf %s "checking for $2... " >&6; }
if eval test \${$3+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  eval "$3=no"
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
if (sizeof ($2))
	 return 0;
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
if (sizeof (($2)))
	    return 0;
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
else $as_nop
  eval "$3=yes"
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
eval ac_res=\$$3
	       { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_res" >&5
printf "%s\n" "$ac_res" >&6; }
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno

} @%:@ ac_fn_c_check_type
ac_configure_args_raw=
for ac_arg
do
  case $ac_arg in
  *\'*)
    ac_arg=`printf "%s\n" "$ac_arg" | sed "s/'/'\\\\\\\\''/g"` ;;
  esac
  as_fn_append ac_configure_args_raw " '$ac_arg'"
done

case $ac_configure_args_raw in
  *$as_nl*)
    ac_safe_unquote= ;;
  *)
    ac_unsafe_z='|&;<>()$`\\"*?@<:@ ''	' # This string ends in space, tab.
    ac_unsafe_a="$ac_unsafe_z#~"
    ac_safe_unquote="s/ '\\([^$ac_unsafe_a][^$ac_unsafe_z]*\\)'/ \\1/g"
    ac_configure_args_raw=`      printf "%s\n" "$ac_configure_args_raw" | sed "$ac_safe_unquote"`;;
esac

cat >config.log <<_ACEOF
This file contains any messages produced by compilers while
running configure, to aid debugging if configure makes a mistake.

It was created by pcap $as_me 1.11.0-PRE-GIT, which was
generated by GNU Autoconf 2.71.  Invocation command line was

  $ $0$ac_configure_args_raw

_ACEOF
exec 5>>config.log
{
cat <<_ASUNAME
## --------- ##
## Platform. ##
## --------- ##

hostname = `(hostname || uname -n) 2>/dev/null | sed 1q`
uname -m = `(uname -m) 2>/dev/null || echo unknown`
uname -r = `(uname -r) 2>/dev/null || echo unknown`
uname -s = `(uname -s) 2>/dev/null || echo unknown`
uname -v = `(uname -v) 2>/dev/null || echo unknown`

/usr/bin/uname -p = `(/usr/bin/uname -p) 2>/dev/null || echo unknown`
/bin/uname -X     = `(/bin/uname -X) 2>/dev/null     || echo unknown`

/bin/arch              = `(/bin/arch) 2>/dev/null              || echo unknown`
/usr/bin/arch -k       = `(/usr/bin/arch -k) 2>/dev/null       || echo unknown`
/usr/convex/getsysinfo = `(/usr/convex/getsysinfo) 2>/dev/null || echo unknown`
/usr/bin/hostinfo      = `(/usr/bin/hostinfo) 2>/dev/null      || echo unknown`
/bin/machine           = `(/bin/machine) 2>/dev/null           || echo unknown`
/usr/bin/oslevel       = `(/usr/bin/oslevel) 2>/dev/null       || echo unknown`
/bin/universe          = `(/bin/universe) 2>/dev/null          || echo unknown`

_ASUNAME

as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    printf "%s\n" "PATH: $as_dir"
  done
IFS=$as_save_IFS

} >&5

cat >&5 <<_ACEOF


## ----------- ##
## Core tests. ##
## ----------- ##

_ACEOF


# Keep a trace of the command line.
# Strip out --no-create and --no-recursion so they do not pile up.
# Strip out --silent because we don't want to record it for future runs.
# Also quote any args containing shell meta-characters.
# Make two passes to allow for proper duplicate-argument suppression.
ac_configure_args=
ac_configure_args0=
ac_configure_args1=
ac_must_keep_next=false
for ac_pass in 1 2
do
  for ac_arg
  do
    case $ac_arg in
    -no-create | --no-c* | -n | -no-recursion | --no-r*) continue ;;
    -q | -quiet | --quiet | --quie | --qui | --qu | --q \
    | -silent | --silent | --silen | --sile | --sil)
      continue ;;
    *\'*)
      ac_arg=`printf "%s\n" "$ac_arg" | sed "s/'/'\\\\\\\\''/g"` ;;
    esac
    case $ac_pass in
    1) as_fn_append ac_configure_args0 " '$ac_arg'" ;;
    2)
      as_fn_append ac_configure_args1 " '$ac_arg'"
      if test $ac_must_keep_next = true; then
	ac_must_keep_next=false # Got value, back to normal.
      else
	case $ac_arg in
	  *=* | --config-cache | -C | -disable-* | --disable-* \
	  | -enable-* | --enable-* | -gas | --g* | -nfp | --nf* \
	  | -q | -quiet | --q* | -silent | --sil* | -v | -verb* \
	  | -with-* | --with-* | -without-* | --without-* | --x)
	    case "$ac_configure_args0 " in
	      "$ac_configure_args1"*" '$ac_arg' "* ) continue ;;
	    esac
	    ;;
	  -* ) ac_must_keep_next=true ;;
	esac
      fi
      as_fn_append ac_configure_args " '$ac_arg'"
      ;;
    esac
  done
done
{ ac_configure_args0=; unset ac_configure_args0;}
{ ac_configure_args1=; unset ac_configure_args1;}

# When interrupted or exit'd, cleanup temporary files, and complete
# config.log.  We remove comments because anyway the quotes in there
# would cause problems or look ugly.
# WARNING: Use '\'' to represent an apostrophe within the trap.
# WARNING: Do not start the trap code with a newline, due to a FreeBSD 4.0 bug.
trap 'exit_status=$?
  # Sanitize IFS.
  IFS=" ""	$as_nl"
  # Save into config.log some information that might help in debugging.
  {
    echo

    printf "%s\n" "## ---------------- ##
## Cache variables. ##
## ---------------- ##"
    echo
    # The following way of writing the cache mishandles newlines in values,
(
  for ac_var in `(set) 2>&1 | sed -n '\''s/^\([a-zA-Z_][a-zA-Z0-9_]*\)=.*/\1/p'\''`; do
    eval ac_val=\$$ac_var
    case $ac_val in #(
    *${as_nl}*)
      case $ac_var in #(
      *_cv_*) { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: cache variable $ac_var contains a newline" >&5
printf "%s\n" "$as_me: WARNING: cache variable $ac_var contains a newline" >&2;} ;;
      esac
      case $ac_var in #(
      _ | IFS | as_nl) ;; #(
      BASH_ARGV | BASH_SOURCE) eval $ac_var= ;; #(
      *) { eval $ac_var=; unset $ac_var;} ;;
      esac ;;
    esac
  done
  (set) 2>&1 |
    case $as_nl`(ac_space='\'' '\''; set) 2>&1` in #(
    *${as_nl}ac_space=\ *)
      sed -n \
	"s/'\''/'\''\\\\'\'''\''/g;
	  s/^\\([_$as_cr_alnum]*_cv_[_$as_cr_alnum]*\\)=\\(.*\\)/\\1='\''\\2'\''/p"
      ;; #(
    *)
      sed -n "/^[_$as_cr_alnum]*_cv_[_$as_cr_alnum]*=/p"
      ;;
    esac |
    sort
)
    echo

    printf "%s\n" "## ----------------- ##
## Output variables. ##
## ----------------- ##"
    echo
    for ac_var in $ac_subst_vars
    do
      eval ac_val=\$$ac_var
      case $ac_val in
      *\'\''*) ac_val=`printf "%s\n" "$ac_val" | sed "s/'\''/'\''\\\\\\\\'\'''\''/g"`;;
      esac
      printf "%s\n" "$ac_var='\''$ac_val'\''"
    done | sort
    echo

    if test -n "$ac_subst_files"; then
      printf "%s\n" "## ------------------- ##
## File substitutions. ##
## ------------------- ##"
      echo
      for ac_var in $ac_subst_files
      do
	eval ac_val=\$$ac_var
	case $ac_val in
	*\'\''*) ac_val=`printf "%s\n" "$ac_val" | sed "s/'\''/'\''\\\\\\\\'\'''\''/g"`;;
	esac
	printf "%s\n" "$ac_var='\''$ac_val'\''"
      done | sort
      echo
    fi

    if test -s confdefs.h; then
      printf "%s\n" "## ----------- ##
## confdefs.h. ##
## ----------- ##"
      echo
      cat confdefs.h
      echo
    fi
    test "$ac_signal" != 0 &&
      printf "%s\n" "$as_me: caught signal $ac_signal"
    printf "%s\n" "$as_me: exit $exit_status"
  } >&5
  rm -f core *.core core.conftest.* &&
    rm -f -r conftest* confdefs* conf$$* $ac_clean_files &&
    exit $exit_status
' 0
for ac_signal in 1 2 13 15; do
  trap 'ac_signal='$ac_signal'; as_fn_exit 1' $ac_signal
done
ac_signal=0

# confdefs.h avoids OS command line length limits that DEFS can exceed.
rm -f -r conftest* confdefs.h

printf "%s\n" "/* confdefs.h */" > confdefs.h

# Predefined preprocessor variables.

printf "%s\n" "@%:@define PACKAGE_NAME \"$PACKAGE_NAME\"" >>confdefs.h

printf "%s\n" "@%:@define PACKAGE_TARNAME \"$PACKAGE_TARNAME\"" >>confdefs.h

printf "%s\n" "@%:@define PACKAGE_VERSION \"$PACKAGE_VERSION\"" >>confdefs.h

printf "%s\n" "@%:@define PACKAGE_STRING \"$PACKAGE_STRING\"" >>confdefs.h

printf "%s\n" "@%:@define PACKAGE_BUGREPORT \"$PACKAGE_BUGREPORT\"" >>confdefs.h

printf "%s\n" "@%:@define PACKAGE_URL \"$PACKAGE_URL\"" >>confdefs.h


# Let the site file select an alternate cache file if it wants to.
# Prefer an explicitly selected file to automatically selected ones.
if test -n "$CONFIG_SITE"; then
  ac_site_files="$CONFIG_SITE"
elif test "x$prefix" != xNONE; then
  ac_site_files="$prefix/share/config.site $prefix/etc/config.site"
else
  ac_site_files="$ac_default_prefix/share/config.site $ac_default_prefix/etc/config.site"
fi

for ac_site_file in $ac_site_files
do
  case $ac_site_file in @%:@(
  */*) :
     ;; @%:@(
  *) :
    ac_site_file=./$ac_site_file ;;
esac
  if test -f "$ac_site_file" && test -r "$ac_site_file"; then
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: loading site script $ac_site_file" >&5
printf "%s\n" "$as_me: loading site script $ac_site_file" >&6;}
    sed 's/^/| /' "$ac_site_file" >&5
    . "$ac_site_file" \
      || { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error $? "failed to load site script $ac_site_file
See \`config.log' for more details" "$LINENO" 5; }
  fi
done

if test -r "$cache_file"; then
  # Some versions of bash will fail to source /dev/null (special files
  # actually), so we avoid doing that.  DJGPP emulates it as a regular file.
  if test /dev/null != "$cache_file" && test -f "$cache_file"; then
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: loading cache $cache_file" >&5
printf "%s\n" "$as_me: loading cache $cache_file" >&6;}
    case $cache_file in
      [\\/]* | ?:[\\/]* ) . "$cache_file";;
      *)                      . "./$cache_file";;
    esac
  fi
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: creating cache $cache_file" >&5
printf "%s\n" "$as_me: creating cache $cache_file" >&6;}
  >$cache_file
fi

# Test code for whether the C compiler supports C89 (global declarations)
ac_c_conftest_c89_globals='
/* Does the compiler advertise C89 conformance?
   Do not test the value of __STDC__, because some compilers set it to 0
   while being otherwise adequately conformant. */
#if !defined __STDC__
# error "Compiler does not advertise C89 conformance"
#endif

#include <stddef.h>
#include <stdarg.h>
struct stat;
/* Most of the following tests are stolen from RCS 5.7 src/conf.sh.  */
struct buf { int x; };
struct buf * (*rcsopen) (struct buf *, struct stat *, int);
static char *e (p, i)
     char **p;
     int i;
{
  return p[i];
}
static char *f (char * (*g) (char **, int), char **p, ...)
{
  char *s;
  va_list v;
  va_start (v,p);
  s = g (p, va_arg (v,int));
  va_end (v);
  return s;
}

/* OSF 4.0 Compaq cc is some sort of almost-ANSI by default.  It has
   function prototypes and stuff, but not \xHH hex character constants.
   These do not provoke an error unfortunately, instead are silently treated
   as an "x".  The following induces an error, until -std is added to get
   proper ANSI mode.  Curiously \x00 != x always comes out true, for an
   array size at least.  It is necessary to write \x00 == 0 to get something
   that is true only with -std.  */
int osf4_cc_array ['\''\x00'\'' == 0 ? 1 : -1];

/* IBM C 6 for AIX is almost-ANSI by default, but it replaces macro parameters
   inside strings and character constants.  */
#define FOO(x) '\''x'\''
int xlc6_cc_array[FOO(a) == '\''x'\'' ? 1 : -1];

int test (int i, double x);
struct s1 {int (*f) (int a);};
struct s2 {int (*f) (double a);};
int pairnames (int, char **, int *(*)(struct buf *, struct stat *, int),
               int, int);'

# Test code for whether the C compiler supports C89 (body of main).
ac_c_conftest_c89_main='
ok |= (argc == 0 || f (e, argv, 0) != argv[0] || f (e, argv, 1) != argv[1]);
'

# Test code for whether the C compiler supports C99 (global declarations)
ac_c_conftest_c99_globals='
// Does the compiler advertise C99 conformance?
#if !defined __STDC_VERSION__ || __STDC_VERSION__ < 199901L
# error "Compiler does not advertise C99 conformance"
#endif

#include <stdbool.h>
extern int puts (const char *);
extern int printf (const char *, ...);
extern int dprintf (int, const char *, ...);
extern void *malloc (size_t);

// Check varargs macros.  These examples are taken from C99 6.10.3.5.
// dprintf is used instead of fprintf to avoid needing to declare
// FILE and stderr.
#define debug(...) dprintf (2, __VA_ARGS__)
#define showlist(...) puts (#__VA_ARGS__)
#define report(test,...) ((test) ? puts (#test) : printf (__VA_ARGS__))
static void
test_varargs_macros (void)
{
  int x = 1234;
  int y = 5678;
  debug ("Flag");
  debug ("X = %d\n", x);
  showlist (The first, second, and third items.);
  report (x>y, "x is %d but y is %d", x, y);
}

// Check long long types.
#define BIG64 18446744073709551615ull
#define BIG32 4294967295ul
#define BIG_OK (BIG64 / BIG32 == 4294967297ull && BIG64 % BIG32 == 0)
#if !BIG_OK
  #error "your preprocessor is broken"
#endif
#if BIG_OK
#else
  #error "your preprocessor is broken"
#endif
static long long int bignum = -9223372036854775807LL;
static unsigned long long int ubignum = BIG64;

struct incomplete_array
{
  int datasize;
  double data[];
};

struct named_init {
  int number;
  const wchar_t *name;
  double average;
};

typedef const char *ccp;

static inline int
test_restrict (ccp restrict text)
{
  // See if C++-style comments work.
  // Iterate through items via the restricted pointer.
  // Also check for declarations in for loops.
  for (unsigned int i = 0; *(text+i) != '\''\0'\''; ++i)
    continue;
  return 0;
}

// Check varargs and va_copy.
static bool
test_varargs (const char *format, ...)
{
  va_list args;
  va_start (args, format);
  va_list args_copy;
  va_copy (args_copy, args);

  const char *str = "";
  int number = 0;
  float fnumber = 0;

  while (*format)
    {
      switch (*format++)
	{
	case '\''s'\'': // string
	  str = va_arg (args_copy, const char *);
	  break;
	case '\''d'\'': // int
	  number = va_arg (args_copy, int);
	  break;
	case '\''f'\'': // float
	  fnumber = va_arg (args_copy, double);
	  break;
	default:
	  break;
	}
    }
  va_end (args_copy);
  va_end (args);

  return *str && number && fnumber;
}
'

# Test code for whether the C compiler supports C99 (body of main).
ac_c_conftest_c99_main='
  // Check bool.
  _Bool success = false;
  success |= (argc != 0);

  // Check restrict.
  if (test_restrict ("String literal") == 0)
    success = true;
  char *restrict newvar = "Another string";

  // Check varargs.
  success &= test_varargs ("s, d'\'' f .", "string", 65, 34.234);
  test_varargs_macros ();

  // Check flexible array members.
  struct incomplete_array *ia =
    malloc (sizeof (struct incomplete_array) + (sizeof (double) * 10));
  ia->datasize = 10;
  for (int i = 0; i < ia->datasize; ++i)
    ia->data[i] = i * 1.234;

  // Check named initializers.
  struct named_init ni = {
    .number = 34,
    .name = L"Test wide string",
    .average = 543.34343,
  };

  ni.number = 58;

  int dynamic_array[ni.number];
  dynamic_array[0] = argv[0][0];
  dynamic_array[ni.number - 1] = 543;

  // work around unused variable warnings
  ok |= (!success || bignum == 0LL || ubignum == 0uLL || newvar[0] == '\''x'\''
	 || dynamic_array[ni.number - 1] != 543);
'

# Test code for whether the C compiler supports C11 (global declarations)
ac_c_conftest_c11_globals='
// Does the compiler advertise C11 conformance?
#if !defined __STDC_VERSION__ || __STDC_VERSION__ < 201112L
# error "Compiler does not advertise C11 conformance"
#endif

// Check _Alignas.
char _Alignas (double) aligned_as_double;
char _Alignas (0) no_special_alignment;
extern char aligned_as_int;
char _Alignas (0) _Alignas (int) aligned_as_int;

// Check _Alignof.
enum
{
  int_alignment = _Alignof (int),
  int_array_alignment = _Alignof (int[100]),
  char_alignment = _Alignof (char)
};
_Static_assert (0 < -_Alignof (int), "_Alignof is signed");

// Check _Noreturn.
int _Noreturn does_not_return (void) { for (;;) continue; }

// Check _Static_assert.
struct test_static_assert
{
  int x;
  _Static_assert (sizeof (int) <= sizeof (long int),
                  "_Static_assert does not work in struct");
  long int y;
};

// Check UTF-8 literals.
#define u8 syntax error!
char const utf8_literal[] = u8"happens to be ASCII" "another string";

// Check duplicate typedefs.
typedef long *long_ptr;
typedef long int *long_ptr;
typedef long_ptr long_ptr;

// Anonymous structures and unions -- taken from C11 6.7.2.1 Example 1.
struct anonymous
{
  union {
    struct { int i; int j; };
    struct { int k; long int l; } w;
  };
  int m;
} v1;
'

# Test code for whether the C compiler supports C11 (body of main).
ac_c_conftest_c11_main='
  _Static_assert ((offsetof (struct anonymous, i)
		   == offsetof (struct anonymous, w.k)),
		  "Anonymous union alignment botch");
  v1.i = 2;
  v1.w.k = 5;
  ok |= v1.i != 5;
'

# Test code for whether the C compiler supports C11 (complete).
ac_c_conftest_c11_program="${ac_c_conftest_c89_globals}
${ac_c_conftest_c99_globals}
${ac_c_conftest_c11_globals}

int
main (int argc, char **argv)
{
  int ok = 0;
  ${ac_c_conftest_c89_main}
  ${ac_c_conftest_c99_main}
  ${ac_c_conftest_c11_main}
  return ok;
}
"

# Test code for whether the C compiler supports C99 (complete).
ac_c_conftest_c99_program="${ac_c_conftest_c89_globals}
${ac_c_conftest_c99_globals}

int
main (int argc, char **argv)
{
  int ok = 0;
  ${ac_c_conftest_c89_main}
  ${ac_c_conftest_c99_main}
  return ok;
}
"

# Test code for whether the C compiler supports C89 (complete).
ac_c_conftest_c89_program="${ac_c_conftest_c89_globals}

int
main (int argc, char **argv)
{
  int ok = 0;
  ${ac_c_conftest_c89_main}
  return ok;
}
"

as_fn_append ac_header_c_list " stdio.h stdio_h HAVE_STDIO_H"
as_fn_append ac_header_c_list " stdlib.h stdlib_h HAVE_STDLIB_H"
as_fn_append ac_header_c_list " string.h string_h HAVE_STRING_H"
as_fn_append ac_header_c_list " inttypes.h inttypes_h HAVE_INTTYPES_H"
as_fn_append ac_header_c_list " stdint.h stdint_h HAVE_STDINT_H"
as_fn_append ac_header_c_list " strings.h strings_h HAVE_STRINGS_H"
as_fn_append ac_header_c_list " sys/stat.h sys_stat_h HAVE_SYS_STAT_H"
as_fn_append ac_header_c_list " sys/types.h sys_types_h HAVE_SYS_TYPES_H"
as_fn_append ac_header_c_list " unistd.h unistd_h HAVE_UNISTD_H"

# Auxiliary files required by this configure script.
ac_aux_files="install-sh config.guess config.sub"

# Locations in which to look for auxiliary files.
ac_aux_dir_candidates="${srcdir}${PATH_SEPARATOR}${srcdir}/..${PATH_SEPARATOR}${srcdir}/../.."

# Search for a directory containing all of the required auxiliary files,
# $ac_aux_files, from the $PATH-style list $ac_aux_dir_candidates.
# If we don't find one directory that contains all the files we need,
# we report the set of missing files from the *first* directory in
# $ac_aux_dir_candidates and give up.
ac_missing_aux_files=""
ac_first_candidate=:
printf "%s\n" "$as_me:${as_lineno-$LINENO}: looking for aux files: $ac_aux_files" >&5
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
as_found=false
for as_dir in $ac_aux_dir_candidates
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
  as_found=:
  
  printf "%s\n" "$as_me:${as_lineno-$LINENO}:  trying $as_dir" >&5
  ac_aux_dir_found=yes
  ac_install_sh=
  for ac_aux in $ac_aux_files
  do
    # As a special case, if "install-sh" is required, that requirement
    # can be satisfied by any of "install-sh", "install.sh", or "shtool",
    # and $ac_install_sh is set appropriately for whichever one is found.
    if test x"$ac_aux" = x"install-sh"
    then
      if test -f "${as_dir}install-sh"; then
        printf "%s\n" "$as_me:${as_lineno-$LINENO}:   ${as_dir}install-sh found" >&5
        ac_install_sh="${as_dir}install-sh -c"
      elif test -f "${as_dir}install.sh"; then
        printf "%s\n" "$as_me:${as_lineno-$LINENO}:   ${as_dir}install.sh found" >&5
        ac_install_sh="${as_dir}install.sh -c"
      elif test -f "${as_dir}shtool"; then
        printf "%s\n" "$as_me:${as_lineno-$LINENO}:   ${as_dir}shtool found" >&5
        ac_install_sh="${as_dir}shtool install -c"
      else
        ac_aux_dir_found=no
        if $ac_first_candidate; then
          ac_missing_aux_files="${ac_missing_aux_files} install-sh"
        else
          break
        fi
      fi
    else
      if test -f "${as_dir}${ac_aux}"; then
        printf "%s\n" "$as_me:${as_lineno-$LINENO}:   ${as_dir}${ac_aux} found" >&5
      else
        ac_aux_dir_found=no
        if $ac_first_candidate; then
          ac_missing_aux_files="${ac_missing_aux_files} ${ac_aux}"
        else
          break
        fi
      fi
    fi
  done
  if test "$ac_aux_dir_found" = yes; then
    ac_aux_dir="$as_dir"
    break
  fi
  ac_first_candidate=false

  as_found=false
done
IFS=$as_save_IFS
if $as_found
then :
  
else $as_nop
  as_fn_error $? "cannot find required auxiliary files:$ac_missing_aux_files" "$LINENO" 5
fi


# These three variables are undocumented and unsupported,
# and are intended to be withdrawn in a future Autoconf release.
# They can cause serious problems if a builder's source tree is in a directory
# whose full name contains unusual characters.
if test -f "${ac_aux_dir}config.guess"; then
  ac_@&t@config_guess="$SHELL ${ac_aux_dir}config.guess"
fi
if test -f "${ac_aux_dir}config.sub"; then
  ac_@&t@config_sub="$SHELL ${ac_aux_dir}config.sub"
fi
if test -f "$ac_aux_dir/configure"; then
  ac_@&t@configure="$SHELL ${ac_aux_dir}configure"
fi

# Check that the precious variables saved in the cache have kept the same
# value.
ac_cache_corrupted=false
for ac_var in $ac_precious_vars; do
  eval ac_old_set=\$ac_cv_env_${ac_var}_set
  eval ac_new_set=\$ac_env_${ac_var}_set
  eval ac_old_val=\$ac_cv_env_${ac_var}_value
  eval ac_new_val=\$ac_env_${ac_var}_value
  case $ac_old_set,$ac_new_set in
    set,)
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: \`$ac_var' was set to \`$ac_old_val' in the previous run" >&5
printf "%s\n" "$as_me: error: \`$ac_var' was set to \`$ac_old_val' in the previous run" >&2;}
      ac_cache_corrupted=: ;;
    ,set)
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: \`$ac_var' was not set in the previous run" >&5
printf "%s\n" "$as_me: error: \`$ac_var' was not set in the previous run" >&2;}
      ac_cache_corrupted=: ;;
    ,);;
    *)
      if test "x$ac_old_val" != "x$ac_new_val"; then
	# differences in whitespace do not lead to failure.
	ac_old_val_w=`echo x $ac_old_val`
	ac_new_val_w=`echo x $ac_new_val`
	if test "$ac_old_val_w" != "$ac_new_val_w"; then
	  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: \`$ac_var' has changed since the previous run:" >&5
printf "%s\n" "$as_me: error: \`$ac_var' has changed since the previous run:" >&2;}
	  ac_cache_corrupted=:
	else
	  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: warning: ignoring whitespace changes in \`$ac_var' since the previous run:" >&5
printf "%s\n" "$as_me: warning: ignoring whitespace changes in \`$ac_var' since the previous run:" >&2;}
	  eval $ac_var=\$ac_old_val
	fi
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}:   former value:  \`$ac_old_val'" >&5
printf "%s\n" "$as_me:   former value:  \`$ac_old_val'" >&2;}
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}:   current value: \`$ac_new_val'" >&5
printf "%s\n" "$as_me:   current value: \`$ac_new_val'" >&2;}
      fi;;
  esac
  # Pass precious variables to config.status.
  if test "$ac_new_set" = set; then
    case $ac_new_val in
    *\'*) ac_arg=$ac_var=`printf "%s\n" "$ac_new_val" | sed "s/'/'\\\\\\\\''/g"` ;;
    *) ac_arg=$ac_var=$ac_new_val ;;
    esac
    case " $ac_configure_args " in
      *" '$ac_arg' "*) ;; # Avoid dups.  Use of quotes ensures accuracy.
      *) as_fn_append ac_configure_args " '$ac_arg'" ;;
    esac
  fi
done
if $ac_cache_corrupted; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: changes in the environment can compromise the build" >&5
printf "%s\n" "$as_me: error: changes in the environment can compromise the build" >&2;}
  as_fn_error $? "run \`${MAKE-make} distclean' and/or \`rm $cache_file'
	    and start over" "$LINENO" 5
fi
## -------------------- ##
## Main body of script. ##
## -------------------- ##

ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu





#
# These are the variables that are used in Makefile, pcap-config, and
# libpcap.pc.
#
# CFLAGS: inherited from the environment, not modified by us except
# for flags required for the platform for which we're building (and
# except temporarily during tests that involve compilation).  Used only
# when compiling C source.
#
# LDFLAGS: inherited from the environment, not modified by us.
#
# LIBS: inherited from the environment; we add libraries required by
# libpcap.  Libraries that the core libpcap code requires are added
# first; libraries required by additional pcap modules are first
# added to ADDITIONAL_LIBS, and only added to LIBS at the end, after
# we're finished doing configuration tests for the modules.
#
# LIBS_STATIC: libraries with which a program using the libpcap *static*
# library needs to be linked.  This is a superset of LIBS, used in
# pcap-config, so that "pcap-config --libs --static" will report them.
# Initialized to LIBS.
#
# REQUIRES_PRIVATE: pkg-config package names for additional libraries
# with which a program using the libpcap *static* library needs to be
# linked and for which a .pc file exists.  This is used in libpcap.pc,
# so that "pkg-config --libs --static" will report them, and so that
# those libraries will be determined using the library's .pc file, not
# from our .pc file.  Initialized to an empty string.
#
# V_CCOPT: additional compiler flags other than -I and -D flags
# needed when compiling libpcap.  Used in Makefile for both C and
# C++ source.
#
# V_DEFS: additional -D compiler flags needed when compiling
# libpcap.  Used in Makefile for both C and C++ source.
#
# V_INCLS: additional -I compiler flags needed when compiling
# libpcap.  Used in Makefile for both C and C++ source.
#
# ADDITIONAL_LIBS: additional libraries with which the libpcap dynamic
# library needs to be linked.  Used in Makefile; not used in pcap-config
# or libpcap.pc, as, in all platforms on which we run, if a dynamic
# library is linked with other dynamic libraries, a program using
# that dynamic library doesn't have to link with those libraries -
# they will be automatically loaded at run time.  Initialized to an
# empty string.
#
# ADDITIONAL_LIBS_STATIC: additional libraries with which a program
# using the libpcap *static* library needs to be linked.  This is used
# in pcap-config, so that "pcap-config --libs --static" will report
# them.  Initialized to an empty string.
#
# REQUIRES_PRIVATE: pkg-config package names for additional libraries
# with which a program using the libpcap *static* library needs to be
# linked and for which a .pc file exists.  This is used in libpcap.pc,
# so that "pkg-config --libs --static" will report them, and so that
# those libraries will be determined using the library's .pc file, not
# from our .pc file.  Initialized to an empty string.
#
# LIBS_PRIVATE: pkg-config package names for additional libraries with
# which a program using the libpcap *static* library needs to be linked
# and for which a .pc file does not exist.  This is used in libpcap.pc,
# so that "pkg-config --libs --static" will report them (those libraries
# cannot be determined using the library's .pc file, as there is no such
# file, so it has to come from our .pc file.  Initialized to an empty
# string.
#
LIBS_STATIC=""
REQUIRES_PRIVATE=""
LIBS_PRIVATE=""










  
  # Make sure we can run config.sub.
$SHELL "${ac_aux_dir}config.sub" sun4 >/dev/null 2>&1 ||
  as_fn_error $? "cannot run $SHELL ${ac_aux_dir}config.sub" "$LINENO" 5

{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking build system type" >&5
printf %s "checking build system type... " >&6; }
if test ${ac_cv_build+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_build_alias=$build_alias
test "x$ac_build_alias" = x &&
  ac_build_alias=`$SHELL "${ac_aux_dir}config.guess"`
test "x$ac_build_alias" = x &&
  as_fn_error $? "cannot guess build type; you must specify one" "$LINENO" 5
ac_cv_build=`$SHELL "${ac_aux_dir}config.sub" $ac_build_alias` ||
  as_fn_error $? "$SHELL ${ac_aux_dir}config.sub $ac_build_alias failed" "$LINENO" 5

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_build" >&5
printf "%s\n" "$ac_cv_build" >&6; }
case $ac_cv_build in
*-*-*) ;;
*) as_fn_error $? "invalid value of canonical build" "$LINENO" 5;;
esac
build=$ac_cv_build
ac_save_IFS=$IFS; IFS='-'
set x $ac_cv_build
shift
build_cpu=$1
build_vendor=$2
shift; shift
# Remember, the first character of IFS is used to create $*,
# except with old shells:
build_os=$*
IFS=$ac_save_IFS
case $build_os in *\ *) build_os=`echo "$build_os" | sed 's/ /-/g'`;; esac


{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking host system type" >&5
printf %s "checking host system type... " >&6; }
if test ${ac_cv_host+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test "x$host_alias" = x; then
  ac_cv_host=$ac_cv_build
else
  ac_cv_host=`$SHELL "${ac_aux_dir}config.sub" $host_alias` ||
    as_fn_error $? "$SHELL ${ac_aux_dir}config.sub $host_alias failed" "$LINENO" 5
fi

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_host" >&5
printf "%s\n" "$ac_cv_host" >&6; }
case $ac_cv_host in
*-*-*) ;;
*) as_fn_error $? "invalid value of canonical host" "$LINENO" 5;;
esac
host=$ac_cv_host
ac_save_IFS=$IFS; IFS='-'
set x $ac_cv_host
shift
host_cpu=$1
host_vendor=$2
shift; shift
# Remember, the first character of IFS is used to create $*,
# except with old shells:
host_os=$*
IFS=$ac_save_IFS
case $host_os in *\ *) host_os=`echo "$host_os" | sed 's/ /-/g'`;; esac




    
    
    
    
@%:@ Check whether --with-gcc was given.
if test ${with_gcc+y}
then :
  withval=$with_gcc; 
fi

    V_CCOPT=""
    if test "${srcdir}" != "." ; then
	    V_CCOPT="-I\$(srcdir)"
    fi
    if test "${CFLAGS+set}" = set; then
	    LBL_CFLAGS="$CFLAGS"
    fi
    if test -z "$CC" -a "$with_gcc" = no ; then
	    CC=cc
	    export CC
    fi

#
# We require C99 or later.
# Try to get it, which may involve adding compiler flags;
# if that fails, give up.
#









ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu
if test -n "$ac_tool_prefix"; then
  # Extract the first word of "${ac_tool_prefix}gcc", so it can be a program name with args.
set dummy ${ac_tool_prefix}gcc; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$CC"; then
  ac_cv_prog_CC="$CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_CC="${ac_tool_prefix}gcc"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
CC=$ac_cv_prog_CC
if test -n "$CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $CC" >&5
printf "%s\n" "$CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$ac_cv_prog_CC"; then
  ac_ct_CC=$CC
  # Extract the first word of "gcc", so it can be a program name with args.
set dummy gcc; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_ac_ct_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$ac_ct_CC"; then
  ac_cv_prog_ac_ct_CC="$ac_ct_CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_ac_ct_CC="gcc"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
ac_ct_CC=$ac_cv_prog_ac_ct_CC
if test -n "$ac_ct_CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_ct_CC" >&5
printf "%s\n" "$ac_ct_CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

  if test "x$ac_ct_CC" = x; then
    CC=""
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    CC=$ac_ct_CC
  fi
else
  CC="$ac_cv_prog_CC"
fi

if test -z "$CC"; then
          if test -n "$ac_tool_prefix"; then
    # Extract the first word of "${ac_tool_prefix}cc", so it can be a program name with args.
set dummy ${ac_tool_prefix}cc; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$CC"; then
  ac_cv_prog_CC="$CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_CC="${ac_tool_prefix}cc"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
CC=$ac_cv_prog_CC
if test -n "$CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $CC" >&5
printf "%s\n" "$CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


  fi
fi
if test -z "$CC"; then
  # Extract the first word of "cc", so it can be a program name with args.
set dummy cc; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$CC"; then
  ac_cv_prog_CC="$CC" # Let the user override the test.
else
  ac_prog_rejected=no
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    if test "$as_dir$ac_word$ac_exec_ext" = "/usr/ucb/cc"; then
       ac_prog_rejected=yes
       continue
     fi
    ac_cv_prog_CC="cc"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

if test $ac_prog_rejected = yes; then
  # We found a bogon in the path, so make sure we never use it.
  set dummy $ac_cv_prog_CC
  shift
  if test $@%:@ != 0; then
    # We chose a different compiler from the bogus one.
    # However, it has the same basename, so the bogon will be chosen
    # first if we set CC to just the basename; use the full file name.
    shift
    ac_cv_prog_CC="$as_dir$ac_word${1+' '}$@"
  fi
fi
fi
fi
CC=$ac_cv_prog_CC
if test -n "$CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $CC" >&5
printf "%s\n" "$CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$CC"; then
  if test -n "$ac_tool_prefix"; then
  for ac_prog in cl.exe
  do
    # Extract the first word of "$ac_tool_prefix$ac_prog", so it can be a program name with args.
set dummy $ac_tool_prefix$ac_prog; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$CC"; then
  ac_cv_prog_CC="$CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_CC="$ac_tool_prefix$ac_prog"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
CC=$ac_cv_prog_CC
if test -n "$CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $CC" >&5
printf "%s\n" "$CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


    test -n "$CC" && break
  done
fi
if test -z "$CC"; then
  ac_ct_CC=$CC
  for ac_prog in cl.exe
do
  # Extract the first word of "$ac_prog", so it can be a program name with args.
set dummy $ac_prog; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_ac_ct_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$ac_ct_CC"; then
  ac_cv_prog_ac_ct_CC="$ac_ct_CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_ac_ct_CC="$ac_prog"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
ac_ct_CC=$ac_cv_prog_ac_ct_CC
if test -n "$ac_ct_CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_ct_CC" >&5
printf "%s\n" "$ac_ct_CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


  test -n "$ac_ct_CC" && break
done

  if test "x$ac_ct_CC" = x; then
    CC=""
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    CC=$ac_ct_CC
  fi
fi

fi
if test -z "$CC"; then
  if test -n "$ac_tool_prefix"; then
  # Extract the first word of "${ac_tool_prefix}clang", so it can be a program name with args.
set dummy ${ac_tool_prefix}clang; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$CC"; then
  ac_cv_prog_CC="$CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_CC="${ac_tool_prefix}clang"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
CC=$ac_cv_prog_CC
if test -n "$CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $CC" >&5
printf "%s\n" "$CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$ac_cv_prog_CC"; then
  ac_ct_CC=$CC
  # Extract the first word of "clang", so it can be a program name with args.
set dummy clang; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_ac_ct_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$ac_ct_CC"; then
  ac_cv_prog_ac_ct_CC="$ac_ct_CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_ac_ct_CC="clang"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
ac_ct_CC=$ac_cv_prog_ac_ct_CC
if test -n "$ac_ct_CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_ct_CC" >&5
printf "%s\n" "$ac_ct_CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

  if test "x$ac_ct_CC" = x; then
    CC=""
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    CC=$ac_ct_CC
  fi
else
  CC="$ac_cv_prog_CC"
fi

fi


test -z "$CC" && { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error $? "no acceptable C compiler found in \$PATH
See \`config.log' for more details" "$LINENO" 5; }

# Provide some information about the compiler.
printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for C compiler version" >&5
set X $ac_compile
ac_compiler=$2
for ac_option in --version -v -V -qversion -version; do
  { { ac_try="$ac_compiler $ac_option >&5"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_compiler $ac_option >&5") 2>conftest.err
  ac_status=$?
  if test -s conftest.err; then
    sed '10a\
... rest of stderr output deleted ...
         10q' conftest.err >conftest.er1
    cat conftest.er1 >&5
  fi
  rm -f conftest.er1 conftest.err
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }
done

cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
ac_clean_files_save=$ac_clean_files
ac_clean_files="$ac_clean_files a.out a.out.dSYM a.exe b.out"
# Try to create an executable without -o first, disregard a.out.
# It will help us diagnose broken compilers, and finding out an intuition
# of exeext.
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the C compiler works" >&5
printf %s "checking whether the C compiler works... " >&6; }
ac_link_default=`printf "%s\n" "$ac_link" | sed 's/ -o *conftest[^ ]*//'`

# The possible output files:
ac_files="a.out conftest.exe conftest a.exe a_out.exe b.out conftest.*"

ac_rmfiles=
for ac_file in $ac_files
do
  case $ac_file in
    *.$ac_ext | *.xcoff | *.tds | *.d | *.pdb | *.xSYM | *.bb | *.bbg | *.map | *.inf | *.dSYM | *.o | *.obj ) ;;
    * ) ac_rmfiles="$ac_rmfiles $ac_file";;
  esac
done
rm -f $ac_rmfiles

if { { ac_try="$ac_link_default"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_link_default") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }
then :
  # Autoconf-2.13 could set the ac_cv_exeext variable to `no'.
# So ignore a value of `no', otherwise this would lead to `EXEEXT = no'
# in a Makefile.  We should not override ac_cv_exeext if it was cached,
# so that the user can short-circuit this test for compilers unknown to
# Autoconf.
for ac_file in $ac_files ''
do
  test -f "$ac_file" || continue
  case $ac_file in
    *.$ac_ext | *.xcoff | *.tds | *.d | *.pdb | *.xSYM | *.bb | *.bbg | *.map | *.inf | *.dSYM | *.o | *.obj )
	;;
    [ab].out )
	# We found the default executable, but exeext='' is most
	# certainly right.
	break;;
    *.* )
	if test ${ac_cv_exeext+y} && test "$ac_cv_exeext" != no;
	then :; else
	   ac_cv_exeext=`expr "$ac_file" : '[^.]*\(\..*\)'`
	fi
	# We set ac_cv_exeext here because the later test for it is not
	# safe: cross compilers may not add the suffix if given an `-o'
	# argument, so we may need to know it at that point already.
	# Even if this section looks crufty: it has the advantage of
	# actually working.
	break;;
    * )
	break;;
  esac
done
test "$ac_cv_exeext" = no && ac_cv_exeext=

else $as_nop
  ac_file=''
fi
if test -z "$ac_file"
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
printf "%s\n" "$as_me: failed program was:" >&5
sed 's/^/| /' conftest.$ac_ext >&5

{ { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error 77 "C compiler cannot create executables
See \`config.log' for more details" "$LINENO" 5; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for C compiler default output file name" >&5
printf %s "checking for C compiler default output file name... " >&6; }
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_file" >&5
printf "%s\n" "$ac_file" >&6; }
ac_exeext=$ac_cv_exeext

rm -f -r a.out a.out.dSYM a.exe conftest$ac_cv_exeext b.out
ac_clean_files=$ac_clean_files_save
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for suffix of executables" >&5
printf %s "checking for suffix of executables... " >&6; }
if { { ac_try="$ac_link"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_link") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }
then :
  # If both `conftest.exe' and `conftest' are `present' (well, observable)
# catch `conftest.exe'.  For instance with Cygwin, `ls conftest' will
# work properly (i.e., refer to `conftest.exe'), while it won't with
# `rm'.
for ac_file in conftest.exe conftest conftest.*; do
  test -f "$ac_file" || continue
  case $ac_file in
    *.$ac_ext | *.xcoff | *.tds | *.d | *.pdb | *.xSYM | *.bb | *.bbg | *.map | *.inf | *.dSYM | *.o | *.obj ) ;;
    *.* ) ac_cv_exeext=`expr "$ac_file" : '[^.]*\(\..*\)'`
	  break;;
    * ) break;;
  esac
done
else $as_nop
  { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error $? "cannot compute suffix of executables: cannot compile and link
See \`config.log' for more details" "$LINENO" 5; }
fi
rm -f conftest conftest$ac_cv_exeext
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_exeext" >&5
printf "%s\n" "$ac_cv_exeext" >&6; }

rm -f conftest.$ac_ext
EXEEXT=$ac_cv_exeext
ac_exeext=$EXEEXT
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
@%:@include <stdio.h>
int
main (void)
{
FILE *f = fopen ("conftest.out", "w");
 return ferror (f) || fclose (f) != 0;

  ;
  return 0;
}
_ACEOF
ac_clean_files="$ac_clean_files conftest.out"
# Check that the compiler produces executables we can run.  If not, either
# the compiler is broken, or we cross compile.
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether we are cross compiling" >&5
printf %s "checking whether we are cross compiling... " >&6; }
if test "$cross_compiling" != yes; then
  { { ac_try="$ac_link"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_link") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }
  if { ac_try='./conftest$ac_cv_exeext'
  { { case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_try") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }; }; then
    cross_compiling=no
  else
    if test "$cross_compiling" = maybe; then
	cross_compiling=yes
    else
	{ { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error 77 "cannot run C compiled programs.
If you meant to cross compile, use \`--host'.
See \`config.log' for more details" "$LINENO" 5; }
    fi
  fi
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $cross_compiling" >&5
printf "%s\n" "$cross_compiling" >&6; }

rm -f conftest.$ac_ext conftest$ac_cv_exeext conftest.out
ac_clean_files=$ac_clean_files_save
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for suffix of object files" >&5
printf %s "checking for suffix of object files... " >&6; }
if test ${ac_cv_objext+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
rm -f conftest.o conftest.obj
if { { ac_try="$ac_compile"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_compile") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }
then :
  for ac_file in conftest.o conftest.obj conftest.*; do
  test -f "$ac_file" || continue;
  case $ac_file in
    *.$ac_ext | *.xcoff | *.tds | *.d | *.pdb | *.xSYM | *.bb | *.bbg | *.map | *.inf | *.dSYM ) ;;
    *) ac_cv_objext=`expr "$ac_file" : '.*\.\(.*\)'`
       break;;
  esac
done
else $as_nop
  printf "%s\n" "$as_me: failed program was:" >&5
sed 's/^/| /' conftest.$ac_ext >&5

{ { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error $? "cannot compute suffix of object files: cannot compile
See \`config.log' for more details" "$LINENO" 5; }
fi
rm -f conftest.$ac_cv_objext conftest.$ac_ext
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_objext" >&5
printf "%s\n" "$ac_cv_objext" >&6; }
OBJEXT=$ac_cv_objext
ac_objext=$OBJEXT
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports GNU C" >&5
printf %s "checking whether the compiler supports GNU C... " >&6; }
if test ${ac_cv_c_compiler_gnu+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{
#ifndef __GNUC__
       choke me
#endif

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_compiler_gnu=yes
else $as_nop
  ac_compiler_gnu=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
ac_cv_c_compiler_gnu=$ac_compiler_gnu

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_c_compiler_gnu" >&5
printf "%s\n" "$ac_cv_c_compiler_gnu" >&6; }
ac_compiler_gnu=$ac_cv_c_compiler_gnu

if test $ac_compiler_gnu = yes; then
  GCC=yes
else
  GCC=
fi
ac_test_CFLAGS=${CFLAGS+y}
ac_save_CFLAGS=$CFLAGS
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether $CC accepts -g" >&5
printf %s "checking whether $CC accepts -g... " >&6; }
if test ${ac_cv_prog_cc_g+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_save_c_werror_flag=$ac_c_werror_flag
   ac_c_werror_flag=yes
   ac_cv_prog_cc_g=no
   CFLAGS="-g"
   cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_prog_cc_g=yes
else $as_nop
  CFLAGS=""
      cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
else $as_nop
  ac_c_werror_flag=$ac_save_c_werror_flag
	 CFLAGS="-g"
	 cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_prog_cc_g=yes
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
   ac_c_werror_flag=$ac_save_c_werror_flag
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_prog_cc_g" >&5
printf "%s\n" "$ac_cv_prog_cc_g" >&6; }
if test $ac_test_CFLAGS; then
  CFLAGS=$ac_save_CFLAGS
elif test $ac_cv_prog_cc_g = yes; then
  if test "$GCC" = yes; then
    CFLAGS="-g -O2"
  else
    CFLAGS="-g"
  fi
else
  if test "$GCC" = yes; then
    CFLAGS="-O2"
  else
    CFLAGS=
  fi
fi
ac_prog_cc_stdc=no
if test x$ac_prog_cc_stdc = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $CC option to enable C11 features" >&5
printf %s "checking for $CC option to enable C11 features... " >&6; }
if test ${ac_cv_prog_cc_c11+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_cv_prog_cc_c11=no
ac_save_CC=$CC
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$ac_c_conftest_c11_program
_ACEOF
for ac_arg in '' -std=gnu11
do
  CC="$ac_save_CC $ac_arg"
  if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_prog_cc_c11=$ac_arg
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam
  test "x$ac_cv_prog_cc_c11" != "xno" && break
done
rm -f conftest.$ac_ext
CC=$ac_save_CC
fi

if test "x$ac_cv_prog_cc_c11" = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: unsupported" >&5
printf "%s\n" "unsupported" >&6; }
else $as_nop
  if test "x$ac_cv_prog_cc_c11" = x
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: none needed" >&5
printf "%s\n" "none needed" >&6; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_prog_cc_c11" >&5
printf "%s\n" "$ac_cv_prog_cc_c11" >&6; }
     CC="$CC $ac_cv_prog_cc_c11"
fi
  ac_cv_prog_cc_stdc=$ac_cv_prog_cc_c11
  ac_prog_cc_stdc=c11
fi
fi
if test x$ac_prog_cc_stdc = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $CC option to enable C99 features" >&5
printf %s "checking for $CC option to enable C99 features... " >&6; }
if test ${ac_cv_prog_cc_c99+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_cv_prog_cc_c99=no
ac_save_CC=$CC
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$ac_c_conftest_c99_program
_ACEOF
for ac_arg in '' -std=gnu99 -std=c99 -c99 -qlanglvl=extc1x -qlanglvl=extc99 -AC99 -D_STDC_C99=
do
  CC="$ac_save_CC $ac_arg"
  if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_prog_cc_c99=$ac_arg
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam
  test "x$ac_cv_prog_cc_c99" != "xno" && break
done
rm -f conftest.$ac_ext
CC=$ac_save_CC
fi

if test "x$ac_cv_prog_cc_c99" = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: unsupported" >&5
printf "%s\n" "unsupported" >&6; }
else $as_nop
  if test "x$ac_cv_prog_cc_c99" = x
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: none needed" >&5
printf "%s\n" "none needed" >&6; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_prog_cc_c99" >&5
printf "%s\n" "$ac_cv_prog_cc_c99" >&6; }
     CC="$CC $ac_cv_prog_cc_c99"
fi
  ac_cv_prog_cc_stdc=$ac_cv_prog_cc_c99
  ac_prog_cc_stdc=c99
fi
fi
if test x$ac_prog_cc_stdc = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $CC option to enable C89 features" >&5
printf %s "checking for $CC option to enable C89 features... " >&6; }
if test ${ac_cv_prog_cc_c89+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_cv_prog_cc_c89=no
ac_save_CC=$CC
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$ac_c_conftest_c89_program
_ACEOF
for ac_arg in '' -qlanglvl=extc89 -qlanglvl=ansi -std -Ae "-Aa -D_HPUX_SOURCE" "-Xc -D__EXTENSIONS__"
do
  CC="$ac_save_CC $ac_arg"
  if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_prog_cc_c89=$ac_arg
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam
  test "x$ac_cv_prog_cc_c89" != "xno" && break
done
rm -f conftest.$ac_ext
CC=$ac_save_CC
fi

if test "x$ac_cv_prog_cc_c89" = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: unsupported" >&5
printf "%s\n" "unsupported" >&6; }
else $as_nop
  if test "x$ac_cv_prog_cc_c89" = x
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: none needed" >&5
printf "%s\n" "none needed" >&6; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_prog_cc_c89" >&5
printf "%s\n" "$ac_cv_prog_cc_c89" >&6; }
     CC="$CC $ac_cv_prog_cc_c89"
fi
  ac_cv_prog_cc_stdc=$ac_cv_prog_cc_c89
  ac_prog_cc_stdc=c89
fi
fi

ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu


if test "$ac_cv_prog_cc_c99" = "no"; then
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: The C compiler does not support C99; there may be compiler errors" >&5
printf "%s\n" "$as_me: WARNING: The C compiler does not support C99; there may be compiler errors" >&2;}
fi

#
# Try to arrange for large file support.
#

@%:@ Check whether --enable-largefile was given.
if test ${enable_largefile+y}
then :
  enableval=$enable_largefile; 
fi

if test "$enable_largefile" != no; then

  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for special C compiler options needed for large files" >&5
printf %s "checking for special C compiler options needed for large files... " >&6; }
if test ${ac_cv_sys_largefile_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_cv_sys_largefile_CC=no
     if test "$GCC" != yes; then
       ac_save_CC=$CC
       while :; do
	 # IRIX 6.2 and later do not support large files by default,
	 # so use the C compiler's -n32 option if that helps.
	 cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
@%:@include <sys/types.h>
 /* Check that off_t can represent 2**63 - 1 correctly.
    We can't simply define LARGE_OFF_T to be 9223372036854775807,
    since some C++ compilers masquerading as C compilers
    incorrectly reject 9223372036854775807.  */
@%:@define LARGE_OFF_T (((off_t) 1 << 31 << 31) - 1 + ((off_t) 1 << 31 << 31))
  int off_t_is_large[(LARGE_OFF_T % 2147483629 == 721
		       && LARGE_OFF_T % 2147483647 == 1)
		      ? 1 : -1];
int
main (void)
{

  ;
  return 0;
}
_ACEOF
	 if ac_fn_c_try_compile "$LINENO"
then :
  break
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam
	 CC="$CC -n32"
	 if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_sys_largefile_CC=' -n32'; break
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam
	 break
       done
       CC=$ac_save_CC
       rm -f conftest.$ac_ext
    fi
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_sys_largefile_CC" >&5
printf "%s\n" "$ac_cv_sys_largefile_CC" >&6; }
  if test "$ac_cv_sys_largefile_CC" != no; then
    CC=$CC$ac_cv_sys_largefile_CC
  fi

  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for _FILE_OFFSET_BITS value needed for large files" >&5
printf %s "checking for _FILE_OFFSET_BITS value needed for large files... " >&6; }
if test ${ac_cv_sys_file_offset_bits+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  while :; do
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
@%:@include <sys/types.h>
 /* Check that off_t can represent 2**63 - 1 correctly.
    We can't simply define LARGE_OFF_T to be 9223372036854775807,
    since some C++ compilers masquerading as C compilers
    incorrectly reject 9223372036854775807.  */
@%:@define LARGE_OFF_T (((off_t) 1 << 31 << 31) - 1 + ((off_t) 1 << 31 << 31))
  int off_t_is_large[(LARGE_OFF_T % 2147483629 == 721
		       && LARGE_OFF_T % 2147483647 == 1)
		      ? 1 : -1];
int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_sys_file_offset_bits=no; break
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
@%:@define _FILE_OFFSET_BITS 64
@%:@include <sys/types.h>
 /* Check that off_t can represent 2**63 - 1 correctly.
    We can't simply define LARGE_OFF_T to be 9223372036854775807,
    since some C++ compilers masquerading as C compilers
    incorrectly reject 9223372036854775807.  */
@%:@define LARGE_OFF_T (((off_t) 1 << 31 << 31) - 1 + ((off_t) 1 << 31 << 31))
  int off_t_is_large[(LARGE_OFF_T % 2147483629 == 721
		       && LARGE_OFF_T % 2147483647 == 1)
		      ? 1 : -1];
int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_sys_file_offset_bits=64; break
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
  ac_cv_sys_file_offset_bits=unknown
  break
done
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_sys_file_offset_bits" >&5
printf "%s\n" "$ac_cv_sys_file_offset_bits" >&6; }
case $ac_cv_sys_file_offset_bits in #(
  no | unknown) ;;
  *) 
printf "%s\n" "@%:@define _FILE_OFFSET_BITS $ac_cv_sys_file_offset_bits" >>confdefs.h
;;
esac
rm -rf conftest*
  if test $ac_cv_sys_file_offset_bits = unknown; then
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for _LARGE_FILES value needed for large files" >&5
printf %s "checking for _LARGE_FILES value needed for large files... " >&6; }
if test ${ac_cv_sys_large_files+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  while :; do
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
@%:@include <sys/types.h>
 /* Check that off_t can represent 2**63 - 1 correctly.
    We can't simply define LARGE_OFF_T to be 9223372036854775807,
    since some C++ compilers masquerading as C compilers
    incorrectly reject 9223372036854775807.  */
@%:@define LARGE_OFF_T (((off_t) 1 << 31 << 31) - 1 + ((off_t) 1 << 31 << 31))
  int off_t_is_large[(LARGE_OFF_T % 2147483629 == 721
		       && LARGE_OFF_T % 2147483647 == 1)
		      ? 1 : -1];
int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_sys_large_files=no; break
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
@%:@define _LARGE_FILES 1
@%:@include <sys/types.h>
 /* Check that off_t can represent 2**63 - 1 correctly.
    We can't simply define LARGE_OFF_T to be 9223372036854775807,
    since some C++ compilers masquerading as C compilers
    incorrectly reject 9223372036854775807.  */
@%:@define LARGE_OFF_T (((off_t) 1 << 31 << 31) - 1 + ((off_t) 1 << 31 << 31))
  int off_t_is_large[(LARGE_OFF_T % 2147483629 == 721
		       && LARGE_OFF_T % 2147483647 == 1)
		      ? 1 : -1];
int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_sys_large_files=1; break
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
  ac_cv_sys_large_files=unknown
  break
done
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_sys_large_files" >&5
printf "%s\n" "$ac_cv_sys_large_files" >&6; }
case $ac_cv_sys_large_files in #(
  no | unknown) ;;
  *) 
printf "%s\n" "@%:@define _LARGE_FILES $ac_cv_sys_large_files" >>confdefs.h
;;
esac
rm -rf conftest*
  fi
fi

{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for _LARGEFILE_SOURCE value needed for large files" >&5
printf %s "checking for _LARGEFILE_SOURCE value needed for large files... " >&6; }
if test ${ac_cv_sys_largefile_source+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  while :; do
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <sys/types.h> /* for off_t */
     #include <stdio.h>
int
main (void)
{
int (*fp) (FILE *, off_t, int) = fseeko;
     return fseeko (stdin, 0, 0) && fp (stdin, 0, 0);
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_sys_largefile_source=no; break
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
@%:@define _LARGEFILE_SOURCE 1
#include <sys/types.h> /* for off_t */
     #include <stdio.h>
int
main (void)
{
int (*fp) (FILE *, off_t, int) = fseeko;
     return fseeko (stdin, 0, 0) && fp (stdin, 0, 0);
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_sys_largefile_source=1; break
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
  ac_cv_sys_largefile_source=unknown
  break
done
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_sys_largefile_source" >&5
printf "%s\n" "$ac_cv_sys_largefile_source" >&6; }
case $ac_cv_sys_largefile_source in #(
  no | unknown) ;;
  *) 
printf "%s\n" "@%:@define _LARGEFILE_SOURCE $ac_cv_sys_largefile_source" >>confdefs.h
;;
esac
rm -rf conftest*

# We used to try defining _XOPEN_SOURCE=500 too, to work around a bug
# in glibc 2.1.3, but that breaks too many other things.
# If you want fseeko and ftello with glibc, upgrade to a fixed glibc.
if test $ac_cv_sys_largefile_source != unknown; then
  
printf "%s\n" "@%:@define HAVE_FSEEKO 1" >>confdefs.h

fi


#
# Get the size of a void *, to determine whether this is a 32-bit
# or 64-bit build.
#
ac_header= ac_cache=
for ac_item in $ac_header_c_list
do
  if test $ac_cache; then
    ac_fn_c_check_header_compile "$LINENO" $ac_header ac_cv_header_$ac_cache "$ac_includes_default"
    if eval test \"x\$ac_cv_header_$ac_cache\" = xyes; then
      printf "%s\n" "#define $ac_item 1" >> confdefs.h
    fi
    ac_header= ac_cache=
  elif test $ac_header; then
    ac_cache=$ac_item
  else
    ac_header=$ac_item
  fi
done








if test $ac_cv_header_stdlib_h = yes && test $ac_cv_header_string_h = yes
then :
  
printf "%s\n" "@%:@define STDC_HEADERS 1" >>confdefs.h

fi
# The cast to long int works around a bug in the HP C Compiler
# version HP92453-01 B.11.11.23709.GP, which incorrectly rejects
# declarations like `int a3[[(sizeof (unsigned char)) >= 0]];'.
# This bug is HP SR number 8606223364.
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking size of void *" >&5
printf %s "checking size of void *... " >&6; }
if test ${ac_cv_sizeof_void_p+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if ac_fn_c_compute_int "$LINENO" "(long int) (sizeof (void *))" "ac_cv_sizeof_void_p"        "$ac_includes_default"
then :
  
else $as_nop
  if test "$ac_cv_type_void_p" = yes; then
     { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error 77 "cannot compute sizeof (void *)
See \`config.log' for more details" "$LINENO" 5; }
   else
     ac_cv_sizeof_void_p=0
   fi
fi

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_sizeof_void_p" >&5
printf "%s\n" "$ac_cv_sizeof_void_p" >&6; }



printf "%s\n" "@%:@define SIZEOF_VOID_P $ac_cv_sizeof_void_p" >>confdefs.h


ac_lbl_c_sizeof_void_p="$ac_cv_sizeof_void_p"

#
# Get the size of a time_t, to know whether it's 32-bit or 64-bit.
#
# The cast to long int works around a bug in the HP C Compiler
# version HP92453-01 B.11.11.23709.GP, which incorrectly rejects
# declarations like `int a3[[(sizeof (unsigned char)) >= 0]];'.
# This bug is HP SR number 8606223364.
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking size of time_t" >&5
printf %s "checking size of time_t... " >&6; }
if test ${ac_cv_sizeof_time_t+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if ac_fn_c_compute_int "$LINENO" "(long int) (sizeof (time_t))" "ac_cv_sizeof_time_t"        "#include <time.h>
"
then :
  
else $as_nop
  if test "$ac_cv_type_time_t" = yes; then
     { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error 77 "cannot compute sizeof (time_t)
See \`config.log' for more details" "$LINENO" 5; }
   else
     ac_cv_sizeof_time_t=0
   fi
fi

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_sizeof_time_t" >&5
printf "%s\n" "$ac_cv_sizeof_time_t" >&6; }



printf "%s\n" "@%:@define SIZEOF_TIME_T $ac_cv_sizeof_time_t" >>confdefs.h




    
    
    if test "$GCC" = yes ; then
	    #
	    # -Werror forces warnings to be errors.
	    #
	    ac_lbl_cc_force_warning_errors=-Werror

	    #
	    # Try to have the compiler default to hiding symbols,
	    # so that only symbols explicitly exported with
	    # PCAP_API will be visible outside (shared) libraries.
	    #
	    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -fvisibility=hidden option" >&5
printf %s "checking whether the compiler supports the -fvisibility=hidden option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -fvisibility=hidden"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -fvisibility=hidden " >&5
printf %s "checking whether -fvisibility=hidden ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -fvisibility=hidden"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
    else
	    V_INCLS="$V_INCLS -I/usr/local/include"
	    LDFLAGS="$LDFLAGS -L/usr/local/lib"

	    case "$host_os" in

	    darwin*)
		    #
		    # This is assumed either to be GCC or clang, both
		    # of which use -Werror to force warnings to be errors.
		    #
		    ac_lbl_cc_force_warning_errors=-Werror

		    #
		    # Try to have the compiler default to hiding symbols,
		    # so that only symbols explicitly exported with
		    # PCAP_API will be visible outside (shared) libraries.
		    #
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -fvisibility=hidden option" >&5
printf %s "checking whether the compiler supports the -fvisibility=hidden option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -fvisibility=hidden"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -fvisibility=hidden " >&5
printf %s "checking whether -fvisibility=hidden ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -fvisibility=hidden"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    ;;

	    hpux*)
		    #
		    # HP C, which is what we presume we're using, doesn't
		    # exit with a non-zero exit status if we hand it an
		    # invalid -W flag, can't be forced to do so even with
		    # +We, and doesn't handle GCC-style -W flags, so we
		    # don't want to try using GCC-style -W flags.
		    #
		    ac_lbl_cc_dont_try_gcc_dashW=yes
		    ;;

	    solaris*)
		    #
		    # Assumed to be Sun C, which requires -errwarn to force
		    # warnings to be treated as errors.
		    #
		    ac_lbl_cc_force_warning_errors=-errwarn

		    #
		    # Try to have the compiler default to hiding symbols,
		    # so that only symbols explicitly exported with
		    # PCAP_API will be visible outside (shared) libraries.
		    #
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -xldscope=hidden option" >&5
printf %s "checking whether the compiler supports the -xldscope=hidden option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -xldscope=hidden"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -xldscope=hidden " >&5
printf %s "checking whether -xldscope=hidden ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -xldscope=hidden"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    ;;
	    esac
	    V_CCOPT="$V_CCOPT -O"
    fi


    if test "$GCC" = yes ; then
	    #
	    # On platforms where we build a shared library:
	    #
	    #	add options to generate position-independent code,
	    #	if necessary (it's the default in AIX and Darwin/macOS);
	    #
	    #	define option to set the soname of the shared library,
	    #	if the OS supports that;
	    #
	    #	add options to specify, at link time, a directory to
	    #	add to the run-time search path, if that's necessary.
	    #
	    V_SHLIB_CMD="\$(CC)"
	    V_SHLIB_OPT="-shared"
	    case "$host_os" in

	    aix*)
		    ;;

	    freebsd*|netbsd*|openbsd*|dragonfly*|linux*|haiku*|midipix*|gnu*)
		    #
		    # Platforms where the C compiler is GCC or accepts
		    # compatible command-line arguments, and the linker
		    # is the GNU linker or accepts compatible command-line
		    # arguments.
		    #
		    # Some instruction sets require -fPIC on some
		    # operating systems.  Check for them.  If you
		    # have a combination that requires it, add it
		    # here.
		    #
		    PIC_OPT=-fpic
		    case "$host_cpu" in

		    sparc64*)
			case "$host_os" in

			freebsd*|openbsd*|linux*)
			    PIC_OPT=-fPIC
			    ;;
			esac
			;;
		    esac
		    V_SHLIB_CCOPT="$V_SHLIB_CCOPT $PIC_OPT"
		    V_SONAME_OPT="-Wl,-soname,"
		    ;;

	    hpux*)
		    V_SHLIB_CCOPT="$V_SHLIB_CCOPT -fpic"
		    #
		    # XXX - this assumes GCC is using the HP linker,
		    # rather than the GNU linker, and that the "+h"
		    # option is used on all HP-UX platforms, both .sl
		    # and .so.
		    #
		    V_SONAME_OPT="-Wl,+h,"
		    #
		    # By default, directories specified with -L
		    # are added to the run-time search path, so
		    # we don't add them in pcap-config.
		    #
		    ;;

	    solaris*)
		    V_SHLIB_CCOPT="$V_SHLIB_CCOPT -fpic"
		    #
		    # Sun/Oracle's C compiler, GCC, and GCC-compatible
		    # compilers support -Wl,{comma-separated list of options},
		    # and we use the C compiler, not ld, for all linking,
		    # including linking to produce a shared library.
		    #
		    V_SONAME_OPT="-Wl,-h,"
		    ;;
	    esac
    else
	    #
	    # Set the appropriate compiler flags and, on platforms
	    # where we build a shared library:
	    #
	    #	add options to generate position-independent code,
	    #	if necessary (it's the default in Darwin/macOS);
	    #
	    #	if we generate ".so" shared libraries, define the
	    #	appropriate options for building the shared library;
	    #
	    #	add options to specify, at link time, a directory to
	    #	add to the run-time search path, if that's necessary.
	    #
	    # Note: spaces after V_SONAME_OPT are significant; on
	    # some platforms the soname is passed with a GCC-like
	    # "-Wl,-soname,{soname}" option, with the soname part
	    # of the option, while on other platforms the C compiler
	    # driver takes it as a regular option with the soname
	    # following the option.
	    #
	    case "$host_os" in

	    aix*)
		    V_SHLIB_CMD="\$(CC)"
		    V_SHLIB_OPT="-G -bnoentry -bexpall"
		    ;;

	    freebsd*|netbsd*|openbsd*|dragonfly*|linux*)
		    #
		    # Platforms where the C compiler is GCC or accepts
		    # compatible command-line arguments, and the linker
		    # is the GNU linker or accepts compatible command-line
		    # arguments.
		    #
		    # XXX - does 64-bit SPARC require -fPIC?
		    #
		    V_SHLIB_CCOPT="$V_SHLIB_CCOPT -fpic"
		    V_SHLIB_CMD="\$(CC)"
		    V_SHLIB_OPT="-shared"
		    V_SONAME_OPT="-Wl,-soname,"
		    ;;

	    hpux*)
		    V_SHLIB_CCOPT="$V_SHLIB_CCOPT +z"
		    V_SHLIB_CMD="\$(LD)"
		    V_SHLIB_OPT="-b"
		    V_SONAME_OPT="+h "
		    #
		    # By default, directories specified with -L
		    # are added to the run-time search path, so
		    # we don't add them in pcap-config.
		    #
		    ;;

	    solaris*)
		    V_SHLIB_CCOPT="$V_SHLIB_CCOPT -Kpic"
		    V_SHLIB_CMD="\$(CC)"
		    V_SHLIB_OPT="-G"
		    #
		    # Sun/Oracle's C compiler, GCC, and GCC-compatible
		    # compilers support -Wl,{comma-separated list of options},
		    # and we use the C compiler, not ld, for all linking,
		    # including linking to produce a shared library.
		    #
		    V_SONAME_OPT="-Wl,-h,"
		    ;;
	    esac
    fi


	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for __atomic_load_n" >&5
printf %s "checking for __atomic_load_n... " >&6; }
	if test ${ac_cv_have___atomic_load_n+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

		    int i = 17;
		    int j;
		    j = __atomic_load_n(&i, __ATOMIC_RELAXED);
		
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_have___atomic_load_n=yes
else $as_nop
  ac_have___atomic_load_n=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
fi

	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_have___atomic_load_n" >&5
printf "%s\n" "$ac_have___atomic_load_n" >&6; }
	if test $ac_have___atomic_load_n = yes ; then
	    
printf "%s\n" "@%:@define HAVE___ATOMIC_LOAD_N 1" >>confdefs.h

	fi

	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for __atomic_store_n" >&5
printf %s "checking for __atomic_store_n... " >&6; }
	if test ${ac_cv_have___atomic_store_n+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

		    int i;
		    __atomic_store_n(&i, 17, __ATOMIC_RELAXED);
		
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_have___atomic_store_n=yes
else $as_nop
  ac_have___atomic_store_n=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
fi

	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_have___atomic_store_n" >&5
printf "%s\n" "$ac_have___atomic_store_n" >&6; }
	if test $ac_have___atomic_store_n = yes ; then
	    
printf "%s\n" "@%:@define HAVE___ATOMIC_STORE_N 1" >>confdefs.h

	fi

#
# Check whether the platform for which we're compiling requires extra
# defines and libraries.  If so, add them to CFLAGS and LIBS, as we want
# all subsequent tests to be done with those defines and libraries.
#
case "$host_os" in
haiku*)
	#
	# Haiku needs _BSD_SOURCE for the _IO* macros because it doesn't
	# use them.
	#
	CFLAGS="$CFLAGS -D_BSD_SOURCE"

	#
	# Haiku has getpass() in libbsd.
	#
	LIBS="-lbsd $LIBS"
	;;
hpux*)
	#
	# Check to see if the dl_hp_ppa_info_t struct has the HP-UX 11.00
	# dl_module_id_1 member.
	# (This is the case on HP-UX B.11.31.)
	#
	# NOTE: any failure means we conclude that it doesn't have that member,
	# so if we don't have DLPI, don't have a <sys/dlpi_ext.h> header, or
	# have one that doesn't declare a dl_hp_ppa_info_t type, we conclude
	# it doesn't have that member (which is OK, as either we won't be
	# using code that would use that member, or we wouldn't compile in
	# any case).
	#
	ac_fn_c_check_member "$LINENO" "dl_hp_ppa_info_t" "dl_module_id_1" "ac_cv_member_dl_hp_ppa_info_t_dl_module_id_1" "
		#include <sys/types.h>
		#include <sys/dlpi.h>
		#include <sys/dlpi_ext.h>
	    
"
if test "x$ac_cv_member_dl_hp_ppa_info_t_dl_module_id_1" = xyes
then :
  
printf "%s\n" "@%:@define HAVE_DL_HP_PPA_INFO_T_DL_MODULE_ID_1 1" >>confdefs.h


fi


	#
	# On HP-UX DLPI needs putmsg(), which previously was in libstr, but in
	# HP-UX B.11.31 this is no longer the case.
	# AC_SEARCH_LIBS() accounts for that.
	#
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for library containing putmsg" >&5
printf %s "checking for library containing putmsg... " >&6; }
if test ${ac_cv_search_putmsg+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_func_search_save_LIBS=$LIBS
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char putmsg ();
int
main (void)
{
return putmsg ();
  ;
  return 0;
}
_ACEOF
for ac_lib in '' str
do
  if test -z "$ac_lib"; then
    ac_res="none required"
  else
    ac_res=-l$ac_lib
    LIBS="-l$ac_lib  $ac_func_search_save_LIBS"
  fi
  if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_search_putmsg=$ac_res
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext
  if test ${ac_cv_search_putmsg+y}
then :
  break
fi
done
if test ${ac_cv_search_putmsg+y}
then :
  
else $as_nop
  ac_cv_search_putmsg=no
fi
rm conftest.$ac_ext
LIBS=$ac_func_search_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_search_putmsg" >&5
printf "%s\n" "$ac_cv_search_putmsg" >&6; }
ac_res=$ac_cv_search_putmsg
if test "$ac_res" != no
then :
  test "$ac_res" = "none required" || LIBS="$ac_res $LIBS"
  
fi

	;;
esac

ac_fn_c_check_func "$LINENO" "strerror_r" "ac_cv_func_strerror_r"
if test "x$ac_cv_func_strerror_r" = xyes
then :
  
	#
	# We have strerror_r; if we define _GNU_SOURCE, is it a
	# POSIX-compliant strerror_r() or a GNU strerror_r()?
	#
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether strerror_r is GNU-style" >&5
printf %s "checking whether strerror_r is GNU-style... " >&6; }
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

		#define _GNU_SOURCE
#include <string.h>

/* Define it GNU-style; that will cause an error if it's not GNU-style */
extern char *strerror_r(int, char *, size_t);

int
main(void)
{
	return 0;
}

	    
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		# GNU-style
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		
printf "%s\n" "@%:@define HAVE_GNU_STRERROR_R 1" >>confdefs.h

	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		
printf "%s\n" "@%:@define HAVE_POSIX_STRERROR_R 1" >>confdefs.h

	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
    
else $as_nop
  
	#
	# We don't have strerror_r; do we have _wcserror_s?
	#
	ac_fn_c_check_func "$LINENO" "_wcserror_s" "ac_cv_func__wcserror_s"
if test "x$ac_cv_func__wcserror_s" = xyes
then :
  printf "%s\n" "@%:@define HAVE__WCSERROR_S 1" >>confdefs.h

fi

    
fi


#
# Require a proof of suitable snprintf(3), same as in tcpdump.
#
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether snprintf is suitable" >&5
printf %s "checking whether snprintf is suitable... " >&6; }
if test "$cross_compiling" = yes
then :
  
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: not while cross-compiling" >&5
printf "%s\n" "not while cross-compiling" >&6; }
    

else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

        
#include <stdio.h>
#include <string.h>
#include <inttypes.h>
#include <sys/types.h>

#if defined(_WIN32) && !defined(_SSIZE_T_DEFINED)
/*
 * On UN*Xes, this is a signed integer type of the same size as size_t.
 *
 * It's not defined by Visual Studio; we assume that ptrdiff_t will
 * be a type that is a signed integer type of the same size as size_t.
 */
typedef ptrdiff_t ssize_t;
#endif

/*
 * Avoid trying to cast negative values to unsigned types, or doing
 * shifts of signed types, in order not to have the test program fail
 * if we're building with undefined-behavior sanitizers enabled.
 */
int main()
{
  char buf[100];
  unsigned int ui = sizeof(buf);
  int i = sizeof(buf);
  int64_t i64 = INT64_C(0x100000000);
  uint64_t ui64 = UINT64_C(0x100000000);

  snprintf(buf, sizeof(buf), "%zu", (size_t)ui);
  if (strncmp(buf, "100", sizeof(buf)))
    return 1;

  snprintf(buf, sizeof(buf), "%zd", (ssize_t)(-i));
  if (strncmp(buf, "-100", sizeof(buf)))
    return 2;

  snprintf(buf, sizeof(buf), "%" PRId64, -i64);
  if (strncmp(buf, "-4294967296", sizeof(buf)))
    return 3;

  snprintf(buf, sizeof(buf), "0o%" PRIo64, ui64);
  if (strncmp(buf, "0o40000000000", sizeof(buf)))
    return 4;

  snprintf(buf, sizeof(buf), "0x%" PRIx64, ui64);
  if (strncmp(buf, "0x100000000", sizeof(buf)))
    return 5;

  snprintf(buf, sizeof(buf), "%" PRIu64, ui64);
  if (strncmp(buf, "4294967296", sizeof(buf)))
    return 6;

  return 0;
}
        
    
_ACEOF
if ac_fn_c_try_run "$LINENO"
then :
  
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
    
else $as_nop
  
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
        as_fn_error $? "The snprintf(3) implementation in this libc is not suitable,
libpcap would not work correctly even if it managed to compile." "$LINENO" 5
    
fi
rm -f core *.core core.conftest.* gmon.out bb.out conftest$ac_exeext \
  conftest.$ac_objext conftest.beam conftest.$ac_ext
fi


needasprintf=no

  for ac_func in vasprintf asprintf
do :
  as_ac_var=`printf "%s\n" "ac_cv_func_$ac_func" | $as_tr_sh`
ac_fn_c_check_func "$LINENO" "$ac_func" "$as_ac_var"
if eval test \"x\$"$as_ac_var"\" = x"yes"
then :
  cat >>confdefs.h <<_ACEOF
@%:@define `printf "%s\n" "HAVE_$ac_func" | $as_tr_cpp` 1
_ACEOF
 
else $as_nop
  needasprintf=yes
fi

done
if test $needasprintf = yes; then
	case " $LIB@&t@OBJS " in
  *" asprintf.$ac_objext "* ) ;;
  *) LIB@&t@OBJS="$LIB@&t@OBJS asprintf.$ac_objext"
 ;;
esac

fi

needstrlcat=no

  for ac_func in strlcat
do :
  ac_fn_c_check_func "$LINENO" "strlcat" "ac_cv_func_strlcat"
if test "x$ac_cv_func_strlcat" = xyes
then :
  printf "%s\n" "@%:@define HAVE_STRLCAT 1" >>confdefs.h
 
else $as_nop
  needstrlcat=yes
fi

done
if test $needstrlcat = yes; then
	case " $LIB@&t@OBJS " in
  *" strlcat.$ac_objext "* ) ;;
  *) LIB@&t@OBJS="$LIB@&t@OBJS strlcat.$ac_objext"
 ;;
esac

fi

needstrlcpy=no

  for ac_func in strlcpy
do :
  ac_fn_c_check_func "$LINENO" "strlcpy" "ac_cv_func_strlcpy"
if test "x$ac_cv_func_strlcpy" = xyes
then :
  printf "%s\n" "@%:@define HAVE_STRLCPY 1" >>confdefs.h
 
else $as_nop
  needstrlcpy=yes
fi

done
if test $needstrlcpy = yes; then
	case " $LIB@&t@OBJS " in
  *" strlcpy.$ac_objext "* ) ;;
  *) LIB@&t@OBJS="$LIB@&t@OBJS strlcpy.$ac_objext"
 ;;
esac

fi

needstrtok_r=no

  for ac_func in strtok_r
do :
  ac_fn_c_check_func "$LINENO" "strtok_r" "ac_cv_func_strtok_r"
if test "x$ac_cv_func_strtok_r" = xyes
then :
  printf "%s\n" "@%:@define HAVE_STRTOK_R 1" >>confdefs.h
 
else $as_nop
  needstrtok_r=yes
fi

done
if test $needstrtok_r = yes; then
	case " $LIB@&t@OBJS " in
  *" strtok_r.$ac_objext "* ) ;;
  *) LIB@&t@OBJS="$LIB@&t@OBJS strtok_r.$ac_objext"
 ;;
esac

fi

#
# Do this before checking for ether_hostton(), as it's a
# "getaddrinfo()-ish function".
#

    #
    # Most operating systems have getaddrinfo(), and the other routines
    # we may need, in the default searched libraries (e.g., libc).
    #
    # These are: AIX, FreeBSD, Linux, macOS, NetBSD, OpenBSD, Solaris
    # since 11.4.
    #
    # Check there first.
    #
    ac_fn_c_check_func "$LINENO" "getaddrinfo" "ac_cv_func_getaddrinfo"
if test "x$ac_cv_func_getaddrinfo" = xyes
then :
  
else $as_nop
  
	#
	# Not found in the standard system libraries.
	#
	# In some versions of Solaris, we need to link with libsocket
	# and libnsl, so check in libsocket and also link with libnsl
	# when doing this test.
	#
	# These are: illumos, Solaris 9.x, 10.x, 11.x before 11.4.
	#
	# Linking with libsocket and libnsl will find all the routines
	# we need.
	#
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for getaddrinfo in -lsocket" >&5
printf %s "checking for getaddrinfo in -lsocket... " >&6; }
if test ${ac_cv_lib_socket_getaddrinfo+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lsocket -lnsl $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char getaddrinfo ();
int
main (void)
{
return getaddrinfo ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_socket_getaddrinfo=yes
else $as_nop
  ac_cv_lib_socket_getaddrinfo=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_socket_getaddrinfo" >&5
printf "%s\n" "$ac_cv_lib_socket_getaddrinfo" >&6; }
if test "x$ac_cv_lib_socket_getaddrinfo" = xyes
then :
  
	    #
	    # OK, we found it in libsocket.
	    #
	    LIBS="-lsocket -lnsl $LIBS"
	
else $as_nop
  
	    #
	    # Not found in libsocket; test for it in libnetwork, which
	    # is where it is in Haiku.
	    #
	    # Linking with libnetwork will find all the routines we
	    # need.
	    #
	    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for getaddrinfo in -lnetwork" >&5
printf %s "checking for getaddrinfo in -lnetwork... " >&6; }
if test ${ac_cv_lib_network_getaddrinfo+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lnetwork  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char getaddrinfo ();
int
main (void)
{
return getaddrinfo ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_network_getaddrinfo=yes
else $as_nop
  ac_cv_lib_network_getaddrinfo=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_network_getaddrinfo" >&5
printf "%s\n" "$ac_cv_lib_network_getaddrinfo" >&6; }
if test "x$ac_cv_lib_network_getaddrinfo" = xyes
then :
  
		#
		# OK, we found it in libnetwork.
		#
		LIBS="-lnetwork $LIBS"
	    
else $as_nop
  
		#
		# We didn't find it.
		#
		as_fn_error $? "getaddrinfo is required, but wasn't found" "$LINENO" 5
	    
fi

	
fi


	#
	# We require a version of recvmsg() that conforms to the Single
	# UNIX Specification, so that we can check whether a datagram
	# received with recvmsg() was truncated when received due to the
	# buffer being too small.
	#
	# On most systems, the version of recvmsg() in the libraries
	# found above conforms to the SUS.
	#
	# On at least some versions of Solaris, it does not conform to
	# the SUS, and we need the version in libxnet, which does
	# conform.
	#
	# Check whether libxnet exists and has a version of recvmsg();
	# if it does, link with libxnet before we link with libsocket,
	# to get that version.
	#
	# This test also links with libsocket and libnsl.
	#
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for recvmsg in -lxnet" >&5
printf %s "checking for recvmsg in -lxnet... " >&6; }
if test ${ac_cv_lib_xnet_recvmsg+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lxnet -lsocket -lnsl $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char recvmsg ();
int
main (void)
{
return recvmsg ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_xnet_recvmsg=yes
else $as_nop
  ac_cv_lib_xnet_recvmsg=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_xnet_recvmsg" >&5
printf "%s\n" "$ac_cv_lib_xnet_recvmsg" >&6; }
if test "x$ac_cv_lib_xnet_recvmsg" = xyes
then :
  
	    #
	    # libxnet has recvmsg(); link with it as well.
	    #
	    LIBS="-lxnet $LIBS"
	
fi

    
fi



#
# Check for reentrant versions of getnetbyname_r(), as provided by
# Linux (glibc), Solaris, and AIX (with three different APIs!).
# If we don't find one, we just use getnetbyname(), which uses
# thread-specific data on many platforms, but doesn't use it on
# NetBSD or OpenBSD, and may not use it on older versions of other
# platforms.
#
# Only do the check if we have a declaration of getnetbyname_r();
# without it, we can't check which API it has.  (We assume that
# if there's a declaration, it has a prototype, so that the API
# can be checked.)
#
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $CC options needed to detect all undeclared functions" >&5
printf %s "checking for $CC options needed to detect all undeclared functions... " >&6; }
if test ${ac_cv_c_undeclared_builtin_options+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_save_CFLAGS=$CFLAGS
   ac_cv_c_undeclared_builtin_options='cannot detect'
   for ac_arg in '' -fno-builtin; do
     CFLAGS="$ac_save_CFLAGS $ac_arg"
     # This test program should *not* compile successfully.
     cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{
(void) strchr;
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
else $as_nop
  # This test program should compile successfully.
        # No library function is consistently available on
        # freestanding implementations, so test against a dummy
        # declaration.  Include always-available headers on the
        # off chance that they somehow elicit warnings.
        cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <float.h>
#include <limits.h>
#include <stdarg.h>
#include <stddef.h>
extern void ac_decl (int, char *);

int
main (void)
{
(void) ac_decl (0, (char *) 0);
  (void) ac_decl;

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  if test x"$ac_arg" = x
then :
  ac_cv_c_undeclared_builtin_options='none needed'
else $as_nop
  ac_cv_c_undeclared_builtin_options=$ac_arg
fi
          break
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
    done
    CFLAGS=$ac_save_CFLAGS
  
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_c_undeclared_builtin_options" >&5
printf "%s\n" "$ac_cv_c_undeclared_builtin_options" >&6; }
  case $ac_cv_c_undeclared_builtin_options in @%:@(
  'cannot detect') :
    { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error $? "cannot make $CC report undeclared builtins
See \`config.log' for more details" "$LINENO" 5; } ;; @%:@(
  'none needed') :
    ac_c_undeclared_builtin_options='' ;; @%:@(
  *) :
    ac_c_undeclared_builtin_options=$ac_cv_c_undeclared_builtin_options ;;
esac

ac_fn_check_decl "$LINENO" "getnetbyname_r" "ac_cv_have_decl_getnetbyname_r" "#include <netdb.h>
" "$ac_c_undeclared_builtin_options" "CFLAGS"
if test "x$ac_cv_have_decl_getnetbyname_r" = xyes
then :
  
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for the Linux getnetbyname_r()" >&5
printf %s "checking for the Linux getnetbyname_r()... " >&6; }
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <netdb.h>
int
main (void)
{

		struct netent netent_buf;
		char buf[1024];
		struct netent *resultp;
		int h_errnoval;

		return getnetbyname_r((const char *)0, &netent_buf, buf, sizeof buf, &resultp, &h_errnoval);
	    
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		
printf "%s\n" "@%:@define HAVE_LINUX_GETNETBYNAME_R 1" >>confdefs.h

	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }

		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for Solaris getnetbyname_r()" >&5
printf %s "checking for Solaris getnetbyname_r()... " >&6; }
		cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <netdb.h>
int
main (void)
{

			struct netent netent_buf;
			char buf[1024];

			return getnetbyname_r((const char *)0, &netent_buf, buf, (int)sizeof buf) != NULL;
		    
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			
printf "%s\n" "@%:@define HAVE_SOLARIS_GETNETBYNAME_R 1" >>confdefs.h

		    
else $as_nop
  
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }

			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for AIX getnetbyname_r()" >&5
printf %s "checking for AIX getnetbyname_r()... " >&6; }
			cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <netdb.h>
int
main (void)
{

				struct netent netent_buf;
				struct netent_data net_data;

				return getnetbyname_r((const char *)0, &netent_buf, &net_data);
			    
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  
				{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
				
printf "%s\n" "@%:@define HAVE_AIX_GETNETBYNAME_R 1" >>confdefs.h

			    
else $as_nop
  
				{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
			    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
		    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
    
fi

#
# Check for reentrant versions of getprotobyname_r(), as provided by
# Linux (glibc), Solaris, and AIX (with three different APIs!).
# If we don't find one, we just use getprotobyname(), which uses
# thread-specific data on many platforms, but doesn't use it on
# NetBSD or OpenBSD, and may not use it on older versions of other
# platforms.
#
# Only do the check if we have a declaration of getprotobyname_r();
# without it, we can't check which API it has.  (We assume that
# if there's a declaration, it has a prototype, so that the API
# can be checked.)
#
ac_fn_check_decl "$LINENO" "getprotobyname_r" "ac_cv_have_decl_getprotobyname_r" "#include <netdb.h>
" "$ac_c_undeclared_builtin_options" "CFLAGS"
if test "x$ac_cv_have_decl_getprotobyname_r" = xyes
then :
  
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for the Linux getprotobyname_r()" >&5
printf %s "checking for the Linux getprotobyname_r()... " >&6; }
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <netdb.h>
int
main (void)
{

		struct protoent protoent_buf;
		char buf[1024];
		struct protoent *resultp;

		return getprotobyname_r((const char *)0, &protoent_buf, buf, sizeof buf, &resultp);
	    
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		
printf "%s\n" "@%:@define HAVE_LINUX_GETPROTOBYNAME_R 1" >>confdefs.h

	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }

		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for Solaris getprotobyname_r()" >&5
printf %s "checking for Solaris getprotobyname_r()... " >&6; }
		cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <netdb.h>
int
main (void)
{

			struct protoent protoent_buf;
			char buf[1024];

			return getprotobyname_r((const char *)0, &protoent_buf, buf, (int)sizeof buf) != NULL;
		    
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			
printf "%s\n" "@%:@define HAVE_SOLARIS_GETPROTOBYNAME_R 1" >>confdefs.h

		    
else $as_nop
  
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }

			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for AIX getprotobyname_r()" >&5
printf %s "checking for AIX getprotobyname_r()... " >&6; }
			cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <netdb.h>
int
main (void)
{

				struct protoent protoent_buf;
				struct protoent_data proto_data;

				return getprotobyname_r((const char *)0, &protoent_buf, &proto_data);
			    
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  
				{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
				
printf "%s\n" "@%:@define HAVE_AIX_GETPROTOBYNAME_R 1" >>confdefs.h

			    
else $as_nop
  
				{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
			    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
		    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
    
fi

#
# You are in a twisty little maze of UN*Xes, all different.
# Some might not have ether_hostton().
# Some might have it and declare it in <net/ethernet.h>.
# Some might have it and declare it in <netinet/ether.h>
# Some might have it and declare it in <sys/ethernet.h>.
# Some might have it and declare it in <arpa/inet.h>.
# Some might have it and declare it in <netinet/if_ether.h>.
# Some might have it and not declare it in any header file.
#
# Before you is a C compiler.
#
ac_fn_c_check_func "$LINENO" "ether_hostton" "ac_cv_func_ether_hostton"
if test "x$ac_cv_func_ether_hostton" = xyes
then :
  printf "%s\n" "@%:@define HAVE_ETHER_HOSTTON 1" >>confdefs.h

fi

if test "$ac_cv_func_ether_hostton" = yes; then
	#
	# OK, we have ether_hostton().  Is it declared in <net/ethernet.h>?
	#
	# This test fails if we don't have <net/ethernet.h> or if we do
	# but it doesn't declare ether_hostton().
	#
	ac_fn_check_decl "$LINENO" "ether_hostton" "ac_cv_have_decl_ether_hostton" "
#include <net/ethernet.h>
	    
" "$ac_c_undeclared_builtin_options" "CFLAGS"
if test "x$ac_cv_have_decl_ether_hostton" = xyes
then :
  
		
printf "%s\n" "@%:@define NET_ETHERNET_H_DECLARES_ETHER_HOSTTON 1" >>confdefs.h

	    
fi
	#
	# Did that succeed?
	#
	if test "$ac_cv_have_decl_ether_hostton" != yes; then
		#
		# No, how about <netinet/ether.h>, as on Linux?
		#
		# This test fails if we don't have <netinet/ether.h>
		# or if we do but it doesn't declare ether_hostton().
		#
		# Unset ac_cv_have_decl_ether_hostton so we don't
		# treat the previous failure as a cached value and
		# suppress the next test.
		#
		unset ac_cv_have_decl_ether_hostton
		ac_fn_check_decl "$LINENO" "ether_hostton" "ac_cv_have_decl_ether_hostton" "
#include <netinet/ether.h>
		    
" "$ac_c_undeclared_builtin_options" "CFLAGS"
if test "x$ac_cv_have_decl_ether_hostton" = xyes
then :
  
			
printf "%s\n" "@%:@define NETINET_ETHER_H_DECLARES_ETHER_HOSTTON 1" >>confdefs.h

		    
fi
	fi
	#
	# Did that succeed?
	#
	if test "$ac_cv_have_decl_ether_hostton" != yes; then
		#
		# No, how about <sys/ethernet.h>, as on Solaris 10
		# and later?
		#
		# This test fails if we don't have <sys/ethernet.h>
		# or if we do but it doesn't declare ether_hostton().
		#
		# Unset ac_cv_have_decl_ether_hostton so we don't
		# treat the previous failure as a cached value and
		# suppress the next test.
		#
		unset ac_cv_have_decl_ether_hostton
		ac_fn_check_decl "$LINENO" "ether_hostton" "ac_cv_have_decl_ether_hostton" "
#include <sys/ethernet.h>
		    
" "$ac_c_undeclared_builtin_options" "CFLAGS"
if test "x$ac_cv_have_decl_ether_hostton" = xyes
then :
  
			
printf "%s\n" "@%:@define SYS_ETHERNET_H_DECLARES_ETHER_HOSTTON 1" >>confdefs.h

		    
fi
	fi
	#
	# Did that succeed?
	#
	if test "$ac_cv_have_decl_ether_hostton" != yes; then
		#
		# No, how about <arpa/inet.h>, as in AIX?
		#
		# This test fails if we don't have <arpa/inet.h>
		# (if we have ether_hostton(), we should have
		# networking, and if we have networking, we should
		# have <arpa/inet.h>) or if we do but it doesn't
		# declare ether_hostton().
		#
		# Unset ac_cv_have_decl_ether_hostton so we don't
		# treat the previous failure as a cached value and
		# suppress the next test.
		#
		unset ac_cv_have_decl_ether_hostton
		ac_fn_check_decl "$LINENO" "ether_hostton" "ac_cv_have_decl_ether_hostton" "
#include <arpa/inet.h>
		    
" "$ac_c_undeclared_builtin_options" "CFLAGS"
if test "x$ac_cv_have_decl_ether_hostton" = xyes
then :
  
			
printf "%s\n" "@%:@define ARPA_INET_H_DECLARES_ETHER_HOSTTON 1" >>confdefs.h

		    
fi
	fi
	#
	# Did that succeed?
	#
	if test "$ac_cv_have_decl_ether_hostton" != yes; then
		#
		# No, how about <netinet/if_ether.h>?
		# On some platforms, it requires <net/if.h> and
		# <netinet/in.h>, and we always include it with
		# both of them, so test it with both of them.
		#
		# This test fails if we don't have <netinet/if_ether.h>
		# and the headers we include before it, or if we do but
		# <netinet/if_ether.h> doesn't declare ether_hostton().
		#
		# Unset ac_cv_have_decl_ether_hostton so we don't
		# treat the previous failure as a cached value and
		# suppress the next test.
		#
		unset ac_cv_have_decl_ether_hostton
		ac_fn_check_decl "$LINENO" "ether_hostton" "ac_cv_have_decl_ether_hostton" "
#include <sys/types.h>
#include <sys/socket.h>
#include <net/if.h>
#include <netinet/in.h>
#include <netinet/if_ether.h>
		    
" "$ac_c_undeclared_builtin_options" "CFLAGS"
if test "x$ac_cv_have_decl_ether_hostton" = xyes
then :
  
			
printf "%s\n" "@%:@define NETINET_IF_ETHER_H_DECLARES_ETHER_HOSTTON 1" >>confdefs.h

		    
fi
	fi
	#
	# After all that, is ether_hostton() declared?
	#
	if test "$ac_cv_have_decl_ether_hostton" = yes; then
		#
		# Yes.
		#
		
printf "%s\n" "@%:@define HAVE_DECL_ETHER_HOSTTON 1" >>confdefs.h

        else
		#
		# No, we'll have to declare it ourselves.
		# Do we have "struct ether_addr" if we include
		# <netinet/if_ether.h>?
		#
		ac_fn_c_check_type "$LINENO" "struct ether_addr" "ac_cv_type_struct_ether_addr" "
			#include <sys/types.h>
			#include <sys/socket.h>
			#include <net/if.h>
			#include <netinet/in.h>
			#include <netinet/if_ether.h>
		    
"
if test "x$ac_cv_type_struct_ether_addr" = xyes
then :
  
printf "%s\n" "@%:@define HAVE_STRUCT_ETHER_ADDR 1" >>confdefs.h


fi

	fi
fi

if expr "$host_os" : linux >/dev/null; then
	#
	# On Linux there is a couple more factors to consider together with
	# HAVE_ETHER_HOSTTON.  C code can test for __GLIBC__ and __UCLIBC__
	# trivially, make it nearly as trivial for TESTrun.
	#
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking if features.h defines __GLIBC__" >&5
printf %s "checking if features.h defines __GLIBC__... " >&6; }
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

			#include <features.h>
int
main (void)
{
int i = __GLIBC__;
			
  ;
  return 0;
}
		
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			
printf "%s\n" "@%:@define HAVE_GLIBC 1" >>confdefs.h

			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		
else $as_nop
  
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking if features.h defines __UCLIBC__" >&5
printf %s "checking if features.h defines __UCLIBC__... " >&6; }
			cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

					#include <features.h>
int
main (void)
{
int i = __UCLIBC__;
					
  ;
  return 0;
}
				
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
					
printf "%s\n" "@%:@define HAVE_UCLIBC 1" >>confdefs.h

					{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
				
else $as_nop
  
					{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
				
			
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		
	
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi

#
# For various things that might use pthreads.
#
ac_fn_c_check_header_compile "$LINENO" "pthread.h" "ac_cv_header_pthread_h" "$ac_includes_default"
if test "x$ac_cv_header_pthread_h" = xyes
then :
  
	#
	# OK, we have pthread.h.  Do we have pthread_create in the
	# system libraries?
	#
	ac_fn_c_check_func "$LINENO" "pthread_create" "ac_cv_func_pthread_create"
if test "x$ac_cv_func_pthread_create" = xyes
then :
  
		#
		# Yes.
		#
		ac_lbl_have_pthreads="found"
	    
else $as_nop
  
		#
		# No - do we have it in -lpthreads?
		#
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for pthread_create in -lpthreads" >&5
printf %s "checking for pthread_create in -lpthreads... " >&6; }
if test ${ac_cv_lib_pthreads_pthread_create+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lpthreads  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char pthread_create ();
int
main (void)
{
return pthread_create ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_pthreads_pthread_create=yes
else $as_nop
  ac_cv_lib_pthreads_pthread_create=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_pthreads_pthread_create" >&5
printf "%s\n" "$ac_cv_lib_pthreads_pthread_create" >&6; }
if test "x$ac_cv_lib_pthreads_pthread_create" = xyes
then :
  
			#
			# Yes - add -lpthreads.
			#
			ac_lbl_have_pthreads="found"
			PTHREAD_LIBS="$PTHREAD_LIBS -lpthreads"
		    
else $as_nop
  
			#
			# No - do we have it in -lpthread?
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for pthread_create in -lpthread" >&5
printf %s "checking for pthread_create in -lpthread... " >&6; }
if test ${ac_cv_lib_pthread_pthread_create+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lpthread  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char pthread_create ();
int
main (void)
{
return pthread_create ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_pthread_pthread_create=yes
else $as_nop
  ac_cv_lib_pthread_pthread_create=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_pthread_pthread_create" >&5
printf "%s\n" "$ac_cv_lib_pthread_pthread_create" >&6; }
if test "x$ac_cv_lib_pthread_pthread_create" = xyes
then :
  
				#
				# Yes - add -lpthread.
				#
                                ac_lbl_have_pthreads="found"
				PTHREAD_LIBS="$PTHREAD_LIBS -lpthread"
			    
else $as_nop
  
				#
				# No.
				#
				ac_lbl_have_pthreads="not found"
			    
fi

		    
fi

	    
fi

    
else $as_nop
  
	#
	# We didn't find pthread.h.
	#
	ac_lbl_have_pthreads="not found"
    

fi


{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether to enable the instrument functions code" >&5
printf %s "checking whether to enable the instrument functions code... " >&6; }
@%:@ Check whether --enable-instrument-functions was given.
if test ${enable_instrument_functions+y}
then :
  enableval=$enable_instrument_functions; 
else $as_nop
  enableval=no
fi

case "$enableval" in
yes)	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
	
printf "%s\n" "@%:@define ENABLE_INSTRUMENT_FUNCTIONS 1" >>confdefs.h

	# Add '-finstrument-functions' instrumentation option to generate
	# instrumentation calls for entry and exit to functions.
	# Use '--enable-instrument-functions' also with tcpdump (or tcpslice)
	# to see the output. See also https://www.tcpdump.org/faq.html#q17.
	CFLAGS="$CFLAGS -O0 -ggdb -finstrument-functions"
	;;
*)	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
	;;
esac

{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking if --disable-protochain option is specified" >&5
printf %s "checking if --disable-protochain option is specified... " >&6; }
@%:@ Check whether --enable-protochain was given.
if test ${enable_protochain+y}
then :
  enableval=$enable_protochain; 
fi

case "x$enable_protochain" in
xyes)	enable_protochain=enabled	;;
xno)	enable_protochain=disabled	;;
x)	enable_protochain=enabled	;;
esac

if test "$enable_protochain" = "disabled"; then
	
printf "%s\n" "@%:@define NO_PROTOCHAIN 1" >>confdefs.h

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: ${enable_protochain}" >&5
printf "%s\n" "${enable_protochain}" >&6; }


@%:@ Check whether --with-pcap was given.
if test ${with_pcap+y}
then :
  withval=$with_pcap; 
fi

if test ! -z "$with_pcap" ; then
	V_PCAP="$withval"
else
	#
	# Try auto-detecting the obvious types first.
	#
	case "$host_os" in
	linux*)
		V_PCAP=linux
		;;
	haiku)
		V_PCAP=haiku
		;;
	gnu*)
		V_PCAP=hurd
		;;
	esac
fi

if test -z "$V_PCAP"; then
	#
	# It is not one of the above obvious types, let's see if it is BPF.
	# Check this before DLPI to pick BPF on Solaris 11 and later.
	#
	ac_fn_c_check_header_compile "$LINENO" "net/bpf.h" "ac_cv_header_net_bpf_h" "$ac_includes_default"
if test "x$ac_cv_header_net_bpf_h" = xyes
then :
  printf "%s\n" "@%:@define HAVE_NET_BPF_H 1" >>confdefs.h

fi

	if test "$ac_cv_header_net_bpf_h" = yes; then
		#
		# HAVE_SYS_IOCCOM_H will be required for a few workarounds until all
		# supported OSes that use BPF have <net/bpf.h> that includes <sys/ioccom.h>
		# (this might have already happened).
		#
		ac_fn_c_check_header_compile "$LINENO" "sys/ioccom.h" "ac_cv_header_sys_ioccom_h" "$ac_includes_default"
if test "x$ac_cv_header_sys_ioccom_h" = xyes
then :
  printf "%s\n" "@%:@define HAVE_SYS_IOCCOM_H 1" >>confdefs.h

fi


		#
		# Does it define BIOCSETIF?
		# I.e., is it a header for an LBL/BSD-style capture
		# mechanism, or is it just a header for a BPF filter
		# engine?  Some versions of Arch Linux, for example,
		# have a net/bpf.h that doesn't define BIOCSETIF;
		# as it's a Linux, it should use packet sockets,
		# instead.
		#
		# We need:
		#
		#  sys/types.h, because FreeBSD 10's net/bpf.h
		#  requires that various BSD-style integer types
		#  be defined;
		#
		#  sys/time.h, because AIX 5.2 and 5.3's net/bpf.h
		#  doesn't include it but does use struct timeval
		#  in ioctl definitions;
		#
		#  sys/ioctl.h and, if we have it, sys/ioccom.h,
		#  because net/bpf.h defines ioctls;
		#
		#  net/if.h, because it defines some structures
		#  used in ioctls defined by net/bpf.h;
		#
		#  sys/socket.h, because OpenBSD 5.9's net/bpf.h
		#  defines some structure fields as being
		#  struct sockaddrs;
		#
		# and net/bpf.h doesn't necessarily include all
		# of those headers itself.
		#
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking if net/bpf.h defines BIOCSETIF" >&5
printf %s "checking if net/bpf.h defines BIOCSETIF... " >&6; }
		if test ${ac_cv_lbl_bpf_h_defines_biocsetif+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

#include <sys/types.h>
#include <sys/time.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#ifdef HAVE_SYS_IOCCOM_H
#include <sys/ioccom.h>
#endif
#include <net/bpf.h>
#include <net/if.h>

int
main (void)
{
u_int i = BIOCSETIF;
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_lbl_bpf_h_defines_biocsetif=yes
else $as_nop
  ac_cv_lbl_bpf_h_defines_biocsetif=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi

		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lbl_bpf_h_defines_biocsetif" >&5
printf "%s\n" "$ac_cv_lbl_bpf_h_defines_biocsetif" >&6; }
	fi

	if test "$ac_cv_lbl_bpf_h_defines_biocsetif" = yes; then
		V_PCAP=bpf
	fi
fi

if test -z "$V_PCAP"; then
	#
	# It is not BPF either, let's see if it is DLPI on pre-Solaris 11
	# SunOS 5, HP-UX, possibly others.  Otherwise fail properly.
	#
	ac_fn_c_check_header_compile "$LINENO" "sys/dlpi.h" "ac_cv_header_sys_dlpi_h" "$ac_includes_default"
if test "x$ac_cv_header_sys_dlpi_h" = xyes
then :
  printf "%s\n" "@%:@define HAVE_SYS_DLPI_H 1" >>confdefs.h

fi

	if test "$ac_cv_header_sys_dlpi_h" = yes; then
		V_PCAP=dlpi
	else
		#
		# We don't have any capture type we know about.
		# Report an error, and tell the user to configure with
		# --with-pcap=null if they want a libpcap that can't
		# capture but that can read capture files.  That way,
		# nobody gets surprised by getting a no-capture
		# libpcap without asking for that.
		#
		as_fn_error $? "No supported packet capture interface was found.
 See the INSTALL.md file for information on packet capture support in
 various operating systems.
 If you want a libpcap that cannot capture packets but that can read
 pcap and pcapng files, run configure with --with-pcap=null." "$LINENO" 5
	fi
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking packet capture type" >&5
printf %s "checking packet capture type... " >&6; }
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $V_PCAP" >&5
printf "%s\n" "$V_PCAP" >&6; }

#
# valgrindtest directly uses the native capture mechanism, but
# only tests with BPF and PF_PACKET sockets; enable it for OSes
# that have both the type of sockets and a working Valgrind.
#
case "$host_os" in
freebsd*|darwin*|linux*)
	VALGRINDTEST_SRC=valgrindtest.c
	;;
*)
	VALGRINDTEST_SRC=
	;;
esac


#
# Do we have pkg-config?
#







if test "x$ac_cv_env_PKG_CONFIG_set" != "xset"; then
	if test -n "$ac_tool_prefix"; then
  # Extract the first word of "${ac_tool_prefix}pkg-config", so it can be a program name with args.
set dummy ${ac_tool_prefix}pkg-config; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_PKG_CONFIG+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $PKG_CONFIG in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_PKG_CONFIG="$PKG_CONFIG" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_PKG_CONFIG="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
PKG_CONFIG=$ac_cv_path_PKG_CONFIG
if test -n "$PKG_CONFIG"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $PKG_CONFIG" >&5
printf "%s\n" "$PKG_CONFIG" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$ac_cv_path_PKG_CONFIG"; then
  ac_pt_PKG_CONFIG=$PKG_CONFIG
  # Extract the first word of "pkg-config", so it can be a program name with args.
set dummy pkg-config; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_ac_pt_PKG_CONFIG+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $ac_pt_PKG_CONFIG in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_ac_pt_PKG_CONFIG="$ac_pt_PKG_CONFIG" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_ac_pt_PKG_CONFIG="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
ac_pt_PKG_CONFIG=$ac_cv_path_ac_pt_PKG_CONFIG
if test -n "$ac_pt_PKG_CONFIG"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_pt_PKG_CONFIG" >&5
printf "%s\n" "$ac_pt_PKG_CONFIG" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

  if test "x$ac_pt_PKG_CONFIG" = x; then
    PKG_CONFIG=""
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    PKG_CONFIG=$ac_pt_PKG_CONFIG
  fi
else
  PKG_CONFIG="$ac_cv_path_PKG_CONFIG"
fi

fi
if test -n "$PKG_CONFIG"; then
	_pkg_min_version=0.17.0
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking pkg-config is at least version $_pkg_min_version" >&5
printf %s "checking pkg-config is at least version $_pkg_min_version... " >&6; }
	if $PKG_CONFIG --atleast-pkgconfig-version $_pkg_min_version; then
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
	else
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		PKG_CONFIG=""
	fi
fi

#
# Do we have the brew command from Homebrew?
#
case "$host_os" in
darwin*|linux*)
	# Extract the first word of "brew", so it can be a program name with args.
set dummy brew; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_BREW+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $BREW in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_BREW="$BREW" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_BREW="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
BREW=$ac_cv_path_BREW
if test -n "$BREW"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $BREW" >&5
printf "%s\n" "$BREW" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


	;;
esac

#
# Solaris pkg-config is annoying.  For at least one package (D-Bus, I'm
# looking at *you*!), there are separate include files for 32-bit and
# 64-bit builds (I guess using "unsigned long long" as a 64-bit integer
# type on a 64-bit build is like crossing the beams or something), and
# there are two separate .pc files, so if we're doing a 32-bit build we
# should make sure we look in /usr/lib/pkgconfig for .pc files and if
# we're doing a 64-bit build we should make sure we look in
# /usr/lib/amd64/pkgconfig for .pc files.
#
case "$host_os" in

solaris*)
	if test "$ac_cv_sizeof_void_p" -eq 8; then
		#
		# 64-bit build.  If the path is empty, set it to
                # /usr/lib/amd64/pkgconfig; otherwise, if
                # /usr/lib/pkgconfig appears in the path, prepend
		# /usr/lib/amd64/pkgconfig to it; otherwise, put
		# /usr/lib/amd64/pkgconfig at the end.
		#
		if test -z "$PKG_CONFIG_PATH"; then
			#
			# Not set, or empty.  Set it to
			# /usr/lib/amd64/pkgconfig.
			#
			PKG_CONFIG_PATH=/usr/lib/amd64/pkgconfig
		elif test ! -z `echo "$PKG_CONFIG_PATH" | grep "/usr/lib/pkgconfig"`; then
			#
			# It contains /usr/lib/pkgconfig.  Prepend
			# /usr/lib/amd64/pkgconfig to /usr/lib/pkgconfig.
			#
			PKG_CONFIG_PATH=`echo "$PKG_CONFIG_PATH" | sed "s;/usr/lib/pkgconfig;/usr/lib/amd64/pkgconfig:/usr/lib/pkgconfig;"`
		else
			#
			# Not empty, but doesn't contain /usr/lib/pkgconfig.
			# Append /usr/lib/amd64/pkgconfig to it.
			#
			PKG_CONFIG_PATH="$PKG_CONFIG_PATH:/usr/lib/amd64/pkgconfig"
		fi
		export PKG_CONFIG_PATH
	elif test "$ac_cv_sizeof_void_p" -eq 4; then
		#
		# 32-bit build.  If /usr/amd64/lib/pkgconfig appears
		# in the path, prepend /usr/lib/pkgconfig to it.
		#
		if test ! -z `echo "$PKG_CONFIG_PATH" | grep "/usr/lib/amd64/pkgconfig"`; then
			#
			# It contains /usr/lib/amd64/pkgconfig.  Prepend
			# /usr/lib/pkgconfig to /usr/lib/amd64/pkgconfig.
			#
			PKG_CONFIG_PATH=`echo "$PKG_CONFIG_PATH" | sed "s;/usr/lib/amd64/pkgconfig;/usr/lib/pkgconfig:/usr/lib/amd64/pkgconfig;"`
			export PKG_CONFIG_PATH
		fi
	fi
esac

#
# Handle each capture type.
#
case "$V_PCAP" in
dlpi)
	#
	# Checks for some header files.
	#
	ac_fn_c_check_header_compile "$LINENO" "sys/bufmod.h" "ac_cv_header_sys_bufmod_h" "$ac_includes_default"
if test "x$ac_cv_header_sys_bufmod_h" = xyes
then :
  printf "%s\n" "@%:@define HAVE_SYS_BUFMOD_H 1" >>confdefs.h

fi
ac_fn_c_check_header_compile "$LINENO" "sys/dlpi_ext.h" "ac_cv_header_sys_dlpi_ext_h" "$ac_includes_default"
if test "x$ac_cv_header_sys_dlpi_ext_h" = xyes
then :
  printf "%s\n" "@%:@define HAVE_SYS_DLPI_EXT_H 1" >>confdefs.h

fi


	#
	# Checks to see if Solaris has the public libdlpi(3LIB) library.
	# Note: The existence of /usr/include/libdlpi.h does not mean it is the
	# public libdlpi(3LIB) version. Before libdlpi was made public, a
	# private version also existed, which did not have the same APIs.
	# Due to a gcc bug, the default search path for 32-bit libraries does
	# not include /lib, we add it explicitly here.
	# [http://bugs.opensolaris.org/view_bug.do?bug_id=6619485].
	# Also, due to the bug above applications that link to libpcap with
	# libdlpi will have to add "-L/lib" option to "configure".
	#
	save_LDFLAGS="$LDFLAGS"
	LDFLAGS="$LIBS -L/lib"
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for dlpi_walk in -ldlpi" >&5
printf %s "checking for dlpi_walk in -ldlpi... " >&6; }
if test ${ac_cv_lib_dlpi_dlpi_walk+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-ldlpi  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char dlpi_walk ();
int
main (void)
{
return dlpi_walk ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_dlpi_dlpi_walk=yes
else $as_nop
  ac_cv_lib_dlpi_dlpi_walk=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_dlpi_dlpi_walk" >&5
printf "%s\n" "$ac_cv_lib_dlpi_dlpi_walk" >&6; }
if test "x$ac_cv_lib_dlpi_dlpi_walk" = xyes
then :
  
			LIBS="-ldlpi $LIBS"
			LIBS_STATIC="-ldlpi $LIBS_STATIC"
			LIBS_PRIVATE="-ldlpi $LIBS_PRIVATE"
			V_PCAP=libdlpi

			#
			# Capture module plus common code needed for
			# common functions used by pcap-[dlpi,libdlpi].c
			#
			PLATFORM_C_SRC="pcap-libdlpi.c dlpisubs.c"
			
printf "%s\n" "@%:@define HAVE_LIBDLPI 1" >>confdefs.h

		
else $as_nop
  
			V_PCAP=dlpi

			#
			# Capture module plus common code needed for
			# common functions used by pcap-[dlpi,libdlpi].c
			#
			PLATFORM_C_SRC="pcap-dlpi.c dlpisubs.c"
		
fi

	LDFLAGS="$save_LDFLAGS"

	#
	# Checks whether <sys/dlpi.h> is usable, to catch weird SCO
	# versions of DLPI.
	#
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether <sys/dlpi.h> is usable" >&5
printf %s "checking whether <sys/dlpi.h> is usable... " >&6; }
	if test ${ac_cv_sys_dlpi_usable+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

			#include <sys/types.h>
			#include <sys/time.h>
			#include <sys/dlpi.h>
		    
int
main (void)
{
int i = DL_PROMISC_PHYS;
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_sys_dlpi_usable=yes
else $as_nop
  ac_cv_sys_dlpi_usable=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi

	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_sys_dlpi_usable" >&5
printf "%s\n" "$ac_cv_sys_dlpi_usable" >&6; }
	if test $ac_cv_sys_dlpi_usable = no ; then
		as_fn_error $? "<sys/dlpi.h> is not usable on this system; it probably has a non-standard DLPI" "$LINENO" 5
	fi

	#
	# Check to see if Solaris has the dl_passive_req_t struct defined
	# in <sys/dlpi.h>.
	# This check is for DLPI support for passive modes.
	# See dlpi(7P) for more details.
	#
	ac_fn_c_check_type "$LINENO" "dl_passive_req_t" "ac_cv_type_dl_passive_req_t" "
		#include <sys/types.h>
		#include <sys/dlpi.h>
	    
"
if test "x$ac_cv_type_dl_passive_req_t" = xyes
then :
  
printf "%s\n" "@%:@define HAVE_DL_PASSIVE_REQ_T 1" >>confdefs.h


fi

	;;

haiku)
	#
	# Capture module
	#
	PLATFORM_C_SRC="pcap-haiku.c"
	;;

linux)
	#
	# Capture module
	#
	PLATFORM_C_SRC="pcap-linux.c"

	#
	# Do we have libnl?
	# We only want version 3.  Version 2 was, apparently,
	# short-lived, and version 1 is source and binary
	# incompatible with version 3, and it appears that,
	# these days, everybody's using version 3.  We're
	# not supporting older versions of the Linux kernel;
	# let's drop support for older versions of libnl, too.
	#
	
@%:@ Check whether --with-libnl was given.
if test ${with_libnl+y}
then :
  withval=$with_libnl; with_libnl=$withval
else $as_nop
  with_libnl=if_available
fi


	if test x$with_libnl != xno ; then
		#
		# Check for libnl-genl-3.0 with pkg-config.
		#
		
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for libnl-genl-3.0 with pkg-config" >&5
printf %s "checking for libnl-genl-3.0 with pkg-config... " >&6; }
if test -n "$PKG_CONFIG"; then
            
    if { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$PKG_CONFIG --exists --print-errors \"libnl-genl-3.0\""; } >&5
  ($PKG_CONFIG --exists --print-errors "libnl-genl-3.0") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }; then
	#
	# The package was found, so try to get its C flags and
	# libraries.
	#
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: found" >&5
printf "%s\n" "found" >&6; }
	if test ! -n "$LIBNL_CFLAGS"; then
    LIBNL_CFLAGS=`$PKG_CONFIG --cflags "libnl-genl-3.0" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --cflags "libnl-genl-3.0" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --cflags "libnl-genl-3.0" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --cflags \"libnl-genl-3.0\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
	if test ! -n "$LIBNL_LIBS"; then
    LIBNL_LIBS=`$PKG_CONFIG --libs "libnl-genl-3.0" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --libs "libnl-genl-3.0" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --libs "libnl-genl-3.0" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --libs \"libnl-genl-3.0\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
	if test ! -n "$LIBNL_LIBS_STATIC"; then
    LIBNL_LIBS_STATIC=`$PKG_CONFIG --libs --static "libnl-genl-3.0" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --libs --static "libnl-genl-3.0" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --libs --static "libnl-genl-3.0" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --libs --static \"libnl-genl-3.0\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
        
			pkg_config_found_libnl=yes
			V_INCLS="$V_INCLS $LIBNL_CFLAGS"
			ADDITIONAL_LIBS="$LIBNL_LIBS $ADDITIONAL_LIBS"
			ADDITIONAL_LIBS_STATIC="$LIBNL_LIBS_STATIC $ADDITIONAL_LIBS_STATIC"
			REQUIRES_PRIVATE="libnl-genl-3.0 $REQUIRES_PRIVATE"
			
printf "%s\n" "@%:@define HAVE_LIBNL 1" >>confdefs.h

		    
    else
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: not found" >&5
printf "%s\n" "not found" >&6; }
        :
    fi
else
    # No pkg-config, so obviously not found with pkg-config.
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: pkg-config not found" >&5
printf "%s\n" "pkg-config not found" >&6; }
    :
fi


		if test x$pkg_config_found_libnl != xyes; then
			#
			# Check for libnl-tiny with pkg-config.
			#
			
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for libnl-tiny with pkg-config" >&5
printf %s "checking for libnl-tiny with pkg-config... " >&6; }
if test -n "$PKG_CONFIG"; then
            
    if { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$PKG_CONFIG --exists --print-errors \"libnl-tiny\""; } >&5
  ($PKG_CONFIG --exists --print-errors "libnl-tiny") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }; then
	#
	# The package was found, so try to get its C flags and
	# libraries.
	#
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: found" >&5
printf "%s\n" "found" >&6; }
	if test ! -n "$LIBNL_CFLAGS"; then
    LIBNL_CFLAGS=`$PKG_CONFIG --cflags "libnl-tiny" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --cflags "libnl-tiny" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --cflags "libnl-tiny" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --cflags \"libnl-tiny\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
	if test ! -n "$LIBNL_LIBS"; then
    LIBNL_LIBS=`$PKG_CONFIG --libs "libnl-tiny" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --libs "libnl-tiny" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --libs "libnl-tiny" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --libs \"libnl-tiny\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
	if test ! -n "$LIBNL_LIBS_STATIC"; then
    LIBNL_LIBS_STATIC=`$PKG_CONFIG --libs --static "libnl-tiny" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --libs --static "libnl-tiny" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --libs --static "libnl-tiny" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --libs --static \"libnl-tiny\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
        
				pkg_config_found_libnl=yes
				V_INCLS="$V_INCLS $LIBNL_CFLAGS"
				ADDITIONAL_LIBS="$LIBNL_LIBS $ADDITIONAL_LIBS"
				ADDITIONAL_LIBS_STATIC="$LIBNL_LIBS_STATIC $ADDITIONAL_LIBS_STATIC"
				REQUIRES_PRIVATE="libnl-tiny $REQUIRES_PRIVATE"
				
printf "%s\n" "@%:@define HAVE_LIBNL 1" >>confdefs.h

			    
    else
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: not found" >&5
printf "%s\n" "not found" >&6; }
        :
    fi
else
    # No pkg-config, so obviously not found with pkg-config.
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: pkg-config not found" >&5
printf "%s\n" "pkg-config not found" >&6; }
    :
fi

		fi

		if test x$pkg_config_found_libnl != xyes; then
			#
			# OK, either we don't have pkg-config or there
			# wasn't a .pc file for it; Check for it directly.
			#
			case "$with_libnl" in

			yes|if_available)
				incdir=-I/usr/include/libnl3
				libnldir=
				;;

			*)
				if test -d $withval; then
					libnldir=-L${withval}/lib
					incdir=-I${withval}/include
				fi
				;;
			esac

			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for nl_socket_alloc in -lnl-3" >&5
printf %s "checking for nl_socket_alloc in -lnl-3... " >&6; }
if test ${ac_cv_lib_nl_3_nl_socket_alloc+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lnl-3 ${incdir} ${libnldir} -lnl-genl-3 -lnl-3  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char nl_socket_alloc ();
int
main (void)
{
return nl_socket_alloc ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_nl_3_nl_socket_alloc=yes
else $as_nop
  ac_cv_lib_nl_3_nl_socket_alloc=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_nl_3_nl_socket_alloc" >&5
printf "%s\n" "$ac_cv_lib_nl_3_nl_socket_alloc" >&6; }
if test "x$ac_cv_lib_nl_3_nl_socket_alloc" = xyes
then :
  
				#
				# Yes, we have libnl 3.x.
				#
				ADDITIONAL_LIBS="${libnldir} -lnl-genl-3 -lnl-3 $ADDITIONAL_LIBS"
				ADDITIONAL_LIBS_STATIC="${libnldir} -lnl-genl-3 -lnl-3 $ADDITIONAL_LIBS_STATIC"
				LIBS_PRIVATE="${libnldir} -lnl-genl-3 -lnl-3 $LIBS_PRIVATE"
				
printf "%s\n" "@%:@define HAVE_LIBNL 1" >>confdefs.h

				V_INCLS="$V_INCLS ${incdir}"
			
else $as_nop
  
				#
				# No, we don't have libnl at all.
				# Fail if the user explicitly requested
				# it.
				#
				if test x$with_libnl = xyes ; then
					as_fn_error $? "libnl support requested but libnl not found" "$LINENO" 5
				fi
			
fi

		fi
	fi

	#
	# Check to see if the tpacket_auxdata struct has a tp_vlan_tci member.
	#
	# NOTE: any failure means we conclude that it doesn't have that
	# member, so if we don't have tpacket_auxdata, we conclude it
	# doesn't have that member (which is OK, as either we won't be
	# using code that would use that member, or we wouldn't compile
	# in any case).
	ac_fn_c_check_member "$LINENO" "struct tpacket_auxdata" "tp_vlan_tci" "ac_cv_member_struct_tpacket_auxdata_tp_vlan_tci" "
		#include <sys/types.h>
		#include <linux/if_packet.h>
	    
"
if test "x$ac_cv_member_struct_tpacket_auxdata_tp_vlan_tci" = xyes
then :
  
printf "%s\n" "@%:@define HAVE_STRUCT_TPACKET_AUXDATA_TP_VLAN_TCI 1" >>confdefs.h


fi


	# This check is for TESTrun purposes, not for the C code.
	ac_fn_check_decl "$LINENO" "SKF_AD_VLAN_TAG_PRESENT" "ac_cv_have_decl_SKF_AD_VLAN_TAG_PRESENT" "#include <linux/filter.h>
" "$ac_c_undeclared_builtin_options" "CFLAGS"
if test "x$ac_cv_have_decl_SKF_AD_VLAN_TAG_PRESENT" = xyes
then :
  ac_have_decl=1
else $as_nop
  ac_have_decl=0
fi
printf "%s\n" "@%:@define HAVE_DECL_SKF_AD_VLAN_TAG_PRESENT $ac_have_decl" >>confdefs.h

	;;

bpf)
	#
	# Capture module
	#
	PLATFORM_C_SRC="pcap-bpf.c"

	#
	# Check whether we have the *BSD-style ioctls.
	#
	ac_fn_c_check_header_compile "$LINENO" "net/if_media.h" "ac_cv_header_net_if_media_h" "$ac_includes_default"
if test "x$ac_cv_header_net_if_media_h" = xyes
then :
  printf "%s\n" "@%:@define HAVE_NET_IF_MEDIA_H 1" >>confdefs.h

fi


	#
	# Check whether we have struct BPF_TIMEVAL.
	#
	ac_fn_c_check_type "$LINENO" "struct BPF_TIMEVAL" "ac_cv_type_struct_BPF_TIMEVAL" "
		#include <sys/types.h>
		#include <sys/ioctl.h>
		#ifdef HAVE_SYS_IOCCOM_H
		#include <sys/ioccom.h>
		#endif
		#include <net/bpf.h>
	    
"
if test "x$ac_cv_type_struct_BPF_TIMEVAL" = xyes
then :
  
printf "%s\n" "@%:@define HAVE_STRUCT_BPF_TIMEVAL 1" >>confdefs.h


fi


	if expr "$host_os" : solaris >/dev/null; then
		#
		# Check whether there's a net/ipnet.h header and,
		# if so, whether it defines IPNET_ANY_LINK - if so,
		# we assume we have the "any" device (that's a
		# Solaris header, and later versions of Solaris
		# have an "any" device).
		#
		# Attempting to include it at compile time could
		# be a pain, as it's a kernel header.
		#
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the Solaris \"any\" device is supported" >&5
printf %s "checking whether the Solaris \"any\" device is supported... " >&6; }
		if test -e /usr/include/inet/ipnet.h &&
		    grep -q IPNET_ANY_LINK /usr/include/inet/ipnet.h; then
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			
printf "%s\n" "@%:@define HAVE_SOLARIS_ANY_DEVICE 1" >>confdefs.h

		else
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		fi
	fi
	;;

hurd)
	PLATFORM_C_SRC="pcap-hurd.c"
	LIBS="$LIBS -lrt"
	;;

dag)
	#
	# --with-pcap=dag is the only way to get here, and it means
	# "DAG support but nothing else"
	#
	V_DEFS="$V_DEFS -DDAG_ONLY"
	PLATFORM_C_SRC="pcap-dag.c"
	xxx_only=yes
	;;

dpdk)
	#
	# --with-pcap=dpdk is the only way to get here, and it means
	# "DPDK support but nothing else"
	#
	V_DEFS="$V_DEFS -DDPDK_ONLY"
	PLATFORM_C_SRC="pcap-dpdk.c"
	xxx_only=yes
	;;

snf)
	#
	# --with-pcap=snf is the only way to get here, and it means
	# "SNF support but nothing else"
	#
	V_DEFS="$V_DEFS -DSNF_ONLY"
	PLATFORM_C_SRC="pcap-snf.c"
	xxx_only=yes
	;;

null)
	#
	# Capture module
	#
	PLATFORM_C_SRC="pcap-null.c"
	;;

*)
	as_fn_error $? "$V_PCAP is not a valid pcap type" "$LINENO" 5
	;;
esac

if test "$V_PCAP" != null
then
	ac_fn_c_check_func "$LINENO" "getifaddrs" "ac_cv_func_getifaddrs"
if test "x$ac_cv_func_getifaddrs" = xyes
then :
  
		#
		# We have "getifaddrs()"; make sure we have <ifaddrs.h>
		# as well, just in case some platform is really weird.
		#
		ac_fn_c_check_header_compile "$LINENO" "ifaddrs.h" "ac_cv_header_ifaddrs_h" "$ac_includes_default"
if test "x$ac_cv_header_ifaddrs_h" = xyes
then :
  
		    #
		    # We have the header, so we use "getifaddrs()" to
		    # get the list of interfaces.
		    #
		    PLATFORM_C_SRC="$PLATFORM_C_SRC fad-getad.c"
		
else $as_nop
  
		    #
		    # We don't have the header - give up.
		    # XXX - we could also fall back on some other
		    # mechanism, but, for now, this'll catch this
		    # problem so that we can at least try to figure
		    # out something to do on systems with "getifaddrs()"
		    # but without "ifaddrs.h", if there is something
		    # we can do on those systems.
		    #
		    as_fn_error $? "Your system has getifaddrs() but doesn't have a usable <ifaddrs.h>." "$LINENO" 5
		
fi

	
else $as_nop
  
		#
		# Well, we don't have "getifaddrs()", at least not with the
		# libraries with which we've decided we need to link
		# libpcap with, so we have to use some other mechanism.
		#
		# Note that this may happen on Solaris, which has
		# getifaddrs(), but in -lsocket, not in -lxnet, so we
		# won't find it if we link with -lxnet, which we want
		# to do for other reasons.
		#
		# For now, we use either the SIOCGIFCONF ioctl or the
		# SIOCGLIFCONF ioctl, preferring the latter if we have
		# it; the latter is a Solarisism that first appeared
		# in Solaris 8.  (Solaris's getifaddrs() appears to
		# be built atop SIOCGLIFCONF; using it directly
		# avoids a not-all-that-useful middleman.)
		#
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether we have SIOCGLIFCONF" >&5
printf %s "checking whether we have SIOCGLIFCONF... " >&6; }
		if test ${ac_cv_lbl_have_siocglifconf+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <sys/param.h>
			#include <sys/file.h>
			#include <sys/ioctl.h>
			#include <sys/socket.h>
			#include <sys/sockio.h>
int
main (void)
{
ioctl(0, SIOCGLIFCONF, (char *)0);
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_lbl_have_siocglifconf=yes
else $as_nop
  ac_cv_lbl_have_siocglifconf=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi

		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lbl_have_siocglifconf" >&5
printf "%s\n" "$ac_cv_lbl_have_siocglifconf" >&6; }
		if test $ac_cv_lbl_have_siocglifconf = yes ; then
			PLATFORM_C_SRC="$PLATFORM_C_SRC fad-glifc.c"
		else
			PLATFORM_C_SRC="$PLATFORM_C_SRC fad-gifc.c"
		fi
	
fi

fi

case "$host_os" in
linux*)
	ac_fn_c_check_header_compile "$LINENO" "linux/net_tstamp.h" "ac_cv_header_linux_net_tstamp_h" "$ac_includes_default"
if test "x$ac_cv_header_linux_net_tstamp_h" = xyes
then :
  printf "%s\n" "@%:@define HAVE_LINUX_NET_TSTAMP_H 1" >>confdefs.h

fi

	;;
*)
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: no hardware timestamp support implemented for $host_os" >&5
printf "%s\n" "$as_me: no hardware timestamp support implemented for $host_os" >&6;}
	;;
esac

#
# Check for socklen_t.
#
ac_fn_c_check_type "$LINENO" "socklen_t" "ac_cv_type_socklen_t" "
	#include <sys/types.h>
	#include <sys/socket.h>
    
"
if test "x$ac_cv_type_socklen_t" = xyes
then :
  
printf "%s\n" "@%:@define HAVE_SOCKLEN_T 1" >>confdefs.h


fi


# Check for Endace DAG card support.

@%:@ Check whether --with-dag was given.
if test ${with_dag+y}
then :
  withval=$with_dag; 
	if test "$withval" = no
	then
		# User doesn't want DAG support.
		want_dag=no
	elif test "$withval" = yes
	then
		# User wants DAG support but hasn't specified a directory.
		want_dag=yes
	else
		# User wants DAG support and has specified a directory, so use the provided value.
		want_dag=yes
		dag_root=$withval
	fi

else $as_nop
  
	if test "$V_PCAP" = dag; then
		# User requested DAG-only libpcap, so we'd better have
		# the DAG API.
		want_dag=yes
	elif test "$xxx_only" = yes; then
		# User requested something-else-only pcap, so they don't
		# want DAG support.
		want_dag=no
	else
		#
		# Use DAG API if present, otherwise don't
		#
		want_dag=ifpresent
	fi

fi



@%:@ Check whether --with-dag-includes was given.
if test ${with_dag_includes+y}
then :
  withval=$with_dag_includes; 
	# User wants DAG support and has specified a header directory, so use the provided value.
	want_dag=yes
	dag_include_dir=$withval

fi



@%:@ Check whether --with-dag-libraries was given.
if test ${with_dag_libraries+y}
then :
  withval=$with_dag_libraries; 
	# User wants DAG support and has specified a library directory, so use the provided value.
	want_dag=yes
	dag_lib_dir=$withval

fi


if ! expr "$host_os" : linux >/dev/null; then
	case "$want_dag" in
	ifpresent)
		# Replace one default value with another silently and move on.
		want_dag=no
		;;
	yes)
		# Fail hard: this is a user request and it cannot be done.
		as_fn_error $? "cannot enable DAG support (not Linux)" "$LINENO" 5
		;;
	esac
fi

if test "$want_dag" != no; then

	# If necessary, set default paths for DAG API headers and libraries.
	if test -z "$dag_root"; then
		dag_root=/usr
	fi

	if test -z "$dag_include_dir"; then
		dag_include_dir="$dag_root/include"
	fi

	if test -z "$dag_lib_dir"; then
		dag_lib_dir="$dag_root/lib"
		#
		# Handle multiarch systems.
		#
		if test -d "$dag_lib_dir/$host"
		then
			dag_lib_dir="$dag_lib_dir/$host"
		fi
	fi

	
	save_CFLAGS="$CFLAGS"
	save_LIBS="$LIBS"
	save_LDFLAGS="$LDFLAGS"

	CFLAGS="$CFLAGS -I$dag_include_dir"
	ac_fn_c_check_header_compile "$LINENO" "dagapi.h" "ac_cv_header_dagapi_h" "$ac_includes_default"
if test "x$ac_cv_header_dagapi_h" = xyes
then :
  printf "%s\n" "@%:@define HAVE_DAGAPI_H 1" >>confdefs.h

fi

	
	CFLAGS="$save_CFLAGS"
	LIBS="$save_LIBS"
	LDFLAGS="$save_LDFLAGS"


	if test "$ac_cv_header_dagapi_h" = yes; then

		V_INCLS="$V_INCLS -I$dag_include_dir"

		if test $V_PCAP != dag ; then
			 MODULE_C_SRC="$MODULE_C_SRC pcap-dag.c"
		fi

		# Check for various DAG API functions.
		# Don't need to save and restore LIBS to prevent -ldag being
		# included if there's a found-action (arg 3).
		
	save_CFLAGS="$CFLAGS"
	save_LIBS="$LIBS"
	save_LDFLAGS="$LDFLAGS"

		LDFLAGS="-L$dag_lib_dir"
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for dag_attach_stream64 in -ldag" >&5
printf %s "checking for dag_attach_stream64 in -ldag... " >&6; }
if test ${ac_cv_lib_dag_dag_attach_stream64+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-ldag  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char dag_attach_stream64 ();
int
main (void)
{
return dag_attach_stream64 ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_dag_dag_attach_stream64=yes
else $as_nop
  ac_cv_lib_dag_dag_attach_stream64=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_dag_dag_attach_stream64" >&5
printf "%s\n" "$ac_cv_lib_dag_dag_attach_stream64" >&6; }
if test "x$ac_cv_lib_dag_dag_attach_stream64" = xyes
then :
  
			#
			# We assume that if we have libdag we have
			# libdagconf, as they're installed at the
			# same time from the same package.
			#
			ADDITIONAL_LIBS="-L$dag_lib_dir $ADDITIONAL_LIBS -ldag -ldagconf"
			ADDITIONAL_LIBS_STATIC="-L$dag_lib_dir $ADDITIONAL_LIBS_STATIC -ldag -ldagconf"
			LIBS_PRIVATE="-L$dag_lib_dir $LIBS_PRIVATE -ldag -ldagconf"
		    
else $as_nop
  as_fn_error $? "DAG library lacks 64-bit streams support" "$LINENO" 5
fi

		
	CFLAGS="$save_CFLAGS"
	LIBS="$save_LIBS"
	LDFLAGS="$save_LDFLAGS"


		#
		# We assume that if we have libdag we have libdagconf,
		# as they're installed at the same time from the same
		# package.
		#
		
	save_CFLAGS="$CFLAGS"
	save_LIBS="$LIBS"
	save_LDFLAGS="$LDFLAGS"

		LIBS="$LIBS -ldag -ldagconf"
		LDFLAGS="$LDFLAGS -L$dag_lib_dir"
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for vdag_set_device_info in -lvdag" >&5
printf %s "checking for vdag_set_device_info in -lvdag... " >&6; }
if test ${ac_cv_lib_vdag_vdag_set_device_info+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lvdag  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char vdag_set_device_info ();
int
main (void)
{
return vdag_set_device_info ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_vdag_vdag_set_device_info=yes
else $as_nop
  ac_cv_lib_vdag_vdag_set_device_info=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_vdag_vdag_set_device_info" >&5
printf "%s\n" "$ac_cv_lib_vdag_vdag_set_device_info" >&6; }
if test "x$ac_cv_lib_vdag_vdag_set_device_info" = xyes
then :
  ac_dag_have_vdag="1"
else $as_nop
  ac_dag_have_vdag="0"
fi

		
	CFLAGS="$save_CFLAGS"
	LIBS="$save_LIBS"
	LDFLAGS="$save_LDFLAGS"

		if test "$ac_dag_have_vdag" = 1; then
			
printf "%s\n" "@%:@define HAVE_DAG_VDAG 1" >>confdefs.h

			if test "$ac_lbl_have_pthreads" != "found"; then
				as_fn_error $? "DAG requires pthreads, but we didn't find them" "$LINENO" 5
			fi
			ADDITIONAL_LIBS="$ADDITIONAL_LIBS $PTHREAD_LIBS"
			ADDITIONAL_LIBS_STATIC="$ADDITIONAL_LIBS_STATIC $PTHREAD_LIBS"
			LIBS_PRIVATE="$LIBS_PRIVATE $PTHREAD_LIBS"
		fi

		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: using Endace DAG API headers from $dag_include_dir" >&5
printf "%s\n" "$as_me: using Endace DAG API headers from $dag_include_dir" >&6;}
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: using Endace DAG API libraries from $dag_lib_dir" >&5
printf "%s\n" "$as_me: using Endace DAG API libraries from $dag_lib_dir" >&6;}
		
printf "%s\n" "@%:@define HAVE_DAG_API 1" >>confdefs.h


		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether to enable Endace DAG transmit support (EXPERIMENTAL)" >&5
printf %s "checking whether to enable Endace DAG transmit support (EXPERIMENTAL)... " >&6; }
		@%:@ Check whether --enable-dag-tx was given.
if test ${enable_dag_tx+y}
then :
  enableval=$enable_dag_tx; 
fi

		if test "$enable_dag_tx" = "yes"; then
			
printf "%s\n" "@%:@define ENABLE_DAG_TX 1" >>confdefs.h

		fi
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: ${enable_dag_tx-no}" >&5
printf "%s\n" "${enable_dag_tx-no}" >&6; }
	else
		if test "$V_PCAP" = dag; then
			# User requested "dag" capture type but we couldn't
			# find the DAG API support.
			as_fn_error $? "DAG support requested with --with-pcap=dag, but the DAG headers weren't found at $dag_include_dir: make sure the DAG support is installed, specify a different path or paths if necessary, or don't request DAG support" "$LINENO" 5
		fi

		if test "$want_dag" = yes; then
			# User wanted DAG support but we couldn't find it.
			as_fn_error $? "DAG support requested with --with-dag, but the DAG headers weren't found at $dag_include_dir: make sure the DAG support is installed, specify a different path or paths if necessary, or don't request DAG support" "$LINENO" 5
		fi
	fi
fi

# Check for Myricom SNF support.

@%:@ Check whether --with-snf was given.
if test ${with_snf+y}
then :
  withval=$with_snf; 
	if test "$withval" = no
	then
		# User explicitly doesn't want SNF
		want_snf=no
	elif test "$withval" = yes
	then
		# User wants SNF support but hasn't specified a directory.
		want_snf=yes
	else
		# User wants SNF support with a specified directory.
		want_snf=yes
		snf_root=$withval
	fi

else $as_nop
  
	if test "$V_PCAP" = snf; then
		# User requested Sniffer-only libpcap, so we'd better have
		# the Sniffer API.
		want_snf=yes
	elif test "$xxx_only" = yes; then
		# User requested something-else-only pcap, so they don't
		# want SNF support.
		want_snf=no
	else
		#
		# Use Sniffer API if present, otherwise don't
		#
		want_snf=ifpresent
	fi

fi



@%:@ Check whether --with-snf-includes was given.
if test ${with_snf_includes+y}
then :
  withval=$with_snf_includes; 
	# User wants SNF with specific header directory
	want_snf=yes
	snf_include_dir=$withval

fi



@%:@ Check whether --with-snf-libraries was given.
if test ${with_snf_libraries+y}
then :
  withval=$with_snf_libraries; 
	# User wants SNF with specific lib directory
	want_snf=yes
	snf_lib_dir=$withval

fi


# Same as for DAG above.
if ! expr "$host_os" : linux >/dev/null; then
	case "$want_snf" in
	ifpresent)
		want_snf=no
		;;
	yes)
		as_fn_error $? "cannot enable SNF support (not Linux)" "$LINENO" 5
		;;
	esac
fi

if test "$want_snf" != no; then
	# If necessary, set default paths for Sniffer headers and libraries.
	if test -z "$snf_root"; then
		snf_root=/opt/snf
	fi

	if test -z "$snf_include_dir"; then
		snf_include_dir="$snf_root/include"
	fi

	if test -z "$snf_lib_dir"; then
		snf_lib_dir="$snf_root/lib"
		#
		# Handle multiarch systems.
		#
		if test -d "$snf_lib_dir/$host"
		then
			snf_lib_dir="$snf_lib_dir/$host"
		fi
	fi

	
	save_CFLAGS="$CFLAGS"
	save_LIBS="$LIBS"
	save_LDFLAGS="$LDFLAGS"

	CFLAGS="$CFLAGS -I$snf_include_dir"
	# Do not define a symbol.
	ac_fn_c_check_header_compile "$LINENO" "snf.h" "ac_cv_header_snf_h" "$ac_includes_default"
if test "x$ac_cv_header_snf_h" = xyes
then :
  
fi

	
	CFLAGS="$save_CFLAGS"
	LIBS="$save_LIBS"
	LDFLAGS="$save_LDFLAGS"


	if test "$ac_cv_header_snf_h" = yes; then
		# We found a header; make sure we can link with the library
		
	save_CFLAGS="$CFLAGS"
	save_LIBS="$LIBS"
	save_LDFLAGS="$LDFLAGS"

		LDFLAGS="$LDFLAGS -L$snf_lib_dir"
		# Do not define a symbol.
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for snf_init in -lsnf" >&5
printf %s "checking for snf_init in -lsnf... " >&6; }
if test ${ac_cv_lib_snf_snf_init+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lsnf  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char snf_init ();
int
main (void)
{
return snf_init ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_snf_snf_init=yes
else $as_nop
  ac_cv_lib_snf_snf_init=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_snf_snf_init" >&5
printf "%s\n" "$ac_cv_lib_snf_snf_init" >&6; }
if test "x$ac_cv_lib_snf_snf_init" = xyes
then :
  ac_cv_lbl_snf_api="yes"
fi

		
	CFLAGS="$save_CFLAGS"
	LIBS="$save_LIBS"
	LDFLAGS="$save_LDFLAGS"

	fi

	if test "$ac_cv_lbl_snf_api" = yes; then
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: using Myricom SNF API headers from $snf_include_dir" >&5
printf "%s\n" "$as_me: using Myricom SNF API headers from $snf_include_dir" >&6;}
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: using Myricom SNF API libraries from $snf_lib_dir" >&5
printf "%s\n" "$as_me: using Myricom SNF API libraries from $snf_lib_dir" >&6;}

		V_INCLS="$V_INCLS -I$snf_include_dir"
		ADDITIONAL_LIBS="$ADDITIONAL_LIBS -L$snf_lib_dir -lsnf"
		ADDITIONAL_LIBS_STATIC="$ADDITIONAL_LIBS_STATIC -L$snf_lib_dir -lsnf"
		LIBS_PRIVATE="$LIBS_PRIVATE -L$snf_lib_dir -lsnf"

		if test "$V_PCAP" != snf ; then
			MODULE_C_SRC="$MODULE_C_SRC pcap-snf.c"
		fi

		
printf "%s\n" "@%:@define HAVE_SNF_API 1" >>confdefs.h

	else
		if test "$V_PCAP" = snf; then
			# User requested "snf" capture type but
			# we couldn't find the Sniffer API support.
			as_fn_error $? "Myricom Sniffer support requested with --with-pcap=snf, but the Sniffer headers weren't found at $snf_include_dir: make sure the Sniffer support is installed, specify a different path or paths if necessary, or don't request Sniffer support" "$LINENO" 5
		fi

		if test "$want_snf" = yes; then
			as_fn_error $? "Myricom Sniffer support requested with --with-snf, but the Sniffer headers weren't found at $snf_include_dir: make sure the Sniffer support is installed, specify a different path or paths if necessary, or don't request Sniffer support" "$LINENO" 5
		fi
	fi
fi

{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether to enable remote packet capture" >&5
printf %s "checking whether to enable remote packet capture... " >&6; }
@%:@ Check whether --enable-remote was given.
if test ${enable_remote+y}
then :
  enableval=$enable_remote; 
else $as_nop
  enableval=no
fi

case "$enableval" in
yes)	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: Remote packet capture may expose libpcap-based applications" >&5
printf "%s\n" "$as_me: WARNING: Remote packet capture may expose libpcap-based applications" >&2;}
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: to attacks by malicious remote capture servers!" >&5
printf "%s\n" "$as_me: WARNING: to attacks by malicious remote capture servers!" >&2;}
	#
	# rpcapd requires pthreads on UN*X.
	#
	if test "$ac_lbl_have_pthreads" != "found"; then
		as_fn_error $? "rpcapd requires pthreads, but we didn't find them" "$LINENO" 5
	fi
	#
	# It also requires crypt().
	# Do we have it in the system libraries?
	#
	ac_fn_c_check_func "$LINENO" "crypt" "ac_cv_func_crypt"
if test "x$ac_cv_func_crypt" = xyes
then :
  
else $as_nop
  
		#
		# No.  Do we have it in -lcrypt?
		#
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for crypt in -lcrypt" >&5
printf %s "checking for crypt in -lcrypt... " >&6; }
if test ${ac_cv_lib_crypt_crypt+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lcrypt  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char crypt ();
int
main (void)
{
return crypt ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_crypt_crypt=yes
else $as_nop
  ac_cv_lib_crypt_crypt=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_crypt_crypt" >&5
printf "%s\n" "$ac_cv_lib_crypt_crypt" >&6; }
if test "x$ac_cv_lib_crypt_crypt" = xyes
then :
  
			#
			# Yes; add -lcrypt to the libraries for rpcapd.
			#
			RPCAPD_LIBS="$RPCAPD_LIBS -lcrypt"
		    
else $as_nop
  
			as_fn_error $? "rpcapd requires crypt(), but we didn't find it" "$LINENO" 5
		    
fi

	    
fi


	#
	# OK, we have crypt().  Do we have getspnam()?
	#
	ac_fn_c_check_func "$LINENO" "getspnam" "ac_cv_func_getspnam"
if test "x$ac_cv_func_getspnam" = xyes
then :
  printf "%s\n" "@%:@define HAVE_GETSPNAM 1" >>confdefs.h

fi


	#
	# Thanks, IBM, for not providing vsyslog() in AIX!
	#
	ac_fn_c_check_func "$LINENO" "vsyslog" "ac_cv_func_vsyslog"
if test "x$ac_cv_func_vsyslog" = xyes
then :
  printf "%s\n" "@%:@define HAVE_VSYSLOG 1" >>confdefs.h

fi


	#
	# Check for various members of struct msghdr.
	#
	ac_fn_c_check_member "$LINENO" "struct msghdr" "msg_control" "ac_cv_member_struct_msghdr_msg_control" "
		#include \"ftmacros.h\"
		#include <sys/socket.h>
	    
"
if test "x$ac_cv_member_struct_msghdr_msg_control" = xyes
then :
  
printf "%s\n" "@%:@define HAVE_STRUCT_MSGHDR_MSG_CONTROL 1" >>confdefs.h


fi

	ac_fn_c_check_member "$LINENO" "struct msghdr" "msg_flags" "ac_cv_member_struct_msghdr_msg_flags" "
		#include \"ftmacros.h\"
		#include <sys/socket.h>
	    
"
if test "x$ac_cv_member_struct_msghdr_msg_flags" = xyes
then :
  
printf "%s\n" "@%:@define HAVE_STRUCT_MSGHDR_MSG_FLAGS 1" >>confdefs.h


fi


	#
	# Optionally, we may want to support SSL.
	# Check for OpenSSL/libressl.
	#
	# First, try looking for it with pkg-config, if we have it.
	#
	# Homebrew's pkg-config does not, by default, look for
	# pkg-config files for packages it has installed.
	# Furthermore, at least for OpenSSL, they appear to be
	# dumped in package-specific directories whose paths are
	# not only package-specific but package-version-specific.
	#
	# So the only way to find openssl is to get the value of
	# PKG_CONFIG_PATH from "brew --env openssl" and add that
	# to PKG_CONFIG_PATH.  (No, we can't just assume it's under
	# /usr/local; Homebrew have conveniently chosen to put it
	# under /opt/homebrew on ARM.)
	#
	# That's the nice thing about Homebrew - it makes things easier!
	# Thanks!
	#
	save_PKG_CONFIG_PATH="$PKG_CONFIG_PATH"
	if test -n "$BREW"; then
		openssl_pkgconfig_dir=`$BREW --env --plain openssl | sed -n 's/PKG_CONFIG_PATH: //p'`
		PKG_CONFIG_PATH="$openssl_pkgconfig_dir:$PKG_CONFIG_PATH"
	fi
	
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for openssl with pkg-config" >&5
printf %s "checking for openssl with pkg-config... " >&6; }
if test -n "$PKG_CONFIG"; then
            
    if { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$PKG_CONFIG --exists --print-errors \"openssl\""; } >&5
  ($PKG_CONFIG --exists --print-errors "openssl") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }; then
	#
	# The package was found, so try to get its C flags and
	# libraries.
	#
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: found" >&5
printf "%s\n" "found" >&6; }
	if test ! -n "$OPENSSL_CFLAGS"; then
    OPENSSL_CFLAGS=`$PKG_CONFIG --cflags "openssl" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --cflags "openssl" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --cflags "openssl" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --cflags \"openssl\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
	if test ! -n "$OPENSSL_LIBS"; then
    OPENSSL_LIBS=`$PKG_CONFIG --libs "openssl" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --libs "openssl" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --libs "openssl" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --libs \"openssl\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
	if test ! -n "$OPENSSL_LIBS_STATIC"; then
    OPENSSL_LIBS_STATIC=`$PKG_CONFIG --libs --static "openssl" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --libs --static "openssl" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --libs --static "openssl" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --libs --static \"openssl\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
        
		#
		# We found OpenSSL/libressl.
		#
		HAVE_OPENSSL=yes
		REQUIRES_PRIVATE="$REQUIRES_PRIVATE openssl"
	    
    else
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: not found" >&5
printf "%s\n" "not found" >&6; }
        :
    fi
else
    # No pkg-config, so obviously not found with pkg-config.
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: pkg-config not found" >&5
printf "%s\n" "pkg-config not found" >&6; }
    :
fi

	PKG_CONFIG_PATH="$save_PKG_CONFIG_PATH"

	#
	# If it wasn't found, and we have Homebrew installed, see
	# if it's in Homebrew.
	#
	if test "x$HAVE_OPENSSL" != "xyes" -a -n "$BREW"; then
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for openssl in Homebrew" >&5
printf %s "checking for openssl in Homebrew... " >&6; }
		#
		# The brew man page lies when it speaks of
		# $BREW --prefix --installed <formula>
		# outputting nothing.  In Homebrew 3.3.16,
		# it produces output regardless of whether
		# the formula is installed or not, so we
		# send the standard output and error to
		# the bit bucket.
		#
		if $BREW --prefix --installed openssl >/dev/null 2>&1; then
			#
			# Yes.  Get the include directory and library
			# directory.  (No, we can't just assume it's
			# under /usr/local; Homebrew have conveniently
			# chosen to put it under /opt/homebrew on ARM.)
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			HAVE_OPENSSL=yes
			openssl_path=`$BREW --prefix openssl`
			OPENSSL_CFLAGS="-I$openssl_path/include"
			OPENSSL_LIBS="-L$openssl_path/lib -lssl -lcrypto"
			OPENSSL_LIBS_STATIC="-L$openssl_path/lib -lssl -lcrypto"
			OPENSSL_LIBS_PRIVATE="-L$openssl_path/lib -lssl -lcrypto"
		else
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		fi
	fi

	#
	# If it wasn't found, and /usr/local/include and /usr/local/lib
	# exist, check if it's in /usr/local.  (We check whether they
	# exist because, if they don't exist, the compiler will warn
	# about that and then ignore the argument, so they test
	# using just the system header files and libraries.)
	#
	# We include the standard include file to 1) make sure that
	# it's installed (if it's just a shared library for the
	# benefit of existing programs, that's not useful) and 2)
	# because SSL_library_init() is a library routine in some
	# versions and a #defined wrapper around OPENSSL_init_ssl()
	# in others.
	#
	if test "x$HAVE_OPENSSL" != "xyes" -a -d "/usr/local/include" -a -d "/usr/local/lib"; then
		
	save_CFLAGS="$CFLAGS"
	save_LIBS="$LIBS"
	save_LDFLAGS="$LDFLAGS"

		CFLAGS="$CFLAGS -I/usr/local/include"
		LIBS="$LIBS -L/usr/local/lib -lssl -lcrypto"
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether we have OpenSSL/libressl in /usr/local that we can use" >&5
printf %s "checking whether we have OpenSSL/libressl in /usr/local that we can use... " >&6; }
		cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

#include <openssl/ssl.h>
		    
int
main (void)
{

SSL_library_init();
return 0;
		    
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			HAVE_OPENSSL=yes
			OPENSSL_CFLAGS="-I/usr/local/include"
			OPENSSL_LIBS="-L/usr/local/lib -lssl -lcrypto"
			OPENSSL_LIBS_STATIC="-L/usr/local/lib -lssl -lcrypto"
			OPENSSL_LIBS_PRIVATE="-L/usr/local/lib -lssl -lcrypto"
		    
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
		
	CFLAGS="$save_CFLAGS"
	LIBS="$save_LIBS"
	LDFLAGS="$save_LDFLAGS"

	fi

	#
	# If it wasn't found, check if it's a system library.
	#
	# We include the standard include file to 1) make sure that
	# it's installed (if it's just a shared library for the
	# benefit of existing programs, that's not useful) and 2)
	# because SSL_library_init() is a library routine in some
	# versions and a #defined wrapper around OPENSSL_init_ssl()
	# in others.
	#
	if test "x$HAVE_OPENSSL" != "xyes"; then
		
	save_CFLAGS="$CFLAGS"
	save_LIBS="$LIBS"
	save_LDFLAGS="$LDFLAGS"

		LIBS="$LIBS -lssl -lcrypto"
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether we have a system OpenSSL/libressl that we can use" >&5
printf %s "checking whether we have a system OpenSSL/libressl that we can use... " >&6; }
		cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

#include <openssl/ssl.h>
		    
int
main (void)
{

SSL_library_init();
return 0;
		    
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			HAVE_OPENSSL=yes
			OPENSSL_LIBS="-lssl -lcrypto"
			OPENSSL_LIBS_STATIC="-lssl -lcrypto"
			OPENSSL_LIBS_PRIVATE="-lssl -lcrypto"
		    
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
		
	CFLAGS="$save_CFLAGS"
	LIBS="$save_LIBS"
	LDFLAGS="$save_LDFLAGS"

	fi

	#
	# OK, did we find it?
	#
	if test "x$HAVE_OPENSSL" = "xyes"; then
		
printf "%s\n" "@%:@define HAVE_OPENSSL 1" >>confdefs.h

		V_INCLS="$V_INCLS $OPENSSL_CFLAGS"
		ADDITIONAL_LIBS="$ADDITIONAL_LIBS $OPENSSL_LIBS"
		ADDITIONAL_LIBS_STATIC="$ADDITIONAL_LIBS_STATIC $OPENSSL_LIBS_STATIC"
		LIBS_PRIVATE="$LIBS_PRIVATE $OPENSSL_LIBS_PRIVATE"
		REQUIRES_PRIVATE="$REQUIRES_PRIVATE $OPENSSL_REQUIRES_PRIVATE"
		REMOTE_C_SRC="$REMOTE_C_SRC sslutils.c"
	else
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: OpenSSL not found" >&5
printf "%s\n" "$as_me: OpenSSL not found" >&6;}
	fi

	
printf "%s\n" "@%:@define ENABLE_REMOTE 1" >>confdefs.h

	REMOTE_C_SRC="$REMOTE_C_SRC pcap-rpcap.c rpcap-protocol.c sockutils.c"
	BUILD_RPCAPD=build-rpcapd
	INSTALL_RPCAPD=install-rpcapd
	;;
*)	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
	;;
esac

{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether to build optimizer debugging code" >&5
printf %s "checking whether to build optimizer debugging code... " >&6; }
@%:@ Check whether --enable-optimizer-dbg was given.
if test ${enable_optimizer_dbg+y}
then :
  enableval=$enable_optimizer_dbg; 
fi

if test "$enable_optimizer_dbg" = "yes"; then
	
printf "%s\n" "@%:@define BDEBUG 1" >>confdefs.h

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: ${enable_optimizer_dbg-no}" >&5
printf "%s\n" "${enable_optimizer_dbg-no}" >&6; }

{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether to build parser debugging code" >&5
printf %s "checking whether to build parser debugging code... " >&6; }
@%:@ Check whether --enable-yydebug was given.
if test ${enable_yydebug+y}
then :
  enableval=$enable_yydebug; 
fi

if test "$enable_yydebug" = "yes"; then
	
printf "%s\n" "@%:@define YYDEBUG 1" >>confdefs.h

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: ${enable_yydebug-no}" >&5
printf "%s\n" "${enable_yydebug-no}" >&6; }

#
# Look for {f}lex.
#
for ac_prog in flex lex
do
  # Extract the first word of "$ac_prog", so it can be a program name with args.
set dummy $ac_prog; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_LEX+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$LEX"; then
  ac_cv_prog_LEX="$LEX" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_LEX="$ac_prog"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
LEX=$ac_cv_prog_LEX
if test -n "$LEX"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $LEX" >&5
printf "%s\n" "$LEX" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


  test -n "$LEX" && break
done
test -n "$LEX" || LEX=":"

  if test "x$LEX" != "x:"; then
    cat >conftest.l <<_ACEOF
%{
#ifdef __cplusplus
extern "C"
#endif
int yywrap(void);
%}
%%
a { ECHO; }
b { REJECT; }
c { yymore (); }
d { yyless (1); }
e { /* IRIX 6.5 flex 2.5.4 underquotes its yyless argument.  */
#ifdef __cplusplus
    yyless ((yyinput () != 0));
#else
    yyless ((input () != 0));
#endif
  }
f { unput (yytext[0]); }
. { BEGIN INITIAL; }
%%
#ifdef YYTEXT_POINTER
extern char *yytext;
#endif
int
yywrap (void)
{
  return 1;
}
int
main (void)
{
  return ! yylex ();
}
_ACEOF
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for lex output file root" >&5
printf %s "checking for lex output file root... " >&6; }
if test ${ac_cv_prog_lex_root+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  
ac_cv_prog_lex_root=unknown
{ { ac_try="$LEX conftest.l"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$LEX conftest.l") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; } &&
if test -f lex.yy.c; then
  ac_cv_prog_lex_root=lex.yy
elif test -f lexyy.c; then
  ac_cv_prog_lex_root=lexyy
fi
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_prog_lex_root" >&5
printf "%s\n" "$ac_cv_prog_lex_root" >&6; }
if test "$ac_cv_prog_lex_root" = unknown
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: cannot find output from $LEX; giving up on $LEX" >&5
printf "%s\n" "$as_me: WARNING: cannot find output from $LEX; giving up on $LEX" >&2;}
   LEX=: LEXLIB=
fi
LEX_OUTPUT_ROOT=$ac_cv_prog_lex_root

if test ${LEXLIB+y}
then :
  
else $as_nop
  
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for lex library" >&5
printf %s "checking for lex library... " >&6; }
if test ${ac_cv_lib_lex+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  
    ac_save_LIBS="$LIBS"
    ac_found=false
    for ac_cv_lib_lex in 'none needed' -lfl -ll 'not found'; do
      case $ac_cv_lib_lex in @%:@(
  'none needed') :
     ;; @%:@(
  'not found') :
    break ;; @%:@(
  *) :
    LIBS="$ac_cv_lib_lex $ac_save_LIBS" ;; @%:@(
  *) :
     ;;
esac

      cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
`cat $LEX_OUTPUT_ROOT.c`
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_found=:
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
      if $ac_found; then
        break
      fi
    done
    LIBS="$ac_save_LIBS"
  
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_lex" >&5
printf "%s\n" "$ac_cv_lib_lex" >&6; }
  if test "$ac_cv_lib_lex" = 'not found'
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: required lex library not found; giving up on $LEX" >&5
printf "%s\n" "$as_me: WARNING: required lex library not found; giving up on $LEX" >&2;}
	 LEX=: LEXLIB=
elif test "$ac_cv_lib_lex" = 'none needed'
then :
  LEXLIB=''
else $as_nop
  LEXLIB=$ac_cv_lib_lex
fi
  
fi


if test "$LEX" != :
then :
  
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether yytext is a pointer" >&5
printf %s "checking whether yytext is a pointer... " >&6; }
if test ${ac_cv_prog_lex_yytext_pointer+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  # POSIX says lex can declare yytext either as a pointer or an array; the
# default is implementation-dependent.  Figure out which it is, since
# not all implementations provide the %pointer and %array declarations.
ac_cv_prog_lex_yytext_pointer=no
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

  #define YYTEXT_POINTER 1
`cat $LEX_OUTPUT_ROOT.c`
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_prog_lex_yytext_pointer=yes
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_prog_lex_yytext_pointer" >&5
printf "%s\n" "$ac_cv_prog_lex_yytext_pointer" >&6; }
if test $ac_cv_prog_lex_yytext_pointer = yes; then
  
printf "%s\n" "@%:@define YYTEXT_POINTER 1" >>confdefs.h

fi

fi
rm -f conftest.l $LEX_OUTPUT_ROOT.c

fi
if test "$LEX" = ":"; then
	as_fn_error $? "Neither flex nor lex was found." "$LINENO" 5
fi

#
# Make sure {f}lex supports the -P, --header-file, and --nounput flags
# and supports processing our scanner.l.
#
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for capable lex" >&5
printf %s "checking for capable lex... " >&6; }
if test ${tcpdump_cv_capable_lex+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if $LEX -P pcap_ --header-file=/dev/null --nounput -t $srcdir/scanner.l > /dev/null 2>&1; then
	    tcpdump_cv_capable_lex=yes
	else
	    tcpdump_cv_capable_lex=insufficient
	fi
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $tcpdump_cv_capable_lex" >&5
printf "%s\n" "$tcpdump_cv_capable_lex" >&6; }
if test $tcpdump_cv_capable_lex = insufficient ; then
	as_fn_error $? "$LEX is insufficient to compile libpcap.
 libpcap requires Flex 2.5.31 or later, or a compatible version of lex.
 If a suitable version of Lex/Flex is available as a non-standard command
 and/or not in the PATH, you can specify it using the LEX environment
 variable. That said, on some systems the error can mean that Flex/Lex is
 actually acceptable, but m4 is not. Likewise, if a suitable version of
 m4 (such as GNU M4) is available but has not been detected, you can
 specify it using the M4 environment variable." "$LINENO" 5
fi

#
# Look for yacc/bison/byacc.
# If it's Bison, we do not want -y, as 1) we will be using -o to cause
# the output for XXX.y to be written to XXX.c and 2) we don't want
# it to issue warnings about stuff not supported by POSIX YACC - we
# want to use that stuff, and don't care whether plain YACC supports
# it or not, we require either Bison or Berkeley YACC.
#
BISON_BYACC=""
#
# Look for Bison.
#
for ac_prog in bison
do
  # Extract the first word of "$ac_prog", so it can be a program name with args.
set dummy $ac_prog; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_BISON_BYACC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$BISON_BYACC"; then
  ac_cv_prog_BISON_BYACC="$BISON_BYACC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_BISON_BYACC="$ac_prog"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
BISON_BYACC=$ac_cv_prog_BISON_BYACC
if test -n "$BISON_BYACC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $BISON_BYACC" >&5
printf "%s\n" "$BISON_BYACC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


  test -n "$BISON_BYACC" && break
done

if test x"$BISON_BYACC" != x; then
	#
	# We found Bison.
	#
	# Bison prior to 2.4(.1) doesn't support "%define api.pure", so use
	# "%pure-parser".
	#
	bison_major_version=`$BISON_BYACC -V | sed -n 's/.* \(@<:@1-9@:>@@<:@0-9@:>@*\)\.@<:@0-9@:>@@<:@0-9.@:>@*/\1/p'`
	bison_minor_version=`$BISON_BYACC -V | sed -n 's/.* @<:@1-9@:>@@<:@0-9@:>@*\.\(@<:@0-9@:>@+\).*/\1/p'`
	if test "$bison_major_version" -lt 2 -o \
	    \( "$bison_major_version" -eq 2 -a "$bison_major_version" -lt 4 \)
	then
		REENTRANT_PARSER="%pure-parser"
	else
		REENTRANT_PARSER="%define api.pure"
	fi
else
	#
	# We didn't find Bison; check for Berkeley YACC, under the
	# names byacc and yacc.
	#
	for ac_prog in byacc yacc
do
  # Extract the first word of "$ac_prog", so it can be a program name with args.
set dummy $ac_prog; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_BISON_BYACC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$BISON_BYACC"; then
  ac_cv_prog_BISON_BYACC="$BISON_BYACC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_BISON_BYACC="$ac_prog"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
BISON_BYACC=$ac_cv_prog_BISON_BYACC
if test -n "$BISON_BYACC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $BISON_BYACC" >&5
printf "%s\n" "$BISON_BYACC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


  test -n "$BISON_BYACC" && break
done

	if test x"$BISON_BYACC" != x; then
		#
		# Make sure this is Berkeley YACC, not AT&T YACC;
		# the latter doesn't support reentrant parsers.
		# Run it with "-V"; that succeeds and reports the
		# version number with Berkeley YACC, but will
		# (probably) fail with various vendor flavors
		# of AT&T YACC.
		#
		# Hopefully this also eliminates any versions
		# of Berkeley YACC that don't support reentrant
		# parsers, if there are any.
		#
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for capable yacc" >&5
printf %s "checking for capable yacc... " >&6; }
if test ${tcpdump_cv_capable_yacc+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if $BISON_BYACC -V >/dev/null 2>&1; then
			tcpdump_cv_capable_yacc=yes
		    else
			tcpdump_cv_capable_yacc=insufficient
		    fi
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $tcpdump_cv_capable_yacc" >&5
printf "%s\n" "$tcpdump_cv_capable_yacc" >&6; }
		if test $tcpdump_cv_capable_yacc = insufficient ; then
		    as_fn_error $? "$BISON_BYACC is insufficient to compile libpcap.
 libpcap requires Bison, a newer version of Berkeley YACC with support
 for reentrant parsers, or another YACC compatible with them." "$LINENO" 5
		fi
	else
		#
		# OK, we found neither byacc nor yacc.
		#
		as_fn_error $? "Neither bison, byacc, nor yacc was found.
 libpcap requires Bison, a newer version of Berkeley YACC with support
 for reentrant parsers, or another YACC compatible with them." "$LINENO" 5
	fi

	#
	# Berkeley YACC doesn't support "%define api.pure", so use
	# "%pure-parser".
	#
	REENTRANT_PARSER="%pure-parser"
fi



#
# Do various checks for various OSes and versions of those OSes.
#
# Assume, by default, no support for shared libraries and V7/BSD
# convention for man pages (devices in section 4, file formats in
# section 5, miscellaneous info in section 7, administrative commands
# and daemons in section 8).  Individual cases can override this.
#
DYEXT="none"
MAN_DEVICES=4
MAN_FILE_FORMATS=5
MAN_MISC_INFO=7
MAN_ADMIN_COMMANDS=8
case "$host_os" in

aix*)
	#
	# AIX makes it fun to build shared and static libraries,
	# because they're *both* ".a" archive libraries.  We
	# build the static library for the benefit of the traditional
	# scheme of building libpcap and tcpdump in subdirectories of
	# the same directory, with tcpdump statically linked with the
	# libpcap in question, but we also build a shared library as
	# "libpcap.shareda" and install *it*, rather than the static
	# library, as "libpcap.a".
	#
	DYEXT="shareda"

	case "$V_PCAP" in

	dlpi)
		#
		# If we're using DLPI, applications will need to
		# use /lib/pse.exp if present, as we use the
		# STREAMS routines.
		#
		pseexe="/lib/pse.exp"
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $pseexe" >&5
printf %s "checking for $pseexe... " >&6; }
		if test -f $pseexe ; then
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			LIBS="-I:$pseexe"
		fi
		;;

	bpf)
		#
		# If we're using BPF, we need "-lodm" and "-lcfg", as
		# we use them to load the BPF module.
		#
		LIBS="-lodm -lcfg"
		;;
	esac
	;;

darwin*)
	DYEXT="dylib"
	V_CCOPT="$V_CCOPT -fno-common"
	@%:@ Check whether --enable-universal was given.
if test ${enable_universal+y}
then :
  enableval=$enable_universal; 
fi

	if test "$enable_universal" != "no"; then
		case "$host_os" in

		darwin[0-7].*)
			#
			# Pre-Tiger.  Build only for 32-bit PowerPC; no
			# need for any special compiler or linker flags.
			#
			;;

		darwin8.[0123]|darwin8.[0123].*)
			#
			# Tiger, prior to Intel support.  Build
			# libraries and executables for 32-bit PowerPC
			# and 64-bit PowerPC, with 32-bit PowerPC first.
			# (I'm guessing that's what Apple does.)
			#
			# (The double brackets are needed because
			# autotools/m4 use brackets as a quoting
			# character; the double brackets turn into
			# single brackets in the generated configure
			# file.)
			#
			V_LIB_CCOPT_FAT="-arch ppc -arch ppc64"
			V_LIB_LDFLAGS_FAT="-arch ppc -arch ppc64"
			V_PROG_CCOPT_FAT="-arch ppc -arch ppc64"
			V_PROG_LDFLAGS_FAT="-arch ppc -arch ppc64"
			;;

		darwin8.[456]|darwin8.[456].*)
			#
			# Tiger, subsequent to Intel support but prior
			# to x86-64 support.  Build libraries and
			# executables for 32-bit PowerPC, 64-bit
			# PowerPC, and 32-bit x86, with 32-bit PowerPC
			# first.  (I'm guessing that's what Apple does.)
			#
			# (The double brackets are needed because
			# autotools/m4 use brackets as a quoting
			# character; the double brackets turn into
			# single brackets in the generated configure
			# file.)
			#
			V_LIB_CCOPT_FAT="-arch ppc -arch ppc64 -arch i386"
			V_LIB_LDFLAGS_FAT="-arch ppc -arch ppc64 -arch i386"
			V_PROG_CCOPT_FAT="-arch ppc -arch ppc64 -arch i386"
			V_PROG_LDFLAGS_FAT="-arch ppc -arch ppc64 -arch i386"
			;;

		darwin8.*)
			#
			# All other Tiger, so subsequent to x86-64
			# support.  Build libraries and executables for
			# 32-bit PowerPC, 64-bit PowerPC, 32-bit x86,
			# and x86-64, with 32-bit PowerPC first.  (I'm
			# guessing that's what Apple does.)
			#
			V_LIB_CCOPT_FAT="-arch ppc -arch ppc64 -arch i386 -arch x86_64"
			V_LIB_LDFLAGS_FAT="-arch ppc -arch ppc64 -arch i386 -arch x86_64"
			V_PROG_CCOPT_FAT="-arch ppc -arch ppc64 -arch i386 -arch x86_64"
			V_PROG_LDFLAGS_FAT="-arch ppc -arch ppc64 -arch i386 -arch x86_64"
			;;

		darwin9.*)
			#
			# Leopard.  Build libraries for 32-bit PowerPC,
			# 64-bit PowerPC, 32-bit x86, and x86-64, with
			# 32-bit PowerPC first, and build executables
			# for 32-bit x86 and 32-bit PowerPC, with 32-bit
			# x86 first.  (That's what Apple does.)
			#
			V_LIB_CCOPT_FAT="-arch ppc -arch ppc64 -arch i386 -arch x86_64"
			V_LIB_LDFLAGS_FAT="-arch ppc -arch ppc64 -arch i386 -arch x86_64"
			V_PROG_CCOPT_FAT="-arch i386 -arch ppc"
			V_PROG_LDFLAGS_FAT="-arch i386 -arch ppc"
			;;

		darwin10.*)
			#
			# Snow Leopard.  Build libraries for x86-64,
			# 32-bit x86, and 32-bit PowerPC, with x86-64
			# first, and build executables for x86-64 and
			# 32-bit x86, with x86-64 first.  (That's what
			# Apple does, even though Snow Leopard doesn't
			# run on PPC, so PPC libpcap runs under Rosetta,
			# and Rosetta doesn't support BPF ioctls, so PPC
			# programs can't do live captures.)
			#
			V_LIB_CCOPT_FAT="-arch x86_64 -arch i386 -arch ppc"
			V_LIB_LDFLAGS_FAT="-arch x86_64 -arch i386 -arch ppc"
			V_PROG_CCOPT_FAT="-arch x86_64 -arch i386"
			V_PROG_LDFLAGS_FAT="-arch x86_64 -arch i386"
			;;

		darwin1[1-8]*)
			#
			# Post-Snow Leopard, pre-Catalina.  Build
			# libraries for x86-64 and 32-bit x86, with
			# x86-64 first, and build executables only for
			# x86-64.  (That's what Apple does.)  This
			# requires no special flags for programs.
			#
			# We check whether we *can* build for i386 and,
			# if not, suggest that the user install the
			# /usr/include headers if they want to build
			# fat.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether building for 32-bit x86 is supported" >&5
printf %s "checking whether building for 32-bit x86 is supported... " >&6; }
			
	save_CFLAGS="$CFLAGS"
	save_LIBS="$LIBS"
	save_LDFLAGS="$LDFLAGS"

			CFLAGS="$CFLAGS -arch i386"
			cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{
return 0;
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  
				{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
				V_LIB_CCOPT_FAT="-arch x86_64"
				V_LIB_LDFLAGS_FAT="-arch x86_64"

				#
				# OpenSSL installation on macOS seems
				# to install only the libs for 64-bit
				# x86 - at least that's what Brew does:
				# only configure 32-bit builds if we
				# don't have OpenSSL.
				#
				if test "$HAVE_OPENSSL" != yes; then
					V_LIB_CCOPT_FAT="$V_LIB_CCOPT_FAT -arch i386"
					V_LIB_LDFLAGS_FAT="$V_LIB_LDFLAGS_FAT -arch i386"
				fi
			    
else $as_nop
  
				{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
				V_LIB_CCOPT_FAT="-arch x86_64"
				V_LIB_LDFLAGS_FAT="-arch x86_64"
				case "$host_os" in

				darwin18.*)
					#
					# Mojave; you need to install the
					# /usr/include headers to get
					# 32-bit x86 builds to work.
					#
					{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: Compiling for 32-bit x86 gives an error; try installing the command-line tools and, after that, installing the /usr/include headers from the /Library/Developer/CommandLineTools/Packages/macOS_SDK_headers_for_macOS_10.14.pkg package" >&5
printf "%s\n" "$as_me: WARNING: Compiling for 32-bit x86 gives an error; try installing the command-line tools and, after that, installing the /usr/include headers from the /Library/Developer/CommandLineTools/Packages/macOS_SDK_headers_for_macOS_10.14.pkg package" >&2;}
					;;

				*)
					#
					# Pre-Mojave; the command-line
					# tools should be sufficient to
					# enable 32-bit x86 builds.
					#
					{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: Compiling for 32-bit x86 gives an error; try installing the command-line tools" >&5
printf "%s\n" "$as_me: WARNING: Compiling for 32-bit x86 gives an error; try installing the command-line tools" >&2;}
					;;
				esac
			    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
			
	CFLAGS="$save_CFLAGS"
	LIBS="$save_LIBS"
	LDFLAGS="$save_LDFLAGS"

			;;

		darwin19*)
			#
			# Catalina.  Build libraries and executables
			# only for x86-64.  (That's what Apple does;
			# 32-bit x86 binaries are not supported on
			# Catalina.)
			#
			V_LIB_CCOPT_FAT="-arch x86_64"
			V_LIB_LDFLAGS_FAT="-arch x86_64"
			V_PROG_CCOPT_FAT="-arch x86_64"
			V_PROG_LDFLAGS_FAT="-arch x86_64"
			;;

		darwin*)
			#
			# Post-Catalina.  Build libraries and
			# executables for x86-64 and ARM64.
			# (That's what Apple does, except they
			# build for arm64e, which may include
			# some of the pointer-checking extensions.)
			#
			# If we're building with libssl, make sure
			# we can build fat with it (i.e., that it
			# was built fat); if we can't, don't set
			# the target architectures, and just
			# build for the host we're on.
			#
			# Otherwise, just add both of them.
			#
			if test "$HAVE_OPENSSL" = yes; then
				{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether building fat with libssl is supported" >&5
printf %s "checking whether building fat with libssl is supported... " >&6; }
				
	save_CFLAGS="$CFLAGS"
	save_LIBS="$LIBS"
	save_LDFLAGS="$LDFLAGS"

				CFLAGS="$CFLAGS -arch x86_64 -arch arm64"
				LDFLAGS="$LDFLAGS $OPENSSL_LIBS"
				cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

					#include <openssl/ssl.h>
				    
int
main (void)
{

					SSL_library_init();
					return 0;
				    
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  
					{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
					V_LIB_CCOPT_FAT="-arch x86_64 -arch arm64"
					V_LIB_LDFLAGS_FAT="-arch x86_64 -arch arm64"
					V_PROG_CCOPT_FAT="-arch x86_64 -arch arm64"
					V_PROG_LDFLAGS_FAT="-arch x86_64 -arch arm64"
				    
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
				
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
				
	CFLAGS="$save_CFLAGS"
	LIBS="$save_LIBS"
	LDFLAGS="$save_LDFLAGS"

			else
				V_LIB_CCOPT_FAT="-arch x86_64 -arch arm64"
				V_LIB_LDFLAGS_FAT="-arch x86_64 -arch arm64"
				V_PROG_CCOPT_FAT="-arch x86_64 -arch arm64"
				V_PROG_LDFLAGS_FAT="-arch x86_64 -arch arm64"
			fi
			;;
		esac
	fi
	;;

hpux9*|hpux10.01*)
	as_fn_error $? "HP-UX version must be 10.20 or later, not $host_os" "$LINENO" 5
	;;

hpux*)
							
printf "%s\n" "@%:@define HAVE_HPUX10_20_OR_LATER 1" >>confdefs.h

	if test "`uname -m`" = "ia64"; then
		DYEXT="so"
	else
		DYEXT="sl"
	fi

	#
	# "-b" builds a shared library; "+h" sets the soname.
	#
	SHLIB_OPT="-b"
	SONAME_OPT="+h"

	#
	# Use System V conventions for man pages.
	#
	MAN_FILE_FORMATS=4
	MAN_MISC_INFO=5
	;;

linux*|freebsd*|netbsd*|openbsd*|dragonfly*|kfreebsd*|gnu*|haiku*|midipix*)
	DYEXT="so"
	;;

solaris*)
	
printf "%s\n" "@%:@define HAVE_SOLARIS 1" >>confdefs.h


	DYEXT="so"

	#
	# Make sure errno is thread-safe, in case we're called in
	# a multithreaded program.  We don't guarantee that two
	# threads can use the *same* pcap_t safely, but the
	# current version does guarantee that you can use different
	# pcap_t's in different threads, and even that pcap_compile()
	# is thread-safe (it wasn't thread-safe in some older versions).
	#
	V_CCOPT="$V_CCOPT -D_TS_ERRNO"

	case "`uname -r`" in

	5.12)
		;;

	*)
		#
		# Use System V conventions for man pages.
		#
		MAN_ADMIN_COMMANDS=1m
		MAN_FILE_FORMATS=4
		MAN_MISC_INFO=5
		MAN_DEVICES=7D
	esac
	;;
esac










@%:@ Check whether --enable-shared was given.
if test ${enable_shared+y}
then :
  enableval=$enable_shared; 
fi

test "x$enable_shared" = "xno" && DYEXT="none"

if test -n "$ac_tool_prefix"; then
  # Extract the first word of "${ac_tool_prefix}ranlib", so it can be a program name with args.
set dummy ${ac_tool_prefix}ranlib; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_RANLIB+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$RANLIB"; then
  ac_cv_prog_RANLIB="$RANLIB" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_RANLIB="${ac_tool_prefix}ranlib"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
RANLIB=$ac_cv_prog_RANLIB
if test -n "$RANLIB"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $RANLIB" >&5
printf "%s\n" "$RANLIB" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$ac_cv_prog_RANLIB"; then
  ac_ct_RANLIB=$RANLIB
  # Extract the first word of "ranlib", so it can be a program name with args.
set dummy ranlib; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_ac_ct_RANLIB+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$ac_ct_RANLIB"; then
  ac_cv_prog_ac_ct_RANLIB="$ac_ct_RANLIB" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_ac_ct_RANLIB="ranlib"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
ac_ct_RANLIB=$ac_cv_prog_ac_ct_RANLIB
if test -n "$ac_ct_RANLIB"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_ct_RANLIB" >&5
printf "%s\n" "$ac_ct_RANLIB" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

  if test "x$ac_ct_RANLIB" = x; then
    RANLIB=":"
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    RANLIB=$ac_ct_RANLIB
  fi
else
  RANLIB="$ac_cv_prog_RANLIB"
fi

if test -n "$ac_tool_prefix"; then
  # Extract the first word of "${ac_tool_prefix}ar", so it can be a program name with args.
set dummy ${ac_tool_prefix}ar; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_AR+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$AR"; then
  ac_cv_prog_AR="$AR" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_AR="${ac_tool_prefix}ar"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
AR=$ac_cv_prog_AR
if test -n "$AR"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $AR" >&5
printf "%s\n" "$AR" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$ac_cv_prog_AR"; then
  ac_ct_AR=$AR
  # Extract the first word of "ar", so it can be a program name with args.
set dummy ar; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_ac_ct_AR+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$ac_ct_AR"; then
  ac_cv_prog_ac_ct_AR="$ac_ct_AR" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_ac_ct_AR="ar"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
ac_ct_AR=$ac_cv_prog_ac_ct_AR
if test -n "$ac_ct_AR"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_ct_AR" >&5
printf "%s\n" "$ac_ct_AR" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

  if test "x$ac_ct_AR" = x; then
    AR=""
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    AR=$ac_ct_AR
  fi
else
  AR="$ac_cv_prog_AR"
fi


{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether ln -s works" >&5
printf %s "checking whether ln -s works... " >&6; }
LN_S=$as_ln_s
if test "$LN_S" = "ln -s"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no, using $LN_S" >&5
printf "%s\n" "no, using $LN_S" >&6; }
fi



rm -f os-proto.h
    if test "${LBL_CFLAGS+set}" = set; then
	    V_CCOPT="$V_CCOPT ${LBL_CFLAGS}"
    fi
    if test -f .devel ; then
	    #
	    # Skip all the warning option stuff on some compilers.
	    #
	    if test "$ac_lbl_cc_dont_try_gcc_dashW" != yes; then
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -W option" >&5
printf %s "checking whether the compiler supports the -W option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -W"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -W " >&5
printf %s "checking whether -W ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -W"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wall option" >&5
printf %s "checking whether the compiler supports the -Wall option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wall"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wall " >&5
printf %s "checking whether -Wall ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wall"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wcomma option" >&5
printf %s "checking whether the compiler supports the -Wcomma option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wcomma"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wcomma " >&5
printf %s "checking whether -Wcomma ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wcomma"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    # Warns about safeguards added in case the enums are
		    # extended
		    # AC_LBL_CHECK_COMPILER_OPT(V_CCOPT, -Wcovered-switch-default)
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wdocumentation option" >&5
printf %s "checking whether the compiler supports the -Wdocumentation option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wdocumentation"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wdocumentation " >&5
printf %s "checking whether -Wdocumentation ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wdocumentation"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wformat-nonliteral option" >&5
printf %s "checking whether the compiler supports the -Wformat-nonliteral option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wformat-nonliteral"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wformat-nonliteral " >&5
printf %s "checking whether -Wformat-nonliteral ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wformat-nonliteral"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wmissing-noreturn option" >&5
printf %s "checking whether the compiler supports the -Wmissing-noreturn option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wmissing-noreturn"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wmissing-noreturn " >&5
printf %s "checking whether -Wmissing-noreturn ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wmissing-noreturn"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wmissing-prototypes option" >&5
printf %s "checking whether the compiler supports the -Wmissing-prototypes option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wmissing-prototypes"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wmissing-prototypes " >&5
printf %s "checking whether -Wmissing-prototypes ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wmissing-prototypes"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wmissing-variable-declarations option" >&5
printf %s "checking whether the compiler supports the -Wmissing-variable-declarations option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wmissing-variable-declarations"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wmissing-variable-declarations " >&5
printf %s "checking whether -Wmissing-variable-declarations ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wmissing-variable-declarations"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wnull-pointer-subtraction option" >&5
printf %s "checking whether the compiler supports the -Wnull-pointer-subtraction option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wnull-pointer-subtraction"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wnull-pointer-subtraction " >&5
printf %s "checking whether -Wnull-pointer-subtraction ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wnull-pointer-subtraction"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wpointer-arith option" >&5
printf %s "checking whether the compiler supports the -Wpointer-arith option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wpointer-arith"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wpointer-arith " >&5
printf %s "checking whether -Wpointer-arith ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wpointer-arith"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wpointer-sign option" >&5
printf %s "checking whether the compiler supports the -Wpointer-sign option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wpointer-sign"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wpointer-sign " >&5
printf %s "checking whether -Wpointer-sign ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wpointer-sign"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wshadow option" >&5
printf %s "checking whether the compiler supports the -Wshadow option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wshadow"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wshadow " >&5
printf %s "checking whether -Wshadow ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wshadow"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wshorten-64-to-32 option" >&5
printf %s "checking whether the compiler supports the -Wshorten-64-to-32 option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wshorten-64-to-32"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wshorten-64-to-32 " >&5
printf %s "checking whether -Wshorten-64-to-32 ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wshorten-64-to-32"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wsign-compare option" >&5
printf %s "checking whether the compiler supports the -Wsign-compare option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wsign-compare"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wsign-compare " >&5
printf %s "checking whether -Wsign-compare ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wsign-compare"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wstrict-prototypes option" >&5
printf %s "checking whether the compiler supports the -Wstrict-prototypes option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wstrict-prototypes"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wstrict-prototypes " >&5
printf %s "checking whether -Wstrict-prototypes ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wstrict-prototypes"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wundef option" >&5
printf %s "checking whether the compiler supports the -Wundef option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wundef"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wundef " >&5
printf %s "checking whether -Wundef ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wundef"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    #
		    # This can cause problems with ntohs(), ntohl(),
		    # htons(), and htonl() on some platforms, such
		    # as OpenBSD 6.3 with Clang 5.0.1.  I guess the
		    # problem is that the macro that ultimately does
		    # the byte-swapping involves a conditional
		    # expression that tests whether the value being
		    # swapped is a compile-time constant or not,
		    # using __builtin_constant_p(), and, depending
		    # on whether it is, does a compile-time swap or
		    # a run-time swap; perhaps the compiler always
		    # considers one of the two results of the
		    # conditional expression is never evaluated,
		    # because the conditional check is done at
		    # compile time, and thus always says "that
		    # expression is never executed".
		    #
		    # (Perhaps there should be a way of flagging
		    # an expression that you *want* evaluated at
		    # compile time, so that the compiler 1) warns
		    # if it *can't* be evaluated at compile time
		    # and 2) *doesn't* warn that the true or false
		    # branch will never be reached.)
		    #
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wunreachable-code option" >&5
printf %s "checking whether the compiler supports the -Wunreachable-code option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wunreachable-code"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "xgenerates warnings from ntohs()" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wunreachable-code generates warnings from ntohs()" >&5
printf %s "checking whether -Wunreachable-code generates warnings from ntohs()... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <arpa/inet.h>

unsigned short
testme(unsigned short a)
{
	return ntohs(a);
}
		      
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wunreachable-code"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wunused-but-set-parameter option" >&5
printf %s "checking whether the compiler supports the -Wunused-but-set-parameter option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wunused-but-set-parameter"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wunused-but-set-parameter " >&5
printf %s "checking whether -Wunused-but-set-parameter ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wunused-but-set-parameter"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wunused-but-set-variable option" >&5
printf %s "checking whether the compiler supports the -Wunused-but-set-variable option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wunused-but-set-variable"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wunused-but-set-variable " >&5
printf %s "checking whether -Wunused-but-set-variable ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wunused-but-set-variable"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wunused-parameter option" >&5
printf %s "checking whether the compiler supports the -Wunused-parameter option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wunused-parameter"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wunused-parameter " >&5
printf %s "checking whether -Wunused-parameter ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wunused-parameter"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
		    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports the -Wused-but-marked-unused option" >&5
printf %s "checking whether the compiler supports the -Wused-but-marked-unused option... " >&6; }
	save_CFLAGS="$CFLAGS"
	CFLAGS="$CFLAGS -Wused-but-marked-unused"
	#
	# XXX - yes, this depends on the way AC_LANG_WERROR works,
	# but no mechanism is provided to turn AC_LANG_WERROR on
	# *and then turn it back off*, so that we *only* do it when
	# testing compiler options - 15 years after somebody asked
	# for it:
	#
	#     https://autoconf.gnu.narkive.com/gTAVmfKD/how-to-cancel-flags-set-by-ac-lang-werror
	#
	save_ac_c_werror_flag="$ac_c_werror_flag"
	ac_c_werror_flag=yes
	#
	# We use AC_LANG_SOURCE() so that we can control the complete
	# content of the program being compiled.  We do not, for example,
	# want the default "int main()" that AC_LANG_PROGRAM() generates,
	# as it will generate a warning with -Wold-style-definition, meaning
	# that we would treat it as not working, as the test will fail if
	# *any* error output, including a warning due to the flag we're
	# testing, is generated; see
	#
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#    https://www.postgresql.org/message-id/2192993.1591682589%40sss.pgh.pa.us
	#
	# This may, as per those two messages, be fixed in autoconf 2.70,
	# but we only require 2.69 or newer for now.
	#
	cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
		can_add_to_cflags=yes
		#
		# The compile supports this; do we have some C code for
		# which the warning should *not* appear?
		# We test the fourth argument because the third argument
		# could contain quotes, breaking the test.
		#
		if test "x" != "x"
		then
		    CFLAGS="$CFLAGS $ac_lbl_cc_force_warning_errors"
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -Wused-but-marked-unused " >&5
printf %s "checking whether -Wused-but-marked-unused ... " >&6; }
		    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
			#
			# Not a problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		      
else $as_nop
  
			#
			# A problem.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			can_add_to_cflags=no
		      
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
		fi
		CFLAGS="$save_CFLAGS"
		if test x"$can_add_to_cflags" = "xyes"
		then
		    V_CCOPT="$V_CCOPT -Wused-but-marked-unused"
		fi
	    
else $as_nop
  
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		CFLAGS="$save_CFLAGS"
	    
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
	ac_c_werror_flag="$save_ac_c_werror_flag"
    
	    fi
	    
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports generating dependencies" >&5
printf %s "checking whether the compiler supports generating dependencies... " >&6; }
	if test "$GCC" = yes ; then
		#
		# GCC, or a compiler deemed to be GCC by AC_PROG_CC (even
		# though it's not); we assume that, in this case, the flag
		# would be -M.
		#
		ac_lbl_dependency_flag="-M"
	else
		#
		# Not GCC or a compiler deemed to be GCC; what platform is
		# this?  (We're assuming that if the compiler isn't GCC
		# it's the compiler from the vendor of the OS; that won't
		# necessarily be true for x86 platforms, where it might be
		# the Intel C compiler.)
		#
		case "$host_os" in

		darwin*)
			#
			# Clang uses -M.
			#
			ac_lbl_dependency_flag="-M"
			;;

		solaris*)
			#
			# Sun C uses -xM.
			#
			ac_lbl_dependency_flag="-xM"
			;;

		hpux*)
			#
			# HP's older C compilers don't support this.
			# HP's newer C compilers support this with
			# either +M or +Make; the older compilers
			# interpret +M as something completely
			# different, so we use +Make so we don't
			# think it works with the older compilers.
			#
			ac_lbl_dependency_flag="+Make"
			;;

		*)
			#
			# Not one of the above; assume no support for
			# generating dependencies.
			#
			ac_lbl_dependency_flag=""
			;;
		esac
	fi

	#
	# Is ac_lbl_dependency_flag defined and, if so, does the compiler
	# complain about it?
	#
	# Note: clang doesn't seem to exit with an error status when handed
	# an unknown non-warning error, even if you pass it
	# -Werror=unknown-warning-option.  However, it always supports
	# -M, so the fact that this test always succeeds with clang
	# isn't an issue.
	#
	if test ! -z "$ac_lbl_dependency_flag"; then
		cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
int main(void) { return 0; }
_ACEOF
		if { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: eval \"\$CC \$ac_lbl_dependency_flag conftest.c >/dev/null 2>&1\""; } >&5
  (eval "$CC $ac_lbl_dependency_flag conftest.c >/dev/null 2>&1") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }; then
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes, with $ac_lbl_dependency_flag" >&5
printf "%s\n" "yes, with $ac_lbl_dependency_flag" >&6; }
			DEPENDENCY_CFLAG="$ac_lbl_dependency_flag"
			MKDEP='${top_srcdir}/mkdep'
		else
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
			#
			# We can't run mkdep, so have "make depend" do
			# nothing.
			#
			MKDEP='${top_srcdir}/nomkdep'
		fi
		rm -rf conftest*
	else
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
		#
		# We can't run mkdep, so have "make depend" do
		# nothing.
		#
		MKDEP='${top_srcdir}/nomkdep'
	fi
	
	
    
	    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether to use an os-proto.h header" >&5
printf %s "checking whether to use an os-proto.h header... " >&6; }
	    os=`echo $host_os | sed -e 's/\([0-9][0-9]*\)[^0-9].*$/\1/'`
	    name="lbl/os-$os.h"
	    if test -f $name ; then
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes, at \"$name\"" >&5
printf "%s\n" "yes, at \"$name\"" >&6; }
		    ln -s $name os-proto.h
		    
printf "%s\n" "@%:@define HAVE_OS_PROTO_H 1" >>confdefs.h

	    else
		    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
	    fi
    fi

#
# Check to see if the sockaddr struct has the 4.4 BSD sa_len member.
#
ac_fn_c_check_member "$LINENO" "struct sockaddr" "sa_len" "ac_cv_member_struct_sockaddr_sa_len" "
	#include <sys/types.h>
	#include <sys/socket.h>
    
"
if test "x$ac_cv_member_struct_sockaddr_sa_len" = xyes
then :
  
printf "%s\n" "@%:@define HAVE_STRUCT_SOCKADDR_SA_LEN 1" >>confdefs.h


fi


#
# Various Linux-specific mechanisms.
#
@%:@ Check whether --enable-usb was given.
if test ${enable_usb+y}
then :
  enableval=$enable_usb; 
else $as_nop
  enable_usb=yes
fi


#
# If somebody requested an XXX-only pcap, that doesn't include
# additional mechanisms.
#
if test "$xxx_only" != yes; then
  case "$host_os" in
  linux*)
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for Linux usbmon USB sniffing support" >&5
printf %s "checking for Linux usbmon USB sniffing support... " >&6; }
    if test "x$enable_usb" != "xno" ; then
      
printf "%s\n" "@%:@define PCAP_SUPPORT_LINUX_USBMON 1" >>confdefs.h

      MODULE_C_SRC="$MODULE_C_SRC pcap-usb-linux.c"
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
      #
      # Note: if the directory for special files is *EVER* somewhere
      # other than the UN*X standard of /dev (which will break any
      # software that looks for /dev/null or /dev/tty, for example,
      # so doing that is *REALLY* not a good idea), please provide
      # some mechanism to determine that directory at *run time*,
      # rather than *configure time*, so that it works when doing
      # a cross-build, and that works with *multiple* distributions,
      # with our without udev, and with multiple versions of udev,
      # with udevinfo or udevadm or any other mechanism to get the
      # special files directory.
      #
      # Do we have a version of <linux/compiler.h> available?
      # If so, we might need it for <linux/usbdevice_fs.h>.
      #
      ac_fn_c_check_header_compile "$LINENO" "linux/compiler.h" "ac_cv_header_linux_compiler_h" "$ac_includes_default"
if test "x$ac_cv_header_linux_compiler_h" = xyes
then :
  printf "%s\n" "@%:@define HAVE_LINUX_COMPILER_H 1" >>confdefs.h

fi

      if test "$ac_cv_header_linux_compiler_h" = yes; then
        #
        # Yes - include it when testing for <linux/usbdevice_fs.h>.
        #
        ac_fn_c_check_header_compile "$LINENO" "linux/usbdevice_fs.h" "ac_cv_header_linux_usbdevice_fs_h" "#include <linux/compiler.h>
"
if test "x$ac_cv_header_linux_usbdevice_fs_h" = xyes
then :
  printf "%s\n" "@%:@define HAVE_LINUX_USBDEVICE_FS_H 1" >>confdefs.h

fi

      else
        ac_fn_c_check_header_compile "$LINENO" "linux/usbdevice_fs.h" "ac_cv_header_linux_usbdevice_fs_h" "$ac_includes_default"
if test "x$ac_cv_header_linux_usbdevice_fs_h" = xyes
then :
  printf "%s\n" "@%:@define HAVE_LINUX_USBDEVICE_FS_H 1" >>confdefs.h

fi

      fi
      if test "$ac_cv_header_linux_usbdevice_fs_h" = yes; then
        #
        # OK, does it define bRequestType?  Older versions of the kernel
        # define fields with names like "requesttype, "request", and
        # "value", rather than "bRequestType", "bRequest", and
        # "wValue".
        #
        ac_fn_c_check_member "$LINENO" "struct usbdevfs_ctrltransfer" "bRequestType" "ac_cv_member_struct_usbdevfs_ctrltransfer_bRequestType" "
            $ac_includes_default
            #ifdef HAVE_LINUX_COMPILER_H
            #include <linux/compiler.h>
            #endif
            #include <linux/usbdevice_fs.h>
          
"
if test "x$ac_cv_member_struct_usbdevfs_ctrltransfer_bRequestType" = xyes
then :
  
printf "%s\n" "@%:@define HAVE_STRUCT_USBDEVFS_CTRLTRANSFER_BREQUESTTYPE 1" >>confdefs.h


fi

      fi
    else
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
    fi

    #
    # Life's too short to deal with trying to get this to compile
    # if you don't get the right types defined with
    # __KERNEL_STRICT_NAMES getting defined by some other include.
    #
    # Check whether the includes Just Work.  If not, don't turn on
    # netfilter support.
    #
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether we can compile the netfilter support" >&5
printf %s "checking whether we can compile the netfilter support... " >&6; }
    if test ${ac_cv_netfilter_can_compile+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

$ac_includes_default
#include <sys/socket.h>
#include <netinet/in.h>
#include <linux/types.h>

#include <linux/netlink.h>
#include <linux/netfilter.h>
#include <linux/netfilter/nfnetlink.h>
#include <linux/netfilter/nfnetlink_log.h>
#include <linux/netfilter/nfnetlink_queue.h>
int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_netfilter_can_compile=yes
else $as_nop
  ac_cv_netfilter_can_compile=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi

    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_netfilter_can_compile" >&5
printf "%s\n" "$ac_cv_netfilter_can_compile" >&6; }
    if test $ac_cv_netfilter_can_compile = yes ; then
      
printf "%s\n" "@%:@define PCAP_SUPPORT_NETFILTER 1" >>confdefs.h

      MODULE_C_SRC="$MODULE_C_SRC pcap-netfilter-linux.c"
    fi
    ;;
  esac
fi



@%:@ Check whether --enable-netmap was given.
if test ${enable_netmap+y}
then :
  enableval=$enable_netmap; 
else $as_nop
  enable_netmap=yes
fi


# In this block "yes" means the same as "ifpresent" in the DAG block above.
# Windows is CMake-only.
case "$host_os" in
linux*|freebsd*)
	;;
*)
	enable_netmap=no
	;;
esac

if test "x$enable_netmap" != "xno" ; then
	#
	# Check whether net/netmap_user.h is usable if NETMAP_WITH_LIBS is
	# defined; it's not usable on DragonFly BSD 4.6 if NETMAP_WITH_LIBS
	# is defined, for example, as it includes a nonexistent malloc.h
	# header.
	#
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether we can compile the netmap support" >&5
printf %s "checking whether we can compile the netmap support... " >&6; }
	if test ${ac_cv_net_netmap_user_can_compile+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

$ac_includes_default
#define NETMAP_WITH_LIBS
#include <net/netmap_user.h>
int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_net_netmap_user_can_compile=yes
else $as_nop
  ac_cv_net_netmap_user_can_compile=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi

	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_net_netmap_user_can_compile" >&5
printf "%s\n" "$ac_cv_net_netmap_user_can_compile" >&6; }
	if test $ac_cv_net_netmap_user_can_compile = yes ; then
	  
printf "%s\n" "@%:@define PCAP_SUPPORT_NETMAP 1" >>confdefs.h

	    MODULE_C_SRC="$MODULE_C_SRC pcap-netmap.c"
	fi
	
fi

# Check for DPDK support.

@%:@ Check whether --with-dpdk was given.
if test ${with_dpdk+y}
then :
  withval=$with_dpdk; 
	if test "$withval" = no
	then
		# User doesn't want DPDK support.
		want_dpdk=no
	elif test "$withval" = yes
	then
		# User wants DPDK support but hasn't specified a directory.
		want_dpdk=yes
	else
		# User wants DPDK support and has specified a directory,
		# so use the provided value.
		want_dpdk=yes
		dpdk_dir=$withval
	fi

else $as_nop
  
	if test "$V_PCAP" = dpdk; then
		# User requested DPDK-only libpcap, so we'd better have
		# the DPDK API.
		want_dpdk=yes
	elif test "$xxx_only" = yes; then
		# User requested something-else-only pcap, so they don't
		# want DPDK support.
		want_dpdk=no
	else
		#
		# User didn't explicitly request DPDK; don't give it
		# to them without their consent, as the code is
		# immensely hard to keep compiling for every random
		# API change the DPDK folks make.
		#
		want_dpdk=no
	fi

fi


# Same as for DAG above.
case "$host_os" in
linux*|freebsd*)
	;;
*)
	case "$want_dpdk" in
	ifpresent)
		want_dpdk=no
		;;
	yes)
		as_fn_error $? "cannot enable DPDK support (neither Linux nor FreeBSD)" "$LINENO" 5
		;;
	esac
	;;
esac

if test "$want_dpdk" != no; then
	#
	# The user didn't explicitly say they don't want DPDK,
	# so see if we have it.
	#
	# We only try to find it using pkg-config; DPDK is *SO*
	# complicated - DPDK 19.02, for example, has about 117(!)
	# libraries, and the precise set of libraries required has
	# changed over time - so attempting to guess which libraries
	# you need, and hardcoding that in an attempt to find the
	# libraries without DPDK, rather than relying on DPDK to
	# tell you, with a .pc file, what libraries are needed,
	# is *EXTREMELY* fragile and has caused some bug reports,
	# so we're just not going to do it.
	#
	# If that causes a problem, the only thing we will do is
	# accept an alternative way of finding the appropriate
	# library set for the installed version of DPDK that is
	# as robust as pkg-config (i.e., it had better work as well
	# as pkg-config with *ALL* versions of DPDK that provide a
	# libdpdk.pc file).
	#
	# If --with-dpdk={path} was specified, add {path}/pkgconfig
	# to PKG_CONFIG_PATH, so we look for the .pc file there,
	# first.
	#
	save_PKG_CONFIG_PATH="$PKG_CONFIG_PATH"
	if test -n "$dpdk_dir"; then
		PKG_CONFIG_PATH="$dpdk_dir:$PKG_CONFIG_PATH"
	fi
	
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for libdpdk with pkg-config" >&5
printf %s "checking for libdpdk with pkg-config... " >&6; }
if test -n "$PKG_CONFIG"; then
            
    if { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$PKG_CONFIG --exists --print-errors \"libdpdk\""; } >&5
  ($PKG_CONFIG --exists --print-errors "libdpdk") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }; then
	#
	# The package was found, so try to get its C flags and
	# libraries.
	#
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: found" >&5
printf "%s\n" "found" >&6; }
	if test ! -n "$DPDK_CFLAGS"; then
    DPDK_CFLAGS=`$PKG_CONFIG --cflags "libdpdk" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --cflags "libdpdk" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --cflags "libdpdk" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --cflags \"libdpdk\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
	if test ! -n "$DPDK_LIBS"; then
    DPDK_LIBS=`$PKG_CONFIG --libs "libdpdk" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --libs "libdpdk" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --libs "libdpdk" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --libs \"libdpdk\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
	if test ! -n "$DPDK_LIBS_STATIC"; then
    DPDK_LIBS_STATIC=`$PKG_CONFIG --libs --static "libdpdk" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --libs --static "libdpdk" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --libs --static "libdpdk" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --libs --static \"libdpdk\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
        
		found_dpdk=yes
	    
    else
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: not found" >&5
printf "%s\n" "not found" >&6; }
        :
    fi
else
    # No pkg-config, so obviously not found with pkg-config.
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: pkg-config not found" >&5
printf "%s\n" "pkg-config not found" >&6; }
    :
fi

	PKG_CONFIG_PATH="$save_PKG_CONFIG_PATH"

	#
	# Did we find DPDK?
	#
	if test "$found_dpdk" = yes; then
		#
		# Found it.
		#
		# We call rte_eth_dev_count_avail(), and older versions
		# of DPDK didn't have it, so check for it.
		#
		
	save_CFLAGS="$CFLAGS"
	save_LIBS="$LIBS"
	save_LDFLAGS="$LDFLAGS"

		CFLAGS="$CFLAGS $DPDK_CFLAGS"
		LIBS="$LIBS $DPDK_LIBS"
		ac_fn_c_check_func "$LINENO" "rte_eth_dev_count_avail" "ac_cv_func_rte_eth_dev_count_avail"
if test "x$ac_cv_func_rte_eth_dev_count_avail" = xyes
then :
  
fi

		
	CFLAGS="$save_CFLAGS"
	LIBS="$save_LIBS"
	LDFLAGS="$save_LDFLAGS"

	fi

	if test "$ac_cv_func_rte_eth_dev_count_avail" = yes; then
		#
		# We found a usable DPDK.
		#
		# Check whether the rte_ether.h file defines
		# struct ether_addr or struct rte_ether_addr.
		#
		# ("API compatibility?  That's for losers!")
		#
		
	save_CFLAGS="$CFLAGS"
	save_LIBS="$LIBS"
	save_LDFLAGS="$LDFLAGS"

		CFLAGS="$CFLAGS $DPDK_CFLAGS"
		LIBS="$LIBS $DPDK_LIBS"
		ac_fn_c_check_type "$LINENO" "struct rte_ether_addr" "ac_cv_type_struct_rte_ether_addr" "
			#include <rte_ether.h>
		    
"
if test "x$ac_cv_type_struct_rte_ether_addr" = xyes
then :
  
printf "%s\n" "@%:@define HAVE_STRUCT_RTE_ETHER_ADDR 1" >>confdefs.h


fi

		
	CFLAGS="$save_CFLAGS"
	LIBS="$save_LIBS"
	LDFLAGS="$save_LDFLAGS"


		#
		# We can build with DPDK.
		#
		V_INCLS="$V_INCLS $DPDK_CFLAGS"
		ADDITIONAL_LIBS="$ADDITIONAL_LIBS $DPDK_LIBS"
		ADDITIONAL_LIBS_STATIC="$ADDITIONAL_LIBS_STATIC $DPDK_LIBS_STATIC"
		REQUIRES_PRIVATE="$REQUIRES_PRIVATE libdpdk"
		
printf "%s\n" "@%:@define PCAP_SUPPORT_DPDK 1" >>confdefs.h

		if test $V_PCAP != dpdk ; then
			MODULE_C_SRC="$MODULE_C_SRC pcap-dpdk.c"
		fi

		#
		# The last line of the output is:
		# "checking for struct rte_ether_addr... yes"
		# Make it clear what it means for the final result.
		#
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: building with DPDK" >&5
printf "%s\n" "$as_me: building with DPDK" >&6;}
	else
		#
		# We didn't find a usable DPDK.
		# If we required it (with --with-dpdk or --with-pcap=dpdk),
		# fail with an appropriate message telling the user what
		# the problem was.
		#
		if test "$found_dpdk" != yes; then
			#
			# Not found with pkg-config.  Note that we
			# require that DPDK must be findable with
			# pkg-config.
			#
			if test "$V_PCAP" = dpdk; then
				#
				# User requested DPDK-only capture support.
				#
				as_fn_error $? "DPDK support requested with --with-pcap=dpdk, but
we couldn't find DPDK with pkg-config.  Make sure that pkg-config is
installed, that DPDK 18.02.2 or later is installed, and that DPDK
provides a .pc file." "$LINENO" 5
			fi

			if test "$want_dpdk" = yes; then
				#
				# User requested that libpcap include
				# DPDK capture support.
				#
				as_fn_error $? "DPDK support requested with --with-dpdk, but we
couldn't find DPDK with pkg-config.  Make sure that pkg-config
is installed, that DPDK 18.02.2 or later is installed, and that
DPDK provides .pc file." "$LINENO" 5
			fi

			#
			# User didn't indicate whether they wanted DPDK
			# or not; the last line of the output is one of:
			# "checking for libdpdk with pkg-config... pkg-config not found"
			# "checking for libdpdk with pkg-config... not found"
			# This conveys the point and is the right amount of
			# output when auto-detecting an optional module, so do
			# not print anything else.
			#
		elif test "$ac_cv_func_rte_eth_dev_count_avail" != yes; then
			#
			# Found with pkg-config, but we couldn't compile
			# a program that calls rte_eth_dev_count(); we
			# probably have the developer package installed,
			# but don't have a sufficiently recent version
			# of DPDK.  Note that we need a sufficiently
			# recent version of DPDK.
			#
			if test "$V_PCAP" = dpdk; then
				#
				# User requested DPDK-only capture support.
				#
				as_fn_error $? "DPDK support requested with --with-pcap=dpdk, but we
can't compile libpcap with DPDK.  Make sure that DPDK 18.02.2 or later
is installed." "$LINENO" 5
			fi

			if test "$want_dpdk" = yes; then
				#
				# User requested that libpcap include
				# DPDK capture support.
				#
				as_fn_error $? "DPDK support requested with --with-dpdk, but
we can't compile libpcap with DPDK.  Make sure that DPDK 18.02.2
or later is DPDK is installed." "$LINENO" 5
			fi

			#
			# User didn't indicate whether they wanted DPDK
			# or not; the last line of the output is:
			# "checking for rte_eth_dev_count_avail... no"
			# Make it clear what it means for the final result.
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: building without DPDK (present, but cannot be used)" >&5
printf "%s\n" "$as_me: building without DPDK (present, but cannot be used)" >&6;}
		fi
	fi
fi


@%:@ Check whether --enable-bluetooth was given.
if test ${enable_bluetooth+y}
then :
  enableval=$enable_bluetooth; 
else $as_nop
  enable_bluetooth=ifsupportavailable
fi


if test "$xxx_only" = yes; then
	# User requested something-else-only pcap, so they don't
	# want Bluetooth support.
	enable_bluetooth=no
fi

if test "x$enable_bluetooth" != "xno" ; then
		case "$host_os" in
	linux*)
		ac_fn_c_check_header_compile "$LINENO" "bluetooth/bluetooth.h" "ac_cv_header_bluetooth_bluetooth_h" "$ac_includes_default"
if test "x$ac_cv_header_bluetooth_bluetooth_h" = xyes
then :
  
			#
			# We have bluetooth.h, so we support Bluetooth
			# sniffing.
			#
			
printf "%s\n" "@%:@define PCAP_SUPPORT_BT 1" >>confdefs.h

			MODULE_C_SRC="$MODULE_C_SRC pcap-bt-linux.c"
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: Bluetooth sniffing is supported" >&5
printf "%s\n" "$as_me: Bluetooth sniffing is supported" >&6;}
			ac_lbl_bluetooth_available=yes

			#
			# OK, does struct sockaddr_hci have an hci_channel
			# member?
			#
			ac_fn_c_check_member "$LINENO" "struct sockaddr_hci" "hci_channel" "ac_cv_member_struct_sockaddr_hci_hci_channel" "
				#include <bluetooth/bluetooth.h>
				#include <bluetooth/hci.h>
			    
"
if test "x$ac_cv_member_struct_sockaddr_hci_hci_channel" = xyes
then :
  
printf "%s\n" "@%:@define HAVE_STRUCT_SOCKADDR_HCI_HCI_CHANNEL 1" >>confdefs.h


				#
				# Yes; is HCI_CHANNEL_MONITOR defined?
				#
				{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking if HCI_CHANNEL_MONITOR is defined" >&5
printf %s "checking if HCI_CHANNEL_MONITOR is defined... " >&6; }
				if test ${ac_cv_lbl_hci_channel_monitor_is_defined+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

					    #include <bluetooth/bluetooth.h>
					    #include <bluetooth/hci.h>
					
int
main (void)
{

					    int i = HCI_CHANNEL_MONITOR;
					
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  
					    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
					    
printf "%s\n" "@%:@define PCAP_SUPPORT_BT_MONITOR 1" >>confdefs.h

					    MODULE_C_SRC="$MODULE_C_SRC pcap-bt-monitor-linux.c"
					
else $as_nop
  
					    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
					
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi

			    
fi

		    
else $as_nop
  
			#
			# We don't have bluetooth.h, so we don't support
			# Bluetooth sniffing.
			#
			if test "x$enable_bluetooth" = "xyes" ; then
				as_fn_error $? "Bluetooth sniffing is not supported; install a Bluetooth devel library (libbluetooth-dev|bluez-libs-devel|bluez-dev|libbluetooth-devel|...) to enable it" "$LINENO" 5
			else
				{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: Bluetooth sniffing is not supported; install a Bluetooth devel library (libbluetooth-dev|bluez-libs-devel|bluez-dev|libbluetooth-devel|...) to enable it" >&5
printf "%s\n" "$as_me: Bluetooth sniffing is not supported; install a Bluetooth devel library (libbluetooth-dev|bluez-libs-devel|bluez-dev|libbluetooth-devel|...) to enable it" >&6;}
			fi
		    
fi

		;;
	*)
		if test "x$enable_bluetooth" = "xyes" ; then
			as_fn_error $? "no Bluetooth sniffing support implemented for $host_os" "$LINENO" 5
		else
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: no Bluetooth sniffing support implemented for $host_os" >&5
printf "%s\n" "$as_me: no Bluetooth sniffing support implemented for $host_os" >&6;}
		fi
		;;
	esac
	
fi

@%:@ Check whether --enable-dbus was given.
if test ${enable_dbus+y}
then :
  enableval=$enable_dbus; 
else $as_nop
  enable_dbus=ifavailable
fi


if test "$xxx_only" = yes; then
	# User requested something-else-only pcap, so they don't
	# want D-Bus support.
	enable_dbus=no
fi

if test "x$enable_dbus" != "xno"; then
	if test "x$enable_dbus" = "xyes"; then
		case "$host_os" in

		darwin*)
			#
			# We don't support D-Bus sniffing on macOS; see
			#
			# https://bugs.freedesktop.org/show_bug.cgi?id=74029
			#
			# The user requested it, so fail.
			#
			as_fn_error $? "Due to freedesktop.org bug 74029, D-Bus capture support is not available on macOS" "$LINENO" 5
		esac
	else
		case "$host_os" in

		darwin*)
			#
			# We don't support D-Bus sniffing on macOS; see
			#
			# https://bugs.freedesktop.org/show_bug.cgi?id=74029
			#
			# The user didn't explicitly request it, so just
			# silently refuse to enable it.
			#
			enable_dbus="no"
			;;
		esac
	fi
fi

if test "x$enable_dbus" != "xno"; then
	
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for dbus-1 with pkg-config" >&5
printf %s "checking for dbus-1 with pkg-config... " >&6; }
if test -n "$PKG_CONFIG"; then
            
    if { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$PKG_CONFIG --exists --print-errors \"dbus-1\""; } >&5
  ($PKG_CONFIG --exists --print-errors "dbus-1") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }; then
	#
	# The package was found, so try to get its C flags and
	# libraries.
	#
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: found" >&5
printf "%s\n" "found" >&6; }
	if test ! -n "$DBUS_CFLAGS"; then
    DBUS_CFLAGS=`$PKG_CONFIG --cflags "dbus-1" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --cflags "dbus-1" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --cflags "dbus-1" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --cflags \"dbus-1\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
	if test ! -n "$DBUS_LIBS"; then
    DBUS_LIBS=`$PKG_CONFIG --libs "dbus-1" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --libs "dbus-1" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --libs "dbus-1" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --libs \"dbus-1\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
	if test ! -n "$DBUS_LIBS_STATIC"; then
    DBUS_LIBS_STATIC=`$PKG_CONFIG --libs --static "dbus-1" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --libs --static "dbus-1" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --libs --static "dbus-1" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --libs --static \"dbus-1\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
        
		
	save_CFLAGS="$CFLAGS"
	save_LIBS="$LIBS"
	save_LDFLAGS="$LDFLAGS"

		CFLAGS="$CFLAGS $DBUS_CFLAGS"
		LIBS="$LIBS $DBUS_LIBS"
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the D-Bus library defines dbus_connection_read_write" >&5
printf %s "checking whether the D-Bus library defines dbus_connection_read_write... " >&6; }
		cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <string.h>

		     #include <time.h>
		     #include <sys/time.h>

		     #include <dbus/dbus.h>
int
main (void)
{
return dbus_connection_read_write(NULL, 0);
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
			
printf "%s\n" "@%:@define PCAP_SUPPORT_DBUS 1" >>confdefs.h

			MODULE_C_SRC="$MODULE_C_SRC pcap-dbus.c"
			V_INCLS="$V_INCLS $DBUS_CFLAGS"
			ADDITIONAL_LIBS="$ADDITIONAL_LIBS $DBUS_LIBS"
			ADDITIONAL_LIBS_STATIC="$ADDITIONAL_LIBS_STATIC $DBUS_LIBS_STATIC"
			REQUIRES_PRIVATE="$REQUIRES_PRIVATE dbus-1"
		    
else $as_nop
  
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
			if test "x$enable_dbus" = "xyes"; then
			    as_fn_error $? "--enable-dbus was given, but the D-Bus library doesn't define dbus_connection_read_write()" "$LINENO" 5
			fi
		     
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
		
	CFLAGS="$save_CFLAGS"
	LIBS="$save_LIBS"
	LDFLAGS="$save_LDFLAGS"

	    
    else
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: not found" >&5
printf "%s\n" "not found" >&6; }
        
		if test "x$enable_dbus" = "xyes"; then
			as_fn_error $? "--enable-dbus was given, but the dbus-1 package is not installed" "$LINENO" 5
		fi
	    
    fi
else
    # No pkg-config, so obviously not found with pkg-config.
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: pkg-config not found" >&5
printf "%s\n" "pkg-config not found" >&6; }
    
		if test "x$enable_dbus" = "xyes"; then
			as_fn_error $? "--enable-dbus was given, but the dbus-1 package is not installed" "$LINENO" 5
		fi
	    
fi

	
fi

@%:@ Check whether --enable-rdma was given.
if test ${enable_rdma+y}
then :
  enableval=$enable_rdma; 
else $as_nop
  enable_rdma=ifavailable
fi


if test "$xxx_only" = yes; then
	# User requested something-else-only pcap, so they don't
	# want RDMA support.
	enable_rdma=no
fi

# Same as for DAG above.
if ! expr "$host_os" : linux >/dev/null; then
	case "$enable_rdma" in
	ifavailable)
		enable_rdma=no
		;;
	yes)
		as_fn_error $? "cannot enable RDMA support (not Linux)" "$LINENO" 5
		;;
	esac
fi

if test "x$enable_rdma" != "xno"; then
	
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for libibverbs with pkg-config" >&5
printf %s "checking for libibverbs with pkg-config... " >&6; }
if test -n "$PKG_CONFIG"; then
            
    if { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$PKG_CONFIG --exists --print-errors \"libibverbs\""; } >&5
  ($PKG_CONFIG --exists --print-errors "libibverbs") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }; then
	#
	# The package was found, so try to get its C flags and
	# libraries.
	#
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: found" >&5
printf "%s\n" "found" >&6; }
	if test ! -n "$LIBIBVERBS_CFLAGS"; then
    LIBIBVERBS_CFLAGS=`$PKG_CONFIG --cflags "libibverbs" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --cflags "libibverbs" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --cflags "libibverbs" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --cflags \"libibverbs\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
	if test ! -n "$LIBIBVERBS_LIBS"; then
    LIBIBVERBS_LIBS=`$PKG_CONFIG --libs "libibverbs" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --libs "libibverbs" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --libs "libibverbs" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --libs \"libibverbs\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
	if test ! -n "$LIBIBVERBS_LIBS_STATIC"; then
    LIBIBVERBS_LIBS_STATIC=`$PKG_CONFIG --libs --static "libibverbs" 2>/dev/null`
    if test "x$?" != "x0"; then
        #
        # That failed - report an error.
        # Re-run the command, telling pkg-config to print an error
        # message, capture the error message, and report it.
        # This causes the configuration script to fail, as it means
        # the script is almost certainly doing something wrong.
        #
        
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi
	if test $_pkg_short_errors_supported = yes; then
	    _pkg_error_string=`$PKG_CONFIG --short-errors --print-errors --libs --static "libibverbs" 2>&1`
	else
	    _pkg_error_string=`$PKG_CONFIG --print-errors --libs --static "libibverbs" 2>&1`
	fi
        as_fn_error $? "$PKG_CONFIG --libs --static \"libibverbs\" failed: $_pkg_error_string" "$LINENO" 5
    fi
 fi
        
		found_libibverbs=yes
		LIBIBVERBS_REQUIRES_PRIVATE="libibverbs"
	    
    else
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: not found" >&5
printf "%s\n" "not found" >&6; }
        :
    fi
else
    # No pkg-config, so obviously not found with pkg-config.
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: pkg-config not found" >&5
printf "%s\n" "pkg-config not found" >&6; }
    :
fi


	if test "x$found_libibverbs" != "xyes"; then
		{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for ibv_get_device_list in -libverbs" >&5
printf %s "checking for ibv_get_device_list in -libverbs... " >&6; }
if test ${ac_cv_lib_ibverbs_ibv_get_device_list+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-libverbs  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char ibv_get_device_list ();
int
main (void)
{
return ibv_get_device_list ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_ibverbs_ibv_get_device_list=yes
else $as_nop
  ac_cv_lib_ibverbs_ibv_get_device_list=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_ibverbs_ibv_get_device_list" >&5
printf "%s\n" "$ac_cv_lib_ibverbs_ibv_get_device_list" >&6; }
if test "x$ac_cv_lib_ibverbs_ibv_get_device_list" = xyes
then :
  
			found_libibverbs=yes
			LIBIBVERBS_CFLAGS=""
			LIBIBVERBS_LIBS="-libverbs"
			# XXX - at least on Ubuntu 20.04, there are many more
			# libraries needed; is there any platform where
			# libibverbs is available but where pkg-config isn't
			# available or libibverbs doesn't use it?  If not,
			# we should only use pkg-config for it.
			LIBIBVERBS_LIBS_STATIC="-libverbs"
			LIBIBVERBS_LIBS_PRIVATE="-libverbs"
		    
		
fi

	fi

	if test "x$found_libibverbs" = "xyes"; then
		
	save_CFLAGS="$CFLAGS"
	save_LIBS="$LIBS"
	save_LDFLAGS="$LDFLAGS"

		CFLAGS="$CFLAGS $LIBIBVERBS_CFLAGS"
		LIBS="$LIBS $LIBIBVERBS_LIBS"
		ac_fn_c_check_header_compile "$LINENO" "infiniband/verbs.h" "ac_cv_header_infiniband_verbs_h" "$ac_includes_default"
if test "x$ac_cv_header_infiniband_verbs_h" = xyes
then :
  
			#
			# ibv_create_flow may be defined as a static inline
			# function in infiniband/verbs.h, so we can't
			# use AC_CHECK_LIB.
			#
			# Too bad autoconf has no AC_SYMBOL_EXISTS()
			# macro that works like CMake's check_symbol_exists()
			# function, to check do a compile check like
			# this (they do a clever trick to avoid having
			# to know the function's signature).
			#
			{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether libibverbs defines ibv_create_flow" >&5
printf %s "checking whether libibverbs defines ibv_create_flow... " >&6; }
			cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

					#include <infiniband/verbs.h>
				
int
main (void)
{

					(void) ibv_create_flow((struct ibv_qp *) NULL,
							       (struct ibv_flow_attr *) NULL);
				
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  
					{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
					found_usable_libibverbs=yes
				
else $as_nop
  
					{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
				
			
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
		
fi

		
	CFLAGS="$save_CFLAGS"
	LIBS="$save_LIBS"
	LDFLAGS="$save_LDFLAGS"

	fi

	if test "x$found_usable_libibverbs" = "xyes"
	then
		
printf "%s\n" "@%:@define PCAP_SUPPORT_RDMASNIFF 1" >>confdefs.h

		MODULE_C_SRC="$MODULE_C_SRC pcap-rdmasniff.c"
		CFLAGS="$LIBIBVERBS_CFLAGS $CFLAGS"
		ADDITIONAL_LIBS="$LIBIBVERBS_LIBS $ADDITIONAL_LIBS"
		ADDITIONAL_LIBS_STATIC="$LIBIBVERBS_LIBS_STATIC $ADDITIONAL_LIBS_STATIC"
		LIBS_PRIVATE="$LIBIBVERBS_LIBS_PRIVATE $LIBS_PRIVATE"
		REQUIRES_PRIVATE="$REQUIRES_PRIVATE $LIBIBVERBS_REQUIRES_PRIVATE"
	fi
	
fi

#
# If this is a platform where we need to have the .pc file and
# pcap-config script supply an rpath option to specify the directory
# in which the libpcap shared library is installed, and the install
# prefix /usr (meaning we're not installing a system library), provide
# the rpath option.
#
# (We must check $prefix, as $libdir isn't necessarily /usr/lib in this
# case - for example, Linux distributions for 64-bit platforms that
# also provide support for binaries for a 32-bit version of the
# platform may put the 64-bit libraries, the 32-bit libraries, or both
# in directories other than /usr/lib.)
#
# In AIX, do we have to do this?
#
# In Darwin-based OSes, the full paths of the shared libraries with
# which the program was linked are stored in the executable, so we don't
# need to provide an rpath option.
#
# With the HP-UX linker, directories specified with -L are, by default,
# added to the run-time search path, so we don't need to supply them.
#
# This must *not* depend on the compiler, as, on platforms where there's
# a GCC-compatible compiler and a vendor compiler, we need to work with
# both.
#
if test "$prefix" != "/usr"; then
	case "$host_os" in

	freebsd*|netbsd*|openbsd*|dragonfly*|linux*|haiku*|midipix*|gnu*)
		#
		# Platforms where the "native" C compiler is GCC or
		# accepts compatible command-line arguments, and the
		# "native" linker is the GNU linker or accepts
		# compatible command-line arguments.
		#
		RPATH="-Wl,-rpath,\${libdir}"
		;;

	solaris*)
		#
		# Sun/Oracle's linker, the GNU linker, and
		# GNU-compatible linkers all support -R.
		#
		RPATH="-Wl,-R,\${libdir}"
		;;
	esac
fi


  # Find a good install program.  We prefer a C program (faster),
# so one script is as good as another.  But avoid the broken or
# incompatible versions:
# SysV /etc/install, /usr/sbin/install
# SunOS /usr/etc/install
# IRIX /sbin/install
# AIX /bin/install
# AmigaOS /C/install, which installs bootblocks on floppy discs
# AIX 4 /usr/bin/installbsd, which doesn't work without a -g flag
# AFS /usr/afsws/bin/install, which mishandles nonexistent args
# SVR4 /usr/ucb/install, which tries to use the nonexistent group "staff"
# OS/2's system install, which has a completely different semantic
# ./install, which can be erroneously created by make from ./install.sh.
# Reject install programs that cannot install multiple files.
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for a BSD-compatible install" >&5
printf %s "checking for a BSD-compatible install... " >&6; }
if test -z "$INSTALL"; then
if test ${ac_cv_path_install+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    # Account for fact that we put trailing slashes in our PATH walk.
case $as_dir in @%:@((
  ./ | /[cC]/* | \
  /etc/* | /usr/sbin/* | /usr/etc/* | /sbin/* | /usr/afsws/bin/* | \
  ?:[\\/]os2[\\/]install[\\/]* | ?:[\\/]OS2[\\/]INSTALL[\\/]* | \
  /usr/ucb/* ) ;;
  *)
    # OSF1 and SCO ODT 3.0 have their own names for install.
    # Don't use installbsd from OSF since it installs stuff as root
    # by default.
    for ac_prog in ginstall scoinst install; do
      for ac_exec_ext in '' $ac_executable_extensions; do
	if as_fn_executable_p "$as_dir$ac_prog$ac_exec_ext"; then
	  if test $ac_prog = install &&
	    grep dspmsg "$as_dir$ac_prog$ac_exec_ext" >/dev/null 2>&1; then
	    # AIX install.  It has an incompatible calling convention.
	    :
	  elif test $ac_prog = install &&
	    grep pwplus "$as_dir$ac_prog$ac_exec_ext" >/dev/null 2>&1; then
	    # program-specific install script used by HP pwplus--don't use.
	    :
	  else
	    rm -rf conftest.one conftest.two conftest.dir
	    echo one > conftest.one
	    echo two > conftest.two
	    mkdir conftest.dir
	    if "$as_dir$ac_prog$ac_exec_ext" -c conftest.one conftest.two "`pwd`/conftest.dir/" &&
	      test -s conftest.one && test -s conftest.two &&
	      test -s conftest.dir/conftest.one &&
	      test -s conftest.dir/conftest.two
	    then
	      ac_cv_path_install="$as_dir$ac_prog$ac_exec_ext -c"
	      break 3
	    fi
	  fi
	fi
      done
    done
    ;;
esac

  done
IFS=$as_save_IFS

rm -rf conftest.one conftest.two conftest.dir

fi
  if test ${ac_cv_path_install+y}; then
    INSTALL=$ac_cv_path_install
  else
    # As a last resort, use the slow shell script.  Don't cache a
    # value for INSTALL within a source directory, because that will
    # break other packages using the cache if that directory is
    # removed, or if the value is a relative name.
    INSTALL=$ac_install_sh
  fi
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $INSTALL" >&5
printf "%s\n" "$INSTALL" >&6; }

# Use test -z because SunOS4 sh mishandles braces in ${var-val}.
# It thinks the first close brace ends the variable substitution.
test -z "$INSTALL_PROGRAM" && INSTALL_PROGRAM='${INSTALL}'

test -z "$INSTALL_SCRIPT" && INSTALL_SCRIPT='${INSTALL}'

test -z "$INSTALL_DATA" && INSTALL_DATA='${INSTALL} -m 644'


ac_config_headers="$ac_config_headers config.h"

















#
# We're done with configuration operations; add ADDITIONAL_LIBS and
# ADDITIONAL_LIBS_STATIC to LIBS and LIBS_STATIC, respectively.
#
LIBS="$ADDITIONAL_LIBS $LIBS"
LIBS_STATIC="$ADDITIONAL_LIBS_STATIC $LIBS_STATIC"

ac_config_commands="$ac_config_commands .devel"

ac_config_files="$ac_config_files Makefile grammar.y pcap-filter.manmisc pcap-linktype.manmisc pcap-tstamp.manmisc cbpf-savefile.manfile pcap-savefile.manfile pcap.3pcap pcap_compile.3pcap pcap_datalink.3pcap pcap_dump_open.3pcap pcap_get_tstamp_precision.3pcap pcap_list_datalinks.3pcap pcap_list_tstamp_types.3pcap pcap_open_dead.3pcap pcap_open_offline.3pcap pcap_set_immediate_mode.3pcap pcap_set_tstamp_precision.3pcap pcap_set_tstamp_type.3pcap rpcapd/Makefile rpcapd/rpcapd.manadmin rpcapd/rpcapd-config.manfile testprogs/Makefile"

cat >confcache <<\_ACEOF
# This file is a shell script that caches the results of configure
# tests run on this system so they can be shared between configure
# scripts and configure runs, see configure's option --config-cache.
# It is not useful on other systems.  If it contains results you don't
# want to keep, you may remove or edit it.
#
# config.status only pays attention to the cache file if you give it
# the --recheck option to rerun configure.
#
# `ac_cv_env_foo' variables (set or unset) will be overridden when
# loading this file, other *unset* `ac_cv_foo' will be assigned the
# following values.

_ACEOF

# The following way of writing the cache mishandles newlines in values,
# but we know of no workaround that is simple, portable, and efficient.
# So, we kill variables containing newlines.
# Ultrix sh set writes to stderr and can't be redirected directly,
# and sets the high bit in the cache file unless we assign to the vars.
(
  for ac_var in `(set) 2>&1 | sed -n 's/^\([a-zA-Z_][a-zA-Z0-9_]*\)=.*/\1/p'`; do
    eval ac_val=\$$ac_var
    case $ac_val in #(
    *${as_nl}*)
      case $ac_var in #(
      *_cv_*) { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: cache variable $ac_var contains a newline" >&5
printf "%s\n" "$as_me: WARNING: cache variable $ac_var contains a newline" >&2;} ;;
      esac
      case $ac_var in #(
      _ | IFS | as_nl) ;; #(
      BASH_ARGV | BASH_SOURCE) eval $ac_var= ;; #(
      *) { eval $ac_var=; unset $ac_var;} ;;
      esac ;;
    esac
  done

  (set) 2>&1 |
    case $as_nl`(ac_space=' '; set) 2>&1` in #(
    *${as_nl}ac_space=\ *)
      # `set' does not quote correctly, so add quotes: double-quote
      # substitution turns \\\\ into \\, and sed turns \\ into \.
      sed -n \
	"s/'/'\\\\''/g;
	  s/^\\([_$as_cr_alnum]*_cv_[_$as_cr_alnum]*\\)=\\(.*\\)/\\1='\\2'/p"
      ;; #(
    *)
      # `set' quotes correctly as required by POSIX, so do not add quotes.
      sed -n "/^[_$as_cr_alnum]*_cv_[_$as_cr_alnum]*=/p"
      ;;
    esac |
    sort
) |
  sed '
     /^ac_cv_env_/b end
     t clear
     :clear
     s/^\([^=]*\)=\(.*[{}].*\)$/test ${\1+y} || &/
     t end
     s/^\([^=]*\)=\(.*\)$/\1=${\1=\2}/
     :end' >>confcache
if diff "$cache_file" confcache >/dev/null 2>&1; then :; else
  if test -w "$cache_file"; then
    if test "x$cache_file" != "x/dev/null"; then
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: updating cache $cache_file" >&5
printf "%s\n" "$as_me: updating cache $cache_file" >&6;}
      if test ! -f "$cache_file" || test -h "$cache_file"; then
	cat confcache >"$cache_file"
      else
        case $cache_file in #(
        */* | ?:*)
	  mv -f confcache "$cache_file"$$ &&
	  mv -f "$cache_file"$$ "$cache_file" ;; #(
        *)
	  mv -f confcache "$cache_file" ;;
	esac
      fi
    fi
  else
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: not updating unwritable cache $cache_file" >&5
printf "%s\n" "$as_me: not updating unwritable cache $cache_file" >&6;}
  fi
fi
rm -f confcache

test "x$prefix" = xNONE && prefix=$ac_default_prefix
# Let make expand exec_prefix.
test "x$exec_prefix" = xNONE && exec_prefix='${prefix}'

DEFS=-DHAVE_CONFIG_H

ac_libobjs=
ac_ltlibobjs=
U=
for ac_i in : $LIB@&t@OBJS; do test "x$ac_i" = x: && continue
  # 1. Remove the extension, and $U if already installed.
  ac_script='s/\$U\././;s/\.o$//;s/\.obj$//'
  ac_i=`printf "%s\n" "$ac_i" | sed "$ac_script"`
  # 2. Prepend LIBOBJDIR.  When used with automake>=1.10 LIBOBJDIR
  #    will be set to the directory where LIBOBJS objects are built.
  as_fn_append ac_libobjs " \${LIBOBJDIR}$ac_i\$U.$ac_objext"
  as_fn_append ac_ltlibobjs " \${LIBOBJDIR}$ac_i"'$U.lo'
done
LIB@&t@OBJS=$ac_libobjs

LTLIBOBJS=$ac_ltlibobjs



: "${CONFIG_STATUS=./config.status}"
ac_write_fail=0
ac_clean_files_save=$ac_clean_files
ac_clean_files="$ac_clean_files $CONFIG_STATUS"
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: creating $CONFIG_STATUS" >&5
printf "%s\n" "$as_me: creating $CONFIG_STATUS" >&6;}
as_write_fail=0
cat >$CONFIG_STATUS <<_ASEOF || as_write_fail=1
#! $SHELL
# Generated by $as_me.
# Run this file to recreate the current configuration.
# Compiler output produced by configure, useful for debugging
# configure, is in config.log if it exists.

debug=false
ac_cs_recheck=false
ac_cs_silent=false

SHELL=\${CONFIG_SHELL-$SHELL}
export SHELL
_ASEOF
cat >>$CONFIG_STATUS <<\_ASEOF || as_write_fail=1
## -------------------- ##
## M4sh Initialization. ##
## -------------------- ##

# Be more Bourne compatible
DUALCASE=1; export DUALCASE # for MKS sh
as_nop=:
if test ${ZSH_VERSION+y} && (emulate sh) >/dev/null 2>&1
then :
  emulate sh
  NULLCMD=:
  # Pre-4.2 versions of Zsh do word splitting on ${1+"$@"}, which
  # is contrary to our usage.  Disable this feature.
  alias -g '${1+"$@"}'='"$@"'
  setopt NO_GLOB_SUBST
else $as_nop
  case `(set -o) 2>/dev/null` in @%:@(
  *posix*) :
    set -o posix ;; @%:@(
  *) :
     ;;
esac
fi



# Reset variables that may have inherited troublesome values from
# the environment.

# IFS needs to be set, to space, tab, and newline, in precisely that order.
# (If _AS_PATH_WALK were called with IFS unset, it would have the
# side effect of setting IFS to empty, thus disabling word splitting.)
# Quoting is to prevent editors from complaining about space-tab.
as_nl='
'
export as_nl
IFS=" ""	$as_nl"

PS1='$ '
PS2='> '
PS4='+ '

# Ensure predictable behavior from utilities with locale-dependent output.
LC_ALL=C
export LC_ALL
LANGUAGE=C
export LANGUAGE

# We cannot yet rely on "unset" to work, but we need these variables
# to be unset--not just set to an empty or harmless value--now, to
# avoid bugs in old shells (e.g. pre-3.0 UWIN ksh).  This construct
# also avoids known problems related to "unset" and subshell syntax
# in other old shells (e.g. bash 2.01 and pdksh 5.2.14).
for as_var in BASH_ENV ENV MAIL MAILPATH CDPATH
do eval test \${$as_var+y} \
  && ( (unset $as_var) || exit 1) >/dev/null 2>&1 && unset $as_var || :
done

# Ensure that fds 0, 1, and 2 are open.
if (exec 3>&0) 2>/dev/null; then :; else exec 0</dev/null; fi
if (exec 3>&1) 2>/dev/null; then :; else exec 1>/dev/null; fi
if (exec 3>&2)            ; then :; else exec 2>/dev/null; fi

# The user is always right.
if ${PATH_SEPARATOR+false} :; then
  PATH_SEPARATOR=:
  (PATH='/bin;/bin'; FPATH=$PATH; sh -c :) >/dev/null 2>&1 && {
    (PATH='/bin:/bin'; FPATH=$PATH; sh -c :) >/dev/null 2>&1 ||
      PATH_SEPARATOR=';'
  }
fi


# Find who we are.  Look in the path if we contain no directory separator.
as_myself=
case $0 in @%:@((
  *[\\/]* ) as_myself=$0 ;;
  *) as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    test -r "$as_dir$0" && as_myself=$as_dir$0 && break
  done
IFS=$as_save_IFS

     ;;
esac
# We did not find ourselves, most probably we were run as `sh COMMAND'
# in which case we are not to be found in the path.
if test "x$as_myself" = x; then
  as_myself=$0
fi
if test ! -f "$as_myself"; then
  printf "%s\n" "$as_myself: error: cannot find myself; rerun with an absolute file name" >&2
  exit 1
fi



@%:@ as_fn_error STATUS ERROR [LINENO LOG_FD]
@%:@ ----------------------------------------
@%:@ Output "`basename @S|@0`: error: ERROR" to stderr. If LINENO and LOG_FD are
@%:@ provided, also output the error to LOG_FD, referencing LINENO. Then exit the
@%:@ script with STATUS, using 1 if that was 0.
as_fn_error ()
{
  as_status=$1; test $as_status -eq 0 && as_status=1
  if test "$4"; then
    as_lineno=${as_lineno-"$3"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: $2" >&$4
  fi
  printf "%s\n" "$as_me: error: $2" >&2
  as_fn_exit $as_status
} @%:@ as_fn_error



@%:@ as_fn_set_status STATUS
@%:@ -----------------------
@%:@ Set @S|@? to STATUS, without forking.
as_fn_set_status ()
{
  return $1
} @%:@ as_fn_set_status

@%:@ as_fn_exit STATUS
@%:@ -----------------
@%:@ Exit the shell with STATUS, even in a "trap 0" or "set -e" context.
as_fn_exit ()
{
  set +e
  as_fn_set_status $1
  exit $1
} @%:@ as_fn_exit

@%:@ as_fn_unset VAR
@%:@ ---------------
@%:@ Portably unset VAR.
as_fn_unset ()
{
  { eval $1=; unset $1;}
}
as_unset=as_fn_unset

@%:@ as_fn_append VAR VALUE
@%:@ ----------------------
@%:@ Append the text in VALUE to the end of the definition contained in VAR. Take
@%:@ advantage of any shell optimizations that allow amortized linear growth over
@%:@ repeated appends, instead of the typical quadratic growth present in naive
@%:@ implementations.
if (eval "as_var=1; as_var+=2; test x\$as_var = x12") 2>/dev/null
then :
  eval 'as_fn_append ()
  {
    eval $1+=\$2
  }'
else $as_nop
  as_fn_append ()
  {
    eval $1=\$$1\$2
  }
fi # as_fn_append

@%:@ as_fn_arith ARG...
@%:@ ------------------
@%:@ Perform arithmetic evaluation on the ARGs, and store the result in the
@%:@ global @S|@as_val. Take advantage of shells that can avoid forks. The arguments
@%:@ must be portable across @S|@(()) and expr.
if (eval "test \$(( 1 + 1 )) = 2") 2>/dev/null
then :
  eval 'as_fn_arith ()
  {
    as_val=$(( $* ))
  }'
else $as_nop
  as_fn_arith ()
  {
    as_val=`expr "$@" || test $? -eq 1`
  }
fi # as_fn_arith


if expr a : '\(a\)' >/dev/null 2>&1 &&
   test "X`expr 00001 : '.*\(...\)'`" = X001; then
  as_expr=expr
else
  as_expr=false
fi

if (basename -- /) >/dev/null 2>&1 && test "X`basename -- / 2>&1`" = "X/"; then
  as_basename=basename
else
  as_basename=false
fi

if (as_dir=`dirname -- /` && test "X$as_dir" = X/) >/dev/null 2>&1; then
  as_dirname=dirname
else
  as_dirname=false
fi

as_me=`$as_basename -- "$0" ||
$as_expr X/"$0" : '.*/\([^/][^/]*\)/*$' \| \
	 X"$0" : 'X\(//\)$' \| \
	 X"$0" : 'X\(/\)' \| . 2>/dev/null ||
printf "%s\n" X/"$0" |
    sed '/^.*\/\([^/][^/]*\)\/*$/{
	    s//\1/
	    q
	  }
	  /^X\/\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\/\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`

# Avoid depending upon Character Ranges.
as_cr_letters='abcdefghijklmnopqrstuvwxyz'
as_cr_LETTERS='ABCDEFGHIJKLMNOPQRSTUVWXYZ'
as_cr_Letters=$as_cr_letters$as_cr_LETTERS
as_cr_digits='0123456789'
as_cr_alnum=$as_cr_Letters$as_cr_digits


# Determine whether it's possible to make 'echo' print without a newline.
# These variables are no longer used directly by Autoconf, but are AC_SUBSTed
# for compatibility with existing Makefiles.
ECHO_C= ECHO_N= ECHO_T=
case `echo -n x` in @%:@(((((
-n*)
  case `echo 'xy\c'` in
  *c*) ECHO_T='	';;	# ECHO_T is single tab character.
  xy)  ECHO_C='\c';;
  *)   echo `echo ksh88 bug on AIX 6.1` > /dev/null
       ECHO_T='	';;
  esac;;
*)
  ECHO_N='-n';;
esac

# For backward compatibility with old third-party macros, we provide
# the shell variables $as_echo and $as_echo_n.  New code should use
# AS_ECHO(["message"]) and AS_ECHO_N(["message"]), respectively.
as_@&t@echo='printf %s\n'
as_@&t@echo_n='printf %s'

rm -f conf$$ conf$$.exe conf$$.file
if test -d conf$$.dir; then
  rm -f conf$$.dir/conf$$.file
else
  rm -f conf$$.dir
  mkdir conf$$.dir 2>/dev/null
fi
if (echo >conf$$.file) 2>/dev/null; then
  if ln -s conf$$.file conf$$ 2>/dev/null; then
    as_ln_s='ln -s'
    # ... but there are two gotchas:
    # 1) On MSYS, both `ln -s file dir' and `ln file dir' fail.
    # 2) DJGPP < 2.04 has no symlinks; `ln -s' creates a wrapper executable.
    # In both cases, we have to default to `cp -pR'.
    ln -s conf$$.file conf$$.dir 2>/dev/null && test ! -f conf$$.exe ||
      as_ln_s='cp -pR'
  elif ln conf$$.file conf$$ 2>/dev/null; then
    as_ln_s=ln
  else
    as_ln_s='cp -pR'
  fi
else
  as_ln_s='cp -pR'
fi
rm -f conf$$ conf$$.exe conf$$.dir/conf$$.file conf$$.file
rmdir conf$$.dir 2>/dev/null


@%:@ as_fn_mkdir_p
@%:@ -------------
@%:@ Create "@S|@as_dir" as a directory, including parents if necessary.
as_fn_mkdir_p ()
{

  case $as_dir in #(
  -*) as_dir=./$as_dir;;
  esac
  test -d "$as_dir" || eval $as_mkdir_p || {
    as_dirs=
    while :; do
      case $as_dir in #(
      *\'*) as_qdir=`printf "%s\n" "$as_dir" | sed "s/'/'\\\\\\\\''/g"`;; #'(
      *) as_qdir=$as_dir;;
      esac
      as_dirs="'$as_qdir' $as_dirs"
      as_dir=`$as_dirname -- "$as_dir" ||
$as_expr X"$as_dir" : 'X\(.*[^/]\)//*[^/][^/]*/*$' \| \
	 X"$as_dir" : 'X\(//\)[^/]' \| \
	 X"$as_dir" : 'X\(//\)$' \| \
	 X"$as_dir" : 'X\(/\)' \| . 2>/dev/null ||
printf "%s\n" X"$as_dir" |
    sed '/^X\(.*[^/]\)\/\/*[^/][^/]*\/*$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)[^/].*/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`
      test -d "$as_dir" && break
    done
    test -z "$as_dirs" || eval "mkdir $as_dirs"
  } || test -d "$as_dir" || as_fn_error $? "cannot create directory $as_dir"


} @%:@ as_fn_mkdir_p
if mkdir -p . 2>/dev/null; then
  as_mkdir_p='mkdir -p "$as_dir"'
else
  test -d ./-p && rmdir ./-p
  as_mkdir_p=false
fi


@%:@ as_fn_executable_p FILE
@%:@ -----------------------
@%:@ Test if FILE is an executable regular file.
as_fn_executable_p ()
{
  test -f "$1" && test -x "$1"
} @%:@ as_fn_executable_p
as_test_x='test -x'
as_executable_p=as_fn_executable_p

# Sed expression to map a string onto a valid CPP name.
as_tr_cpp="eval sed 'y%*$as_cr_letters%P$as_cr_LETTERS%;s%[^_$as_cr_alnum]%_%g'"

# Sed expression to map a string onto a valid variable name.
as_tr_sh="eval sed 'y%*+%pp%;s%[^_$as_cr_alnum]%_%g'"


exec 6>&1
## ----------------------------------- ##
## Main body of $CONFIG_STATUS script. ##
## ----------------------------------- ##
_ASEOF
test $as_write_fail = 0 && chmod +x $CONFIG_STATUS || ac_write_fail=1

cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
# Save the log message, to keep $0 and so on meaningful, and to
# report actual input values of CONFIG_FILES etc. instead of their
# values after options handling.
ac_log="
This file was extended by pcap $as_me 1.11.0-PRE-GIT, which was
generated by GNU Autoconf 2.71.  Invocation command line was

  CONFIG_FILES    = $CONFIG_FILES
  CONFIG_HEADERS  = $CONFIG_HEADERS
  CONFIG_LINKS    = $CONFIG_LINKS
  CONFIG_COMMANDS = $CONFIG_COMMANDS
  $ $0 $@

on `(hostname || uname -n) 2>/dev/null | sed 1q`
"

_ACEOF

case $ac_config_files in *"
"*) set x $ac_config_files; shift; ac_config_files=$*;;
esac

case $ac_config_headers in *"
"*) set x $ac_config_headers; shift; ac_config_headers=$*;;
esac


cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
# Files that config.status was made for.
config_files="$ac_config_files"
config_headers="$ac_config_headers"
config_commands="$ac_config_commands"

_ACEOF

cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
ac_cs_usage="\
\`$as_me' instantiates files and other configuration actions
from templates according to the current configuration.  Unless the files
and actions are specified as TAGs, all are instantiated by default.

Usage: $0 [OPTION]... [TAG]...

  -h, --help       print this help, then exit
  -V, --version    print version number and configuration settings, then exit
      --config     print configuration, then exit
  -q, --quiet, --silent
                   do not print progress messages
  -d, --debug      don't remove temporary files
      --recheck    update $as_me by reconfiguring in the same conditions
      --file=FILE[:TEMPLATE] 
                   instantiate the configuration file FILE
      --header=FILE[:TEMPLATE] 
                   instantiate the configuration header FILE

Configuration files:
$config_files

Configuration headers:
$config_headers

Configuration commands:
$config_commands

Report bugs to <https://github.com/the-tcpdump-group/libpcap/issues>.
pcap home page: <https://www.tcpdump.org/>."

_ACEOF
ac_cs_config=`printf "%s\n" "$ac_configure_args" | sed "$ac_safe_unquote"`
ac_cs_config_escaped=`printf "%s\n" "$ac_cs_config" | sed "s/^ //; s/'/'\\\\\\\\''/g"`
cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
ac_cs_config='$ac_cs_config_escaped'
ac_cs_version="\\
pcap config.status 1.11.0-PRE-GIT
configured by $0, generated by GNU Autoconf 2.71,
  with options \\"\$ac_cs_config\\"

Copyright (C) 2021 Free Software Foundation, Inc.
This config.status script is free software; the Free Software Foundation
gives unlimited permission to copy, distribute and modify it."

ac_pwd='$ac_pwd'
srcdir='$srcdir'
INSTALL='$INSTALL'
test -n "\$AWK" || AWK=awk
_ACEOF

cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
# The default lists apply if the user does not specify any file.
ac_need_defaults=:
while test $# != 0
do
  case $1 in
  --*=?*)
    ac_option=`expr "X$1" : 'X\([^=]*\)='`
    ac_optarg=`expr "X$1" : 'X[^=]*=\(.*\)'`
    ac_shift=:
    ;;
  --*=)
    ac_option=`expr "X$1" : 'X\([^=]*\)='`
    ac_optarg=
    ac_shift=:
    ;;
  *)
    ac_option=$1
    ac_optarg=$2
    ac_shift=shift
    ;;
  esac

  case $ac_option in
  # Handling of the options.
  -recheck | --recheck | --rechec | --reche | --rech | --rec | --re | --r)
    ac_cs_recheck=: ;;
  --version | --versio | --versi | --vers | --ver | --ve | --v | -V )
    printf "%s\n" "$ac_cs_version"; exit ;;
  --config | --confi | --conf | --con | --co | --c )
    printf "%s\n" "$ac_cs_config"; exit ;;
  --debug | --debu | --deb | --de | --d | -d )
    debug=: ;;
  --file | --fil | --fi | --f )
    $ac_shift
    case $ac_optarg in
    *\'*) ac_optarg=`printf "%s\n" "$ac_optarg" | sed "s/'/'\\\\\\\\''/g"` ;;
    '') as_fn_error $? "missing file argument" ;;
    esac
    as_fn_append CONFIG_FILES " '$ac_optarg'"
    ac_need_defaults=false;;
  --header | --heade | --head | --hea )
    $ac_shift
    case $ac_optarg in
    *\'*) ac_optarg=`printf "%s\n" "$ac_optarg" | sed "s/'/'\\\\\\\\''/g"` ;;
    esac
    as_fn_append CONFIG_HEADERS " '$ac_optarg'"
    ac_need_defaults=false;;
  --he | --h)
    # Conflict between --help and --header
    as_fn_error $? "ambiguous option: \`$1'
Try \`$0 --help' for more information.";;
  --help | --hel | -h )
    printf "%s\n" "$ac_cs_usage"; exit ;;
  -q | -quiet | --quiet | --quie | --qui | --qu | --q \
  | -silent | --silent | --silen | --sile | --sil | --si | --s)
    ac_cs_silent=: ;;

  # This is an error.
  -*) as_fn_error $? "unrecognized option: \`$1'
Try \`$0 --help' for more information." ;;

  *) as_fn_append ac_config_targets " $1"
     ac_need_defaults=false ;;

  esac
  shift
done

ac_configure_extra_args=

if $ac_cs_silent; then
  exec 6>/dev/null
  ac_configure_extra_args="$ac_configure_extra_args --silent"
fi

_ACEOF
cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
if \$ac_cs_recheck; then
  set X $SHELL '$0' $ac_configure_args \$ac_configure_extra_args --no-create --no-recursion
  shift
  \printf "%s\n" "running CONFIG_SHELL=$SHELL \$*" >&6
  CONFIG_SHELL='$SHELL'
  export CONFIG_SHELL
  exec "\$@"
fi

_ACEOF
cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
exec 5>>config.log
{
  echo
  sed 'h;s/./-/g;s/^.../@%:@@%:@ /;s/...$/ @%:@@%:@/;p;x;p;x' <<_ASBOX
@%:@@%:@ Running $as_me. @%:@@%:@
_ASBOX
  printf "%s\n" "$ac_log"
} >&5

_ACEOF
cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
_ACEOF

cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1

# Handling of arguments.
for ac_config_target in $ac_config_targets
do
  case $ac_config_target in
    "config.h") CONFIG_HEADERS="$CONFIG_HEADERS config.h" ;;
    ".devel") CONFIG_COMMANDS="$CONFIG_COMMANDS .devel" ;;
    "Makefile") CONFIG_FILES="$CONFIG_FILES Makefile" ;;
    "grammar.y") CONFIG_FILES="$CONFIG_FILES grammar.y" ;;
    "pcap-filter.manmisc") CONFIG_FILES="$CONFIG_FILES pcap-filter.manmisc" ;;
    "pcap-linktype.manmisc") CONFIG_FILES="$CONFIG_FILES pcap-linktype.manmisc" ;;
    "pcap-tstamp.manmisc") CONFIG_FILES="$CONFIG_FILES pcap-tstamp.manmisc" ;;
    "cbpf-savefile.manfile") CONFIG_FILES="$CONFIG_FILES cbpf-savefile.manfile" ;;
    "pcap-savefile.manfile") CONFIG_FILES="$CONFIG_FILES pcap-savefile.manfile" ;;
    "pcap.3pcap") CONFIG_FILES="$CONFIG_FILES pcap.3pcap" ;;
    "pcap_compile.3pcap") CONFIG_FILES="$CONFIG_FILES pcap_compile.3pcap" ;;
    "pcap_datalink.3pcap") CONFIG_FILES="$CONFIG_FILES pcap_datalink.3pcap" ;;
    "pcap_dump_open.3pcap") CONFIG_FILES="$CONFIG_FILES pcap_dump_open.3pcap" ;;
    "pcap_get_tstamp_precision.3pcap") CONFIG_FILES="$CONFIG_FILES pcap_get_tstamp_precision.3pcap" ;;
    "pcap_list_datalinks.3pcap") CONFIG_FILES="$CONFIG_FILES pcap_list_datalinks.3pcap" ;;
    "pcap_list_tstamp_types.3pcap") CONFIG_FILES="$CONFIG_FILES pcap_list_tstamp_types.3pcap" ;;
    "pcap_open_dead.3pcap") CONFIG_FILES="$CONFIG_FILES pcap_open_dead.3pcap" ;;
    "pcap_open_offline.3pcap") CONFIG_FILES="$CONFIG_FILES pcap_open_offline.3pcap" ;;
    "pcap_set_immediate_mode.3pcap") CONFIG_FILES="$CONFIG_FILES pcap_set_immediate_mode.3pcap" ;;
    "pcap_set_tstamp_precision.3pcap") CONFIG_FILES="$CONFIG_FILES pcap_set_tstamp_precision.3pcap" ;;
    "pcap_set_tstamp_type.3pcap") CONFIG_FILES="$CONFIG_FILES pcap_set_tstamp_type.3pcap" ;;
    "rpcapd/Makefile") CONFIG_FILES="$CONFIG_FILES rpcapd/Makefile" ;;
    "rpcapd/rpcapd.manadmin") CONFIG_FILES="$CONFIG_FILES rpcapd/rpcapd.manadmin" ;;
    "rpcapd/rpcapd-config.manfile") CONFIG_FILES="$CONFIG_FILES rpcapd/rpcapd-config.manfile" ;;
    "testprogs/Makefile") CONFIG_FILES="$CONFIG_FILES testprogs/Makefile" ;;

  *) as_fn_error $? "invalid argument: \`$ac_config_target'" "$LINENO" 5;;
  esac
done


# If the user did not use the arguments to specify the items to instantiate,
# then the envvar interface is used.  Set only those that are not.
# We use the long form for the default assignment because of an extremely
# bizarre bug on SunOS 4.1.3.
if $ac_need_defaults; then
  test ${CONFIG_FILES+y} || CONFIG_FILES=$config_files
  test ${CONFIG_HEADERS+y} || CONFIG_HEADERS=$config_headers
  test ${CONFIG_COMMANDS+y} || CONFIG_COMMANDS=$config_commands
fi

# Have a temporary directory for convenience.  Make it in the build tree
# simply because there is no reason against having it here, and in addition,
# creating and moving files from /tmp can sometimes cause problems.
# Hook for its removal unless debugging.
# Note that there is a small window in which the directory will not be cleaned:
# after its creation but before its name has been assigned to `$tmp'.
$debug ||
{
  tmp= ac_tmp=
  trap 'exit_status=$?
  : "${ac_tmp:=$tmp}"
  { test ! -d "$ac_tmp" || rm -fr "$ac_tmp"; } && exit $exit_status
' 0
  trap 'as_fn_exit 1' 1 2 13 15
}
# Create a (secure) tmp directory for tmp files.

{
  tmp=`(umask 077 && mktemp -d "./confXXXXXX") 2>/dev/null` &&
  test -d "$tmp"
}  ||
{
  tmp=./conf$$-$RANDOM
  (umask 077 && mkdir "$tmp")
} || as_fn_error $? "cannot create a temporary directory in ." "$LINENO" 5
ac_tmp=$tmp

# Set up the scripts for CONFIG_FILES section.
# No need to generate them if there are no CONFIG_FILES.
# This happens for instance with `./config.status config.h'.
if test -n "$CONFIG_FILES"; then


ac_cr=`echo X | tr X '\015'`
# On cygwin, bash can eat \r inside `` if the user requested igncr.
# But we know of no other shell where ac_cr would be empty at this
# point, so we can use a bashism as a fallback.
if test "x$ac_cr" = x; then
  eval ac_cr=\$\'\\r\'
fi
ac_cs_awk_cr=`$AWK 'BEGIN { print "a\rb" }' </dev/null 2>/dev/null`
if test "$ac_cs_awk_cr" = "a${ac_cr}b"; then
  ac_cs_awk_cr='\\r'
else
  ac_cs_awk_cr=$ac_cr
fi

echo 'BEGIN {' >"$ac_tmp/subs1.awk" &&
_ACEOF


{
  echo "cat >conf$$subs.awk <<_ACEOF" &&
  echo "$ac_subst_vars" | sed 's/.*/&!$&$ac_delim/' &&
  echo "_ACEOF"
} >conf$$subs.sh ||
  as_fn_error $? "could not make $CONFIG_STATUS" "$LINENO" 5
ac_delim_num=`echo "$ac_subst_vars" | grep -c '^'`
ac_delim='%!_!# '
for ac_last_try in false false false false false :; do
  . ./conf$$subs.sh ||
    as_fn_error $? "could not make $CONFIG_STATUS" "$LINENO" 5

  ac_delim_n=`sed -n "s/.*$ac_delim\$/X/p" conf$$subs.awk | grep -c X`
  if test $ac_delim_n = $ac_delim_num; then
    break
  elif $ac_last_try; then
    as_fn_error $? "could not make $CONFIG_STATUS" "$LINENO" 5
  else
    ac_delim="$ac_delim!$ac_delim _$ac_delim!! "
  fi
done
rm -f conf$$subs.sh

cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
cat >>"\$ac_tmp/subs1.awk" <<\\_ACAWK &&
_ACEOF
sed -n '
h
s/^/S["/; s/!.*/"]=/
p
g
s/^[^!]*!//
:repl
t repl
s/'"$ac_delim"'$//
t delim
:nl
h
s/\(.\{148\}\)..*/\1/
t more1
s/["\\]/\\&/g; s/^/"/; s/$/\\n"\\/
p
n
b repl
:more1
s/["\\]/\\&/g; s/^/"/; s/$/"\\/
p
g
s/.\{148\}//
t nl
:delim
h
s/\(.\{148\}\)..*/\1/
t more2
s/["\\]/\\&/g; s/^/"/; s/$/"/
p
b
:more2
s/["\\]/\\&/g; s/^/"/; s/$/"\\/
p
g
s/.\{148\}//
t delim
' <conf$$subs.awk | sed '
/^[^""]/{
  N
  s/\n//
}
' >>$CONFIG_STATUS || ac_write_fail=1
rm -f conf$$subs.awk
cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
_ACAWK
cat >>"\$ac_tmp/subs1.awk" <<_ACAWK &&
  for (key in S) S_is_set[key] = 1
  FS = ""

}
{
  line = $ 0
  nfields = split(line, field, "@")
  substed = 0
  len = length(field[1])
  for (i = 2; i < nfields; i++) {
    key = field[i]
    keylen = length(key)
    if (S_is_set[key]) {
      value = S[key]
      line = substr(line, 1, len) "" value "" substr(line, len + keylen + 3)
      len += length(value) + length(field[++i])
      substed = 1
    } else
      len += 1 + keylen
  }

  print line
}

_ACAWK
_ACEOF
cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
if sed "s/$ac_cr//" < /dev/null > /dev/null 2>&1; then
  sed "s/$ac_cr\$//; s/$ac_cr/$ac_cs_awk_cr/g"
else
  cat
fi < "$ac_tmp/subs1.awk" > "$ac_tmp/subs.awk" \
  || as_fn_error $? "could not setup config files machinery" "$LINENO" 5
_ACEOF

# VPATH may cause trouble with some makes, so we remove sole $(srcdir),
# ${srcdir} and @srcdir@ entries from VPATH if srcdir is ".", strip leading and
# trailing colons and then remove the whole line if VPATH becomes empty
# (actually we leave an empty line to preserve line numbers).
if test "x$srcdir" = x.; then
  ac_vpsub='/^[	 ]*VPATH[	 ]*=[	 ]*/{
h
s///
s/^/:/
s/[	 ]*$/:/
s/:\$(srcdir):/:/g
s/:\${srcdir}:/:/g
s/:@srcdir@:/:/g
s/^:*//
s/:*$//
x
s/\(=[	 ]*\).*/\1/
G
s/\n//
s/^[^=]*=[	 ]*$//
}'
fi

cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
fi # test -n "$CONFIG_FILES"

# Set up the scripts for CONFIG_HEADERS section.
# No need to generate them if there are no CONFIG_HEADERS.
# This happens for instance with `./config.status Makefile'.
if test -n "$CONFIG_HEADERS"; then
cat >"$ac_tmp/defines.awk" <<\_ACAWK ||
BEGIN {
_ACEOF

# Transform confdefs.h into an awk script `defines.awk', embedded as
# here-document in config.status, that substitutes the proper values into
# config.h.in to produce config.h.

# Create a delimiter string that does not exist in confdefs.h, to ease
# handling of long lines.
ac_delim='%!_!# '
for ac_last_try in false false :; do
  ac_tt=`sed -n "/$ac_delim/p" confdefs.h`
  if test -z "$ac_tt"; then
    break
  elif $ac_last_try; then
    as_fn_error $? "could not make $CONFIG_HEADERS" "$LINENO" 5
  else
    ac_delim="$ac_delim!$ac_delim _$ac_delim!! "
  fi
done

# For the awk script, D is an array of macro values keyed by name,
# likewise P contains macro parameters if any.  Preserve backslash
# newline sequences.

ac_word_re=[_$as_cr_Letters][_$as_cr_alnum]*
sed -n '
s/.\{148\}/&'"$ac_delim"'/g
t rset
:rset
s/^[	 ]*#[	 ]*define[	 ][	 ]*/ /
t def
d
:def
s/\\$//
t bsnl
s/["\\]/\\&/g
s/^ \('"$ac_word_re"'\)\(([^()]*)\)[	 ]*\(.*\)/P["\1"]="\2"\
D["\1"]=" \3"/p
s/^ \('"$ac_word_re"'\)[	 ]*\(.*\)/D["\1"]=" \2"/p
d
:bsnl
s/["\\]/\\&/g
s/^ \('"$ac_word_re"'\)\(([^()]*)\)[	 ]*\(.*\)/P["\1"]="\2"\
D["\1"]=" \3\\\\\\n"\\/p
t cont
s/^ \('"$ac_word_re"'\)[	 ]*\(.*\)/D["\1"]=" \2\\\\\\n"\\/p
t cont
d
:cont
n
s/.\{148\}/&'"$ac_delim"'/g
t clear
:clear
s/\\$//
t bsnlc
s/["\\]/\\&/g; s/^/"/; s/$/"/p
d
:bsnlc
s/["\\]/\\&/g; s/^/"/; s/$/\\\\\\n"\\/p
b cont
' <confdefs.h | sed '
s/'"$ac_delim"'/"\\\
"/g' >>$CONFIG_STATUS || ac_write_fail=1

cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
  for (key in D) D_is_set[key] = 1
  FS = ""
}
/^[\t ]*#[\t ]*(define|undef)[\t ]+$ac_word_re([\t (]|\$)/ {
  line = \$ 0
  split(line, arg, " ")
  if (arg[1] == "#") {
    defundef = arg[2]
    mac1 = arg[3]
  } else {
    defundef = substr(arg[1], 2)
    mac1 = arg[2]
  }
  split(mac1, mac2, "(") #)
  macro = mac2[1]
  prefix = substr(line, 1, index(line, defundef) - 1)
  if (D_is_set[macro]) {
    # Preserve the white space surrounding the "#".
    print prefix "define", macro P[macro] D[macro]
    next
  } else {
    # Replace #undef with comments.  This is necessary, for example,
    # in the case of _POSIX_SOURCE, which is predefined and required
    # on some systems where configure will not decide to define it.
    if (defundef == "undef") {
      print "/*", prefix defundef, macro, "*/"
      next
    }
  }
}
{ print }
_ACAWK
_ACEOF
cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
  as_fn_error $? "could not setup config headers machinery" "$LINENO" 5
fi # test -n "$CONFIG_HEADERS"


eval set X "  :F $CONFIG_FILES  :H $CONFIG_HEADERS    :C $CONFIG_COMMANDS"
shift
for ac_tag
do
  case $ac_tag in
  :[FHLC]) ac_mode=$ac_tag; continue;;
  esac
  case $ac_mode$ac_tag in
  :[FHL]*:*);;
  :L* | :C*:*) as_fn_error $? "invalid tag \`$ac_tag'" "$LINENO" 5;;
  :[FH]-) ac_tag=-:-;;
  :[FH]*) ac_tag=$ac_tag:$ac_tag.in;;
  esac
  ac_save_IFS=$IFS
  IFS=:
  set x $ac_tag
  IFS=$ac_save_IFS
  shift
  ac_file=$1
  shift

  case $ac_mode in
  :L) ac_source=$1;;
  :[FH])
    ac_file_inputs=
    for ac_f
    do
      case $ac_f in
      -) ac_f="$ac_tmp/stdin";;
      *) # Look for the file first in the build tree, then in the source tree
	 # (if the path is not absolute).  The absolute path cannot be DOS-style,
	 # because $ac_f cannot contain `:'.
	 test -f "$ac_f" ||
	   case $ac_f in
	   [\\/$]*) false;;
	   *) test -f "$srcdir/$ac_f" && ac_f="$srcdir/$ac_f";;
	   esac ||
	   as_fn_error 1 "cannot find input file: \`$ac_f'" "$LINENO" 5;;
      esac
      case $ac_f in *\'*) ac_f=`printf "%s\n" "$ac_f" | sed "s/'/'\\\\\\\\''/g"`;; esac
      as_fn_append ac_file_inputs " '$ac_f'"
    done

    # Let's still pretend it is `configure' which instantiates (i.e., don't
    # use $as_me), people would be surprised to read:
    #    /* config.h.  Generated by config.status.  */
    configure_input='Generated from '`
	  printf "%s\n" "$*" | sed 's|^[^:]*/||;s|:[^:]*/|, |g'
	`' by configure.'
    if test x"$ac_file" != x-; then
      configure_input="$ac_file.  $configure_input"
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: creating $ac_file" >&5
printf "%s\n" "$as_me: creating $ac_file" >&6;}
    fi
    # Neutralize special characters interpreted by sed in replacement strings.
    case $configure_input in #(
    *\&* | *\|* | *\\* )
       ac_sed_conf_input=`printf "%s\n" "$configure_input" |
       sed 's/[\\\\&|]/\\\\&/g'`;; #(
    *) ac_sed_conf_input=$configure_input;;
    esac

    case $ac_tag in
    *:-:* | *:-) cat >"$ac_tmp/stdin" \
      || as_fn_error $? "could not create $ac_file" "$LINENO" 5 ;;
    esac
    ;;
  esac

  ac_dir=`$as_dirname -- "$ac_file" ||
$as_expr X"$ac_file" : 'X\(.*[^/]\)//*[^/][^/]*/*$' \| \
	 X"$ac_file" : 'X\(//\)[^/]' \| \
	 X"$ac_file" : 'X\(//\)$' \| \
	 X"$ac_file" : 'X\(/\)' \| . 2>/dev/null ||
printf "%s\n" X"$ac_file" |
    sed '/^X\(.*[^/]\)\/\/*[^/][^/]*\/*$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)[^/].*/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`
  as_dir="$ac_dir"; as_fn_mkdir_p
  ac_builddir=.

case "$ac_dir" in
.) ac_dir_suffix= ac_top_builddir_sub=. ac_top_build_prefix= ;;
*)
  ac_dir_suffix=/`printf "%s\n" "$ac_dir" | sed 's|^\.[\\/]||'`
  # A ".." for each directory in $ac_dir_suffix.
  ac_top_builddir_sub=`printf "%s\n" "$ac_dir_suffix" | sed 's|/[^\\/]*|/..|g;s|/||'`
  case $ac_top_builddir_sub in
  "") ac_top_builddir_sub=. ac_top_build_prefix= ;;
  *)  ac_top_build_prefix=$ac_top_builddir_sub/ ;;
  esac ;;
esac
ac_abs_top_builddir=$ac_pwd
ac_abs_builddir=$ac_pwd$ac_dir_suffix
# for backward compatibility:
ac_top_builddir=$ac_top_build_prefix

case $srcdir in
  .)  # We are building in place.
    ac_srcdir=.
    ac_top_srcdir=$ac_top_builddir_sub
    ac_abs_top_srcdir=$ac_pwd ;;
  [\\/]* | ?:[\\/]* )  # Absolute name.
    ac_srcdir=$srcdir$ac_dir_suffix;
    ac_top_srcdir=$srcdir
    ac_abs_top_srcdir=$srcdir ;;
  *) # Relative name.
    ac_srcdir=$ac_top_build_prefix$srcdir$ac_dir_suffix
    ac_top_srcdir=$ac_top_build_prefix$srcdir
    ac_abs_top_srcdir=$ac_pwd/$srcdir ;;
esac
ac_abs_srcdir=$ac_abs_top_srcdir$ac_dir_suffix


  case $ac_mode in
  :F)
  #
  # CONFIG_FILE
  #

  case $INSTALL in
  [\\/$]* | ?:[\\/]* ) ac_INSTALL=$INSTALL ;;
  *) ac_INSTALL=$ac_top_build_prefix$INSTALL ;;
  esac
_ACEOF

cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
# If the template does not know about datarootdir, expand it.
# FIXME: This hack should be removed a few years after 2.60.
ac_datarootdir_hack=; ac_datarootdir_seen=
ac_sed_dataroot='
/datarootdir/ {
  p
  q
}
/@datadir@/p
/@docdir@/p
/@infodir@/p
/@localedir@/p
/@mandir@/p'
case `eval "sed -n \"\$ac_sed_dataroot\" $ac_file_inputs"` in
*datarootdir*) ac_datarootdir_seen=yes;;
*@datadir@*|*@docdir@*|*@infodir@*|*@localedir@*|*@mandir@*)
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: $ac_file_inputs seems to ignore the --datarootdir setting" >&5
printf "%s\n" "$as_me: WARNING: $ac_file_inputs seems to ignore the --datarootdir setting" >&2;}
_ACEOF
cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
  ac_datarootdir_hack='
  s&@datadir@&$datadir&g
  s&@docdir@&$docdir&g
  s&@infodir@&$infodir&g
  s&@localedir@&$localedir&g
  s&@mandir@&$mandir&g
  s&\\\${datarootdir}&$datarootdir&g' ;;
esac
_ACEOF

# Neutralize VPATH when `$srcdir' = `.'.
# Shell code in configure.ac might set extrasub.
# FIXME: do we really want to maintain this feature?
cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
ac_sed_extra="$ac_vpsub
$extrasub
_ACEOF
cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
:t
/@[a-zA-Z_][a-zA-Z_0-9]*@/!b
s|@configure_input@|$ac_sed_conf_input|;t t
s&@top_builddir@&$ac_top_builddir_sub&;t t
s&@top_build_prefix@&$ac_top_build_prefix&;t t
s&@srcdir@&$ac_srcdir&;t t
s&@abs_srcdir@&$ac_abs_srcdir&;t t
s&@top_srcdir@&$ac_top_srcdir&;t t
s&@abs_top_srcdir@&$ac_abs_top_srcdir&;t t
s&@builddir@&$ac_builddir&;t t
s&@abs_builddir@&$ac_abs_builddir&;t t
s&@abs_top_builddir@&$ac_abs_top_builddir&;t t
s&@INSTALL@&$ac_INSTALL&;t t
$ac_datarootdir_hack
"
eval sed \"\$ac_sed_extra\" "$ac_file_inputs" | $AWK -f "$ac_tmp/subs.awk" \
  >$ac_tmp/out || as_fn_error $? "could not create $ac_file" "$LINENO" 5

test -z "$ac_datarootdir_hack$ac_datarootdir_seen" &&
  { ac_out=`sed -n '/\${datarootdir}/p' "$ac_tmp/out"`; test -n "$ac_out"; } &&
  { ac_out=`sed -n '/^[	 ]*datarootdir[	 ]*:*=/p' \
      "$ac_tmp/out"`; test -z "$ac_out"; } &&
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: $ac_file contains a reference to the variable \`datarootdir'
which seems to be undefined.  Please make sure it is defined" >&5
printf "%s\n" "$as_me: WARNING: $ac_file contains a reference to the variable \`datarootdir'
which seems to be undefined.  Please make sure it is defined" >&2;}

  rm -f "$ac_tmp/stdin"
  case $ac_file in
  -) cat "$ac_tmp/out" && rm -f "$ac_tmp/out";;
  *) rm -f "$ac_file" && mv "$ac_tmp/out" "$ac_file";;
  esac \
  || as_fn_error $? "could not create $ac_file" "$LINENO" 5
 ;;
  :H)
  #
  # CONFIG_HEADER
  #
  if test x"$ac_file" != x-; then
    {
      printf "%s\n" "/* $configure_input  */" >&1 \
      && eval '$AWK -f "$ac_tmp/defines.awk"' "$ac_file_inputs"
    } >"$ac_tmp/config.h" \
      || as_fn_error $? "could not create $ac_file" "$LINENO" 5
    if diff "$ac_file" "$ac_tmp/config.h" >/dev/null 2>&1; then
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: $ac_file is unchanged" >&5
printf "%s\n" "$as_me: $ac_file is unchanged" >&6;}
    else
      rm -f "$ac_file"
      mv "$ac_tmp/config.h" "$ac_file" \
	|| as_fn_error $? "could not create $ac_file" "$LINENO" 5
    fi
  else
    printf "%s\n" "/* $configure_input  */" >&1 \
      && eval '$AWK -f "$ac_tmp/defines.awk"' "$ac_file_inputs" \
      || as_fn_error $? "could not create -" "$LINENO" 5
  fi
 ;;
  
  :C)  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: executing $ac_file commands" >&5
printf "%s\n" "$as_me: executing $ac_file commands" >&6;}
 ;;
  esac


  case $ac_file$ac_mode in
    ".devel":C) if test -f .devel; then
	echo timestamp > stamp-h
	cat $srcdir/Makefile-devel-adds >> Makefile
	make depend || exit 1
fi ;;

  esac
done # for ac_tag


as_fn_exit 0
_ACEOF
ac_clean_files=$ac_clean_files_save

test $ac_write_fail = 0 ||
  as_fn_error $? "write failure creating $CONFIG_STATUS" "$LINENO" 5


# configure is writing to config.log, and then calls config.status.
# config.status does its own redirection, appending to config.log.
# Unfortunately, on DOS this fails, as config.log is still kept open
# by configure, so config.status won't be able to write to it; its
# output is simply discarded.  So we exec the FD to /dev/null,
# effectively closing config.log, so it can be properly (re)opened and
# appended to by config.status.  When coming back to configure, we
# need to make the FD available again.
if test "$no_create" != yes; then
  ac_cs_success=:
  ac_config_status_args=
  test "$silent" = yes &&
    ac_config_status_args="$ac_config_status_args --quiet"
  exec 5>/dev/null
  $SHELL $CONFIG_STATUS $ac_config_status_args || ac_cs_success=false
  exec 5>>config.log
  # Use ||, not &&, to avoid exiting from the if with $? = 1, which
  # would make configure fail if this is the last instruction.
  $ac_cs_success || as_fn_exit 1
fi
if test -n "$ac_unrecognized_opts" && test "$enable_option_checking" != no; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: unrecognized options: $ac_unrecognized_opts" >&5
printf "%s\n" "$as_me: WARNING: unrecognized options: $ac_unrecognized_opts" >&2;}
fi

exit 0
