/*
 * Copyright (c) 1993, 1994, 1995, 1996, 1997, 1998
 *	The Regents of the University of California.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *	This product includes software developed by the Computer Systems
 *	Engineering Group at Lawrence Berkeley Laboratory.
 * 4. Neither the name of the University nor of the Laboratory may be used
 *    to endorse or promote products derived from this software without
 *    specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 */
#include <time.h>
#include <sys/utsname.h>  // 用于获取内核版本信息

// timerfd支持 (Linux 2.6.25+)
#ifdef __linux__
// 检查内核版本，只在支持的内核上包含timerfd头文件
#include <sys/utsname.h>
#include <unistd.h>
#include <errno.h>

// 尝试包含timerfd头文件，如果不存在则定义必要的常量
#if defined(__has_include)
  #if __has_include(<sys/timerfd.h>)
    #include <sys/timerfd.h>
    #define HAVE_TIMERFD_H 1
  #else
    #define HAVE_TIMERFD_H 0
  #endif
#else
  // 对于不支持__has_include的编译器，尝试包含并处理错误
  #ifdef __GLIBC__
    #if __GLIBC__ > 2 || (__GLIBC__ == 2 && __GLIBC_MINOR__ >= 8)
      #include <sys/timerfd.h>
      #define HAVE_TIMERFD_H 1
    #else
      #define HAVE_TIMERFD_H 0
    #endif
  #else
    // 对于非glibc系统，假设不支持
    #define HAVE_TIMERFD_H 0
  #endif
#endif

// 如果没有timerfd支持，定义必要的常量和函数声明
#if !HAVE_TIMERFD_H
#define TFD_CLOEXEC 02000000
#define CLOCK_REALTIME 0
#define CLOCK_MONOTONIC 1

// 声明timerfd函数（如果系统不支持，这些函数将返回错误）
static inline int timerfd_create(int clockid, int flags) {
    errno = ENOSYS;
    return -1;
}

static inline int timerfd_settime(int fd, int flags, const struct itimerspec *new_value, struct itimerspec *old_value) {
    errno = ENOSYS;
    return -1;
}
#endif

#endif

#ifndef lint
static const char rcsid[] _U_ =
	"@(#) $Header: /tcpdump/master/libpcap/pcap.c,v 1.88.2.8 2005/08/13 22:29:46 hannes Exp $ (LBL)";
#endif

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

#ifdef WIN32
#include <pcap-stdinc.h>
#else /* WIN32 */
#include <sys/types.h>
#endif /* WIN32 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#if !defined(_MSC_VER) && !defined(__BORLANDC__)
#include <unistd.h>
#endif
#include <fcntl.h>
#include <errno.h>

#ifdef HAVE_OS_PROTO_H
#include "os-proto.h"
#endif

#ifdef MSDOS
#include "pcap-dos.h"
#endif

#include "pcap-int.h"

#ifdef HAVE_DAG_API
#include <dagnew.h>
#include <dagapi.h>
#endif

// add 2024
#include "../tcpdump-4.99.3/my.h"
#include <pthread.h>
#include "../agent/top.h"
#include "../sar/sa1.h"
#include <sys/socket.h>
#include <netinet/ether.h>
#include <time.h>
#include <signal.h>

// 高效同步机制 - 替换pthread_barrier避免高CPU使用
typedef struct {
    pthread_mutex_t mutex;
    pthread_cond_t cond;
    int count;
    int waiting;
    int generation;
} efficient_barrier_t;

efficient_barrier_t barrier_a_done;    // 门禁1: A完成工作的标志
efficient_barrier_t barrier_bc_done;   // 门禁2: BC完成工作的标志
static int barriers_initialized = 0;

// POSIX定时器相关函数声明（实现在pcap-linux.c中）
extern int setup_posix_timer();
extern int set_posix_timer(int sec, long nsec);
extern void cancel_posix_timer();
extern void cleanup_posix_timer();
extern int get_timer_fd();
extern int is_timer_initialized();

// 双门禁同步机制函数声明
int init_barriers();
int cleanup_barriers();

// 内核版本自适应定时器函数声明
int get_kernel_version();
int setup_legacy_posix_timer();
int cleanup_legacy_posix_timer();
int setup_timerfd_timer();
int cleanup_timerfd_timer();
int get_timerfd();
int setup_optimal_timer();
int cleanup_optimal_timer();

// 定时器类型枚举
typedef enum {
    TIMER_TYPE_UNKNOWN = 0,
    TIMER_TYPE_LEGACY_POSIX = 1,    // 内核 < 2.6.25
    TIMER_TYPE_TIMERFD = 2          // 内核 >= 2.6.25
} timer_type_t;

// 全局变量
static timer_type_t current_timer_type = TIMER_TYPE_UNKNOWN;
static int timerfd = -1;  // timerfd文件描述符

// #include <sys/epoll.h>
int live_open_old1(pcap_t *, const char *, int, int, char *);
int live_open_new1(pcap_t *, const char *, int, int, char *);
extern int test;
extern int ths;
extern int pcap_read_packet(pcap_t *, pcap_handler, u_char *, fd_set *readfds, struct timeval *tv1, int timer_fd);

volatile int pcap_timeout1; // posix 计时器
timer_t timerid;
extern FILE *g_logfile;
extern void write_log(const char *format, ...);
int pcap_dispatch(pcap_t *p, int cnt, pcap_handler callback, u_char *user)
{

	return p->read_op(p, cnt, callback, user);
}

/*
 * XXX - is this necessary?
 */
int pcap_read(pcap_t *p, int cnt, pcap_handler callback, u_char *users)
{

	return p->read_op(p, cnt, callback, users);
}

static void *alloc_r(void *ptr, size_t num)
{
	void *pv;

	if (!num)
		++num;
	if (!(pv = realloc(ptr, num)))
		perror("alloc_r failed");
	;
	return pv;
} // end: alloc_r

int recreate_fd(pcap_t *handle, const char *device, int promisc, int to_ms, char *ebuf)
{

	int err, live_open_ok = 0;

	if ((err = live_open_new1(handle, device, promisc, to_ms, ebuf)) == 1)
		live_open_ok = 1;
	else if (err == 0)
	{
		/* Non-fatal error; try old way */
		if (live_open_old1(handle, device, promisc, to_ms, ebuf))
			live_open_ok = 1;
	}
	if (!live_open_ok)
	{
		/*
		 * Both methods to open the packet socket failed. Tidy
		 * up and report our failure (ebuf is expected to be
		 * set by the functions above).
		 */

		if (handle->md.device != NULL)
			free(handle->md.device);
		free(handle);
		return -1;  // 返回错误码而不是NULL
	}
	return 0;  // 成功时返回0
}

/*
 * 双门禁同步机制实现
 * 替换sem_wait/sem_post，实现更精确的三线程同步
 */

/*
 * 高效barrier实现 - 避免pthread_barrier的高CPU使用
 */
int efficient_barrier_init(efficient_barrier_t *barrier, int count)
{
	if (pthread_mutex_init(&barrier->mutex, NULL) != 0) {
		return -1;
	}
	if (pthread_cond_init(&barrier->cond, NULL) != 0) {
		pthread_mutex_destroy(&barrier->mutex);
		return -1;
	}
	barrier->count = count;
	barrier->waiting = 0;
	barrier->generation = 0;
	return 0;
}

int efficient_barrier_wait(efficient_barrier_t *barrier)
{
	pthread_mutex_lock(&barrier->mutex);

	int gen = barrier->generation;
	barrier->waiting++;

	if (barrier->waiting == barrier->count) {
		// 最后一个线程到达，唤醒所有等待的线程
		barrier->waiting = 0;
		barrier->generation++;
		pthread_cond_broadcast(&barrier->cond);
		pthread_mutex_unlock(&barrier->mutex);
		return 1; // 类似PTHREAD_BARRIER_SERIAL_THREAD
	} else {
		// 等待其他线程
		while (gen == barrier->generation) {
			pthread_cond_wait(&barrier->cond, &barrier->mutex);
		}
		pthread_mutex_unlock(&barrier->mutex);
		return 0;
	}
}

// 带超时的barrier等待函数
int efficient_barrier_wait_timeout(efficient_barrier_t *barrier, int timeout_seconds)
{
	pthread_mutex_lock(&barrier->mutex);

	int gen = barrier->generation;
	barrier->waiting++;

	if (barrier->waiting == barrier->count) {
		// 最后一个线程到达，唤醒所有等待的线程
		barrier->waiting = 0;
		barrier->generation++;
		pthread_cond_broadcast(&barrier->cond);
		pthread_mutex_unlock(&barrier->mutex);
		return 1; // 类似PTHREAD_BARRIER_SERIAL_THREAD
	} else {
		// 等待其他线程，带超时
		struct timespec timeout;
		clock_gettime(CLOCK_REALTIME, &timeout);
		timeout.tv_sec += timeout_seconds;

		int ret = 0;
		while (gen == barrier->generation) {
			ret = pthread_cond_timedwait(&barrier->cond, &barrier->mutex, &timeout);
			if (ret == ETIMEDOUT) {
				// 超时，减少等待计数并返回错误
				barrier->waiting--;
				write_log("efficient_barrier_wait_timeout: 超时(%d秒)，其他线程可能已退出\n", timeout_seconds);
				pthread_mutex_unlock(&barrier->mutex);
				return -2; // 超时错误
			}
		}
		pthread_mutex_unlock(&barrier->mutex);
		return 0;
	}
}

void efficient_barrier_destroy(efficient_barrier_t *barrier)
{
	pthread_mutex_destroy(&barrier->mutex);
	pthread_cond_destroy(&barrier->cond);
}

/*
 * 初始化双门禁barrier
 * 返回0表示成功，-1表示失败
 */
int init_barriers()
{
	if (barriers_initialized) {
		write_log("init_barriers: barriers已经初始化\n");
		return 0;
	}

	write_log("init_barriers: 初始化高效同步机制\n");

	// 初始化门禁1 (barrier_a_done) - 3个线程参与
	if (efficient_barrier_init(&barrier_a_done, 3) != 0) {
		write_log("init_barriers: 初始化barrier_a_done失败\n");
		return -1;
	}

	// 初始化门禁2 (barrier_bc_done) - 3个线程参与
	if (efficient_barrier_init(&barrier_bc_done, 3) != 0) {
		write_log("init_barriers: 初始化barrier_bc_done失败\n");
		efficient_barrier_destroy(&barrier_a_done);
		return -1;
	}

	barriers_initialized = 1;
	write_log("init_barriers: 高效同步机制初始化成功\n");
	return 0;
}

/*
 * 清理双门禁barrier资源
 * 返回0表示成功，-1表示失败
 */
int cleanup_barriers()
{
	if (!barriers_initialized) {
		write_log("cleanup_barriers: barriers未初始化，无需清理\n");
		return 0;
	}

	write_log("cleanup_barriers: 清理高效同步机制资源\n");

	// 销毁门禁1
	efficient_barrier_destroy(&barrier_a_done);

	// 销毁门禁2
	efficient_barrier_destroy(&barrier_bc_done);

	barriers_initialized = 0;
	write_log("cleanup_barriers: 高效同步机制清理完成\n");
	return 0;
}

void timer_handler(int signum)
{
	pcap_timeout1 = 1;
}

// SA_SIGINFO版本的信号处理函数
void timer_handler_siginfo(int signum, siginfo_t *si, void *uc)
{
	pcap_timeout1 = 1;
}

/*
 * 针对Linux内核小于2.6.25的POSIX定时器实现
 * 在老内核中，POSIX定时器的实现可能存在一些限制和差异
 * 这个函数提供了更稳定和兼容的定时器方案
 */
int setup_legacy_posix_timer()
{
	struct sigevent sev;
	struct itimerspec its;
	struct sigaction sa;

	write_log("setup_legacy_posix_timer: 为Linux内核<2.6.25初始化POSIX定时器\n");

	// 检查是否已经初始化
	static int legacy_timer_initialized = 0;
	if (legacy_timer_initialized) {
		write_log("setup_legacy_posix_timer: 定时器已初始化，跳过\n");
		return 0;
	}

	// 设置信号处理函数 - 针对老内核优化
	memset(&sa, 0, sizeof(sa));
	sa.sa_flags = SA_SIGINFO | SA_RESTART; // 添加SA_RESTART提高稳定性
	sa.sa_sigaction = timer_handler_siginfo;
	sigemptyset(&sa.sa_mask);

	// 阻塞其他实时信号，避免信号竞争
	sigaddset(&sa.sa_mask, SIGRTMIN + 1);
	sigaddset(&sa.sa_mask, SIGRTMIN + 2);

	if (sigaction(SIGRTMIN, &sa, NULL) == -1) {
		write_log("setup_legacy_posix_timer: 设置信号处理失败: %s\n", strerror(errno));
		return -1;
	}

	// 创建定时器 - 针对老内核的兼容性设置
	memset(&sev, 0, sizeof(sev));
	sev.sigev_notify = SIGEV_SIGNAL;
	sev.sigev_signo = SIGRTMIN;
	sev.sigev_value.sival_ptr = &timerid;

	// 在老内核中，某些字段可能需要显式初始化
	sev.sigev_notify_function = NULL;
	sev.sigev_notify_attributes = NULL;

	if (timer_create(CLOCK_REALTIME, &sev, &timerid) == -1) {
		write_log("setup_legacy_posix_timer: timer_create失败: %s\n", strerror(errno));

		// 如果CLOCK_REALTIME失败，尝试CLOCK_MONOTONIC（如果可用）
		if (errno == EINVAL) {
			write_log("setup_legacy_posix_timer: 尝试使用CLOCK_MONOTONIC\n");
			if (timer_create(CLOCK_MONOTONIC, &sev, &timerid) == -1) {
				write_log("setup_legacy_posix_timer: CLOCK_MONOTONIC也失败: %s\n", strerror(errno));
				return -1;
			}
		} else {
			return -1;
		}
	}

	write_log("setup_legacy_posix_timer: timer_create成功，定时器ID: %p\n", (void*)&timerid);

	// 设置定时器参数 - 针对老内核的保守设置
	memset(&its, 0, sizeof(its));
	its.it_value.tv_sec = 1;     // 1秒后触发
	its.it_value.tv_nsec = 0;
	its.it_interval.tv_sec = 0;  // 不重复触发，避免老内核的定时器累积问题
	its.it_interval.tv_nsec = 0;

	if (timer_settime(timerid, 0, &its, NULL) == -1) {
		write_log("setup_legacy_posix_timer: timer_settime失败: %s\n", strerror(errno));
		timer_delete(timerid); // 清理已创建的定时器
		return -1;
	}

	legacy_timer_initialized = 1;
	write_log("setup_legacy_posix_timer: 老内核POSIX定时器初始化成功\n");
	return 0;
}

/*
 * 清理老内核POSIX定时器资源
 */
int cleanup_legacy_posix_timer()
{
	static int cleanup_done = 0;

	if (cleanup_done) {
		write_log("cleanup_legacy_posix_timer: 已清理过，跳过\n");
		return 0;
	}

	write_log("cleanup_legacy_posix_timer: 开始清理老内核定时器资源\n");

	// 取消定时器
	struct itimerspec its;
	memset(&its, 0, sizeof(its));

	if (timer_settime(timerid, 0, &its, NULL) == -1) {
		write_log("cleanup_legacy_posix_timer: 取消定时器失败: %s\n", strerror(errno));
	}

	// 删除定时器
	if (timer_delete(timerid) == -1) {
		write_log("cleanup_legacy_posix_timer: timer_delete失败: %s\n", strerror(errno));
		return -1;
	}

	cleanup_done = 1;
	write_log("cleanup_legacy_posix_timer: 老内核定时器资源清理完成\n");
	return 0;
}

/*
 * 初始化timerfd定时器 (Linux 2.6.25+)
 * 返回0表示成功，-1表示失败
 */
int setup_timerfd_timer()
{
#ifdef __linux__
#if HAVE_TIMERFD_H
	// 检查是否已经初始化
	if (timerfd >= 0) {
		write_log("setup_timerfd_timer: timerfd已经初始化，fd=%d\n", timerfd);
		return 0;
	}

	write_log("setup_timerfd_timer: 初始化timerfd定时器...\n");

	// 创建timerfd
	timerfd = timerfd_create(CLOCK_REALTIME, TFD_CLOEXEC);
	if (timerfd == -1) {
		write_log("setup_timerfd_timer: timerfd_create失败: %s\n", strerror(errno));
		return -1;
	}

	write_log("setup_timerfd_timer: timerfd创建成功，fd=%d\n", timerfd);
	current_timer_type = TIMER_TYPE_TIMERFD;
	return 0;
#else
	write_log("setup_timerfd_timer: 系统不支持timerfd，请使用legacy POSIX定时器\n");
	return -1;
#endif
#else
	write_log("setup_timerfd_timer: 非Linux系统，不支持timerfd\n");
	return -1;
#endif
}

/*
 * 设置timerfd定时器
 * sec: 秒数
 * nsec: 纳秒数
 * 返回0表示成功，-1表示失败
 */
int set_timerfd_timer(int sec, long nsec)
{
#ifdef __linux__
#if HAVE_TIMERFD_H
	if (timerfd < 0) {
		write_log("set_timerfd_timer: timerfd未初始化\n");
		return -1;
	}

	struct itimerspec its;
	memset(&its, 0, sizeof(its));

	its.it_value.tv_sec = sec;
	its.it_value.tv_nsec = nsec;
	its.it_interval.tv_sec = 0;  // 不重复
	its.it_interval.tv_nsec = 0;

	if (timerfd_settime(timerfd, 0, &its, NULL) == -1) {
		write_log("set_timerfd_timer: timerfd_settime失败: %s\n", strerror(errno));
		return -1;
	}

	write_log("set_timerfd_timer: 定时器设置成功，%d秒%ld纳秒后触发\n", sec, nsec);
	return 0;
#else
	write_log("set_timerfd_timer: 系统不支持timerfd\n");
	return -1;
#endif
#else
	write_log("set_timerfd_timer: 非Linux系统，不支持timerfd\n");
	return -1;
#endif
}

/*
 * 获取timerfd文件描述符
 */
int get_timerfd()
{
	return timerfd;
}

/*
 * 清理timerfd定时器资源
 */
int cleanup_timerfd_timer()
{
#ifdef __linux__
#if HAVE_TIMERFD_H
	if (timerfd < 0) {
		write_log("cleanup_timerfd_timer: timerfd未初始化，无需清理\n");
		return 0;
	}

	write_log("cleanup_timerfd_timer: 开始清理timerfd资源，fd=%d\n", timerfd);

	// 取消定时器
	struct itimerspec its;
	memset(&its, 0, sizeof(its));
	if (timerfd_settime(timerfd, 0, &its, NULL) == -1) {
		write_log("cleanup_timerfd_timer: 取消定时器失败: %s\n", strerror(errno));
	}

	// 关闭文件描述符
	if (close(timerfd) == -1) {
		write_log("cleanup_timerfd_timer: 关闭timerfd失败: %s\n", strerror(errno));
		return -1;
	}

	timerfd = -1;
	current_timer_type = TIMER_TYPE_UNKNOWN;
	write_log("cleanup_timerfd_timer: timerfd资源清理完成\n");
	return 0;
#else
	write_log("cleanup_timerfd_timer: 系统不支持timerfd，无需清理\n");
	return 0;
#endif
#else
	write_log("cleanup_timerfd_timer: 非Linux系统，无需清理\n");
	return 0;
#endif
}

/*
 * 获取Linux内核版本
 * 返回值：内核版本号（如2.6.18返回20618）
 * 返回-1表示获取失败
 */
int get_kernel_version()
{
	FILE *fp;
	char version_str[256];
	int major, minor, patch;

	// 读取/proc/version文件
	fp = fopen("/proc/version", "r");
	if (fp == NULL) {
		write_log("get_kernel_version: 无法打开/proc/version: %s\n", strerror(errno));
		return -1;
	}

	if (fgets(version_str, sizeof(version_str), fp) == NULL) {
		write_log("get_kernel_version: 读取/proc/version失败\n");
		fclose(fp);
		return -1;
	}
	fclose(fp);

	// 解析版本号，格式通常是 "Linux version 2.6.18..."
	if (sscanf(version_str, "Linux version %d.%d.%d", &major, &minor, &patch) != 3) {
		write_log("get_kernel_version: 解析版本号失败: %s\n", version_str);
		return -1;
	}

	int version_code = major * 10000 + minor * 100 + patch;
	write_log("get_kernel_version: 检测到内核版本 %d.%d.%d (代码: %d)\n",
			  major, minor, patch, version_code);

	return version_code;
}

/*
 * 根据内核版本自动选择最优的定时器实现
 * 内核 < 2.6.25: 使用legacy POSIX定时器
 * 内核 >= 2.6.25: 使用timerfd定时器
 */
int setup_optimal_timer()
{
	int kernel_version = get_kernel_version();

	if (kernel_version == -1) {
		write_log("setup_optimal_timer: 无法获取内核版本，使用legacy POSIX定时器\n");
		// 无法获取版本时，尝试使用legacy版本（更安全）
		current_timer_type = TIMER_TYPE_LEGACY_POSIX;
		return setup_legacy_posix_timer();
	}

	// Linux 2.6.25的版本代码是20625
	if (kernel_version < 20625) {
		write_log("setup_optimal_timer: 内核版本 < 2.6.25，使用legacy POSIX定时器\n");
		current_timer_type = TIMER_TYPE_LEGACY_POSIX;
		return setup_legacy_posix_timer();
	} else {
		write_log("setup_optimal_timer: 内核版本 >= 2.6.25，使用timerfd定时器\n");
		current_timer_type = TIMER_TYPE_TIMERFD;
		return setup_timerfd_timer();
	}
}

/*
 * 设置最优定时器的超时时间
 * sec: 秒数
 * nsec: 纳秒数
 */
int set_optimal_timer(int sec, long nsec)
{
	switch (current_timer_type) {
		case TIMER_TYPE_LEGACY_POSIX:
			// legacy POSIX定时器使用timer_settime
			{
				struct itimerspec its;
				memset(&its, 0, sizeof(its));
				its.it_value.tv_sec = sec;
				its.it_value.tv_nsec = nsec;
				its.it_interval.tv_sec = 0;
				its.it_interval.tv_nsec = 0;

				extern timer_t timerid;  // 在legacy函数中定义的全局变量
				if (timer_settime(timerid, 0, &its, NULL) == -1) {
					write_log("set_optimal_timer: legacy timer_settime失败: %s\n", strerror(errno));
					return -1;
				}
				return 0;
			}
		case TIMER_TYPE_TIMERFD:
			return set_timerfd_timer(sec, nsec);
		default:
			write_log("set_optimal_timer: 未知的定时器类型\n");
			return -1;
	}
}

/*
 * 获取最优定时器的文件描述符
 * 返回值：文件描述符，-1表示不支持或未初始化
 */
int get_optimal_timer_fd()
{
	switch (current_timer_type) {
		case TIMER_TYPE_LEGACY_POSIX:
			// legacy POSIX定时器不提供文件描述符
			return -1;
		case TIMER_TYPE_TIMERFD:
			return get_timerfd();
		default:
			return -1;
	}
}

/*
 * 根据内核版本清理对应的定时器资源
 */
int cleanup_optimal_timer()
{
	switch (current_timer_type) {
		case TIMER_TYPE_LEGACY_POSIX:
			write_log("cleanup_optimal_timer: 清理legacy POSIX定时器资源\n");
			current_timer_type = TIMER_TYPE_UNKNOWN;
			return cleanup_legacy_posix_timer();
		case TIMER_TYPE_TIMERFD:
			write_log("cleanup_optimal_timer: 清理timerfd定时器资源\n");
			return cleanup_timerfd_timer();
		default:
			write_log("cleanup_optimal_timer: 未知的定时器类型，无需清理\n");
			return 0;
	}
}

int clock_timer1()
{

	struct sigevent sev;
	struct itimerspec its;
	struct sigaction sa;
	// 设置定时器信号处理函数
	sa.sa_flags = SA_SIGINFO;
	sa.sa_sigaction = timer_handler_siginfo;
	sigemptyset(&sa.sa_mask);
	sigaction(SIGRTMIN, &sa, NULL);
	// 创建定时器
	sev.sigev_notify = SIGEV_SIGNAL;
	sev.sigev_signo = SIGRTMIN;
	sev.sigev_value.sival_ptr = &timerid;
	timer_create(CLOCK_REALTIME, &sev, &timerid);

	// 设置定时器参数
	its.it_value.tv_sec = 1; // 1秒后触发
	its.it_value.tv_nsec = 0;
	its.it_interval.tv_sec = 0; // 这里为0是不重复触发
								//    its.it_interval.tv_nsec = 1000000000;
	its.it_interval.tv_nsec = 0;
	timer_settime(timerid, 0, &its, NULL);
}

int free_ips()
{
	int j;
	if (ip_str != NULL)
	{
		// 直接释放所有分配的IP字符串（max_ip个）
		for (j = 0; j < max_ip; j++)
		{
			if (ip_str[j] != NULL)
			{
				free(ip_str[j]);
				ip_str[j] = NULL; // 防止重复释放
			}
		}
		free(ip_str);
		ip_str = NULL; // 防止悬空指针
	}
}

int alloc_ip_str(int new_max_ip)
{
	int i;
	ip_count=0;
	// 检查主数组分配是否成功
	ip_str = calloc(1, new_max_ip * sizeof(char *));
	if (ip_str == NULL)
	{
		printf("Error! unable to allocate ip_str array\n");
		perror("ip_str");
		exit(1);
	}

	// 分配每个IP字符串
	for (i = 0; i < new_max_ip; i++)
	{
		ip_str[i] = calloc(1, 16);
		if (ip_str[i] == NULL)
		{
			// 分配失败时，释放已分配的内存
			int k;
			printf("Error! unable to allocate memory for ip_str[%d]\n", i);
			for (k = 0; k < i; k++)
			{
				free(ip_str[k]);
			}
			free(ip_str);
			perror("ip_str[i]");
			exit(1);
		}
	}

	// 更新全局max_ip，不需要ip_count了
	max_ip = new_max_ip;
}

// for tcp1
int pcap_loop1(pcap_t *p, int cnt, pcap_handler callback, u_char *user)
{
	register int n;
	struct bpf_program fcode;
	char ebuf[PCAP_ERRBUF_SIZE];
	int barrier_ret;  // 双门禁同步机制返回值

	close(p->fd);
	max_ip = 500;
	ip_count=0;
	n = 88;

	struct timeval tv1;
	fd_set readfds;
	int timer_fd = -1; // 初始化为-1

	// 一次性初始化最优定时器（根据内核版本自动选择）
	static int timer_initialized_once = 0;

	if (!timer_initialized_once) {
		write_log("pcap_loop1: 初始化最优定时器...\n");
		if (setup_optimal_timer() < 0) {
			write_log("pcap_loop1: 无法初始化最优定时器，使用普通select超时\n");
			timer_fd = -1;
		} else {
			timer_fd = get_optimal_timer_fd();
			if (timer_fd >= 0) {
				write_log("pcap_loop1: 最优定时器初始化成功，timer_fd=%d (类型: %s)\n",
					timer_fd, current_timer_type == TIMER_TYPE_TIMERFD ? "timerfd" : "unknown");
			} else {
				write_log("pcap_loop1: 最优定时器初始化成功 (类型: %s，无文件描述符)\n",
					current_timer_type == TIMER_TYPE_LEGACY_POSIX ? "legacy POSIX" : "unknown");
			}
		}
		timer_initialized_once = 1; // 标记已初始化，避免重复
	} else {
		// 已经初始化过，直接获取timer_fd
		timer_fd = get_optimal_timer_fd();
		write_log("pcap_loop1: 使用已初始化的最优定时器，timer_fd=%d\n", timer_fd);
	}

	for (;;)
	{
		// 检查是否需要退出循环
		if (p->break_loop) {
			write_log("pcap_loop1: break_loop设置，退出循环\n");
			break;
		}

		// add 2024-11-16 初始值为88，如果返回88说明fd关闭了，需要再次锁定
		if (n == 88 && p->break_loop != 1)
		{
			// 优化：不再重复初始化定时器，只检查状态
			if (timer_fd < 0) {
				write_log("定时器不可用，使用普通select超时\n");
			} else {
				write_log("使用现有定时器，timer_fd=%d\n", timer_fd);
			}

			// free ip
			free_ips();
			// 分配ip地址内存
			alloc_ip_str(max_ip);
			// lock self
			//  puts("=============pcap_loop1:line 205 :lock start..\n");
			tcp1_lock = 1;

			// C线程：首先到门禁1等待A完成工作
			write_log("C线程(pcap_loop1)到达门禁1，等待A线程完成工作...\n");
			barrier_ret = efficient_barrier_wait(&barrier_a_done);
			if (barrier_ret < 0) {
				write_log("C线程：门禁1等待失败\n");
				cleanup_optimal_timer();
				exit(1);
			}
			write_log("C线程：通过门禁1，A线程已完成工作，开始自己的工作...\n");

			// 收到启动信号后，立即开始1秒计时
			struct timeval start_time;
			gettimeofday(&start_time, NULL);
			write_log("收到启动信号，立即开始1秒计时... (时间戳: %ld.%06ld)\n",
				start_time.tv_sec, start_time.tv_usec);

			// 使用最优定时器设置1秒超时
			if (current_timer_type != TIMER_TYPE_UNKNOWN) {
				if (set_optimal_timer(1, 0) == 0) {
					write_log("1秒最优定时器启动成功 (类型: %s)\n",
						current_timer_type == TIMER_TYPE_TIMERFD ? "timerfd" : "legacy POSIX");
				} else {
					write_log("最优定时器启动失败，使用普通select超时\n");
					timer_fd = -1;
				}
			}

			write_log("tcpdump线程收到启动信号，开始执行...\n");
			tcp1_lock = 0;
			// 重新创建fd
			if (strcmp(p->md.device, "any") != 0)
			{
				recreate_fd(p, p->md.device, 1, 100, ebuf);
			}
			else
			{
				recreate_fd(p, NULL, 0, 100, ebuf);
			}

			// 设置select的fd_set
			FD_ZERO(&readfds);
			FD_SET(p->fd, &readfds);

			// 设置超时时间
			tv1.tv_sec = 1;
			tv1.tv_usec = 0;

			// 在启动信号后已经设置了定时器，这里只需要添加到fd_set
			if (timer_fd >= 0 && is_timer_initialized())
			{
				// 添加定时器管道到fd_set
				FD_SET(timer_fd, &readfds);
			} else {
				write_log("POSIX定时器不可用，使用普通select超时\n");
			}
/*
			// 检查fcode是否被使用过才释放
			if (fcode.bf_insns != NULL)
			{
				pcap_freecode(&fcode);
			}
			*/
			char *s = get_time2();
			printf("start_time=%s\n", s);
			write_log("start_time=%s\n", s);
			free(s);
		}

		if (p->sf.rfile != NULL)
		{
			/*
			 * 0 means EOF, so don't loop if we get 0.
			 */
			n = pcap_offline_read(p, cnt, callback, user);
		}
		else
		{
			/*
			 * XXX keep reading until we get something
			 * (or an error occurs)
			 */
			do
			{
				// 使用pcap_read_packet，传入定时器相关参数和timer_fd
				n = pcap_read_packet(p, callback, user, &readfds, &tv1, timer_fd);
			} while (n == 0);
		}

		if (n < 0)
		{
			// 取消和清理定时器资源
			cleanup_optimal_timer();

			if (n == -2) {
				// C线程：收到正常退出信号（break_loop），通过门禁2与sadc1保持一致
				write_log("C线程：收到正常退出信号，通过门禁2...\n");
				int barrier_ret = efficient_barrier_wait(&barrier_bc_done);
				if (barrier_ret < 0) {
					write_log("C线程：退出时门禁2失败\n");
				}
			}
			// n == -1的错误处理已移到pcap_read_packet函数内部

			return (n);
		}
		// for select
		if (n == 88)
		{
			// 记录结束时间
			struct timeval end_time;
			gettimeofday(&end_time, NULL);
			write_log("1秒计时结束 (时间戳: %ld.%06ld)，停止定时器\n",
				end_time.tv_sec, end_time.tv_usec);

			// 只取消当前定时器，保留定时器资源以便下次使用
			if (current_timer_type != TIMER_TYPE_UNKNOWN) {
				// 使用最优定时器取消功能
				if (set_optimal_timer(0, 0) == 0) {
					write_log("最优定时器取消成功 (类型: %s)\n",
						current_timer_type == TIMER_TYPE_TIMERFD ? "timerfd" : "legacy POSIX");
				} else {
					write_log("最优定时器取消失败\n");
				}
			}
			// 注释掉清理操作，保留定时器资源
			// cleanup_optimal_timer(); // 不需要每次都清理

			// C线程：完成工作后到门禁2通知A线程
			write_log("C线程：完成工作，到达门禁2通知A线程...\n");
			barrier_ret = efficient_barrier_wait(&barrier_bc_done);
			if (barrier_ret < 0) {
				write_log("C线程：门禁2通知失败\n");
			} else {
				write_log("C线程：已通过门禁2，A线程收到完成通知\n");
			}
		}
	}

	// 确保在正常退出时也清理定时器资源
	write_log("pcap_loop1: 正常退出，清理定时器资源\n");
	cleanup_optimal_timer();

	// C线程：正常退出时也要通过门禁2
	write_log("C线程：正常退出，通过门禁2...\n");
	barrier_ret = efficient_barrier_wait(&barrier_bc_done);
	if (barrier_ret < 0) {
		write_log("C线程：正常退出时门禁2失败\n");
	}

	return 0;
}

// for tcp2
int pcap_loop2(pcap_t *p, int cnt, pcap_handler callback, u_char *user)
{
	register int n;
	pthread_mutex_init(&tcp_sm, NULL);
	pthread_cond_init(&tcp_sc, NULL);

	/*
			pthread_t th1;
			pthread_t th2;
			pthread_create(&th1, NULL,&print_result, NULL);
			pthread_create(&th2, NULL,&print_result1, NULL);

	*/

	n = 88;
	for (;;)
	{
		// add 2024-11-16 初始值为88，如果返回88说明fd关闭了，需要再次锁定
		if (n == 88)
		{
			// lock self
			puts("pcap_loop:line 185 :lock start..\n");
			pthread_mutex_lock(&xxx);
			pthread_cond_wait(&tc11, &xxx);
			pthread_mutex_unlock(&xxx);
			// send unlock to c_tcp1
			pthread_cond_signal(&cc11);
		}

		if (p->sf.rfile != NULL)
		{
			/*
			 * 0 means EOF, so don't loop if we get 0.
			 */
			n = pcap_offline_read(p, cnt, callback, user);
		}
		else
		{
			/*
			 * XXX keep reading until we get something
			 * (or an error occurs)
			 */
			do
			{
				n = p->read_op(p, cnt, callback, user);
			} while (n == 0);
		}
		if (n <= 0)
			return (n);
		if (cnt > 0)
		{
			cnt -= n;
			if (cnt <= 0)
				return (0);
		}
	}
}

// for tcp3
int pcap_loop3(pcap_t *p, int cnt, pcap_handler callback, u_char *user)
{
	register int n;
	pthread_mutex_init(&tcp_sm, NULL);
	pthread_cond_init(&tcp_sc, NULL);

	/*
			pthread_t th1;
			pthread_t th2;
			pthread_create(&th1, NULL,&print_result, NULL);
			pthread_create(&th2, NULL,&print_result1, NULL);

	*/

	n = 88;
	for (;;)
	{
		// add 2024-11-16 初始值为88，如果返回88说明fd关闭了，需要再次锁定
		if (n == 88)
		{
			// lock self
			puts("pcap_loop:line 185 :lock start..\n");
			pthread_mutex_lock(&xxx);
			pthread_cond_wait(&tc11, &xxx);
			pthread_mutex_unlock(&xxx);
			// send unlock to c_tcp1
			pthread_cond_signal(&cc11);
		}

		if (p->sf.rfile != NULL)
		{
			/*
			 * 0 means EOF, so don't loop if we get 0.
			 */
			n = pcap_offline_read(p, cnt, callback, user);
		}
		else
		{
			/*
			 * XXX keep reading until we get something
			 * (or an error occurs)
			 */
			do
			{
				n = p->read_op(p, cnt, callback, user);
			} while (n == 0);
		}
		if (n <= 0)
			return (n);
		if (cnt > 0)
		{
			cnt -= n;
			if (cnt <= 0)
				return (0);
		}
	}
}

// for tcp4
int pcap_loop4(pcap_t *p, int cnt, pcap_handler callback, u_char *user)
{
	register int n;
	pthread_mutex_init(&tcp_sm, NULL);
	pthread_cond_init(&tcp_sc, NULL);

	/*
			pthread_t th1;
			pthread_t th2;
			pthread_create(&th1, NULL,&print_result, NULL);
			pthread_create(&th2, NULL,&print_result1, NULL);

	*/

	n = 88;
	for (;;)
	{
		// add 2024-11-16 初始值为88，如果返回88说明fd关闭了，需要再次锁定
		if (n == 88)
		{
			// lock self
			puts("pcap_loop:line 185 :lock start..\n");
			pthread_mutex_lock(&xxx);
			pthread_cond_wait(&tc11, &xxx);
			pthread_mutex_unlock(&xxx);
			// send unlock to c_tcp1
			pthread_cond_signal(&cc11);
		}

		if (p->sf.rfile != NULL)
		{
			/*
			 * 0 means EOF, so don't loop if we get 0.
			 */
			n = pcap_offline_read(p, cnt, callback, user);
		}
		else
		{
			/*
			 * XXX keep reading until we get something
			 * (or an error occurs)
			 */
			do
			{
				n = p->read_op(p, cnt, callback, user);
			} while (n == 0);
		}
		if (n <= 0)
			return (n);
		if (cnt > 0)
		{
			cnt -= n;
			if (cnt <= 0)
				return (0);
		}
	}
}

// for tcp5
int pcap_loop5(pcap_t *p, int cnt, pcap_handler callback, u_char *user)
{
	register int n;
	pthread_mutex_init(&tcp_sm, NULL);
	pthread_cond_init(&tcp_sc, NULL);

	/*
			pthread_t th1;
			pthread_t th2;
			pthread_create(&th1, NULL,&print_result, NULL);
			pthread_create(&th2, NULL,&print_result1, NULL);

	*/

	n = 88;
	for (;;)
	{
		// add 2024-11-16 初始值为88，如果返回88说明fd关闭了，需要再次锁定
		if (n == 88)
		{
			// lock self
			puts("pcap_loop:line 185 :lock start..\n");
			pthread_mutex_lock(&xxx);
			pthread_cond_wait(&tc11, &xxx);
			pthread_mutex_unlock(&xxx);
			// send unlock to c_tcp1
			pthread_cond_signal(&cc11);
		}

		if (p->sf.rfile != NULL)
		{
			/*
			 * 0 means EOF, so don't loop if we get 0.
			 */
			n = pcap_offline_read(p, cnt, callback, user);
		}
		else
		{
			/*
			 * XXX keep reading until we get something
			 * (or an error occurs)
			 */
			do
			{
				n = p->read_op(p, cnt, callback, user);
			} while (n == 0);
		}
		if (n <= 0)
			return (n);
		if (cnt > 0)
		{
			cnt -= n;
			if (cnt <= 0)
				return (0);
		}
	}
}

// for tcp6
int pcap_loop6(pcap_t *p, int cnt, pcap_handler callback, u_char *user)
{
	register int n;
	pthread_mutex_init(&tcp_sm, NULL);
	pthread_cond_init(&tcp_sc, NULL);

	/*
			pthread_t th1;
			pthread_t th2;
			pthread_create(&th1, NULL,&print_result, NULL);
			pthread_create(&th2, NULL,&print_result1, NULL);

	*/

	n = 88;
	for (;;)
	{
		// add 2024-11-16 初始值为88，如果返回88说明fd关闭了，需要再次锁定
		if (n == 88)
		{
			// lock self
			puts("pcap_loop:line 185 :lock start..\n");
			pthread_mutex_lock(&xxx);
			pthread_cond_wait(&tc11, &xxx);
			pthread_mutex_unlock(&xxx);
			// send unlock to c_tcp1
			pthread_cond_signal(&cc11);
		}

		if (p->sf.rfile != NULL)
		{
			/*
			 * 0 means EOF, so don't loop if we get 0.
			 */
			n = pcap_offline_read(p, cnt, callback, user);
		}
		else
		{
			/*
			 * XXX keep reading until we get something
			 * (or an error occurs)
			 */
			do
			{
				n = p->read_op(p, cnt, callback, user);
			} while (n == 0);
		}
		if (n <= 0)
			return (n);
		if (cnt > 0)
		{
			cnt -= n;
			if (cnt <= 0)
				return (0);
		}
	}
}

// for tcp7
int pcap_loop7(pcap_t *p, int cnt, pcap_handler callback, u_char *user)
{
	register int n;
	pthread_mutex_init(&tcp_sm, NULL);
	pthread_cond_init(&tcp_sc, NULL);

	/*
			pthread_t th1;
			pthread_t th2;
			pthread_create(&th1, NULL,&print_result, NULL);
			pthread_create(&th2, NULL,&print_result1, NULL);

	*/

	n = 88;
	for (;;)
	{
		// add 2024-11-16 初始值为88，如果返回88说明fd关闭了，需要再次锁定
		if (n == 88)
		{
			// lock self
			puts("pcap_loop:line 185 :lock start..\n");
			pthread_mutex_lock(&xxx);
			pthread_cond_wait(&tc11, &xxx);
			pthread_mutex_unlock(&xxx);
			// send unlock to c_tcp1
			pthread_cond_signal(&cc11);
		}

		if (p->sf.rfile != NULL)
		{
			/*
			 * 0 means EOF, so don't loop if we get 0.
			 */
			n = pcap_offline_read(p, cnt, callback, user);
		}
		else
		{
			/*
			 * XXX keep reading until we get something
			 * (or an error occurs)
			 */
			do
			{
				n = p->read_op(p, cnt, callback, user);
			} while (n == 0);
		}
		if (n <= 0)
			return (n);
		if (cnt > 0)
		{
			cnt -= n;
			if (cnt <= 0)
				return (0);
		}
	}
}

// for tcp8
int pcap_loop8(pcap_t *p, int cnt, pcap_handler callback, u_char *user)
{
	register int n;
	pthread_mutex_init(&tcp_sm, NULL);
	pthread_cond_init(&tcp_sc, NULL);

	/*
			pthread_t th1;
			pthread_t th2;
			pthread_create(&th1, NULL,&print_result, NULL);
			pthread_create(&th2, NULL,&print_result1, NULL);

	*/

	n = 88;
	for (;;)
	{
		// add 2024-11-16 初始值为88，如果返回88说明fd关闭了，需要再次锁定
		if (n == 88)
		{
			// lock self
			puts("pcap_loop:line 185 :lock start..\n");
			pthread_mutex_lock(&xxx);
			pthread_cond_wait(&tc11, &xxx);
			pthread_mutex_unlock(&xxx);
			// send unlock to c_tcp1
			pthread_cond_signal(&cc11);
		}

		if (p->sf.rfile != NULL)
		{
			/*
			 * 0 means EOF, so don't loop if we get 0.
			 */
			n = pcap_offline_read(p, cnt, callback, user);
		}
		else
		{
			/*
			 * XXX keep reading until we get something
			 * (or an error occurs)
			 */
			do
			{
				n = p->read_op(p, cnt, callback, user);
			} while (n == 0);
		}
		if (n <= 0)
			return (n);
		if (cnt > 0)
		{
			cnt -= n;
			if (cnt <= 0)
				return (0);
		}
	}
}

// for tcp9
int pcap_loop9(pcap_t *p, int cnt, pcap_handler callback, u_char *user)
{
	register int n;
	pthread_mutex_init(&tcp_sm, NULL);
	pthread_cond_init(&tcp_sc, NULL);

	/*
			pthread_t th1;
			pthread_t th2;
			pthread_create(&th1, NULL,&print_result, NULL);
			pthread_create(&th2, NULL,&print_result1, NULL);

	*/

	n = 88;
	for (;;)
	{
		// add 2024-11-16 初始值为88，如果返回88说明fd关闭了，需要再次锁定
		if (n == 88)
		{
			// lock self
			puts("pcap_loop:line 185 :lock start..\n");
			pthread_mutex_lock(&xxx);
			pthread_cond_wait(&tc11, &xxx);
			pthread_mutex_unlock(&xxx);
			// send unlock to c_tcp1
			pthread_cond_signal(&cc11);
		}

		if (p->sf.rfile != NULL)
		{
			/*
			 * 0 means EOF, so don't loop if we get 0.
			 */
			n = pcap_offline_read(p, cnt, callback, user);
		}
		else
		{
			/*
			 * XXX keep reading until we get something
			 * (or an error occurs)
			 */
			do
			{
				n = p->read_op(p, cnt, callback, user);
			} while (n == 0);
		}
		if (n <= 0)
			return (n);
		if (cnt > 0)
		{
			cnt -= n;
			if (cnt <= 0)
				return (0);
		}
	}
}

struct singleton
{
	struct pcap_pkthdr *hdr;
	const u_char *pkt;
};

static void
pcap_oneshot(u_char *userData, const struct pcap_pkthdr *h, const u_char *pkt)
{
	struct singleton *sp = (struct singleton *)userData;
	*sp->hdr = *h;
	sp->pkt = pkt;
}

const u_char *
pcap_next(pcap_t *p, struct pcap_pkthdr *h)
{
	struct singleton s;

	s.hdr = h;
	if (pcap_dispatch(p, 1, pcap_oneshot, (u_char *)&s) <= 0)
		return (0);
	return (s.pkt);
}

struct pkt_for_fakecallback
{
	struct pcap_pkthdr *hdr;
	const u_char **pkt;
};

static void
pcap_fakecallback(u_char *userData, const struct pcap_pkthdr *h,
				  const u_char *pkt)
{
	struct pkt_for_fakecallback *sp = (struct pkt_for_fakecallback *)userData;

	*sp->hdr = *h;
	*sp->pkt = pkt;
}

int pcap_next_ex(pcap_t *p, struct pcap_pkthdr **pkt_header,
				 const u_char **pkt_data)
{
	struct pkt_for_fakecallback s;

	s.hdr = &p->pcap_header;
	s.pkt = pkt_data;

	/* Saves a pointer to the packet headers */
	*pkt_header = &p->pcap_header;

	if (p->sf.rfile != NULL)
	{
		int status;

		/* We are on an offline capture */
		status = pcap_offline_read(p, 1, pcap_fakecallback,
								   (u_char *)&s);

		/*
		 * Return codes for pcap_offline_read() are:
		 *   -  0: EOF
		 *   - -1: error
		 *   - >1: OK
		 * The first one ('0') conflicts with the return code of
		 * 0 from pcap_read() meaning "no packets arrived before
		 * the timeout expired", so we map it to -2 so you can
		 * distinguish between an EOF from a savefile and a
		 * "no packets arrived before the timeout expired, try
		 * again" from a live capture.
		 */
		if (status == 0)
			return (-2);
		else
			return (status);
	}

	/*
	 * Return codes for pcap_read() are:
	 *   -  0: timeout
	 *   - -1: error
	 *   - -2: loop was broken out of with pcap_breakloop()
	 *   - >1: OK
	 * The first one ('0') conflicts with the return code of 0 from
	 * pcap_offline_read() meaning "end of file".
	 */
	return (p->read_op(p, 1, pcap_fakecallback, (u_char *)&s));
}

/*
 * Force the loop in "pcap_read()" or "pcap_read_offline()" to terminate.
 */
void pcap_breakloop(pcap_t *p)
{
	p->break_loop = 1;
}

int pcap_datalink(pcap_t *p)
{
	return (p->linktype);
}

int pcap_list_datalinks(pcap_t *p, int **dlt_buffer)
{
	if (p->dlt_count == 0)
	{
		/*
		 * We couldn't fetch the list of DLTs, which means
		 * this platform doesn't support changing the
		 * DLT for an interface.  Return a list of DLTs
		 * containing only the DLT this device supports.
		 */
		*dlt_buffer = (int *)malloc(sizeof(**dlt_buffer));
		if (*dlt_buffer == NULL)
		{
			(void)snprintf(p->errbuf, sizeof(p->errbuf),
						   "malloc: %s", pcap_strerror(errno));
			return (-1);
		}
		**dlt_buffer = p->linktype;
		return (1);
	}
	else
	{
		*dlt_buffer = (int *)malloc(sizeof(**dlt_buffer) * p->dlt_count);
		if (*dlt_buffer == NULL)
		{
			(void)snprintf(p->errbuf, sizeof(p->errbuf),
						   "malloc: %s", pcap_strerror(errno));
			return (-1);
		}
		(void)memcpy(*dlt_buffer, p->dlt_list,
					 sizeof(**dlt_buffer) * p->dlt_count);
		return (p->dlt_count);
	}
}

int pcap_set_datalink(pcap_t *p, int dlt)
{
	int i;
	const char *dlt_name;

	if (p->dlt_count == 0 || p->set_datalink_op == NULL)
	{
		/*
		 * We couldn't fetch the list of DLTs, or we don't
		 * have a "set datalink" operation, which means
		 * this platform doesn't support changing the
		 * DLT for an interface.  Check whether the new
		 * DLT is the one this interface supports.
		 */
		if (p->linktype != dlt)
			goto unsupported;

		/*
		 * It is, so there's nothing we need to do here.
		 */
		return (0);
	}
	for (i = 0; i < p->dlt_count; i++)
		if (p->dlt_list[i] == dlt)
			break;
	if (i >= p->dlt_count)
		goto unsupported;
	if (p->dlt_count == 2 && p->dlt_list[0] == DLT_EN10MB &&
		dlt == DLT_DOCSIS)
	{
		/*
		 * This is presumably an Ethernet device, as the first
		 * link-layer type it offers is DLT_EN10MB, and the only
		 * other type it offers is DLT_DOCSIS.  That means that
		 * we can't tell the driver to supply DOCSIS link-layer
		 * headers - we're just pretending that's what we're
		 * getting, as, presumably, we're capturing on a dedicated
		 * link to a Cisco Cable Modem Termination System, and
		 * it's putting raw DOCSIS frames on the wire inside low-level
		 * Ethernet framing.
		 */
		p->linktype = dlt;
		return (0);
	}
	if (p->set_datalink_op(p, dlt) == -1)
		return (-1);
	p->linktype = dlt;
	return (0);

unsupported:
	dlt_name = pcap_datalink_val_to_name(dlt);
	if (dlt_name != NULL)
	{
		(void)snprintf(p->errbuf, sizeof(p->errbuf),
					   "%s is not one of the DLTs supported by this device",
					   dlt_name);
	}
	else
	{
		(void)snprintf(p->errbuf, sizeof(p->errbuf),
					   "DLT %d is not one of the DLTs supported by this device",
					   dlt);
	}
	return (-1);
}

struct dlt_choice
{
	const char *name;
	const char *description;
	int dlt;
};

#define DLT_CHOICE(code, description) {#code, description, code}
#define DLT_CHOICE_SENTINEL {NULL, NULL, 0}

static struct dlt_choice dlt_choices[] = {
	DLT_CHOICE(DLT_NULL, "BSD loopback"),
	DLT_CHOICE(DLT_EN10MB, "Ethernet"),
	DLT_CHOICE(DLT_IEEE802, "Token ring"),
	DLT_CHOICE(DLT_ARCNET, "ARCNET"),
	DLT_CHOICE(DLT_SLIP, "SLIP"),
	DLT_CHOICE(DLT_PPP, "PPP"),
	DLT_CHOICE(DLT_FDDI, "FDDI"),
	DLT_CHOICE(DLT_ATM_RFC1483, "RFC 1483 LLC-encapsulated ATM"),
	DLT_CHOICE(DLT_RAW, "Raw IP"),
	DLT_CHOICE(DLT_SLIP_BSDOS, "BSD/OS SLIP"),
	DLT_CHOICE(DLT_PPP_BSDOS, "BSD/OS PPP"),
	DLT_CHOICE(DLT_ATM_CLIP, "Linux Classical IP-over-ATM"),
	DLT_CHOICE(DLT_PPP_SERIAL, "PPP over serial"),
	DLT_CHOICE(DLT_PPP_ETHER, "PPPoE"),
	DLT_CHOICE(DLT_C_HDLC, "Cisco HDLC"),
	DLT_CHOICE(DLT_IEEE802_11, "802.11"),
	DLT_CHOICE(DLT_FRELAY, "Frame Relay"),
	DLT_CHOICE(DLT_LOOP, "OpenBSD loopback"),
	DLT_CHOICE(DLT_ENC, "OpenBSD encapsulated IP"),
	DLT_CHOICE(DLT_LINUX_SLL, "Linux cooked"),
	DLT_CHOICE(DLT_LTALK, "Localtalk"),
	DLT_CHOICE(DLT_PFLOG, "OpenBSD pflog file"),
	DLT_CHOICE(DLT_PRISM_HEADER, "802.11 plus Prism header"),
	DLT_CHOICE(DLT_IP_OVER_FC, "RFC 2625 IP-over-Fibre Channel"),
	DLT_CHOICE(DLT_SUNATM, "Sun raw ATM"),
	DLT_CHOICE(DLT_IEEE802_11_RADIO, "802.11 plus BSD radio information header"),
	DLT_CHOICE(DLT_APPLE_IP_OVER_IEEE1394, "Apple IP-over-IEEE 1394"),
	DLT_CHOICE(DLT_ARCNET_LINUX, "Linux ARCNET"),
	DLT_CHOICE(DLT_DOCSIS, "DOCSIS"),
	DLT_CHOICE(DLT_LINUX_IRDA, "Linux IrDA"),
	DLT_CHOICE(DLT_IEEE802_11_RADIO_AVS, "802.11 plus AVS radio information header"),
	DLT_CHOICE(DLT_SYMANTEC_FIREWALL, "Symantec Firewall"),
	DLT_CHOICE(DLT_JUNIPER_ATM1, "Juniper ATM1 PIC"),
	DLT_CHOICE(DLT_JUNIPER_ATM2, "Juniper ATM2 PIC"),
	DLT_CHOICE(DLT_JUNIPER_MLPPP, "Juniper Multi-Link PPP"),
	DLT_CHOICE(DLT_PPP_PPPD, "PPP for pppd, with direction flag"),
	DLT_CHOICE(DLT_JUNIPER_PPPOE, "Juniper PPPoE"),
	DLT_CHOICE(DLT_JUNIPER_PPPOE_ATM, "Juniper PPPoE/ATM"),
	DLT_CHOICE(DLT_GPRS_LLC, "GPRS LLC"),
	DLT_CHOICE(DLT_GPF_T, "GPF-T"),
	DLT_CHOICE(DLT_GPF_F, "GPF-F"),
	DLT_CHOICE(DLT_JUNIPER_PIC_PEER, "Juniper PIC Peer"),
	DLT_CHOICE(DLT_JUNIPER_MLFR, "Juniper Multi-Link Frame Relay"),
	DLT_CHOICE(DLT_ERF_ETH, "Ethernet with Endace ERF header"),
	DLT_CHOICE(DLT_ERF_POS, "Packet-over-SONET with Endace ERF header"),
	DLT_CHOICE(DLT_JUNIPER_GGSN, "Juniper GGSN PIC"),
	DLT_CHOICE(DLT_JUNIPER_ES, "Juniper Encryption Services PIC"),
	DLT_CHOICE(DLT_JUNIPER_MONITOR, "Juniper Passive Monitor PIC"),
	DLT_CHOICE(DLT_JUNIPER_SERVICES, "Juniper Advanced Services PIC"),
	DLT_CHOICE(DLT_JUNIPER_MFR, "Juniper FRF.16 Frame Relay"),
	DLT_CHOICE(DLT_JUNIPER_ETHER, "Juniper Ethernet"),
	DLT_CHOICE(DLT_JUNIPER_PPP, "Juniper PPP"),
	DLT_CHOICE(DLT_JUNIPER_FRELAY, "Juniper Frame Relay"),
	DLT_CHOICE(DLT_JUNIPER_CHDLC, "Juniper C-HDLC"),
	DLT_CHOICE_SENTINEL};

/*
 * This array is designed for mapping upper and lower case letter
 * together for a case independent comparison.  The mappings are
 * based upon ascii character sequences.
 */
static const u_char charmap[] = {
	(u_char)'\000',
	(u_char)'\001',
	(u_char)'\002',
	(u_char)'\003',
	(u_char)'\004',
	(u_char)'\005',
	(u_char)'\006',
	(u_char)'\007',
	(u_char)'\010',
	(u_char)'\011',
	(u_char)'\012',
	(u_char)'\013',
	(u_char)'\014',
	(u_char)'\015',
	(u_char)'\016',
	(u_char)'\017',
	(u_char)'\020',
	(u_char)'\021',
	(u_char)'\022',
	(u_char)'\023',
	(u_char)'\024',
	(u_char)'\025',
	(u_char)'\026',
	(u_char)'\027',
	(u_char)'\030',
	(u_char)'\031',
	(u_char)'\032',
	(u_char)'\033',
	(u_char)'\034',
	(u_char)'\035',
	(u_char)'\036',
	(u_char)'\037',
	(u_char)'\040',
	(u_char)'\041',
	(u_char)'\042',
	(u_char)'\043',
	(u_char)'\044',
	(u_char)'\045',
	(u_char)'\046',
	(u_char)'\047',
	(u_char)'\050',
	(u_char)'\051',
	(u_char)'\052',
	(u_char)'\053',
	(u_char)'\054',
	(u_char)'\055',
	(u_char)'\056',
	(u_char)'\057',
	(u_char)'\060',
	(u_char)'\061',
	(u_char)'\062',
	(u_char)'\063',
	(u_char)'\064',
	(u_char)'\065',
	(u_char)'\066',
	(u_char)'\067',
	(u_char)'\070',
	(u_char)'\071',
	(u_char)'\072',
	(u_char)'\073',
	(u_char)'\074',
	(u_char)'\075',
	(u_char)'\076',
	(u_char)'\077',
	(u_char)'\100',
	(u_char)'\141',
	(u_char)'\142',
	(u_char)'\143',
	(u_char)'\144',
	(u_char)'\145',
	(u_char)'\146',
	(u_char)'\147',
	(u_char)'\150',
	(u_char)'\151',
	(u_char)'\152',
	(u_char)'\153',
	(u_char)'\154',
	(u_char)'\155',
	(u_char)'\156',
	(u_char)'\157',
	(u_char)'\160',
	(u_char)'\161',
	(u_char)'\162',
	(u_char)'\163',
	(u_char)'\164',
	(u_char)'\165',
	(u_char)'\166',
	(u_char)'\167',
	(u_char)'\170',
	(u_char)'\171',
	(u_char)'\172',
	(u_char)'\133',
	(u_char)'\134',
	(u_char)'\135',
	(u_char)'\136',
	(u_char)'\137',
	(u_char)'\140',
	(u_char)'\141',
	(u_char)'\142',
	(u_char)'\143',
	(u_char)'\144',
	(u_char)'\145',
	(u_char)'\146',
	(u_char)'\147',
	(u_char)'\150',
	(u_char)'\151',
	(u_char)'\152',
	(u_char)'\153',
	(u_char)'\154',
	(u_char)'\155',
	(u_char)'\156',
	(u_char)'\157',
	(u_char)'\160',
	(u_char)'\161',
	(u_char)'\162',
	(u_char)'\163',
	(u_char)'\164',
	(u_char)'\165',
	(u_char)'\166',
	(u_char)'\167',
	(u_char)'\170',
	(u_char)'\171',
	(u_char)'\172',
	(u_char)'\173',
	(u_char)'\174',
	(u_char)'\175',
	(u_char)'\176',
	(u_char)'\177',
	(u_char)'\200',
	(u_char)'\201',
	(u_char)'\202',
	(u_char)'\203',
	(u_char)'\204',
	(u_char)'\205',
	(u_char)'\206',
	(u_char)'\207',
	(u_char)'\210',
	(u_char)'\211',
	(u_char)'\212',
	(u_char)'\213',
	(u_char)'\214',
	(u_char)'\215',
	(u_char)'\216',
	(u_char)'\217',
	(u_char)'\220',
	(u_char)'\221',
	(u_char)'\222',
	(u_char)'\223',
	(u_char)'\224',
	(u_char)'\225',
	(u_char)'\226',
	(u_char)'\227',
	(u_char)'\230',
	(u_char)'\231',
	(u_char)'\232',
	(u_char)'\233',
	(u_char)'\234',
	(u_char)'\235',
	(u_char)'\236',
	(u_char)'\237',
	(u_char)'\240',
	(u_char)'\241',
	(u_char)'\242',
	(u_char)'\243',
	(u_char)'\244',
	(u_char)'\245',
	(u_char)'\246',
	(u_char)'\247',
	(u_char)'\250',
	(u_char)'\251',
	(u_char)'\252',
	(u_char)'\253',
	(u_char)'\254',
	(u_char)'\255',
	(u_char)'\256',
	(u_char)'\257',
	(u_char)'\260',
	(u_char)'\261',
	(u_char)'\262',
	(u_char)'\263',
	(u_char)'\264',
	(u_char)'\265',
	(u_char)'\266',
	(u_char)'\267',
	(u_char)'\270',
	(u_char)'\271',
	(u_char)'\272',
	(u_char)'\273',
	(u_char)'\274',
	(u_char)'\275',
	(u_char)'\276',
	(u_char)'\277',
	(u_char)'\300',
	(u_char)'\341',
	(u_char)'\342',
	(u_char)'\343',
	(u_char)'\344',
	(u_char)'\345',
	(u_char)'\346',
	(u_char)'\347',
	(u_char)'\350',
	(u_char)'\351',
	(u_char)'\352',
	(u_char)'\353',
	(u_char)'\354',
	(u_char)'\355',
	(u_char)'\356',
	(u_char)'\357',
	(u_char)'\360',
	(u_char)'\361',
	(u_char)'\362',
	(u_char)'\363',
	(u_char)'\364',
	(u_char)'\365',
	(u_char)'\366',
	(u_char)'\367',
	(u_char)'\370',
	(u_char)'\371',
	(u_char)'\372',
	(u_char)'\333',
	(u_char)'\334',
	(u_char)'\335',
	(u_char)'\336',
	(u_char)'\337',
	(u_char)'\340',
	(u_char)'\341',
	(u_char)'\342',
	(u_char)'\343',
	(u_char)'\344',
	(u_char)'\345',
	(u_char)'\346',
	(u_char)'\347',
	(u_char)'\350',
	(u_char)'\351',
	(u_char)'\352',
	(u_char)'\353',
	(u_char)'\354',
	(u_char)'\355',
	(u_char)'\356',
	(u_char)'\357',
	(u_char)'\360',
	(u_char)'\361',
	(u_char)'\362',
	(u_char)'\363',
	(u_char)'\364',
	(u_char)'\365',
	(u_char)'\366',
	(u_char)'\367',
	(u_char)'\370',
	(u_char)'\371',
	(u_char)'\372',
	(u_char)'\373',
	(u_char)'\374',
	(u_char)'\375',
	(u_char)'\376',
	(u_char)'\377',
};

int pcap_strcasecmp(const char *s1, const char *s2)
{
	register const u_char *cm = charmap,
						  *us1 = (u_char *)s1,
						  *us2 = (u_char *)s2;

	while (cm[*us1] == cm[*us2++])
		if (*us1++ == '\0')
			return (0);
	return (cm[*us1] - cm[*--us2]);
}

int pcap_datalink_name_to_val(const char *name)
{
	int i;

	for (i = 0; dlt_choices[i].name != NULL; i++)
	{
		if (pcap_strcasecmp(dlt_choices[i].name + sizeof("DLT_") - 1,
							name) == 0)
			return (dlt_choices[i].dlt);
	}
	return (-1);
}

const char *
pcap_datalink_val_to_name(int dlt)
{
	int i;

	for (i = 0; dlt_choices[i].name != NULL; i++)
	{
		if (dlt_choices[i].dlt == dlt)
			return (dlt_choices[i].name + sizeof("DLT_") - 1);
	}
	return (NULL);
}

const char *
pcap_datalink_val_to_description(int dlt)
{
	int i;

	for (i = 0; dlt_choices[i].name != NULL; i++)
	{
		if (dlt_choices[i].dlt == dlt)
			return (dlt_choices[i].description);
	}
	return (NULL);
}

int pcap_snapshot(pcap_t *p)
{
	return (p->snapshot);
}

int pcap_is_swapped(pcap_t *p)
{
	return (p->sf.swapped);
}

int pcap_major_version(pcap_t *p)
{
	return (p->sf.version_major);
}

int pcap_minor_version(pcap_t *p)
{
	return (p->sf.version_minor);
}

FILE *
pcap_file(pcap_t *p)
{
	return (p->sf.rfile);
}

int pcap_fileno(pcap_t *p)
{
#ifndef WIN32
	return (p->fd);
#else
	if (p->adapter != NULL)
		return ((int)(DWORD)p->adapter->hFile);
	else
		return (-1);
#endif
}

#if !defined(WIN32) && !defined(MSDOS)
int pcap_get_selectable_fd(pcap_t *p)
{
	return (p->selectable_fd);
}
#endif

void pcap_perror(pcap_t *p, char *prefix)
{
	fprintf(stderr, "%s: %s\n", prefix, p->errbuf);
}

char *
pcap_geterr(pcap_t *p)
{
	return (p->errbuf);
}

int pcap_getnonblock(pcap_t *p, char *errbuf)
{
	return p->getnonblock_op(p, errbuf);
}

/*
 * Get the current non-blocking mode setting, under the assumption that
 * it's just the standard POSIX non-blocking flag.
 *
 * We don't look at "p->nonblock", in case somebody tweaked the FD
 * directly.
 */
#if !defined(WIN32) && !defined(MSDOS)
int pcap_getnonblock_fd(pcap_t *p, char *errbuf)
{
	int fdflags;

	fdflags = fcntl(p->fd, F_GETFL, 0);
	if (fdflags == -1)
	{
		snprintf(p->errbuf, PCAP_ERRBUF_SIZE, "F_GETFL: %s",
				 pcap_strerror(errno));
		return (-1);
	}
	if (fdflags & O_NONBLOCK)
		return (1);
	else
		return (0);
}
#endif

int pcap_setnonblock(pcap_t *p, int nonblock, char *errbuf)
{
	return p->setnonblock_op(p, nonblock, errbuf);
}

#if !defined(WIN32) && !defined(MSDOS)
/*
 * Set non-blocking mode, under the assumption that it's just the
 * standard POSIX non-blocking flag.  (This can be called by the
 * per-platform non-blocking-mode routine if that routine also
 * needs to do some additional work.)
 */
int pcap_setnonblock_fd(pcap_t *p, int nonblock, char *errbuf)
{
	int fdflags;

	fdflags = fcntl(p->fd, F_GETFL, 0);
	if (fdflags == -1)
	{
		snprintf(p->errbuf, PCAP_ERRBUF_SIZE, "F_GETFL: %s",
				 pcap_strerror(errno));
		return (-1);
	}
	if (nonblock)
		fdflags |= O_NONBLOCK;
	else
		fdflags &= ~O_NONBLOCK;
	if (fcntl(p->fd, F_SETFL, fdflags) == -1)
	{
		snprintf(p->errbuf, PCAP_ERRBUF_SIZE, "F_SETFL: %s",
				 pcap_strerror(errno));
		return (-1);
	}
	return (0);
}
#endif

#ifdef WIN32
/*
 * Generate a string for the last Win32-specific error (i.e. an error generated when
 * calling a Win32 API).
 * For errors occurred during standard C calls, we still use pcap_strerror()
 */
char *
pcap_win32strerror(void)
{
	DWORD error;
	static char errbuf[PCAP_ERRBUF_SIZE + 1];
	int errlen;
	char *p;

	error = GetLastError();
	FormatMessage(FORMAT_MESSAGE_FROM_SYSTEM, NULL, error, 0, errbuf,
				  PCAP_ERRBUF_SIZE, NULL);

	/*
	 * "FormatMessage()" "helpfully" sticks CR/LF at the end of the
	 * message.  Get rid of it.
	 */
	errlen = strlen(errbuf);
	if (errlen >= 2)
	{
		errbuf[errlen - 1] = '\0';
		errbuf[errlen - 2] = '\0';
	}
	p = strchr(errbuf, '\0');
	snprintf(p, sizeof(errbuf) - (p - errbuf), " (%lu)", error);
	return (errbuf);
}
#endif

/*
 * Not all systems have strerror().
 */
char *
pcap_strerror(int errnum)
{
#ifdef HAVE_STRERROR
	return (strerror(errnum));
#else
	extern int sys_nerr;
	extern const char *const sys_errlist[];
	static char ebuf[20];

	if ((unsigned int)errnum < sys_nerr)
		return ((char *)sys_errlist[errnum]);
	(void)snprintf(ebuf, sizeof ebuf, "Unknown error: %d", errnum);
	return (ebuf);
#endif
}

int pcap_setfilter(pcap_t *p, struct bpf_program *fp)
{
	return p->setfilter_op(p, fp);
}

/*
 * Set direction flag, which controls whether we accept only incoming
 * packets, only outgoing packets, or both.
 * Note that, depending on the platform, some or all direction arguments
 * might not be supported.
 */
int pcap_setdirection(pcap_t *p, pcap_direction_t d)
{
	if (p->setdirection_op == NULL)
	{
		snprintf(p->errbuf, PCAP_ERRBUF_SIZE,
				 "Setting direction is not implemented on this platform");
		return -1;
	}
	else
		return p->setdirection_op(p, d);
}

int pcap_stats(pcap_t *p, struct pcap_stat *ps)
{
	return p->stats_op(p, ps);
}

static int
pcap_stats_dead(pcap_t *p, struct pcap_stat *ps _U_)
{
	snprintf(p->errbuf, PCAP_ERRBUF_SIZE,
			 "Statistics aren't available from a pcap_open_dead pcap_t");
	return (-1);
}

void pcap_close_common(pcap_t *p)
{
	if (p->buffer != NULL)
		free(p->buffer);
#if !defined(WIN32) && !defined(MSDOS)
	if (p->fd >= 0)
		close(p->fd);
#endif
}

static void
pcap_close_dead(pcap_t *p _U_)
{
	/* Nothing to do. */
}

pcap_t *
pcap_open_dead(int linktype, int snaplen)
{
	pcap_t *p;

	p = malloc(sizeof(*p));
	if (p == NULL)
		return NULL;
	memset(p, 0, sizeof(*p));
	p->snapshot = snaplen;
	p->linktype = linktype;
	p->stats_op = pcap_stats_dead;
	p->close_op = pcap_close_dead;
	return p;
}

/*
 * API compatible with WinPcap's "send a packet" routine - returns -1
 * on error, 0 otherwise.
 *
 * XXX - what if we get a short write?
 */
int pcap_sendpacket(pcap_t *p, const u_char *buf, int size)
{
	if (p->inject_op(p, buf, size) == -1)
		return (-1);
	return (0);
}

/*
 * API compatible with OpenBSD's "send a packet" routine - returns -1 on
 * error, number of bytes written otherwise.
 */
int pcap_inject(pcap_t *p, const void *buf, size_t size)
{
	return (p->inject_op(p, buf, size));
}

void pcap_close(pcap_t *p)
{
	p->close_op(p);
	if (p->dlt_list != NULL)
		free(p->dlt_list);
	pcap_freecode(&p->fcode);
	free(p);
}

/*
 * We make the version string static, and return a pointer to it, rather
 * than exporting the version string directly.  On at least some UNIXes,
 * if you import data from a shared library into an program, the data is
 * bound into the program binary, so if the string in the version of the
 * library with which the program was linked isn't the same as the
 * string in the version of the library with which the program is being
 * run, various undesirable things may happen (warnings, the string
 * being the one from the version of the library with which the program
 * was linked, or even weirder things, such as the string being the one
 * from the library but being truncated).
 */
#ifdef HAVE_VERSION_H
#include "version.h"
#else
static const char pcap_version_string[] = "libpcap version 0.9[.x]";
#endif

#ifdef WIN32
/*
 * XXX - it'd be nice if we could somehow generate the WinPcap and libpcap
 * version numbers when building WinPcap.  (It'd be nice to do so for
 * the packet.dll version number as well.)
 */
static const char wpcap_version_string[] = "3.1";
static const char pcap_version_string_fmt[] =
	"WinPcap version %s, based on %s";
static const char pcap_version_string_packet_dll_fmt[] =
	"WinPcap version %s (packet.dll version %s), based on %s";
static char *full_pcap_version_string;

const char *
pcap_lib_version(void)
{
	char *packet_version_string;
	size_t full_pcap_version_string_len;

	if (full_pcap_version_string == NULL)
	{
		/*
		 * Generate the version string.
		 */
		packet_version_string = PacketGetVersion();
		if (strcmp(wpcap_version_string, packet_version_string) == 0)
		{
			/*
			 * WinPcap version string and packet.dll version
			 * string are the same; just report the WinPcap
			 * version.
			 */
			full_pcap_version_string_len =
				(sizeof pcap_version_string_fmt - 4) +
				strlen(wpcap_version_string) +
				strlen(pcap_version_string);
			full_pcap_version_string =
				malloc(full_pcap_version_string_len);
			sprintf(full_pcap_version_string,
					pcap_version_string_fmt, wpcap_version_string,
					pcap_version_string);
		}
		else
		{
			/*
			 * WinPcap version string and packet.dll version
			 * string are different; that shouldn't be the
			 * case (the two libraries should come from the
			 * same version of WinPcap), so we report both
			 * versions.
			 */
			full_pcap_version_string_len =
				(sizeof pcap_version_string_packet_dll_fmt - 6) +
				strlen(wpcap_version_string) +
				strlen(packet_version_string) +
				strlen(pcap_version_string);
			full_pcap_version_string = malloc(full_pcap_version_string_len);

			sprintf(full_pcap_version_string,
					pcap_version_string_packet_dll_fmt,
					wpcap_version_string, packet_version_string,
					pcap_version_string);
		}
	}
	return (full_pcap_version_string);
}

#elif defined(MSDOS)

static char *full_pcap_version_string;

const char *
pcap_lib_version(void)
{
	char *packet_version_string;
	size_t full_pcap_version_string_len;
	static char dospfx[] = "DOS-";

	if (full_pcap_version_string == NULL)
	{
		/*
		 * Generate the version string.
		 */
		full_pcap_version_string_len =
			sizeof dospfx + strlen(pcap_version_string);
		full_pcap_version_string =
			malloc(full_pcap_version_string_len);
		strcpy(full_pcap_version_string, dospfx);
		strcat(full_pcap_version_string, pcap_version_string);
	}
	return (full_pcap_version_string);
}

#else /* UN*X */

const char *
pcap_lib_version(void)
{
	return (pcap_version_string);
}
#endif
