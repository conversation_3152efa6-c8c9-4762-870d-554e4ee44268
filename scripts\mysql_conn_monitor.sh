#!/bin/bash

# MySQL真实短连接监控脚本
# 用法: ./mysql_conn_monitor.sh [监控时长秒数]

MONITOR_DURATION=${1:-60}  # 默认监控60秒
GENERAL_LOG="/tmp/mysql-logs/general.log"

echo "========================================="
echo "MySQL真实短连接监控"
echo "========================================="
echo "监控时长: ${MONITOR_DURATION}秒"
echo "日志文件: ${GENERAL_LOG}"
echo "开始时间: $(date)"
echo "========================================="

# 检查日志文件是否存在
if [ ! -f "$GENERAL_LOG" ]; then
    echo "错误: 通用日志文件不存在: $GENERAL_LOG"
    echo "请先启用MySQL通用日志:"
    echo "mysql -u root -e \"SET GLOBAL general_log = 'ON'; SET GLOBAL general_log_file = '$GENERAL_LOG';\""
    exit 1
fi

# 获取当前日志文件大小
START_SIZE=$(stat -c%s "$GENERAL_LOG")
START_TIME=$(date +%s)

echo "等待 ${MONITOR_DURATION} 秒..."
sleep $MONITOR_DURATION

# 获取结束时的日志文件大小
END_SIZE=$(stat -c%s "$GENERAL_LOG")
END_TIME=$(date +%s)

# 计算新增的日志内容
NEW_BYTES=$((END_SIZE - START_SIZE))

if [ $NEW_BYTES -le 0 ]; then
    echo "警告: 在监控期间没有新的日志产生"
    exit 1
fi

# 提取监控期间的日志
echo "分析最近 ${MONITOR_DURATION} 秒的连接数据..."
tail -c +$((START_SIZE + 1)) "$GENERAL_LOG" > /tmp/mysql_monitor_temp.log

# 统计连接数据
CONNECT_COUNT=$(grep -c "Connect" /tmp/mysql_monitor_temp.log)
QUIT_COUNT=$(grep -c "Quit" /tmp/mysql_monitor_temp.log)
QUERY_COUNT=$(grep -c "Query" /tmp/mysql_monitor_temp.log)

# 计算速率
ACTUAL_DURATION=$((END_TIME - START_TIME))
CONNECT_RATE=$(echo "scale=1; $CONNECT_COUNT / $ACTUAL_DURATION" | bc -l)
QUIT_RATE=$(echo "scale=1; $QUIT_COUNT / $ACTUAL_DURATION" | bc -l)

echo "========================================="
echo "监控结果统计"
echo "========================================="
echo "实际监控时长: ${ACTUAL_DURATION}秒"
echo "总连接数: $CONNECT_COUNT"
echo "总断开数: $QUIT_COUNT"
echo "总查询数: $QUERY_COUNT"
echo "连接建立速率: ${CONNECT_RATE}/秒"
echo "连接断开速率: ${QUIT_RATE}/秒"
echo "平均每连接查询数: $(echo "scale=1; $QUERY_COUNT / $CONNECT_COUNT" | bc -l 2>/dev/null || echo "0")"

# IP统计
echo ""
echo "========================================="
echo "客户端IP连接统计"
echo "========================================="
grep "Connect" /tmp/mysql_monitor_temp.log | \
    sed -n 's/.*Connect.*@\([^[:space:]]*\).*/\1/p' | \
    sort | uniq -c | sort -nr | head -10 | \
    while read count ip; do
        rate=$(echo "scale=1; $count / $ACTUAL_DURATION" | bc -l)
        printf "%-20s %6d连接 (%5.1f/秒)\n" "$ip" "$count" "$rate"
    done

# 连接持续时间分析（简化版）
echo ""
echo "========================================="
echo "连接模式分析"
echo "========================================="

# 统计短连接（Connect后立即Quit）
SHORT_CONN_PATTERN=$(grep -A2 "Connect" /tmp/mysql_monitor_temp.log | grep -B1 "Quit" | grep "Connect" | wc -l)
echo "疑似短连接数: $SHORT_CONN_PATTERN"

# 统计查询类型
echo ""
echo "查询类型统计:"
grep "Query" /tmp/mysql_monitor_temp.log | \
    awk '{print $NF}' | \
    sort | uniq -c | sort -nr | head -5 | \
    while read count query; do
        printf "%-30s %6d次\n" "$query" "$count"
    done

# 清理临时文件
rm -f /tmp/mysql_monitor_temp.log

echo ""
echo "========================================="
echo "监控完成: $(date)"
echo "========================================="
