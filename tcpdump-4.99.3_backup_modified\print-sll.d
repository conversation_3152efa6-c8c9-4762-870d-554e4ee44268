print-sll.o: print-sll.c config.h diag-control.h compiler-tests.h \
 netdissect-stdinc.h ftmacros.h varattrs.h funcattrs.h netdissect.h \
 status-exit-codes.h ../libpcap/pcap.h ../libpcap/pcap/pcap.h \
 ../libpcap/pcap/funcattrs.h ../libpcap/pcap/compiler-tests.h \
 ../libpcap/pcap/pcap-inttypes.h ../libpcap/pcap/socket.h \
 ../libpcap/pcap/bpf.h ../libpcap/pcap/dlt.h ip.h ip6.h addrtoname.h \
 extract.h ethertype.h
config.h:
diag-control.h:
compiler-tests.h:
netdissect-stdinc.h:
ftmacros.h:
varattrs.h:
funcattrs.h:
netdissect.h:
status-exit-codes.h:
../libpcap/pcap.h:
../libpcap/pcap/pcap.h:
../libpcap/pcap/funcattrs.h:
../libpcap/pcap/compiler-tests.h:
../libpcap/pcap/pcap-inttypes.h:
../libpcap/pcap/socket.h:
../libpcap/pcap/bpf.h:
../libpcap/pcap/dlt.h:
ip.h:
ip6.h:
addrtoname.h:
extract.h:
ethertype.h:
