#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include "send.h"
int sock;
struct sockaddr_in server;
char message[1000], server_reply[2000];
char* port="8080";
const char *server_ip="*************"; 
int get_sock() {

    // 确保提供了端口号和消息
/*
    if (argc < 3) {
        printf("usage: %s <server-ip> <port> <message>\n", argv[0]);
        return 1;
    }
*/
 
    // 创建套接字
    sock = socket(AF_INET, SOCK_STREAM, 0);
    if (sock == -1) {
        printf("Could not create socket\n");
    }
    puts("Socket created");
    return sock;

}

int connect1(){

 
    // 指定服务器端口和IP
    server.sin_family = AF_INET;
    server.sin_port = htons(atoi(port));
 
    // 将IP地址从字符转换为整数
    if (inet_pton(AF_INET, server_ip, &server.sin_addr) <= 0) {
        printf("Invalid address\n");
        return 1;
    }
 
    // 连接到服务器
    if (connect(sock, (struct sockaddr *)&server, sizeof(server)) < 0) {
        printf("Connection failed\n");
        return 1;
    }
    puts("Connected\n");
 
    // 接收服务器回复
/*
    if (recv(sock, server_reply, 2000, 0) < 0) {
        printf("Recv failed\n");
    }
*/
 
//    puts("Server reply:");
 //   puts(server_reply);
    // 关闭连接
      return 0;
}
