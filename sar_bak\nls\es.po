# NLS support for the sysstat package.
# Copyright (C) 1999 Free Software Foundation, Inc.
# This file is distributed under the same license as the sysstat package.
# <PERSON><PERSON><PERSON><PERSON> GODARD <sysstat [at] orange.fr>, 1999.
# <PERSON> <<EMAIL>>, 1999.
# <PERSON>gma<PERSON>í <<EMAIL>>, 2014-2015.
#
msgid ""
msgstr ""
"Project-Id-Version: sysstat 11.1.7\n"
"Report-Msgid-Bugs-To: sysstat <at> orange.fr\n"
"POT-Creation-Date: 2015-09-18 09:57+0200\n"
"PO-Revision-Date: 2015-09-20 11:26+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish <<EMAIL>>\n"
"Language: es\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.4\n"

#: cifsiostat.c:70 mpstat.c:90 pidstat.c:87 iostat.c:86 tapestat.c:89 sar.c:95
#: nfsiostat-sysstat.c:69
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ]\n"
msgstr "Uso: %s [ opciones ] [ <intervalo> [ <iteraciones> ] ]\n"

#: cifsiostat.c:74 nfsiostat-sysstat.c:73
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"
msgstr ""
"Las opciones son:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ] [ --debuginfo ]\n"

#: cifsiostat.c:77 nfsiostat-sysstat.c:76
#, c-format
msgid ""
"Options are:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"
msgstr ""
"Las opciones son:\n"
"[ -h ] [ -k | -m ] [ -t ] [ -V ]\n"

#: sadc.c:88
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
msgstr "Uso: %s [ opciones ] [ <intervalo> [ <iteraciones> ] ] [ <fichero_de_salida> ]\n"

#: sadc.c:91
#, c-format
msgid ""
"Options are:\n"
"[ -C <comment> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"
msgstr ""
"Las opciones son:\n"
"[ -C <comentario> ] [ -D ] [ -F ] [ -L ] [ -V ]\n"
"[ -S { INT | DISK | IPV6 | POWER | SNMP | XDISK | ALL | XALL } ]\n"

#: sadc.c:264
#, c-format
msgid "Cannot write data to system activity file: %s\n"
msgstr "No se pueden escribir datos en el fichero de actividad del sistema: %s\n"

#: sadc.c:560
#, c-format
msgid "Cannot write system activity file header: %s\n"
msgstr "No se puede escribir la cabecera del fichero de actividad del sistema: %s\n"

#: sadc.c:760 sadc.c:769 sadc.c:836 ioconf.c:508 rd_stats.c:69
#: sa_common.c:1273 count.c:118
#, c-format
msgid "Cannot open %s: %s\n"
msgstr "No se puede abrir %s: %s\n"

#: sadc.c:1009
#, c-format
msgid "Cannot append data to that file (%s)\n"
msgstr "No se puede añadir datos al fichero (%s)\n"

#: mpstat.c:93
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -P { <cpu> [,...] | ON | ALL } ]\n"
msgstr ""
"Las opciones son:\n"
"[ -A ] [ -u ] [ -V ] [ -I { SUM | CPU | SCPU | ALL } ]\n"
"[ -P { <cpu> [,...] | ON | ALL } ]\n"

# sar.c:
#: mpstat.c:624 pidstat.c:2280 sar.c:425
msgid "Average:"
msgstr "Media:"

#: mpstat.c:996
#, c-format
msgid "Not that many processors!\n"
msgstr "¡No hay tantos procesadores!\n"

#: sadf.c:86
#, c-format
msgid "Usage: %s [ options ] [ <interval> [ <count> ] ] [ <datafile> | -[0-9]+ ]\n"
msgstr "Uso: %s [ opciones ] [ <intervalo> [ <iteraciones> ] ] [ <fichero_de_datos> | -[0-9]+ ]\n"

#: sadf.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -C ] [ -c | -d | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -P { <cpu> [,...] | ALL } ] [ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
"[ -- <sar_options> ]\n"
msgstr ""
"Las opciones son:\n"
"[ -C ] [ -c | -d | -j | -p | -x ] [ -H ] [ -h ] [ -T | -t | -U ] [ -V ]\n"
"[ -P { <cpu> [,...] | ALL } ] [ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
"[ -- <opciones_de_sar> ]\n"

#: sa_common.c:1069
#, c-format
msgid "Error while reading system activity file: %s\n"
msgstr "Error en la lectura del fichero de actividad del sistema: %s\n"

#: sa_common.c:1079
#, c-format
msgid "End of system activity file unexpected\n"
msgstr "Final inesperado del fichero de actividad del sistema\n"

#: sa_common.c:1098
#, c-format
msgid "File created by sar/sadc from sysstat version %d.%d.%d"
msgstr "Fichero creado por sar/sadc desde sysstat versión %d.%d.%d"

#: sa_common.c:1131
#, c-format
msgid "Invalid system activity file: %s\n"
msgstr "Fichero de actividad del sistema no válido: %s\n"

#: sa_common.c:1143
#, c-format
msgid "Endian format mismatch\n"
msgstr "Formato incompatible de orden de bytes (Endian)\n"

#: sa_common.c:1147
#, c-format
msgid "Current sysstat version cannot read the format of this file (%#x)\n"
msgstr "La versión actual de sysstat no puede leer el formato de este fichero (%#x)\n"

#: sa_common.c:1276
#, c-format
msgid "Please check if data collecting is enabled\n"
msgstr "Por favor, compruebe que el recopilador de datos esté habilitado\n"

#: sa_common.c:1443
#, c-format
msgid "Requested activities not available in file %s\n"
msgstr "Las actividades solicitadas no están disponibles en el fichero %s\n"

#: sa_common.c:1624 iostat.c:1399
#, c-format
msgid "Invalid type of persistent device name\n"
msgstr "Tipo no válido de nombre de dispositivo persistente\n"

#: pidstat.c:90
#, c-format
msgid ""
"Options are:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ] [ -U [ <username> ] ]\n"
"[ -u ] [ -V ] [ -v ] [ -w ] [ -C <command> ] [ -G <process_name> ]\n"
"[ -p { <pid> [,...] | SELF | ALL } ] [ -T { TASK | CHILD | ALL } ]\n"
msgstr ""
"Las opciones son:\n"
"[ -d ] [ -h ] [ -I ] [ -l ] [ -R ] [ -r ] [ -s ] [ -t ] [ -U [ <nombre_de_usuario> ] ]\n"
"[ -u ] [ -V ] [ -v ] [ -w ] [ -C <orden> ] [ -G <nombre_proceso> ]\n"
"[ -p { <pid> [,...] | SELF | ALL } ] [ -T { TASK | CHILD | ALL } ]\n"

#: pidstat.c:239 sar.c:1100
#, c-format
msgid "Requested activities not available\n"
msgstr "Las actividades solicitadas no están disponibles\n"

#: sadf_misc.c:624
#, c-format
msgid "System activity data file: %s (%#x)\n"
msgstr "Fichero de actividad del sistema: %s (%#x)\n"

#: sadf_misc.c:633
#, c-format
msgid "Genuine sa datafile: %s (%x)\n"
msgstr "Fichero de datos sa auténtico: %s (%x)\n"

#: sadf_misc.c:634
msgid "no"
msgstr "no"

#: sadf_misc.c:634
msgid "yes"
msgstr "sí"

#: sadf_misc.c:637
#, c-format
msgid "Host: "
msgstr "Anfitrión: "

#: sadf_misc.c:643
#, c-format
msgid "Number of CPU for last samples in file: %u\n"
msgstr "Número de CPU para las últimas muestras en el fichero: %u\n"

#: sadf_misc.c:649
#, c-format
msgid "File date: %s\n"
msgstr "Fecha del fichero: %s\n"

#: sadf_misc.c:652
#, c-format
msgid "File time: "
msgstr "Fecha del fichero: "

#: sadf_misc.c:657
#, c-format
msgid "Size of a long int: %d\n"
msgstr "Tamaño de un entero largo: %d\n"

#: sadf_misc.c:663
#, c-format
msgid "List of activities:\n"
msgstr "Lista de actividades:\n"

#: sadf_misc.c:676
#, c-format
msgid "\t[Unknown activity format]"
msgstr "\t[Formato de actividad desconocido]"

#: count.c:169
#, c-format
msgid "Cannot handle so many processors!\n"
msgstr "¡No se pueden gestionar tantos procesadores!\n"

#: iostat.c:89
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ] [ --debuginfo ]\n"
msgstr ""
"Las opciones son:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -H ] -g <nombre_grupo> ] [ -p [ <dispositivo> [,...] | ALL ] ]\n"
"[ <dispositivo> [...] | ALL ] [ --debuginfo ]\n"

#: iostat.c:95
#, c-format
msgid ""
"Options are:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -H ] -g <group_name> ] [ -p [ <device> [,...] | ALL ] ]\n"
"[ <device> [...] | ALL ]\n"
msgstr ""
"Las opciones son:\n"
"[ -c ] [ -d ] [ -h ] [ -k | -m ] [ -N ] [ -t ] [ -V ] [ -x ] [ -y ] [ -z ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ [ -H ] -g <nombre_grupo> ] [ -p [ <dispositivo> [,...] | ALL ] ]\n"
"[ <dispositivo> [...] | ALL ]\n"

#: iostat.c:326
#, c-format
msgid "Cannot find disk data\n"
msgstr "No se encuentra el disco de datos\n"

#: common.c:73
#, c-format
msgid "sysstat version %s\n"
msgstr "sysstat versión %s\n"

#: pr_stats.c:2483 pr_stats.c:2495 pr_stats.c:2599 pr_stats.c:2610
msgid "Summary:"
msgstr "Resumen:"

#: pr_stats.c:2538
msgid "Other devices not listed here"
msgstr "Otros dispositivos que no aparecen en esta lista"

#: tapestat.c:91
#, c-format
msgid ""
"Options are:\n"
"[ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"
msgstr ""
"Las opciones son:\n"
"[ -k | -m ] [ -t ] [ -V ] [ -y ] [ -z ]\n"

#: tapestat.c:251
#, c-format
msgid "No tape drives with statistics found\n"
msgstr "No se han encontrado unidades de cinta con estadísticas\n"

#: sar.c:110
#, c-format
msgid ""
"Options are:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ MOUNT ] ] [ -H ] [ -h ]\n"
"[ -p ] [ -q ] [ -R ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --sadc ]\n"
"[ -I { <int> [,...] | SUM | ALL | XALL } ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -m { <keyword> [,...] | ALL } ] [ -n { <keyword> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <filename> ] | -o [ <filename> ] | -[0-9]+ ]\n"
"[ -i <interval> ] [ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"
msgstr ""
"Las opciones son:\n"
"[ -A ] [ -B ] [ -b ] [ -C ] [ -D ] [ -d ] [ -F [ MOUNT ] ] [ -H ] [ -h ]\n"
"[ -p ] [ -q ] [ -R ] [ -r [ ALL ] ] [ -S ] [ -t ] [ -u [ ALL ] ] [ -V ]\n"
"[ -v ] [ -W ] [ -w ] [ -y ] [ --sadc ]\n"
"[ -I { <int> [,...] | SUM | ALL | XALL } ] [ -P { <cpu> [,...] | ALL } ]\n"
"[ -m { <palabra_clave> [,...] | ALL } ] [ -n { <palabra_clave> [,...] | ALL } ]\n"
"[ -j { ID | LABEL | PATH | UUID | ... } ]\n"
"[ -f [ <nombre_fichero> ] | -o [ <nombre_fichero> ] | -[0-9]+ ]\n"
"[ -i <intervalo> ] [ -s [ <hh:mm[:ss]> ] ] [ -e [ <hh:mm[:ss]> ] ]\n"

#: sar.c:133
#, c-format
msgid "Main options and reports:\n"
msgstr "Principales opciones e informes:\n"

#: sar.c:134
#, c-format
msgid "\t-B\tPaging statistics\n"
msgstr "\t-B\tEstadísticas de paginación\n"

#: sar.c:135
#, c-format
msgid "\t-b\tI/O and transfer rate statistics\n"
msgstr "\t-b\tEstadísticas de E/S y velocidad de transferencia\n"

#: sar.c:136
#, c-format
msgid "\t-d\tBlock devices statistics\n"
msgstr "\t-d\tEstadísticas de dispositivos de bloque\n"

#: sar.c:137
#, c-format
msgid "\t-F [ MOUNT ]\n"
msgstr "\t-F [ MOUNT ]\n"

#: sar.c:138
#, c-format
msgid "\t\tFilesystems statistics\n"
msgstr "\t\tEstadísticas del sistema de ficheros\n"

#: sar.c:139
#, c-format
msgid "\t-H\tHugepages utilization statistics\n"
msgstr "\t-H\tEstadísticas de uso de Hugepages\n"

#: sar.c:140
#, c-format
msgid ""
"\t-I { <int> | SUM | ALL | XALL }\n"
"\t\tInterrupts statistics\n"
msgstr ""
"\t-I { <interrupción> | SUM | ALL | XALL }\n"
"\t\tEstadísticas de interrupciones\n"

#: sar.c:142
#, c-format
msgid ""
"\t-m { <keyword> [,...] | ALL }\n"
"\t\tPower management statistics\n"
"\t\tKeywords are:\n"
"\t\tCPU\tCPU instantaneous clock frequency\n"
"\t\tFAN\tFans speed\n"
"\t\tFREQ\tCPU average clock frequency\n"
"\t\tIN\tVoltage inputs\n"
"\t\tTEMP\tDevices temperature\n"
"\t\tUSB\tUSB devices plugged into the system\n"
msgstr ""
"\t-m { <palabra clave> [,...] | ALL }\n"
"\t\tEstadísticas de administración de energía\n"
"\t\tLas palabras clave son:\n"
"\t\tCPU\tFrecuencia de reloj instantánea de la CPU\n"
"\t\tFAN\tVelocidad de los ventiladores\n"
"\t\tFREQ\tFrecuencia de reloj promedia de la CPU\n"
"\t\tIN\tVoltaje de entrada\n"
"\t\tTEMP\tDispositivos de temperatura\n"
"\t\tUSB\tDispositivos USB conectados al sistema\n"

#: sar.c:151
#, c-format
msgid ""
"\t-n { <keyword> [,...] | ALL }\n"
"\t\tNetwork statistics\n"
"\t\tKeywords are:\n"
"\t\tDEV\tNetwork interfaces\n"
"\t\tEDEV\tNetwork interfaces (errors)\n"
"\t\tNFS\tNFS client\n"
"\t\tNFSD\tNFS server\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tIP traffic\t(v4)\n"
"\t\tEIP\tIP traffic\t(v4) (errors)\n"
"\t\tICMP\tICMP traffic\t(v4)\n"
"\t\tEICMP\tICMP traffic\t(v4) (errors)\n"
"\t\tTCP\tTCP traffic\t(v4)\n"
"\t\tETCP\tTCP traffic\t(v4) (errors)\n"
"\t\tUDP\tUDP traffic\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tIP traffic\t(v6)\n"
"\t\tEIP6\tIP traffic\t(v6) (errors)\n"
"\t\tICMP6\tICMP traffic\t(v6)\n"
"\t\tEICMP6\tICMP traffic\t(v6) (errors)\n"
"\t\tUDP6\tUDP traffic\t(v6)\n"
"\t\tFC\tFibre channel HBAs\n"
msgstr ""
"\t-n { <palabra_clave> [,...] | ALL }\n"
"\t\tEstadísticas de red\n"
"\t\tLas palabras clave son:\n"
"\t\tDEV\tInterfaces de red\n"
"\t\tEDEV\tInterfaces de red (errores)\n"
"\t\tNFS\tCliente NFS\n"
"\t\tNFSD\tServidor NFS\n"
"\t\tSOCK\tSockets\t(v4)\n"
"\t\tIP\tTráfico IP\t(v4)\n"
"\t\tEIP\tTráfico IP\t(v4) (errores)\n"
"\t\tICMP\tTráfico ICMP\t(v4)\n"
"\t\tEICMP\tTráfico ICMP\t(v4) (errores)\n"
"\t\tTCP\tTráfico TCP\t(v4)\n"
"\t\tETCP\tTráfico TCP\t(v4) (errores)\n"
"\t\tUDP\tTráfico UDP\t(v4)\n"
"\t\tSOCK6\tSockets\t(v6)\n"
"\t\tIP6\tTráfico IP\t(v6)\n"
"\t\tEIP6\tTráfico IP\t(v6) (errores)\n"
"\t\tICMP6\tTráfico ICMP\t(v6)\n"
"\t\tEICMP6\tTráfico ICMP\t(v6) (errores)\n"
"\t\tUDP6\tTráfico UDP\t(v6)\n"
"\t\tFC\tCanal de fibra HBAs\n"

#: sar.c:173
#, c-format
msgid "\t-q\tQueue length and load average statistics\n"
msgstr "\t-q\tEstadísticas de longitud de cola y promedios de carga\n"

#: sar.c:174
#, c-format
msgid "\t-R\tMemory statistics\n"
msgstr "\t-R\tEstadísticas de memoria\n"

#: sar.c:175
#, c-format
msgid ""
"\t-r [ ALL ]\n"
"\t\tMemory utilization statistics\n"
msgstr ""
"\t-r [ ALL ]\n"
"\t\tEstadísticas de uso de memoria\n"

#: sar.c:177
#, c-format
msgid "\t-S\tSwap space utilization statistics\n"
msgstr "\t-S\tEstadísticas de uso de intercambio de espacio\n"

#: sar.c:178
#, c-format
msgid ""
"\t-u [ ALL ]\n"
"\t\tCPU utilization statistics\n"
msgstr ""
"\t-u [ ALL ]\n"
"\t\tEstadísticas de uso de la CPU\n"

#: sar.c:180
#, c-format
msgid "\t-v\tKernel tables statistics\n"
msgstr "\t-v\tEstadísticas de las tablas del núcleo\n"

#: sar.c:181
#, c-format
msgid "\t-W\tSwapping statistics\n"
msgstr "\t-W\tEstadísticas de intercambio\n"

#: sar.c:182
#, c-format
msgid "\t-w\tTask creation and system switching statistics\n"
msgstr "\t-w\tEstadísticas de creación de tareas y conmutación de sistema\n"

#: sar.c:183
#, c-format
msgid "\t-y\tTTY devices statistics\n"
msgstr "\t-y\tEstadísticas de dispositivos TTY\n"

#: sar.c:197
#, c-format
msgid "Data collector will be sought in PATH\n"
msgstr "Se buscará el colector de datos en el PATH\n"

#: sar.c:200
#, c-format
msgid "Data collector found: %s\n"
msgstr "Encontrado el colector de datos: %s\n"

#: sar.c:259
#, c-format
msgid "End of data collecting unexpected\n"
msgstr "Final inesperado de recolección de datos\n"

#: sar.c:877
#, c-format
msgid "Using a wrong data collector from a different sysstat version\n"
msgstr "Se ha usado un recolector de datos incorrecto de una versión diferente de sysstat\n"

#: sar.c:929
#, c-format
msgid "Inconsistent input data\n"
msgstr "Datos de entrada inconsistentes\n"

#: sar.c:1398
#, c-format
msgid "-f and -o options are mutually exclusive\n"
msgstr "Las opciones -f y -o son mutuamente excluyentes\n"

#: sar.c:1404
#, c-format
msgid "Not reading from a system activity file (use -f option)\n"
msgstr "No se está leyendo de un fichero de actividad del sistema (use la opción -f)\n"

#: sar.c:1540
#, c-format
msgid "Cannot find the data collector (%s)\n"
msgstr "No se puede encontrar el recopilador de datos (%s)\n"

#: sa_conv.c:69
#, c-format
msgid "Cannot convert the format of this file\n"
msgstr "No se puede convertir el formato de este fichero\n"

#: sa_conv.c:562
#, c-format
msgid ""
"\n"
"CPU activity not found in file. Aborting...\n"
msgstr ""
"\n"
"No se ha encontrado actividad de la CPU en el fichero. Abortando…\n"

#: sa_conv.c:578
#, c-format
msgid ""
"\n"
"Invalid data found. Aborting...\n"
msgstr ""
"\n"
"Formato de datos no válido. Abortando…\n"

#: sa_conv.c:897
#, c-format
msgid "Statistics: "
msgstr "Estadísticas:"

#: sa_conv.c:1016
#, c-format
msgid ""
"\n"
"File successfully converted to sysstat format version %s\n"
msgstr ""
"\n"
"Fichero convertido con éxito al formato de la versión %s de sysstat\n"

#~ msgid ""
#~ "Usage: %s [ options... ] [ <interval> [ <count> ] ]\n"
#~ "Options are:\n"
#~ "[ -c ] [ -d ] [ -N ] [ -n ] [ -k | -m ] [ -t ] [ -V ] [ -x ]\n"
#~ "[ <device> [ ... ] | ALL ] [ -p [ <device> | ALL ] ]\n"
#~ msgstr ""
#~ "Uso: %s [ opciones... ] [ <intervalo> [ <número_de_muestras> ] ]\n"
#~ "Opciones posibles:\n"
#~ "[ -c ] [ -d ] [ -N ] [ -n ] [ -k | -m ] [ -t ] [ -V ] [ -x ]\n"
#~ "[ <dispositivo> [ ... ] | ALL ] [ -p [ <dispositivo> | ALL ] ]\n"

#~ msgid "Time: %s\n"
#~ msgstr "Tiempo: %s\n"

#~ msgid "-x and -p options are mutually exclusive\n"
#~ msgstr "-x y -p son opciones mutuamente excluyentes\n"

#~ msgid ""
#~ "Usage: %s [ options... ] [ <interval> [ <count> ] ] [ <outfile> ]\n"
#~ "Options are:\n"
#~ "[ -C <comment> ] [ -d ] [ -F ] [ -I ] [ -V ]\n"
#~ msgstr ""
#~ "Uso: %s [ opciones... ] [ <intervalo> [ <número_de_muestras> ] ] [ <fichero> ]\n"
#~ "Opciones posibles:\n"
#~ "[ -C <comment> ] [ -d ] [ -F ] [ -I ] [ -V ]\n"

#~ msgid "Not an SMP machine...\n"
#~ msgstr "No es un multiprocesador...\n"
