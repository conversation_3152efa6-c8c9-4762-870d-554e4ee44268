

//work for ctrl -c
#include <signal.h>
extern int sar_lock1;
extern int wait1;
extern int wait2;
extern int release1;
//int ths;使用extern
extern struct sigaction int_act;
extern int sigint_caught;

extern int tcp1_lock;

#include <pthread.h>
extern pthread_mutex_t db_mutex;
extern pthread_cond_t dblost;
extern pthread_mutex_t rdb;
extern pthread_cond_t rcond;
extern pthread_mutex_t xxx;
extern pthread_cond_t sar_rcond;

//for tcp1
extern pthread_cond_t tc11;
extern pthread_cond_t cc11;


//add 2024-9-22 tcpdump
extern pthread_mutex_t tcpdump;
extern pthread_cond_t tcp_d;
//int sadc1();
