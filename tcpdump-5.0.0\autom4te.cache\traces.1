m4trace:configure.ac:17: -1- AC_INIT([tcpdump], [m4_esyscmd_s(cat VERSION)], [https://github.com/the-tcpdump-group/tcpdump/issues], [tcpdump], [https://www.tcpdump.org/])
m4trace:configure.ac:17: -1- m4_pattern_forbid([^_?A[CHUM]_])
m4trace:configure.ac:17: -1- m4_pattern_forbid([_AC_])
m4trace:configure.ac:17: -1- m4_pattern_forbid([^LIBOBJS$], [do not use LIBOBJS directly, use AC_LIBOBJ (see section `AC_LIBOBJ vs LIBOBJS'])
m4trace:configure.ac:17: -1- m4_pattern_allow([^AS_FLAGS$])
m4trace:configure.ac:17: -1- m4_pattern_forbid([^_?m4_])
m4trace:configure.ac:17: -1- m4_pattern_forbid([^dnl$])
m4trace:configure.ac:17: -1- m4_pattern_forbid([^_?AS_])
m4trace:configure.ac:17: -1- AC_SUBST([SHELL])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([SHELL])
m4trace:configure.ac:17: -1- m4_pattern_allow([^SHELL$])
m4trace:configure.ac:17: -1- AC_SUBST([PATH_SEPARATOR])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([PATH_SEPARATOR])
m4trace:configure.ac:17: -1- m4_pattern_allow([^PATH_SEPARATOR$])
m4trace:configure.ac:17: -1- AC_SUBST([PACKAGE_NAME], [m4_ifdef([AC_PACKAGE_NAME],      ['AC_PACKAGE_NAME'])])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([PACKAGE_NAME])
m4trace:configure.ac:17: -1- m4_pattern_allow([^PACKAGE_NAME$])
m4trace:configure.ac:17: -1- AC_SUBST([PACKAGE_TARNAME], [m4_ifdef([AC_PACKAGE_TARNAME],   ['AC_PACKAGE_TARNAME'])])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([PACKAGE_TARNAME])
m4trace:configure.ac:17: -1- m4_pattern_allow([^PACKAGE_TARNAME$])
m4trace:configure.ac:17: -1- AC_SUBST([PACKAGE_VERSION], [m4_ifdef([AC_PACKAGE_VERSION],   ['AC_PACKAGE_VERSION'])])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([PACKAGE_VERSION])
m4trace:configure.ac:17: -1- m4_pattern_allow([^PACKAGE_VERSION$])
m4trace:configure.ac:17: -1- AC_SUBST([PACKAGE_STRING], [m4_ifdef([AC_PACKAGE_STRING],    ['AC_PACKAGE_STRING'])])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([PACKAGE_STRING])
m4trace:configure.ac:17: -1- m4_pattern_allow([^PACKAGE_STRING$])
m4trace:configure.ac:17: -1- AC_SUBST([PACKAGE_BUGREPORT], [m4_ifdef([AC_PACKAGE_BUGREPORT], ['AC_PACKAGE_BUGREPORT'])])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([PACKAGE_BUGREPORT])
m4trace:configure.ac:17: -1- m4_pattern_allow([^PACKAGE_BUGREPORT$])
m4trace:configure.ac:17: -1- AC_SUBST([PACKAGE_URL], [m4_ifdef([AC_PACKAGE_URL],       ['AC_PACKAGE_URL'])])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([PACKAGE_URL])
m4trace:configure.ac:17: -1- m4_pattern_allow([^PACKAGE_URL$])
m4trace:configure.ac:17: -1- AC_SUBST([exec_prefix], [NONE])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([exec_prefix])
m4trace:configure.ac:17: -1- m4_pattern_allow([^exec_prefix$])
m4trace:configure.ac:17: -1- AC_SUBST([prefix], [NONE])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([prefix])
m4trace:configure.ac:17: -1- m4_pattern_allow([^prefix$])
m4trace:configure.ac:17: -1- AC_SUBST([program_transform_name], [s,x,x,])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([program_transform_name])
m4trace:configure.ac:17: -1- m4_pattern_allow([^program_transform_name$])
m4trace:configure.ac:17: -1- AC_SUBST([bindir], ['${exec_prefix}/bin'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([bindir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^bindir$])
m4trace:configure.ac:17: -1- AC_SUBST([sbindir], ['${exec_prefix}/sbin'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([sbindir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^sbindir$])
m4trace:configure.ac:17: -1- AC_SUBST([libexecdir], ['${exec_prefix}/libexec'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([libexecdir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^libexecdir$])
m4trace:configure.ac:17: -1- AC_SUBST([datarootdir], ['${prefix}/share'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([datarootdir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^datarootdir$])
m4trace:configure.ac:17: -1- AC_SUBST([datadir], ['${datarootdir}'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([datadir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^datadir$])
m4trace:configure.ac:17: -1- AC_SUBST([sysconfdir], ['${prefix}/etc'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([sysconfdir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^sysconfdir$])
m4trace:configure.ac:17: -1- AC_SUBST([sharedstatedir], ['${prefix}/com'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([sharedstatedir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^sharedstatedir$])
m4trace:configure.ac:17: -1- AC_SUBST([localstatedir], ['${prefix}/var'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([localstatedir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^localstatedir$])
m4trace:configure.ac:17: -1- AC_SUBST([runstatedir], ['${localstatedir}/run'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([runstatedir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^runstatedir$])
m4trace:configure.ac:17: -1- AC_SUBST([includedir], ['${prefix}/include'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([includedir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^includedir$])
m4trace:configure.ac:17: -1- AC_SUBST([oldincludedir], ['/usr/include'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([oldincludedir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^oldincludedir$])
m4trace:configure.ac:17: -1- AC_SUBST([docdir], [m4_ifset([AC_PACKAGE_TARNAME],
				     ['${datarootdir}/doc/${PACKAGE_TARNAME}'],
				     ['${datarootdir}/doc/${PACKAGE}'])])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([docdir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^docdir$])
m4trace:configure.ac:17: -1- AC_SUBST([infodir], ['${datarootdir}/info'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([infodir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^infodir$])
m4trace:configure.ac:17: -1- AC_SUBST([htmldir], ['${docdir}'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([htmldir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^htmldir$])
m4trace:configure.ac:17: -1- AC_SUBST([dvidir], ['${docdir}'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([dvidir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^dvidir$])
m4trace:configure.ac:17: -1- AC_SUBST([pdfdir], ['${docdir}'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([pdfdir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^pdfdir$])
m4trace:configure.ac:17: -1- AC_SUBST([psdir], ['${docdir}'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([psdir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^psdir$])
m4trace:configure.ac:17: -1- AC_SUBST([libdir], ['${exec_prefix}/lib'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([libdir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^libdir$])
m4trace:configure.ac:17: -1- AC_SUBST([localedir], ['${datarootdir}/locale'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([localedir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^localedir$])
m4trace:configure.ac:17: -1- AC_SUBST([mandir], ['${datarootdir}/man'])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([mandir])
m4trace:configure.ac:17: -1- m4_pattern_allow([^mandir$])
m4trace:configure.ac:17: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_NAME])
m4trace:configure.ac:17: -1- m4_pattern_allow([^PACKAGE_NAME$])
m4trace:configure.ac:17: -1- AH_OUTPUT([PACKAGE_NAME], [/* Define to the full name of this package. */
@%:@undef PACKAGE_NAME])
m4trace:configure.ac:17: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_TARNAME])
m4trace:configure.ac:17: -1- m4_pattern_allow([^PACKAGE_TARNAME$])
m4trace:configure.ac:17: -1- AH_OUTPUT([PACKAGE_TARNAME], [/* Define to the one symbol short name of this package. */
@%:@undef PACKAGE_TARNAME])
m4trace:configure.ac:17: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_VERSION])
m4trace:configure.ac:17: -1- m4_pattern_allow([^PACKAGE_VERSION$])
m4trace:configure.ac:17: -1- AH_OUTPUT([PACKAGE_VERSION], [/* Define to the version of this package. */
@%:@undef PACKAGE_VERSION])
m4trace:configure.ac:17: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_STRING])
m4trace:configure.ac:17: -1- m4_pattern_allow([^PACKAGE_STRING$])
m4trace:configure.ac:17: -1- AH_OUTPUT([PACKAGE_STRING], [/* Define to the full name and version of this package. */
@%:@undef PACKAGE_STRING])
m4trace:configure.ac:17: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_BUGREPORT])
m4trace:configure.ac:17: -1- m4_pattern_allow([^PACKAGE_BUGREPORT$])
m4trace:configure.ac:17: -1- AH_OUTPUT([PACKAGE_BUGREPORT], [/* Define to the address where bug reports for this package should be sent. */
@%:@undef PACKAGE_BUGREPORT])
m4trace:configure.ac:17: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_URL])
m4trace:configure.ac:17: -1- m4_pattern_allow([^PACKAGE_URL$])
m4trace:configure.ac:17: -1- AH_OUTPUT([PACKAGE_URL], [/* Define to the home page for this package. */
@%:@undef PACKAGE_URL])
m4trace:configure.ac:17: -1- AC_SUBST([DEFS])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([DEFS])
m4trace:configure.ac:17: -1- m4_pattern_allow([^DEFS$])
m4trace:configure.ac:17: -1- AC_SUBST([ECHO_C])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([ECHO_C])
m4trace:configure.ac:17: -1- m4_pattern_allow([^ECHO_C$])
m4trace:configure.ac:17: -1- AC_SUBST([ECHO_N])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([ECHO_N])
m4trace:configure.ac:17: -1- m4_pattern_allow([^ECHO_N$])
m4trace:configure.ac:17: -1- AC_SUBST([ECHO_T])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([ECHO_T])
m4trace:configure.ac:17: -1- m4_pattern_allow([^ECHO_T$])
m4trace:configure.ac:17: -1- AC_SUBST([LIBS])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.ac:17: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:17: -1- AC_SUBST([build_alias])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([build_alias])
m4trace:configure.ac:17: -1- m4_pattern_allow([^build_alias$])
m4trace:configure.ac:17: -1- AC_SUBST([host_alias])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([host_alias])
m4trace:configure.ac:17: -1- m4_pattern_allow([^host_alias$])
m4trace:configure.ac:17: -1- AC_SUBST([target_alias])
m4trace:configure.ac:17: -1- AC_SUBST_TRACE([target_alias])
m4trace:configure.ac:17: -1- m4_pattern_allow([^target_alias$])
m4trace:configure.ac:25: -1- AC_CANONICAL_HOST
m4trace:configure.ac:25: -1- AC_CANONICAL_BUILD
m4trace:configure.ac:25: -1- AC_REQUIRE_AUX_FILE([config.sub])
m4trace:configure.ac:25: -1- AC_REQUIRE_AUX_FILE([config.guess])
m4trace:configure.ac:25: -1- AC_SUBST([build], [$ac_cv_build])
m4trace:configure.ac:25: -1- AC_SUBST_TRACE([build])
m4trace:configure.ac:25: -1- m4_pattern_allow([^build$])
m4trace:configure.ac:25: -1- AC_SUBST([build_cpu], [$[1]])
m4trace:configure.ac:25: -1- AC_SUBST_TRACE([build_cpu])
m4trace:configure.ac:25: -1- m4_pattern_allow([^build_cpu$])
m4trace:configure.ac:25: -1- AC_SUBST([build_vendor], [$[2]])
m4trace:configure.ac:25: -1- AC_SUBST_TRACE([build_vendor])
m4trace:configure.ac:25: -1- m4_pattern_allow([^build_vendor$])
m4trace:configure.ac:25: -1- AC_SUBST([build_os])
m4trace:configure.ac:25: -1- AC_SUBST_TRACE([build_os])
m4trace:configure.ac:25: -1- m4_pattern_allow([^build_os$])
m4trace:configure.ac:25: -1- AC_SUBST([host], [$ac_cv_host])
m4trace:configure.ac:25: -1- AC_SUBST_TRACE([host])
m4trace:configure.ac:25: -1- m4_pattern_allow([^host$])
m4trace:configure.ac:25: -1- AC_SUBST([host_cpu], [$[1]])
m4trace:configure.ac:25: -1- AC_SUBST_TRACE([host_cpu])
m4trace:configure.ac:25: -1- m4_pattern_allow([^host_cpu$])
m4trace:configure.ac:25: -1- AC_SUBST([host_vendor], [$[2]])
m4trace:configure.ac:25: -1- AC_SUBST_TRACE([host_vendor])
m4trace:configure.ac:25: -1- m4_pattern_allow([^host_vendor$])
m4trace:configure.ac:25: -1- AC_SUBST([host_os])
m4trace:configure.ac:25: -1- AC_SUBST_TRACE([host_os])
m4trace:configure.ac:25: -1- m4_pattern_allow([^host_os$])
m4trace:configure.ac:32: -1- _m4_warn([obsolete], [The macro `AC_PROG_CC_C99' is obsolete.
You should run autoupdate.], [./lib/autoconf/c.m4:1659: AC_PROG_CC_C99 is expanded from...
configure.ac:32: the top level])
m4trace:configure.ac:32: -1- AC_SUBST([CC])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:32: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:32: -1- AC_SUBST([CFLAGS])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([CFLAGS])
m4trace:configure.ac:32: -1- m4_pattern_allow([^CFLAGS$])
m4trace:configure.ac:32: -1- AC_SUBST([LDFLAGS])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([LDFLAGS])
m4trace:configure.ac:32: -1- m4_pattern_allow([^LDFLAGS$])
m4trace:configure.ac:32: -1- AC_SUBST([LIBS])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.ac:32: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:32: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.ac:32: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:32: -1- AC_SUBST([CC])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:32: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:32: -1- AC_SUBST([CC])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:32: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:32: -1- AC_SUBST([CC])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:32: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:32: -1- AC_SUBST([CC])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:32: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:32: -1- AC_SUBST([ac_ct_CC])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([ac_ct_CC])
m4trace:configure.ac:32: -1- m4_pattern_allow([^ac_ct_CC$])
m4trace:configure.ac:32: -1- AC_SUBST([CC])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:32: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:32: -1- AC_SUBST([EXEEXT], [$ac_cv_exeext])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([EXEEXT])
m4trace:configure.ac:32: -1- m4_pattern_allow([^EXEEXT$])
m4trace:configure.ac:32: -1- AC_SUBST([OBJEXT], [$ac_cv_objext])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([OBJEXT])
m4trace:configure.ac:32: -1- m4_pattern_allow([^OBJEXT$])
m4trace:configure.ac:46: -1- AH_OUTPUT([HAVE_RPC_RPC_H], [/* Define to 1 if you have the <rpc/rpc.h> header file. */
@%:@undef HAVE_RPC_RPC_H])
m4trace:configure.ac:46: -1- AH_OUTPUT([HAVE_STDIO_H], [/* Define to 1 if you have the <stdio.h> header file. */
@%:@undef HAVE_STDIO_H])
m4trace:configure.ac:46: -1- AH_OUTPUT([HAVE_STDLIB_H], [/* Define to 1 if you have the <stdlib.h> header file. */
@%:@undef HAVE_STDLIB_H])
m4trace:configure.ac:46: -1- AH_OUTPUT([HAVE_STRING_H], [/* Define to 1 if you have the <string.h> header file. */
@%:@undef HAVE_STRING_H])
m4trace:configure.ac:46: -1- AH_OUTPUT([HAVE_INTTYPES_H], [/* Define to 1 if you have the <inttypes.h> header file. */
@%:@undef HAVE_INTTYPES_H])
m4trace:configure.ac:46: -1- AH_OUTPUT([HAVE_STDINT_H], [/* Define to 1 if you have the <stdint.h> header file. */
@%:@undef HAVE_STDINT_H])
m4trace:configure.ac:46: -1- AH_OUTPUT([HAVE_STRINGS_H], [/* Define to 1 if you have the <strings.h> header file. */
@%:@undef HAVE_STRINGS_H])
m4trace:configure.ac:46: -1- AH_OUTPUT([HAVE_SYS_STAT_H], [/* Define to 1 if you have the <sys/stat.h> header file. */
@%:@undef HAVE_SYS_STAT_H])
m4trace:configure.ac:46: -1- AH_OUTPUT([HAVE_SYS_TYPES_H], [/* Define to 1 if you have the <sys/types.h> header file. */
@%:@undef HAVE_SYS_TYPES_H])
m4trace:configure.ac:46: -1- AH_OUTPUT([HAVE_UNISTD_H], [/* Define to 1 if you have the <unistd.h> header file. */
@%:@undef HAVE_UNISTD_H])
m4trace:configure.ac:46: -1- AC_DEFINE_TRACE_LITERAL([STDC_HEADERS])
m4trace:configure.ac:46: -1- m4_pattern_allow([^STDC_HEADERS$])
m4trace:configure.ac:46: -1- AH_OUTPUT([STDC_HEADERS], [/* Define to 1 if all of the C90 standard headers exist (not just the ones
   required in a freestanding environment). This macro is provided for
   backward compatibility; new code need not use it. */
@%:@undef STDC_HEADERS])
m4trace:configure.ac:46: -1- AC_DEFINE_TRACE_LITERAL([HAVE_RPC_RPC_H])
m4trace:configure.ac:46: -1- m4_pattern_allow([^HAVE_RPC_RPC_H$])
m4trace:configure.ac:46: -1- AH_OUTPUT([HAVE_RPC_RPCENT_H], [/* Define to 1 if you have the <rpc/rpcent.h> header file. */
@%:@undef HAVE_RPC_RPCENT_H])
m4trace:configure.ac:46: -1- AC_DEFINE_TRACE_LITERAL([HAVE_RPC_RPCENT_H])
m4trace:configure.ac:46: -1- m4_pattern_allow([^HAVE_RPC_RPCENT_H$])
m4trace:configure.ac:55: -1- AC_DEFINE_TRACE_LITERAL([SIZEOF_VOID_P])
m4trace:configure.ac:55: -1- m4_pattern_allow([^SIZEOF_VOID_P$])
m4trace:configure.ac:55: -1- AH_OUTPUT([SIZEOF_VOID_P], [/* The size of `void *\', as computed by sizeof. */
@%:@undef SIZEOF_VOID_P])
m4trace:configure.ac:60: -1- AC_DEFINE_TRACE_LITERAL([SIZEOF_TIME_T])
m4trace:configure.ac:60: -1- m4_pattern_allow([^SIZEOF_TIME_T$])
m4trace:configure.ac:60: -1- AH_OUTPUT([SIZEOF_TIME_T], [/* The size of `time_t\', as computed by sizeof. */
@%:@undef SIZEOF_TIME_T])
m4trace:configure.ac:95: -1- m4_pattern_forbid([^_?PKG_[A-Z_]+$])
m4trace:configure.ac:95: -1- m4_pattern_allow([^PKG_CONFIG(_(PATH|LIBDIR|SYSROOT_DIR|ALLOW_SYSTEM_(CFLAGS|LIBS)))?$])
m4trace:configure.ac:95: -1- m4_pattern_allow([^PKG_CONFIG_(DISABLE_UNINSTALLED|TOP_BUILD_DIR|DEBUG_SPEW)$])
m4trace:configure.ac:95: -1- AC_SUBST([PKG_CONFIG])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([PKG_CONFIG])
m4trace:configure.ac:95: -1- m4_pattern_allow([^PKG_CONFIG$])
m4trace:configure.ac:95: -1- AC_SUBST([PKG_CONFIG_PATH])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([PKG_CONFIG_PATH])
m4trace:configure.ac:95: -1- m4_pattern_allow([^PKG_CONFIG_PATH$])
m4trace:configure.ac:95: -1- AC_SUBST([PKG_CONFIG_LIBDIR])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([PKG_CONFIG_LIBDIR])
m4trace:configure.ac:95: -1- m4_pattern_allow([^PKG_CONFIG_LIBDIR$])
m4trace:configure.ac:95: -1- AC_SUBST([PKG_CONFIG])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([PKG_CONFIG])
m4trace:configure.ac:95: -1- m4_pattern_allow([^PKG_CONFIG$])
m4trace:configure.ac:102: -1- AC_SUBST([BREW])
m4trace:configure.ac:102: -1- AC_SUBST_TRACE([BREW])
m4trace:configure.ac:102: -1- m4_pattern_allow([^BREW$])
m4trace:configure.ac:113: -1- AC_DEFINE_TRACE_LITERAL([USE_LIBSMI])
m4trace:configure.ac:113: -1- m4_pattern_allow([^USE_LIBSMI$])
m4trace:configure.ac:113: -1- AH_OUTPUT([USE_LIBSMI], [/* Define if you enable support for libsmi */
@%:@undef USE_LIBSMI])
m4trace:configure.ac:192: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_INSTRUMENT_FUNCTIONS])
m4trace:configure.ac:192: -1- m4_pattern_allow([^ENABLE_INSTRUMENT_FUNCTIONS$])
m4trace:configure.ac:192: -1- AH_OUTPUT([ENABLE_INSTRUMENT_FUNCTIONS], [/* define if you want to build the instrument functions code */
@%:@undef ENABLE_INSTRUMENT_FUNCTIONS])
m4trace:configure.ac:214: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_SMB])
m4trace:configure.ac:214: -1- m4_pattern_allow([^ENABLE_SMB$])
m4trace:configure.ac:214: -1- AH_OUTPUT([ENABLE_SMB], [/* define if you want to build the possibly-buggy SMB printer */
@%:@undef ENABLE_SMB])
m4trace:configure.ac:230: -1- AC_DEFINE_TRACE_LITERAL([WITH_USER])
m4trace:configure.ac:230: -1- m4_pattern_allow([^WITH_USER$])
m4trace:configure.ac:230: -1- AH_OUTPUT([WITH_USER], [/* define if should drop privileges by default */
@%:@undef WITH_USER])
m4trace:configure.ac:249: -1- AC_DEFINE_TRACE_LITERAL([WITH_CHROOT])
m4trace:configure.ac:249: -1- m4_pattern_allow([^WITH_CHROOT$])
m4trace:configure.ac:249: -1- AH_OUTPUT([WITH_CHROOT], [/* define if should chroot when dropping privileges */
@%:@undef WITH_CHROOT])
m4trace:configure.ac:285: -1- AH_OUTPUT([HAVE_CAP_ENTER], [/* Define to 1 if you have the `cap_enter\' function. */
@%:@undef HAVE_CAP_ENTER])
m4trace:configure.ac:285: -1- AH_OUTPUT([HAVE_CAP_RIGHTS_LIMIT], [/* Define to 1 if you have the `cap_rights_limit\' function. */
@%:@undef HAVE_CAP_RIGHTS_LIMIT])
m4trace:configure.ac:285: -1- AH_OUTPUT([HAVE_CAP_IOCTLS_LIMIT], [/* Define to 1 if you have the `cap_ioctls_limit\' function. */
@%:@undef HAVE_CAP_IOCTLS_LIMIT])
m4trace:configure.ac:285: -1- AH_OUTPUT([HAVE_OPENAT], [/* Define to 1 if you have the `openat\' function. */
@%:@undef HAVE_OPENAT])
m4trace:configure.ac:299: -1- AC_DEFINE_TRACE_LITERAL([HAVE_CAPSICUM])
m4trace:configure.ac:299: -1- m4_pattern_allow([^HAVE_CAPSICUM$])
m4trace:configure.ac:299: -1- AH_OUTPUT([HAVE_CAPSICUM], [/* capsicum support available */
@%:@undef HAVE_CAPSICUM])
m4trace:configure.ac:306: -1- AC_DEFINE_TRACE_LITERAL([HAVE_CASPER])
m4trace:configure.ac:306: -1- m4_pattern_allow([^HAVE_CASPER$])
m4trace:configure.ac:306: -1- AH_OUTPUT([HAVE_CASPER], [/* Casper support available */
@%:@undef HAVE_CASPER])
m4trace:configure.ac:321: -1- AH_OUTPUT([HAVE_STRLCAT], [/* Define to 1 if you have the `strlcat\' function. */
@%:@undef HAVE_STRLCAT])
m4trace:configure.ac:321: -1- AC_LIBSOURCE([strlcat.c])
m4trace:configure.ac:321: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRLCAT])
m4trace:configure.ac:321: -1- m4_pattern_allow([^HAVE_STRLCAT$])
m4trace:configure.ac:321: -1- AC_SUBST([LIB@&t@OBJS], ["$LIB@&t@OBJS strlcat.$ac_objext"])
m4trace:configure.ac:321: -1- AC_SUBST_TRACE([LIB@&t@OBJS])
m4trace:configure.ac:321: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.ac:321: -1- AH_OUTPUT([HAVE_STRLCPY], [/* Define to 1 if you have the `strlcpy\' function. */
@%:@undef HAVE_STRLCPY])
m4trace:configure.ac:321: -1- AC_LIBSOURCE([strlcpy.c])
m4trace:configure.ac:321: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRLCPY])
m4trace:configure.ac:321: -1- m4_pattern_allow([^HAVE_STRLCPY$])
m4trace:configure.ac:321: -1- AC_SUBST([LIB@&t@OBJS], ["$LIB@&t@OBJS strlcpy.$ac_objext"])
m4trace:configure.ac:321: -1- AC_SUBST_TRACE([LIB@&t@OBJS])
m4trace:configure.ac:321: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.ac:321: -1- AH_OUTPUT([HAVE_STRSEP], [/* Define to 1 if you have the `strsep\' function. */
@%:@undef HAVE_STRSEP])
m4trace:configure.ac:321: -1- AC_LIBSOURCE([strsep.c])
m4trace:configure.ac:321: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRSEP])
m4trace:configure.ac:321: -1- m4_pattern_allow([^HAVE_STRSEP$])
m4trace:configure.ac:321: -1- AC_SUBST([LIB@&t@OBJS], ["$LIB@&t@OBJS strsep.$ac_objext"])
m4trace:configure.ac:321: -1- AC_SUBST_TRACE([LIB@&t@OBJS])
m4trace:configure.ac:321: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.ac:321: -1- AH_OUTPUT([HAVE_GETSERVENT], [/* Define to 1 if you have the `getservent\' function. */
@%:@undef HAVE_GETSERVENT])
m4trace:configure.ac:321: -1- AC_LIBSOURCE([getservent.c])
m4trace:configure.ac:321: -1- AC_DEFINE_TRACE_LITERAL([HAVE_GETSERVENT])
m4trace:configure.ac:321: -1- m4_pattern_allow([^HAVE_GETSERVENT$])
m4trace:configure.ac:321: -1- AC_SUBST([LIB@&t@OBJS], ["$LIB@&t@OBJS getservent.$ac_objext"])
m4trace:configure.ac:321: -1- AC_SUBST_TRACE([LIB@&t@OBJS])
m4trace:configure.ac:321: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.ac:321: -1- AH_OUTPUT([HAVE_GETOPT_LONG], [/* Define to 1 if you have the `getopt_long\' function. */
@%:@undef HAVE_GETOPT_LONG])
m4trace:configure.ac:321: -1- AC_LIBSOURCE([getopt_long.c])
m4trace:configure.ac:321: -1- AC_DEFINE_TRACE_LITERAL([HAVE_GETOPT_LONG])
m4trace:configure.ac:321: -1- m4_pattern_allow([^HAVE_GETOPT_LONG$])
m4trace:configure.ac:321: -1- AC_SUBST([LIB@&t@OBJS], ["$LIB@&t@OBJS getopt_long.$ac_objext"])
m4trace:configure.ac:321: -1- AC_SUBST_TRACE([LIB@&t@OBJS])
m4trace:configure.ac:321: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.ac:322: -1- AH_OUTPUT([HAVE_FORK], [/* Define to 1 if you have the `fork\' function. */
@%:@undef HAVE_FORK])
m4trace:configure.ac:322: -1- AC_DEFINE_TRACE_LITERAL([HAVE_FORK])
m4trace:configure.ac:322: -1- m4_pattern_allow([^HAVE_FORK$])
m4trace:configure.ac:322: -1- AH_OUTPUT([HAVE_VFORK], [/* Define to 1 if you have the `vfork\' function. */
@%:@undef HAVE_VFORK])
m4trace:configure.ac:322: -1- AC_DEFINE_TRACE_LITERAL([HAVE_VFORK])
m4trace:configure.ac:322: -1- m4_pattern_allow([^HAVE_VFORK$])
m4trace:configure.ac:412: -1- AH_OUTPUT([HAVE_LIBRPC], [/* Define to 1 if you have the `rpc\' library (-lrpc). */
@%:@undef HAVE_LIBRPC])
m4trace:configure.ac:412: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LIBRPC])
m4trace:configure.ac:412: -1- m4_pattern_allow([^HAVE_LIBRPC$])
m4trace:configure.ac:416: -2- AC_DEFINE_TRACE_LITERAL([HAVE_GETRPCBYNUMBER])
m4trace:configure.ac:416: -2- m4_pattern_allow([^HAVE_GETRPCBYNUMBER$])
m4trace:configure.ac:416: -2- AH_OUTPUT([HAVE_GETRPCBYNUMBER], [/* define if you have getrpcbynumber() */
@%:@undef HAVE_GETRPCBYNUMBER])
m4trace:configure.ac:418: -1- AC_SUBST([GREP])
m4trace:configure.ac:418: -1- AC_SUBST_TRACE([GREP])
m4trace:configure.ac:418: -1- m4_pattern_allow([^GREP$])
m4trace:configure.ac:418: -1- AC_SUBST([EGREP])
m4trace:configure.ac:418: -1- AC_SUBST_TRACE([EGREP])
m4trace:configure.ac:418: -1- m4_pattern_allow([^EGREP$])
m4trace:configure.ac:418: -1- AC_SUBST([LIBPCAP_CFLAGS])
m4trace:configure.ac:418: -1- AC_SUBST_TRACE([LIBPCAP_CFLAGS])
m4trace:configure.ac:418: -1- m4_pattern_allow([^LIBPCAP_CFLAGS$])
m4trace:configure.ac:418: -1- AC_SUBST([LIBPCAP_LIBS])
m4trace:configure.ac:418: -1- AC_SUBST_TRACE([LIBPCAP_LIBS])
m4trace:configure.ac:418: -1- m4_pattern_allow([^LIBPCAP_LIBS$])
m4trace:configure.ac:418: -1- AC_SUBST([LIBPCAP_LIBS_STATIC])
m4trace:configure.ac:418: -1- AC_SUBST_TRACE([LIBPCAP_LIBS_STATIC])
m4trace:configure.ac:418: -1- m4_pattern_allow([^LIBPCAP_LIBS_STATIC$])
m4trace:configure.ac:418: -1- AC_SUBST([PCAP_CONFIG])
m4trace:configure.ac:418: -1- AC_SUBST_TRACE([PCAP_CONFIG])
m4trace:configure.ac:418: -1- m4_pattern_allow([^PCAP_CONFIG$])
m4trace:configure.ac:418: -1- AC_DEFINE_TRACE_LITERAL([SIZEOF_VOID_P])
m4trace:configure.ac:418: -1- m4_pattern_allow([^SIZEOF_VOID_P$])
m4trace:configure.ac:418: -1- AH_OUTPUT([SIZEOF_VOID_P], [/* The size of `void *\', as computed by sizeof. */
@%:@undef SIZEOF_VOID_P])
m4trace:configure.ac:418: -1- AC_SUBST([PCAP_CONFIG])
m4trace:configure.ac:418: -1- AC_SUBST_TRACE([PCAP_CONFIG])
m4trace:configure.ac:418: -1- m4_pattern_allow([^PCAP_CONFIG$])
m4trace:configure.ac:418: -1- AC_DEFINE_TRACE_LITERAL([SIZEOF_VOID_P])
m4trace:configure.ac:418: -1- m4_pattern_allow([^SIZEOF_VOID_P$])
m4trace:configure.ac:418: -1- AH_OUTPUT([SIZEOF_VOID_P], [/* The size of `void *\', as computed by sizeof. */
@%:@undef SIZEOF_VOID_P])
m4trace:configure.ac:418: -1- AC_SUBST([PCAP_CONFIG])
m4trace:configure.ac:418: -1- AC_SUBST_TRACE([PCAP_CONFIG])
m4trace:configure.ac:418: -1- m4_pattern_allow([^PCAP_CONFIG$])
m4trace:configure.ac:435: -1- AH_OUTPUT([HAVE_ETHER_NTOHOST], [/* Define to 1 if you have the `ether_ntohost\' function. */
@%:@undef HAVE_ETHER_NTOHOST])
m4trace:configure.ac:435: -1- AC_DEFINE_TRACE_LITERAL([HAVE_ETHER_NTOHOST])
m4trace:configure.ac:435: -1- m4_pattern_allow([^HAVE_ETHER_NTOHOST$])
m4trace:configure.ac:435: -1- AC_DEFINE_TRACE_LITERAL([NET_ETHERNET_H_DECLARES_ETHER_NTOHOST])
m4trace:configure.ac:435: -1- m4_pattern_allow([^NET_ETHERNET_H_DECLARES_ETHER_NTOHOST$])
m4trace:configure.ac:435: -1- AH_OUTPUT([NET_ETHERNET_H_DECLARES_ETHER_NTOHOST], [/* Define to 1 if net/ethernet.h declares `ether_ntohost\' */
@%:@undef NET_ETHERNET_H_DECLARES_ETHER_NTOHOST])
m4trace:configure.ac:435: -1- AC_DEFINE_TRACE_LITERAL([NETINET_ETHER_H_DECLARES_ETHER_NTOHOST])
m4trace:configure.ac:435: -1- m4_pattern_allow([^NETINET_ETHER_H_DECLARES_ETHER_NTOHOST$])
m4trace:configure.ac:435: -1- AH_OUTPUT([NETINET_ETHER_H_DECLARES_ETHER_NTOHOST], [/* Define to 1 if netinet/ether.h declares `ether_ntohost\' */
@%:@undef NETINET_ETHER_H_DECLARES_ETHER_NTOHOST])
m4trace:configure.ac:435: -1- AC_DEFINE_TRACE_LITERAL([SYS_ETHERNET_H_DECLARES_ETHER_NTOHOST])
m4trace:configure.ac:435: -1- m4_pattern_allow([^SYS_ETHERNET_H_DECLARES_ETHER_NTOHOST$])
m4trace:configure.ac:435: -1- AH_OUTPUT([SYS_ETHERNET_H_DECLARES_ETHER_NTOHOST], [/* Define to 1 if sys/ethernet.h declares `ether_ntohost\' */
@%:@undef SYS_ETHERNET_H_DECLARES_ETHER_NTOHOST])
m4trace:configure.ac:435: -1- AC_DEFINE_TRACE_LITERAL([ARPA_INET_H_DECLARES_ETHER_NTOHOST])
m4trace:configure.ac:435: -1- m4_pattern_allow([^ARPA_INET_H_DECLARES_ETHER_NTOHOST$])
m4trace:configure.ac:435: -1- AH_OUTPUT([ARPA_INET_H_DECLARES_ETHER_NTOHOST], [/* Define to 1 if arpa/inet.h declares `ether_ntohost\' */
@%:@undef ARPA_INET_H_DECLARES_ETHER_NTOHOST])
m4trace:configure.ac:435: -1- AC_DEFINE_TRACE_LITERAL([NETINET_IF_ETHER_H_DECLARES_ETHER_NTOHOST])
m4trace:configure.ac:435: -1- m4_pattern_allow([^NETINET_IF_ETHER_H_DECLARES_ETHER_NTOHOST$])
m4trace:configure.ac:435: -1- AH_OUTPUT([NETINET_IF_ETHER_H_DECLARES_ETHER_NTOHOST], [/* Define to 1 if netinet/if_ether.h declares `ether_ntohost\' */
@%:@undef NETINET_IF_ETHER_H_DECLARES_ETHER_NTOHOST])
m4trace:configure.ac:435: -1- AC_DEFINE_TRACE_LITERAL([HAVE_DECL_ETHER_NTOHOST])
m4trace:configure.ac:435: -1- m4_pattern_allow([^HAVE_DECL_ETHER_NTOHOST$])
m4trace:configure.ac:435: -1- AH_OUTPUT([HAVE_DECL_ETHER_NTOHOST], [/* Define to 1 if you have the declaration of `ether_ntohost\' */
@%:@undef HAVE_DECL_ETHER_NTOHOST])
m4trace:configure.ac:435: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRUCT_ETHER_ADDR])
m4trace:configure.ac:435: -1- m4_pattern_allow([^HAVE_STRUCT_ETHER_ADDR$])
m4trace:configure.ac:435: -1- AH_OUTPUT([HAVE_STRUCT_ETHER_ADDR], [/* Define to 1 if the system has the type `struct ether_addr\'. */
@%:@undef HAVE_STRUCT_ETHER_ADDR])
m4trace:configure.ac:435: -1- AC_DEFINE_TRACE_LITERAL([USE_ETHER_NTOHOST])
m4trace:configure.ac:435: -1- m4_pattern_allow([^USE_ETHER_NTOHOST$])
m4trace:configure.ac:435: -1- AH_OUTPUT([USE_ETHER_NTOHOST], [/* define if you have ether_ntohost() and it works */
@%:@undef USE_ETHER_NTOHOST])
m4trace:configure.ac:682: -1- AH_OUTPUT([HAVE_PCAP_SET_TSTAMP_TYPE], [/* Define to 1 if you have the `pcap_set_tstamp_type\' function. */
@%:@undef HAVE_PCAP_SET_TSTAMP_TYPE])
m4trace:configure.ac:682: -1- AC_DEFINE_TRACE_LITERAL([HAVE_PCAP_SET_TSTAMP_TYPE])
m4trace:configure.ac:682: -1- m4_pattern_allow([^HAVE_PCAP_SET_TSTAMP_TYPE$])
m4trace:configure.ac:687: -1- AH_OUTPUT([HAVE_PCAP_SET_TSTAMP_PRECISION], [/* Define to 1 if you have the `pcap_set_tstamp_precision\' function. */
@%:@undef HAVE_PCAP_SET_TSTAMP_PRECISION])
m4trace:configure.ac:687: -1- AC_DEFINE_TRACE_LITERAL([HAVE_PCAP_SET_TSTAMP_PRECISION])
m4trace:configure.ac:687: -1- m4_pattern_allow([^HAVE_PCAP_SET_TSTAMP_PRECISION$])
m4trace:configure.ac:693: -1- AH_OUTPUT([HAVE_PCAP_SET_IMMEDIATE_MODE], [/* Define to 1 if you have the `pcap_set_immediate_mode\' function. */
@%:@undef HAVE_PCAP_SET_IMMEDIATE_MODE])
m4trace:configure.ac:693: -1- AC_DEFINE_TRACE_LITERAL([HAVE_PCAP_SET_IMMEDIATE_MODE])
m4trace:configure.ac:693: -1- m4_pattern_allow([^HAVE_PCAP_SET_IMMEDIATE_MODE$])
m4trace:configure.ac:693: -1- AH_OUTPUT([HAVE_PCAP_DUMP_FTELL64], [/* Define to 1 if you have the `pcap_dump_ftell64\' function. */
@%:@undef HAVE_PCAP_DUMP_FTELL64])
m4trace:configure.ac:693: -1- AC_DEFINE_TRACE_LITERAL([HAVE_PCAP_DUMP_FTELL64])
m4trace:configure.ac:693: -1- m4_pattern_allow([^HAVE_PCAP_DUMP_FTELL64$])
m4trace:configure.ac:700: -1- AH_OUTPUT([HAVE_PCAP_OPEN], [/* Define to 1 if you have the `pcap_open\' function. */
@%:@undef HAVE_PCAP_OPEN])
m4trace:configure.ac:700: -1- AC_DEFINE_TRACE_LITERAL([HAVE_PCAP_OPEN])
m4trace:configure.ac:700: -1- m4_pattern_allow([^HAVE_PCAP_OPEN$])
m4trace:configure.ac:700: -1- AH_OUTPUT([HAVE_PCAP_FINDALLDEVS_EX], [/* Define to 1 if you have the `pcap_findalldevs_ex\' function. */
@%:@undef HAVE_PCAP_FINDALLDEVS_EX])
m4trace:configure.ac:700: -1- AC_DEFINE_TRACE_LITERAL([HAVE_PCAP_FINDALLDEVS_EX])
m4trace:configure.ac:700: -1- m4_pattern_allow([^HAVE_PCAP_FINDALLDEVS_EX$])
m4trace:configure.ac:706: -1- AH_OUTPUT([HAVE_PCAP_SET_PARSER_DEBUG], [/* Define to 1 if you have the `pcap_set_parser_debug\' function. */
@%:@undef HAVE_PCAP_SET_PARSER_DEBUG])
m4trace:configure.ac:706: -1- AC_DEFINE_TRACE_LITERAL([HAVE_PCAP_SET_PARSER_DEBUG])
m4trace:configure.ac:706: -1- m4_pattern_allow([^HAVE_PCAP_SET_PARSER_DEBUG$])
m4trace:configure.ac:723: -1- AC_DEFINE_TRACE_LITERAL([HAVE_PCAP_DEBUG])
m4trace:configure.ac:723: -1- m4_pattern_allow([^HAVE_PCAP_DEBUG$])
m4trace:configure.ac:723: -1- AH_OUTPUT([HAVE_PCAP_DEBUG], [/* define if libpcap has pcap_debug */
@%:@undef HAVE_PCAP_DEBUG])
m4trace:configure.ac:740: -1- AC_DEFINE_TRACE_LITERAL([HAVE_YYDEBUG])
m4trace:configure.ac:740: -1- m4_pattern_allow([^HAVE_YYDEBUG$])
m4trace:configure.ac:740: -1- AH_OUTPUT([HAVE_YYDEBUG], [/* define if libpcap has yydebug */
@%:@undef HAVE_YYDEBUG])
m4trace:configure.ac:746: -1- AH_OUTPUT([HAVE_PCAP_SET_OPTIMIZER_DEBUG], [/* Define to 1 if you have the `pcap_set_optimizer_debug\' function. */
@%:@undef HAVE_PCAP_SET_OPTIMIZER_DEBUG])
m4trace:configure.ac:746: -1- AC_DEFINE_TRACE_LITERAL([HAVE_PCAP_SET_OPTIMIZER_DEBUG])
m4trace:configure.ac:746: -1- m4_pattern_allow([^HAVE_PCAP_SET_OPTIMIZER_DEBUG$])
m4trace:configure.ac:754: -1- AH_OUTPUT([HAVE_BPF_DUMP], [/* Define to 1 if you have the `bpf_dump\' function. */
@%:@undef HAVE_BPF_DUMP])
m4trace:configure.ac:754: -1- AC_LIBSOURCE([bpf_dump.c])
m4trace:configure.ac:754: -1- AC_DEFINE_TRACE_LITERAL([HAVE_BPF_DUMP])
m4trace:configure.ac:754: -1- m4_pattern_allow([^HAVE_BPF_DUMP$])
m4trace:configure.ac:754: -1- AC_SUBST([LIB@&t@OBJS], ["$LIB@&t@OBJS bpf_dump.$ac_objext"])
m4trace:configure.ac:754: -1- AC_SUBST_TRACE([LIB@&t@OBJS])
m4trace:configure.ac:754: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.ac:788: -1- AH_OUTPUT([HAVE_PCAP_PCAP_INTTYPES_H], [/* Define to 1 if you have the <pcap/pcap-inttypes.h> header file. */
@%:@undef HAVE_PCAP_PCAP_INTTYPES_H])
m4trace:configure.ac:788: -1- AC_DEFINE_TRACE_LITERAL([HAVE_PCAP_PCAP_INTTYPES_H])
m4trace:configure.ac:788: -1- m4_pattern_allow([^HAVE_PCAP_PCAP_INTTYPES_H$])
m4trace:configure.ac:796: -1- AC_DEFINE_TRACE_LITERAL([u_int8_t])
m4trace:configure.ac:796: -1- m4_pattern_allow([^u_int8_t$])
m4trace:configure.ac:796: -1- AH_OUTPUT([u_int8_t], [/* Define to `uint8_t\' if u_int8_t not defined. */
@%:@undef u_int8_t])
m4trace:configure.ac:802: -1- AC_DEFINE_TRACE_LITERAL([u_int16_t])
m4trace:configure.ac:802: -1- m4_pattern_allow([^u_int16_t$])
m4trace:configure.ac:802: -1- AH_OUTPUT([u_int16_t], [/* Define to `uint16_t\' if u_int16_t not defined. */
@%:@undef u_int16_t])
m4trace:configure.ac:808: -1- AC_DEFINE_TRACE_LITERAL([u_int32_t])
m4trace:configure.ac:808: -1- m4_pattern_allow([^u_int32_t$])
m4trace:configure.ac:808: -1- AH_OUTPUT([u_int32_t], [/* Define to `uint32_t\' if u_int32_t not defined. */
@%:@undef u_int32_t])
m4trace:configure.ac:814: -1- AC_DEFINE_TRACE_LITERAL([u_int64_t])
m4trace:configure.ac:814: -1- m4_pattern_allow([^u_int64_t$])
m4trace:configure.ac:814: -1- AH_OUTPUT([u_int64_t], [/* Define to `uint64_t\' if u_int64_t not defined. */
@%:@undef u_int64_t])
m4trace:configure.ac:821: -1- AC_SUBST([RANLIB])
m4trace:configure.ac:821: -1- AC_SUBST_TRACE([RANLIB])
m4trace:configure.ac:821: -1- m4_pattern_allow([^RANLIB$])
m4trace:configure.ac:822: -1- AC_SUBST([AR])
m4trace:configure.ac:822: -1- AC_SUBST_TRACE([AR])
m4trace:configure.ac:822: -1- m4_pattern_allow([^AR$])
m4trace:configure.ac:824: -1- AC_SUBST([DEPENDENCY_CFLAG])
m4trace:configure.ac:824: -1- AC_SUBST_TRACE([DEPENDENCY_CFLAG])
m4trace:configure.ac:824: -1- m4_pattern_allow([^DEPENDENCY_CFLAG$])
m4trace:configure.ac:824: -1- AC_SUBST([MKDEP])
m4trace:configure.ac:824: -1- AC_SUBST_TRACE([MKDEP])
m4trace:configure.ac:824: -1- m4_pattern_allow([^MKDEP$])
m4trace:configure.ac:824: -1- AC_DEFINE_TRACE_LITERAL([HAVE_OS_PROTO_H])
m4trace:configure.ac:824: -1- m4_pattern_allow([^HAVE_OS_PROTO_H$])
m4trace:configure.ac:824: -1- AH_OUTPUT([HAVE_OS_PROTO_H], [/* if there\'s an os-proto.h for this platform, to use additional prototypes */
@%:@undef HAVE_OS_PROTO_H])
m4trace:configure.ac:890: -1- AC_SUBST([LIBCRYPTO_CFLAGS])
m4trace:configure.ac:890: -1- AC_SUBST_TRACE([LIBCRYPTO_CFLAGS])
m4trace:configure.ac:890: -1- m4_pattern_allow([^LIBCRYPTO_CFLAGS$])
m4trace:configure.ac:890: -1- AC_SUBST([LIBCRYPTO_LIBS])
m4trace:configure.ac:890: -1- AC_SUBST_TRACE([LIBCRYPTO_LIBS])
m4trace:configure.ac:890: -1- m4_pattern_allow([^LIBCRYPTO_LIBS$])
m4trace:configure.ac:890: -1- AC_SUBST([LIBCRYPTO_LIBS_STATIC])
m4trace:configure.ac:890: -1- AC_SUBST_TRACE([LIBCRYPTO_LIBS_STATIC])
m4trace:configure.ac:890: -1- m4_pattern_allow([^LIBCRYPTO_LIBS_STATIC$])
m4trace:configure.ac:1037: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LIBCRYPTO])
m4trace:configure.ac:1037: -1- m4_pattern_allow([^HAVE_LIBCRYPTO$])
m4trace:configure.ac:1037: -1- AH_OUTPUT([HAVE_LIBCRYPTO], [/* Define to 1 if you have a usable `crypto\' library (-lcrypto). */
@%:@undef HAVE_LIBCRYPTO])
m4trace:configure.ac:1068: -1- AH_OUTPUT([HAVE_EVP_CIPHER_CTX_NEW], [/* Define to 1 if you have the `EVP_CIPHER_CTX_new\' function. */
@%:@undef HAVE_EVP_CIPHER_CTX_NEW])
m4trace:configure.ac:1068: -1- AC_DEFINE_TRACE_LITERAL([HAVE_EVP_CIPHER_CTX_NEW])
m4trace:configure.ac:1068: -1- m4_pattern_allow([^HAVE_EVP_CIPHER_CTX_NEW$])
m4trace:configure.ac:1068: -1- AH_OUTPUT([HAVE_EVP_DECRYPTINIT_EX], [/* Define to 1 if you have the `EVP_DecryptInit_ex\' function. */
@%:@undef HAVE_EVP_DECRYPTINIT_EX])
m4trace:configure.ac:1068: -1- AC_DEFINE_TRACE_LITERAL([HAVE_EVP_DECRYPTINIT_EX])
m4trace:configure.ac:1068: -1- m4_pattern_allow([^HAVE_EVP_DECRYPTINIT_EX$])
m4trace:configure.ac:1102: -1- AH_OUTPUT([HAVE_LIBCAP_NG], [/* Define to 1 if you have the `cap-ng\' library (-lcap-ng). */
@%:@undef HAVE_LIBCAP_NG])
m4trace:configure.ac:1102: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LIBCAP_NG])
m4trace:configure.ac:1102: -1- m4_pattern_allow([^HAVE_LIBCAP_NG$])
m4trace:configure.ac:1103: -1- AH_OUTPUT([HAVE_CAP_NG_H], [/* Define to 1 if you have the <cap-ng.h> header file. */
@%:@undef HAVE_CAP_NG_H])
m4trace:configure.ac:1103: -1- AC_DEFINE_TRACE_LITERAL([HAVE_CAP_NG_H])
m4trace:configure.ac:1103: -1- m4_pattern_allow([^HAVE_CAP_NG_H$])
m4trace:configure.ac:1113: -1- AC_SUBST([V_CCOPT])
m4trace:configure.ac:1113: -1- AC_SUBST_TRACE([V_CCOPT])
m4trace:configure.ac:1113: -1- m4_pattern_allow([^V_CCOPT$])
m4trace:configure.ac:1114: -1- AC_SUBST([V_DEFS])
m4trace:configure.ac:1114: -1- AC_SUBST_TRACE([V_DEFS])
m4trace:configure.ac:1114: -1- m4_pattern_allow([^V_DEFS$])
m4trace:configure.ac:1115: -1- AC_SUBST([V_INCLS])
m4trace:configure.ac:1115: -1- AC_SUBST_TRACE([V_INCLS])
m4trace:configure.ac:1115: -1- m4_pattern_allow([^V_INCLS$])
m4trace:configure.ac:1116: -1- AC_SUBST([V_PCAPDEP])
m4trace:configure.ac:1116: -1- AC_SUBST_TRACE([V_PCAPDEP])
m4trace:configure.ac:1116: -1- m4_pattern_allow([^V_PCAPDEP$])
m4trace:configure.ac:1117: -1- AC_SUBST([LOCALSRC])
m4trace:configure.ac:1117: -1- AC_SUBST_TRACE([LOCALSRC])
m4trace:configure.ac:1117: -1- m4_pattern_allow([^LOCALSRC$])
m4trace:configure.ac:1118: -1- AC_SUBST([MAN_FILE_FORMATS])
m4trace:configure.ac:1118: -1- AC_SUBST_TRACE([MAN_FILE_FORMATS])
m4trace:configure.ac:1118: -1- m4_pattern_allow([^MAN_FILE_FORMATS$])
m4trace:configure.ac:1119: -1- AC_SUBST([MAN_MISC_INFO])
m4trace:configure.ac:1119: -1- AC_SUBST_TRACE([MAN_MISC_INFO])
m4trace:configure.ac:1119: -1- m4_pattern_allow([^MAN_MISC_INFO$])
m4trace:configure.ac:1121: -1- AC_REQUIRE_AUX_FILE([install-sh])
m4trace:configure.ac:1121: -1- AC_SUBST([INSTALL_PROGRAM])
m4trace:configure.ac:1121: -1- AC_SUBST_TRACE([INSTALL_PROGRAM])
m4trace:configure.ac:1121: -1- m4_pattern_allow([^INSTALL_PROGRAM$])
m4trace:configure.ac:1121: -1- AC_SUBST([INSTALL_SCRIPT])
m4trace:configure.ac:1121: -1- AC_SUBST_TRACE([INSTALL_SCRIPT])
m4trace:configure.ac:1121: -1- m4_pattern_allow([^INSTALL_SCRIPT$])
m4trace:configure.ac:1121: -1- AC_SUBST([INSTALL_DATA])
m4trace:configure.ac:1121: -1- AC_SUBST_TRACE([INSTALL_DATA])
m4trace:configure.ac:1121: -1- m4_pattern_allow([^INSTALL_DATA$])
m4trace:configure.ac:1123: -1- AC_CONFIG_HEADERS([config.h])
m4trace:configure.ac:1124: -1- AH_OUTPUT([00001], [
#ifndef TCPDUMP_CONFIG_H_
#define TCPDUMP_CONFIG_H_
])
m4trace:configure.ac:1128: -1- AH_OUTPUT([zzzz2], [
#endif // TCPDUMP_CONFIG_H_
])
m4trace:configure.ac:1137: -1- AC_CONFIG_FILES([Makefile tcpdump.1])
m4trace:configure.ac:1138: -1- AC_SUBST([LIB@&t@OBJS], [$ac_libobjs])
m4trace:configure.ac:1138: -1- AC_SUBST_TRACE([LIB@&t@OBJS])
m4trace:configure.ac:1138: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.ac:1138: -1- AC_SUBST([LTLIBOBJS], [$ac_ltlibobjs])
m4trace:configure.ac:1138: -1- AC_SUBST_TRACE([LTLIBOBJS])
m4trace:configure.ac:1138: -1- m4_pattern_allow([^LTLIBOBJS$])
m4trace:configure.ac:1138: -1- AC_SUBST_TRACE([top_builddir])
m4trace:configure.ac:1138: -1- AC_SUBST_TRACE([top_build_prefix])
m4trace:configure.ac:1138: -1- AC_SUBST_TRACE([srcdir])
m4trace:configure.ac:1138: -1- AC_SUBST_TRACE([abs_srcdir])
m4trace:configure.ac:1138: -1- AC_SUBST_TRACE([top_srcdir])
m4trace:configure.ac:1138: -1- AC_SUBST_TRACE([abs_top_srcdir])
m4trace:configure.ac:1138: -1- AC_SUBST_TRACE([builddir])
m4trace:configure.ac:1138: -1- AC_SUBST_TRACE([abs_builddir])
m4trace:configure.ac:1138: -1- AC_SUBST_TRACE([abs_top_builddir])
m4trace:configure.ac:1138: -1- AC_SUBST_TRACE([INSTALL])
